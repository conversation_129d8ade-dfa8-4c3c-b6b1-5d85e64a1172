package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	_ "github.com/herenow/atomic-gtw/mapper"
	"github.com/herenow/atomic-tools/pkg/asset_translations"
	"github.com/herenow/atomic-tools/pkg/exchange_path_finder"
	"github.com/jessevdk/go-flags"
)

var opts struct {
	Exchanges    []string `long:"exchange" description:"Exchange(s) to find paths in. Can be specified multiple times."`
	FromAsset    string   `long:"fromAsset" description:"Asset to convert from" required:"true"`
	ToAsset      string   `long:"toAsset" description:"Asset to convert to" required:"true"`
	MaxSteps     int      `long:"maxSteps" description:"Maximum number of conversion steps" default:"3"`
	Translations []string `long:"translations" description:"Asset translation rule in format Exchange:Asset=Exchange:Asset (can use * for exchange wildcards)"`
}

func main() {
	_, err := flags.Parse(&opts)
	if err != nil {
		log.Fatalf("Failed to parse flags: %s", err)
	}

	if len(opts.Exchanges) == 0 {
		log.Fatal("At least one exchange must be specified")
	}

	// Initialize translator if translations provided
	var translator *asset_translations.Translator
	if len(opts.Translations) > 0 {
		translator = asset_translations.New()
		err := translator.LoadTranslations(opts.Translations)
		if err != nil {
			log.Fatalf("Failed to load translations: %s", err)
		}
	}

	// Initialize path finder
	finder := exchange_path_finder.New()
	if translator != nil {
		finder.SetTranslator(translator)
	}

	// Process each exchange
	for _, exchangeName := range opts.Exchanges {
		markets, err := loadMarketsFromExchange(exchangeName)
		if err != nil {
			log.Printf("Warning: failed to load markets from %s: %s", exchangeName, err)
			continue
		}
		finder.LoadMarkets(markets)
		log.Printf("Loaded %d markets from %s", len(markets), exchangeName)
	}

	// Find paths
	paths := finder.FindPaths(
		strings.ToUpper(opts.FromAsset),
		strings.ToUpper(opts.ToAsset),
		opts.MaxSteps,
	)

	// Display results
	if len(paths) == 0 {
		fmt.Printf("No conversion paths found from %s to %s\n", opts.FromAsset, opts.ToAsset)
		os.Exit(1)
	}

	fmt.Printf("Found %d possible conversion paths from %s to %s:\n\n", len(paths), opts.FromAsset, opts.ToAsset)
	for i, path := range paths {
		fmt.Printf("Path %d:\n%s\n", i+1, path.String())
	}
}

func loadMarketsFromExchange(exchangeName string) ([]gateway.Market, error) {
	// Find exchange by name
	exchange, ok := gateway.ExchangeBySymbol(exchangeName)
	if !ok {
		return nil, fmt.Errorf("exchange not found: %s", exchangeName)
	}

	// Initialize gateway
	gtw, ok := gateway.NewByExchange(exchange, gateway.Options{})
	if !ok {
		return nil, fmt.Errorf("failed to initialize gateway for exchange: %s", exchangeName)
	}

	// Connect to exchange
	err := gtw.Connect()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to exchange %s: %s", exchangeName, err)
	}
	defer gtw.Close()

	// Get markets
	markets, err := gtw.GetMarkets()
	if err != nil {
		return nil, fmt.Errorf("failed to get markets from exchange %s: %s", exchangeName, err)
	}
	return markets, nil
}
