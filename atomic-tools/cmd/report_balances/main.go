package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	_ "github.com/herenow/atomic-gtw/mapper"
	"github.com/herenow/atomic-gtw/utils"
	_db "github.com/herenow/atomic-tools/internal/db"
	"github.com/herenow/atomic-tools/pkg/balances_converter"
	"github.com/herenow/atomic-tools/pkg/options_parser"
	"github.com/jessevdk/go-flags"
)

type Options struct {
	gateway.Options
	Exchange     string `long:"exchange" description:"Exchange to connect to" required:"true"`
	AccountID    string `long:"accountID" description:"Account id to use for the report, for identification" required:"true"`
	Source       string `long:"source" description:"Source tag to identify this report, something like the build version"`
	Benchmarks   string `long:"benchmarks" description:"Convert balances to this benchmark, multiple assets can be passed separated by comma: format: ASSET,ASSET,ASSET" default:"USD"`
	MaxSteps     int    `long:"maxSteps" description:"Maximum number of conversion steps" default:"3"`
	ClickhouseDB string `long:"clickhouseDB" description:"ClickHouse database URI (e.g., clickhouse://user:password@host:9000/database)"`
	Proxies      string `long:"proxies" description:"Comma separated list of proxy servers to use"`
}

var opts Options

var optsParser = flags.NewParser(&opts, flags.Default)

func main() {
	// Parse flags
	if _, err := optsParser.Parse(); err != nil {
		if flagsErr, ok := err.(*flags.Error); ok && flagsErr.Type == flags.ErrHelp {
			log.Fatalf("Usage: %s [OPTIONS]", os.Args[0])
		} else {
			log.Fatalf("Failed to parse flags, err: %s", err)
		}
	}

	maxSteps := opts.MaxSteps
	if maxSteps < 1 {
		log.Fatalf("--maxSteps invalid value: %d", maxSteps)
	}

	if opts.Exchange == "" {
		log.Fatal("You must pass an --exchange")
	}

	accGtwOpts := opts.Options
	exchange := options_parser.FindExchangeOrFatal(opts.Exchange)
	accGtw := options_parser.NewGatewayOrFatal(exchange, accGtwOpts)

	proxies, err := utils.ParseAndTestProxiesOpt(opts.Proxies)
	if err != nil {
		log.Fatalf("invalid --proxies opt, err: %s", err)
	}

	mktdGtwOpts := gateway.Options{
		Proxies:         proxies,
		PollMarketData:  opts.PollMarketData,
		UseAWSEndpoints: opts.UseAWSEndpoints,
		OriginSource:    opts.OriginSource,
		APIBaseURL:      opts.APIBaseURL,
	}
	mktdGtw := options_parser.NewGatewayOrFatal(exchange, mktdGtwOpts)

	// Try to connect to DB to validate the connection
	var db *_db.DB
	if opts.ClickhouseDB != "" {
		fmt.Printf("Connecting to ClickHouse database...\n")

		// 30 second timeout for ClickHouse
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		conn, err := _db.New(ctx, opts.ClickhouseDB)
		if err != nil {
			log.Fatalf("Failed to connect to database, err: %s", err)
		}

		db = conn
		defer db.Close()
	}

	// Connect gtw
	if err := accGtw.Connect(); err != nil {
		log.Fatalf("Failed to connect to accGtw, err: %s", err)
	}
	if err := mktdGtw.Connect(); err != nil {
		log.Fatalf("Failed to connect to mktdGtw, err: %s", err)
	}

	benchmarkAssets := strings.Split(opts.Benchmarks, ",")
	converter := balances_converter.New(accGtw, mktdGtw)

	for _, toAsset := range benchmarkAssets {
		report, err := converter.Convert(
			toAsset,
			maxSteps,
		)
		if err != nil {
			log.Fatalf("Failed to get report, err: %s\n", err)
		}

		if db != nil {
			printReport(report)

			fmt.Printf("Storing report for %s in database...\n", opts.AccountID)

			if err := storeReport(db, exchange, opts.AccountID, opts.Source, report); err != nil {
				log.Fatalf("Failed to store report, err: %s\n", err)
			}
		} else {
			printFullReport(report)
		}

	}
}

func storeReport(db *_db.DB, exchange gateway.Exchange, accountID, source string, report balances_converter.ConversionReport) error {
	conversions := make(_db.BalanceReportConversions)
	for _, conv := range report.Conversions {
		steps := make([]string, 0)
		for _, step := range conv.Steps {
			steps = append(steps, step.Market.String())
		}

		conversions[conv.Asset] = _db.BalanceReportConversion{
			Amount: conv.Amount,
			Value:  conv.Value,
			Steps:  steps,
		}
	}

	balanceReport := _db.BalanceReport{
		Ts:          time.Now().UTC(),
		Source:      source,
		AccountID:   accountID,
		Exchange:    exchange.ID(),
		Asset:       report.Asset,
		Value:       report.Value,
		Conversions: conversions,
	}

	if err := db.CreateBalanceReport(context.Background(), balanceReport); err != nil {
		return fmt.Errorf("creating balance report: %w", err)
	}

	return nil
}

func printReport(report balances_converter.ConversionReport) {
	fmt.Printf("Converted %d assets to %s\n", len(report.Conversions), report.Asset)
	fmt.Printf("Total value in %s: %f\n", report.Asset, report.Value)
}

func printFullReport(report balances_converter.ConversionReport) {
	for _, conv := range report.Conversions {
		fmt.Printf("--------------------\n")
		fmt.Printf("Converted %f %s to %f %s using %d steps\n",
			conv.Amount,
			conv.Asset,
			report.Value,
			report.Asset,
			len(conv.Steps))
		fmt.Printf("Best path steps: %s\n", conv.Steps)
	}
	fmt.Printf("Total value in %s: %f\n", report.Asset, report.Value)
}
