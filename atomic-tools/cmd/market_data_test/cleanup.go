package main

import (
	"sync"
	"time"

	"github.com/herenow/atomic-tools/pkg/logger"
	"go.uber.org/zap"
)

// cleanupTask represents a named cleanup function
type cleanupTask struct {
	name string
	fn   func()
}

// cleanupCoordinator manages graceful shutdown
type cleanupCoordinator struct {
	tasks  []cleanupTask
	mu     sync.Mutex
	logger *logger.Logger
}

func newCleanupCoordinator(logger *logger.Logger) *cleanupCoordinator {
	return &cleanupCoordinator{
		tasks:  make([]cleanupTask, 0),
		logger: logger,
	}
}

// registerCleanupTask registers a cleanup task to be executed during shutdown
func (c *cleanupCoordinator) registerCleanupTask(name string, task func()) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.tasks = append(c.tasks, cleanupTask{name: name, fn: task})
}

// runCleanup executes all registered cleanup tasks and waits for completion
func (c *cleanupCoordinator) runCleanup(timeout time.Duration) bool {
	var wg sync.WaitGroup
	c.mu.Lock()
	tasks := c.tasks // Get copy of tasks under lock
	c.mu.Unlock()

	// Start all cleanup tasks
	for _, task := range tasks {
		wg.Add(1)
		go func(t cleanupTask) {
			defer wg.Done()
			c.logger.Info("running cleanup task", zap.String("task", t.name))
			t.fn()
			c.logger.Info("completed cleanup task", zap.String("task", t.name))
		}(task)
	}

	// Wait for completion with timeout
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		return true
	case <-time.After(timeout):
		c.logger.Warn("cleanup timed out")
		return false
	}
}
