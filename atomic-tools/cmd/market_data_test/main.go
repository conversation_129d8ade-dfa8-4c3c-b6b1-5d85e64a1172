package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	_ "github.com/herenow/atomic-gtw/mapper"
	"github.com/herenow/atomic-tools/pkg/logger"
	"github.com/herenow/atomic-tools/pkg/marketdata"
	"github.com/herenow/atomic-tools/pkg/safegwbuilder"
	"go.uber.org/zap"
)

type options struct {
	exchanges string
	markets   string
	logLevel  string
}

type marketPair struct {
	Base  string
	Quote string
}

func main() {
	go func() {
		log.Println("Starting pprof on :6060")
		log.Println(http.ListenAndServe("localhost:6060", nil))
	}()

	opts := options{}
	flag.StringVar(&opts.exchanges, "exchanges", "", "Comma-separated list of exchanges (e.g., binance,bitso)")
	flag.StringVar(&opts.markets, "markets", "", "Comma-separated list of markets (e.g., BTC/USDT,ETH/USDT)")
	flag.StringVar(&opts.logLevel, "log-level", "info", "Log level (debug, info, warn, error)")
	flag.Parse()

	if opts.exchanges == "" {
		log.Fatal("--exchanges is required")
	}
	if opts.markets == "" {
		log.Fatal("--markets is required")
	}

	// Initialize logger
	ctx := context.Background()

	_logger, err := logger.New(ctx, logger.Config{Level: opts.logLevel})
	if err != nil {
		log.Fatal("Failed to create logger", zap.Error(err))
	}

	_logger.Info("starting market data monitor")

	// Initialize cleanup coordinator
	cleanup := newCleanupCoordinator(_logger)

	// Setup clean shutdown
	ctx, cancel := context.WithCancel(ctx)
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, os.Interrupt)
	go func() {
		<-sigCh
		_logger.Info("received interrupt signal, initiating graceful shutdown...")

		// Wait for cleanup tasks with timeout
		if cleanup.runCleanup(30 * time.Second) {
			_logger.Info("graceful shutdown completed")
		} else {
			_logger.Warn("forced exit due to cleanup timeout")
		}

		cancel()
	}()

	// Setup safegateway binary
	prepareSafegatewayBinary(ctx, _logger, cleanup)

	// Parse market pairs
	marketPairs := make([]marketPair, 0)
	for _, pair := range strings.Split(opts.markets, ",") {
		mp, err := parseMarketPair(pair)
		if err != nil {
			_logger.Fatal("invalid market pair", zap.Error(err))
		}
		marketPairs = append(marketPairs, mp)
	}

	mktMetrics := &marketdata.NoopMarketMetrics{}
	// Initialize market data manager
	manager := marketdata.NewManager(ctx, _logger, mktMetrics, gateway.Options{})
	cleanup.registerCleanupTask("market-data-manager", func() {
		if err = manager.Close(); err != nil {
			_logger.Error("failed to close market data manager", zap.Error(err))
		}
	})

	// Process each exchange
	var exchanges []gateway.Exchange
	if opts.exchanges == "*" {
		exchanges = gateway.Exchanges
	} else {
		for _, exchangeName := range strings.Split(opts.exchanges, ",") {
			exchangeName = strings.TrimSpace(exchangeName)
			if exchangeName == "" {
				continue
			}

			exchange, ok := gateway.ExchangeBySymbol(exchangeName)
			if !ok {
				_logger.Error("exchange not found", zap.String("exchange", exchangeName))
				continue
			}
			exchanges = append(exchanges, exchange)
		}
	}

	_logger.Info("processing exchanges",
		zap.Int("count", len(exchanges)),
		zap.String("exchanges", fmt.Sprintf("%v", exchanges)))

	// Find and subscribe to markets for each exchange
	allMarkets := make([]gateway.Market, 0)
	for _, exchange := range exchanges {
		// Find available markets
		matchingMarkets, err := findAvailableMarkets(ctx, exchange, marketPairs, _logger)
		if err != nil {
			_logger.Error("failed to find available markets",
				zap.String("exchange", exchange.Name),
				zap.Error(err))
			continue
		}

		if len(matchingMarkets) == 0 {
			_logger.Warn("no matching markets found",
				zap.String("exchange", exchange.Name))
			continue
		}

		// Subscribe to matching markets
		_logger.Info("subscribing to markets",
			zap.String("exchange", exchange.Name),
			zap.Int("markets", len(matchingMarkets)))

		if err := manager.SubscribeMarkets(matchingMarkets); err != nil {
			_logger.Error("failed to subscribe markets",
				zap.String("exchange", exchange.Name),
				zap.Error(err))
			continue
		}

		allMarkets = append(allMarkets, matchingMarkets...)
	}

	// Get list of market states that we will track
	marketStates := make(map[gateway.Market]*marketdata.MarketState)
	for _, market := range allMarkets {
		st, ok := manager.GetMarketState(market)
		if ok {
			marketStates[market] = st
		} else {
			_logger.Error("failed to get market state",
				zap.String("market", market.String()))
		}
	}

	// Monitor market states and updates
	monitorMarketStates(ctx, _logger, marketStates)
	_logger.Info("main loop finished, exiting")
}

func parseMarketPair(pair string) (marketPair, error) {
	parts := strings.Split(pair, "/")
	if len(parts) != 2 {
		return marketPair{}, fmt.Errorf("invalid market pair format: %s, expected BASE/QUOTE", pair)
	}
	return marketPair{
		Base:  strings.ToUpper(strings.TrimSpace(parts[0])),
		Quote: strings.ToUpper(strings.TrimSpace(parts[1])),
	}, nil
}

// findAvailableMarkets initializes a gateway for the exchange and finds matching markets
func findAvailableMarkets(
	ctx context.Context,
	exchange gateway.Exchange,
	marketPairs []marketPair,
	logger *logger.Logger,
) ([]gateway.Market, error) {
	logger = logger.With(zap.String("exchange", exchange.Name))

	// Initialize gateway with default options
	gtw, ok := gateway.NewByExchange(exchange, gateway.Options{})
	if !ok {
		return nil, fmt.Errorf("failed to create gateway for exchange %s", exchange.Name)
	}
	defer gtw.Close()

	// Fetch available markets
	markets, err := gtw.GetMarkets()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch markets: %w", err)
	}

	// Find matching markets based on requested pairs
	matchingMarkets := make([]gateway.Market, 0)
	for _, market := range markets {
		for _, pair := range marketPairs {
			if (pair.Base == "*" || market.Pair.Base == pair.Base) &&
				(pair.Quote == "*" || market.Pair.Quote == pair.Quote) {
				logger.Debug("found matching market",
					zap.String("symbol", market.Symbol),
					zap.String("base", market.Pair.Base),
					zap.String("quote", market.Pair.Quote))
				matchingMarkets = append(matchingMarkets, market)
				break
			}
		}
	}

	logger.Info("market matching results",
		zap.Int("available_markets", len(markets)),
		zap.Int("matching_markets", len(matchingMarkets)))

	return matchingMarkets, nil
}

func monitorMarketStates(
	ctx context.Context,
	logger *logger.Logger,
	marketStates map[gateway.Market]*marketdata.MarketState,
) {
	file, err := os.OpenFile("tmp/MARKET_DATA_STATS", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		fmt.Printf("Error opening file: %v\n", err)
		return
	}
	defer file.Close()

	// Monitor all market subscriptions
	for {
		select {
		case <-ctx.Done():
			logger.Info("exiting market data stats monitor")
			return
		case <-time.After(5 * time.Second):
		}

		// Summarize market states
		stats := summarizeMarketStates(marketStates)

		// Generate report
		now := time.Now()
		report := fmt.Sprintf("\n=== Market Data Stats [%s] ===\n", now.Format(time.RFC3339))
		report += fmt.Sprintf("Total Markets: %d\n", stats.total)
		report += fmt.Sprintf("Market States:\n")
		report += fmt.Sprintf("  Queued: %d\n", stats.queued)
		report += fmt.Sprintf("  Initializing: %d\n", stats.initializing)
		report += fmt.Sprintf("  Ready: %d\n", stats.ready)
		report += fmt.Sprintf("  Out of Sync: %d\n", stats.outOfSync)
		report += fmt.Sprintf("  Disconnected: %d\n", stats.disconnected)
		report += fmt.Sprintf("  Failed: %d\n", stats.failed)
		report += fmt.Sprintf("  Stopped: %d\n", stats.stopped)

		if stats.total > 0 {
			report += fmt.Sprintf("Update Rates:\n")
			report += fmt.Sprintf("  Total updates/sec: %.2f\n", stats.updatesPerSec)
			report += fmt.Sprintf("  Average updates/sec: %.2f\n", stats.avgUpdatesPerSec)
			report += fmt.Sprintf("  Highest updates/sec: %.2f (%s)\n", stats.maxUpdatesPerSec, stats.maxUpdatesSecMarket)
		}

		if len(stats.connectingMarkets) > 0 {
			report += fmt.Sprintf("Connecting markets:\n")
			i := 0
			for market, state := range stats.connectingMarkets {
				report += fmt.Sprintf("  - %s [%s] [%s] [%d seq]\n",
					market.String(),
					state.State(),
					state.LastUpdate().Format(time.RFC3339),
					state.Sequence())

				// Print only first 5 stalled markets
				i++
				if i >= 5 {
					report += fmt.Sprintf("  ... and %d more\n", len(stats.connectingMarkets)-5)
					break
				}
			}
		}

		if len(stats.failedMarkets) > 0 {
			report += fmt.Sprintf("Failed Markets:\n")
			i := 0
			for market, state := range stats.failedMarkets {
				report += fmt.Sprintf("  - %s [%s] [%v]\n",
					market.String(),
					state.State(),
					state.LastError())

				// Print only first 5 stalled markets
				i++
				if i >= 5 {
					report += fmt.Sprintf("  ... and %d more\n", len(stats.failedMarkets)-5)
					break
				}
			}
		}

		// Write report to file
		if _, err := file.WriteString(report); err != nil {
			logger.Error("failed to write stats to file", zap.Error(err))
		}

		// Also log summary to stdout
		logger.Info("market data stats",
			zap.Int("total", stats.total),
			zap.Int("ready", stats.ready),
			zap.Int("failed", len(stats.failedMarkets)),
			zap.Int("updates_last_sec", int(stats.updatesPerSec)))
	}
}

type marketStats struct {
	total               int
	ready               int
	queued              int
	initializing        int
	recconnecting       int
	disconnected        int
	failed              int
	outOfSync           int
	stopped             int
	updatesPerSec       uint32
	avgUpdatesPerSec    float64
	maxUpdatesPerSec    uint32
	maxUpdatesSecMarket gateway.Market
	connectingMarkets   map[gateway.Market]*marketdata.MarketState
	failedMarkets       map[gateway.Market]*marketdata.MarketState
	lastUpdates         map[gateway.Market]time.Time
}

func summarizeMarketStates(marketStates map[gateway.Market]*marketdata.MarketState) marketStats {
	stats := marketStats{
		total:             len(marketStates),
		lastUpdates:       make(map[gateway.Market]time.Time),
		connectingMarkets: make(map[gateway.Market]*marketdata.MarketState),
		failedMarkets:     make(map[gateway.Market]*marketdata.MarketState),
	}

	stats.updatesPerSec = 0.0

	for market, state := range marketStates {
		currentState := state.State()

		// Track state counts
		switch currentState {
		case marketdata.MarketStateReady:
			stats.ready++
		case marketdata.MarketStateQueued:
			stats.queued++
		case marketdata.MarketStateInitializing:
			stats.initializing++
		case marketdata.MarketStateDisconnected:
			stats.disconnected++
		case marketdata.MarketStateFailed:
			stats.failed++
		case marketdata.MarketStateStopped:
			stats.stopped++
		case marketdata.MarketStateOutOfSync:
			stats.outOfSync++
		}

		// Track update rates
		updates := state.UpdatePerMinute()
		lastUpdatesPerSec := updates.Last()
		stats.updatesPerSec += lastUpdatesPerSec
		if lastUpdatesPerSec > stats.maxUpdatesPerSec {
			stats.maxUpdatesPerSec = lastUpdatesPerSec
			stats.maxUpdatesSecMarket = market
		}

		// Track last updates
		stats.lastUpdates[market] = state.LastUpdate()

		// Check for stalled markets (no updates in last minute)
		switch currentState {
		case marketdata.MarketStateInitializing:
			stats.connectingMarkets[market] = state
		case marketdata.MarketStateFailed, marketdata.MarketStateOutOfSync, marketdata.MarketStateStopped:
			stats.failedMarkets[market] = state
		}
	}

	if stats.total > 0 {
		stats.avgUpdatesPerSec = float64(stats.updatesPerSec) / float64(stats.total)
	}

	return stats
}

func prepareSafegatewayBinary(ctx context.Context, logger *logger.Logger, cleanup *cleanupCoordinator) {
	logger.Info("building safegateway binary")

	builder := safegwbuilder.New(nil)

	// Ensure binary exists before creating manager
	if err := builder.EnsureBinary(); err != nil {
		panic(fmt.Errorf("failed to ensure safegateway binary: %w", err))
	}

	// Set SAFEGATEWAY_BIN_PATH environment variable
	os.Setenv("SAFEGATEWAY_BIN_PATH", builder.BinaryPath())

	// Add cleanup on context cancellation
	cleanup.registerCleanupTask("safegateway-binary", func() {
		logger.Info("cleaning up safegateway binary")
		if err := builder.Cleanup(); err != nil {
			logger.Error("failed to cleanup safegateway binary", zap.Error(err))
		}
	})
}
