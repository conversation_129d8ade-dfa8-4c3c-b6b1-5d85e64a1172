package db

import (
	"context"
	"fmt"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
)

type DB struct {
	conn driver.Conn
}

func New(ctx context.Context, dsn string) (*DB, error) {
	options, err := clickhouse.ParseDSN(dsn)
	if err != nil {
		return nil, fmt.E<PERSON>rf("parsing DSN: %w", err)
	}

	conn, err := clickhouse.Open(options)
	if err != nil {
		return nil, fmt.Errorf("opening connection: %w", err)
	}

	if err := conn.Ping(ctx); err != nil {
		return nil, fmt.Errorf("pinging database: %w", err)
	}

	db := &DB{conn: conn}

	if err := db.createSchema(ctx); err != nil {
		return nil, fmt.Errorf("creating schema: %w", err)
	}

	return db, nil
}

func (db *DB) createSchema(ctx context.Context) error {
	stmts := []struct {
		name string
		stmt string
	}{
		{
			name: "balance_reports",
			stmt: `CREATE TABLE IF NOT EXISTS balance_reports (
		ts DateTime64(3, 'UTC') DEFAULT now64(3),
		source LowCardinality(String),
		account_id LowCardinality(String),
		exchange LowCardinality(String),
		asset LowCardinality(String),
		value Float64,
		conversions JSON,
	)
	ENGINE = MergeTree()
   	ORDER BY (exchange, account_id, asset, ts)
	PARTITION BY toYYYYMM(ts);`,
		},
	}

	for _, s := range stmts {
		if err := db.conn.Exec(ctx, s.stmt); err != nil {
			return fmt.Errorf("creating schema for %s: %w", s.name, err)
		}
	}

	return nil
}

func (db *DB) Close() error {
	return db.conn.Close()
}
