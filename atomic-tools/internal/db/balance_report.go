package db

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
)

type BalanceReport struct {
	Ts          time.Time
	Source      string
	AccountID   string
	Exchange    string
	Asset       string
	Value       float64
	Conversions BalanceReportConversions
}

type BalanceReportConversions map[string]BalanceReportConversion

type BalanceReportConversion struct {
	Amount float64  `json:"amount"`
	Value  float64  `json:"value"`
	Steps  []string `json:"steps"`
}

func (db *DB) CreateBalanceReport(ctx context.Context, report BalanceReport) error {
	query := `
	INSERT INTO balance_reports (
		ts, source, account_id, exchange, asset, value, conversions
	) VALUES (
		?, ?, ?, ?, ?, ?, ?
	)`

	conversionsJSON, err := json.Marshal(report.Conversions)
	if err != nil {
		return fmt.Errorf("marshaling conversions json: %w", err)
	}

	err = db.conn.Exec(ctx, query,
		report.Ts,
		report.Source,
		report.AccountID,
		report.Exchange,
		report.Asset,
		report.Value,
		string(conversionsJSON),
	)

	if err != nil {
		return fmt.Erro<PERSON>("inserting balance report: %w", err)
	}

	return nil
}
