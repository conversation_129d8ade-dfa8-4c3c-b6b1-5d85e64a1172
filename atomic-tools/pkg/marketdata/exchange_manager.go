package marketdata

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/logger"
	"go.uber.org/zap"
)

const (
	maxMarketsPerGateway   = 25
	subscriptionTick       = 250 * time.Millisecond
	subscriptionBatchDelay = 1500 * time.Millisecond
)

type exchangeManager struct {
	exchange   gateway.Exchange
	gtwOpts    gateway.Options
	ctx        context.Context
	cancel     context.CancelFunc
	mu         sync.RWMutex
	logger     *logger.Logger
	gtwSeq     uint64
	mktMetrics IMarketStateMetrics

	// Track markets we want to be subscribed to
	desiredMarkets map[string]gateway.Market

	marketManagers   map[string]*MarketManager
	muMarketManagers sync.RWMutex
}

func NewExchangeManager(ctx context.Context, logger *logger.Logger, exchange gateway.Exchange, mktMetrics IMarketStateMetrics) Manager {
	ctx, cancel := context.WithCancel(ctx)

	log := logger.With(
		zap.String("system", "market_data"),
		zap.String("component", "exchange_manager"),
		zap.String("exchange", exchange.Name),
	)

	em := &exchangeManager{
		exchange:       exchange,
		ctx:            ctx,
		cancel:         cancel,
		logger:         log,
		mktMetrics:     mktMetrics,
		desiredMarkets: make(map[string]gateway.Market),
		marketManagers: make(map[string]*MarketManager),
	}

	go em.startSubscriptionProcessor()

	return em
}

// SetGatewayOptions can change the gateway options at any time
// Eventually, the gateway will be reconnected with the new options
func (em *exchangeManager) SetGatewayOptions(_ gateway.Exchange, opts gateway.Options) {
	em.gtwOpts = opts
}

func (em *exchangeManager) SubscribeMarkets(markets []gateway.Market) error {
	em.logger.Info("subscribing to markets",
		zap.String("markets", strings.Join(gateway.MarketsToSymbols(markets), ",")))

	em.mu.Lock()
	defer em.mu.Unlock()

	// Validation
	for _, market := range markets {
		if market.Exchange.Name != em.exchange.Name {
			return fmt.Errorf("market %s is not for exchange %s", market.Symbol, em.exchange.Name)
		}
	}

	for _, market := range markets {
		// Init market manager so the caller start querying the market state
		_ = em.managerForMarket(market)

		// Add to desired markets
		em.desiredMarkets[market.Symbol] = market
	}

	return nil
}

func (em *exchangeManager) UnsubscribeMarkets(markets []gateway.Market) error {
	em.logger.Info("unsubscribing from markets",
		zap.String("markets", strings.Join(gateway.MarketsToSymbols(markets), ",")))

	em.mu.Lock()
	defer em.mu.Unlock()

	for _, market := range markets {
		delete(em.desiredMarkets, market.Symbol)
	}

	return nil
}

func (em *exchangeManager) startSubscriptionProcessor() {
	ticker := time.NewTicker(subscriptionTick)
	defer ticker.Stop()

	for {
		select {
		case <-em.ctx.Done():
			return

		case <-ticker.C:
			// Check for markets that need subscription
			unsubscribedMarkets := em.collectUnsubscribedMarkets()

			if len(unsubscribedMarkets) > 0 {
				// Log the start of the batch accumulation period
				em.logger.Debug("starting subscription batch accumulation period",
					zap.Int("markets", len(unsubscribedMarkets)))

				// Wait for the batch delay to accumulate more subscription requests
				select {
				case <-em.ctx.Done():
					return
				case <-time.After(subscriptionBatchDelay):
					unsubscribedMarkets = em.collectUnsubscribedMarkets()

					// Process all accumulated subscription requests
					em.processSubscriptions(unsubscribedMarkets)
				}
			}
		}
	}
}

func (em *exchangeManager) collectUnsubscribedMarkets() []gateway.Market {
	unsubscribedMarkets := make([]gateway.Market, 0)

	em.mu.Lock()
	defer em.mu.Unlock()

	for _, market := range em.desiredMarkets {
		mgmr := em.managerForMarket(market)
		state := mgmr.GetState()

		switch {
		case state.CanReconnect():
			unsubscribedMarkets = append(unsubscribedMarkets, market)
		case state.IsQueued():
			unsubscribedMarkets = append(unsubscribedMarkets, market)
		}
	}

	return unsubscribedMarkets
}

func (em *exchangeManager) hasUnsubscribedMarkets() bool {
	return len(em.collectUnsubscribedMarkets()) > 0
}

func (em *exchangeManager) processSubscriptions(markets []gateway.Market) {
	if len(markets) == 0 {
		return
	}

	// Set all markets as initializing
	for _, market := range markets {
		mgmr := em.managerForMarket(market)
		mgmr.GetState().SetState(MarketStateInitializing, nil)
	}

	// Log the batch processing
	em.logger.Info("processing subscriptions to unsubscribed markets",
		zap.String("markets", strings.Join(gateway.MarketsToSymbols(markets), ",")))

	// Process unsubscribed markets in chunks to avoid creating too many gateway instances
	for i := 0; i < len(markets); i += maxMarketsPerGateway {
		end := i + maxMarketsPerGateway
		if end > len(markets) {
			end = len(markets)
		}

		// Asynchronously initialize gateway for markets batch
		go func(marketsChunk []gateway.Market) {
			if err := em.initGatewayForMarkets(marketsChunk); err != nil {
				em.logger.Error("failed to init gateway for markets",
					zap.Error(err),
					zap.Int("markets_count", len(marketsChunk)))
			}
		}(markets[i:end])
	}
}

func (em *exchangeManager) managerForMarket(market gateway.Market) *MarketManager {
	em.muMarketManagers.Lock()
	defer em.muMarketManagers.Unlock()

	mgmr, exists := em.marketManagers[market.Symbol]
	if !exists {
		mgmr = NewMarketManager(market, em.logger, em.mktMetrics)
		em.marketManagers[market.Symbol] = mgmr
	}

	return mgmr
}

func (em *exchangeManager) initGatewayForMarkets(markets []gateway.Market) error {
	// Setup markets and set their state to initializing
	gtwMarkets := make([]GatewayMarket, 0, len(markets))
	for _, market := range markets {
		mgmr := em.managerForMarket(market)
		gtwMarkets = append(gtwMarkets, GatewayMarket{
			Market:  market,
			Manager: mgmr,
		})
	}

	seq := atomic.AddUint64(&em.gtwSeq, 1)
	id := fmt.Sprintf("%s-%d", em.exchange.Name, seq)

	log := em.logger.With(
		zap.String("gateway_id", id),
	)

	log.Info("initializing gateway instance",
		zap.String("markets", strings.Join(gateway.MarketsToSymbols(markets), ",")))

	gtwInstance := NewGatewayInstance(
		em.ctx,
		em.logger,
		id,
		em.exchange,
		em.gtwOpts,
		true, // Use safe gateway
	)

	// Try to connect gateway
	// If fail, set markets as disconnected and suspend them for 15 seconds...
	if err := gtwInstance.Connect(em.ctx); err != nil {
		log.Error("failed to connect gateway", zap.Error(err))
		err = fmt.Errorf("failed to connect gateway: %w", err)
		em.setMarketsAsFailed(markets, err)
		return err
	}

	// Now the process of controlling market state is handled by the GatewayInstance
	// it will handle disconnections and market health, if we try to subscribe markets
	// and they fail, the GatewayInstance will handle the reconnection process
	if err := gtwInstance.SubscribeMarkets(em.ctx, gtwMarkets); err != nil {
		log.Error("failed to subscribe markets", zap.Error(err))
		return fmt.Errorf("failed to subscribe markets: %w", err)
	}

	// Reset failed connect attemps, since we subscribed markets successfully
	em.resetMarketsFailed(markets)

	return nil
}

// Sets a group of markets as failed, this will set their state to MarketStateFailed
// which triggers a backoff and reconnection
func (em *exchangeManager) setMarketsAsFailed(markets []gateway.Market, err error) {
	for _, market := range markets {
		em.managerForMarket(market).GetState().SetFailed(err)
	}
}

// Sets a group of markets as disconnected, this will suspend them for a duration
// and set their state to MarketStateDisconnected, this will trigger a reconnection
func (em *exchangeManager) setMarketsAsDisconnected(markets []gateway.Market, err error, duration time.Duration) {
	for _, market := range markets {
		em.managerForMarket(market).GetState().SetDisconnected(err)
	}
}

func (em *exchangeManager) resetMarketsFailed(markets []gateway.Market) {
	for _, market := range markets {
		em.managerForMarket(market).GetState().ResetFailure()
	}
}

func (em *exchangeManager) GetMarketState(market gateway.Market) (*MarketState, bool) {
	return em.managerForMarket(market).GetState(), true
}

func (em *exchangeManager) SubscribeMarketUpdate(market gateway.Market) <-chan MarketUpdate {
	em.mu.RLock()
	mgmr, exists := em.marketManagers[market.Symbol]
	em.mu.RUnlock()

	if !exists {
		return nil
	}

	return mgmr.Subscribe(em.ctx)
}

func (em *exchangeManager) Close() error {
	em.logger.Info("closing exchange manager")
	em.cancel()
	return nil
}
