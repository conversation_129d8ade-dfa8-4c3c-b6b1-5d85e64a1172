package marketdata

import (
	"context"
	"fmt"
	"sync"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/logger"
	"go.uber.org/zap"
)

// Manager interface defines the contract for market data management
type Manager interface {
	// Close cleans up all resources and connections.
	// After Close is called, the manager cannot be reused.
	Close() error

	// SubscribeMarkets initiates market data subscriptions for the given markets.
	// If called multiple times with overlapping markets, it will only subscribe
	// to markets that aren't already subscribed.
	// Returns error if subscription fails.
	SubscribeMarkets([]gateway.Market) error

	// UnsubscribeMarkets removes market data subscriptions for the given markets.
	// Returns error if unsubscription fails.
	UnsubscribeMarkets([]gateway.Market) error

	// GetMarketState returns the current market state for a given market
	// If the market is not yet subscribed, the second return value will be false
	GetMarketState(gateway.Market) (*MarketState, bool)

	// SubscribeMarketUpdate returns a channel that will receive notifications
	// when market data for the given market is updated.
	// It will contain a diff of what has changed since the last update.
	// It will also contian a sequence number that can be used to detect missing updates.
	SubscribeMarketUpdate(gateway.Market) <-chan MarketUpdate

	// SetGatewayOptions sets the gateway options for a given exchange
	SetGatewayOptions(gateway.Exchange, gateway.Options)
}

// Manages many exchange instances
type manager struct {
	ctx              context.Context
	cancel           context.CancelFunc
	logger           *logger.Logger
	defaultGtwOpts   gateway.Options
	mktMetrics       IMarketStateMetrics
	mu               sync.RWMutex
	exchangeManagers map[gateway.Exchange]Manager
}

func NewManager(ctx context.Context, logger *logger.Logger, mktMetrics IMarketStateMetrics, gtwOpts gateway.Options) Manager {
	ctx, cancel := context.WithCancel(ctx)
	return &manager{
		ctx:    ctx,
		cancel: cancel,
		logger: logger.With(
			zap.String("system", "market_data"),
			zap.String("component", "manager"),
		),
		mktMetrics:       mktMetrics,
		defaultGtwOpts:   gtwOpts,
		exchangeManagers: make(map[gateway.Exchange]Manager),
	}
}

// SubscribeMarkets ensures exchange managers exist for the requested markets
// and forwards subscription requests to the appropriate managers
func (m *manager) SubscribeMarkets(markets []gateway.Market) error {
	// Group markets by exchange
	marketsByExchange := make(map[gateway.Exchange][]gateway.Market)
	for _, market := range markets {
		marketsByExchange[market.Exchange] = append(
			marketsByExchange[market.Exchange],
			market,
		)
	}

	var errs []error
	for exchange, mkts := range marketsByExchange {
		// Get or create exchange manager
		em := m.getOrCreateExchangeManager(exchange)

		// Subscribe markets for this exchange
		if err := em.SubscribeMarkets(mkts); err != nil {
			errs = append(errs, fmt.Errorf("failed to subscribe %d markets for %s: %w", len(mkts), exchange, err))
			continue
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("subscription errors: %v", errs)
	}

	return nil
}

func (m *manager) UnsubscribeMarkets(markets []gateway.Market) error {
	// Group markets by exchange
	marketsByExchange := make(map[gateway.Exchange][]gateway.Market)
	for _, market := range markets {
		marketsByExchange[market.Exchange] = append(
			marketsByExchange[market.Exchange],
			market,
		)
	}

	var errs []error
	for exchange, mkts := range marketsByExchange {
		m.mu.RLock()
		em, exists := m.exchangeManagers[exchange]
		m.mu.RUnlock()

		if !exists {
			continue // Already unsubscribed/never subscribed
		}

		if err := em.UnsubscribeMarkets(mkts); err != nil {
			errs = append(errs, fmt.Errorf("failed to unsubscribe %d markets for %s: %w", len(mkts), exchange, err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("unsubscription errors: %v", errs)
	}

	return nil
}

func (m *manager) SetGatewayOptions(exchange gateway.Exchange, opts gateway.Options) {
	mgr := m.getOrCreateExchangeManager(exchange)
	mgr.SetGatewayOptions(exchange, opts)
}

func (m *manager) GetMarketState(market gateway.Market) (*MarketState, bool) {
	m.mu.RLock()
	em, exists := m.exchangeManagers[market.Exchange]
	m.mu.RUnlock()

	if !exists {
		return nil, false
	}

	return em.GetMarketState(market)
}

func (m *manager) SubscribeMarketUpdate(market gateway.Market) <-chan MarketUpdate {
	m.mu.RLock()
	em, exists := m.exchangeManagers[market.Exchange]
	m.mu.RUnlock()

	if !exists {
		return nil
	}

	return em.SubscribeMarketUpdate(market)
}

func (m *manager) Close() error {
	m.cancel() // Cancel context for all child managers

	m.mu.Lock()
	defer m.mu.Unlock()

	var errs []error
	for exchange, exchangeManager := range m.exchangeManagers {
		if err := exchangeManager.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close exchange manager for %s: %w", exchange, err))
		} else {
			delete(m.exchangeManagers, exchange)
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("close errors: %v", errs)
	}

	return nil
}

// getOrCreateExchangeManager returns an existing exchange manager or creates a new one
func (m *manager) getOrCreateExchangeManager(exchange gateway.Exchange) Manager {
	m.mu.Lock()
	em, exists := m.exchangeManagers[exchange]
	if !exists {
		// Create new exchange manager
		em = NewExchangeManager(m.ctx, m.logger, exchange, m.mktMetrics)

		m.exchangeManagers[exchange] = em

		m.logger.Info("created new exchange manager",
			zap.String("exchange", exchange.Name))
	}
	m.mu.Unlock()

	// Set default gateway options
	em.SetGatewayOptions(exchange, m.defaultGtwOpts)

	return em
}
