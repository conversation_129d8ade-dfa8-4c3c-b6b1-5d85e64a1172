package marketdata

type MarketStateEnum int

const (
	// Initial states during setup
	MarketStateQueued       MarketStateEnum = iota
	MarketStateInitializing MarketStateEnum = iota

	// Active states after connection
	MarketStateReady // Received snapshot, processing updates normally

	// Terminal states
	MarketStateOutOfSync    // Book state significantly differs from exchange
	MarketStateStopped      // Market cleanly stopped
	MarketStateFailed       // Market failed to initialize
	MarketStateDisconnected // Gateway connection lost
)

func isTerminalState(state MarketStateEnum) bool {
	switch state {
	case MarketStateOutOfSync, MarketStateStopped, MarketStateFailed, MarketStateDisconnected:
		return true
	default:
		return false
	}
}

func (s MarketStateEnum) String() string {
	switch s {
	case MarketStateQueued:
		return "queued"
	case MarketStateInitializing:
		return "initializing"
	case MarketStateReady:
		return "ready"
	case MarketStateOutOfSync:
		return "out_of_sync"
	case MarketStateDisconnected:
		return "disconnected"
	case MarketStateFailed:
		return "failed"
	case MarketStateStopped:
		return "stopped"
	default:
		return "unknown"
	}
}

// IsValidTransition checks if a state transition is allowed
func IsValidTransition(from, to MarketStateEnum) bool {
	// Define valid transitions map
	validTransitions := map[MarketStateEnum][]MarketStateEnum{
		MarketStateQueued: {
			MarketStateInitializing,
		},
		MarketStateInitializing: {
			MarketStateFailed,
			MarketStateDisconnected,
			MarketStateStopped,
			MarketStateReady,
		},
		MarketStateReady: {
			MarketStateOutOfSync,
			MarketStateDisconnected,
			MarketStateFailed,
			MarketStateStopped,
		},
		MarketStateOutOfSync: {
			MarketStateReady,
			MarketStateDisconnected,
			MarketStateInitializing,
		},
		MarketStateDisconnected: {
			MarketStateStopped,
			MarketStateInitializing,
		},
		MarketStateStopped: {
			MarketStateInitializing,
		},
		MarketStateFailed: {
			MarketStateInitializing,
			MarketStateDisconnected,
		},
	}

	allowed, exists := validTransitions[from]
	if !exists {
		return false
	}

	for _, validState := range allowed {
		if validState == to {
			return true
		}
	}

	return false
}
