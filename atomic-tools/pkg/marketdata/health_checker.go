package marketdata

import (
	"context"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/book"
	"github.com/herenow/atomic-tools/pkg/logger"
	"go.uber.org/zap"
)

const (
	// Check intervals
	scheduleInterval    = 5 * time.Second
	randomCheckInterval = 5 * time.Minute

	// Market monitoring thresholds
	staleThreshold = 60 * time.Second
	randomCheckPct = 0.20 // Check 20% of markets

	// Maximum time to retry schedule due to errors
	maxRetryDuration = 30 * time.Second

	// Retry delays
	retryBaseDelay = 100 * time.Millisecond
	retryMaxDelay  = 3 * time.Second

	// Divergence thresholds based on spread
	spreadMultiplier       = 10.0  // Max divergence = 10x current spread
	minDivergenceThreshold = 0.001 // 0.1% minimum threshold
	maxDivergenceThreshold = 0.05  // 5% maximum threshold
)

type scheduleState struct {
	startTime    time.Time
	lastAttempt  time.Time
	retryAttempt int
	nextDelay    time.Duration
}

func (s *scheduleState) shouldRetry() bool {
	return time.Since(s.startTime) < maxRetryDuration
}

func (s *scheduleState) updateRetry() {
	s.retryAttempt++
	s.lastAttempt = time.Now()

	// Calculate next backoff with jitter
	backoff := float64(retryBaseDelay) * math.Pow(2, float64(s.retryAttempt-1))
	if backoff > float64(retryMaxDelay) {
		backoff = float64(retryMaxDelay)
	}

	// Add jitter (±20%)
	jitter := (rand.Float64() * 0.4) - 0.2
	s.nextDelay = time.Duration(backoff * (1 + jitter))
}

func (s *scheduleState) reset() {
	s.startTime = time.Now()
	s.retryAttempt = 0
	s.nextDelay = 0
}

type HealthChecker struct {
	ctx      context.Context
	cancel   context.CancelFunc
	logger   *logger.Logger
	gtw      gateway.Gateway
	mu       sync.RWMutex
	markets  map[gateway.Market]*MarketState
	updateCh chan HealthUpdate
	err      error     // Last fatal error
	errOnce  sync.Once // Ensure we only set fatal error once
	state    *scheduleState
}

// HealthUpdate represents a single market going out of sync
type HealthUpdate struct {
	Market gateway.Market
	Source string
	Error  error
}

func NewHealthChecker(
	ctx context.Context,
	logger *logger.Logger,
	gtw gateway.Gateway,
	markets map[gateway.Market]*MarketState,
) *HealthChecker {
	ctx, cancel := context.WithCancel(ctx)

	return &HealthChecker{
		ctx:      ctx,
		cancel:   cancel,
		logger:   logger.With(zap.String("component", "health_checker")),
		gtw:      gtw,
		markets:  markets,
		updateCh: make(chan HealthUpdate, 100),
		state:    &scheduleState{},
	}
}

var ErrHealthCheckerClosed = errors.New("health checker closed")

// NextUpdate returns the next market update or fatal error that killed the checker.
// When it returns an error, the checker is dead and no more updates will be available.
func (h *HealthChecker) NextUpdate() (HealthUpdate, error) {
	// If we already have a fatal error, return it
	if h.err != nil {
		return HealthUpdate{}, h.err
	}

	select {
	case <-h.ctx.Done():
		if h.err != nil {
			return HealthUpdate{}, h.err
		}
		return HealthUpdate{}, ErrHealthCheckerClosed

	case update, ok := <-h.updateCh:
		if !ok {
			if h.err != nil {
				return HealthUpdate{}, h.err
			}
			// We should never reach this point
			// since the channel is closed only when the context is done
			h.logger.Error("health checker update channel closed unexpectedly")
			return HealthUpdate{}, h.ctx.Err()
		}
		return update, nil
	}
}

func (h *HealthChecker) Start() {
	go h.monitorSchedule()
}

func (h *HealthChecker) Stop() {
	h.cancel()
	close(h.updateCh)
}

// setFatalError sets the fatal error that killed the checker
// It ensures we only set the error once and stops the checker
func (h *HealthChecker) setFatalError(err error) {
	h.errOnce.Do(func() {
		h.err = err
		h.Stop()
	})
}

func (h *HealthChecker) RemoveMarket(market gateway.Market) {
	h.mu.Lock()
	delete(h.markets, market)
	if len(h.markets) == 0 {
		h.mu.Unlock()
		h.cancel()
	} else {
		h.mu.Unlock()
	}
}

func (h *HealthChecker) monitorSchedule() {
	ticker := time.NewTicker(scheduleInterval)
	defer ticker.Stop()

	lastRandomCheck := time.Now()
	for {
		select {
		case <-h.ctx.Done():
			return
		case <-ticker.C:
			h.state.reset() // Reset retry state at start of each schedule

			// First check for stale markets
			checkedMarkets := h.checkStaleMarkets()

			// Then check if it's time for random checks
			if time.Since(lastRandomCheck) >= randomCheckInterval {
				h.performRandomChecks(checkedMarkets)
				lastRandomCheck = time.Now()
			}
		}
	}
}

func (h *HealthChecker) checkStaleMarkets() []gateway.Market {
	// Markets for checking
	checkMarkets := make([]gateway.Market, 0)

	h.mu.RLock()
	for market, state := range h.markets {
		// Only check markets in Ready state
		if state.State() != MarketStateReady {
			continue
		}

		// Check if market is stale
		if time.Since(state.LastUpdate()) > staleThreshold {
			checkMarkets = append(checkMarkets, market)
		}
	}
	h.mu.RUnlock()

	// Perform snapshot checks
	for _, market := range checkMarkets {
		// Check if context not terminated between checks
		if h.ctx.Err() != nil {
			return checkMarkets
		}
		h.checkMarket(market, "stale")
	}

	return checkMarkets
}

func (h *HealthChecker) performRandomChecks(excludeMarkets []gateway.Market) {
	excludeMarketsMap := make(map[string]struct{})
	for _, market := range excludeMarkets {
		excludeMarketsMap[market.String()] = struct{}{}
	}

	// Get list of eligible markets (not out of sync, not already checked)
	var eligibleMarkets []gateway.Market
	h.mu.RLock()
	for market, state := range h.markets {
		// Market must be in Ready state for a random check
		if state.State() != MarketStateReady {
			continue
		}

		if _, excluded := excludeMarketsMap[market.String()]; !excluded {
			eligibleMarkets = append(eligibleMarkets, market)
		}
	}
	h.mu.RUnlock()

	if len(eligibleMarkets) == 0 {
		return
	}

	// Shuffle markets
	rand.Shuffle(len(eligibleMarkets), func(i, j int) {
		eligibleMarkets[i], eligibleMarkets[j] = eligibleMarkets[j], eligibleMarkets[i]
	})

	// Select percentage of markets to check
	numChecks := int(math.Ceil(float64(len(eligibleMarkets)) * randomCheckPct))
	for i := 0; i < numChecks && i < len(eligibleMarkets); i++ {
		// Check if context not terminated between checks
		if h.ctx.Err() != nil {
			return
		}
		h.checkMarket(eligibleMarkets[i], "random")
	}
}

func (h *HealthChecker) checkMarket(market gateway.Market, source string) {
	log := h.logger.With(
		zap.String("market", market.String()),
		zap.String("source", source),
	)

	// Check if we should continue retrying for this schedule
	if !h.state.shouldRetry() {
		err := fmt.Errorf("reached max retry duration of %v", maxRetryDuration)
		log.Error("health checker max retries exiting", zap.Error(err))
		h.setFatalError(err)
		return
	}

	// If we have a delay from previous failures, wait
	if h.state.nextDelay > 0 {
		select {
		case <-h.ctx.Done():
			return
		case <-time.After(h.state.nextDelay):
		}
	}

	// Check if context terminated
	if h.ctx.Err() != nil {
		return
	}

	snapshot, err := h.gtw.GetDepthBook(market, gateway.DefaultDepthParams)
	if err != nil {
		h.state.updateRetry()
		log.Debug("snapshot check failed, retrying",
			zap.Error(err),
			zap.Duration("next_retry_in", h.state.nextDelay))
		return
	}

	// Check context terminated
	if h.ctx.Err() != nil {
		return
	}

	// Check if market data is in sync
	if h.validateMarketData(market, snapshot) {
		// Update LastCheck time after successful validation
		h.mu.RLock()
		state, exists := h.markets[market]
		h.mu.RUnlock()
		if exists {
			state.SetLastCheck(time.Now())
		}
		return
	}

	// Market is out of sync, send update
	h.updateCh <- HealthUpdate{
		Market: market,
		Source: source,
		Error:  fmt.Errorf("market data out of sync with snapshot"),
	}
	return
}

func (h *HealthChecker) validateMarketData(market gateway.Market, snapshot gateway.DepthBook) bool {
	h.mu.RLock()
	state := h.markets[market]
	h.mu.RUnlock()

	if state == nil {
		return false
	}

	marketBook := state.Book()

	// Get current top of book prices
	bookBid, hasBid := marketBook.Bids.Top()
	bookAsk, hasAsk := marketBook.Asks.Top()

	// Get snapshot top of book prices
	if len(snapshot.Bids) == 0 || len(snapshot.Asks) == 0 || !hasBid || !hasAsk {
		h.logger.Debug("missing top of book prices, skipping check",
			zap.String("market", market.Symbol),
			zap.Bool("has_book_bid", hasBid),
			zap.Bool("has_book_ask", hasAsk),
			zap.Int("snapshot_bids", len(snapshot.Bids)),
			zap.Int("snapshot_asks", len(snapshot.Asks)))
		return true // Skip check if we don't have full data
	}

	snapshotBid := snapshot.Bids[0].Price
	snapshotAsk := snapshot.Asks[0].Price

	// Calculate current book spread
	currentSpread := book.BidAskSpread(bookBid.Value, bookAsk.Value)

	// Calculate maximum allowed divergence based on spread
	currentDivergence := currentSpread * spreadMultiplier

	// Clamp divergence threshold between min and max values
	maxDivergence := math.Max(minDivergenceThreshold, currentDivergence)
	maxDivergence = math.Min(maxDivergenceThreshold, maxDivergence)

	// Calculate price divergence percentages
	bidDivergence := math.Abs(bookBid.Value-snapshotBid) / snapshotBid
	askDivergence := math.Abs(bookAsk.Value-snapshotAsk) / snapshotAsk

	// Get maximum divergence between bid and ask
	observedDivergence := math.Max(bidDivergence, askDivergence)

	if observedDivergence > maxDivergence {
		h.logger.Warn("market data divergence detected",
			zap.String("market", market.Symbol),
			zap.Float64("book_bid", bookBid.Value),
			zap.Float64("book_ask", bookAsk.Value),
			zap.Float64("snapshot_bid", snapshotBid),
			zap.Float64("snapshot_ask", snapshotAsk),
			zap.Float64("current_spread", currentSpread),
			zap.Float64("max_allowed_divergence", maxDivergence),
			zap.Float64("observed_divergence", observedDivergence))
		return false
	}

	return true
}
