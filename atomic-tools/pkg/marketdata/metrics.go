// Package marketdata provides market data management functionality
package marketdata

import (
	"sync"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	defaultMetrics *MarketStateMetrics
	metricsOnce    sync.Once
)

// GetDefaultMetrics returns the default metrics singleton
func GetDefaultMetrics(factory promauto.Factory) *MarketStateMetrics {
	metricsOnce.Do(func() {
		defaultMetrics = NewMarketMetrics(factory)
	})
	return defaultMetrics
}

// IMarketStateMetrics defines the interface for market metrics collection
type IMarketStateMetrics interface {
	TrackMarketState(state *MarketState)
	TrackError(state *MarketState, errorType string)
}

// NoopMarketMetrics implements IMarketStateMetrics with no-op operations
type NoopMarketMetrics struct{}

func NewNoopMarketMetrics() *NoopMarketMetrics {
	return &NoopMarketMetrics{}
}

func (n *NoopMarketMetrics) TrackMarketState(*MarketState)   {}
func (n *NoopMarketMetrics) TrackError(*MarketState, string) {}

// MarketStateMetrics handles all market state related metrics
type MarketStateMetrics struct {
	// Core market state tracking
	marketStateGauge *prometheus.GaugeVec
	errorCounter     *prometheus.CounterVec
}

func NewMarketMetrics(factory promauto.Factory) *MarketStateMetrics {
	return &MarketStateMetrics{
		marketStateGauge: factory.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "market_state",
				Help: "Current market state encoded as integer",
			},
			[]string{"exchange", "market", "current_state"},
		),

		errorCounter: factory.NewCounterVec(
			prometheus.CounterOpts{
				Name: "market_errors_total",
				Help: "Count of market errors by type",
			},
			[]string{"exchange", "market", "error_type"},
		),
	}
}

// TrackMarketState updates the market state gauge
func (m *MarketStateMetrics) TrackMarketState(state *MarketState) {
	// Delete the metric for all other states first
	states := []MarketStateEnum{
		MarketStateQueued,
		MarketStateInitializing,
		MarketStateReady,
		MarketStateOutOfSync,
		MarketStateDisconnected,
		MarketStateFailed,
		MarketStateStopped,
	}

	for _, s := range states {
		curState := state.State()
		if s != curState {
			m.marketStateGauge.Delete(prometheus.Labels{
				"exchange":      state.market.Exchange.Name,
				"market":        state.market.Symbol,
				"current_state": s.String(),
			})
		}
	}

	curState := state.State()
	// Set the current state
	m.marketStateGauge.With(prometheus.Labels{
		"exchange":      state.market.Exchange.Name,
		"market":        state.market.Symbol,
		"current_state": curState.String(),
	}).Set(float64(curState))
}

// TrackError increments the error counter for a given type
func (m *MarketStateMetrics) TrackError(state *MarketState, errorType string) {
	m.errorCounter.With(prometheus.Labels{
		"exchange":   state.market.Exchange.Name,
		"market":     state.market.Symbol,
		"error_type": errorType,
	}).Inc()
}
