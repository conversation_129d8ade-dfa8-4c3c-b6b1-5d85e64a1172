package marketdata

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"net/url"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/safegateway"
	"github.com/herenow/atomic-tools/pkg/book"
	"github.com/herenow/atomic-tools/pkg/logger"
	"go.uber.org/zap"
)

type GatewayMarket struct {
	Market                 gateway.Market
	Manager                *MarketManager
	WaitingInitialSnapshot bool
}

// GatewayInstance manages a single gateway connection and its lifecycle
type GatewayInstance struct {
	exchange      gateway.Exchange
	options       gateway.Options
	gateway       gateway.Gateway
	ctx           context.Context
	cancel        context.CancelFunc
	logger        *logger.Logger
	healthChecker *HealthChecker
	mu            sync.RWMutex

	useSafeGateway    bool
	lastErr           error
	connected         bool
	marketsSubscribed bool
	marketsMap        map[string]GatewayMarket
	totalMarketsInit  int
}

func NewGatewayInstance(
	ctx context.Context,
	logger *logger.Logger,
	id string,
	exchange gateway.Exchange,
	options gateway.Options,
	useSafeGateway bool,
) *GatewayInstance {
	loggerWrapped := logger.With(
		zap.String("component", "gateway_instance"),
		zap.String("gateway_id", id),
	)

	ctx, cancel := context.WithCancel(ctx)

	// Randomize proxy list before creating the gateway instance
	// This is to avoid all instances using the same proxy if sorted by order
	// Also, cap the number of proxies to 5 to avoid testing all proxies every time
	// a new gateway instance is created.
	if len(options.Proxies) > 0 {
		// Make a copy of the proxy slice to avoid modifying the original
		shuffledProxies := make([]*url.URL, len(options.Proxies))
		copy(shuffledProxies, options.Proxies)

		// Use crypto/rand seed for better randomization
		rand.Seed(time.Now().UnixNano())
		rand.Shuffle(len(shuffledProxies), func(i, j int) {
			shuffledProxies[i], shuffledProxies[j] = shuffledProxies[j], shuffledProxies[i]
		})

		if len(shuffledProxies) > 5 {
			shuffledProxies = shuffledProxies[:5]
		}

		options.Proxies = shuffledProxies
	}

	inst := &GatewayInstance{
		exchange:       exchange,
		options:        options,
		useSafeGateway: useSafeGateway,
		ctx:            ctx,
		cancel:         cancel,
		logger:         loggerWrapped,
		marketsMap:     make(map[string]GatewayMarket),
	}

	return inst
}

func (g *GatewayInstance) initHealthChecker(markets map[gateway.Market]*MarketState) {
	g.healthChecker = NewHealthChecker(
		g.ctx,
		g.logger,
		g.gateway,
		markets,
	)

	// Process health updates
	go func() {
		for {
			// Safety check if the gateway instance is closed
			if g.ctx.Err() != nil {
				return
			}

			update, err := g.healthChecker.NextUpdate()
			if err != nil {
				g.handleHealthCheckerTermination(err)
				return
			}

			g.handleOutOfSyncMarket(update.Market, update.Source)
		}
	}()

	// Start the health checker
	go g.healthChecker.Start()
}

// If the health checker fails, we should mark all markets as failed and close ourselves
func (g *GatewayInstance) handleHealthCheckerTermination(err error) {
	// This is a fatal error, we should close the gateway instance
	if !errors.Is(err, ErrHealthCheckerClosed) {
		g.logger.Error("health checker failed, closing gateway instance",
			zap.Error(err))

		// Set all markets as failed and remove them
		g.mu.Lock()
		for symbol, gtwMkt := range g.marketsMap {
			marketManager := gtwMkt.Manager
			marketManager.GetState().SetFailed(err)
			marketManager.ReleaseGatewayInstance(g) // Release gateway instance
			delete(g.marketsMap, symbol)
		}
		g.mu.Unlock()
	}

	g.Close()
}

// If a single market is out of sync, we should mark it as out of sync and stop processing it
// If too many markets are out of sync, we should close the gateway instance
func (g *GatewayInstance) handleOutOfSyncMarket(market gateway.Market, source string) {
	g.mu.Lock()
	gtwMkt, exists := g.marketsMap[market.Symbol]
	if exists {
		delete(g.marketsMap, market.Symbol) // Remove market from map

		// Total markets still running and removed
		totalInit := g.totalMarketsInit
		totalRunning := len(g.marketsMap)
		totalRemoved := g.totalMarketsInit - totalRunning
		g.mu.Unlock()

		// Update market state and stop checking it
		marketManager := gtwMkt.Manager
		marketManager.GetState().SetOutOfSync(fmt.Errorf("market data out of sync with snapshot"))
		marketManager.ReleaseGatewayInstance(g) // Release gateway instance to allow re-subscription
		g.healthChecker.RemoveMarket(market)

		// Stop the gateway instance if:
		// Tracking more than 10 markets and more than 50% of markets are out of sync
		// All markets are out of sync
		if totalRunning == 0 || (totalInit > 10 && totalRemoved >= totalInit/2) {
			g.logger.Warn("stopping gateway instance too many markets out of sync",
				zap.Int("total_removed_markets", totalRemoved),
				zap.Int("still_running_markets", totalRunning))
			g.Close()
		}
	} else {
		g.mu.Unlock()
	}
}

func (g *GatewayInstance) Connect(ctx context.Context) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.connected {
		return fmt.Errorf("gateway already connected")
	}

	if g.useSafeGateway {
		return g.connectSafeGateway(ctx)
	}

	return g.connectGateway(ctx)
}

func (g *GatewayInstance) connectGateway(_ context.Context) error {
	gtw, ok := gateway.NewByExchange(g.exchange, g.options)
	if !ok {
		return fmt.Errorf("failed to create gateway exchange \"%s\" not found", g.exchange)
	}

	if err := gtw.Connect(); err != nil {
		return fmt.Errorf("failed to connect gateway: %w", err)
	}

	g.connected = true
	g.gateway = gtw

	return nil
}

func (g *GatewayInstance) connectSafeGateway(ctx context.Context) error {
	done := make(chan error)
	gtw := safegateway.NewSafeGateway(
		g.exchange,
		g.options,
		safegateway.SafeGatewayOptions{
			OnDisconnect: g.handleDisconnect,
			OnConnect: func() {
				close(done)
			},
		},
	)

	// This could potentially block for a long time, that's why we run it in a goroutine
	go func() {
		if err := gtw.Connect(); err != nil {
			done <- fmt.Errorf("failed to connect gateway: %w", err)
		}
	}()

	g.gateway = gtw

	timeoutCtx, cancel := context.WithTimeout(ctx, 15*time.Second)
	defer cancel()

	select {
	case <-timeoutCtx.Done():
		go g.Close() // Clean up async
		return fmt.Errorf("failed to create gateway ctx.Done: %w", ctx.Err())
	case err := <-done:
		if err != nil {
			return err
		}
	}

	g.connected = true

	go g.processMarketData()

	return nil
}

func (g *GatewayInstance) closeGateway() error {
	if err := g.gateway.Close(); err != nil {
		g.logger.Error("failed to close gateway instance", zap.Error(err))
		return err
	}

	return nil
}

func (g *GatewayInstance) handleDisconnect(err error) {
	g.mu.Lock()
	g.connected = false
	g.mu.Unlock()

	g.logger.Warn("gateway instance disconnected",
		zap.Error(err))

	// Close the gateway instance
	if errCloser := g.Close(); errCloser != nil {
		g.logger.Error("failed to close gateway instance", zap.Error(errCloser))
	}
}

// SubscribeMarkets attempts to connect gateway and subscribe markets
func (g *GatewayInstance) SubscribeMarkets(ctx context.Context, gtwMarkets []GatewayMarket) error {
	if !g.connected {
		return fmt.Errorf("gateway not connected")
	}

	if g.marketsSubscribed {
		return fmt.Errorf("markets already subscribed, currently not supported to resubscribe markets")
	}

	markets := make([]gateway.Market, 0, len(gtwMarkets))
	for _, gtwMarket := range gtwMarkets {
		// Associate gateway instance with market manager
		// This is for sanity checks and to avoid double associations
		if err := gtwMarket.Manager.AssociateGatewayInstance(g); err != nil {
			g.logger.Error("failed to associate gateway instance with market manager", zap.Error(err))
		} else {
			// Set the market as waiting for initial snapshot
			gtwMarket.WaitingInitialSnapshot = true
			markets = append(markets, gtwMarket.Market)
			g.marketsMap[gtwMarket.Market.Symbol] = gtwMarket
		}
	}
	g.totalMarketsInit = len(markets)

	// This could potentially block for a long time, that's why we run it in a goroutine
	done := make(chan error)
	go func() {
		err := g.gateway.SubscribeMarkets(markets)
		done <- err
	}()

	ctx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()

	select {
	case <-ctx.Done():
		go g.Close() // Clean up async
		return fmt.Errorf("failed to subscribe markets ctx.Done: %w", ctx.Err())
	case err := <-done:
		if err != nil {
			go g.Close() // Clean up async
			return err
		}
	}

	g.marketsSubscribed = true

	// After successful subscription, initialize health checker
	marketStates := make(map[gateway.Market]*MarketState)
	for _, gtwMarket := range gtwMarkets {
		marketStates[gtwMarket.Market] = gtwMarket.Manager.GetState()
	}

	// Start the health checker
	go g.initHealthChecker(marketStates)

	return nil
}

// Close cleanly shuts down the gateway
func (g *GatewayInstance) Close() error {
	// Disconnect all remaining markets
	g.mu.Lock()
	for symbol, gtwMkt := range g.marketsMap {
		marketManager := gtwMkt.Manager
		marketManager.GetState().SetDisconnected(fmt.Errorf("gateway instance closing"))
		marketManager.ReleaseGatewayInstance(g) // Release gateway instance
		delete(g.marketsMap, symbol)
	}
	g.mu.Unlock()

	// Closes the gateway instance and its dependencies
	g.cancel()

	if err := g.closeGateway(); err != nil {
		return err
	}

	return nil
}

func (g *GatewayInstance) processMarketData() {
	for {
		select {
		case <-g.ctx.Done():
			return
		case tick := <-g.gateway.Tick():
			g.processTick(tick)
		}
	}
}

func (g *GatewayInstance) processTick(tick gateway.Tick) {
	g.mu.RLock()
	if !g.connected {
		g.mu.RUnlock()
		g.logger.Warn("received tick while disconnected")
		return
	}
	g.mu.RUnlock()

	eventsByMarket := gateway.EventsBySymbol(tick.EventLog)

	for symbol, events := range eventsByMarket {
		// Get the market manager for this symbol
		g.mu.RLock()
		gtwMkt, exists := g.marketsMap[symbol]
		g.mu.RUnlock()
		if !exists {
			continue
		}

		waitingSnapshot := gtwMkt.WaitingInitialSnapshot
		mgr := gtwMkt.Manager

		update := MarketUpdate{
			Timestamp:  tick.ReceivedTimestamp,
			IsSnapshot: false,
			Bids:       make([]book.Price, 0),
			Asks:       make([]book.Price, 0),
			Trades:     make([]gateway.Trade, 0),
		}

		// Process events
		for _, event := range events {
			switch event.Type {
			case gateway.SnapshotSequenceEvent:
				update.IsSnapshot = true
			case gateway.DepthEvent:
				depth := event.Data.(gateway.Depth)
				price := book.Price{
					Value:  depth.Price,
					Amount: depth.Amount,
				}

				if depth.Side == gateway.Bid {
					update.Bids = append(update.Bids, price)
				} else {
					update.Asks = append(update.Asks, price)
				}
			case gateway.TradeEvent:
				trade := event.Data.(gateway.Trade)
				update.Trades = append(update.Trades, trade)
			}
		}

		// Process update if we have changes or trades
		if update.IsSnapshot || len(update.Bids) > 0 || len(update.Asks) > 0 || len(update.Trades) > 0 {
			mgr.ProcessUpdate(update)

			if waitingSnapshot && (len(update.Bids) > 0 || len(update.Asks) > 0) {
				mgr.GetState().SetState(MarketStateReady, nil)
				gtwMkt.WaitingInitialSnapshot = false
				g.mu.Lock()
				g.marketsMap[symbol] = gtwMkt
				g.mu.Unlock()
			}
		}
	}
}
