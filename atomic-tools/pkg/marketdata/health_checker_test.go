package marketdata

import (
	"context"
	"testing"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-tools/pkg/book"
	"github.com/herenow/atomic-tools/pkg/logger"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

type mockGateway struct {
	base.Gateway
	mock.Mock
}

func (m *mockGateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	args := m.Called(market, params)
	return args.Get(0).(gateway.DepthBook), args.Error(1)
}

type mockCallback struct {
	mock.Mock
}

func (m *mockCallback) OnMarketOutOfSync(market gateway.Market, source string) {
	m.Called(market, source)
}

func setStateReady(state *MarketState) {
	// Follow proper state transition
	state.SetState(MarketStateInitializing, nil)
	state.SetState(MarketStateReady, nil)
}

func TestHealthChecker_StaleMarketDetection(t *testing.T) {
	// Setup
	ctx := context.Background()
	mockGtw := &mockGateway{}
	mockCb := &mockCallback{}
	logger, _ := logger.New(ctx, logger.Config{})

	market := gateway.Market{Symbol: "BTC/USDT"}
	mktMetrics := &NoopMarketMetrics{}
	marketState := NewMarketState(market, logger, mktMetrics)
	markets := map[gateway.Market]*MarketState{
		market: marketState,
	}

	// Set proper state flow
	setStateReady(marketState)

	checker := NewHealthChecker(ctx, logger, mockGtw, markets)

	// Set up mock response
	mockGtw.On("GetDepthBook", market, gateway.DefaultDepthParams).Return(
		gateway.DepthBook{
			Bids: []gateway.PriceLevel{{Price: 30000, Amount: 1.0}},
			Asks: []gateway.PriceLevel{{Price: 30100, Amount: 1.0}},
		},
		nil,
	)

	// Set market book state with significant divergence
	marketState.Book().Snapshot(
		[]book.Price{{Value: 30500, Amount: 1.0}}, // Much higher divergence
		[]book.Price{{Value: 30600, Amount: 1.0}},
	)

	// Set last update to trigger stale check
	marketState.SetLastUpdate(time.Now().Add(-31 * time.Second))

	// Mock callback expectation
	mockCb.On("OnMarketOutOfSync", market, "stale").Once()

	// Run stale check
	checkedMarkets := checker.checkStaleMarkets()
	checkedMarketsSymbol := getMarketSymbols(checkedMarkets)

	// Verify
	assert.Contains(t, checkedMarketsSymbol, market.Symbol)
	mockCb.AssertExpectations(t)
}

func getMarketSymbols(markets []gateway.Market) []string {
	symbols := make([]string, 0)
	for _, market := range markets {
		symbols = append(symbols, market.Symbol)
	}
	return symbols
}

func TestHealthChecker_ValidateMarketData(t *testing.T) {
	tests := []struct {
		name        string
		bookPrices  struct{ bid, ask float64 }
		snapPrices  struct{ bid, ask float64 }
		expectValid bool
	}{
		{
			name: "within spread-based threshold",
			bookPrices: struct{ bid, ask float64 }{
				bid: 30000,
				ask: 30100,
			},
			snapPrices: struct{ bid, ask float64 }{
				bid: 30010,
				ask: 30090,
			},
			expectValid: true,
		},
		{
			name: "exceeds spread-based threshold",
			bookPrices: struct{ bid, ask float64 }{
				bid: 30000,
				ask: 30100,
			},
			snapPrices: struct{ bid, ask float64 }{
				bid: 31000, // Much larger divergence
				ask: 31100,
			},
			expectValid: false,
		},
		{
			name: "respects minimum threshold",
			bookPrices: struct{ bid, ask float64 }{
				bid: 100.00,
				ask: 100.01, // 0.01% spread
			},
			snapPrices: struct{ bid, ask float64 }{
				bid: 100.00,
				ask: 100.50, // 0.5% divergence, above min threshold
			},
			expectValid: false,
		},
		{
			name: "respects maximum threshold",
			bookPrices: struct{ bid, ask float64 }{
				bid: 100.00,
				ask: 200.00, // 100% spread
			},
			snapPrices: struct{ bid, ask float64 }{
				bid: 100.00,
				ask: 300.00, // Would exceed max threshold even with large spread
			},
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			mockGtw := &mockGateway{}
			logger, _ := logger.New(ctx, logger.Config{})

			market := gateway.Market{Symbol: "TEST"}
			mktMetrics := &NoopMarketMetrics{}
			marketState := NewMarketState(market, logger, mktMetrics)
			markets := map[gateway.Market]*MarketState{
				market: marketState,
			}

			// Set proper state flow
			setStateReady(marketState)

			checker := NewHealthChecker(ctx, logger, mockGtw, markets)

			// Set book state
			marketState.Book().Snapshot(
				[]book.Price{{Value: tt.bookPrices.bid, Amount: 1.0}},
				[]book.Price{{Value: tt.bookPrices.ask, Amount: 1.0}},
			)

			// Create snapshot
			snapshot := gateway.DepthBook{
				Bids: []gateway.PriceLevel{{Price: tt.snapPrices.bid, Amount: 1.0}},
				Asks: []gateway.PriceLevel{{Price: tt.snapPrices.ask, Amount: 1.0}},
			}

			// Validate
			isValid := checker.validateMarketData(market, snapshot)
			assert.Equal(t, tt.expectValid, isValid)
		})
	}
}

func TestHealthChecker_IgnoresNonReadyMarkets(t *testing.T) {
	ctx := context.Background()
	mockGtw := &mockGateway{}
	mockCb := &mockCallback{}
	logger, _ := logger.New(ctx, logger.Config{})

	// Create
	// Create markets in different states
	markets := make(map[gateway.Market]*MarketState)

	// Ready market
	readyMarket := gateway.Market{Symbol: "READY"}
	mktMetrics := &NoopMarketMetrics{}
	readyState := NewMarketState(readyMarket, logger, mktMetrics)
	setStateReady(readyState)
	readyState.SetLastUpdate(time.Now().Add(-31 * time.Second))
	markets[readyMarket] = readyState

	// Initializing market
	initializingMarket := gateway.Market{Symbol: "INITIALIZING"}
	initializingState := NewMarketState(initializingMarket, logger, mktMetrics)
	initializingState.SetState(MarketStateInitializing, nil)
	initializingState.SetLastUpdate(time.Now().Add(-31 * time.Second))
	markets[initializingMarket] = initializingState

	checker := NewHealthChecker(ctx, logger, mockGtw, markets)

	// Mock response only for ready market
	mockGtw.On("GetDepthBook", readyMarket, gateway.DefaultDepthParams).Return(
		gateway.DepthBook{
			Bids: []gateway.PriceLevel{{Price: 100, Amount: 1.0}},
			Asks: []gateway.PriceLevel{{Price: 101, Amount: 1.0}},
		},
		nil,
	).Maybe()

	// Mock callback expectation for ready market only
	mockCb.On("OnMarketOutOfSync", readyMarket, "stale").Once()

	// Run stale check
	checkedMarkets := checker.checkStaleMarkets()
	checkedMarketsSymbol := getMarketSymbols(checkedMarkets)

	// Verify only ready market was checked
	assert.Contains(t, checkedMarketsSymbol, readyMarket.Symbol)
	assert.NotContains(t, checkedMarketsSymbol, initializingMarket.Symbol)
}
