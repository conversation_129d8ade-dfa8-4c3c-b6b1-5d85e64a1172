package asset_translations

import (
	"testing"
)

func TestParseTranslation(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		want        Translation
		expectError bool
	}{
		{
			name:  "exact translation",
			input: "Bitso:POL=Binance:MATIC",
			want: Translation{
				FromExchange: "Bitso",
				FromAsset:    "POL",
				ToExchange:   "Binance",
				ToAsset:      "MATIC",
			},
			expectError: false,
		},
		{
			name:  "wildcard translation",
			input: "Bitso:USD=*:USDC",
			want: Translation{
				FromExchange: "Bitso",
				FromAsset:    "USD",
				ToExchange:   "*",
				ToAsset:      "USDC",
			},
			expectError: false,
		},
		{
			name:        "invalid format",
			input:       "invalid",
			expectError: true,
		},
		{
			name:        "wildcard assets are not allowed",
			input:       "Bitso:*=Binance:MATIC",
			expectError: true,
		},
		{
			name:        "wildcard assets are not allowed [2]",
			input:       "Bitso:POL=Binance:*",
			expectError: true,
		},
		{
			name:        "wildcard assets are not allowed [3]",
			input:       "Bitso:*=Binance:*",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseTranslation(tt.input)
			if (err != nil) != tt.expectError {
				t.Errorf("ParseTranslation() error = %v, expectError %v", err, tt.expectError)
				return
			}
			if !tt.expectError && got != tt.want {
				t.Errorf("ParseTranslation() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTranslator(t *testing.T) {
	translator := New()
	rules := []string{
		"Bitso:POL=Binance:MATIC",
		"Bitso:USD=*:USDC",
		"*:ETH2=*:ETH",
	}

	err := translator.LoadTranslations(rules)
	if err != nil {
		t.Fatalf("LoadTranslations() error = %v", err)
	}

	tests := []struct {
		name         string
		fromExchange string
		fromAsset    string
		toExchange   string
		wantAsset    string
		wantFound    bool
	}{
		{
			name:         "exact match forward",
			fromExchange: "Bitso",
			fromAsset:    "POL",
			toExchange:   "Binance",
			wantAsset:    "MATIC",
			wantFound:    true,
		},
		{
			name:         "exact match reverse",
			fromExchange: "Binance",
			fromAsset:    "MATIC",
			toExchange:   "Bitso",
			wantAsset:    "POL",
			wantFound:    true,
		},
		{
			name:         "wildcard target exchange forward",
			fromExchange: "Bitso",
			fromAsset:    "USD",
			toExchange:   "Binance",
			wantAsset:    "USDC",
			wantFound:    true,
		},
		{
			name:         "wildcard target exchange reverse",
			fromExchange: "Binance",
			fromAsset:    "USDC",
			toExchange:   "Bitso",
			wantAsset:    "USD",
			wantFound:    true,
		},
		{
			name:         "wildcard source and target forward",
			fromExchange: "Kraken",
			fromAsset:    "ETH2",
			toExchange:   "Binance",
			wantAsset:    "ETH",
			wantFound:    true,
		},
		{
			name:         "wildcard source and target reverse",
			fromExchange: "Kraken",
			fromAsset:    "ETH",
			toExchange:   "Binance",
			wantAsset:    "ETH2",
			wantFound:    true,
		},
		{
			name:         "no match",
			fromExchange: "Bitso",
			fromAsset:    "BTC",
			toExchange:   "Binance",
			wantAsset:    "BTC",
			wantFound:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotAsset, gotFound := translator.FindTranslation(tt.fromExchange, tt.fromAsset, tt.toExchange)
			if gotAsset != tt.wantAsset {
				t.Errorf("FindTranslation() gotAsset = %v, want %v", gotAsset, tt.wantAsset)
			}
			if gotFound != tt.wantFound {
				t.Errorf("FindTranslation() gotFound = %v, want %v", gotFound, tt.wantFound)
			}
		})
	}
}
