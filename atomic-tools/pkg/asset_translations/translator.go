package asset_translations

import (
	"fmt"
	"strings"
)

type Translation struct {
	FromExchange string
	FromAsset    string
	ToExchange   string
	ToAsset      string
}

func (t Translation) String() string {
	fromPart := fmt.Sprintf("%s:%s", t.FromExchange, t.FromAsset)
	toPart := fmt.Sprintf("%s:%s", t.<PERSON>, t.<PERSON>)
	return fmt.Sprintf("%s=%s", fromPart, toPart)
}

// Matches checks if this translation matches the given from/to parameters in either direction
func (t Translation) Matches(fromExchange, fromAsset, toExchange string) (string, bool) {
	// Check forward direction
	if (t.FromExchange == fromExchange || t.FromExchange == "*") &&
		t.FromAsset == fromAsset &&
		(t.ToExchange == toExchange || t.ToExchange == "*") {
		return t.ToAsset, true
	}

	// Check reverse direction
	if (t.ToExchange == fromExchange || t.ToExchange == "*") &&
		t.To<PERSON> == fromAsset &&
		(t.FromExchange == toExchange || t.FromExchange == "*") {
		return t.FromAsset, true
	}

	return "", false
}

type Translator struct {
	translations []Translation
}

func New() *Translator {
	return &Translator{
		translations: make([]Translation, 0),
	}
}

// LoadTranslations loads a list of translation rules
// Format: "Exchange:Asset=Exchange:Asset"
// Use * as wildcard for exchange
func (t *Translator) LoadTranslations(rules []string) error {
	for _, rule := range rules {
		translation, err := ParseTranslation(rule)
		if err != nil {
			return fmt.Errorf("parse translation rule %s: %w", rule, err)
		}
		t.translations = append(t.translations, translation)
	}
	return nil
}

func ParseTranslation(rule string) (Translation, error) {
	parts := strings.Split(rule, "=")
	if len(parts) != 2 {
		return Translation{}, fmt.Errorf("invalid translation rule format, expected Exchange:Asset=Exchange:Asset")
	}

	from := strings.Split(parts[0], ":")
	to := strings.Split(parts[1], ":")

	if len(from) != 2 || len(to) != 2 {
		return Translation{}, fmt.Errorf("invalid translation rule format, expected Exchange:Asset=Exchange:Asset")
	}

	// Prevent asset wildcards
	if from[1] == "*" || to[1] == "*" {
		return Translation{}, fmt.Errorf("wildcards (*) not allowed for assets in translation rule: %s", rule)
	}

	return Translation{
		FromExchange: from[0],
		FromAsset:    from[1],
		ToExchange:   to[0],
		ToAsset:      to[1],
	}, nil
}

// FindTranslation finds a translation for the given asset in fromExchange to toExchange
// It returns the translated asset name and whether a translation was found
func (t *Translator) FindTranslation(fromExchange, fromAsset, toExchange string) (string, bool) {
	// Check for any matching translation rules (exact or wildcard)
	for _, trans := range t.translations {
		if asset, found := trans.Matches(fromExchange, fromAsset, toExchange); found {
			return asset, true
		}
	}

	// No translation found, return original asset
	return fromAsset, false
}

// ResetTranslations removes all loaded translations
func (t *Translator) ResetTranslations() {
	t.translations = make([]Translation, 0)
}
