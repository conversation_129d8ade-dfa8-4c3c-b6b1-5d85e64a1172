package exchange_path_finder

import (
	"testing"

	"github.com/herenow/atomic-gtw/gateway"
)

// Test markets setup covering major trading pairs and some exotic ones
var allTestMarkets = []gateway.Market{
	// Major crypto pairs
	{
		Symbol: "BTC-USDT",
		Pair:   gateway.Pair{Base: "BTC", Quote: "USDT"},
	},
	{
		Symbol: "ETH-USDT",
		Pair:   gateway.Pair{Base: "ETH", Quote: "USDT"},
	},
	{
		Symbol: "ETH-BTC",
		Pair:   gateway.Pair{Base: "ETH", Quote: "BTC"},
	},
	// Stablecoin pairs
	{
		Symbol: "USDC-USDT",
		Pair:   gateway.Pair{Base: "USDC", Quote: "USDT"},
	},
	{
		Symbol: "DAI-USDT",
		Pair:   gateway.Pair{Base: "DAI", Quote: "USDT"},
	},
	// Altcoin pairs
	{
		Symbol: "SOL-USDT",
		Pair:   gateway.Pair{Base: "SOL", Quote: "USDT"},
	},
	{
		Symbol: "SOL-BTC",
		Pair:   gateway.Pair{Base: "SOL", Quote: "BTC"},
	},
	{
		Symbol: "PEPE-USDT",
		Pair:   gateway.Pair{Base: "PEPE", Quote: "USDT"},
	},
	// Fiat pairs
	{
		Symbol: "BTC-BRL",
		Pair:   gateway.Pair{Base: "BTC", Quote: "BRL"},
	},
	{
		Symbol: "USDT-BRL",
		Pair:   gateway.Pair{Base: "USDT", Quote: "BRL"},
	},
	// Closed market
	{
		Symbol: "LUNA-USDT",
		Pair:   gateway.Pair{Base: "LUNA", Quote: "USDT"},
		Closed: true,
	},
}

func TestDifferentStepPaths(t *testing.T) {
	finder := New()
	finder.LoadMarkets(allTestMarkets)

	tests := []struct {
		name          string
		fromAsset     string
		toAsset       string
		maxSteps      int
		expectedPaths int
	}{
		{
			name:          "Direct conversion only",
			fromAsset:     "BTC",
			toAsset:       "USDT",
			maxSteps:      1,
			expectedPaths: 1,
		},
		{
			name:          "Two step conversion",
			fromAsset:     "SOL",
			toAsset:       "BRL",
			maxSteps:      2,
			expectedPaths: 2, // Through USDT->BRL and BTC->BRL
		},
		{
			name:          "Three step conversion",
			fromAsset:     "PEPE",
			toAsset:       "BRL",
			maxSteps:      3,
			expectedPaths: 1, // Only through USDT->BRL
		},
		{
			name:          "Zero steps",
			fromAsset:     "BTC",
			toAsset:       "USDT",
			maxSteps:      0,
			expectedPaths: 0,
		},
		{
			name:          "Negative steps",
			fromAsset:     "BTC",
			toAsset:       "USDT",
			maxSteps:      -1,
			expectedPaths: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			paths := finder.FindPaths(tt.fromAsset, tt.toAsset, tt.maxSteps)

			if len(paths) != tt.expectedPaths {
				t.Errorf("Expected %d paths, got %d\nFound paths:\n%v", tt.expectedPaths, len(paths), paths)
			}

			// Check that no path exceeds max steps
			for _, path := range paths {
				if len(path.Steps) > tt.maxSteps {
					t.Errorf("Path exceeds maximum steps: got %d, max %d", len(path.Steps), tt.maxSteps)
				}
			}
		})
	}
}

func TestBasicPathFinding(t *testing.T) {
	finder := New()
	finder.LoadMarkets(allTestMarkets)

	tests := []struct {
		name          string
		fromAsset     string
		toAsset       string
		maxSteps      int
		expectedPaths int
		validatePaths func(t *testing.T, paths []ConversionPath)
	}{
		{
			name:          "Direct path",
			fromAsset:     "BTC",
			toAsset:       "USDT",
			maxSteps:      1,
			expectedPaths: 1,
			validatePaths: func(t *testing.T, paths []ConversionPath) {
				if len(paths) != 1 || len(paths[0].Steps) != 1 {
					t.Errorf("Expected single step path")
				}
				if paths[0].Steps[0].Market.Symbol != "BTC-USDT" {
					t.Errorf("Expected BTC-USDT path, got %s", paths[0].Steps[0].Market.Symbol)
				}
			},
		},
		{
			name:          "Two possible paths",
			fromAsset:     "SOL",
			toAsset:       "BRL",
			maxSteps:      2,
			expectedPaths: 2, // Through USDT->BRL and BTC->BRL
			validatePaths: func(t *testing.T, paths []ConversionPath) {
				if len(paths) != 2 {
					t.Errorf("Expected 2 paths, got %d", len(paths))
				}
				for _, path := range paths {
					if len(path.Steps) != 2 {
						t.Errorf("Expected 2 steps, got %d", len(path.Steps))
					}
				}
			},
		},
		{
			name:          "Single path through USDT",
			fromAsset:     "PEPE",
			toAsset:       "BRL",
			maxSteps:      4,
			expectedPaths: 1, // Only PEPE->USDT->BRL is possible
			validatePaths: func(t *testing.T, paths []ConversionPath) {
				if len(paths) != 1 {
					t.Errorf("Expected 1 path, got %d", len(paths))
				}
				if len(paths) > 0 {
					if len(paths[0].Steps) != 2 {
						t.Errorf("Expected 2 steps, got %d", len(paths[0].Steps))
					}
					// Verify the exact path
					expectedPath := []struct {
						symbol string
						isBuy  bool
					}{
						{"PEPE-USDT", false}, // SELL PEPE-USDT
						{"USDT-BRL", false},  // SELL USDT-BRL
					}
					for i, expectedStep := range expectedPath {
						if paths[0].Steps[i].Market.Symbol != expectedStep.symbol {
							t.Errorf("Step %d: Expected %s, got %s", i+1, expectedStep.symbol, paths[0].Steps[i].Market.Symbol)
						}
						if paths[0].Steps[i].IsBuy != expectedStep.isBuy {
							t.Errorf("Step %d: Expected isBuy=%v, got %v", i+1, expectedStep.isBuy, paths[0].Steps[i].IsBuy)
						}
					}
				}
			},
		},
		{
			name:          "Same asset",
			fromAsset:     "BTC",
			toAsset:       "BTC",
			maxSteps:      3,
			expectedPaths: 0,
		},
		{
			name:          "Invalid steps",
			fromAsset:     "BTC",
			toAsset:       "USDT",
			maxSteps:      0,
			expectedPaths: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			paths := finder.FindPaths(tt.fromAsset, tt.toAsset, tt.maxSteps)

			if len(paths) != tt.expectedPaths {
				t.Errorf("Expected %d paths, got %d", tt.expectedPaths, len(paths))
				for i, path := range paths {
					t.Logf("Path %d: %v", i+1, path)
				}
			}

			if tt.validatePaths != nil {
				tt.validatePaths(t, paths)
			}
		})
	}
}

func TestRedundantPaths(t *testing.T) {
	finder := New()
	finder.LoadMarkets(allTestMarkets)

	// Test ETH -> BRL which could have redundant paths:
	// 1. ETH -> USDT -> BRL
	// 2. ETH -> BTC -> USDT -> BRL (redundant, longer version of path 1)
	// 3. ETH -> BTC -> BRL
	paths := finder.FindPaths("ETH", "BRL", 4)

	// We expect only the two optimal paths
	expectedPaths := 2 // ETH->USDT->BRL and ETH->BTC->BRL

	if len(paths) != expectedPaths {
		t.Errorf("Expected %d paths, got %d paths:", expectedPaths, len(paths))
		for i, path := range paths {
			t.Logf("Path %d: %v", i+1, path)
		}
	}

	// Check no path is longer than necessary
	for _, path := range paths {
		if len(path.Steps) > 2 {
			t.Errorf("Found redundant path: %v", path)
		}
	}

	// Verify we got the expected paths
	foundPaths := make(map[string]bool)
	for _, path := range paths {
		// Create a signature of the path
		signature := ""
		for _, step := range path.Steps {
			signature += step.Market.Symbol + " "
		}
		foundPaths[signature] = true
	}

	// Check for expected path signatures
	expectedSignatures := []string{
		"ETH-USDT USDT-BRL ", // Path through USDT
		"ETH-BTC BTC-BRL ",   // Path through BTC
	}

	for _, expectedSig := range expectedSignatures {
		if !foundPaths[expectedSig] {
			t.Errorf("Expected path not found: %s", expectedSig)
		}
	}
}

func TestPathValidation(t *testing.T) {
	finder := New()
	finder.LoadMarkets(allTestMarkets)

	// Test SOL -> BRL paths specifically for correct buy/sell operations
	paths := finder.FindPaths("SOL", "BRL", 3)

	for _, path := range paths {
		// Validate each step connects properly to the next
		currentAsset := path.FromAsset
		for i, step := range path.Steps {
			if step.IsBuy {
				// When buying, we should have the quote currency and receive the base
				if step.Market.Pair.Quote != currentAsset {
					t.Errorf("Invalid path at step %d: cannot buy %s with %s", i, step.Market.Symbol, currentAsset)
				}
				currentAsset = step.Market.Pair.Base
			} else {
				// When selling, we should have the base currency and receive the quote
				if step.Market.Pair.Base != currentAsset {
					t.Errorf("Invalid path at step %d: cannot sell %s with %s", i, step.Market.Symbol, currentAsset)
				}
				currentAsset = step.Market.Pair.Quote
			}
		}

		// Verify we ended up with the target asset
		if currentAsset != path.ToAsset {
			t.Errorf("Path ends with %s instead of target asset %s", currentAsset, path.ToAsset)
		}
	}
}

func TestClosedMarkets(t *testing.T) {
	finder := New()
	finder.LoadMarkets(allTestMarkets)

	// Try to find paths involving LUNA which has a closed market
	paths := finder.FindPaths("LUNA", "USDT", 3)
	if len(paths) != 0 {
		t.Error("Expected no paths for closed market")
	}

	// Verify number of loaded markets is less than total markets due to filtering
	expectedOpenMarkets := len(allTestMarkets) - 1 // -1 for LUNA-USDT
	if len(finder.markets) != expectedOpenMarkets {
		t.Errorf("Expected %d open markets, got %d", expectedOpenMarkets, len(finder.markets))
	}
}
