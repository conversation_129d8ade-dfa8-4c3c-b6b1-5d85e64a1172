package exchange_path_finder

import (
	"fmt"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
)

type ConversionStep struct {
	Market gateway.Market
	IsBuy  bool // true = buy order, false = sell order
}

func (s ConversionStep) String() string {
	action := "SELL"
	if s.<PERSON>Buy {
		action = "BUY"
	}
	return fmt.Sprintf("%s %s", action, s.Market)
}

type ConversionPath struct {
	Steps     []ConversionStep
	FromAsset string
	ToAsset   string
}

func (p ConversionPath) String() string {
	str := fmt.Sprintf("Convert %s -> %s (%d steps):\n", p.FromAsset, p.ToAsset, len(p.Steps))
	for i, step := range p.Steps {
		str += fmt.Sprintf("  %d. %s\n", i+1, step)
	}
	return str
}

// pathKey generates a key representing the path's steps
// We use this to detect redundant paths
func (p ConversionPath) pathKey(upToStep int) string {
	if upToStep > len(p.Steps) {
		upToStep = len(p.Steps)
	}

	parts := make([]string, upToStep)
	for i := 0; i < upToStep; i++ {
		step := p.Steps[i]
		action := "SELL"
		if step.IsBuy {
			action = "BUY"
		}
		parts[i] = fmt.Sprintf("%s %s", action, step.Market)
	}
	return strings.Join(parts, "|")
}

type AssetTranslator interface {
	FindTranslation(fromExchange, asset, toExchange string) (string, bool)
}

type PathFinder struct {
	markets    []gateway.Market
	translator AssetTranslator
}

func New() *PathFinder {
	return &PathFinder{
		markets: make([]gateway.Market, 0),
	}
}

func (f *PathFinder) SetTranslator(t AssetTranslator) {
	f.translator = t
}

func (f *PathFinder) marketExists(market gateway.Market) bool {
	for _, m := range f.markets {
		if m.Symbol == market.Symbol && m.Exchange == market.Exchange {
			return true
		}
	}
	return false
}

func (f *PathFinder) LoadMarkets(markets []gateway.Market) {
	for _, market := range markets {
		if !market.Closed && !f.marketExists(market) {
			f.markets = append(f.markets, market)
		}
	}
}

func (f *PathFinder) ResetMarkets() {
	f.markets = make([]gateway.Market, 0)
}

func (f *PathFinder) translateAsset(fromExchange, asset, toExchange string) string {
	if f.translator == nil {
		return asset
	}
	translated, _ := f.translator.FindTranslation(fromExchange, asset, toExchange)
	return translated
}

func (f *PathFinder) FindPaths(fromAsset, toAsset string, maxSteps int) []ConversionPath {
	if fromAsset == toAsset || maxSteps <= 0 {
		return nil
	}

	// Store all found paths and their subpaths
	allPaths := make([]ConversionPath, 0)
	knownSubpaths := make(map[string]bool)

	// Find paths of increasing length
	for stepCount := 1; stepCount <= maxSteps; stepCount++ {
		paths := f.findPathsWithLength(fromAsset, toAsset, stepCount, knownSubpaths)

		// Record all subpaths from these new paths
		for _, path := range paths {
			for i := 1; i <= len(path.Steps); i++ {
				key := path.pathKey(i)
				knownSubpaths[key] = true
			}
		}

		allPaths = append(allPaths, paths...)
	}

	return allPaths
}

func (f *PathFinder) findPathsWithLength(
	fromAsset string,
	toAsset string,
	exactSteps int,
	knownSubpaths map[string]bool,
) []ConversionPath {
	var paths []ConversionPath

	// Initial path
	currentPath := ConversionPath{
		Steps:     make([]ConversionStep, 0, exactSteps),
		FromAsset: fromAsset,
		ToAsset:   toAsset,
	}

	// Track assets we've converted to in this path
	convertedTo := make(map[string]bool)
	convertedTo[fromAsset] = true

	f.findPathsDFS(fromAsset, toAsset, exactSteps, convertedTo, currentPath, knownSubpaths, &paths)
	return paths
}

func (f *PathFinder) findPathsDFS(
	currentAsset string,
	targetAsset string,
	remainingSteps int,
	convertedTo map[string]bool,
	currentPath ConversionPath,
	knownSubpaths map[string]bool,
	paths *[]ConversionPath,
) {
	// Check if the current partial path matches any known shorter path
	if len(currentPath.Steps) > 0 {
		currentKey := currentPath.pathKey(len(currentPath.Steps))
		if knownSubpaths[currentKey] {
			return
		}
	}

	// Found a path
	currentExchange := ""
	if len(currentPath.Steps) > 0 {
		currentExchange = currentPath.Steps[len(currentPath.Steps)-1].Market.Exchange.Name
	}

	// Check if current asset matches target asset (considering translations)
	translatedCurrentAsset := f.translateAsset(currentExchange, currentAsset, "")
	translatedTargetAsset := f.translateAsset("", targetAsset, currentExchange)

	if translatedCurrentAsset == translatedTargetAsset && remainingSteps == 0 {
		pathCopy := ConversionPath{
			Steps:     make([]ConversionStep, len(currentPath.Steps)),
			FromAsset: currentPath.FromAsset,
			ToAsset:   currentPath.ToAsset,
		}
		copy(pathCopy.Steps, currentPath.Steps)
		*paths = append(*paths, pathCopy)
		return
	}

	// Stop if no more steps allowed
	if remainingSteps == 0 {
		return
	}

	// Try all possible next markets
	for _, market := range f.markets {
		// Try buying if we have the quote currency
		quoteAsset := f.translateAsset(market.Exchange.Name, market.Pair.Quote, currentExchange)
		if quoteAsset == currentAsset {
			nextAsset := market.Pair.Base
			if !convertedTo[nextAsset] {
				step := ConversionStep{
					Market: market,
					IsBuy:  true,
				}
				currentPath.Steps = append(currentPath.Steps, step)
				convertedTo[nextAsset] = true

				f.findPathsDFS(nextAsset, targetAsset, remainingSteps-1, convertedTo, currentPath, knownSubpaths, paths)

				delete(convertedTo, nextAsset)
				currentPath.Steps = currentPath.Steps[:len(currentPath.Steps)-1]
			}
		}

		// Try selling if we have the base currency
		baseAsset := f.translateAsset(market.Exchange.Name, market.Pair.Base, currentExchange)
		if baseAsset == currentAsset {
			nextAsset := market.Pair.Quote
			if !convertedTo[nextAsset] {
				step := ConversionStep{
					Market: market,
					IsBuy:  false,
				}
				currentPath.Steps = append(currentPath.Steps, step)
				convertedTo[nextAsset] = true

				f.findPathsDFS(nextAsset, targetAsset, remainingSteps-1, convertedTo, currentPath, knownSubpaths, paths)

				delete(convertedTo, nextAsset)
				currentPath.Steps = currentPath.Steps[:len(currentPath.Steps)-1]
			}
		}
	}
}
