package book

import (
	"fmt"
	"time"
)

type Price struct {
	Value     float64
	Amount    float64
	EntryTime time.Time
}

func (p Price) String() string {
	return fmt.Sprintf("%g@%g", p.Amount, p.Value)
}

func NewPrice(price, amount float64) Price {
	return Price{
		Value:  price,
		Amount: amount,
	}
}

func (p Price) Age() time.Duration {
	if p.EntryTime.IsZero() {
		return 0
	}
	return time.Since(p.EntryTime)
}
