package book

import (
	"testing"
)

func TestEventHistory_10KEvents(t *testing.T) {
	b := NewBook("BTC/USDT")

	initialBids := []Price{
		{Value: 99.0, Amount: 1.0},
		{Value: 98.0, Amount: 2.0},
	}
	initialAsks := []Price{
		{Value: 101.0, Amount: 1.0},
		{Value: 102.0, Amount: 2.0},
	}
	b.<PERSON>nap<PERSON>(initialBids, initialAsks)

	// Add price levels
	for i := range 10_000 {
		values := []Price{
			{Value: 100 + float64(i), Amount: 10},
			{Value: 105.5 + float64(i), Amount: 7.5},
			{Value: 115.75 + float64(i), Amount: 50},
		}

		b.DeltaUpdate(nil, values)
	}

	// Remove price levels
	for i := range 10_000 {
		values := []Price{
			{Value: 100 + float64(i), Amount: 0},
			{Value: 105.5 + float64(i), Amount: 0},
			{Value: 115.75 + float64(i), Amount: 0},
		}

		b.DeltaUpdate(nil, values)
	}

	eventHistory := b.EventHistory()
	if eventHistory.count != 10000 {
		t.Errorf("Expected 10000 events, got %d", eventHistory.count)
	}

	if eventHistory.head != 0 {
		t.Errorf("Expected head to be 0, got %d", eventHistory.head)
	}

	_, ok := b.EventHistory().GetLatestDeletion(Ask, 100)
	if !ok {
		t.Error("Failed to find deletion events")
	}
}
func TestEventHistory_CrossingBidAsk(t *testing.T) {
	b := NewBook("BTC/USDT")

	initialBids := []Price{
		{Value: 100.0, Amount: 1.0}, // This price will be deleted because it's crossing the ask price
		{Value: 99.0, Amount: 2.0},
	}
	initialAsks := []Price{
		{Value: 101.0, Amount: 1.0},
		{Value: 102.0, Amount: 2.0},
	}
	b.Snapshot(initialBids, initialAsks)

	// Add price levels
	for i := range 10_000 {
		values := []Price{
			{Value: 100 + float64(i), Amount: 10},
			{Value: 105.5 + float64(i), Amount: 7.5},
			{Value: 115.75 + float64(i), Amount: 50},
		}

		b.DeltaUpdate(nil, values)
	}

	// Remove price levels
	for i := range 10_000 {
		values := []Price{
			{Value: 100 + float64(i), Amount: 0},
			{Value: 105.5 + float64(i), Amount: 0},
			{Value: 115.75 + float64(i), Amount: 0},
		}

		b.DeltaUpdate(nil, values)
	}

	eventHistory := b.EventHistory()
	if eventHistory.count != 10000 {
		t.Errorf("Expected 10000 events, got %d", eventHistory.count)
	}

	if eventHistory.head != 1 {
		t.Errorf("Expected head to be 1, got %d", eventHistory.head)
	}

	_, ok1 := b.EventHistory().GetLatestDeletion(Bid, 100)
	_, ok2 := b.EventHistory().GetLatestDeletion(Ask, 100)

	if !ok1 || !ok2 {
		t.Error("Failed to find deletion events")
	}
}
