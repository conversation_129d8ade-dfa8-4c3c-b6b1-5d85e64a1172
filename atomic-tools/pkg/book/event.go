package book

import (
	"sync"
	"time"
)

// EventType represents the type of price event
type EventType byte

const (
	EventInsert EventType = iota + 1
	EventUpdate
	EventRemove
)

// Event represents a single price change event
type Event struct {
	Price     Price
	Side      BidAskSide
	Type      EventType
	Timestamp time.Time
}

// EventHistory maintains a circular buffer of the last N price events
// and provides fast lookup for deletion events
type EventHistory struct {
	events [10000]Event
	head   int
	count  int
	mutex  sync.RWMutex

	// Fast lookup map for deletion events: key is "side:price"
	deletionIndex map[eventKey]*Event
}

// NewEventHistory creates a new event history
func NewEventHistory() *EventHistory {
	return &EventHistory{
		deletionIndex: make(map[eventKey]*Event),
	}
}

type eventKey struct {
	side  BidAskSide
	price float64
}

func makeKey(side BidAskSide, price float64) eventKey {
	return eventKey{
		side:  side,
		price: price,
	}
}

func (h *EventHistory) AddEvent(eventType EventType, side BidAskSide, price Price) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	event := Event{
		Price:     price,
		Side:      side,
		Type:      eventType,
		Timestamp: time.Now(),
	}

	// Add to circular buffer
	h.events[h.head] = event
	h.head = (h.head + 1) % len(h.events)
	if h.count < len(h.events) {
		h.count++
	}

	// Update deletion index if it's a deletion
	if eventType == EventRemove {
		key := makeKey(side, price.Value)
		h.deletionIndex[key] = &h.events[(h.head-1+len(h.events))%len(h.events)]
	}
}

// GetLatestDeletion returns the most recent deletion event for a specific side/price
// Note: This only returns the LATEST deletion for this price/side combination
func (h *EventHistory) GetLatestDeletion(side BidAskSide, price float64) (Event, bool) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	key := makeKey(side, price)
	event, ok := h.deletionIndex[key]
	if !ok {
		return Event{}, false
	}

	return *event, ok
}

// Clear removes all events from the history
func (h *EventHistory) Clear() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.head = 0
	h.count = 0
	h.deletionIndex = make(map[eventKey]*Event)
}
