package balances_converter

import (
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/exchange_path_finder"
)

// Represents the final conversion results
type ConversionReport struct {
	Asset       string                  `json:"asset"`
	Value       float64                 `json:"value"`
	Conversions []AssetConversionReport `json:"conversions"`
}

// Details a single asset conversion
type AssetConversionReport struct {
	Asset  string                                `json:"asset"`
	Amount float64                               `json:"amount"`
	Value  float64                               `json:"value"`
	Steps  []exchange_path_finder.ConversionStep `json:"steps"`
	Prices []ConversionPrice                     `json:"prices"`
	Error  error                                 `json:"error"`
}

// Represents the price at a specific point in the conversion
type ConversionPrice struct {
	Price   float64   `json:"price"`
	ValidAt time.Time `json:"valid_at"`
}

type Converter struct {
	accGtw     gateway.Gateway
	mktdGtw    gateway.Gateway
	pathFinder *exchange_path_finder.PathFinder
	bookCache  cachedBooks
}

func New(accGtw, mktdGtw gateway.Gateway) *Converter {
	return &Converter{
		accGtw:     accGtw,
		mktdGtw:    mktdGtw,
		pathFinder: exchange_path_finder.New(),
		bookCache:  make(cachedBooks),
	}
}

// Convert converts all balances to the benchmark asset
func (c *Converter) Convert(benchmarkAsset string, maxSteps int) (ConversionReport, error) {
	markets, err := c.mktdGtw.GetMarkets()
	if err != nil {
		return ConversionReport{}, fmt.Errorf("failed to get markets [mktdGtw]: %w", err)
	}

	// Load all available markets
	c.pathFinder.ResetMarkets()
	c.pathFinder.LoadMarkets(markets)

	// Get current balances
	balances, err := c.accGtw.AccountGateway().Balances()
	if err != nil {
		return ConversionReport{}, fmt.Errorf("failed to get balances [accGtw]: %w", err)
	}

	report := ConversionReport{
		Asset:       benchmarkAsset,
		Value:       0,
		Conversions: make([]AssetConversionReport, 0),
	}

	// Convert each balance
	for _, balance := range balances {
		if balance.Total <= 0 {
			continue
		}

		// Skip if already in benchmark asset
		if balance.Asset == benchmarkAsset {
			report.Value += balance.Total
			report.Conversions = append(report.Conversions, AssetConversionReport{
				Asset:  balance.Asset,
				Amount: balance.Total,
				Value:  balance.Total,
			})
			continue
		}

		// Find best conversion path
		conversionReport, err := c.convertAsset(balance, benchmarkAsset, maxSteps)
		if err != nil {
			var emptyOrderBookError *EmptyOrderBookError
			if errors.Is(err, NoPathFoundError) || errors.As(err, &emptyOrderBookError) {
				log.Printf("Failed to convert %f %s balance: %s", balance.Total, balance.Asset, err)
				report.Conversions = append(report.Conversions, AssetConversionReport{
					Asset:  balance.Asset,
					Amount: balance.Total,
					Value:  0,
					Error:  err,
				})
				continue
			}
			// Handle other errors
			return ConversionReport{}, fmt.Errorf("failed to convert %f %s balance: %w", balance.Total, balance.Asset, err)
		}

		report.Value += conversionReport.Value
		report.Conversions = append(report.Conversions, conversionReport)
	}

	return report, nil
}

var NoPathFoundError = fmt.Errorf("no conversion path found")

// convertAsset converts a single balance to the benchmark asset
func (c *Converter) convertAsset(balance gateway.Balance, toAsset string, maxSteps int) (AssetConversionReport, error) {
	// Find all possible paths with up to 3 steps
	paths := c.pathFinder.FindPaths(balance.Asset, toAsset, maxSteps)
	if len(paths) == 0 {
		return AssetConversionReport{}, NoPathFoundError
	}

	var lastErr error
	var bestPathOk bool
	var bestPath exchange_path_finder.ConversionPath
	var bestValue float64
	var bestPrices []ConversionPrice

	// Try each path and find the one that gives the best final value
	for _, path := range paths {
		prices, convertedValue, err := c.simulateConversion(path, balance.Total)
		if err != nil {
			lastErr = err
			continue
		}

		if !bestPathOk || convertedValue > bestValue {
			bestPathOk = true
			bestPath = path
			bestValue = convertedValue
			bestPrices = prices
		}
	}

	if !bestPathOk {
		return AssetConversionReport{}, fmt.Errorf("no viable conversion path found, last conversion error: %w", lastErr)
	}

	return AssetConversionReport{
		Asset:  balance.Asset,
		Amount: balance.Total,
		Value:  bestValue,
		Steps:  bestPath.Steps,
		Prices: bestPrices,
	}, nil
}

type EmptyOrderBookError struct {
	Market gateway.Market
}

func (e EmptyOrderBookError) Error() string {
	return fmt.Sprintf("%s empty order book", e.Market)
}

// simulateConversion simulates a conversion path and returns the mid prices and final value
func (c *Converter) simulateConversion(path exchange_path_finder.ConversionPath, amount float64) ([]ConversionPrice, float64, error) {
	prices := make([]ConversionPrice, len(path.Steps))
	convertedAmount := amount

	for i, step := range path.Steps {
		// Get cached order book
		bookEntry, err := defaultGetBookFromCacheWithRetry(
			c.bookCache,
			c.mktdGtw,
			step.Market,
		)
		if err != nil {
			return nil, 0, err
		}

		ob := bookEntry.Book

		var conversionPrice float64
		if step.IsBuy {
			topAsk, topAskOk := ob.Asks.Top()
			if !topAskOk {
				return nil, 0, &EmptyOrderBookError{
					Market: step.Market,
				}
			}
			conversionPrice = topAsk.Value
			convertedAmount = convertedAmount / conversionPrice
		} else {
			topBid, topBidOk := ob.Bids.Top()
			if !topBidOk {
				return nil, 0, &EmptyOrderBookError{
					Market: step.Market,
				}
			}
			conversionPrice = topBid.Value
			convertedAmount = convertedAmount * conversionPrice
		}

		prices[i] = ConversionPrice{
			Price:   conversionPrice,
			ValidAt: bookEntry.LastUpdate,
		}
	}

	return prices, convertedAmount, nil
}
