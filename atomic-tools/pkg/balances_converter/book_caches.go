package balances_converter

import (
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/book"
)

const BOOK_CACHE_EXPIRATION = 300 * time.Second

type BookEntry struct {
	Book       *book.Book
	LastUpdate time.Time
}

type cachedBooks map[string]BookEntry

// We will cache the book for 10 seconds, if the book is older than that we will fetch it again
// If it is not initialized we will fetch it for the first time
// Each Exchange::Market pair will have its own book
func getBookEntryFromCache(
	bookCache cachedBooks,
	gtw gateway.Gateway,
	market gateway.Market,
) (BookEntry, error) {
	key := fmt.Sprintf("%s-%s", gtw.Exchange(), market.Symbol)
	cacheEntry, ok := bookCache[key]
	if !ok {
		book := book.NewBook(key)
		cacheEntry = BookEntry{
			Book: book,
		}
	}

	if !ok || time.Since(cacheEntry.LastUpdate) > BOOK_CACHE_EXPIRATION {
		depth, err := gtw.GetDepthBook(market, gateway.DefaultDepthParams)
		if err != nil {
			return BookEntry{}, fmt.Errorf("get depth book, err: %s", err)
		}

		bids := make([]book.Price, 0, len(depth.Bids))
		for _, bid := range depth.Bids {
			bids = append(bids, book.Price{Value: bid.Price, Amount: bid.Amount})
		}
		asks := make([]book.Price, 0, len(depth.Asks))
		for _, ask := range depth.Asks {
			asks = append(asks, book.Price{Value: ask.Price, Amount: ask.Amount})
		}

		cacheEntry.Book.Snapshot(bids, asks)

		cacheEntry.LastUpdate = time.Now()
		bookCache[key] = cacheEntry
	}

	return cacheEntry, nil
}

func getBookFromCacheWithRetry(
	bookCache cachedBooks,
	gtw gateway.Gateway,
	market gateway.Market,
	maxRetries int,
	backoff time.Duration,
) (BookEntry, error) {
	var lastErr error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		entry, err := getBookEntryFromCache(bookCache, gtw, market)
		if err == nil {
			return entry, nil
		}

		lastErr = err

		// If this was our last attempt, break out
		if attempt == maxRetries {
			break
		}

		// Calculate backoff duration with exponential increase
		sleepDuration := backoff * time.Duration(1<<uint(attempt))
		log.Printf("Fetch orderbook [%s] failed retrying in %v. Error: %s", market, sleepDuration, err)
		time.Sleep(sleepDuration)
	}

	return BookEntry{}, fmt.Errorf("failed after %d retries, last error: %w", maxRetries, lastErr)
}

func defaultGetBookFromCacheWithRetry(
	bookCache cachedBooks,
	gtw gateway.Gateway,
	market gateway.Market,
) (BookEntry, error) {
	return getBookFromCacheWithRetry(bookCache, gtw, market, 5, 1*time.Millisecond)
}
