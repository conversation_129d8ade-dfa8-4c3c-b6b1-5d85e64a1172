package logger

import (
	"context"
	"log"
	"os"
	"time"

	"github.com/agoda-com/opentelemetry-logs-go/exporters/otlp/otlplogs"
	"github.com/agoda-com/opentelemetry-logs-go/exporters/otlp/otlplogs/otlplogsgrpc"
	sdk "github.com/agoda-com/opentelemetry-logs-go/sdk/logs"

	"github.com/agoda-com/opentelemetry-go/otelzap"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.27.0"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Config holds logger configuration
type Config struct {
	Level               string
	EnableOpenTelemetry bool
	Token               string
	ServiceName         string
	ServiceVersion      string
	OtelEndpoint        string
}

// Logger wraps zap logging functionality
type Logger struct {
	*zap.Logger
	hasOtel bool
}

// New creates a new logger based on the provided configuration
func New(ctx context.Context, cfg Config) (*Logger, error) {
	level := zap.ErrorLevel
	if cfg.Level != "" {
		var err error
		level, err = zapcore.ParseLevel(cfg.Level)
		if err != nil {
			log.Println("invalid log level")
		}
	}

	// Create production encoder config
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "ts"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder

	// Create console core
	consoleCore := zapcore.NewCore(
		zapcore.NewConsoleEncoder(encoderConfig),
		zapcore.AddSync(os.Stdout),
		level,
	)

	var cores []zapcore.Core
	cores = append(cores, consoleCore)

	// Add OpenTelemetry core if enabled
	var hasOtel bool
	if cfg.EnableOpenTelemetry && cfg.OtelEndpoint != "" {
		hostname, _ := os.Hostname()
		attrs := resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(cfg.ServiceName),
			semconv.ServiceVersion(cfg.ServiceVersion),
			semconv.HostName(hostname),
		)

		headers := make(map[string]string)
		if cfg.Token != "" {
			headers["authorization"] = "Bearer " + cfg.Token
		}

		// Create OTLP exporter
		logExporter, err := otlplogs.NewExporter(ctx, otlplogs.WithClient(
			otlplogsgrpc.NewClient(
				otlplogsgrpc.WithEndpoint(cfg.OtelEndpoint),
				otlplogsgrpc.WithReconnectionPeriod(100*time.Millisecond),
				otlplogsgrpc.WithInsecure(),
				otlplogsgrpc.WithHeaders(headers),
				otlplogsgrpc.WithTimeout(5*time.Second),
			),
		))
		if err != nil {
			return nil, err
		}

		// Create logger provider
		loggerProvider := sdk.NewLoggerProvider(
			sdk.WithBatcher(logExporter),
			sdk.WithResource(attrs),
		)

		if loggerProvider != nil {
			otelCore := otelzap.NewOtelCore(loggerProvider, otelzap.WithLevel(level))
			cores = append(cores, otelCore)
			hasOtel = true
		}
	}

	// Create logger with all cores
	zapLogger := zap.New(zapcore.NewTee(cores...))
	return &Logger{Logger: zapLogger, hasOtel: hasOtel}, nil
}

// WithContext adds OpenTelemetry context to logs if OpenTelemetry is enabled
func (l *Logger) WithContext(ctx context.Context) *Logger {
	if !l.hasOtel {
		return l
	}
	return &Logger{
		Logger:  otelzap.Ctx(ctx).Logger,
		hasOtel: true,
	}
}

// With creates a child logger with the given fields
func (l *Logger) With(fields ...zapcore.Field) *Logger {
	return &Logger{
		Logger:  l.Logger.With(fields...),
		hasOtel: l.hasOtel,
	}
}

// Sugar returns a sugared logger
func (l *Logger) Sugar() *zap.SugaredLogger {
	return l.Logger.Sugar()
}
