package safegwbuilder_test

import (
	"log"
	"os"

	"github.com/herenow/atomic-tools/pkg/safegwbuilder"
)

func ExampleBuilder() {
	// Create a new builder with default configuration
	builder := safegwbuilder.New(nil)

	// Ensure the binary exists and is up to date
	if err := builder.EnsureBinary(); err != nil {
		log.Fatal(err)
	}

	// Clean up the binary when done
	defer builder.Cleanup()

	// Use the binary path (e.g., with SafeGateway)
	os.Setenv("SAFEGATEWAY_BIN_PATH", builder.BinaryPath())

	// Output:
}

func ExampleBuilder_withCustomConfig() {
	// Create a builder with custom configuration
	config := &safegwbuilder.Config{
		BinaryPath: "/tmp/custom-safegateway",
	}

	builder := safegwbuilder.New(config)
	if err := builder.EnsureBinary(); err != nil {
		log.Fatal(err)
	}
	defer builder.Cleanup()

	// Use the custom binary path
	os.Setenv("SAFEGATEWAY_BIN_PATH", builder.BinaryPath())

	// Output:
}
