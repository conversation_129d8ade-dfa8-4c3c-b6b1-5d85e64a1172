package safegwbuilder

import (
	"os"
	"path/filepath"
	"testing"
)

func TestBuilder(t *testing.T) {
	// Create temporary directory for test binaries
	tmpDir, err := os.MkdirTemp("", "safegwbuilder-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	testCases := []struct {
		name        string
		config      *Config
		wantErr     bool
		checkBinary bool
	}{
		{
			name:        "Default config",
			config:      nil,
			wantErr:     false,
			checkBinary: true,
		},
		{
			name: "Custom binary path",
			config: &Config{
				BinaryPath: filepath.Join(tmpDir, "custom-safegateway"),
			},
			wantErr:     false,
			checkBinary: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			builder := New(tc.config)
			defer builder.Cleanup()

			err := builder.EnsureBinary()
			if (err != nil) != tc.wantErr {
				t.Errorf("EnsureBinary() error = %v, wantErr %v", err, tc.wantErr)
				return
			}

			if tc.checkBinary {
				// Check if binary exists
				if _, err := os.Stat(builder.BinaryPath()); os.IsNotExist(err) {
					t.Errorf("Binary was not created at %s", builder.BinaryPath())
				}

				// Check if binary is executable
				if err := os.Chmod(builder.BinaryPath(), 0755); err != nil {
					t.Errorf("Binary is not executable: %v", err)
				}
			}

			// Test cleanup
			if err := builder.Cleanup(); err != nil {
				t.Errorf("Cleanup() error = %v", err)
			}

			// Verify binary was removed
			if _, err := os.Stat(builder.BinaryPath()); !os.IsNotExist(err) {
				t.Errorf("Binary was not removed during cleanup")
			}
		})
	}
}

func TestConcurrentBuilds(t *testing.T) {
	builder := New(nil)
	defer builder.Cleanup()

	// Try to build concurrently
	done := make(chan error, 3)
	for i := 0; i < 3; i++ {
		go func() {
			done <- builder.EnsureBinary()
		}()
	}

	// Wait for all builds
	for i := 0; i < 3; i++ {
		if err := <-done; err != nil {
			t.Errorf("Concurrent build failed: %v", err)
		}
	}

	// Verify only one binary exists
	matches, err := filepath.Glob(builder.BinaryPath() + "*")
	if err != nil {
		t.Fatalf("Failed to check for multiple binaries: %v", err)
	}
	if len(matches) > 1 {
		t.Errorf("Found multiple binaries: %v", matches)
	}
}
