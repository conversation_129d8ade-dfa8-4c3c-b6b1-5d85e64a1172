package safegwbuilder

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"sync"
)

const (
	// Package import path for the safegateway command
	safegatewayPackage = "github.com/herenow/atomic-gtw/cmd/safegateway"
)

// Config holds configuration options for the builder
type Config struct {
	// Optional custom path for the binary output
	BinaryPath string
	// Optional path to the source code
	SourcePath string
}

// Builder manages the building and access of the safegateway binary
type Builder struct {
	// Path to the safegateway binary
	binaryPath string
	// Mutex to ensure thread-safe building
	buildMutex sync.Mutex
	// Flag to track if binary has been built
	built bool
}

// New creates a new Builder instance with the given configuration.
// If config is nil, default values will be used.
func New(config *Config) *Builder {
	binaryPath := "bin/safegateway"
	if config != nil && config.BinaryPath != "" {
		binaryPath = config.BinaryPath
	}

	return &Builder{
		binaryPath: binaryPath,
	}
}

// EnsureBinary ensures the safegateway binary exists and is up to date.
// It will build the binary if it doesn't exist or is outdated.
func (b *Builder) EnsureBinary() error {
	b.buildMutex.Lock()
	defer b.buildMutex.Unlock()

	if b.built {
		return nil
	}

	// Create bin directory if it doesn't exist
	binDir := filepath.Dir(b.binaryPath)
	if err := os.MkdirAll(binDir, 0755); err != nil {
		return fmt.Errorf("create binary directory: %w", err)
	}

	// Build the binary
	cmd := exec.Command("go", "build", "-o", b.binaryPath, safegatewayPackage)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("build safegateway from package %s: %w", safegatewayPackage, err)
	}

	b.built = true
	return nil
}

// BinaryPath returns the path to the safegateway binary
func (b *Builder) BinaryPath() string {
	return b.binaryPath
}

// Cleanup removes the built binary and resets the builder state
func (b *Builder) Cleanup() error {
	b.buildMutex.Lock()
	defer b.buildMutex.Unlock()

	if err := os.Remove(b.binaryPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("remove binary: %w", err)
	}

	b.built = false
	return nil
}
