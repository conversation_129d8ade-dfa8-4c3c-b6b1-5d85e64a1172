package safegwbuilder

// Package safegwbuilder provides functionality for building and managing the safegateway binary
// required by the atomic-gtw package's SafeGateway feature.
//
// The package automatically handles building the safegateway binary from the module cache,
// ensuring it's up to date, and cleaning up when no longer needed.
//
// Example usage:
//
//	builder := safegwbuilder.New(nil)
//	if err := builder.EnsureBinary(); err != nil {
//	    log.Fatal(err)
//	}
//	defer builder.Cleanup()
//
//	// Use the binary path
//	os.Setenv("SAFEGATEWAY_BIN_PATH", builder.BinaryPath())
