package depthbook

import (
	"github.com/herenow/atomic-tools/pkg/book"
)

// Cumulative stores depth at different price levels
type Cumulative struct {
	BidsDepth [][2]float64 // [price, amount]
	AsksDepth [][2]float64 // [price, amount]
}

// CumulateByBps returns cumulative depth in basis point increments, limited to N levels
func CumulateByBps(b *book.Book, bpsIncrement int, limit int) *Cumulative {
	c := &Cumulative{
		BidsDepth: make([][2]float64, 0, limit),
		AsksDepth: make([][2]float64, 0, limit),
	}

	bps := float64(bpsIncrement) / 10000.0

	// Process asks side
	if bestAsk, hasAsk := b.Asks.Top(); hasAsk {
		askLevels := b.Asks.PriceLevels()
		if len(askLevels) > 0 {
			nextPrice := bestAsk.Value * (1.0 + bps)
			currentAmount := 0.0
			currentNotional := 0.0

			for i := 0; i < len(askLevels); i++ {
				level := askLevels[i]

				// Add current level to accumulation
				currentAmount += level.Amount
				currentNotional += level.Amount * level.Value

				// Check if we need to record increment (on next level or end of list)
				recordIncrement := false
				if i < len(askLevels)-1 {
					nextLevel := askLevels[i+1]
					recordIncrement = nextLevel.Value > nextPrice
				} else {
					recordIncrement = true // Last level
				}

				if recordIncrement {
					// Store the increment
					avgPrice := currentNotional / currentAmount
					c.AsksDepth = append(c.AsksDepth, [2]float64{avgPrice, currentAmount})

					if len(c.AsksDepth) >= limit {
						break
					}

					// Calculate next increment boundary
					nextPrice = nextPrice * (1.0 + bps)
				}
			}
		}
	}

	// Process bids side
	if bestBid, hasBid := b.Bids.Top(); hasBid {
		bidLevels := b.Bids.PriceLevels()
		if len(bidLevels) > 0 {
			nextPrice := bestBid.Value * (1.0 - bps)
			currentAmount := 0.0
			currentNotional := 0.0

			for i := 0; i < len(bidLevels); i++ {
				level := bidLevels[i]

				// Add current level to accumulation
				currentAmount += level.Amount
				currentNotional += level.Amount * level.Value

				// Check if we need to record increment (on next level or end of list)
				recordIncrement := false
				if i < len(bidLevels)-1 {
					nextLevel := bidLevels[i+1]
					recordIncrement = nextLevel.Value < nextPrice
				} else {
					recordIncrement = true // Last level
				}

				if recordIncrement {
					// Store the increment
					avgPrice := currentNotional / currentAmount
					c.BidsDepth = append(c.BidsDepth, [2]float64{avgPrice, currentAmount})

					if len(c.BidsDepth) >= limit {
						break
					}

					// Calculate next increment boundary
					nextPrice = nextPrice * (1.0 - bps)
				}
			}
		}
	}

	return c
}
