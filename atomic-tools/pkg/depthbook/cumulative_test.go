package depthbook

import (
	"math"
	"testing"

	"github.com/herenow/atomic-tools/pkg/book"
)

func TestCumulateByBps(t *testing.T) {
	// Create test book
	b := book.NewBook("TEST")

	// Add test price levels
	// Start at $100 with 5bps = $0.05 increments
	askLevels := []book.Price{
		{Value: 100.00, Amount: 1.0}, // Initial price
		{Value: 100.02, Amount: 2.0}, // Within first 5bps
		{Value: 100.06, Amount: 1.5}, // In second 5bps increment
		{Value: 100.08, Amount: 1.0}, // Also in second 5bps increment
		{Value: 100.12, Amount: 2.0}, // In third 5bps increment
	}

	bidLevels := []book.Price{
		{Value: 99.95, Amount: 1.0}, // Initial price
		{Value: 99.93, Amount: 2.0}, // Within first 5bps
		{Value: 99.89, Amount: 1.5}, // In second 5bps increment
		{Value: 99.87, Amount: 1.0}, // Also in second 5bps increment
		{Value: 99.83, Amount: 2.0}, // In third 5bps increment
	}

	b.Snapshot(bidLevels, askLevels)

	// Calculate 5bps depth with limit 2
	depth := CumulateByBps(b, 5, 2)

	// Should only have 2 levels regardless of data
	if len(depth.AsksDepth) != 2 {
		t.Fatalf("Expected 2 ask levels, got %d", len(depth.AsksDepth))
	}
	if len(depth.BidsDepth) != 2 {
		t.Fatalf("Expected 2 bid levels, got %d", len(depth.BidsDepth))
	}

	// Test with empty ask side
	b = book.NewBook("TEST")
	b.Snapshot(bidLevels, nil)
	depth = CumulateByBps(b, 5, 2)

	if len(depth.AsksDepth) != 0 {
		t.Error("Expected empty ask depth when no ask levels")
	}
	if len(depth.BidsDepth) != 2 {
		t.Error("Expected 2 bid levels when only bid side has data")
	}

	// Test with empty bid side
	b = book.NewBook("TEST")
	b.Snapshot(nil, askLevels)
	depth = CumulateByBps(b, 5, 2)

	if len(depth.BidsDepth) != 0 {
		t.Error("Expected empty bid depth when no bid levels")
	}
	if len(depth.AsksDepth) != 2 {
		t.Error("Expected 2 ask levels when only ask side has data")
	}
}

func TestDepthLimit(t *testing.T) {
	b := book.NewBook("TEST")

	// Create many price levels
	askLevels := make([]book.Price, 0)
	price := 100.0
	for i := 0; i < 10; i++ {
		askLevels = append(askLevels, book.Price{Value: price, Amount: 1.0})
		price += 0.05 // 5bp increments
	}

	b.Snapshot(nil, askLevels)

	// Test different limits
	limits := []int{1, 2, 5}
	for _, limit := range limits {
		depth := CumulateByBps(b, 5, limit)
		if len(depth.AsksDepth) != limit {
			t.Errorf("With limit %d: expected %d levels, got %d",
				limit, limit, len(depth.AsksDepth))
		}
	}
}

func TestBpsAsInteger(t *testing.T) {
	b := book.NewBook("TEST")

	// Test a few common bps values
	askLevels := []book.Price{
		{Value: 100.00, Amount: 1.0},
		{Value: 100.10, Amount: 1.0}, // +10bps
		{Value: 100.25, Amount: 1.0}, // +25bps
		{Value: 100.50, Amount: 1.0}, // +50bps
	}
	b.Snapshot(nil, askLevels)

	bpsValues := []int{5, 10, 25}
	for _, bps := range bpsValues {
		depth := CumulateByBps(b, bps, 10)
		if len(depth.AsksDepth) == 0 {
			t.Errorf("Expected non-empty depth with %dbps increment", bps)
		}
	}
}

// Helper to compare floating point values within a tolerance
func closeTo(a, b, tolerance float64) bool {
	return math.Abs(a-b) <= tolerance
}

func TestNoThresholdCross(t *testing.T) {
	b := book.NewBook("TEST")

	// Create prices that never cross the bps threshold
	askLevels := []book.Price{
		{Value: 100.00, Amount: 1.0},
		{Value: 100.02, Amount: 2.0}, // All within first 5bps
		{Value: 100.04, Amount: 1.5}, // Never crosses 100.05 (5bps)
	}

	b.Snapshot(nil, askLevels)

	depth := CumulateByBps(b, 5, 2)

	// Should still have one level with accumulated amounts
	if len(depth.AsksDepth) != 1 {
		t.Fatalf("Expected 1 ask level, got %d", len(depth.AsksDepth))
	}

	level := depth.AsksDepth[0]
	expectedAmount := 4.5 // 1.0 + 2.0 + 1.5
	expectedNotional := 100.00*1.0 + 100.02*2.0 + 100.04*1.5
	expectedPrice := expectedNotional / expectedAmount

	if !closeTo(level[1], expectedAmount, 0.0001) {
		t.Errorf("Expected amount %.4f, got %.4f", expectedAmount, level[1])
	}
	if !closeTo(level[0], expectedPrice, 0.0001) {
		t.Errorf("Expected avg price %.4f, got %.4f", expectedPrice, level[0])
	}
}
