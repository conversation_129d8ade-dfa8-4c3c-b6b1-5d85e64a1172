package options_parser

import (
	"fmt"
	"net/url"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	gtwutils "github.com/herenow/atomic-gtw/utils"
	"github.com/iancoleman/strcase"
)

var matchFirstCap = regexp.MustCompile("(.)([A-Z][a-z]+)")
var matchAllCap = regexp.MustCompile("([a-z0-9])([A-Z])")

func OptNameToSymbol(str string) string {
	snake := matchFirstCap.ReplaceAllString(str, "${1}_${2}")
	snake = matchAllCap.ReplaceAllString(snake, "${1}_${2}")
	return strings.ToLower(snake)
}

// Example usage to parse flags into opts struct
// e.g:
// bid.order_size=2
// ask.order_size=2
// flags are separatedd by space
// dots are separators for nested structs
func ParseFlagsToOpts(str string, opt interface{}) error {
	optType := reflect.TypeOf(opt)
	optVal := reflect.ValueOf(opt)
	if optType.Kind() != reflect.Ptr {
		return fmt.Errorf(
			"expected \"%s\" to be passed as pointer, instead was passed by value",
			optType.Name(),
		)
	}

	if optVal.Elem().Kind() != reflect.Struct {
		return fmt.Errorf(
			"expected \"%s\" to be a struct, instead was \"%s\"",
			optType.Elem().Name(),
			optVal.Elem().Kind(),
		)
	}

	flags := sepOptsFlags(str)
	for _, flag := range flags {
		key, value, err := flagToKV(flag)
		if err != nil {
			return fmt.Errorf("flagToKV err: %s", err)
		}

		structPath := keyToStructPath(key)

		optValue := reflect.ValueOf(opt).Elem()
		for _, path := range structPath {
			if reflect.TypeOf(optValue).Kind() != reflect.Struct {
				return fmt.Errorf(
					"expected flag \"%s\" key \"%s\" path \"%s\" to be nested struct, instead was \"%s\"",
					flag,
					key,
					path,
					reflect.TypeOf(optValue).Kind(),
				)
			}

			optValue = optValue.FieldByName(path)
			if optValue.Kind() == reflect.Invalid {
				return fmt.Errorf(
					"key \"%s\" doesn't exists in \"%s\"",
					key,
					optType.Elem().Name(),
				)
			}
		}

		switch t := optValue.Interface().(type) {
		case float32, float64:
			val, err := strconv.ParseFloat(value, 64)
			if err != nil {
				return fmt.Errorf("failed to parse flag \"%s\" of type %T, err: %s", flag, t, err)
			}
			optValue.SetFloat(val)
		case int, int8, int16, int32, int64:
			val, err := strconv.ParseInt(value, 10, 64)
			if err != nil {
				return fmt.Errorf("failed to parse flag \"%s\" of type %T, err: %s", flag, t, err)
			}
			optValue.SetInt(val)
		case bool:
			val, err := strconv.ParseBool(value)
			if err != nil {
				return fmt.Errorf("failed to parse flag \"%s\" of type %T, err: %s", flag, t, err)
			}
			optValue.SetBool(val)
		case string:
			optValue.SetString(value)
		case time.Duration:
			duration, err := time.ParseDuration(value)
			if err != nil {
				return err
			}
			optValue.Set(reflect.ValueOf(duration))
		case []*url.URL:
			proxies, err := gtwutils.ParseProxiesOpt(value)
			if err != nil {
				return fmt.Errorf("failed to parse flag \"%s\" of type %T, value: %s err: %s", flag, t, value, err)
			}
			optValue.Set(reflect.ValueOf(proxies))
		default:
			return fmt.Errorf("unable to parse flag \"%s\" of type %T, parser not implemented for this type", flag, t)
		}
	}

	return nil
}

// Splits the parts separated by a given separator, unless
// the part is inside double quotes, in which case it will
// be kept together.
// Example for separator ",":
// "a,b,c" -> ["a,b,c"]
// "a,b,c",d -> ["a,b,c", "d"]
func SepFlags(str string, separator byte) []string {
	var token []byte
	var tokens []string

	inQuotes := false
	inEscape := false
	for i := 0; i < len(str); i++ {
		s := str[i]

		// If in escape mode, accept any following character
		if inEscape {
			token = append(token, s)
			inEscape = false
			continue
		}

		if s == '\\' && !inEscape {
			inEscape = true
			continue
		}

		if s == '"' {
			inQuotes = !inQuotes
			continue
		}

		if s == separator && !inQuotes {
			tokens = append(tokens, string(token))
			token = token[:0]
			continue
		}

		token = append(token, s)
	}

	tokens = append(tokens, string(token))

	return tokens
}

func sepOptsFlags(str string) []string {
	str = strings.ReplaceAll(str, "\n", " ")
	str = strings.ReplaceAll(str, ";", " ")

	flags := make([]string, 0)
	parts := strings.Split(str, " ")
	for _, flag := range parts {
		flag = strings.TrimSpace(flag)
		if len(flag) > 0 {
			flags = append(flags, flag)
		}
	}

	return flags
}

func flagToKV(flag string) (key, value string, err error) {
	parts := strings.Split(flag, "=")
	if len(parts) == 2 {
		return parts[0], parts[1], nil
	}

	return "", "", fmt.Errorf("invalid flag \"%s\"", flag)
}

func keyToStructPath(key string) []string {
	parts := strings.Split(key, ".")
	for i, key := range parts {
		parts[i] = strcase.ToCamel(key)
	}

	return parts
}
