package options_parser

import (
	"log"
	"os"

	"github.com/herenow/atomic-gtw/gateway"
)

func FindExchangeOrFatal(exgSymbol string) gateway.Exchange {
	exchange, ok := gateway.ExchangeBySymbol(exgSymbol)
	if !ok {
		log.Printf("Failed to find exchange \"%s\"", exgSymbol)
		log.Printf("Available exchange are:")
		for _, exchange := range gateway.Exchanges {
			log.Printf("- %s", exchange.Symbol())
		}
		os.Exit(1)
	}

	return exchange
}

func NewGatewayOrFatal(exg gateway.Exchange, options gateway.Options) gateway.Gateway {
	gtw, ok := gateway.NewByExchange(exg, options)
	if !ok {
		log.Fatalf("Failed to find gateway for exchange %s", exg)
	}

	return gtw
}
