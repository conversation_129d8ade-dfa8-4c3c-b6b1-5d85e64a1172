package options_parser

import (
	"reflect"
	"testing"
	"time"
)

type TestOptions struct {
	Symbol              string
	AllowTradeEmptyBook bool
	AllowNCancelErrs    int
	WarmupPeriod        time.Duration
	Inventory           InventoryOptions
	Bid                 TradingOptions
	Ask                 TradingOptions
}

type InventoryOptions struct {
	MaxStockInventory float64
}

type TradingOptions struct {
	MinSpread float64
}

func TestParseFlagsToOpts(t *testing.T) {
	tests := []struct {
		flag     string
		expected TestOptions
	}{
		{
			"symbol=btcusdt",
			TestOptions{
				Symbol: "btcusdt",
			},
		},
		{
			"allow_trade_empty_book=true",
			TestOptions{
				AllowTradeEmptyBook: true,
			},
		},
		{
			"inventory.max_stock_inventory=52.5",
			TestOptions{
				Inventory: InventoryOptions{
					MaxStockInventory: 52.5,
				},
			},
		},
		{
			"warmup_period=5s",
			TestOptions{
				WarmupPeriod: 5 * time.Second,
			},
		},
		{
			"symbol=btcusdt allow_n_cancel_errs=5",
			TestOptions{
				Symbol:           "btcusdt",
				AllowNCancelErrs: 5,
			},
		},
		{
			"symbol=btcusdt\nallow_n_cancel_errs=5",
			TestOptions{
				Symbol:           "btcusdt",
				AllowNCancelErrs: 5,
			},
		},
		{
			"bid.min_spread=1.0 ask.min_spread=2.0",
			TestOptions{
				Bid: TradingOptions{MinSpread: 1.0},
				Ask: TradingOptions{MinSpread: 2.0},
			},
		},
		{
			"bid.min_spread=1.0;ask.min_spread=2.0",
			TestOptions{
				Bid: TradingOptions{MinSpread: 1.0},
				Ask: TradingOptions{MinSpread: 2.0},
			},
		},
	}

	for _, test := range tests {
		var result TestOptions

		err := ParseFlagsToOpts(test.flag, &result)
		if err != nil {
			t.Errorf("ParseFlagsToOpts, flag: %v err: %v.", test.flag, err)
		}

		if reflect.DeepEqual(result, test.expected) != true {
			t.Errorf("ParseFlagsToOpts, flag: %v\ngot: %v\nexpected: %v.", test.flag, result, test.expected)
		}
	}
}
