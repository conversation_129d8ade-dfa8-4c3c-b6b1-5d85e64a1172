package ratetracker

import (
	"sync"
	"testing"
	"time"
)

func TestBasicRateTracking(t *testing.T) {
	r := New(60) // 1 minute window

	// Test initial state
	if got := r.Total(); got != 0 {
		t.<PERSON><PERSON><PERSON>("initial total = %d, want 0", got)
	}

	// Test single increment
	r.Increment()
	if got := r.Total(); got != 1 {
		t.<PERSON>rf("after increment total = %d, want 1", got)
	}

	// Test increment by
	r.<PERSON>ent<PERSON>y(5)
	if got := r.Total(); got != 6 {
		t.<PERSON>rrorf("after increment by 5 total = %d, want 6", got)
	}

	// Test rate calculations
	if got := r.Avg(); got != 6.0 { // 6 since only 1 second has passed
		t.<PERSON><PERSON><PERSON>("Avg = %f, want 0.1", got)
	}
	if got := r.Min(); got != 0 {
		t.<PERSON><PERSON>("Min = %d, want 0", got)
	}
	if got := r.Max(); got != 6 {
		t.<PERSON>("Max = %d, want 6", got)
	}
}

func TestRateAverageWithElapsedTime(t *testing.T) {
	// Skip in short test mode
	if testing.Short() {
		t.Skip("skipping test in short mode")
	}

	// Create a rate tracker with a 5-second window (small enough for the test)
	r := New(5)

	// Add 10 events initially
	r.IncrementBy(10)

	// Initial average should be 10/1 = 10.0 (since less than 1 second has elapsed)
	initialAvg := r.Avg()
	if initialAvg < 9.0 || initialAvg > 10.0 {
		t.Errorf("Initial Avg = %f, want approximately 10.0", initialAvg)
	}

	// Sleep for 1 second
	time.Sleep(1 * time.Second)

	// Now the average should be approximately 10/1 = 10.0
	afterOneSec := r.Avg()
	if afterOneSec < 9.0 || afterOneSec > 10.0 {
		t.Errorf("Avg after 1s = %f, want approximately 10.0", afterOneSec)
	}

	// Sleep for 2 more seconds (total 3 seconds)
	time.Sleep(2 * time.Second)

	// Now the average should be approximately 10/3 = 3.33
	afterThreeSecs := r.Avg()
	if afterThreeSecs < 3.0 || afterThreeSecs > 3.7 {
		t.Errorf("Avg after 3s = %f, want approximately 3.33", afterThreeSecs)
	}

	// Add 5 more events
	r.IncrementBy(5)

	// Average should now be approximately 15/3 = 5.0
	afterAddingMore := r.Avg()
	if afterAddingMore < 4.7 || afterAddingMore > 5.3 {
		t.Errorf("Avg after adding 5 more = %f, want approximately 5.0", afterAddingMore)
	}
}

func TestWindowWraparound(t *testing.T) {
	// Use a small window to test wraparound
	r := New(1)

	// Add some initial events
	r.IncrementBy(2)

	// Force time to pass
	time.Sleep(1001 * time.Millisecond)

	// Add more events in a different second
	r.IncrementBy(3)

	// Total should handle the wraparound correctly
	if got := r.Total(); got != 3 {
		t.Errorf("after wraparound total = %d, want 3", got)
	}
}

func TestConcurrentAccess(t *testing.T) {
	r := New(60)

	var wg sync.WaitGroup
	numGoroutines := 10
	incrementsPerGoroutine := 1000

	// Start multiple goroutines incrementing concurrently
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < incrementsPerGoroutine; j++ {
				r.Increment()
			}
		}()
	}

	// Also start goroutines that read concurrently
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < incrementsPerGoroutine; j++ {
				_ = r.Total()
				_ = r.Avg()
			}
		}()
	}

	wg.Wait()

	// Verify final count
	expectedTotal := uint32(numGoroutines * incrementsPerGoroutine)
	if got := r.Total(); got != expectedTotal {
		t.Errorf("final total = %d, want %d", got, expectedTotal)
	}
}

func TestInvalidWindowSize(t *testing.T) {
	testCases := []struct {
		name       string
		windowSize int
		wantPanic  bool
	}{
		{"zero size", 0, true},
		{"negative size", -1, true},
		{"valid size", 60, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer func() {
				r := recover()
				if (r != nil) != tc.wantPanic {
					t.Errorf("New(%d): panic = %v, wantPanic = %v", tc.windowSize, r != nil, tc.wantPanic)
				}
			}()
			_ = New(tc.windowSize)
		})
	}
}

func TestHighFrequencyUpdates(t *testing.T) {
	r := New(60)

	// Simulate high frequency updates
	start := time.Now()
	for i := 0; i < 10000; i++ {
		r.Increment()
	}
	duration := time.Since(start)

	// Check that we can handle high frequency updates efficiently
	if duration > time.Second {
		t.Errorf("10000 rapid increments took %v, suggests inefficient implementation", duration)
	}
}

func TestLongWindowCleanup(t *testing.T) {
	testCases := []struct {
		name       string
		windowSize int           // in seconds
		sleepTime  time.Duration // time to sleep between checks
		increments int           // number of increments to perform
	}{
		{
			name:       "5 minute window cleanup",
			windowSize: 300, // 5 minutes
			sleepTime:  time.Minute,
			increments: 10,
		},
		{
			name:       "2 hour window cleanup",
			windowSize: 7200, // 2 hours
			sleepTime:  time.Minute,
			increments: 10,
		},
		{
			name:       "Check cleanup after window",
			windowSize: 60,               // 1 minute
			sleepTime:  90 * time.Second, // Sleep longer than window
			increments: 5,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			r := New(tc.windowSize)

			// Perform initial increments
			for i := 0; i < tc.increments; i++ {
				r.Increment()
			}

			initialTotal := r.Total()
			if initialTotal != uint32(tc.increments) {
				t.Errorf("Initial total = %v, want %v", initialTotal, tc.increments)
			}

			// Force time to advance
			r.mu.Lock()
			r.lastTick = r.lastTick.Add(-tc.sleepTime)
			r.mu.Unlock()

			// This should trigger cleanup
			currentTotal := r.Total()

			// If we slept longer than window, total should be 0
			if tc.sleepTime >= time.Duration(tc.windowSize)*time.Second {
				if currentTotal != 0 {
					t.Errorf("After window expired, total = %v, want 0", currentTotal)
				}
			}
		})
	}
}

func TestContinuousUpdates(t *testing.T) {
	r := New(7200) // 2 hour window

	// Simulate updates over time
	intervals := []struct {
		sleep time.Duration
		count int
	}{
		{0 * time.Second, 10}, // Initial burst
		{30 * time.Minute, 5}, // After 30 minutes
		{1 * time.Hour, 5},    // After 1 hour
		{90 * time.Minute, 5}, // After 1.5 hours
		{2 * time.Hour, 5},    // After 2 hours (should reset)
	}

	for _, interval := range intervals {
		r.mu.Lock()
		r.lastTick = r.lastTick.Add(-interval.sleep)
		r.mu.Unlock()

		for i := 0; i < interval.count; i++ {
			r.Increment()
		}

		// After 2 hours, only the most recent increments should be counted
		if interval.sleep >= 2*time.Hour {
			if total := r.Total(); total != uint32(interval.count) {
				t.Errorf("After %v, total = %v, want %v", interval.sleep, total, interval.count)
			}
		}
	}
}

func TestSampleCleanup(t *testing.T) {
	r := New(300) // 5 minute window

	// Add some initial samples
	for i := 0; i < 10; i++ {
		r.Increment()
	}

	// Verify initial state
	if total := r.Total(); total != 10 {
		t.Errorf("Initial total = %v, want 10", total)
	}

	// Force time forward by 6 minutes (beyond window size)
	r.mu.Lock()
	r.lastTick = r.lastTick.Add(-6 * time.Minute)
	r.mu.Unlock()

	// This should trigger cleanup of all samples
	if total := r.Total(); total != 0 {
		t.Errorf("After window expiration, total = %v, want 0", total)
	}

	// Add new samples and verify they're counted
	for i := 0; i < 5; i++ {
		r.Increment()
	}

	if total := r.Total(); total != 5 {
		t.Errorf("After new samples, total = %v, want 5", total)
	}
}

func BenchmarkIncrement(b *testing.B) {
	r := New(60)
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		r.Increment()
	}
}

func BenchmarkConcurrentIncrement(b *testing.B) {
	r := New(60)
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			r.Increment()
		}
	})
}

func BenchmarkTotal(b *testing.B) {
	r := New(60)
	// Add some data
	for i := 0; i < 1000; i++ {
		r.Increment()
	}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		r.Total()
	}
}
