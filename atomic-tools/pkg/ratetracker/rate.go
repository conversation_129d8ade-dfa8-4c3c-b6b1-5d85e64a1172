package ratetracker

import (
	"sync"
	"time"
)

// Rate tracks the average rate of events over a specified time window,
// sampling at 1-second granularity.
type Rate struct {
	mu         sync.RWMutex
	samples    []uint32  // One sample per second in the window
	windowSize int       // Window size in seconds
	lastTick   time.Time // Time of the last sample
	startTime  time.Time // Time when the ratetracker was created
}

func New(windowSizeSeconds int) *Rate {
	if windowSizeSeconds <= 0 {
		panic("window size must be positive")
	}

	now := time.Now()
	return &Rate{
		samples:    make([]uint32, windowSizeSeconds),
		windowSize: windowSizeSeconds,
		lastTick:   now,
		startTime:  now,
	}
}

// cleanOldSamples zeroes out any samples that have aged out of our window
func (r *Rate) cleanOldSamples(now time.Time) {
	// Calculate how many seconds we need to clear
	elapsed := int(now.Sub(r.lastTick).Seconds())
	if elapsed <= 0 {
		return
	}

	// If more time has passed than our window, clear everything
	if elapsed >= r.windowSize {
		for i := range r.samples {
			r.samples[i] = 0
		}
		r.lastTick = now
		return
	}

	// Clear elapsed samples
	lastIdx := int(r.lastTick.Unix() % int64(r.windowSize))

	// Clear samples in elapsed time range
	for i := 1; i <= elapsed; i++ {
		idx := (lastIdx + i) % r.windowSize
		r.samples[idx] = 0
	}

	r.lastTick = now
}

func (r *Rate) Increment() {
	now := time.Now()

	r.mu.Lock()
	defer r.mu.Unlock()

	r.cleanOldSamples(now)
	idx := int(now.Unix() % int64(r.windowSize))
	r.samples[idx]++
}

func (r *Rate) IncrementBy(n uint32) {
	now := time.Now()

	r.mu.Lock()
	defer r.mu.Unlock()

	r.cleanOldSamples(now)
	idx := int(now.Unix() % int64(r.windowSize))
	r.samples[idx] += n
}

func (r *Rate) Total() uint32 {
	now := time.Now()

	r.mu.RLock()
	defer r.mu.RUnlock()

	r.cleanOldSamples(now)

	var total uint32
	for _, count := range r.samples {
		total += count
	}
	return total
}

func (r *Rate) Last() uint32 {
	now := time.Now()

	r.mu.RLock()
	defer r.mu.RUnlock()

	r.cleanOldSamples(now)

	idx := int(now.Unix() % int64(r.windowSize))
	return r.samples[idx]
}

func (r *Rate) Min() uint32 {
	now := time.Now()

	r.mu.RLock()
	defer r.mu.RUnlock()

	r.cleanOldSamples(now)

	var min uint32
	for _, count := range r.samples {
		if count < min {
			min = count
		}
	}

	return min
}

func (r *Rate) Max() uint32 {
	now := time.Now()

	r.mu.RLock()
	defer r.mu.RUnlock()

	r.cleanOldSamples(now)

	var max uint32
	for _, count := range r.samples {
		if count > max {
			max = count
		}
	}

	return max
}

func (r *Rate) Avg() float64 {
	now := time.Now()

	r.mu.RLock()
	defer r.mu.RUnlock()

	r.cleanOldSamples(now)

	var total uint32
	for _, count := range r.samples {
		total += count
	}

	// Calculate the number of seconds that have passed since the tracker was created
	// so we can adjust the window size to the actual time passed
	elapsedSeconds := int(now.Sub(r.startTime).Seconds())

	if elapsedSeconds >= r.windowSize {
		return float64(total) / float64(r.windowSize)
	}

	// Minimum window size is 1 second to avoid division by zero
	if elapsedSeconds < 1 {
		elapsedSeconds = 1
	}

	return float64(total) / float64(elapsedSeconds)
}

func (r *Rate) WindowSize() int {
	return r.windowSize
}
