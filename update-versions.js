// update-versions.js
const fs = require('fs');
const path = require('path');

const version = process.argv[2];
if (!version) {
    console.error('Error: No version specified.');
    process.exit(1);
}

const filesToUpdate = [
    {
        path: path.join(__dirname, 'internal', 'app', 'routes.go'),
        regex: /\/\/.*@version.*/,
        replacement: `// @version      ${version}`,
    },
    {
        path: path.join(__dirname, 'version.go'),
        regex: /Version = ".*"/,
        replacement: `Version = "${version}"`,
    },
];

filesToUpdate.forEach(file => {
    try {
        let content = fs.readFileSync(file.path, 'utf8');
        if (!file.regex.test(content)) {
            console.error(`Error: Pattern not found in ${file.path}`);
            process.exit(1);
        }
        content = content.replace(file.regex, file.replacement);
        fs.writeFileSync(file.path, content, 'utf8');
        console.log(`Successfully updated ${file.path} to version ${version}`);
    } catch (error) {
        console.error(`Error processing file ${file.path}:`, error);
        process.exit(1);
    }
});