package botregionalclient

import (
	"encoding/json"
	"fmt"
	"net/url"
	"reflect"
	"strings"
	"time"

	"github.com/go-viper/mapstructure/v2"
	"google.golang.org/protobuf/types/known/structpb"
)

// ToGoStruct converts a *structpb.Struct into a Go struct.
// It uses mapstructure for robust decoding with custom hooks for string-to-type conversion.
func ToGoStruct[T any](s *structpb.Struct) (T, error) {
	var result T
	if s == nil {
		return result, nil
	}

	config := &mapstructure.DecoderConfig{
		Result: &result,
		// NOTE: TagName is removed. By default, mapstructure matches map keys to struct field
		// names, which is the behavior we want. This avoids the conflict with the `json:"-"`
		// tag on the Proxies field.
		WeaklyTypedInput: true, // This helps convert string-to-bool/int/float
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			// Add custom hooks for types that WeaklyTypedInput doesn't handle
			stringToTimeDurationHookFunc(),
			stringToSliceURLHookFunc(),
			stringToSliceStringHookFunc(),
		),
	}

	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return result, fmt.Errorf("failed to create mapstructure decoder: %w", err)
	}

	if err = decoder.Decode(s.AsMap()); err != nil {
		return result, fmt.Errorf("failed to decode map to struct: %w", err)
	}

	return result, nil
}

// FromGoStruct converts any Go struct or value into a *structpb.Struct.
// It uses JSON marshaling as an intermediate step to create a map.
func FromGoStruct(v interface{}) (*structpb.Struct, error) {
	if v == nil {
		return nil, nil
	}

	bytes, err := json.Marshal(v)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal struct to JSON: %w", err)
	}

	var interfacedMap map[string]interface{}
	if err = json.Unmarshal(bytes, &interfacedMap); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON to map: %w", err)
	}

	s, err := structpb.NewStruct(interfacedMap)
	if err != nil {
		return nil, fmt.Errorf("failed to create struct from map: %w", err)
	}

	return s, nil
}

// stringToTimeDurationHookFunc is a mapstructure hook that converts a string to a time.Duration.
func stringToTimeDurationHookFunc() mapstructure.DecodeHookFunc {
	return func(f reflect.Type, t reflect.Type, data interface{}) (interface{}, error) {
		if f.Kind() != reflect.String || t != reflect.TypeOf(time.Duration(0)) {
			return data, nil
		}
		return time.ParseDuration(data.(string))
	}
}

// stringToSliceURLHookFunc is a mapstructure hook that converts a string
// (either a JSON array or comma-separated) into a slice of *url.URL.
func stringToSliceURLHookFunc() mapstructure.DecodeHookFunc {
	return func(f reflect.Type, t reflect.Type, data interface{}) (interface{}, error) {
		if f.Kind() != reflect.String || t.Kind() != reflect.Slice || t.Elem() != reflect.TypeOf(&url.URL{}) {
			return data, nil
		}

		raw := data.(string)
		if raw == "" {
			return []*url.URL{}, nil
		}

		var urlsStr []string
		// The string from the DB might be a JSON array or a simple comma-separated list.
		if err := json.Unmarshal([]byte(raw), &urlsStr); err != nil {
			urlsStr = strings.Split(raw, ",")
		}

		urls := make([]*url.URL, 0, len(urlsStr))
		for _, urlStr := range urlsStr {
			trimmedURL := strings.TrimSpace(urlStr)
			if trimmedURL == "" {
				continue
			}
			u, err := url.Parse(trimmedURL)
			if err != nil {
				return nil, fmt.Errorf("failed to parse URL '%s': %w", urlStr, err)
			}
			urls = append(urls, u)
		}
		return urls, nil
	}
}

// stringToSliceStringHookFunc is a mapstructure hook that converts a string
// (either a JSON array or comma-separated) into a slice of strings.
func stringToSliceStringHookFunc() mapstructure.DecodeHookFunc {
	return func(f reflect.Type, t reflect.Type, data interface{}) (interface{}, error) {
		if f.Kind() != reflect.String || t.Kind() != reflect.Slice || t.Elem().Kind() != reflect.String {
			return data, nil
		}

		raw := data.(string)
		if raw == "" {
			return []string{}, nil
		}

		var items []string
		if err := json.Unmarshal([]byte(raw), &items); err != nil {
			items = strings.Split(raw, ",")
			for i := range items {
				items[i] = strings.TrimSpace(items[i])
			}
		}
		return items, nil
	}
}

// MergeToExistingStruct decodes a protobuf Struct into an existing Go struct pointer.
// This is ideal for applying partial updates, as it merges data instead of replacing it.
func MergeToExistingStruct(target interface{}, data *structpb.Struct) error {
	if data == nil {
		return nil // Nothing to merge
	}

	// Create a mapstructure decoder configured to merge into the 'target' struct.
	config := &mapstructure.DecoderConfig{
		Result:           target, // Decode into the existing pointer provided by 'target'.
		WeaklyTypedInput: true,
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			stringToTimeDurationHookFunc(),
			stringToSliceURLHookFunc(),
			stringToSliceStringHookFunc(),
		),
	}

	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return fmt.Errorf("failed to create mapstructure decoder: %w", err)
	}

	// Decode the map from the protobuf Struct into the target.
	// This will overwrite fields in 'target' that are present in the 'data' map
	// and leave other fields in 'target' untouched.
	return decoder.Decode(data.AsMap())
}
