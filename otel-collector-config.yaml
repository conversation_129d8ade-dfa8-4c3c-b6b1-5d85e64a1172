# Receivers collect telemetry from one or more sources.
# They can be pulled or push-based, and may support one
# or more data sources.
receivers:
  # Data sources: traces, metrics, logs
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

  # Data sources: metrics
  hostmetrics:
    collection_interval: 1m # default 1m
    scrapers:
      cpu:
      memory:
      load:
      network:
      processes:
      paging:
      disk:
      filesystem:


# Processors take the data collected by receivers and modify
# it or transform it before sending it to the exporters.
# Data processing happens, according to rules or settings defined
# for each processor, which might include filtering, dropping,
# renaming, or recalculating telemetry, among other operations.
# The order of the processors in a pipeline determines the order
# of the processing operations that the Collector applies to the signal.
processors:
  # Data sources: traces, metrics, logs
  batch:
    # Number of spans, metric data points, or log records after which a
    # batch will be sent regardless of the timeout. Send_batch_size acts
    # as a trigger and does not affect the size of the batch. If you need
    # to enforce batch size limits sent to the next component in the
    # pipeline, see send_batch_max_size.
    send_batch_size: 1
    # Time duration after which a batch will be sent regardless of size.
    # If set to zero, send_batch_size is ignored as data will be sent
    # immediately, subject to only send_batch_max_size.
    timeout: 0s
    # The upper limit of the batch size. 0 means no upper limit of the batch size.
    # This property ensures that larger batches are split into smaller units.
    # It must be greater than or equal to send_batch_size.
    send_batch_max_size: 0

  resourcedetection:
    # Using OTEL_RESOURCE_ATTRIBUTES envvar, env detector adds custom labels.
    detectors: [env, system] # include ec2 for AWS, gcp for GCP and azure for Azure.
    timeout: 2s

# Exporters send data to one or more backends or destinations.
# Exporters can be pull or push based, and may support one or more data sources.

exporters:
  # Data sources: traces, metrics, logs
  otlp:
    endpoint: signoz-otel-collector:4320
    tls:
      insecure: true
  # Data sources: traces, metrics, logs
  otlphttp:
      endpoint: signoz-otel-collector:4318
      tls:
        insecure: true

# Extensions are optional components that expand the capabilities of the Collector
# to accomplish tasks not directly involved with processing telemetry data.
extensions:
  health_check: # default = localhost:13133
  pprof: # Default = localhost:1777
  # Enables an extension that serves zPages, an HTTP endpoint that provides live data
  # for debugging different components that were properly instrumented for such.
  # All core exporters and receivers provide some zPage instrumentation.
  # zPages are useful for in-process diagnostics without having to depend on any
  # backend to examine traces or metrics.
  # Exposed routes: https://github.com/open-telemetry/opentelemetry-collector/blob/main/extension/zpagesextension/README.md#exposed-zpages-routes
  zpages: # default = localhost:55679

service:
  telemetry:
    logs:
      level: "debug"
  pipelines: # https://opentelemetry.io/docs/collector/configuration/#pipelines
    metrics:
      receivers: [otlp]
      processors: [batch]
      exporters: [otlp]
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [otlp]
    logs:
      receivers: [otlp]
      processors: [batch]
      exporters: [otlp]
