package session

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"

	"github.com/alexedwards/scs/v2"
	"github.com/gin-gonic/gin"
	"github.com/segmentio/encoding/json"
	"github.com/uptrace/bun"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
)

const (
	UserSession = "session"
	UserID      = "userID"
)

var _spanName = "SessionManager/%s"

type Manager struct {
	session *scs.SessionManager
	tracer  trace.Tracer
}

type ManagerConfig struct {
	// IdleTimeout controls the maximum length of time a session can be inactive
	// before it expires. For example, some applications may wish to set this so
	// there is a timeout after 20 minutes of inactivity.
	IdleTimeout int `mapstructure:"idle_timeout"`

	// Lifetime controls the maximum length of time that a session is valid for
	// before it expires. The lifetime is an 'absolute expiry' which is set when
	// the session is first created and does not change.
	Lifetime int `mapstructure:"lifetime"`

	// CleanupInterval controls how frequently expired session data is removed by the
	// background cleanup goroutine.
	CleanupInterval int `mapstructure:"cleanup_interval"`

	// Cookie contains the configuration settings for session cookies.
	Cookie struct {
		// Name sets the name of the session cookie. It should not contain
		// whitespace, commas, colons, semicolons, backslashes, the equals sign or
		// control characters as per RFC6265. The default cookie name is "session".
		// If your application uses two different sessions, you must make sure that
		// the cookie name for each is unique.
		Name string `mapstructure:"name"`

		// Domain sets the 'Domain' attribute on the session cookie. By default,
		// it will be set to the domain name that the cookie was issued from.
		Domain string `mapstructure:"domain"`

		// HttpOnly sets the 'HttpOnly' attribute on the session cookie. The
		// default value is true.
		HttpOnly bool `mapstructure:"http_only"`

		// Path sets the 'Path' attribute on the session cookie. The default value
		// is "/". Passing the empty string "" will result in it being set to the
		// path that the cookie was issued from.
		Path string `mapstructure:"path"`

		// Persist sets whether the session cookie should be persistent or not
		// (i.e. whether it should be retained after a user closes their browser).
		// The default value is true, which means that the session cookie will not
		// be destroyed when the user closes their browser and the appropriate
		// 'Expires' and 'MaxAge' values will be added to the session cookie. If you
		// want to only persist some sessions (rather than all of them), then set this
		// to false and call the RememberMe() method for the specific sessions that you
		// want to persist.
		Persist bool `mapstructure:"persist"`

		// SameSite controls the value of the 'SameSite' attribute on the session
		// cookie. By default, this is set to 'SameSite=Lax'. If you want no SameSite
		// attribute or value in the session cookie then you should set this to 0.
		SameSite int `mapstructure:"same_site"`

		// Secure sets the 'Secure' attribute on the session cookie. The default
		// value is false. It's recommended that you set this to true and serve all
		// requests over HTTPS in production environments.
		Secure bool `mapstructure:"secure"`
	} `mapstructure:"cookie"`
}

func NewManager(db *bun.DB, cfg ManagerConfig) *Manager {
	cleanupInterval := time.Duration(cfg.CleanupInterval) * time.Minute

	sm := &scs.SessionManager{
		Codec: JSONCodec{},
	}

	sessionRepo, err := NewManagerWithCleanupInterval(db, cleanupInterval)
	if err != nil {
		zap.L().Fatal("failed to initialize session manager", zap.Error(err))
	}

	sm.IdleTimeout = time.Duration(cfg.IdleTimeout) * time.Hour
	sm.Lifetime = time.Duration(cfg.Lifetime) * time.Hour
	sm.Store = sessionRepo
	sm.Cookie = scs.SessionCookie{
		Name:     cfg.Cookie.Name,
		Domain:   cfg.Cookie.Domain,
		HttpOnly: cfg.Cookie.HttpOnly,
		Path:     cfg.Cookie.Path,
		Persist:  cfg.Cookie.Persist,
		SameSite: http.SameSite(cfg.Cookie.SameSite),
		Secure:   cfg.Cookie.Secure,
	}

	return &Manager{
		session: sm,
		tracer:  otel.Tracer(_ScopeName),
	}
}

func (sm *Manager) Create(c *gin.Context, s *Session) {
	_, span := sm.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "Create"))
	defer span.End()

	ctx := c.MustGet(UserSession).(context.Context)
	ctxWithSpan := trace.ContextWithSpan(ctx, span)

	sm.session.Put(ctxWithSpan, UserSession, s)
}

func (sm *Manager) RenewToken(c *gin.Context) error {
	ctx := c.MustGet(UserSession).(context.Context)
	return sm.session.RenewToken(ctx)
}

func (sm *Manager) UserIDFromContext(c *gin.Context) string {
	ctx := c.MustGet(UserSession).(context.Context)
	return sm.session.GetString(ctx, UserID)
}

func (sm *Manager) FromContext(c *gin.Context) *Session {
	ctx := c.MustGet(UserSession).(context.Context)
	userID := sm.session.GetString(ctx, UserID)
	expiryAt := sm.session.Deadline(ctx)
	token := sm.session.Token(ctx)

	return &Session{
		Token:  token,
		UserID: userID,
		Expiry: expiryAt,
	}
}

func (sm *Manager) Delete(c *gin.Context) error {
	ctx := c.MustGet(UserSession).(context.Context)
	return sm.session.Destroy(ctx)
}

func (sm *Manager) LoadFromQueryParam() gin.HandlerFunc {
	return func(c *gin.Context) {
		s := sm.session

		token := c.Query("token")
		if token == "" {
			ginx.ResError(c, errors.ForbiddenResourceError)
			return
		}

		session, err := s.Load(c.Request.Context(), token)
		if err != nil {
			ginx.ResError(c, errors.Wrap(err, "loading user session info"))
			return
		}

		c.Set(UserSession, session)

		// Propagate the span context in the Gin context for subsequent handlers
		c.Request = c.Request.WithContext(c.Request.Context())

		c.Next()
	}
}

func (sm *Manager) LoadAndSave() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()

		token := ginx.GetToken(c)

		ctxWithSession, err := sm.session.Load(ctx, token)
		if err != nil {
			ginx.ResError(c, errors.UnexpectedError)
			return
		}

		// Set the user session in the Gin context
		c.Set(UserSession, ctxWithSession)

		// Propagate the span context in the Gin context for subsequent handlers
		c.Request = c.Request.WithContext(ctxWithSession)

		c.Next()

		switch sm.session.Status(ctxWithSession) {
		case scs.Modified:
			token, _, err = sm.session.Commit(ctxWithSession)
			if err != nil {
				ginx.ResError(c, errors.UnexpectedError)
				return
			}
		default:
			return
		}
	}
}

// JSONCodec is used for encoding/decoding Session data to and from a byte
// slice using JSON encoding.
type JSONCodec struct{}

// Encode converts a Session deadline and values into a byte slice.
func (JSONCodec) Encode(deadline time.Time, values map[string]interface{}) ([]byte, error) {
	aux := struct {
		Deadline time.Time
		Values   map[string]interface{}
	}{
		Deadline: deadline,
		Values:   values,
	}

	data, err := json.Marshal(aux)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// Decode converts a byte slice into a Session deadline and values.
func (JSONCodec) Decode(b []byte) (time.Time, map[string]interface{}, error) {
	aux := struct {
		Deadline time.Time
		Values   map[string]interface{}
	}{}

	if err := json.Unmarshal(b, &aux); err != nil {
		return time.Time{}, nil, err
	}

	return aux.Deadline, aux.Values, nil
}
