package session

import (
	"context"
	"log"
	"time"

	"github.com/segmentio/encoding/json"
	"github.com/uptrace/bun"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/pkg/util"
)

var _sessionSpanName = "SessionStore/%s"

const _ScopeName = "SessionStore"

// Store represents the Session store.
type Store struct {
	db          *bun.DB
	trace       trace.Tracer
	stopCleanup chan bool
	codec       JSONCodec
}

type MD struct {
	IP           string
	DeviceType   string
	DeviceName   string
	PlatformType string
	Location     Location
}

type Location struct {
	Country string
	City    string
	Area    string
}

type Session struct {
	bun.BaseModel `bun:"table:sessions,alias:s"`

	ID        uint64    `json:"id" bun:"id,pk,autoincrement"`
	Token     string    `json:"token" bun:"token,pk"`
	UserID    string    `json:"userId" bun:"user_id"`
	Details   MD        `json:"details" bun:"data,type:json"`
	Expiry    time.Time `json:"expiry" bun:"expiry,nullzero,notnull"`
	CreatedAt time.Time `json:"createdAt" bun:"created_at,nullzero,notnull"`
	UpdatedAt time.Time `json:"updatedAt" bun:"updated_at,nullzero,notnull,default:current_timestamp"`
}

// NewManagerWithCleanupInterval returns a new Store instance. The cleanupInterval
// parameter controls how frequently expired Session data is removed by the
// background cleanup goroutine. Setting it to 0 prevents the cleanup goroutine
// from running (i.e. expired sessions will not be removed).
func NewManagerWithCleanupInterval(db *bun.DB, cleanupInterval time.Duration) (*Store, error) {
	b := &Store{
		db:    db,
		codec: JSONCodec{},
		trace: otel.Tracer(_ScopeName),
	}

	if cleanupInterval > 0 {
		go b.startCleanup(cleanupInterval)
	}

	return b, nil
}

// FindCtx returns the data for a given Session token.
// If the Session token is not found or is expired, the returned exists flag will
// be set to false.
func (sr *Store) FindCtx(ctx context.Context, token string) (bb []byte, exists bool, err error) {
	s := &Session{}
	count, err := sr.db.NewSelect().
		Model(s).
		Where("token = ? AND expiry >= ?", token, time.Now()).
		ScanAndCount(ctx)
	if count == 0 {
		return []byte{}, false, nil
	}
	if err != nil {
		return []byte{}, false, err
	}

	// Update the updated_at field to track the last time that this token was used
	if _, err = sr.db.NewUpdate().
		Model(s).
		Set("updated_at = ?", time.Now()).
		Where("token = ?", s.Token).
		Exec(ctx); err != nil {
		return []byte{}, false, err
	}

	encode, err := sr.codec.Encode(s.Expiry, map[string]interface{}{UserSession: &s, UserID: &s.UserID})
	if err != nil {
		return []byte{}, false, err
	}

	return encode, true, nil
}

// CommitCtx adds a Session token and data to the Store instance with the
// given expiry time. If the Session token already exists, then the data and expiry
// time are updated.
func (sr *Store) CommitCtx(ctx context.Context, token string, bb []byte, expiry time.Time) error {
	_, sb, err := sr.codec.Decode(bb)
	if err != nil {
		return errors.Wrap(err, "failed to decode session")
	}

	var out struct {
		Session `json:"session"`
	}
	if err = util.MapToStruct(sb, &out); err != nil {
		return err
	}

	s := &Session{
		Token:   token,
		UserID:  out.UserID,
		Details: out.Details,
		Expiry:  expiry,
	}

	if _, err = sr.db.NewInsert().
		Model(s).
		On("CONFLICT (token) DO UPDATE").
		Set("data = EXCLUDED.data").
		Set("user_id = EXCLUDED.user_id").
		Exec(ctx); err != nil {
		return errors.Wrap(err, "failed to insert new session")
	}

	return nil
}

// DeleteCtx removes a Session token and corresponding data.
func (sr *Store) DeleteCtx(ctx context.Context, token string) error {
	_, err := sr.db.NewUpdate().
		Model(&Session{}).
		Set("expiry = ?", time.Now()).
		Where("token = ?", token).
		Exec(ctx)
	if err != nil {
		return errors.Wrap(err, "failed to update session")
	}

	return nil
}

// AllCtx returns a map containing the token and data for all active (i.e.
// not expired) sessions in the Store instance.
func (sr *Store) AllCtx(ctx context.Context) (map[string][]byte, error) {
	rows, err := sr.db.NewSelect().
		Model(&[]Session{}).
		Where("expiry >= ?", time.Now()).
		Rows(ctx)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	ss := make(map[string][]byte)

	for rows.Next() {
		s := &Session{}
		if err = sr.db.ScanRow(ctx, rows, s); err != nil {
			return nil, err
		}

		sb, err := json.Marshal(s)
		if err != nil {
			return nil, err
		}

		ss[s.Token] = sb
	}
	if err = rows.Close(); err != nil {
		return nil, err
	}

	return ss, nil
}

func (sr *Store) startCleanup(interval time.Duration) {
	sr.stopCleanup = make(chan bool)
	ticker := time.NewTicker(interval)
	for {
		select {
		case <-ticker.C:
			err := sr.deleteExpired()
			if err != nil {
				log.Println(err)
			}
		case <-sr.stopCleanup:
			ticker.Stop()
			return
		}
	}
}

// StopCleanup terminates the background cleanup goroutine for the Store
// instance. It's rare to terminate this; generally Store instances and
// their cleanup goroutines are intended to be long-lived and run for the lifetime
// of your application.
//
// There may be occasions though when your use of the Store is transient.
// An example is creating a new Store instance in a test function. In this
// scenario, the cleanup goroutine (which will run forever) will prevent the
// Store object from being garbage collected even after the test function
// has finished. You can prevent this by manually calling StopCleanup.
func (sr *Store) StopCleanup() {
	if sr.stopCleanup != nil {
		sr.stopCleanup <- true
	}
}

func (sr *Store) deleteExpired() error {
	ctx := context.Background()
	_, err := sr.db.NewDelete().
		Model(&Session{}).
		Where("expiry < ?", time.Now()).
		Exec(ctx)
	if err != nil {
		return err
	}

	return nil
}

// We have to add the plain Store methods here to be recognized a Store
// by the go compiler. Not using a separate type makes any errors caught
// only at runtime instead of compile time. Oh, well.

func (sr *Store) Find(token string) ([]byte, bool, error) {
	panic("missing context arg")
}
func (sr *Store) Commit(token string, bb []byte, expiry time.Time) error {
	panic("missing context arg")
}
func (sr *Store) Delete(token string) error {
	panic("missing context arg")
}
