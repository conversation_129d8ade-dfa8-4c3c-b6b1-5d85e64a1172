package casbin

import (
	"context"
	"net/http"
	"time"

	"github.com/casbin/casbin/v2"
	"github.com/casbin/casbin/v2/model"
	"github.com/casbin/casbin/v2/util"
	casbinbunadapter "github.com/cuipeiyu/casbin-bun-adapter"
	"github.com/pkg/errors"
	"github.com/uptrace/bun"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
)

var (
	Roles = []string{Role<PERSON><PERSON>, RoleUser, RoleManager, RoleAdmin}
)

// Define constants for role names.
const (
	GuestUser   = "guest"
	RoleAdmin   = "role_admin"
	RoleGuest   = "role_guest"
	RoleUser    = "role_user"
	RoleManager = "role_manager"
)

type PolicyEnforcer struct {
	enforcer *casbin.SyncedEnforcer
	log      *zap.Logger
}

// initModel initializes a new Casbin model with predefined definitions.
//
//	This function sets up the basic definitions for a Casbin model suitable for access control.
//	The model consists of multiple sections, each with its purpose and parameters.
//
//	The "r" section defines the request structure, including subject, object, and action identifiers.
//	The "p" section defines access control policies for subjects, objects, and actions.
//	The "g" section defines role grouping policies (not explicitly used in this model).
//	The "e" section defines the policy effect, specifying whether an access attempt is allowed or denied.
//	The "m" section defines the matchers that determine whether a policy rule is applicable.
func initModel() model.Model {
	// Create a new Casbin model instance.
	m := model.NewModel()

	// AddDef adds a new section definition to the model.
	// Here, we are adding definitions for "r", "p", "g", "e", and "m" sections.
	// Each section is identified by a keyword and associated parameters.
	// These definitions form the basis of the access control model.

	// "r" section definition: Defines request structure with subject, object, and action identifiers.
	m.AddDef("r", "r", "sub, obj, act")

	// "p" section definition: Defines access control policies for subjects, objects, and actions.
	m.AddDef("p", "p", "sub, obj, act")

	// "g" section definition: Defines role grouping policies.
	m.AddDef("g", "g", "_, _")

	// "e" section definition: Defines the policy effect, specifying allow or deny decision.
	m.AddDef("e", "e", "some(where (p.eft == allow))")

	// "m" section definition: Defines matchers for policy enforcement conditions.
	// Matchers combine subject, object, and action identifiers with specified matching functions.
	m.AddDef("m", "m", "g(r.sub, p.sub) && keyMatch2(r.obj, p.obj) && regexMatch(r.act, p.act)")

	return m
}

func NewPolicyEnforcer(ctx context.Context, db *bun.DB, logger *zap.Logger) *PolicyEnforcer {
	span := trace.SpanFromContext(ctx)
	defer span.End()
	policyEnforcer := &PolicyEnforcer{log: logger}
	a, err := casbinbunadapter.NewAdapterWithClient(db, casbinbunadapter.WithTableName("public", "casbin_rules"))
	if err != nil {
		logger.Fatal("failed to start casbin adapter", zap.Error(err), zap.Any("context", ctx))
	}

	m := initModel()
	e, err := casbin.NewSyncedEnforcer(m, a)
	if err != nil {
		logger.Fatal("failed to start casbin synced cached enforcer", zap.Error(err), zap.Any("context", ctx))
	}

	// Update new policies every 10 seconds
	e.StartAutoLoadPolicy(10 * time.Second)

	// Define access control rules to determine permissions for different roles.
	// Each inner slice represents a rule with the first element being the role,
	// the second element being the path/resource, and the third element being the HTTP method or Websockets Command.
	// These rules specify which roles have access to specific resources and methods.
	rules := [][]string{
		// Admin
		{RoleAdmin, "*", "(POST)|(GET)|(DELETE)|(PATCH)|(CMD)"},

		// Guest is used for public endpoints
		{RoleGuest, "/api/v1/users/login", http.MethodPost},
		{RoleGuest, "/api/v1/health", http.MethodGet},

		// User
		{RoleUser, "/api/v1/users/logout", http.MethodPost},
		{RoleUser, "/api/v1/users/sessions/active", http.MethodGet},
		{RoleUser, "/api/v1/users/sessions/:id", http.MethodDelete},
		{RoleUser, "/api/v1/users/sessions/bulkDelete", http.MethodPost},
		{RoleUser, "/api/v1/users/info", http.MethodGet},
		{RoleUser, "/api/v1/users/me", http.MethodGet},
		{RoleUser, "/api/v1/users/password", http.MethodPatch},

		// Exchanges
		{RoleUser, "/api/v1/exchanges", http.MethodGet},
		{RoleUser, "/api/v1/exchanges/:exchangeId", http.MethodGet},

		// Accounts
		{RoleUser, "/api/v1/accounts", http.MethodPost},
		{RoleUser, "/api/v1/accounts", http.MethodGet},
		{RoleUser, "/api/v1/accounts/:accountId", http.MethodGet},
		{RoleUser, "/api/v1/accounts/:accountId", http.MethodDelete},

		// Regions
		{RoleUser, "/api/v1/regions", http.MethodGet},
		{RoleUser, "/api/v1/regions/:regionId", http.MethodGet},

		// Trollbox
		{RoleUser, "/api/v1/trollbox/history", http.MethodGet},
		{RoleUser, "/api/v1/trollbox/search", http.MethodPost},
		{RoleUser, "/api/v1/trollbox/filter", http.MethodPost},

		// Bots
		{RoleUser, "/api/v1/bots", http.MethodPost},
		{RoleUser, "/api/v1/bots", http.MethodGet},
		{RoleUser, "/api/v1/bots/:botId/start", http.MethodPost},
		{RoleUser, "/api/v1/bots/:botId/stop", http.MethodPost},
		{RoleUser, "/api/v1/bots/:botId", http.MethodPatch},
		{RoleUser, "/api/v1/bots/:botId", http.MethodGet},
		{RoleUser, "/api/v1/bots/:botId", http.MethodDelete},
		{RoleUser, "/api/v1/bots/state", http.MethodGet},
		{RoleUser, "/api/v1/bots/:botId/state", http.MethodGet},

		// Bots params
		{RoleUser, "/api/v1/params", http.MethodGet},
		{RoleUser, "/api/v1/params", http.MethodPost},
		{RoleUser, "/api/v1/params/:botId/activeParams", http.MethodGet},
		{RoleUser, "/api/v1/params/bulkDelete", http.MethodPost},

		// Bot Param Groups
		{RoleUser, "/api/v1/params/groups", http.MethodPost},
		{RoleUser, "/api/v1/params/groups", http.MethodGet},
		{RoleUser, "/api/v1/params/groups/:groupId", http.MethodDelete},

		// Bot Param Groups Relation
		{RoleUser, "/api/v1/params/groups/bots", http.MethodGet},
		{RoleUser, "/api/v1/params/groups/:groupId/bots/:botId", http.MethodPost},
		{RoleUser, "/api/v1/params/groups/:groupId/bots/:botId", http.MethodDelete},

		// Push Notifications
		{RoleUser, "/api/v1/notifications/push", http.MethodPatch},
		{RoleUser, "/api/v1/notifications/push", http.MethodGet},
		{RoleUser, "/api/v1/notifications/push/:deviceToken", http.MethodDelete},

		// Notification Subscriptions
		{RoleUser, "/api/v1/notifications/subscribe", http.MethodPost},
		{RoleUser, "/api/v1/notifications/subscribe", http.MethodGet},
		{RoleUser, "/api/v1/notifications/subscribe/bulkDelete", http.MethodPost},

		// User ws
		{RoleUser, "/ws", http.MethodGet},
		{RoleUser, "chat/trollbox", "(POST)|(GET)"},
		{RoleUser, "bots/state", http.MethodGet},
	}

	policies, err := e.GetPolicy()
	if err != nil {
		logger.Fatal("failed to get casbin policies", zap.Error(err), zap.Any("context", ctx))
	}

	// We need to verify manually because the adapter that we are using does not support validation feature
	for _, rule := range rules {
		if !policyExists(policies, rule) {
			if _, err = e.AddPolicy(rule); err != nil {
				logger.Fatal("failed to create casbin policy", zap.Error(err), zap.Any("context", ctx))
			}
		}
	}

	// Define groups to establish relationships between roles.
	// Each inner slice represents a group with the first element being the parent role
	// and the second element being the child role. These relationships help determine
	// which roles inherit permissions from others.
	groups := [][]string{
		// RoleUser inherits permissions from RoleGuest
		{RoleUser, RoleGuest},
		// RoleManager inherits permissions from RoleUser
		{RoleManager, RoleUser},
		// RoleAdmin inherits permissions from RoleManager
		{RoleAdmin, RoleManager},
	}

	// We need to verify manually because the adapter that we are using does not support validation feature
	for _, group := range groups {
		if !groupingPolicyExists(policies, group) {
			if _, err = e.AddGroupingPolicy(group); err != nil {
				logger.Fatal("failed to create casbin group policy", zap.Error(err), zap.Any("context", ctx))
			}
		}
	}

	// Guarantee that the Anonymous user has the Anonymous Role for be able to log in
	if _, err = e.AddRoleForUser(GuestUser, RoleGuest); err != nil {
		logger.Fatal("failed to added role for default user", zap.Error(err), zap.Any("context", ctx))
	}
	policyEnforcer.enforcer = e
	return policyEnforcer
}

// policyExists checks if a given policy rule already exists in the policies slice.
func policyExists(policies [][]string, rule []string) bool {
	for _, p := range policies {
		if util.ArrayEquals(p, rule) {
			return true
		}
	}
	return false
}

// groupingPolicyExists checks if a given grouping policy already exists in the policies slice.
func groupingPolicyExists(policies [][]string, group []string) bool {
	for _, p := range policies {
		if util.ArrayEquals(p, group) {
			return true
		}
	}
	return false
}

// Enforce check if the user has the permission to access the requested resource.
func (p *PolicyEnforcer) Enforce(userID, resource, method string) (bool, error) {
	return p.enforcer.Enforce(userID, resource, method)
}

// AddRoleForUser adds a role for a user in the policy enforcer.
// It returns an error if the operation fails or if the user already has the provided role.
func (p *PolicyEnforcer) AddRoleForUser(userID, role string) error {
	exists, err := p.enforcer.AddRoleForUser(userID, role)
	if err != nil {
		return errors.Wrapf(err, "add role %s for user %s", role, userID)
	}

	if !exists {
		return errors.New("user already has provided role")
	}

	return nil
}

// DeleteRoleForUser deletes a role for a user in the policy enforcer.
// It returns an error if the operation fails or if the user already has the provided role.
func (p *PolicyEnforcer) DeleteRoleForUser(userID, role string) error {
	exists, err := p.enforcer.DeleteRoleForUser(userID, role)
	if err != nil {
		return errors.Wrapf(err, "delete role %s for user %s", role, userID)
	}

	if !exists {
		return errors.New("user does not has provided role")
	}

	return nil
}

// GetUsers retrieves a map of user roles to associated users from the policy enforcer,
// including roles of RoleUser, RoleAdmin, and RoleManager.
// It consolidates user information from multiple roles and returns the map structure.
func (p *PolicyEnforcer) GetUsers(ctx context.Context) (map[string][]string, error) {
	roles := []string{RoleUser, RoleAdmin, RoleManager}
	usersByRole := make(map[string][]string)

	for _, roleName := range roles {
		users, err := p.getUsersForRole(roleName)
		if err != nil {
			p.log.Error("failed to get users for role", zap.String("role", roleName), zap.Any("context", ctx))
			continue
		}
		usersByRole[roleName] = users
	}

	return usersByRole, nil
}

// It excludes users with predefined roles (like 'user', 'admin', 'manager') from the returned list.
func (p *PolicyEnforcer) getUsersForRole(roleName string) ([]string, error) {
	allUsers, err := p.enforcer.GetUsersForRole(roleName)
	if err != nil {
		return nil, err
	}

	var filteredUsers []string
	for _, user := range allUsers {
		if !checkUserPredefinedRole(user) {
			filteredUsers = append(filteredUsers, user)
		}
	}

	return filteredUsers, nil
}

func checkUserPredefinedRole(userID string) bool {
	for _, role := range Roles {
		if userID == role {
			return true
		}
	}
	return false
}
