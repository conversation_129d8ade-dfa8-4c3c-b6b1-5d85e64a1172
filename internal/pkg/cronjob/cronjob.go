package cronjob

import (
	"time"
	_ "time/tzdata"

	"github.com/hibiken/asynq"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

type Manager struct {
	logger           *zap.Logger
	scheduler        *asynq.Scheduler
	client           *asynq.Client
	server           *asynq.Server
	serverHandlerMux *asynq.ServeMux
}

func New(redisAddr string, logger *zap.Logger) (*Manager, error) {
	if redisAddr == "" {
		return nil, errors.New("redis addr is empty")
	}

	redisClientOpt := asynq.RedisClientOpt{
		Addr:         redisAddr,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
	}

	loc, err := time.LoadLocation("America/Sao_Paulo")
	if err != nil {
		return nil, err
	}

	scheduler := asynq.NewScheduler(asynq.RedisClientOpt{Addr: redisAddr}, &asynq.SchedulerOpts{
		LogLevel: parseLogLevel(logger.Level()),
		Location: loc,
	})

	client := asynq.NewClient(redisClientOpt)

	srv := asynq.NewServer(redisClientOpt, asynq.Config{
		Concurrency: 0,
		BaseContext: nil,
		Queues: map[string]int{
			"critical": 6,
			"default":  3,
			"low":      1,
		},
		StrictPriority:           false,
		ErrorHandler:             nil,
		LogLevel:                 parseLogLevel(logger.Level()),
		ShutdownTimeout:          time.Second * 10,
		HealthCheckInterval:      time.Second * 15,
		DelayedTaskCheckInterval: time.Second * 5,
		GroupGracePeriod:         time.Second * 60,
		GroupMaxDelay:            0,
		GroupMaxSize:             0,
		GroupAggregator:          nil,
	})

	return &Manager{
		scheduler:        scheduler,
		client:           client,
		server:           srv,
		serverHandlerMux: asynq.NewServeMux(),
		logger:           logger,
	}, nil
}

func (m *Manager) RegisterJob(taskName string, handler asynq.HandlerFunc) {
	m.serverHandlerMux.HandleFunc(taskName, handler)
}

func (m *Manager) RegisterCronJob(cronspec, taskName string, handler asynq.HandlerFunc, opts ...asynq.Option) {
	m.serverHandlerMux.HandleFunc(taskName, handler)

	taskID := asynq.TaskID(taskName)
	task := asynq.NewTask(taskName, nil, opts...)

	if _, err := m.scheduler.Register(cronspec, task, taskID); err != nil {
		m.logger.Fatal("Failed to Register Cron Job", zap.String("taskName", taskName), zap.String("cronspec", cronspec))
	}
}

// Run starts the scheduler and server service
func (m *Manager) Run() error {
	if err := m.scheduler.Start(); err != nil {
		return err
	}

	return m.server.Run(m.serverHandlerMux)
}

func (m *Manager) Close() error {
	m.server.Shutdown()
	m.scheduler.Shutdown()
	return m.client.Close()
}

func parseLogLevel(logger zapcore.Level) asynq.LogLevel {
	switch logger {
	case zapcore.DebugLevel:
		return asynq.InfoLevel
	case zapcore.ErrorLevel:
		return asynq.ErrorLevel
	case zapcore.InfoLevel:
		return asynq.InfoLevel
	case zapcore.WarnLevel:
		return asynq.WarnLevel
	default:
		return asynq.ErrorLevel
	}
}
