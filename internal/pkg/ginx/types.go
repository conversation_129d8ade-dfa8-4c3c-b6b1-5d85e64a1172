package ginx

// Response is the API response without pagination
// @Description Response is the basic response structure for our API.
type Response[T any] struct {
	// The Status field denotes the HTTP status code of the response.
	Status int `json:"status" example:"200" extensions:"x-order=0"`
	// The Data field carries the payload of the API response.
	Data T `json:"data" extensions:"x-order=1"`
} //@name Response

// PageResponse is the API response with pagination.
// @Description PageResponse is the pagination structure response for our API.
type PageResponse[T any] struct {
	// The Status field denotes the HTTP status code of the response.
	Status int `json:"status" example:"200" extensions:"x-order=0"`
	// Total indicates the total number of items available, if applicable.
	Total int32 `json:"total" example:"100" extensions:"x-order=1"`
	// Offset is the starting index of items in the data response.
	// It represents the position from which items are retrieved in a paginated result set.
	Offset int `json:"offset" example:"3" extensions:"x-order=2"`
	// Limit is the size of items in the data response.
	Limit int `json:"limit" example:"10" extensions:"x-order=3"`
	// The Data field carries the payload of the API response.
	Data T `json:"data" extensions:"x-order=4"`
} //@name PageResponse

type PaginationResult struct {
	Total  int32 `json:"total"`
	Offset int   `json:"offset"`
	Limit  int   `json:"limit"`
}

// TODO: improve the Pagination flow by integrating help functions
//  in the PaginationParam struct, cursor, offset based.
//  right now we're duplicating logics in many parts of our repositories
//  this is very silly and we should consider moving
//  everything related to pagination to somewhere in our project
//  orm ref: https://github.com/formancehq/stack/tree/main/libs/go-libs/bun/bunpaginate
//  impl ref: https://github.com/LyricTian/gin-admin/blob/main/pkg/util/db.go

// PaginationParam is the basic query params for a List endpoint
// @Description PaginationParam is the basic query params for a List endpoint
type PaginationParam struct {
	// Sets the order direction for sorting results (ASC or DESC).
	OrderDirection string `form:"orderDirection" validate:"optional,omitempty,oneof=DESC ASC desc asc" example:"DESC" enums:"DESC,ASC" default:"DESC" extensions:"x-order=32"`
	// Specifies the maximum number of items to return per page.
	Limit int `form:"limit" validate:"optional,omitempty" example:"10" default:"10" extensions:"x-order=33"`
	// Specifies the starting position of the items to be retrieved.
	Offset int `form:"offset" validate:"optional,omitempty" example:"0" default:"0" extensions:"x-order=34"`
} //@name PaginationParam
