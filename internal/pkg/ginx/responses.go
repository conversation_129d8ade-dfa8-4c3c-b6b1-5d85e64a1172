package ginx

import (
	"fmt"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"unicode"

	"github.com/go-playground/validator/v10"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"

	"github.com/gin-gonic/gin"
	"github.com/segmentio/encoding/json"
	"go.uber.org/zap"
)

// GetToken retrieves the access token from the Authorization header or query parameter.
func GetToken(c *gin.Context) string {
	var token string

	// Attempt to get token from Authorization header
	auth := c.GetHeader("Authorization")
	if auth != "" {
		parts := strings.SplitN(auth, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			token = parts[1]
		}
	}

	// If the token is not found in the header, try getting it from the query parameters
	if token == "" {
		token = c.Query("token")
	}

	return token
}

// ParseJSON parses body json data to struct
func ParseJSON(c *gin.Context, obj interface{}) error {
	return c.ShouldBind<PERSON>(obj)
}

// ParseQuery parses query parameter to struct
func ParseQuery(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindQuery(obj); err != nil {
		return err
	}
	return nil
}

// ParseUri parses path parameter to struct
func ParseUri(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindUri(obj); err != nil {
		return err
	}
	return nil
}

// ResJSON response with json data and status code
func ResJSON(c *gin.Context, status int, v interface{}) {
	buf, err := json.Marshal(v)
	if err != nil {
		panic(err)
	}

	c.Data(status, "application/json; charset=utf-8", buf)
	c.Abort()
}

func ResSuccess[T any](c *gin.Context, v T) {
	ResJSON(c, http.StatusOK, Response[T]{
		Status: http.StatusOK,
		Data:   v,
	})
}

func ResCreated[T any](c *gin.Context, v T) {
	ResJSON(c, http.StatusCreated, Response[T]{
		Status: http.StatusOK,
		Data:   v,
	})
}

func ResOK(c *gin.Context) {
	c.AbortWithStatus(http.StatusOK)
}

func ResNoContent(c *gin.Context) {
	c.AbortWithStatus(http.StatusNoContent)
}

func ResNotImplemented(c *gin.Context) {
	c.AbortWithStatus(http.StatusNotImplemented)
}

func ResPage[T any](c *gin.Context, v T, pr *PaginationResult) {
	var total int32
	var offset, limit int
	if pr != nil {
		total = pr.Total
		offset = pr.Offset
		limit = pr.Limit
	}

	reflectValue := reflect.Indirect(reflect.ValueOf(v))
	if reflectValue.IsNil() {
		sliceType := reflect.SliceOf(reflect.TypeOf(v))
		v = reflect.MakeSlice(sliceType, 0, 0).Interface().(T)
	}

	ResJSON(c, http.StatusOK, PageResponse[T]{
		Status: http.StatusOK,
		Total:  total,
		Offset: offset,
		Limit:  limit,
		Data:   v,
	})
}

func ResError(c *gin.Context, err error, status ...int) {
	var (
		ierr       = &errors.Error{}
		verr       = validator.ValidationErrors{}
		strconvErr = &strconv.NumError{}
		jsonUnErr  = &json.InvalidUnmarshalError{}
		jsonMasErr = &json.MarshalerError{}
	)

	switch {
	// Check if the error is well known (API Error)
	case errors.As(err, &ierr):
		ierr = errors.FromError(err)
	// We also use validator.ValidationErrors for request validation
	case errors.As(err, &verr):
		ierr.Status = http.StatusBadRequest
		ierr.Code = codes.Parameters
		ierr.Message = "invalid parameters"
		ierr.Details = parseValidateErrors(verr)
	// There some validations that use native library so the types are different
	case errors.As(err, &strconvErr):
		ierr.Status = http.StatusBadRequest
		ierr.Code = codes.Parameters
		ierr.Message = strconvErr.Unwrap().Error()
	// HandleJSONProcessingError handles errors related to processing JSON data.
	// It detects common mistakes, such as missing pointer usage in JSON operations.
	case errors.As(err, &jsonUnErr), errors.As(err, &jsonMasErr):
		ierr.Status = http.StatusInternalServerError
		ierr.Code = errors.DefaultInternalServerErrorID
		ierr.Message = "failed to process JSON data on the server side"
	// If we don't know about the error, we try to parse to well know a format
	default:
		ierr.Status = http.StatusInternalServerError
		ierr.Code = errors.DefaultInternalServerErrorID
		ierr.Message = err.Error()
	}

	code := ierr.Status
	if len(status) > 0 {
		code = status[0]
	}

	if code >= 500 {
		zap.L().Error("Internal Server Error", zap.Error(err), zap.Any("context", c.Request.Context()))
	}

	ResJSON(c, code, ierr)
}

func parseValidateErrors(errors validator.ValidationErrors) map[string]interface{} {
	errorMap := make(map[string]interface{})
	for _, err := range errors {
		field := toCamelCase(err.Field())
		if formatMessage, ok := mappedErrors[err.Tag()]; ok {
			errorMap[field] = formatMessage(err.Value(), err.Param())
		} else {
			errorMap[field] = err.Error()
		}
	}
	return errorMap
}

var mappedErrors = map[string]func(value, param any) string{
	"required": func(value, param any) string {
		return "is required"
	},
	"uuid": func(value, param any) string {
		return "is an invalid uuid"
	},
	"lt": func(value, param any) string {
		return fmt.Sprintf("'%v' must be less than '%v'", value, param)
	},
	"lte": func(value, param any) string {
		return fmt.Sprintf("'%v' must be less than or equal to '%v'", value, param)
	},
	"gt": func(value, param any) string {
		return fmt.Sprintf("'%v' must be greater than '%v'", value, param)
	},
	"gte": func(value, param any) string {
		return fmt.Sprintf("'%v' must be greater than or equal to '%v'", value, param)
	},
	"oneof": func(value, param any) string {
		return fmt.Sprintf("'%v' must be one of '%v'", value, param)
	},
	"min": func(value, param any) string {
		return fmt.Sprintf("'%v' must be at least '%v'", value, param)
	},
	"max": func(value, param any) string {
		return fmt.Sprintf("'%v' must be at most '%v'", value, param)
	},
	"email": func(value, param any) string {
		return "invalid email format"
	},
}

func toCamelCase(input string) string {
	result := strings.ToLower(string(input[0]))
	for i := 1; i < len(input); i++ {
		if unicode.IsUpper(rune(input[i])) {
			if i > 1 && unicode.IsUpper(rune(input[i-1])) {
				result += strings.ToLower(string(input[i]))
			} else {
				result += string(input[i])
			}
		} else {
			result += strings.ToLower(string(input[i]))
		}
	}
	return result
}
