package cache

import (
	"time"

	"github.com/dgraph-io/ristretto"
	"github.com/pkg/errors"
)

// ICache defines the interface for our cache.
type ICache interface {
	Get(key any) (any, bool)
	Set(key, value any, cost int64) bool
	SetWithTTL(key, value any, cost int64, ttl time.Duration) bool
	Del(key any)
	Close()
	Wait()
}

// Config holds the configuration for the Ristretto cache.
type Config struct {
	// NumCounters is the number of keys to track frequency of.
	NumCounters int64 `mapstructure:"num_counters"`
	// MaxCost is the maximum cost of the cache.
	MaxCost int64 `mapstructure:"max_cost"`
	// BufferItems is the number of keys per Get buffer.
	BufferItems int64 `mapstructure:"buffer_items"`
}

type cache struct {
	*ristretto.Cache
}

// New creates a new Ristretto cache instance.
func New(config Config) (ICache, error) {
	if config.NumCounters == 0 {
		config.NumCounters = 1e7 // 10M
	}
	if config.MaxCost == 0 {
		config.MaxCost = 1 << 30 // 1GB
	}
	if config.BufferItems == 0 {
		config.BufferItems = 64
	}

	r, err := ristretto.NewCache(&ristretto.Config{
		NumCounters: config.NumCounters,
		MaxCost:     config.MaxCost,
		BufferItems: config.BufferItems,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to create Ristretto cache")
	}

	return &cache{r}, nil
}
