package validate

import (
	"sync"

	"github.com/go-playground/validator/v10"
)

type defaultValidator struct {
	once     sync.Once
	validate *validator.Validate
}

var Validator = &defaultValidator{}

// ValidateStruct receives struct type
func (v *defaultValidator) ValidateStruct(obj any) error {
	v.lazyinit()
	return v.validate.Struct(obj)
}

// ValidateVar receives var type
func (v *defaultValidator) ValidateVar(obj any, tag string) error {
	v.lazyinit()
	return v.validate.Var(obj, tag)
}

func (v *defaultValidator) lazyinit() {
	v.once.Do(func() {
		v.validate = validator.New(validator.WithRequiredStructEnabled())
		// here we create a custom dummy validation because of the swagger optional parameter
		_ = v.validate.RegisterValidation("optional", func(fl validator.FieldLevel) bool { return true })
	})
}
