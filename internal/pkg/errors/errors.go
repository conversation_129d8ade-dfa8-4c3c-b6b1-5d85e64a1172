package errors

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
)

// Define alias
var (
	// WithStack annotates err with a stack trace at the point WithStack was called.
	// If err is nil, WithStack returns nil.
	WithStack = errors.WithStack

	// Wrap returns an error annotating err with a stack trace
	// at the point Wrap is called, and the supplied message.
	// If err is nil, Wrap returns nil.
	Wrap = errors.Wrap

	// Wrapf returns an error annotating err with a stack trace
	// at the point Wrapf is called, and the format specifier.
	// If err is nil, Wrapf returns nil.
	Wrapf = errors.Wrapf

	// Is reports whether any error in err's chain matches target.
	//
	// The chain consists of err itself followed by the sequence of errors obtained by
	// repeatedly calling Unwrap.
	//
	// An error is considered to match a target if it is equal to that target or if
	// it implements a method Is(error) bool such that Is(target) returns true.
	Is = errors.Is

	// Errorf formats according to a format specifier and returns the string
	// as a value that satisfies error.
	// Errorf also records the stack trace at the point it was called.
	Errorf = errors.Errorf

	// As finds the first error in err's chain that matches target, and if so, sets
	// target to that error value and returns true.
	//
	// The chain consists of err itself followed by the sequence of errors obtained by
	// repeatedly calling Unwrap.
	//
	// An error matches target if the error's concrete value is assignable to the value
	// pointed to by target, or if the error has a method ErrorAs(interface{}) bool such that
	// ErrorAs(target) returns true. In the latter case, the ErrorAs method is responsible for
	// setting target.
	//
	// ErrorAs will panic if target is not a non-nil pointer to either a type that implements
	// error, or to any interface type. ErrorAs returns false if err is nil.
	As = errors.As

	// Cause returns the underlying cause of the error, if possible.
	// An error value has a cause if it implements the following
	// interface:
	//
	//     type causer interface {
	//            Cause() error
	//     }
	//
	// If the error does not implement Cause, the original error will
	// be returned. If the error is nil, nil will be returned without further
	// investigation.
	Cause = errors.Cause
)

// Common Errors
var (
	ForbiddenResourceError = Forbidden("", "you are not authorized to access this resource")
	UnexpectedError        = InternalServerError("", "unexpected error")
)

// Default Codes
const (
	DefaultBadRequestID          = "Bad Request"
	DefaultUnauthorizedID        = "Unauthorized"
	DefaultForbiddenID           = "Forbidden"
	DefaultNotFoundID            = "Not Found"
	DefaultMethodNotAllowedID    = "Method Not Allowed"
	DefaultTooManyRequestsID     = "Too Many Requests"
	DefaultInternalServerErrorID = "Internal Server Error"
	DefaultRequestTimeoutID      = "Request Timeout"
)

// Error is the error structure used by or app
// @Description Error is the basic structure used by our API
type Error struct {
	// Status is the HTTP status code
	Status int `json:"status" example:"403"  extensions:"x-order=0"`
	// Code is a section to identifier the module of the error
	Code string `json:"code" example:"Forbidden"  extensions:"x-order=1"`
	// Message describing the error response if exists
	Message string `json:"message"  example:"you are not authorized to access this resource" extensions:"x-order=2"`
	// Details field for additional error details
	Details map[string]any `json:"details,omitempty" swaggertype:"object,string" validate:"optional" example:"auth:missing Authorization header token" extensions:"x-order=3,x-omitempty"`
} //@name Error

// New generates a custom error.
func New(status int, code, message string) error {
	return &Error{
		Code:    code,
		Status:  status,
		Message: message,
	}
}

// NewWithDetails generates a custom error with details.
func NewWithDetails(status int, code, message string, details map[string]interface{}) error {
	return &Error{
		Code:    code,
		Status:  status,
		Message: message,
		Details: details,
	}
}

func (e *Error) Error() string {
	b, _ := json.Marshal(e)
	return string(b)
}

// FromError try to convert go error to *Error
func FromError(err error) *Error {
	if err == nil {
		return nil
	}

	var verr *Error
	if errors.As(err, &verr) && verr != nil {
		return verr
	}

	return Parse(err.Error())
}

// Parse tries to parse a JSON string into an error. If that
// fails, it will set the given string as the error detail.
func Parse(err string) *Error {
	e := new(Error)
	errr := json.Unmarshal([]byte(err), e)
	if errr != nil {
		e.Message = err
	}
	return e
}

// BadRequestWithDetails generates a 400 error with details.
func BadRequestWithDetails(code, format string, details map[string]interface{}, a ...interface{}) error {
	if code == "" {
		code = DefaultBadRequestID
	}
	return &Error{
		Code:    code,
		Status:  http.StatusBadRequest,
		Message: fmt.Sprintf(format, a...),
		Details: details,
	}
}

// BadRequest generates a 400 error.
func BadRequest(code, format string, a ...interface{}) error {
	if code == "" {
		code = DefaultBadRequestID
	}
	return &Error{
		Code:    code,
		Status:  http.StatusBadRequest,
		Message: fmt.Sprintf(format, a...),
	}
}

// Unauthorized generates a 401 error.
func Unauthorized(code, format string, a ...interface{}) error {
	if code == "" {
		code = DefaultUnauthorizedID
	}
	return &Error{
		Code:    code,
		Status:  http.StatusUnauthorized,
		Message: fmt.Sprintf(format, a...),
	}
}

// Forbidden generates a 403 error.
func Forbidden(code, format string, a ...interface{}) error {
	if code == "" {
		code = DefaultForbiddenID
	}
	return &Error{
		Code:    code,
		Status:  http.StatusForbidden,
		Message: fmt.Sprintf(format, a...),
	}
}

// NotFound generates a 404 error.
func NotFound(code, format string, a ...interface{}) error {
	if code == "" {
		code = DefaultNotFoundID
	}
	return &Error{
		Code:    code,
		Status:  http.StatusNotFound,
		Message: fmt.Sprintf(format, a...),
	}
}

// MethodNotAllowed generates a 405 error.
func MethodNotAllowed(code, format string, a ...interface{}) error {
	if code == "" {
		code = DefaultMethodNotAllowedID
	}
	return &Error{
		Code:    code,
		Status:  http.StatusMethodNotAllowed,
		Message: fmt.Sprintf(format, a...),
	}
}

// TooManyRequests generates a 429 error.
func TooManyRequests(code, format string, a ...interface{}) error {
	if code == "" {
		code = DefaultTooManyRequestsID
	}
	return &Error{
		Code:    code,
		Status:  http.StatusTooManyRequests,
		Message: fmt.Sprintf(format, a...),
	}
}

// Timeout generates a 408 error.
func Timeout(code, format string, a ...interface{}) error {
	if code == "" {
		code = DefaultRequestTimeoutID
	}
	return &Error{
		Code:    code,
		Status:  http.StatusRequestTimeout,
		Message: fmt.Sprintf(format, a...),
	}
}

// InternalServerError generates a 500 error.
func InternalServerError(code, format string, a ...interface{}) error {
	if code == "" {
		code = DefaultInternalServerErrorID
	}
	return &Error{
		Code:    code,
		Status:  http.StatusInternalServerError,
		Message: fmt.Sprintf(format, a...),
	}
}

// Equal tries to compare errors
func Equal(err1 error, err2 error) bool {
	var verr1 *Error
	ok1 := errors.As(err1, &verr1)

	var verr2 *Error
	ok2 := errors.As(err2, &verr2)

	if ok1 != ok2 {
		return false
	}

	if !ok1 {
		return errors.Is(err1, err2)
	}

	if verr1.Code != verr2.Code {
		return false
	}

	return true
}
