package operatorservice

import (
	"context"
	"fmt"
	"time"

	"github.com/herenow/atomic-bm/atomic-br/botregionalclient"
	"github.com/herenow/atomic-bm/internal/db/repo/botrepo"
	"github.com/herenow/atomic-bm/internal/services/paramsservice"
	"github.com/herenow/atomic-protocols/gen/atomic/api/enums/v1"
	v1 "github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1"
	"github.com/sony/gobreaker"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/structpb"
)

// desiredBotState represents the state of a bot as configured in the database.
// This is the "source of truth".
type desiredBotState struct {
	// Bot contains the full bot model from the database, including its relations.
	Bot *botrepo.Bot
}

// actualBotState represents the real-time state of a bot running in a regional service.
type actualBotState struct {
	// Bot contains the bot's state as reported by the botregional gRPC service.
	Bot *v1.Bot
	// RegionID is the ID of the region where the bot is running.
	RegionID string
}

// executor is responsible for executing actions on the bot regional clients.
// It wraps gRPC calls with circuit breakers for resilience.
type executor struct {
	logger          *zap.Logger
	botRegionalCli  botregionalclient.IBotRegionalClient
	paramsSvc       paramsservice.IParamsService
	circuitBreakers map[string]*gobreaker.CircuitBreaker
}

// newExecutor creates a new executor instance.
func newExecutor(logger *zap.Logger, cli botregionalclient.IBotRegionalClient) *executor {
	return &executor{
		logger:          logger,
		botRegionalCli:  cli,
		paramsSvc:       nil,
		circuitBreakers: make(map[string]*gobreaker.CircuitBreaker),
	}
}

func (e *executor) setParamsService(svc paramsservice.IParamsService) {
	e.paramsSvc = svc
}

// getCircuitBreaker returns a circuit breaker for a given region, creating one if it doesn't exist.
func (e *executor) getCircuitBreaker(regionID string) *gobreaker.CircuitBreaker {
	if cb, ok := e.circuitBreakers[regionID]; ok {
		return cb
	}
	st := gobreaker.Settings{
		Name:        fmt.Sprintf("region-%s", regionID),
		MaxRequests: 3,
		Interval:    1 * time.Minute,
		Timeout:     10 * time.Second,
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			e.logger.Warn("circuit breaker state changed",
				zap.String("name", name),
				zap.String("from", from.String()),
				zap.String("to", to.String()),
			)
		},
	}

	e.circuitBreakers[regionID] = gobreaker.NewCircuitBreaker(st)
	return e.circuitBreakers[regionID]
}

// CreateBot executes the action to create a new bot in a regional service.
func (e *executor) CreateBot(ctx context.Context, state *desiredBotState) error {
	regionID := state.Bot.Account.RegionID
	cb := e.getCircuitBreaker(regionID)

	_, err := cb.Execute(func() (interface{}, error) {
		if e.paramsSvc == nil {
			return nil, fmt.Errorf("ParamsService not configured in Executor")
		}
		// On creation, params can be nil. The reconciler will handle param updates later.
		return nil, e.botRegionalCli.NewBot(ctx, regionID, &v1.Bot{
			Id:             state.Bot.ID,
			ExchangeId:     state.Bot.Account.ExchangeID,
			AccountId:      state.Bot.AccountID,
			Symbol:         state.Bot.Symbol,
			Type:           enums.BotType_BOT_TYPE_MARKET_MAKER,
			GatewayOptions: nil,
			Params:         nil,
		})
	})

	return err
}

// StartBot executes the action to start a bot.
func (e *executor) StartBot(ctx context.Context, state *actualBotState) error {
	regionID := state.RegionID
	cb := e.getCircuitBreaker(regionID)

	_, err := cb.Execute(func() (interface{}, error) {
		// Before starting, ensure parameters are up-to-date.
		if err := e.UpdateParams(ctx, state); err != nil {
			return nil, fmt.Errorf("failed to update params before starting bot: %w", err)
		}
		return nil, e.botRegionalCli.StartBot(ctx, regionID, state.Bot.Id)
	})

	return err
}

// StopBot executes the action to stop a bot.
func (e *executor) StopBot(ctx context.Context, state *actualBotState) error {
	regionID := state.RegionID
	cb := e.getCircuitBreaker(regionID)

	_, err := cb.Execute(func() (interface{}, error) {
		return nil, e.botRegionalCli.StopBot(ctx, regionID, state.Bot.Id)
	})

	return err
}

// UpdateParams retrieves the latest active parameters and pushes them to the regional bot.
func (e *executor) UpdateParams(ctx context.Context, state *actualBotState) error {
	regionID := state.RegionID
	botID := state.Bot.Id
	cb := e.getCircuitBreaker(regionID)

	_, err := cb.Execute(func() (interface{}, error) {
		if e.paramsSvc == nil {
			return nil, fmt.Errorf("ParamsService not configured in Executor")
		}

		activeParams, err := e.paramsSvc.GetActiveParams(ctx, paramsservice.GetActiveParamsRequest{BotID: botID})
		if err != nil {
			return nil, fmt.Errorf("failed to get active params for bot %s: %w", botID, err)
		}

		// Separate params into strategy params and gateway options
		strategyParamsMap := make(map[string]string)
		gatewayParamsMap := make(map[string]string)

		for _, p := range activeParams.Params {
			if p.Scope == paramsservice.GatewayScope.String() {
				gatewayParamsMap[p.Key] = p.Value
			} else {
				strategyParamsMap[p.Key] = p.Value
			}
		}

		// Update strategy params
		if len(strategyParamsMap) > 0 {
			var paramsStruct *structpb.Struct
			paramsStruct, err = botregionalclient.FromGoStruct(strategyParamsMap)
			if err != nil {
				return nil, fmt.Errorf("failed to convert strategy params to struct for bot %s: %w", botID, err)
			}
			if err = e.botRegionalCli.UpdateBotParams(ctx, regionID, []botregionalclient.BotParams{{BotID: botID, Params: paramsStruct}}); err != nil {
				return nil, fmt.Errorf("failed to update strategy params for bot %s: %w", botID, err)
			}
		}

		// Update gateway options
		if len(gatewayParamsMap) > 0 {
			var gtwOptsStruct *structpb.Struct
			gtwOptsStruct, err = botregionalclient.FromGoStruct(gatewayParamsMap)
			if err != nil {
				return nil, fmt.Errorf("failed to convert gateway options to struct for bot %s: %w", botID, err)
			}
			if err = e.botRegionalCli.UpdateGtwOpts(ctx, regionID, []botregionalclient.GtwOpts{{BotID: botID, GtwOpts: gtwOptsStruct}}); err != nil {
				return nil, fmt.Errorf("failed to update gateway options for bot %s: %w", botID, err)
			}
		}

		return nil, nil
	})

	return err
}
