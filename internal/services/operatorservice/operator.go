package operatorservice

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/herenow/atomic-bm/atomic-br/botregionalclient"
	"github.com/herenow/atomic-bm/internal/db/repo/botrepo"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/services/botsservice/types"
	"github.com/herenow/atomic-bm/internal/services/paramsservice"
	"github.com/herenow/atomic-bm/internal/ws"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
)

var _spanName = "OperatorService/%s"

const _ScopeName = "OperatorService"

type IOperator interface {
	Start(ctx context.Context)
	Stop()
	TriggerOperator(ctx context.Context, botID string) error
}

type Service struct {
	logger *zap.Logger

	botsRepo botrepo.IBots

	botRegionalCli botregionalclient.IBotRegionalClient
	regions        []string

	wsClient *ws.WebsocketClient

	tracer trace.Tracer

	executor  *executor
	publisher *publisher

	stopChan         chan struct{}
	reconcileTicker  *time.Ticker
	reconcileNowChan chan string
	wg               sync.WaitGroup
}

type Config struct {
	Logger           *zap.Logger
	BotsRepo         botrepo.IBots
	Regions          []string
	BotRegionalCli   botregionalclient.IBotRegionalClient
	WsClient         *ws.WebsocketClient
	OperatorInterval time.Duration
}

func New(cfg Config) *Service {
	if cfg.OperatorInterval == 0 {
		cfg.OperatorInterval = 30 * time.Second
	}
	s := &Service{
		logger:           cfg.Logger,
		botsRepo:         cfg.BotsRepo,
		botRegionalCli:   cfg.BotRegionalCli,
		wsClient:         cfg.WsClient,
		tracer:           otel.Tracer(_ScopeName),
		stopChan:         make(chan struct{}),
		reconcileTicker:  time.NewTicker(cfg.OperatorInterval),
		reconcileNowChan: make(chan string, 100),
		regions:          cfg.Regions,
	}
	s.executor = newExecutor(s.logger, s.botRegionalCli)
	s.publisher = newPublisher(s.logger, s.wsClient)
	return s
}

func (s *Service) SetParamsService(paramsSvc paramsservice.IParamsService) {
	s.executor.setParamsService(paramsSvc)
}

func (s *Service) Start(ctx context.Context) {
	s.logger.Info("starting reconciler service")
	s.wg.Add(1)
	s.operatorLoop(ctx)
}

func (s *Service) Stop() {
	s.logger.Info("stopping reconciler service")
	close(s.stopChan)
	s.reconcileTicker.Stop()
	s.wg.Wait()
	s.logger.Info("reconciler service stopped")
}

func (s *Service) TriggerOperator(ctx context.Context, botID string) error {
	select {
	case s.reconcileNowChan <- botID:
		s.logger.Info("operator triggered for bot", zap.String("botID", botID))
		return nil
	case <-ctx.Done():
		return ctx.Err()
	default:
		return fmt.Errorf("operator channel is full, please try again later")
	}
}

func (s *Service) operatorLoop(ctx context.Context) {
	defer s.wg.Done()
	for {
		select {
		case <-s.stopChan:
			return
		case <-s.reconcileTicker.C:
			s.runFullSyncOperation(ctx)
		case botID := <-s.reconcileNowChan:
			s.runSingleBotOperation(ctx, botID)
		}
	}
}

func (s *Service) runFullSyncOperation(ctx context.Context) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "runFullSyncOperation"))
	defer span.End()
	s.logger.Debug("starting full operator cycle")

	desiredState, err := s.fetchDesiredState(ctx, "")
	if err != nil {
		s.logger.Error("failed to fetch desired state for all bots", zap.Error(err))
		return
	}

	actualState, err := s.fetchActualState(ctx)
	if err != nil {
		s.logger.Error("failed to fetch actual state for all bots", zap.Error(err))
		return
	}

	s.reconcile(ctx, desiredState, actualState)
	s.logger.Debug("full operator cycle finished")
}

func (s *Service) runSingleBotOperation(ctx context.Context, botID string) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "runSingleBotOperation"))
	defer span.End()
	s.logger.Debug("starting single bot operator", zap.String("botID", botID))

	desiredState, err := s.fetchDesiredState(ctx, botID)
	if err != nil {
		s.logger.Error("failed to fetch desired state for bot", zap.String("botID", botID), zap.Error(err))
		return
	}

	actualState, err := s.fetchActualState(ctx)
	if err != nil {
		s.logger.Error("failed to fetch actual state for bot", zap.String("botID", botID), zap.Error(err))
		return
	}

	s.reconcile(ctx, desiredState, actualState)
}

func (s *Service) fetchDesiredState(ctx context.Context, botID string) (map[string]*desiredBotState, error) {
	var bots []*botrepo.Bot
	if botID != "" {
		bot, err := s.botsRepo.Get(ctx, botID, true)
		if err != nil {
			return nil, err
		}
		bots = append(bots, bot)
	} else {
		listResp, err := s.botsRepo.List(ctx, botrepo.ListBotsRequest{
			PaginationParam: ginx.PaginationParam{Limit: 1000},
			WithAccount:     true,
		})
		if err != nil {
			return nil, err
		}
		bots = listResp.Bots
	}

	state := make(map[string]*desiredBotState)
	for _, bot := range bots {
		state[bot.ID] = &desiredBotState{Bot: bot}
	}
	return state, nil
}

func (s *Service) fetchActualState(ctx context.Context) (map[string]*actualBotState, error) {
	state := make(map[string]*actualBotState)
	var mu sync.Mutex
	var wg sync.WaitGroup

	for _, regionID := range s.regions {
		wg.Add(1)
		go func(rID string) {
			defer wg.Done()
			regionalBots, err := s.botRegionalCli.ListBots(ctx, rID)
			if err != nil {
				s.logger.Error("failed to list bots for region", zap.String("regionID", rID), zap.Error(err))
				return
			}
			mu.Lock()
			for _, bot := range regionalBots {
				state[bot.Id] = &actualBotState{
					Bot:      bot,
					RegionID: rID,
				}
			}
			mu.Unlock()
		}(regionID)
	}
	wg.Wait()

	return state, nil
}

// reconcile logic is updated to correctly handle transient states.
func (s *Service) reconcile(ctx context.Context, desired map[string]*desiredBotState, actual map[string]*actualBotState) {
	for botID, dState := range desired {
		aState, exists := actual[botID]
		if !exists {
			s.logger.Info("reconcile: bot needs to be created", zap.String("botID", botID))
			if err := s.executor.CreateBot(ctx, dState); err != nil {
				s.logger.Error("failed to execute create bot action", zap.String("botID", botID), zap.Error(err))
			}
			continue
		}

		// Convert the actual gRPC status to our internal service status type
		actualStatus := types.FromProtoStatus(aState.Bot.Status)

		// Sync DB status if it's out of sync with reality
		statusChanged := !strings.EqualFold(dState.Bot.Status, actualStatus.String())
		lastErrorChanged := dState.Bot.LastError != aState.Bot.LastError

		if statusChanged || lastErrorChanged {
			dState.Bot.Status = actualStatus.String()
			dState.Bot.LastError = aState.Bot.LastError

			if err := s.botsRepo.Update(ctx, dState.Bot); err != nil {
				s.logger.Error("failed to update bot status in DB", zap.String("botID", botID), zap.Error(err))
			}
			s.publisher.PublishState(ctx, dState.Bot)
		}

		desiredStatus := types.ToStatus(dState.Bot.DesiredStatus)

		switch desiredStatus {
		case types.Running:
			// Take action only if the bot is fully stopped or has errored.
			// If it's Starting or Stopping, we wait for the transition to complete.
			if actualStatus == types.Stopped || actualStatus == types.Error {
				s.logger.Info("reconcile: bot needs to be started", zap.String("botID", botID))
				if err := s.executor.StartBot(ctx, aState); err != nil {
					s.logger.Error("failed to execute start bot action", zap.String("botID", botID), zap.Error(err))
				}
			}
		case types.Stopped:
			// Take action if the bot is not already stopped or stopping
			if actualStatus != types.Stopped && actualStatus != types.Stopping {
				s.logger.Info("reconcile: bot needs to be stopped", zap.String("botID", botID))
				if err := s.executor.StopBot(ctx, aState); err != nil {
					s.logger.Error("failed to execute stop bot action", zap.String("botID", botID), zap.Error(err))
				}
			}
		}
	}
}
