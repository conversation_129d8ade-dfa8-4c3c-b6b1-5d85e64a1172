package operatorservice

import (
	"context"
	"encoding/json"
	"time"

	"github.com/centrifugal/centrifuge"
	"github.com/herenow/atomic-bm/internal/db/repo/botrepo"
	"github.com/herenow/atomic-bm/internal/ws"
	"go.uber.org/zap"
)

// _WebsocketChannel defines the single, global channel for all bot state updates.
const _WebsocketChannel = "bots/state"

// publisher is responsible for publishing bot state changes and managing subscriptions for its channel.
type publisher struct {
	logger   *zap.Logger
	wsClient *ws.WebsocketClient
}

// newPublisher creates a new publisher instance and registers its WebSocket channel handlers.
func newPublisher(logger *zap.Logger, wsClient *ws.WebsocketClient) *publisher {
	p := &publisher{
		logger:   logger,
		wsClient: wsClient,
	}

	if wsClient != nil {
		wsClient.RegisterSubscribeHook(_WebsocketChannel, p.onSubscribe)
		wsClient.RegisterUnsubscribeHook(_WebsocketChannel, p.onUnsubscribe)
	}

	return p
}

// onSubscribe is the callback executed when a client subscribes to the bots:state channel.
func (p *publisher) onSubscribe(ctx context.Context, client *centrifuge.Client, event centrifuge.SubscribeEvent, cb centrifuge.SubscribeCallback) {
	p.logger.Debug("client subscribed to bots state channel", zap.String("userID", client.UserID()))
	// Any authenticated user can subscribe. The auth middleware handles the authentication part.
	cb(centrifuge.SubscribeReply{
		Options: centrifuge.SubscribeOptions{},
	}, nil)
}

// onUnsubscribe is the callback executed when a client unsubscribes.
func (p *publisher) onUnsubscribe(ctx context.Context, client *centrifuge.Client, event centrifuge.UnsubscribeEvent) {
	p.logger.Debug("client unsubscribed from bots state channel", zap.String("userID", client.UserID()))
}

// StatePayload is the structure for messages sent over WebSocket.
type StatePayload struct {
	BotID         string    `json:"bot_id"`
	Status        string    `json:"status"`
	DesiredStatus string    `json:"desired_status"`
	LastError     string    `json:"last_error,omitempty"`
	Timestamp     time.Time `json:"timestamp"`
}

// PublishState sends a bot's state change to the global WebSocket channel.
func (p *publisher) PublishState(ctx context.Context, bot *botrepo.Bot) {
	if p.wsClient == nil {
		p.logger.Warn("websocket client is not configured; skipping publish")
		return
	}

	payload := StatePayload{
		BotID:         bot.ID,
		Status:        bot.Status,
		DesiredStatus: bot.DesiredStatus,
		LastError:     bot.LastError,
		Timestamp:     time.Now().UTC(),
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		p.logger.Error("failed to marshal websocket state payload", zap.String("botID", bot.ID), zap.Error(err))
		return
	}

	// Publish to the static, global channel.
	if err = p.wsClient.Publish(ctx, _WebsocketChannel, payloadBytes); err != nil {
		p.logger.Error("failed to publish state to websocket",
			zap.String("channel", _WebsocketChannel),
			zap.Error(err),
		)
	} else {
		p.logger.Debug("published state update to websocket", zap.String("channel", _WebsocketChannel))
	}
}
