package notificationsmanagerservice

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/db/repo/botrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/notificationsrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/pushnotificationrepo"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/pkg/validate"
	"github.com/herenow/atomic-bm/internal/tasks"
	"github.com/herenow/atomic-bm/pkg/util"
)

const (
	_spanName  = "NotificationManagerService/%s"
	_ScopeName = "NotificationsManagerService"
)

var availableEventTypes = map[string]struct{}{
	tasks.TypeBotsOffline: {},
}

func validateEventType(eventType string) bool {
	_, exists := availableEventTypes[eventType]
	return exists
}

var EmptyRequestOrMissingParameter = errors.BadRequest(codes.Notifications, "request is empty or missing parameters")

type INotificationsService interface {
	// SubscribeEvents subscribes a user one or more events
	SubscribeEvents(ctx context.Context, events []SubscribeEventsRequest) ([]notificationsrepo.NotificationSubscription, error)

	// UnsubscribeEvents delete one or more user notifications subscriptions
	UnsubscribeEvents(ctx context.Context, req UnsubscribeEventRequest) error

	// ListSubscribeEvents list of notification subscriptions with filters
	ListSubscribeEvents(ctx context.Context, req ListNotificationSubscriptionsRequest) (*notificationsrepo.ListNotificationSubscriptionsResponse, error)

	// CreateOrUpdatePushNotification add or update a user push notification device
	CreateOrUpdatePushNotification(ctx context.Context, req *CreatePushNotificationRequest) (*CreatePushNotificationResponse, error)

	// DeletePushNotificationByDeviceTokenID deletes a user push notification device token by its device token id
	DeletePushNotificationByDeviceTokenID(ctx context.Context, deviceToken string) error

	// ListPushNotifications list push notifications with filters
	ListPushNotifications(ctx context.Context, filter ListPushNotificationsRequest) (*pushnotificationrepo.ListPushNotificationsResponse, error)
}

// Service represents the notification manager functionality.
type Service struct {
	notifySubRepo  notificationsrepo.INotificationSubscriptionsRepo
	pushNotifyRepo pushnotificationrepo.INotificationsRepo
	botRepo        botrepo.IBots
	tracer         trace.Tracer
}

// New creates a new instance of the Service.
func New(
	ntfBotRepo notificationsrepo.INotificationSubscriptionsRepo,
	pushNtfRepo pushnotificationrepo.INotificationsRepo,
	botRepo botrepo.IBots,
) *Service {
	return &Service{
		notifySubRepo:  ntfBotRepo,
		pushNotifyRepo: pushNtfRepo,
		botRepo:        botRepo,
		tracer:         otel.Tracer(_ScopeName),
	}
}

type SubscribeEventsRequest struct {
	UserID     string `validate:"required"`
	EventType  string `validate:"required"`
	ResourceID string `validate:"required,uuid"`
}

// SubscribeEvents subscribes a user one or more events
func (s *Service) SubscribeEvents(ctx context.Context, events []SubscribeEventsRequest) ([]notificationsrepo.NotificationSubscription, error) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "SubscribeEvents"))
	defer span.End()

	if len(events) == 0 {
		return nil, EmptyRequestOrMissingParameter
	}

	// Single insert
	if len(events) == 1 {
		event := events[0]
		if err := validate.Validator.ValidateStruct(event); err != nil {
			return nil, err
		}

		if err := s.validateEventType(ctx, event); err != nil {
			return nil, err
		}

		newSub := notificationsrepo.NotificationSubscription{
			EventType:  event.EventType,
			UserID:     event.UserID,
			ResourceID: event.ResourceID,
		}

		if err := s.notifySubRepo.Create(ctx, &newSub); err != nil {
			return nil, err
		}

		return []notificationsrepo.NotificationSubscription{newSub}, nil
	}

	// Many inserts
	var newSubs []notificationsrepo.NotificationSubscription
	for _, ev := range events {
		if err := validate.Validator.ValidateStruct(ev); err != nil {
			return nil, err
		}

		if err := s.validateEventType(ctx, ev); err != nil {
			return nil, err
		}

		newSubs = append(newSubs, notificationsrepo.NotificationSubscription{
			EventType:  ev.EventType,
			ResourceID: ev.ResourceID,
			UserID:     ev.UserID,
		})
	}

	if err := s.notifySubRepo.BulkCreate(ctx, &newSubs); err != nil {
		return nil, err
	}

	return newSubs, nil
}

func (s *Service) validateEventType(ctx context.Context, event SubscribeEventsRequest) error {
	if !validateEventType(event.EventType) {
		return errors.BadRequest(codes.Parameters, "invalid event type")
	}

	switch event.EventType {
	case tasks.TypeBotsOffline:
		bot, err := s.botRepo.Get(ctx, event.ResourceID, false)
		if err != nil {
			return err
		}
		if bot.DeletedAt != nil {
			return errors.BadRequest(codes.Parameters, "cannot create notification subscription for a deleted bot")
		}
		// Here can add more check because the resource id is dynamic and does not have a relation
	}

	return nil
}

type UnsubscribeEventRequest struct {
	UserID           string `validate:"required"`
	SubscriptionsIDs []int  `validate:"required,gt=0"`
}

// UnsubscribeEvents delete one or more user notifications subscriptions
func (s *Service) UnsubscribeEvents(ctx context.Context, req UnsubscribeEventRequest) error {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "UnsubscribeEvents"))
	defer span.End()

	if req.UserID == "" {
		return errors.BadRequest(codes.Parameters, "user id is required")
	}

	if len(req.SubscriptionsIDs) == 0 {
		return errors.BadRequest(codes.Parameters, "subscription ids is required")
	}

	if err := s.notifySubRepo.BulkDelete(ctx, req.UserID, req.SubscriptionsIDs); err != nil {
		if errors.Is(err, util.ErrNoRowsAffected) {
			return errors.NotFound(codes.Parameters, "subscriptions not founded")
		}
	}

	return nil
}

// ListNotificationSubscriptionsRequest represents the request parameters for listing notification subscriptions with filters.
type ListNotificationSubscriptionsRequest struct {
	ginx.PaginationParam
	OrderBy string

	EventType  string
	UserID     string
	ResourceID string
}

// isValidOrderBy checks if the provided field is a valid option for ordering.
func isValidOrderBy(orderBy string) bool {
	if orderBy == "" {
		return true
	}
	validFields := map[string]bool{
		"eventType":  true,
		"created_at": true,
	}
	return validFields[orderBy]
}

// ListSubscribeEvents list of notification subscriptions with filters
func (s *Service) ListSubscribeEvents(ctx context.Context, req ListNotificationSubscriptionsRequest) (*notificationsrepo.ListNotificationSubscriptionsResponse, error) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "ListSubscribeEvents"))
	defer span.End()

	if !isValidOrderBy(req.OrderBy) {
		return nil, errors.BadRequest(codes.Parameters, "'%s' is a invalid field for orderBy sorter", req.OrderBy)
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	if req.OrderDirection == "" {
		req.OrderDirection = "DESC"
	}
	req.OrderDirection = strings.ToUpper(req.OrderDirection)

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	subList, err := s.notifySubRepo.List(ctx, notificationsrepo.ListNotificationSubscriptionsRequest{
		OrderBy:        req.OrderBy,
		OrderDirection: req.OrderDirection,
		Offset:         req.Offset,
		Limit:          req.Limit,
		EventType:      req.EventType,
		UserID:         req.UserID,
		ResourceID:     req.ResourceID,
	})
	if err != nil {
		return nil, err
	}

	return subList, nil
}

// CreatePushNotificationRequest for creating or updating a push notification.
type CreatePushNotificationRequest struct {
	UserID      string `validate:"required,uuid"`
	DeviceToken string `validate:"required"`
	Platform    string `validate:"required,oneof=web ios android"`
}

type CreatePushNotificationResponse struct {
	ID          string
	UserID      string
	DeviceToken string
	Platform    string
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

// CreateOrUpdatePushNotification add or update a user push notification device
func (s *Service) CreateOrUpdatePushNotification(ctx context.Context, req *CreatePushNotificationRequest) (*CreatePushNotificationResponse, error) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "CreateOrUpdatePushNotification"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	npn := &pushnotificationrepo.PushNotification{
		UserID:      req.UserID,
		DeviceToken: req.DeviceToken,
		Platform:    req.Platform,
	}

	if err := s.pushNotifyRepo.CreateOrUpdate(ctx, npn); err != nil {
		return nil, err
	}

	return &CreatePushNotificationResponse{
		ID:          npn.ID,
		UserID:      npn.UserID,
		DeviceToken: npn.DeviceToken,
		Platform:    npn.Platform,
		CreatedAt:   npn.CreatedAt,
		UpdatedAt:   npn.UpdatedAt,
	}, nil
}

// DeletePushNotificationByDeviceTokenID deletes a user push notification device token by its device token id
func (s *Service) DeletePushNotificationByDeviceTokenID(ctx context.Context, deviceToken string) error {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "DeletePushNotificationByDeviceTokenID"))
	defer span.End()

	if err := s.pushNotifyRepo.DeleteByDeviceToken(ctx, deviceToken); err != nil {
		return err
	}

	return nil
}

// ListPushNotificationsRequest represents the request parameters for listing notifications with filters.
type ListPushNotificationsRequest struct {
	ginx.PaginationParam
	OrderBy string

	UserID   string
	Platform string
}

// ListPushNotifications list push notifications with filters
func (s *Service) ListPushNotifications(ctx context.Context, req ListPushNotificationsRequest) (*pushnotificationrepo.ListPushNotificationsResponse, error) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "ListPushNotifications"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	pushNtfs, err := s.pushNotifyRepo.List(ctx, pushnotificationrepo.ListPushNotificationsRequest{
		UserID:         req.UserID,
		Platform:       req.Platform,
		OrderBy:        req.OrderBy,
		OrderDirection: req.OrderDirection,
		Offset:         req.Offset,
		Limit:          req.Limit,
	})
	if err != nil {
		return nil, err
	}

	return pushNtfs, nil
}
