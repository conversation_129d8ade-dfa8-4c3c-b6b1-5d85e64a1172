package accountservice

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	accRepo "github.com/herenow/atomic-bm/internal/db/repo/accountrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/exchangerepo"
	"github.com/herenow/atomic-bm/internal/db/repo/regionsrepo"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/pkg/validate"
)

var _spanName = "AccountService/%s"

const _ScopeName = "AccountService"

type IAccountService interface {
	Create(ctx context.Context, req *CreateAccountRequest) (*CreateAccountResponse, error)
	Get(ctx context.Context, req *GetAccountRequest) (*GetAccountResponse, error)
	List(ctx context.Context, req *ListAccountsRequest) (*ListAccountsResponse, error)
	Delete(ctx context.Context, req *DeleteAccountRequest) error
}

type Service struct {
	accRepo    accRepo.IAccountsRepo
	excRepo    exchangerepo.IExchange
	regionRepo regionsrepo.IRegionRepo

	trace trace.Tracer
}

func New(accRepo accRepo.IAccountsRepo, excRepo exchangerepo.IExchange, regionRepo regionsrepo.IRegionRepo) *Service {
	return &Service{
		accRepo:    accRepo,
		excRepo:    excRepo,
		regionRepo: regionRepo,
		trace:      otel.Tracer(_ScopeName),
	}
}

type CreateAccountRequest struct {
	ExchangeID string `validate:"required"`
	RegionID   string `validate:"required"`
	Tag        string `validate:"required"`
}

type CreateAccountResponse struct {
	ID         string
	ExchangeID string
	RegionID   string
	Tag        string
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

func (s *Service) Create(ctx context.Context, req *CreateAccountRequest) (*CreateAccountResponse, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "Create"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	if _, err := s.excRepo.Get(ctx, req.ExchangeID); err != nil {
		return nil, err
	}

	if _, err := s.regionRepo.Get(ctx, req.RegionID); err != nil {
		return nil, err
	}

	newAcc := accRepo.Account{
		ExchangeID: req.ExchangeID,
		RegionID:   req.RegionID,
		Tag:        req.Tag,
	}

	if err := s.accRepo.Create(ctx, &newAcc); err != nil {
		return nil, err
	}

	return &CreateAccountResponse{
		ID:         newAcc.ID,
		ExchangeID: newAcc.ExchangeID,
		RegionID:   newAcc.RegionID,
		Tag:        newAcc.Tag,
		CreatedAt:  newAcc.CreatedAt,
		UpdatedAt:  newAcc.UpdatedAt,
	}, nil
}

type GetAccountRequest struct {
	AccountID string `validate:"required,uuid"`
}

type GetAccountResponse struct {
	ID         string
	ExchangeID string
	RegionID   string
	Tag        string
	CreatedAt  time.Time
	UpdatedAt  time.Time
	DeletedAt  *time.Time
}

// Get retrieving an account by ID.
func (s *Service) Get(ctx context.Context, req *GetAccountRequest) (*GetAccountResponse, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "Get"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	acc, err := s.accRepo.Get(ctx, req.AccountID)
	if err != nil {
		return nil, err
	}

	return &GetAccountResponse{
		ID:         acc.ID,
		ExchangeID: acc.ExchangeID,
		RegionID:   acc.RegionID,
		Tag:        acc.Tag,
		CreatedAt:  acc.CreatedAt,
		UpdatedAt:  acc.UpdatedAt,
		DeletedAt:  acc.DeletedAt,
	}, nil
}

// ListAccountsRequest represents the request parameters for listing accounts with filters.
type ListAccountsRequest struct {
	ginx.PaginationParam
	IsDeleted bool
	OrderBy   string

	ExchangeID string
	RegionID   string
	Tag        string
}

// isValidOrderBy checks if the provided field is a valid option for ordering.
func isValidOrderBy(orderBy string) bool {
	if orderBy == "" {
		return true
	}
	validFields := map[string]bool{
		"exchange_id": true,
		"region_id":   true,
		"tag":         true,
		"created_at":  true,
		"updated_at":  true,
		"deleted_at":  true,
	}

	return validFields[orderBy]
}

func (c *ListAccountsRequest) ToRepositoryRequest() accRepo.ListAccountsRequest {
	return accRepo.ListAccountsRequest{
		ExchangeID: c.ExchangeID,
		RegionID:   c.RegionID,
		Tag:        c.Tag,
		PaginationParam: ginx.PaginationParam{
			OrderDirection: c.OrderDirection,
			Offset:         c.Offset,
			Limit:          c.Limit,
		},
	}
}

// ListAccountsResponse represents the response body for listing accounts.
type ListAccountsResponse struct {
	Total    int32
	Accounts []*accRepo.Account
}

// List listing accounts based on the provided filters.
func (s *Service) List(ctx context.Context, req *ListAccountsRequest) (*ListAccountsResponse, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "List"))
	defer span.End()

	if req.Limit == 0 {
		req.Limit = 10
	}

	if req.OrderDirection == "" {
		req.OrderDirection = "DESC"
	}
	req.OrderDirection = strings.ToUpper(req.OrderDirection)

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	if !isValidOrderBy(req.OrderBy) {
		return nil, errors.BadRequest(codes.Parameters, "'%s' is a invalid field for orderBy sorter", req.OrderBy)
	}

	accs, err := s.accRepo.List(ctx, req.ToRepositoryRequest())
	if err != nil {
		return nil, err
	}

	return &ListAccountsResponse{Accounts: accs.Accounts, Total: accs.Total}, nil
}

type DeleteAccountRequest struct {
	AccountID string `validate:"required,uuid"`
}

// Delete deleting an account by ID.
func (s *Service) Delete(ctx context.Context, req *DeleteAccountRequest) error {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "Delete"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return err
	}

	if err := s.accRepo.Delete(ctx, req.AccountID); err != nil {
		return err
	}

	return nil
}
