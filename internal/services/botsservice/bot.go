package botsservice

import (
	"context"
	"fmt"
	"time"

	"github.com/herenow/atomic-bm/atomic-br/botregionalclient"
	"github.com/herenow/atomic-bm/internal/pkg/cache"
	"github.com/herenow/atomic-bm/internal/services/botsservice/types"
	"github.com/herenow/atomic-bm/internal/services/operatorservice"
	"github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	accRepo "github.com/herenow/atomic-bm/internal/db/repo/accountrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/botrepo"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/pkg/validate"
)

var _spanName = "BotService/%s"

const _ScopeName = "BotService"

var (
	ErrBotDesiredStateIsSame = errors.BadRequest(codes.Bots, "bot desired state is already set to the requested state")
)

type IBotsService interface {
	Create(ctx context.Context, req CreateBotRequest) (*Bot, error)
	Get(ctx context.Context, botID string) (*Bot, error)
	// Deprecated: updating a bot is current not viable due the simplicity of our current business
	Update(ctx context.Context, req UpdateBotRequest) (*Bot, error)
	// Deprecated: deleting a bot is current not viable due the simplicity of our current business
	Delete(ctx context.Context, botID string) error
	List(ctx context.Context, req ListBotsRequest) (*ListBotsResponse, error)
	StartBot(ctx context.Context, botID string) error
	StopBot(ctx context.Context, botID string) error
	GetBotState(ctx context.Context, botID string) (*proto.State, error)
	ListBotsState(ctx context.Context) (interface{}, bool)
	Start(ctx context.Context)
}

type Service struct {
	accRepo  accRepo.IAccountsRepo
	botsRepo botrepo.IBots
	operator operatorservice.IOperator
	tracer   trace.Tracer
	cache    cache.ICache
	logger   *zap.Logger
	brcli    botregionalclient.IBotRegionalClient
	regions  []string
}

func New(
	accRepo accRepo.IAccountsRepo,
	botsRepo botrepo.IBots,
	operator operatorservice.IOperator,
	cache cache.ICache,
	logger *zap.Logger,
	brcli botregionalclient.IBotRegionalClient,
	regions []string,
) *Service {
	return &Service{
		accRepo:  accRepo,
		botsRepo: botsRepo,
		operator: operator,
		tracer:   otel.Tracer(_ScopeName),
		cache:    cache,
		logger:   logger,
		brcli:    brcli,
		regions:  regions,
	}
}

// Bot is the public Data Transfer Object (DTO) for this service.
type Bot struct {
	ID            string
	AccountID     string
	Symbol        string
	RegionID      string
	Tag           string
	DesiredStatus string
	Status        types.Status
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
}

func toServiceBot(repoBot *botrepo.Bot) *Bot {
	if repoBot == nil {
		return nil
	}
	var regionID string
	if repoBot.Account.ID != "" {
		regionID = repoBot.Account.RegionID
	}
	return &Bot{
		ID:            repoBot.ID,
		AccountID:     repoBot.AccountID,
		Symbol:        repoBot.Symbol,
		RegionID:      regionID,
		Tag:           repoBot.Tag,
		DesiredStatus: repoBot.DesiredStatus,
		Status:        types.ToStatus(repoBot.Status),
		CreatedAt:     repoBot.CreatedAt,
		UpdatedAt:     repoBot.UpdatedAt,
		DeletedAt:     repoBot.DeletedAt,
	}
}

type CreateBotRequest struct {
	AccountID string `validate:"required,uuid"`
	Symbol    string `validate:"required"`
	Tag       string `validate:"required"`
}

func (s *Service) Create(ctx context.Context, req CreateBotRequest) (*Bot, error) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "Create"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	if _, err := s.accRepo.Get(ctx, req.AccountID); err != nil {
		return nil, err
	}

	newBot := &botrepo.Bot{
		AccountID:     req.AccountID,
		Symbol:        req.Symbol,
		Tag:           req.Tag,
		Status:        types.Pending.String(),
		DesiredStatus: types.Stopped.String(),
	}

	if err := s.botsRepo.Create(ctx, newBot); err != nil {
		return nil, err
	}

	if err := s.operator.TriggerOperator(ctx, newBot.ID); err != nil {
		s.tracer.Start(ctx, "failed-trigger-reconciliation-on-create")
	}

	return toServiceBot(newBot), nil
}

func (s *Service) Get(ctx context.Context, botID string) (*Bot, error) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "Get"))
	defer span.End()

	if err := validate.Validator.ValidateVar(botID, "uuid"); err != nil {
		return nil, err
	}

	repoBot, err := s.botsRepo.Get(ctx, botID, true)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get bot with id %s", botID)
	}

	return toServiceBot(repoBot), nil
}

type UpdateBotRequest struct {
	BotID string `validate:"required,uuid"`
	Tag   string `validate:"required"`
}

func (s *Service) Update(ctx context.Context, req UpdateBotRequest) (*Bot, error) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "Update"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	botToUpdate, err := s.botsRepo.Get(ctx, req.BotID, false)
	if err != nil {
		return nil, err
	}

	botToUpdate.Tag = req.Tag

	if err = s.botsRepo.Update(ctx, botToUpdate); err != nil {
		return nil, errors.Wrapf(err, "failed to update bot tag in database")
	}

	return toServiceBot(botToUpdate), nil
}

func (s *Service) Delete(ctx context.Context, botID string) error {
	return errors.Errorf("not implemented")
}

type ListBotsRequest struct {
	ginx.PaginationParam
	OrderBy string

	IsDeleted     bool
	AccountID     string
	Region        string
	Symbol        string
	DesiredStatus string
	Status        types.Status
}

func (r *ListBotsRequest) isValidOrderBy() bool {
	if r.OrderBy == "" {
		return true
	}
	validFields := map[string]bool{
		"account_id":     true,
		"symbol":         true,
		"status":         true,
		"desired_status": true,
		"created_at":     true,
	}
	return validFields[r.OrderBy]
}

type ListBotsResponse struct {
	Bots  []*Bot
	Total int32
}

func (s *Service) List(ctx context.Context, req ListBotsRequest) (*ListBotsResponse, error) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "List"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	if !req.isValidOrderBy() {
		return nil, errors.BadRequest(codes.Parameters, "'%s' is an invalid field for orderBy sorter", req.OrderBy)
	}

	repoBots, err := s.botsRepo.List(ctx, botrepo.ListBotsRequest{
		PaginationParam: req.PaginationParam,
		AccountID:       req.AccountID,
		Symbol:          req.Symbol,
		Status:          req.Status.String(),
		DesiredStatus:   req.DesiredStatus,
		OrderBy:         req.OrderBy,
		IsDeleted:       req.IsDeleted,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to list bots from database")
	}

	serviceBots := make([]*Bot, len(repoBots.Bots))
	for i, bot := range repoBots.Bots {
		serviceBots[i] = toServiceBot(bot)
	}

	return &ListBotsResponse{
		Bots:  serviceBots,
		Total: int32(repoBots.Total),
	}, nil
}

func (s *Service) StartBot(ctx context.Context, botID string) error {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "StartBot"))
	defer span.End()

	return s.setDesiredState(ctx, botID, types.Running, types.Starting)
}

func (s *Service) StopBot(ctx context.Context, botID string) error {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "StopBot"))
	defer span.End()

	return s.setDesiredState(ctx, botID, types.Stopped, types.Stopping)
}

// setDesiredState now accepts a transient state to provide immediate user feedback.
func (s *Service) setDesiredState(ctx context.Context, botID string, finalState, transientState types.Status) error {
	if err := validate.Validator.ValidateVar(botID, "required,uuid"); err != nil {
		return err
	}

	bot, err := s.botsRepo.Get(ctx, botID, false)
	if err != nil {
		return err
	}

	if bot.DesiredStatus == finalState.String() {
		return ErrBotDesiredStateIsSame
	}

	// Set both the final desired state and the immediate transient state.
	bot.DesiredStatus = finalState.String()
	bot.Status = transientState.String()
	if err = s.botsRepo.Update(ctx, bot); err != nil {
		return errors.Wrapf(err, "failed to update bot desired state to '%s' in database", finalState.String())
	}

	if err = s.operator.TriggerOperator(ctx, bot.ID); err != nil {
		s.tracer.Start(ctx, "failed-trigger-reconciliation-on-set-desired-state")
	}

	return nil
}
