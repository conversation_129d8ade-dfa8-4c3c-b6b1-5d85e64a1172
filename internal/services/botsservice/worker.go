package botsservice

import (
	"context"
	"time"

	"github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1"
	"go.uber.org/zap"
)

const (
	// stateWorkerInterval defines how often the worker fetches the bots state.
	// This should be tuned based on performance and freshness requirements.
	stateWorkerInterval = 15 * time.Second
)

// Start begins the background workers for the bots service.
// Currently, it starts the worker responsible for fetching the bots state.
func (s *Service) Start(ctx context.Context) {
	go s.startStateWorker(ctx)
}

// startStateWorker initializes and runs a ticker to periodically
// fetch the bots state from all botregional services.
func (s *Service) startStateWorker(ctx context.Context) {
	s.logger.Info("Starting bots state worker")
	ticker := time.NewTicker(stateWorkerInterval)
	defer ticker.Stop()

	// Perform an initial fetch immediately on startup.
	s.fetchAndCacheBotsState(ctx)

	for {
		select {
		case <-ticker.C:
			s.fetchAndCacheBotsState(ctx)
		case <-ctx.Done():
			s.logger.Info("Stopping bot state worker", zap.Error(ctx.Err()))
			return
		}
	}
}

// fetchAndCacheBotsState is the core logic of the worker. It calls the
// botregional client to get all bots and stores the result in a cache.
func (s *Service) fetchAndCacheBotsState(ctx context.Context) {
	s.logger.Debug("Worker is fetching bots state")

	var states []*proto.State
	for _, region := range s.regions {
		state, err := s.brcli.ListBotsState(ctx, region)
		if err != nil {
			s.logger.Error("Worker failed to fetch bots state", zap.Error(err))
			return
		}

		states = append(states, state...)
	}

	s.cache.Set(botsStatesCacheKey, states, 1)
}
