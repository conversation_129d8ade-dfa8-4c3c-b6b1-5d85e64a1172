package types

import (
	v1 "github.com/herenow/atomic-protocols/gen/atomic/api/enums/v1"
)

// Status is a type local to the botsservice package.
type Status int

const (
	Starting Status = iota
	Running
	Stopping
	Stopped
	Paused
	Error
	Pending // Bot created, configuration needs validation.
	Invalid // Bot has a configuration error (e.g., missing required params).
	Unknown
)

func (s Status) String() string {
	return [...]string{"starting", "running", "stopping", "stopped", "paused", "error", "pending", "invalid", "unknown"}[s]
}

// ToStatus converts a string to a Status enum.
func ToStatus(s string) Status {
	switch s {
	case "running":
		return Running
	case "starting":
		return Starting
	case "stopping":
		return Stopping
	case "stopped":
		return Stopped
	case "paused":
		return Paused
	case "error":
		return Error
	case "pending":
		return Pending
	case "invalid":
		return Invalid
	default:
		return Unknown
	}
}

// FromProtoStatus converts a protobuf BotStatus to a service Status.
func FromProtoStatus(s v1.BotStatus) Status {
	switch s {
	case v1.BotStatus_BOT_STATUS_RUNNING:
		return Running
	case v1.BotStatus_BOT_STATUS_STARTING:
		return Starting
	case v1.BotStatus_BOT_STATUS_STOPPING:
		return Stopping
	case v1.BotStatus_BOT_STATUS_STOPPED:
		return Stopped
	//case v1.BotStatus_BOT_STATUS_PAUSED:
	//	return Paused
	case v1.BotStatus_BOT_STATUS_ERROR:
		return Error
	default:
		return Unknown
	}
}
