package botsservice

import (
	"context"
	"fmt"
	"time"

	"github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1"
	"go.uber.org/zap"
)

const (
	// botStateCachePrefix is the prefix for caching individual bot states.
	botStateCachePrefix = "botstate:"
	// botRegionCachePrefix is the prefix for caching the region of a bot.
	botRegionCachePrefix = "botregion:"
	// botsStatesCacheKey is the cache key for the aggregated bots state.
	botsStatesCacheKey = "bots:state"
	// botStateCacheTTL is the short time-to-live for a bot's state in the cache.
	botStateCacheTTL = 200 * time.Millisecond
)

// GetBotState retrieves the state of a specific bot from a botregional service.
// It uses an in-memory cache to store the state for 200ms to reduce load on botregional services.
func (s *Service) GetBotState(ctx context.Context, botID string) (*proto.State, error) {
	stateCacheKey := botStateCachePrefix + botID

	// Attempt to retrieve the bot state from the cache.
	if cached, found := s.cache.Get(stateCacheKey); found {
		if state, ok := cached.(*proto.State); ok {
			s.logger.Debug("cache hit for bot state", zap.String("botID", botID))
			return state, nil
		}
	}
	s.logger.Debug("cache miss for bot state", zap.String("botID", botID))

	regionID, err := s.getRegionForBot(ctx, botID)
	if err != nil {
		return nil, err
	}

	state, err := s.brcli.GetBotState(ctx, regionID, botID)
	if err != nil {
		return nil, fmt.Errorf("could not get bot state from regional: %w", err)
	}

	s.cache.SetWithTTL(stateCacheKey, state, 1, botStateCacheTTL)

	return state, nil
}

// getRegionForBot retrieves the region for a bot, using a cache to avoid DB lookups.
func (s *Service) getRegionForBot(ctx context.Context, botID string) (string, error) {
	regionCacheKey := botRegionCachePrefix + botID
	if cached, found := s.cache.Get(regionCacheKey); found {
		if regionID, ok := cached.(string); ok {
			s.logger.Debug("cache hit for bot region", zap.String("botID", botID))
			return regionID, nil
		}
	}
	s.logger.Debug("cache miss for bot region", zap.String("botID", botID))

	bot, err := s.Get(ctx, botID)
	if err != nil {
		return "", fmt.Errorf("could not get bot to determine region: %w", err)
	}

	if bot.RegionID == "" {
		return "", fmt.Errorf("bot %s has no region ID assigned", botID)
	}

	s.cache.Set(regionCacheKey, bot.RegionID, 1)
	return bot.RegionID, nil
}

// ListBotsState retrieves the cached state of all bots from all botregional services.
// The data is populated by a background worker. It returns the data and a boolean indicating if the data was found.
func (s *Service) ListBotsState(ctx context.Context) (interface{}, bool) {
	states, found := s.cache.Get(botsStatesCacheKey)
	return states, found
}
