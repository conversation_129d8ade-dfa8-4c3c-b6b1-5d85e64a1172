package trollboxservice

import (
	"context"
	"fmt"

	"github.com/centrifugal/centrifuge"
	"github.com/google/uuid"
	"github.com/segmentio/encoding/json"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"github.com/herenow/atomic-bm/internal/db/repo/trollboxrepo"
	"github.com/herenow/atomic-bm/internal/ws"
	"github.com/herenow/atomic-bm/pkg/util"
)

// Service LogMessage a struct for your chat channel handlers.
type Service struct {
	tbRepo   trollboxrepo.ITrollBoxRepository
	wsClient *ws.WebsocketClient
	tracer   trace.Tracer
	logger   *zap.Logger
}

var _spanName = "TrollBox/%s"

const (
	ChannelName = "chat/trollbox"
	_ScopeName  = "TrollboxService"
)

func New(wsClient *ws.WebsocketClient, tbRepo trollboxrepo.ITrollBoxRepository, logger *zap.Logger) {
	tbHandler := &Service{
		tbRepo:   tbRepo,
		wsClient: wsClient,
		tracer:   otel.Tracer(_ScopeName),
		logger:   logger,
	}

	wsClient.RegisterSubscribeHook(ChannelName, tbHandler.OnSubscribe)
	wsClient.RegisterPublishHook(ChannelName, tbHandler.OnPublish)
	wsClient.RegisterPresenceHook(ChannelName, tbHandler.OnPresence)
	wsClient.RegisterUnsubscribeHook(ChannelName, tbHandler.OnUnsubscribe)
}

func (c *Service) OnSubscribe(ctx context.Context, client *centrifuge.Client, event centrifuge.SubscribeEvent, cb centrifuge.SubscribeCallback) {
	cb(centrifuge.SubscribeReply{
		Options: centrifuge.SubscribeOptions{
			EmitPresence:  true,
			EmitJoinLeave: true,
			PushJoinLeave: true,
		},
	}, nil)
}

func (c *Service) OnUnsubscribe(ctx context.Context, client *centrifuge.Client, event centrifuge.UnsubscribeEvent) {

}

type Message struct {
	ID        string `json:"id"`
	Seq       int64  `json:"seq"`
	UserID    string `json:"userId"`
	Content   string `json:"content"`
	Timestamp string `json:"timestamp"`
}

func (c *Service) OnPublish(ctx context.Context, client *centrifuge.Client, e centrifuge.PublishEvent, cb centrifuge.PublishCallback) {
	ctx, span := c.tracer.Start(ctx, fmt.Sprintf(_spanName, "OnPublish"))
	defer span.End()

	var msg Message
	if err := json.Unmarshal(e.Data, &msg); err != nil {
		c.logger.Debug("failed to unmarshal message", zap.Error(err), zap.Any("message", string(e.Data)))
		cb(centrifuge.PublishReply{}, centrifuge.ErrorBadRequest)
	}

	if msg.Content == "" {
		cb(centrifuge.PublishReply{}, nil)
		return
	}

	msg.ID = uuid.New().String()
	msg.UserID = client.UserID()
	timestamp, err := util.ParseAndValidateTimestamp(msg.Timestamp)
	if err != nil {
		cb(centrifuge.PublishReply{}, centrifuge.ErrorBadRequest)
	}

	tb := &trollboxrepo.TrollBox{
		ID:        msg.ID,
		UserID:    msg.UserID,
		Content:   msg.Content,
		Timestamp: timestamp,
	}

	if err = c.tbRepo.LogMessage(ctx, tb); err != nil {
		c.logger.Error("failed to save message on database", zap.Error(err))
		cb(centrifuge.PublishReply{}, centrifuge.ErrorInternal)
	}
	msg.Seq = tb.Sequence

	data, err := json.Marshal(msg)
	if err != nil {
		c.logger.Debug("failed to marshal message", zap.Error(err))
		cb(centrifuge.PublishReply{}, centrifuge.ErrorInternal)
	}

	res, err := c.wsClient.PublishWithResult(ctx, e.Channel, data)
	if err != nil {
		c.logger.Error("failed to publish message", zap.Error(err))
		cb(centrifuge.PublishReply{}, centrifuge.ErrorInternal)
	}

	cb(centrifuge.PublishReply{Result: res}, nil)
}

func (c *Service) OnPresence(ctx context.Context, client *centrifuge.Client, event centrifuge.PresenceEvent, cb centrifuge.PresenceCallback) {
	cb(centrifuge.PresenceReply{}, nil)
}
