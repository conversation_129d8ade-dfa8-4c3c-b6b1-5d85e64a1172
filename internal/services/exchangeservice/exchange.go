package exchangeservice

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/db/repo/exchangerepo"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/pkg/validate"
)

var _spanName = "ExchangeService/%s"

const _ScopeName = "ExchangeService"

type IExchangeService interface {
	Get(ctx context.Context, exchangeID string) (*Exchange, error)
	List(ctx context.Context, req ListExchangeRequest) (*ListExchangeResponse, error)
}

type Service struct {
	exchangeRepo exchangerepo.IExchange
	tracer       trace.Tracer
}

func New(exchangeRepo exchangerepo.IExchange) *Service {
	return &Service{
		exchangeRepo: exchangeRepo,
		tracer:       otel.Tracer(_ScopeName),
	}
}

type Exchange struct {
	ExchangeID string
	Name       string
	CreatedAt  time.Time
}

type ListExchangeRequest struct {
	ginx.PaginationParam
	OrderBy string

	ExchangeID string
	Name       string
}

type ListExchangeResponse struct {
	Total     int32
	Exchanges []Exchange
}

func (s *Service) List(ctx context.Context, req ListExchangeRequest) (*ListExchangeResponse, error) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "List"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	list, err := s.exchangeRepo.List(ctx, exchangerepo.ListExchangesRequest{
		PaginationParam: req.PaginationParam,
		OrderBy:         req.OrderBy,
		ExchangeID:      req.ExchangeID,
		Name:            req.Name,
	})
	if err != nil {
		return nil, err
	}

	var res = make([]Exchange, len(list.Exchanges))
	for i, ex := range list.Exchanges {
		res[i] = Exchange{
			ExchangeID: ex.ID,
			Name:       ex.Name,
			CreatedAt:  ex.CreatedAt,
		}
	}

	return &ListExchangeResponse{Exchanges: res, Total: int32(list.Total)}, nil
}

func (s *Service) Get(ctx context.Context, exchangeID string) (*Exchange, error) {
	ctx, span := s.tracer.Start(ctx, fmt.Sprintf(_spanName, "Get"))
	defer span.End()

	if exchangeID == "" {
		return nil, errors.BadRequest(codes.Exchanges, "missing exchangeId parameter")
	}

	ex, err := s.exchangeRepo.Get(ctx, exchangeID)
	if err != nil {
		return nil, err
	}

	return &Exchange{
		ExchangeID: ex.ID,
		Name:       ex.Name,
		CreatedAt:  ex.CreatedAt,
	}, nil
}
