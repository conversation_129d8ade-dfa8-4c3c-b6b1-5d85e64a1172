package paramsservice

import (
	"context"
	"fmt"
	"time"

	"github.com/herenow/atomic-bm/internal/db/repo/paramrepo"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/pkg/validate"
)

type ParamGroup struct {
	ID              string
	Name            string
	Priority        int32
	CreatedByUserID string
	CreatedAt       time.Time
}

type BotParamGroup struct {
	BotID           string
	GroupID         string
	CreatedByUserID string
	CreatedAt       time.Time
}

type CreateParamGroupRequest struct {
	Name     string `validate:"required"`
	Priority int32
	UserID   string `validate:"required,uuid"`
}

func (s *Service) CreateParamGroup(ctx context.Context, req CreateParamGroupRequest) (*ParamGroup, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "CreateParamGroup"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	if req.Priority == 0 {
		req.Priority = 1
	}

	paramGroup, err := s.paramsRepo.CreateParamGroup(ctx, paramrepo.CreateGroupRequest{
		Name:            req.Name,
		Priority:        req.Priority,
		CreatedByUserID: req.UserID,
	})
	if err != nil {
		return nil, err
	}

	return &ParamGroup{
		ID:              paramGroup.ID,
		Name:            paramGroup.Name,
		Priority:        paramGroup.Priority,
		CreatedByUserID: paramGroup.CreatedByUserID,
		CreatedAt:       paramGroup.CreatedAt,
	}, nil
}

type ListParamGroupsRequest struct {
	ginx.PaginationParam
	OrderBy string

	Name            string
	CreatedByUserID string `validate:"omitempty,uuid"`
}

func (l *ListParamGroupsRequest) isValidOrderBy() bool {
	if l.OrderBy == "" {
		return true
	}
	validFields := map[string]bool{
		"name":               true,
		"created_by_user_id": true,
		"created_at":         true,
	}
	return validFields[l.OrderBy]
}

type ListParamGroupsResponse struct {
	Total       int32
	ParamGroups []ParamGroup
}

func (s *Service) ListParamGroups(ctx context.Context, req ListParamGroupsRequest) (*ListParamGroupsResponse, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "ListParamGroups"))
	defer span.End()

	if !req.isValidOrderBy() {
		return nil, errors.BadRequest(codes.Parameters, "'%s' is a invalid field for orderBy sorter", req.OrderBy)
	}

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	paramGroups, err := s.paramsRepo.ListParamGroups(ctx, paramrepo.ListParamGroupsRequest{
		PaginationParam: req.PaginationParam,
		OrderBy:         req.OrderBy,
		Name:            req.Name,
		CreatedByUserID: req.CreatedByUserID,
	})
	if err != nil {
		return nil, err
	}

	var res = make([]ParamGroup, len(paramGroups.ParamGroups))
	for i, group := range paramGroups.ParamGroups {
		res[i] = ParamGroup{
			ID:              group.ID,
			Name:            group.Name,
			Priority:        group.Priority,
			CreatedByUserID: group.CreatedByUserID,
			CreatedAt:       group.CreatedAt,
		}
	}

	return &ListParamGroupsResponse{ParamGroups: res, Total: int32(paramGroups.Total)}, nil
}

func (s *Service) GetParamGroup(ctx context.Context, paramGroupId string) (*ParamGroup, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "GetParamGroup"))
	defer span.End()

	return nil, nil
}

type DeleteParamGroupRequest struct {
	UserID  string `validate:"required,uuid"`
	GroupID string `validate:"required,uuid"`
}

func (s *Service) DeleteParamGroup(ctx context.Context, req DeleteParamGroupRequest) error {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "DeleteParamGroup"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return err
	}

	return s.paramsRepo.DeleteParamGroup(ctx, paramrepo.DeleteParamGroupRequest{
		GroupID: req.GroupID,
	})
}

type CreateBotParamGroupRelationRequest struct {
	UserID  string `validate:"required,uuid"`
	BotID   string `validate:"required,uuid"`
	GroupID string `validate:"required,uuid"`
}

func (s *Service) CreateBotParamGroupRelation(ctx context.Context, req CreateBotParamGroupRelationRequest) (*BotParamGroup, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "CreateBotParamGroupRelation"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	newBotParamGroupRelation := paramrepo.BotParamGroup{
		BotID:           req.BotID,
		ParamGroupsID:   req.GroupID,
		CreatedByUserID: req.UserID,
	}

	if err := s.paramsRepo.CreateBotParamGroupRelation(ctx, newBotParamGroupRelation); err != nil {
		return nil, err
	}

	return &BotParamGroup{
		BotID:           newBotParamGroupRelation.BotID,
		GroupID:         newBotParamGroupRelation.ParamGroupsID,
		CreatedByUserID: newBotParamGroupRelation.CreatedByUserID,
		CreatedAt:       newBotParamGroupRelation.CreatedAt,
	}, nil
}

type ListBotParamGroupRelationsRequest struct {
	ginx.PaginationParam
	OrderBy string `validate:"omitempty,oneof=created_at created_by_user_id param_groups_id bot_id"`

	CreatedByUserID string `validate:"omitempty,uuid"`
	BotID           string `validate:"omitempty,uuid"`
	GroupID         string `validate:"omitempty,uuid"`
}

type ListBotParamGroupRelationsResponse struct {
	Total          int32
	BotParamGroups []BotParamGroup
}

func (s *Service) ListBotParamGroupRelations(ctx context.Context, req ListBotParamGroupRelationsRequest) (*ListBotParamGroupRelationsResponse, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "ListBotParamGroupRelations"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	if req.OrderBy == "" {
		req.OrderBy = "created_at"
	}

	result, err := s.paramsRepo.ListBotParamGroupsRelations(ctx, paramrepo.ListBotParamGroupsRelationsRequest{
		PaginationParam: req.PaginationParam,
		OrderBy:         req.OrderBy,
		GroupID:         req.GroupID,
		BotIDs:          []string{req.BotID},
		CreatedByUserID: req.CreatedByUserID,
	})
	if err != nil {
		return nil, err
	}

	var res = make([]BotParamGroup, len(result.BotParamGroups))
	for i, botParamGroup := range result.BotParamGroups {
		res[i] = BotParamGroup{
			BotID:           botParamGroup.BotID,
			GroupID:         botParamGroup.ParamGroupsID,
			CreatedByUserID: botParamGroup.CreatedByUserID,
			CreatedAt:       botParamGroup.CreatedAt,
		}
	}

	return &ListBotParamGroupRelationsResponse{
		Total:          int32(result.Total),
		BotParamGroups: res,
	}, nil
}

type RemoveBotParamGroupRelationRequest struct {
	BotID           string `validate:"required,uuid"`
	GroupID         string `validate:"required,uuid"`
	DeletedByUserID string `validate:"required,uuid"`
}

func (s *Service) RemoveBotParamGroupRelation(ctx context.Context, req RemoveBotParamGroupRelationRequest) error {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "RemoveBotParamGroupRelation"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return err
	}

	return s.paramsRepo.DeleteBotParamGroupRelation(ctx, paramrepo.BotParamGroup{
		BotID:           req.BotID,
		ParamGroupsID:   req.GroupID,
		DeletedByUserID: req.DeletedByUserID,
	})
}
