package paramsservice

import (
	"context"
	"fmt"
	"time"

	"github.com/herenow/atomic-bm/internal/db/repo/accountrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/botrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/paramrepo"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/pkg/validate"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

var _spanName = "ParamsService/%s"

const _ScopeName = "ParamsService"

var EncryptedParams = map[string]struct{}{
	"apiSecret": {},
	"apiKey":    {},
	"token":     {},
	"cookie":    {},
}

func shouldEncryptParam(value string) bool {
	_, found := EncryptedParams[value]
	return found
}

// BotOperator defines the interface that paramsservice needs to communicate with the operator.
type BotOperator interface {
	TriggerOperator(ctx context.Context, botID string) error
}

// IParamsService interface remains the same from the outside.
type IParamsService interface {
	Create(ctx context.Context, req CreateParamsRequest) (*CreateParamsResponse, error)
	List(ctx context.Context, req ListParamsRequest) (*ListParamsResponse, error)
	Delete(ctx context.Context, req DeleteParamsRequest) error
	CreateParamGroup(ctx context.Context, req CreateParamGroupRequest) (*ParamGroup, error)
	ListParamGroups(ctx context.Context, req ListParamGroupsRequest) (*ListParamGroupsResponse, error)
	DeleteParamGroup(ctx context.Context, req DeleteParamGroupRequest) error
	CreateBotParamGroupRelation(ctx context.Context, req CreateBotParamGroupRelationRequest) (*BotParamGroup, error)
	ListBotParamGroupRelations(ctx context.Context, req ListBotParamGroupRelationsRequest) (*ListBotParamGroupRelationsResponse, error)
	RemoveBotParamGroupRelation(ctx context.Context, req RemoveBotParamGroupRelationRequest) error
	GetActiveParams(ctx context.Context, req GetActiveParamsRequest) (*GetActiveParamsResponse, error)
}

// Service struct is updated to depend on the BotOperator interface.
type Service struct {
	botsRepo              botrepo.IBots
	paramsRepo            paramrepo.IParams
	accountsRepo          accountrepo.IAccountsRepo
	operatorSvc           BotOperator
	paramSecretKey        string
	paramSecretKeyVersion int
	trace                 trace.Tracer
}

// New constructor is updated for the new dependency interface.
func New(
	botsRepo botrepo.IBots,
	paramsRepo paramrepo.IParams,
	accountsRepo accountrepo.IAccountsRepo,
	operatorSvc BotOperator,
	paramSecretKey string,
	paramSecretKeyVersion int,
) *Service {
	return &Service{
		botsRepo:              botsRepo,
		paramsRepo:            paramsRepo,
		accountsRepo:          accountsRepo,
		operatorSvc:           operatorSvc,
		paramSecretKey:        paramSecretKey,
		paramSecretKeyVersion: paramSecretKeyVersion,
		trace:                 otel.Tracer(_ScopeName),
	}
}

type Scope int

const (
	BotScope Scope = iota
	AccountScope
	GatewayScope
	ParamGroupScope
)

// String returns the string representation of a Scope.
func (s Scope) String() string {
	return [...]string{"bot", "account", "gateway", "param_group"}[s]
}

var scopePrecedence = map[string]int32{
	GatewayScope.String():    1, // Low Priority
	AccountScope.String():    2,
	ParamGroupScope.String(): 3,
	BotScope.String():        4, // High Priority
}

type Param struct {
	ScopeID          string
	Scope            string
	Key              string
	Value            string
	IsEncrypted      bool
	SecretKeyVersion int
	Version          int
	CreatedById      string
	CreatedAt        time.Time
}

type ParamRequest struct {
	Key   string `validate:"required"`
	Value string `validate:"required"`
}

type CreateParamsRequest struct {
	UserID  string         `validate:"required,uuid"`
	ScopeID string         `validate:"required,uuid"`
	Scope   string         `validate:"required,oneof=bot account gateway param_group"`
	Params  []ParamRequest `validate:"required,dive"`
}

type CreateParamsResponse struct {
	Params []Param
}

// Create method now calls the OperatorSvc through the interface.
func (s *Service) Create(ctx context.Context, req CreateParamsRequest) (*CreateParamsResponse, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "Create"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	// Validate that the scope ID exists
	switch req.Scope {
	case BotScope.String():
		if _, err := s.botsRepo.Get(ctx, req.ScopeID, true); err != nil {
			return nil, err
		}
	case AccountScope.String(), GatewayScope.String():
		if _, err := s.accountsRepo.Get(ctx, req.ScopeID); err != nil {
			return nil, err
		}
	case ParamGroupScope.String():
		if _, err := s.paramsRepo.GetParamGroup(ctx, req.ScopeID); err != nil {
			return nil, err
		}
	}

	processedParams := make([]paramrepo.Param, 0, len(req.Params))
	for _, param := range req.Params {
		newParam := paramrepo.Param{
			ScopeID:     req.ScopeID,
			Scope:       req.Scope,
			Key:         param.Key,
			Value:       param.Value,
			CreatedByID: req.UserID,
		}

		if req.Scope == GatewayScope.String() && shouldEncryptParam(param.Key) {
			var err error
			newParam.Value, err = s.encryptParamValue(param.Value)
			if err != nil {
				return nil, err
			}
			newParam.IsEncrypted = true
			newParam.SecretKeyVersion = s.paramSecretKeyVersion
		}
		processedParams = append(processedParams, newParam)
	}

	if err := s.paramsRepo.Create(ctx, processedParams); err != nil {
		return nil, err
	}

	// The orchestration logic is replaced with a call to trigger the OperatorSvc.
	botsToUpdate, err := s.getBotsAffectedByScope(ctx, req.Scope, req.ScopeID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get bots affected by scope change")
	}

	for _, bot := range botsToUpdate {
		if err = s.operatorSvc.TriggerOperator(ctx, bot.ID); err != nil {
			// Log the error but don't fail the operation. The OperatorSvc's periodic loop will pick it up.
			s.trace.Start(ctx, "failed-trigger-reconciliation-on-param-create")
		}
	}

	decryptedParams, err := s.decryptParams(processedParams)
	if err != nil {
		return nil, err
	}

	return &CreateParamsResponse{Params: decryptedParams}, nil
}

type DeleteParamsRequest struct {
	UserID  string   `validate:"required,uuid"`
	ScopeID string   `validate:"required,uuid"`
	Scope   string   `validate:"required,oneof=bot account gateway param_group"`
	Keys    []string `validate:"required,dive"`
}

// Delete method is also simplified, removing all direct gRPC calls.
func (s *Service) Delete(ctx context.Context, req DeleteParamsRequest) error {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "Delete"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return err
	}

	paramsExists, err := s.paramsRepo.List(ctx, paramrepo.ListParamsRequest{
		Scope:   []string{req.Scope},
		ScopeID: []string{req.ScopeID},
	})
	if err != nil {
		return err
	}
	if paramsExists.Total == 0 {
		return errors.NotFound(codes.Params, "there are no parameters for this scope")
	}

	// To "delete" a parameter, we create a new entry with a null value.
	// The view `params_view` in the DB will then exclude this parameter.
	paramsToDelete := make([]paramrepo.Param, len(req.Keys))
	for i, key := range req.Keys {
		paramsToDelete[i] = paramrepo.Param{
			ScopeID:     req.ScopeID,
			Scope:       req.Scope,
			Key:         key,
			Value:       "", // An empty value signifies deletion in the view logic
			CreatedByID: req.UserID,
		}
	}

	if err = s.paramsRepo.Create(ctx, paramsToDelete); err != nil {
		return errors.Wrap(err, "failed to delete params")
	}

	// Trigger the OperatorSvc for all affected bots.
	botsToUpdate, err := s.getBotsAffectedByScope(ctx, req.Scope, req.ScopeID)
	if err != nil {
		return errors.Wrap(err, "failed to get bots affected by scope change")
	}

	for _, bot := range botsToUpdate {
		if err = s.operatorSvc.TriggerOperator(ctx, bot.ID); err != nil {
			s.trace.Start(ctx, "failed-trigger-reconciliation-on-param-delete")
		}
	}

	return nil
}

// getBotsAffectedByScope remains as a private helper function. It's still needed
// to determine which bots need to be reconciled after a parameter change.
func (s *Service) getBotsAffectedByScope(ctx context.Context, scope, scopeID string) ([]*botrepo.Bot, error) {
	var botsToUpdate []*botrepo.Bot

	switch scope {
	case BotScope.String():
		bot, err := s.botsRepo.Get(ctx, scopeID, true)
		if err != nil {
			return nil, err
		}
		botsToUpdate = append(botsToUpdate, bot)

	case AccountScope.String(), GatewayScope.String():
		botsList, err := s.botsRepo.List(ctx, botrepo.ListBotsRequest{AccountID: scopeID, WithAccount: true})
		if err != nil {
			return nil, err
		}
		botsToUpdate = botsList.Bots

	case ParamGroupScope.String():
		relations, err := s.paramsRepo.ListBotParamGroupsRelations(ctx, paramrepo.ListBotParamGroupsRelationsRequest{
			GroupID: scopeID,
		})
		if err != nil {
			return nil, err
		}

		for _, rel := range relations.BotParamGroups {
			var bot *botrepo.Bot
			bot, err = s.botsRepo.Get(ctx, rel.BotID, true)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to get bot %s from param group relation", rel.BotID)
			}
			botsToUpdate = append(botsToUpdate, bot)
		}
	}

	return botsToUpdate, nil
}

type GetActiveParamsRequest struct {
	BotID string `validate:"required"`
}

type GetActiveParamsResponse struct {
	Params []Param
}

func (s *Service) GetActiveParams(ctx context.Context, req GetActiveParamsRequest) (*GetActiveParamsResponse, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "GetActiveParams"))
	defer span.End()

	if err := validate.Validator.ValidateStruct(req); err != nil {
		return nil, err
	}

	bot, err := s.botsRepo.Get(ctx, req.BotID, true)
	if err != nil {
		return nil, err
	}

	botsParams, err := s.getActiveParamsForBots(ctx, []*botrepo.Bot{bot})
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get active params for bot %s", req.BotID)
	}

	return &GetActiveParamsResponse{Params: botsParams[req.BotID]}, nil
}

type ListParamsRequest struct {
	ginx.PaginationParam
	OrderBy string

	Scope   []string
	ScopeID []string
}

func (l *ListParamsRequest) isValidOrderBy() bool {
	if l.OrderBy == "" {
		return true
	}
	validFields := map[string]bool{
		"scope":         true,
		"key":           true,
		"created_by_id": true,
		"created_at":    true,
	}
	return validFields[l.OrderBy]
}

type ListParamsResponse struct {
	Total  int32
	Params []Param
}

func (s *Service) List(ctx context.Context, req ListParamsRequest) (*ListParamsResponse, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "List"))
	defer span.End()

	if !req.isValidOrderBy() {
		return nil, errors.BadRequest(codes.Parameters, "'%s' is a invalid field for orderBy sorter", req.OrderBy)
	}

	params, err := s.paramsRepo.List(ctx, paramrepo.ListParamsRequest{
		PaginationParam: req.PaginationParam,
		OrderBy:         req.OrderBy,
		Scope:           req.Scope,
		ScopeID:         req.ScopeID,
	})
	if err != nil {
		return nil, err
	}

	decryptedParams, err := s.decryptParams(params.Params)
	if err != nil {
		return nil, err
	}

	return &ListParamsResponse{Params: decryptedParams, Total: int32(params.Total)}, nil
}

type paramDataBundle struct {
	botToGroupIDs      map[string][]string
	paramsByScope      map[string]map[string][]Param
	paramGroupPriority map[string]int32
}

// fetchBatchData encapsulates all the data fetching and initial processing.
func (s *Service) fetchBatchData(ctx context.Context, bots []*botrepo.Bot) (*paramDataBundle, error) {
	var allBotIDs []string
	accountIDs := make(map[string]struct{})
	for _, bot := range bots {
		allBotIDs = append(allBotIDs, bot.ID)
		accountIDs[bot.AccountID] = struct{}{}
	}

	relations, err := s.paramsRepo.ListBotParamGroupsRelations(ctx, paramrepo.ListBotParamGroupsRelationsRequest{BotIDs: allBotIDs})
	if err != nil {
		return nil, errors.Wrap(err, "failed to list bot param group relations in batch")
	}

	botToGroupIDs := make(map[string][]string)
	paramGroupIDs := make(map[string]struct{})
	for _, rel := range relations.BotParamGroups {
		botToGroupIDs[rel.BotID] = append(botToGroupIDs[rel.BotID], rel.ParamGroupsID)
		paramGroupIDs[rel.ParamGroupsID] = struct{}{}
	}

	var allScopeIDs []string
	allScopeIDs = append(allScopeIDs, allBotIDs...)
	for id := range accountIDs {
		allScopeIDs = append(allScopeIDs, id)
	}
	for id := range paramGroupIDs {
		allScopeIDs = append(allScopeIDs, id)
	}

	paramsList, err := s.paramsRepo.List(ctx, paramrepo.ListParamsRequest{ScopeID: allScopeIDs})
	if err != nil {
		return nil, err
	}

	decryptedParams, err := s.decryptParams(paramsList.Params)
	if err != nil {
		return nil, err
	}

	paramGroupsList, err := s.paramsRepo.ListParamGroups(ctx, paramrepo.ListParamGroupsRequest{})
	if err != nil {
		return nil, err
	}

	paramGroupPriority := make(map[string]int32)
	for _, pg := range paramGroupsList.ParamGroups {
		paramGroupPriority[pg.ID] = pg.Priority
	}

	paramsByScope := make(map[string]map[string][]Param)
	for _, p := range decryptedParams {
		if _, ok := paramsByScope[p.Scope]; !ok {
			paramsByScope[p.Scope] = make(map[string][]Param)
		}
		paramsByScope[p.Scope][p.ScopeID] = append(paramsByScope[p.Scope][p.ScopeID], p)
	}

	return &paramDataBundle{
		botToGroupIDs:      botToGroupIDs,
		paramsByScope:      paramsByScope,
		paramGroupPriority: paramGroupPriority,
	}, nil
}

// resolveBotParams determines the final active parameters for a single bot by applying a hierarchical precedence logic.
// The hierarchy is: Bot > ParamGroup > Account > Gateway.
// If there are conflicts within the same level, specific tie-breaking rules are applied.
func (s *Service) resolveBotParams(bot *botrepo.Bot, bundle *paramDataBundle) []Param {
	// activeParams stores the winning parameter for each key. The key of the map is the parameter key (e.g., "minSpread").
	activeParams := make(map[string]Param)

	// 1. Gather all parameters that could possibly apply to this bot from all scopes.
	// The order of appending here does not matter as the precedence logic will sort it out.
	var relevantParams []Param
	relevantParams = append(relevantParams, bundle.paramsByScope[GatewayScope.String()][bot.AccountID]...)
	relevantParams = append(relevantParams, bundle.paramsByScope[AccountScope.String()][bot.AccountID]...)
	for _, groupID := range bundle.botToGroupIDs[bot.ID] {
		relevantParams = append(relevantParams, bundle.paramsByScope[ParamGroupScope.String()][groupID]...)
	}
	relevantParams = append(relevantParams, bundle.paramsByScope[BotScope.String()][bot.ID]...)

	// 2. Iterate through all collected parameters and decide which one wins for each key.
	for _, newParam := range relevantParams {
		actualParam, exists := activeParams[newParam.Key]
		// If we haven't seen this parameter key before, it's the current winner by default.
		if !exists {
			activeParams[newParam.Key] = newParam
			continue
		}

		// If the key already exists, we need to apply precedence rules to decide if the new one should overwrite the existing one.
		newScopePrecedence := scopePrecedence[newParam.Scope]
		actualScopePrecedence := scopePrecedence[actualParam.Scope]

		// A parameter from a scope with higher precedence always wins (e.g., Bot scope (4) > Account scope (2)).
		if newScopePrecedence > actualScopePrecedence {
			activeParams[newParam.Key] = newParam
		} else if newScopePrecedence == actualScopePrecedence {
			// If the scopes have the same precedence, we need a tie-breaker.
			// For ParamGroups, the one with the higher priority value wins.
			if newParam.Scope == ParamGroupScope.String() {
				newPriority := bundle.paramGroupPriority[newParam.ScopeID]
				actualPriority := bundle.paramGroupPriority[actualParam.ScopeID]
				// If priorities are also the same, the most recently created parameter wins.
				if newPriority > actualPriority || (newPriority == actualPriority && newParam.CreatedAt.After(actualParam.CreatedAt)) {
					activeParams[newParam.Key] = newParam
				}
			} else if newParam.CreatedAt.After(actualParam.CreatedAt) {
				// For all other scopes (Bot, Account, Gateway), the most recently created parameter wins the tie.
				activeParams[newParam.Key] = newParam
			}
		}
	}

	// 3. Convert the map of winning parameters back to a slice for the final result.
	var finalParams []Param
	for _, param := range activeParams {
		finalParams = append(finalParams, param)
	}
	return finalParams
}

// getActiveParamsForBots fetches the active params for an array of bots.
func (s *Service) getActiveParamsForBots(ctx context.Context, bots []*botrepo.Bot) (map[string][]Param, error) {
	ctx, span := s.trace.Start(ctx, fmt.Sprintf(_spanName, "getActiveParamsForBots"))
	defer span.End()

	if len(bots) == 0 {
		return make(map[string][]Param), nil
	}

	bundle, err := s.fetchBatchData(ctx, bots)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch batch data for param resolution")
	}

	finalBotParams := make(map[string][]Param)
	for _, bot := range bots {
		finalBotParams[bot.ID] = s.resolveBotParams(bot, bundle)
	}

	return finalBotParams, nil
}
