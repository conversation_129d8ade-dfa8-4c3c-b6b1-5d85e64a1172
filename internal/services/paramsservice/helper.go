package paramsservice

import (
	"github.com/segmentio/asm/base64"

	"github.com/herenow/atomic-bm/internal/db/repo/paramrepo"
	"github.com/herenow/atomic-bm/pkg/crypto"
)

// encryptParamValue encrypts a parameter value using the configured secret key.
//
// It takes a string 'value' representing the parameter value to be encrypted
// and encrypts it using the secret key configured in the controller's configuration.
// The encrypted value is returned as a Base64-encoded string.
func (s *Service) encryptParamValue(value string) (string, error) {
	key, err := crypto.DecodeBase64KeyString(s.paramSecretKey)
	if err != nil {
		return "", err
	}

	encryptedValue, err := crypto.Encrypt([]byte(value), key)
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(encryptedValue), nil
}

// decryptParamValue decrypts a parameter value using the configured secret key.
//
// It takes a Base64-encoded string 'value', representing the encrypted parameter value,
// and decrypts it using the secret key configured in the controller's configuration.
// The decrypted value is returned as a string.
func (s *Service) decryptParamValue(value string) (string, error) {
	key, err := crypto.DecodeBase64KeyString(s.paramSecretKey)
	if err != nil {
		return "", err
	}

	decodedValue, err := base64.StdEncoding.DecodeString(value)
	if err != nil {
		return "", err
	}

	encryptedValue, err := crypto.Decrypt(decodedValue, key)
	if err != nil {
		return "", err
	}
	return string(encryptedValue), nil
}

// decryptParams decrypts gateway params and convert everything to Param struct
func (s *Service) decryptParams(params []paramrepo.Param) ([]Param, error) {
	decryptedParams := make([]Param, len(params))

	for i, param := range params {
		isEncryptedWithSameVersion := param.Scope == "gateway" && param.IsEncrypted && param.SecretKeyVersion == s.paramSecretKeyVersion
		if isEncryptedWithSameVersion {
			var err error
			param.Value, err = s.decryptParamValue(param.Value)
			if err != nil {
				return nil, err
			}
			decryptedParams[i] = fromRepositoryResponse(param.CreatedByID, param)
		} else {
			decryptedParams[i] = fromRepositoryResponse(param.CreatedByID, param)
		}
	}

	return decryptedParams, nil
}

func fromRepositoryResponse(userID string, param paramrepo.Param) Param {
	return Param{
		ScopeID:          param.ScopeID,
		Scope:            param.Scope,
		Key:              param.Key,
		Value:            param.Value,
		IsEncrypted:      param.IsEncrypted,
		SecretKeyVersion: param.SecretKeyVersion,
		Version:          param.Version,
		CreatedById:      userID,
		CreatedAt:        param.CreatedAt,
	}
}
