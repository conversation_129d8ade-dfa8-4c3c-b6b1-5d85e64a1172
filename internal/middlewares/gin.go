package middlewares

import (
	"context"

	"github.com/gin-gonic/gin"

	"github.com/herenow/atomic-bm/internal/pkg/errors"
)

type ctxGinKey struct{}

// GinToContextMiddleware - at the resolver level we only have access
// to context.Context inside centrifuge, but we need the gin context.
// So we create gin middleware to add its context to the context.Context
// used by centrifuge websocket server.
func GinToContextMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := context.WithValue(c.Request.Context(), ctxGinKey{}, c)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

// GinFromContext - we recover the gin context from the context.Context
// struct where we added it just above
func GinFromContext(ctx context.Context) (*gin.Context, error) {
	ginContext := ctx.Value(ctxGinKey{})
	if ginContext == nil {
		return nil, errors.Errorf("could not retrieve gin.Context")
	}

	gc, ok := ginContext.(*gin.Context)
	if !ok {
		return nil, errors.<PERSON>rrorf("gin context has wrong type")
	}

	return gc, nil
}
