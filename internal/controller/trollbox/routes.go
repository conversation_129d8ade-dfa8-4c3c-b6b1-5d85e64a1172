package trollbox

import (
	"github.com/herenow/atomic-bm/cmd/app/dependencies"
	"github.com/herenow/atomic-bm/internal/app"
)

func Register(app *app.App, dp *dependencies.Container) {
	tbHandler := New(dp.TrollboxRepo, app.Session())

	g := app.APIGroup().Group("/trollbox")
	g.GET("/history", tbHandler.MessageHistory)
	g.POST("/search", tbHandler.SearchMessagesByContent)
	g.POST("/filter", tbHandler.SearchByTimeRange)
}
