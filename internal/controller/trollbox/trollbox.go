package trollbox

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/auth/session"
	"github.com/herenow/atomic-bm/internal/db/repo/trollboxrepo"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/pkg/util"
)

var _spanName = "TrollboxController/%s"

const _ScopeName = "TrollboxController"

var (
	ErrEndTimeMustBeGreater = errors.BadRequest(codes.Parameters, "endTime must be greater than or equal to startTime")
	ErrStartTimeFormat      = errors.BadRequest(codes.Parameters, "invalid startTime format")
	ErrEndTimeFormat        = errors.BadRequest(codes.Parameters, "invalid endTime format")
)

type Controller struct {
	tbRepo  trollboxrepo.ITrollBoxRepository
	session *session.Manager
	tracer  trace.Tracer
}

func New(tbRepo trollboxrepo.ITrollBoxRepository, session *session.Manager) *Controller {
	return &Controller{
		tbRepo:  tbRepo,
		session: session,
		tracer:  otel.Tracer(_ScopeName),
	}
}

// Message represents a chat message
// @Description Message structure for chat messages
type Message struct {
	// Message ID
	ID string `json:"id" example:"7ae09324-87d3-4029-b96c-8a0769f300cd" format:"uuid" extensions:"x-order=0"`
	// User ID associated with the message
	UserID string `json:"userId" example:"df5e7fdc-6ad1-42ee-98b4-b6159d9bd232" format:"uuid" extensions:"x-order=1"`
	// Sequence number of the message
	Sequence int64 `json:"seq" example:"1" extensions:"x-order=2"`
	// Content of the message
	Content string `json:"content" example:"Hello, World." extensions:"x-order=3"`
	// Timestamp when the message was sent
	Timestamp time.Time `json:"timestamp" example:"2023-09-11T18:35:32.891411Z" extensions:"x-order=4"`
} //@name Message

// MessageHistoryRequest represents the request parameters for fetching message history
// @Description MessageHistoryRequest is used to specify parameters for retrieving message history.
type MessageHistoryRequest struct {
	// Cursor for pagination
	Cursor string `form:"cursor" example:"MjAyMy0wOS0xMVQxODoxNDoyMy43MTg2MzVa" validate:"optional" extensions:"x-order=0"`
	// Page size for pagination
	PageSize int `form:"pageSize" default:"20" example:"30" validate:"optional" extensions:"x-order=1"`
} //@name MessageHistoryRequest

// MessageHistoryResponse represents the response for fetching message history
// @Description MessageHistoryResponse is the response structure for retrieving message history.
type MessageHistoryResponse struct {
	// Indicates whether there is more history available
	HashNext bool `json:"hashNext" example:"true" extensions:"x-order=0"`
	// Cursor for the next page of history
	NextCursor string `json:"nextCursor" example:"MjAyMy0wOS0xMVQxODoxNDoyMy43MTg2MzVa" extensions:"x-order=1"`
	// List of messages in the history
	Messages []Message `json:"messages" extensions:"x-order=2"`
} //@name MessageHistoryResponse

// MessageHistory retrieves chat message history
//
// @Summary Get chat message history.
// @Description Retrieve chat message history with optional pagination parameters.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Trollbox
// @Param data query MessageHistoryRequest true "Message History Parameters"
// @Success 200 {object} ginx.Response[MessageHistoryResponse] "Message History"
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /trollbox/history [get]
func (ctrl *Controller) MessageHistory(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "MessageHistory"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	var req MessageHistoryRequest
	if err := ginx.ParseQuery(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	if req.PageSize == 0 {
		req.PageSize = 20
	}

	hist, hasNext, nextCur, err := ctrl.tbRepo.GetMessagesByCursor(ctx, req.Cursor, req.PageSize)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResSuccess(c, &MessageHistoryResponse{
		HashNext:   hasNext,
		NextCursor: nextCur,
		Messages:   fromRepositoryResponse(hist),
	})
}

// TODO(refactor): We dont need two endpoints for doing search functionalities
//  we should merge the SearchMessagesByContent and SearchByTimeRange into a single endpoint

// SearchMessagesByContentRequest represents the request parameters for searching messages by content
// @Description SearchMessagesByContentRequest is used to specify the content for filtering messages.
type SearchMessagesByContentRequest struct {
	// Content to search for in messages
	Content string `json:"content" example:"Hello, World." extensions:"x-order=0"`
} //@name SearchMessagesByContentRequest

// SearchMessagesByContent searches messages by content
//
// @Summary Search messages by content.
// @Description Retrieve messages that match the specified content.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Trollbox
// @Param data body SearchMessagesByContentRequest true "Search Content"
// @Success 200 {object} ginx.Response[[]Message] "Matching Messages"
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /trollbox/search [post]
func (ctrl *Controller) SearchMessagesByContent(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "SearchMessagesByContent"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	var req SearchMessagesByContentRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	filteredMsgs, err := ctrl.tbRepo.GetMessagesByContent(ctx, req.Content)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResSuccess(c, fromRepositoryResponse(filteredMsgs))
}

// SearchMessagesByTimeRangeRequest represents the request parameters for searching messages by time range
// @Description SearchMessagesByTimeRangeRequest is used to specify the time range for filtering messages.
type SearchMessagesByTimeRangeRequest struct {
	// Start time of the search range
	StartTime string `json:"startTime" example:"1694456065.811983" extensions:"x-order=0"`
	// End time of the search range
	EndTime string `json:"endTime" example:"1694456065.811983" extensions:"x-order=1"`
} //@name SearchMessagesByTimeRangeRequest

// SearchByTimeRange searches messages by time range
//
// @Summary Search messages by time range.
// @Description Retrieve messages that fall within the specified time range.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Trollbox
// @Param data body SearchMessagesByTimeRangeRequest true "Time Range"
// @Success 200 {object} ginx.Response[[]Message] "Matching Messages"
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /trollbox/filter [post]
func (ctrl *Controller) SearchByTimeRange(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "SearchByTimeRange"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	var req SearchMessagesByTimeRangeRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	startTime, err := util.ParseAndValidateTimestamp(req.StartTime)
	if err != nil {
		ginx.ResError(c, ErrStartTimeFormat)
		return
	}

	endTime, err := util.ParseAndValidateTimestamp(req.EndTime)
	if err != nil {
		ginx.ResError(c, ErrEndTimeFormat)
		return
	}

	if endTime.Before(startTime) {
		ginx.ResError(c, ErrEndTimeMustBeGreater)
		return
	}

	filteredMsgs, err := ctrl.tbRepo.GetMessagesByTimeRange(ctx, startTime, endTime)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResSuccess(c, fromRepositoryResponse(filteredMsgs))
}

// fromRepositoryResponse converts repository messages to API messages
func fromRepositoryResponse(repoMsgs []*trollboxrepo.TrollBox) []Message {
	var r = make([]Message, len(repoMsgs))
	for i, m := range repoMsgs {
		r[i] = Message{
			ID:        m.ID,
			UserID:    m.UserID,
			Sequence:  m.Sequence,
			Content:   m.Content,
			Timestamp: m.Timestamp,
		}
	}
	return r
}
