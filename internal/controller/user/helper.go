package user

import (
	"context"
	"fmt"
	"net/http"

	"github.com/mileusna/useragent"
	"github.com/segmentio/encoding/json"

	"github.com/herenow/atomic-bm/internal/auth/session"
)

func (ctrl *Controller) getGeoLocation(ctx context.Context, ip string) (country, city, area string) {
	ctx, span := ctrl.tracer.Start(ctx, fmt.Sprintf(_spanName, "getGeoLocation"))
	defer span.End()

	unknown := "Unknown"
	var res struct {
		City    string `json:"city"`
		Region  string `json:"regionName"`
		Country string `json:"country"`
	}
	res.City = unknown
	res.Region = unknown
	res.Country = unknown

	resp, err := http.Get(fmt.Sprintf("http://ip-api.com/json/%s", ip))
	if err != nil {
		return res.Country, res.City, res.Region
	}
	defer resp.Body.Close()

	if err = json.NewDecoder(resp.Body).Decode(&res); err != nil {
		return res.Country, res.City, res.Region
	}

	return res.Country, res.City, res.Region
}

func getDeviceInfo(userAgent string) (name, os, deviceType string) {
	unknown := "Unknown"
	ua := useragent.Parse(userAgent)
	if !ua.Desktop && !ua.Mobile && !ua.Tablet && !ua.Bot {
		ua.Name = unknown
		ua.OS = unknown
	}

	switch {
	case ua.Mobile:
		deviceType = "Mobile"
	case ua.Desktop:
		deviceType = "Desktop"
	case ua.Bot:
		deviceType = "Bot"
	case ua.Tablet:
		deviceType = "Tablet"
	default:
		deviceType = unknown
	}

	return ua.Name, ua.OS, deviceType
}

// convertSessionListToResponseFormat converts the repository session list to the desired array response format.
func convertSessionListToResponseFormat(ss *[]session.Session) []Session {
	response := make([]Session, len(*ss))
	for i, s := range *ss {
		d := s.Details
		l := d.Location
		response[i] = Session{
			ID:     s.ID,
			Token:  s.Token,
			UserID: s.UserID,
			Details: Details{
				IP:           d.IP,
				DeviceType:   d.DeviceType,
				DeviceName:   d.DeviceName,
				PlatformType: d.PlatformType,
				Location: Location{
					Country: l.Country,
					City:    l.City,
					Area:    l.Area,
				},
			},
			Expiry:    s.Expiry,
			CreatedAt: s.CreatedAt,
			UpdatedAt: s.UpdatedAt,
		}
	}
	return response
}
