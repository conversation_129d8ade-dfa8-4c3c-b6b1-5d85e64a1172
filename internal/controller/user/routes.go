package user

import (
	"github.com/herenow/atomic-bm/cmd/app/dependencies"
	"github.com/herenow/atomic-bm/internal/app"
)

func Register(app *app.App, dp *dependencies.Container) {
	userHandler := New(dp.UserRepo, dp.SessionRepo, app.Session(), app.WebSocketClient())
	{
		usersRoot := app.APIGroup().Group("/users")
		usersRoot.POST("/login", userHandler.Login)
		usersRoot.POST("/logout", userHandler.Logout)
		usersRoot.PATCH("/password", userHandler.ChangeUserPassword)
		usersRoot.GET("/info", userHandler.ListUsers)
		usersRoot.GET("/me", userHandler.Me)
		{
			userSessions := usersRoot.Group("/sessions")
			userSessions.POST("/bulkDelete", userHandler.DeleteAllSessions)
			userSessions.DELETE("/:id", userHandler.DeleteUserSessionByID)
			userSessions.GET("/active", userHandler.ListUserSessions)
		}
	}
}
