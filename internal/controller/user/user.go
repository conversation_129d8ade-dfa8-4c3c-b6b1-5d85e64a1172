package user

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"golang.org/x/crypto/bcrypt"

	"github.com/herenow/atomic-bm/internal/auth/session"
	"github.com/herenow/atomic-bm/internal/db/repo/sessionrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/userrepo"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/ws"
)

var _spanName = "UserController/%s"

const _ScopeName = "UserController"

var (
	InvalidEmailOrPassword = errors.BadRequest(codes.Credentials, "invalid email or password")
	InvalidPassword        = errors.BadRequest(codes.Credentials, "invalid password")
	InvalidPasswordLength  = errors.BadRequest(codes.Parameters, "invalid password length")
)

// TODO(refactor): move logic to the services package

type Controller struct {
	userRepo    userrepo.IUserRepository
	sessionRepo sessionrepo.ISessionRepository
	session     *session.Manager
	tracer      trace.Tracer

	// For now that we are using user repo as the manager of sessions
	// it is, login and logout operations, we're going to need ws package
	// dependency for alerting about expired tokens due user logout.
	wsClient *ws.WebsocketClient
}

func New(userRepo userrepo.IUserRepository,
	sessionRepo sessionrepo.ISessionRepository,
	session *session.Manager,
	wsClient *ws.WebsocketClient) *Controller {
	return &Controller{
		userRepo:    userRepo,
		sessionRepo: sessionRepo,
		tracer:      otel.Tracer(_ScopeName),
		session:     session,
		wsClient:    wsClient,
	}
}

// User represents a user.
// @Description User information about individuals.
type User struct {
	// User ID
	ID string `json:"id" example:"e7cf3a21-9c6b-4a01-af13-2c8e8f91a6bb" format:"uuid" extensions:"x-order=0"`
	// User's full name
	Name string `json:"name" example:"John Doe" extensions:"x-order=1"`
	// User's email address
	Email string `json:"email" example:"<EMAIL>" extensions:"x-order=2"`
	// User's password (not exposed in JSON)
	Password string `json:"-"`
	// User's role in the system
	Role string `json:"role" example:"role_admin" extensions:"x-order=3"`
	// Time when the user was created
	CreatedAt time.Time `json:"createdAt" example:"2023-08-19T14:11:07.84483Z" extensions:"x-order=4"`
	// Last time when the user was modified
	UpdatedAt time.Time `json:"updatedAt" example:"2024-02-04T19:41:44.008444328Z" extensions:"x-order=5"`
} // @name User

// Me retrieves information about the currently authenticated user.
//
// @Summary Get user details.
// @Description Retrieves details of the authenticated user.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags User
// @Success 200 {object} ginx.Response[User]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /users/me [get]
func (ctrl *Controller) Me(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "Me"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	user, err := ctrl.userRepo.Get(ctx, userID)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResSuccess(c, User{
		ID:        user.ID,
		Name:      user.Name,
		Email:     user.Email,
		Role:      user.Role,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	})
}

// ListUsers retrieves a list of users.
//
// @Summary Get a list of users.
// @Description Retrieves a list of users.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags User
// @Success 200 {object} ginx.Response[[]User] "List of Users"
// @Failure 401 {object} errors.Error "you are not authorized to access this resource"
// @Router /users/info [get]
func (ctrl *Controller) ListUsers(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ListUsers"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	userList, err := ctrl.userRepo.List(ctx)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	var users = make([]User, len(userList))
	for i, user := range userList {
		users[i] = User{
			ID:        user.ID,
			Name:      user.Name,
			Email:     user.Email,
			Role:      user.Role,
			CreatedAt: user.CreatedAt,
			UpdatedAt: user.UpdatedAt,
		}
	}

	// TODO(refactor): return response with pagination

	ginx.ResSuccess(c, users)
}

// LoginRequest represents the payload for user login.
// @Description Request payload for user login.
type LoginRequest struct {
	// User's email address
	Email string `json:"email" validate:"required" example:"<EMAIL>" extensions:"x-order=0"`
	// User's password
	Password string `json:"password" validate:"required"  example:"P@ssw0rd" extensions:"x-order=1"`
} // @name LoginRequest

// LoginResponse represents the response after a successful user login.
// @Description Response payload for a successful user login.
type LoginResponse struct {
	// Session token
	Token string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ" extensions:"x-order=0"`
	// Token expiration time
	Expiry time.Time `json:"expiry" example:"2024-02-07T15:45:00.000Z" extensions:"x-order=1"`
} // @name LoginResponse

// Login authenticates a user and generates a session token.
//
// @Summary User login.
// @Description Authenticates a user based on email and password, generating a session token.
// @Accept json
// @Produce json
// @Tags User
// @Param data body LoginRequest true "Login information"
// @Success 200 {object} ginx.Response[LoginResponse] "Login successful"
// @Failure 401 {object} errors.Error "invalid email or password"
// @Router /users/login [post]
func (ctrl *Controller) Login(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "Login"))
	defer span.End()

	var req LoginRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	user, err := ctrl.userRepo.ByEmail(ctx, req.Email)
	if err != nil {
		if errors.Is(err, userrepo.ErrUsersNotFound) {
			ginx.ResError(c, InvalidEmailOrPassword)
			return
		}
		ginx.ResError(c, err)
		return
	}

	if err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		ginx.ResError(c, InvalidPassword)
		return
	}

	userIP := c.ClientIP()
	country, city, area := ctrl.getGeoLocation(ctx, userIP)
	name, os, deviceType := getDeviceInfo(c.Request.UserAgent())

	ctrl.session.Create(c, &session.Session{
		UserID: user.ID,
		Details: session.MD{
			IP:           userIP,
			DeviceType:   deviceType,
			DeviceName:   name,
			PlatformType: os,
			Location: session.Location{
				Country: country,
				City:    city,
				Area:    area,
			},
		},
	})

	if err = ctrl.session.RenewToken(c); err != nil {
		ginx.ResError(c, errors.Wrap(err, "renew session token"))
		return
	}

	s := ctrl.session.FromContext(c)
	if s.Token == "" {
		ginx.ResError(c, errors.Errorf("empty session token"))
		return
	}

	ginx.ResSuccess(c, &LoginResponse{
		Token:  s.Token,
		Expiry: s.Expiry,
	})
}

// Logout terminates the user's session and logs them out.
//
// @Summary User logout.
// @Description Terminates the user's session, logs them out, and disconnects any associated WebSocket connection.
// @Security Bearer
// @Produce json
// @Tags User
// @Success 204 "Logout successful"
// @Failure 401 {object} errors.Error "you are not authorized to access this resource"
// @Router /users/logout [post]
func (ctrl *Controller) Logout(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "Logout"))
	defer span.End()

	s := ctrl.session.FromContext(c)
	if s.Token == "" {
		ginx.ResError(c, errors.Errorf("empty session token"))
		return
	}

	if s.UserID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	ctrl.wsClient.NotifyTokenExpirationAndDisconnect(ctx, s.Token)

	if err := ctrl.session.RenewToken(c); err != nil {
		ginx.ResError(c, errors.Wrap(err, "renew session token"))
		return
	}

	if err := ctrl.session.Delete(c); err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResNoContent(c)
}

// Session represents a user session.
// @Description User session details including session ID, user ID, device information, and location.
type Session struct {
	// Session ID
	ID uint64 `json:"id" example:"12345" extensions:"x-order=0"`
	// User's session token (not exposed in JSON)
	Token string `json:"-"`
	// User ID associated with the session
	UserID string `json:"userID" example:"e7cf3a21-9c6b-4a01-af13-2c8e8f91a6bb" format:"uuid" extensions:"x-order=1"`
	// Session details including IP, device type, device name, platform type, and location
	Details Details `json:"details" extensions:"x-order=2"`
	// Session expiration time
	Expiry time.Time `json:"expiry" example:"2024-02-07T15:45:00.000Z" extensions:"x-order=3"`
	// Time when the session was created
	CreatedAt time.Time `json:"createdAt" example:"2023-08-19T14:11:07.84483Z" extensions:"x-order=4"`
	// Last time when the session was updated
	UpdatedAt time.Time `json:"updatedAt" example:"2024-02-04T19:41:44.008444328Z" extensions:"x-order=5"`
} // @name Session

// Details represents additional details about a user session.
// @Description Additional details about a user session, including
// @Description IP, device type, device name, platform type, and location.
type Details struct {
	// IP address of the device
	IP string `json:"ip" example:"***********" extensions:"x-order=0"`
	// Type of device (e.g., mobile, desktop)
	DeviceType string `json:"deviceType" enums:"mobile,desktop" example:"mobile" extensions:"x-order=1"`
	// Name of the device
	DeviceName string `json:"deviceName" example:"John's iPhone" extensions:"x-order=2"`
	// Type of platform (e.g., iOS, Android, Web)
	PlatformType string `json:"platformType" enums:"ios,android,web" example:"ios" extensions:"x-order=3"`
	// Location details including country, city, and area
	Location Location `json:"location" extensions:"x-order=4"`
} // @name Details

// Location represents the location details of a user session.
// @Description Location details including country, city, and area.
type Location struct {
	// Country name
	Country string `json:"country" example:"United States" extensions:"x-order=0"`
	// City name
	City string `json:"city" example:"New York" extensions:"x-order=1"`
	// Area name
	Area string `json:"area" example:"Manhattan" extensions:"x-order=2"`
} // @name Location

// ListUserSessions retrieves a list of sessions for the authenticated user.
//
// @Summary Get user active sessions.
// @Description Retrieves a list of sessions associated with the authenticated user.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags User
// @Success 200 {object} ginx.Response[[]Session] "List of User Sessions"
// @Failure 401 {object} errors.Error "you are not authorized to access this resource"
// @Router /users/sessions/active [get]
func (ctrl *Controller) ListUserSessions(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ListUserSessions"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	ss, err := ctrl.sessionRepo.ListByUserID(ctx, userID)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	// TODO(refactor): return response with pagination

	ginx.ResSuccess(c, convertSessionListToResponseFormat(ss))
}

// DeleteUserSessionByID deletes a user session by ID.
//
// @Summary Delete a user session.
// @Description Deletes a user session identified by the provided session ID.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags User
// @Param sessionId path int true "Session ID to be deleted" example(13)
// @Success 204 "No Content"
// @Failure 401 {object} errors.Error "you are not authorized to access this resource"
// @Router /users/sessions/{sessionId} [delete]
func (ctrl *Controller) DeleteUserSessionByID(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "DeleteUserSessionByID"))
	defer span.End()

	sessionId, err := strconv.ParseInt(c.Param("id"), 10, 32)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	if sessionId == 0 {
		ginx.ResError(c, errors.BadRequest(codes.Parameters, "sessionId is required"))
		return
	}

	s := ctrl.session.FromContext(c)
	if s.UserID == "" || s.Token == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	s, err = ctrl.sessionRepo.Get(ctx, s.UserID, int32(sessionId))
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	if err = ctrl.sessionRepo.Delete(ctx, s.UserID, int32(sessionId)); err != nil {
		ginx.ResError(c, err)
		return
	}

	ctrl.wsClient.NotifyTokenExpirationAndDisconnect(ctx, s.Token)

	ginx.ResNoContent(c)
}

// DeleteAllSessions terminates all sessions associated with the authenticated user.
//
// @Summary Delete all user sessions.
// @Description Terminates all sessions associated with the authenticated user, logging them out,
// @Description and disconnecting any associated WebSocket connections.
// @Security Bearer
// @Produce json
// @Tags User
// @Success 204 "No Content"
// @Failure 401 {object} errors.Error "you are not authorized to access this resource"
// @Router /users/sessions/bulkDelete [post]
func (ctrl *Controller) DeleteAllSessions(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "DeleteAllSessions"))
	defer span.End()

	userS := ctrl.session.FromContext(c)
	if userS.UserID == "" || userS.Token == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	ss, err := ctrl.sessionRepo.ListByUserID(ctx, userS.UserID)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	if err = ctrl.sessionRepo.DeleteAll(ctx, userS.UserID); err != nil {
		ginx.ResError(c, err)
		return
	}

	for _, s := range *ss {
		ctrl.wsClient.NotifyTokenExpirationAndDisconnect(ctx, s.Token)
	}

	ginx.ResNoContent(c)
}

// ChangeUserPasswordRequest represents the payload for changing a user's password.
// @Description Request payload for changing a user's password.
type ChangeUserPasswordRequest struct {
	// User's current password
	CurrentPassword string `json:"currentPassword" example:"12345678" extensions:"x-order=0"`
	// User's new password must be greater than eight characters
	NewPassword string `json:"newPassword" example:"87654321" extensions:"x-order=1"`
} // @name ChangeUserPasswordRequest

// ChangeUserPassword updates the user's password.
//
// @Summary Change user's password.
// @Description Changes the user's password by verifying the old password,
// @Description validating the new password, and updating it in the database.
// @Accept json
// @Produce json
// @Security Bearer
// @Tags User
// @Param data body ChangeUserPasswordRequest true "Change Password Information"
// @Success 200
// @Failure 401 {object} errors.Error "you are not authorized to access this resource"
// @Router /users/password [patch]
func (ctrl *Controller) ChangeUserPassword(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ChangeUserPassword"))
	defer span.End()

	var req ChangeUserPasswordRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	minPassLen := 8
	if len(req.NewPassword) < minPassLen {
		ginx.ResError(c, InvalidPasswordLength)
		return
	}

	user, err := ctrl.userRepo.Get(ctx, userID)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	if err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.CurrentPassword)); err != nil {
		ginx.ResError(c, InvalidPassword)
		return
	}

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		ginx.ResError(c, errors.Wrap(err, "hash new password"))
		return
	}

	if err = ctrl.userRepo.UpdatePassword(ctx, userID, string(hashedPassword)); err != nil {
		ginx.ResError(c, errors.Wrap(err, "error updating password"))
		return
	}

	ginx.ResOK(c)
}
