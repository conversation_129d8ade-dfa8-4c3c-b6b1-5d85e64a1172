package exchange

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	_ "github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/services/exchangeservice"
)

var _spanName = "ExchangeController/%s"

const _ScopeName = "ExchangeController"

type Controller struct {
	exSvc  exchangeservice.IExchangeService
	tracer trace.Tracer
}

func New(exSvc exchangeservice.IExchangeService) *Controller {
	return &Controller{
		exSvc:  exSvc,
		tracer: otel.Tracer(_ScopeName),
	}
}

// Exchange represents the exchange model
// @Description Model for exchange
type Exchange struct {
	// Exchange identifier
	ExchangeID string `json:"exchangeId" example:"binance" extensions:"x-order=0"`
	// Pretty name of the exchange
	Name string `json:"name" example:"Binance" extensions:"x-order=1"`
	// Time when the parameter was created
	CreatedAt time.Time `json:"createdAt" example:"2024-02-10T12:30:45Z" format:"date-time" extensions:"x-order=2"`
} //@name Exchange

// GetExchangeRequest for fetching an exchange by ID
// @Description Request payload for fetching an exchange.
type GetExchangeRequest struct {
	// Exchange id to be fetched
	ExchangeID string `uri:"exchangeId" example:"binance" extensions:"x-order=0"`
} //@name GetExchangeRequest

// GetExchange fetches an exchange by its ID
//
// @Summary Fetch an exchange
// @Description Fetch an exchange by providing its exchangeID
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Exchange
// @Param data path GetExchangeRequest true "Exchange ID"
// @Success 200 {object} ginx.Response[Exchange]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /exchanges/{exchangeId} [get]
func (ctrl *Controller) GetExchange(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "GetExchange"))
	defer span.End()

	var req GetExchangeRequest
	if err := ginx.ParseUri(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	ex, err := ctrl.exSvc.Get(ctx, req.ExchangeID)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResSuccess(c, &Exchange{
		ExchangeID: ex.ExchangeID,
		Name:       ex.Name,
		CreatedAt:  ex.CreatedAt,
	})
}

// ListExchangesRequest for listing exchanges
// @Description Request payload for listing exchanges
type ListExchangesRequest struct {
	ginx.PaginationParam
	// Field, by which the results should be ordered
	OrderBy string `form:"orderBy" validate:"optional" enums:"exchange_id,name,created_at" default:"created_at" example:"created_at" extensions:"x-order=31"`
	// Exchange id to filter the results
	ExchangeID string `form:"exchangeId" validate:"optional" example:"binance" extensions:"x-order=0"`
	// Name to filter the results
	Name string `form:"name" validate:"optional" example:"Binance" extensions:"x-order=1"`
} //@name ListExchangesRequest

// ListExchanges lists exchanges with pagination
//
// @Summary List exchanges.
// @Description List exchanges with optional filters and pagination
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Exchange
// @Param data query ListExchangesRequest true "Query parameters for listing exchanges"
// @Success 200 {object} ginx.Response[[]Exchange]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /exchanges [get]
func (ctrl *Controller) ListExchanges(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ListExchanges"))
	defer span.End()

	var req ListExchangesRequest
	if err := ginx.ParseQuery(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	list, err := ctrl.exSvc.List(ctx, exchangeservice.ListExchangeRequest{
		PaginationParam: req.PaginationParam,
		OrderBy:         req.OrderBy,
		ExchangeID:      req.ExchangeID,
		Name:            req.Name,
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	var res = make([]Exchange, len(list.Exchanges))
	for i, ex := range list.Exchanges {
		res[i] = Exchange{
			ExchangeID: ex.ExchangeID,
			Name:       ex.Name,
			CreatedAt:  ex.CreatedAt,
		}
	}

	ginx.ResPage(c, res, &ginx.PaginationResult{
		Total:  list.Total,
		Offset: req.Limit,
		Limit:  req.Offset,
	})
}
