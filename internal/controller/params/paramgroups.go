package params

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/services/paramsservice"
)

// ParamGroup represents the params group model.
type ParamGroup struct {
	ID              string    `json:"id" example:"123e4567-e89b-12d3-a456-426614174001" extensions:"x-order=0"`
	Name            string    `json:"name" example:"highRiskGroup" extensions:"x-order=1"`
	Priority        int32     `json:"priority" example:"1" extensions:"x-order=1"`
	CreatedByUserID string    `json:"createdByUserID" example:"123e4567-e89b-12d3-a456-426614174001" extensions:"x-order=2"`
	CreatedAt       time.Time `json:"createdAt" example:"2024-02-10T12:30:45Z" extensions:"x-order=3"`
} //@name ParamGroup

// BotParamGroup represents the bot params group model.
type BotParamGroup struct {
	BotID           string    `json:"botId" example:"123e4567-e89b-12d3-a456-426614174001" extensions:"x-order=0"`
	ParamGroupID    string    `json:"groupId" example:"123e4567-e89b-12d3-a456-426614174002" extensions:"x-order=1"`
	CreatedByUserID string    `json:"createdByUserID" example:"123e4567-e89b-12d3-a456-426614174001" extensions:"x-order=2"`
	CreatedAt       time.Time `json:"createdAt" example:"2024-02-10T12:30:45Z" extensions:"x-order=3"`
} //@name BotParamGroup

// CreateParamGroupRequest for creating a param group
// @Description Request payload for creating a param group
type CreateParamGroupRequest struct {
	// Name of the param group
	Name string `json:"name" example:"highRiskStrategy" extensions:"x-order=0"`
	// Priority is used to define the precedence order when there two groups with same key value
	Priority int32 `json:"priority" example:"1" extensions:"x-order=1"`
} //@name CreateParamGroupRequest

// CreateParamGroup creates a new param group
//
// @Summary Create a param group
// @Description Creates a param group by providing a name
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Param Groups
// @Param data body CreateParamGroupRequest true "Param Group Name"
// @Success 201 {object} ginx.Response[ParamGroup]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /groups [post]
func (ctrl *Controller) CreateParamGroup(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "CreateParamGroup"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
	}

	var req CreateParamGroupRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
	}

	newParamGroup, err := ctrl.paramsSvc.CreateParamGroup(ctx, paramsservice.CreateParamGroupRequest{
		Name: req.Name,

		UserID: userID,
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResCreated(c, ParamGroup{
		ID:              newParamGroup.ID,
		Name:            newParamGroup.Name,
		Priority:        newParamGroup.Priority,
		CreatedByUserID: newParamGroup.CreatedByUserID,
		CreatedAt:       newParamGroup.CreatedAt,
	})
}

// ListParamGroupsRequest for listing param groups
// @Description Request payload for listing param groups with filters and pagination
type ListParamGroupsRequest struct {
	ginx.PaginationParam
	// Specifies the field by which the results should be ordered.
	OrderBy string `form:"orderBy" validate:"optional" enums:"name,created_by_user_id,created_at" default:"created_at" example:"created_at" extensions:"x-order=31"`
	// Filter param groups by Name
	Name string `form:"name" validate:"optional" example:"highRiskGroup" extensions:"x-order=0"`
	// Filter by user that created this group
	CreatedByUserID string `form:"createdByUserId" validate:"optional" example:"e7cf3a21-9c6b-4a01-af13-2c8e8f91a6bb" extensions:"x-order=1"`
} //@name ListParamGroupsRequest

// ListParamGroups list all param groups
//
// @Summary List param groups
// @Description List param groups with optional filters and pagination
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Param Groups
// @Param data query ListParamGroupsRequest false "Filters and pagination"
// @Success 200 {object} ginx.Response[[]ParamGroup]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /groups [get]
func (ctrl *Controller) ListParamGroups(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ListParamGroups"))
	defer span.End()

	var req ListParamGroupsRequest
	if err := ginx.ParseQuery(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	result, err := ctrl.paramsSvc.ListParamGroups(ctx, paramsservice.ListParamGroupsRequest{
		PaginationParam: req.PaginationParam,
		OrderBy:         req.OrderBy,
		Name:            req.Name,
		CreatedByUserID: req.CreatedByUserID,
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	var res = make([]ParamGroup, len(result.ParamGroups))
	for i, group := range result.ParamGroups {
		res[i] = ParamGroup{
			ID:              group.ID,
			Name:            group.Name,
			Priority:        group.Priority,
			CreatedByUserID: group.CreatedByUserID,
			CreatedAt:       group.CreatedAt,
		}
	}

	ginx.ResPage(c, res, &ginx.PaginationResult{
		Total:  result.Total,
		Limit:  req.Limit,
		Offset: req.Offset,
	})
}

// DeleteParamGroupRequest for deleting a param group
// @Description Request payload for deleting a param group
type DeleteParamGroupRequest struct {
	// ID of the param group to be deleted
	GroupID string `uri:"groupId" example:"123e4567-e89b-12d3-a456-426614174001" extensions:"x-order=0"`
} //@name DeleteParamGroupRequest

// DeleteParamGroup delete a param group
//
// @Summary Delete a param group
// @Description Delete a param group by providing its ID
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Param Groups
// @Param data path DeleteParamGroupRequest true "param Group ID"
// @Success 204 "No Content"
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /groups/{groupId} [delete]
func (ctrl *Controller) DeleteParamGroup(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "DeleteParamGroup"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
	}

	var req DeleteParamGroupRequest
	if err := ginx.ParseUri(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	err := ctrl.paramsSvc.DeleteParamGroup(ctx, paramsservice.DeleteParamGroupRequest{
		GroupID: req.GroupID,
		UserID:  userID,
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResNoContent(c)
}

// CreateBotParamGroupRelationsRequest for creating bot-param group relations
// @Description Request payload for creating relations between bots and param groups
type CreateBotParamGroupRelationsRequest struct {
	// ID of the param group
	ParamGroupID string `uri:"groupId" example:"123e4567-e89b-12d3-a456-426614174002" extensions:"x-order=1"`
	// ID of the bot to create the relation with group
	BotID string `uri:"botId" example:"123e4567-e89b-12d3-a456-426614174001" extensions:"x-order=0"`
} //@name CreateBotParamGroupRelationsRequest

// CreateBotParamGroupRelation create relation between a bot and param group
//
// @Summary Create bot param group relation
// @Description Creates relation between bot and param groups by providing bot and param group IDs
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Param Groups
// @Param data path CreateBotParamGroupRelationsRequest true "Bot Param Group Relation"
// @Success 201 {object} ginx.Response[[]BotParamGroup]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /params/groups/{groupId}/bots/{botId} [post]
func (ctrl *Controller) CreateBotParamGroupRelation(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "CreateBotParamGroupRelation"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
	}

	var req CreateBotParamGroupRelationsRequest
	if err := ginx.ParseUri(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	res, err := ctrl.paramsSvc.CreateBotParamGroupRelation(ctx, paramsservice.CreateBotParamGroupRelationRequest{
		UserID:  userID,
		BotID:   req.BotID,
		GroupID: req.ParamGroupID,
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResCreated(c, BotParamGroup{
		BotID:           res.BotID,
		ParamGroupID:    res.GroupID,
		CreatedByUserID: res.CreatedByUserID,
		CreatedAt:       res.CreatedAt,
	})
}

// ListBotParamGroupRelationsRequest for listing bot-param group relations
// @Description Request payload for listing bot param group relations with filters and pagination
type ListBotParamGroupRelationsRequest struct {
	ginx.PaginationParam
	// Specifies the field by which the results should be ordered
	OrderBy string `form:"orderBy" validate:"optional" enums:"param_group_id,created_by_user_id,created_at" default:"created_at" example:"created_at" extensions:"x-order=31"`
	// Filter relations by the user who created them
	CreatedByUserID string `form:"createdByUserId" validate:"optional" example:"123e4567-e89b-12d3-a456-426614174002" extensions:"x-order=0"`
	// Filter relations by Bot ID
	BotID string `form:"botId" validate:"optional" example:"123e4567-e89b-12d3-a456-426614174001" extensions:"x-order=1"`
	// Filter relations by param Group ID
	ParamGroupID string `form:"groupId" validate:"optional" example:"123e4567-e89b-12d3-a456-426614174002" extensions:"x-order=2"`
} //@name ListBotParamGroupRelationsRequest

// ListBotParamGroupRelations list all bot param group relations
//
// @Summary List bot param group relations
// @Description List bot param group relations with optional filters and pagination
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Param Groups
// @Param data query ListBotParamGroupRelationsRequest false "Filters and pagination"
// @Success 200 {object} ginx.Response[[]BotParamGroup]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /params/groups/bots [get]
func (ctrl *Controller) ListBotParamGroupRelations(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ListBotParamGroupRelations"))
	defer span.End()

	var req ListBotParamGroupRelationsRequest
	if err := ginx.ParseQuery(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	result, err := ctrl.paramsSvc.ListBotParamGroupRelations(ctx, paramsservice.ListBotParamGroupRelationsRequest{
		PaginationParam: req.PaginationParam,
		OrderBy:         req.OrderBy,
		BotID:           req.BotID,
		GroupID:         req.ParamGroupID,
		CreatedByUserID: req.CreatedByUserID,
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	var res = make([]BotParamGroup, len(result.BotParamGroups))
	for i, group := range result.BotParamGroups {
		res[i] = BotParamGroup{
			BotID:           group.BotID,
			ParamGroupID:    group.GroupID,
			CreatedByUserID: group.CreatedByUserID,
			CreatedAt:       group.CreatedAt,
		}
	}

	ginx.ResPage(c, res, &ginx.PaginationResult{
		Total:  result.Total,
		Limit:  req.Limit,
		Offset: req.Offset,
	})
}

// RemoveBotParamGroupRelationsRequest for deleting bot-param group relations
// @Description Request payload for deleting bot-param group relations
type RemoveBotParamGroupRelationsRequest struct {
	// ID of the param group
	ParamGroupID string `uri:"groupId" example:"123e4567-e89b-12d3-a456-426614174002" extensions:"x-order=1"`
	// ID of the bot to be removed from a group
	BotID string `uri:"botId" example:"123e4567-e89b-12d3-a456-426614174001" extensions:"x-order=0"`
} //@name DeleteBotParamGroupRelationsRequest

// RemoveBotParamGroupRelation delete a bot param group relation
//
// @Summary Delete bot param group relations
// @Description Delete bot param group relations specified in the request payload
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Param Groups
// @Param data path RemoveBotParamGroupRelationsRequest true "List of bot param group relations to be deleted"
// @Success 204 "No Content"
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /params/groups/{groupId}/bots/{botId} [delete]
func (ctrl *Controller) RemoveBotParamGroupRelation(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "RemoveBotParamGroupRelation"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
	}

	var req RemoveBotParamGroupRelationsRequest
	if err := ginx.ParseUri(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	if err := ctrl.paramsSvc.RemoveBotParamGroupRelation(ctx, paramsservice.RemoveBotParamGroupRelationRequest{
		BotID:           req.BotID,
		GroupID:         req.ParamGroupID,
		DeletedByUserID: userID,
	}); err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResNoContent(c)
}
