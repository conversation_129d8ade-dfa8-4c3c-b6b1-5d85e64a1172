package params

import (
	"github.com/herenow/atomic-bm/cmd/app/dependencies"

	"github.com/herenow/atomic-bm/internal/app"
)

func Register(app *app.App, dp *dependencies.Container) {
	paramsHandler := New(dp.ParamsService, app.Session(), app.Config())

	g := app.APIGroup().Group("/params")
	{
		g.POST("", paramsHandler.CreateParams)
		g.GET("", paramsHandler.ListParams)
		g.GET("/:botId/activeParams", paramsHandler.GetActiveParams)
		g.POST("/bulkDelete", paramsHandler.DeleteParams)

		botGroup := g.Group("/groups")
		{
			botGroup.POST("", paramsHandler.CreateParamGroup)
			botGroup.GET("", paramsHandler.ListParamGroups)
			botGroup.DELETE("/:groupId", paramsHandler.DeleteParamGroup)

			botGroup.GET("/bots", paramsHandler.ListBotParamGroupRelations)
			botGroup.POST("/:groupId/bots/:botId", paramsHandler.CreateBotParamGroupRelation)
			botGroup.DELETE("/:groupId/bots/:botId", paramsHandler.RemoveBotParamGroupRelation)

		}
	}
}
