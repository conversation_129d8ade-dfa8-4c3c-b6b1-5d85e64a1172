package params

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/app"
	"github.com/herenow/atomic-bm/internal/auth/session"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/services/paramsservice"
)

var _spanName = "ParamsController/%s"

const _ScopeName = "ParamsController"

type Controller struct {
	paramsSvc paramsservice.IParamsService
	tracer    trace.Tracer
	session   *session.Manager
	cfg       *app.Config
}

func New(paramSvc paramsservice.IParamsService, session *session.Manager, cfg *app.Config) *Controller {
	return &Controller{
		paramsSvc: paramSvc,
		tracer:    otel.Tracer(_ScopeName),
		session:   session,
		cfg:       cfg,
	}
}

// Param represents the parameters model.
// @Description Model for parameters.
type Param struct {
	// Scope ID associated with the parameter
	ScopeID string `json:"scopeId" example:"123e4567-e89b-12d3-a456-************" extensions:"x-order=0"`
	// Scope of the parameter (bot, account, gateway)
	Scope string `json:"scope" example:"bot" extensions:"x-order=1"`
	// Key of the parameter
	Key string `json:"key" example:"param_key" extensions:"x-order=2"`
	// Value of the parameter
	Value string `json:"value" example:"param_value" extensions:"x-order=3"`
	// Indicates whether the value is encrypted
	IsEncrypted bool `json:"-" example:"false" extensions:"x-order=4"`
	// Secret key version associated with the parameter
	SecretKeyVersion int `json:"-" example:"1" extensions:"x-order=5"`
	// Version of the parameter
	Version int `json:"version" example:"1" extensions:"x-order=6"`
	// ID of the user who created the parameter
	CreatedById string `json:"createdById" example:"user123" extensions:"x-order=7"`
	// Time when the parameter was created
	CreatedAt time.Time `json:"createdAt" example:"2024-02-10T12:30:45Z" format:"date-time" extensions:"x-order=8"`
} //@name Param

// ParamRequest represents the request payload for creating a parameter.
// @Description Request payload for creating a parameter.
type ParamRequest struct {
	// Key of the parameter
	Key string `json:"key" example:"paramKey" extensions:"x-order=0"`
	// Value of the parameter
	Value string `json:"value" example:"paramValue" extensions:"x-order=1"`
} //@name ParamRequest

// ScopeParamsPayload defines the structure for parameters within a specific scope.
// @Description Payload containing the scope ID and parameters for a single scope.
type ScopeParamsPayload struct {
	// Scope ID for the parameters
	ScopeID string `json:"scopeId" example:"cc015665-8f5c-4429-b48f-4b3743bb391e"`
	// Array of parameter key-value pairs
	Params []ParamRequest `json:"params"`
} // @name ScopeParamsPayload

// CreateParamsRequest for creating parameters across multiple scopes.
// @Description Request payload for creating parameters, organized by scope.
type CreateParamsRequest struct {
	// Parameters for the 'gateway' scope
	Gateway *ScopeParamsPayload `json:"gateway,omitempty"`
	// Parameters for the 'account' scope
	Account *ScopeParamsPayload `json:"account,omitempty"`
	// Parameters for the 'bot' scope
	Bot *ScopeParamsPayload `json:"bot,omitempty"`
	// Parameters for the 'param_group' scope
	ParamGroup *ScopeParamsPayload `json:"param_group,omitempty"`
} // @name CreateParamsRequest

// CreateParams create parameters for one or more scopes.
//
// @Summary      Create parameters.
// @Description  Creates parameters for one or more scopes (gateway, account, bot, etc.) in a single request.
// @Description  When the Scope is 'gateway' or 'account', the scopeId must be a valid accountId.
// @Description  When the Scope is 'bot', the scopeId must be a valid botId.
// @Description  When the Scope is 'param_group', the scopeId must be a valid paramGroupId.
// @Security     Bearer
// @Accept       json
// @Produce      json
// @Tags         Params
// @Param        data body CreateParamsRequest true "JSON object with scopes (gateway, account) containing scopeId and params"
// @Success      201 {object} ginx.Response[[]Param]
// @Failure      403 {object} errors.Error "you are not authorized to access this resource"
// @Router       /params [post]
func (ctrl *Controller) CreateParams(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "CreateParams"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	var req CreateParamsRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	var allCreatedParams []Param

	// A map to iterate over the scopes present in the request
	scopesToProcess := map[string]*ScopeParamsPayload{
		"gateway":     req.Gateway,
		"account":     req.Account,
		"bot":         req.Bot,
		"param_group": req.ParamGroup,
	}

	for scope, payload := range scopesToProcess {
		// Skip if the scope is not included in the request
		if payload == nil {
			continue
		}

		// Convert controller request to service request for the current scope
		svcParams := make([]paramsservice.ParamRequest, len(payload.Params))
		for i, p := range payload.Params {
			svcParams[i] = paramsservice.ParamRequest{Key: p.Key, Value: p.Value}
		}

		svcReq := paramsservice.CreateParamsRequest{
			UserID:  userID,
			ScopeID: payload.ScopeID,
			Scope:   scope,
			Params:  svcParams,
		}

		// Call the service to create params for the current scope
		result, err := ctrl.paramsSvc.Create(ctx, svcReq)
		if err != nil {
			// Stop and return on the first error encountered
			ginx.ResError(c, err)
			return
		}

		// Convert service response to controller response and append to the final list
		for _, p := range result.Params {
			allCreatedParams = append(allCreatedParams, Param{
				ScopeID:     p.ScopeID,
				Scope:       p.Scope,
				Key:         p.Key,
				Value:       p.Value,
				Version:     p.Version,
				CreatedById: p.CreatedById,
				CreatedAt:   p.CreatedAt,
			})
		}
	}

	ginx.ResCreated(c, allCreatedParams)
}

// ListParamsRequest for listing parameters
// @Description Request payload for listing parameters.
type ListParamsRequest struct {
	ginx.PaginationParam
	// Specifies the field by which the results should be ordered.
	OrderBy string `form:"orderBy" validate:"optional" enums:"scope,key,created_by_id,created_at" default:"created_at" example:"created_at" extensions:"x-order=31"`

	// Array of scope values for filtering
	Scope []string `form:"scope" validate:"optional" example:"bot,account,gateway,param_group" extensions:"x-order=0"`
	// Array of scope IDs for filtering
	ScopeID []string `form:"scopeId" validate:"optional" example:"123e4567-e89b-12d3-a456-************" extensions:"x-order=1"`
} //@name ListParamsRequest

// ListParams retrieve the latest params for a specific scope or scopeId.
//
// @Summary List parameters.
// @Description List parameters with optional filtering and ordering based on scope or scopeId.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Params
// @Param data query ListParamsRequest true "Query parameters for listing parameters"
// @Success 200 {object} ginx.PageResponse[[]Param]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /params [get]
func (ctrl *Controller) ListParams(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ListParams"))
	defer span.End()

	var req ListParamsRequest
	if err := ginx.ParseQuery(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	res, err := ctrl.paramsSvc.List(ctx, paramsservice.ListParamsRequest{
		PaginationParam: req.PaginationParam,
		OrderBy:         req.OrderBy,
		Scope:           req.Scope,
		ScopeID:         req.ScopeID,
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	params := make([]Param, len(res.Params))
	for i, p := range res.Params {
		params[i] = Param{
			ScopeID:     p.ScopeID,
			Scope:       p.Scope,
			Key:         p.Key,
			Value:       p.Value,
			Version:     p.Version,
			CreatedById: p.CreatedById,
			CreatedAt:   p.CreatedAt,
		}
	}

	ginx.ResPage(c, res.Params, &ginx.PaginationResult{
		Total:  res.Total,
		Limit:  req.Limit,
		Offset: req.Offset,
	})
}

// GetActiveParams retrieve the active params for a bot.
//
// @Summary Get active parameters.
// @Description Gets the active parameters based on botId.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Params
// @Param botId path string true "Bot ID for retrieval" format(uuid) example(1dfc543a2-a5d6-445e-be53-f2b07d109823)
// @Success 200 {object} ginx.PageResponse[[]Param]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /params/{botId}/activeParams [get]
func (ctrl *Controller) GetActiveParams(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "GetActiveParams"))
	defer span.End()

	botID := c.Param("botId")
	if botID == "" {
		ginx.ResError(c, errors.BadRequest(codes.Parameters, "botId is required"))
		return
	}

	res, err := ctrl.paramsSvc.GetActiveParams(ctx, paramsservice.GetActiveParamsRequest{BotID: botID})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	params := make([]Param, len(res.Params))
	for i, p := range res.Params {
		params[i] = Param{
			ScopeID:     p.ScopeID,
			Scope:       p.Scope,
			Key:         p.Key,
			Value:       p.Value,
			Version:     p.Version,
			CreatedById: p.CreatedById,
			CreatedAt:   p.CreatedAt,
		}
	}

	ginx.ResSuccess(c, params)
}

// DeleteParamsRequest for deleting parameters.
// @Description Request payload for deleting parameters for a specific scope.
type DeleteParamsRequest struct {
	// Scope ID for the parameters
	ScopeID string `json:"scopeId" example:"123e4567-e89b-12d3-a456-************" extensions:"x-order=0"`
	// Scope of the parameters (bot, account, gateway)
	Scope string `json:"scope" example:"bot" extensions:"x-order=1"`
	// Array of parameter requests
	Keys []string `json:"keys" example:"apikey,minSize,minSpread" extensions:"x-order=2"`
} //@name DeleteParamsRequest

// ConvertDeleteRequestToService converts controller DeleteParamsRequest to service struct
func (r *DeleteParamsRequest) ConvertDeleteRequestToService(userID string) paramsservice.DeleteParamsRequest {
	svcReq := paramsservice.DeleteParamsRequest{
		UserID:  userID,
		ScopeID: r.ScopeID,
		Scope:   r.Scope,
		Keys:    r.Keys,
	}
	return svcReq
}

// DeleteParams deletes a list of parameters by their keys and scope with the specified scopeID.
//
// @Summary Delete parameters.
// @Description Delete parameters by their keys and scope with the specified scopeID.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Params
// @Param data body DeleteParamsRequest true "Scope ID, Scope, and Keys"
// @Success 204 "No Content"
// @Failure 403 {object} errors.Error "You are not authorized to access this resource"
// @Router /params/bulkDelete [post]
func (ctrl *Controller) DeleteParams(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "DeleteParams"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	var req DeleteParamsRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	if err := ctrl.paramsSvc.Delete(ctx, req.ConvertDeleteRequestToService(userID)); err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResNoContent(c)
}
