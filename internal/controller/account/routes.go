package account

import (
	"github.com/herenow/atomic-bm/cmd/app/dependencies"
	"github.com/herenow/atomic-bm/internal/app"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/services/accountservice"
)

var _spanName = "AccountController/%s"

const _ScopeName = "AccountController"

type Controller struct {
	tracer trace.Tracer

	s accountservice.IAccountService
}

func New(accSvc accountservice.IAccountService) *Controller {
	return &Controller{
		s:      accSvc,
		tracer: otel.Tracer(_ScopeName),
	}
}

func Register(app *app.App, dp *dependencies.Container) {
	accountHandler := New(dp.AccountService)
	{
		g := app.APIGroup().Group("/accounts")
		g.POST("", accountHandler.CreateAccount)
		g.GET("/:accountId", accountHandler.GetAccount)
		g.GET("", accountHandler.ListAccounts)
		g.DELETE("/:accountId", accountHandler.DeleteAccount)
	}
}
