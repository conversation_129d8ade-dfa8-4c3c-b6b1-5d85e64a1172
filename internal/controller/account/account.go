package account

import (
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/pkg/validate"
	accsvc "github.com/herenow/atomic-bm/internal/services/accountservice"
)

// Account manages bots
// @Description Accounts is responsible for managing exchanges
type Account struct {
	// Account UUID
	ID string `json:"id" example:"8dfc86a3-a9f7-445e-be53-f2b07d109829" extensions:"x-order=0"`
	// Exchange id that is managed by the account
	ExchangeID string `json:"exchangeId" example:"Binance" extensions:"x-order=1"`
	// RegionID information about which region this account operates
	RegionID string `json:"regionId" example:"aws-tokyo" extensions:"x-order=2"`
	// Tag used to identify a unique account with an exchange
	Tag string `json:"tag" example:"MM@BTC_BRL@Binance" extensions:"x-order=3"`
	// When an account was created
	CreatedAt time.Time `json:"createdAt" example:"2023-08-19T14:11:07.84483Z" extensions:"x-order=4"`
	// Last time when the account was modified
	UpdatedAt time.Time `json:"updatedAt" example:"2024-02-04T19:41:44.008444328Z" extensions:"x-order=5"`
	// Time when the account was deleted
	DeletedAt *time.Time `json:"deletedAt,omitempty" example:"2024-17-03T10:11:24.758443321Z" validate:"optional" extensions:"x-order=6,x-omitempty"`
} // @name Account

// CreateAccountRequest for creating a bot
// @Description Request payload for creating an account.
type CreateAccountRequest struct {
	// Exchange id that is managed by the account
	ExchangeID string `json:"exchangeId" example:"Binance" extensions:"x-order=0"`
	// RegionID region where account operates
	RegionID string `json:"regionId" example:"us-east-2" extensions:"x-order=1"`
	// Tag used to identify a unique account with an exchange
	Tag string `json:"tag" example:"MM@BTC_BRL@Binance" extensions:"x-order=2"`
} // @name CreateAccountRequest

// CreateAccount create a new account.
//
// @Summary Create an account.
// @Description Create an account by giving exchangeID and tag.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Account
// @Param data body CreateAccountRequest true "Exchange and Tag"
// @Success 201 {object} ginx.Response[Account]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /accounts [post]
func (ctrl *Controller) CreateAccount(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "Create"))
	defer span.End()

	var req CreateAccountRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	acc, err := ctrl.s.Create(ctx, &accsvc.CreateAccountRequest{
		ExchangeID: req.ExchangeID,
		RegionID:   req.RegionID,
		Tag:        req.Tag,
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResCreated(c, Account{
		ID:         acc.ID,
		ExchangeID: acc.ExchangeID,
		Tag:        acc.Tag,
		RegionID:   acc.RegionID,
		CreatedAt:  acc.CreatedAt,
		UpdatedAt:  acc.UpdatedAt,
	})
}

// GetAccount retrieve an account by its ID.
//
// @Summary Get an account.
// @Description Fetch an account by its ID.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Account
// @Param accountId path string true "Account ID" format(uuid) example(8dfc86a3-a9f7-445e-be53-f2b07d109829)
// @Success 200 {object} ginx.Response[Account] "Account"
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /accounts/{accountId} [get]
func (ctrl *Controller) GetAccount(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "GetAccount"))
	defer span.End()

	accountID := c.Param("accountId")
	if accountID == "" {
		ginx.ResError(c, errors.BadRequest(codes.Parameters, "accountId is required"))
		return
	}

	if err := validate.Validator.ValidateVar(accountID, "uuid"); err != nil {
		ginx.ResError(c, err)
		return
	}

	acc, err := ctrl.s.Get(ctx, &accsvc.GetAccountRequest{AccountID: accountID})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResSuccess(c, Account{
		ID:         acc.ID,
		ExchangeID: acc.ExchangeID,
		RegionID:   acc.RegionID,
		Tag:        acc.Tag,
		CreatedAt:  acc.CreatedAt,
		UpdatedAt:  acc.UpdatedAt,
		DeletedAt:  acc.DeletedAt,
	})
}

// ListAccountRequest options available for list accounts
type ListAccountRequest struct {
	ginx.PaginationParam
	// Indicates whether deleted items should be included.
	IsDeleted bool `form:"isDeleted" validate:"optional" default:"false" example:"true" extensions:"x-order=30"`
	// Specifies the field by which the results should be ordered.
	OrderBy string `form:"orderBy" validate:"optional" enums:"exchange_id,tag,created_at,updated_at,deleted_at" default:"created_at" example:"created_at" extensions:"x-order=31"`

	// Specifies the identifier of the exchange (e.g., "Binance").
	ExchangeID string `form:"exchangeId" validate:"optional" example:"Binance"  extensions:"x-order=0"`
	// RegionID region where account operates
	RegionID string `json:"regionId" validate:"optional" example:"us-east-2" extensions:"x-order=1"`
	// Filter accounts based on a specific tag (e.g., "atomic-mm").
	Tag string `form:"tag" validate:"optional" example:"atomic-mm" extensions:"x-order=2"`
} // @name ListAccountRequest

func (c *ListAccountRequest) ToServiceRequest() *accsvc.ListAccountsRequest {
	c.PaginationParam.OrderDirection = strings.ToUpper(c.PaginationParam.OrderDirection)
	return &accsvc.ListAccountsRequest{
		PaginationParam: c.PaginationParam,
		IsDeleted:       c.IsDeleted,
		OrderBy:         c.OrderBy,
		ExchangeID:      c.ExchangeID,
		RegionID:        c.RegionID,
		Tag:             c.Tag,
	}
}

// ListAccounts list all accounts.
//
// @Summary Get a list of accounts.
// @Description List accounts.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Account
// @Param params query ListAccountRequest true "Exchange ID"
// @Success 200 {object} ginx.PageResponse[[]Account] "Accounts"
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /accounts [get]
func (ctrl *Controller) ListAccounts(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ListAccounts"))
	defer span.End()

	var params ListAccountRequest
	if err := ginx.ParseQuery(c, &params); err != nil {
		ginx.ResError(c, err)
		return
	}

	accs, err := ctrl.s.List(ctx, params.ToServiceRequest())
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	var res = make([]Account, len(accs.Accounts))
	for i, acc := range accs.Accounts {
		res[i] = Account{
			ID:         acc.ID,
			ExchangeID: acc.ExchangeID,
			RegionID:   acc.RegionID,
			Tag:        acc.Tag,
			CreatedAt:  acc.CreatedAt,
			UpdatedAt:  acc.UpdatedAt,
			DeletedAt:  acc.DeletedAt,
		}
	}

	ginx.ResPage(c, res, &ginx.PaginationResult{
		Total:  accs.Total,
		Limit:  params.Limit,
		Offset: params.Offset,
	})
}

// DeleteAccount delete an account by ID.
//
// @Summary Delete an account.
// @Description Delete an account by its ID.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Account
// @Param accountId path string true "Account ID" format(uuid) example(8dfc86a3-a9f7-445e-be53-f2b07d109829)
// @Success 204
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /accounts/{accountId} [delete]
func (ctrl *Controller) DeleteAccount(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "DeleteAccount"))
	defer span.End()

	accID := c.Param("accountId")
	if accID == "" {
		ginx.ResError(c, errors.BadRequest(codes.Parameters, "accountId is required"))
		return
	}

	if err := validate.Validator.ValidateVar(accID, "uuid"); err != nil {
		ginx.ResError(c, err)
		return
	}

	if err := ctrl.s.Delete(ctx, &accsvc.DeleteAccountRequest{AccountID: accID}); err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResNoContent(c)
}
