package bot

import (
	"github.com/herenow/atomic-bm/cmd/app/dependencies"
	"github.com/herenow/atomic-bm/internal/app"
)

func Register(app *app.App, dp *dependencies.Container) {
	botHandler := New(dp.BotService)

	g := app.APIGroup().Group("/bots")
	{
		g.POST("", botHandler.CreateBot)
		g.POST("/:botId/start", botHandler.StartBot)
		g.POST("/:botId/stop", botHandler.StopBot)
		g.GET("", botHandler.ListBots)
		//g.PATCH("/:botId", botHandler.UpdateBot)
		g.GET("/:botId", botHandler.GetBot)
		//g.DELETE("/:botId", botHandler.DeleteBot)

		g.GET("/:botId/state", botHandler.GetBotState)
		g.GET("/state", botHandler.ListBotsState)

	}
}
