package bot

import (
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/herenow/atomic-bm/internal/services/botsservice/types"
	"github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/services/botsservice"
)

var _spanName = "BotController/%s"

const _ScopeName = "BotController"

type Controller struct {
	botSvc botsservice.IBotsService
	tracer trace.Tracer
}

func New(botSvc botsservice.IBotsService) *Controller {
	return &Controller{
		botSvc: botSvc,
		tracer: otel.Tracer(_ScopeName),
	}
}

// Bot represents a bot entity.
//
//	@Description	Bot entity with details such as ID, account ID, symbol, region, tag, status, and dates.
type Bot struct {
	// Bot ID
	ID string `json:"id" example:"1dfc543a2-a5d6-445e-be53-f2b07d109823" format:"uuid" extensions:"x-order=0"`
	// Account ID associated with the bot
	AccountID string `json:"accountId" example:"8dfc86a3-a9f7-445e-be53-f2b07d109829" format:"uuid" extensions:"x-order=1"`
	// Symbol associated with the bot
	Symbol string `json:"symbol" example:"BTCUSD" extensions:"x-order=2"`
	// Tag used to identify the bot
	Tag string `json:"tag" example:"atomic-mm-mercado-bitcoin" extensions:"x-order=4"`
	// Status of the bot
	Status string `json:"status" example:"running" extensions:"x-order=5"`
	// DesiredStatus used to identify the desired state of the bot
	DesiredStatus string `json:"desiredStatus" example:"running" extensions:"x-order=6"`
	// Date when the bot was created
	CreatedAt time.Time `json:"createdAt" example:"2023-01-15T12:30:45Z" format:"date-time" extensions:"x-order=7"`
	// Date when the bot was last updated
	UpdatedAt time.Time `json:"updatedAt" example:"2023-02-20T15:45:30Z" format:"date-time" extensions:"x-order=8"`
	// Date when the bot was deleted (if applicable)
	DeletedAt *time.Time `json:"deletedAt,omitempty" example:"2023-03-25T09:20:15Z" format:"date-time" validate:"optional" extensions:"x-order=9,x-omitempty"`
} //@name Bot

func FromBotService(b *botsservice.Bot) *Bot {
	return &Bot{
		ID:            b.ID,
		AccountID:     b.AccountID,
		Symbol:        b.Symbol,
		Tag:           b.Tag,
		Status:        b.Status.String(),
		DesiredStatus: b.DesiredStatus,
		CreatedAt:     b.CreatedAt,
		UpdatedAt:     b.UpdatedAt,
		DeletedAt:     b.DeletedAt,
	}
}

// CreateBotRequest represents the request parameters for creating a new bot.
//
//	@Description	Request payload for creating a new bot with account ID, symbol, region, and tag.
type CreateBotRequest struct {
	// Account ID associated with the bot
	AccountID string `json:"accountId" validate:"required,uuid" example:"8dfc86a3-a9f7-445e-be53-f2b07d109829" format:"uuid" extensions:"x-order=0"`
	// Symbol associated with the bot
	Symbol string `json:"symbol" validate:"required" example:"BTCUSD" extensions:"x-order=1"`
	// Tag used to identify the bot
	Tag string `json:"tag" validate:"required" example:"atomic-mm-mercado-bitcoin" extensions:"x-order=3"`
} //@name CreateBotRequest

// CreateBot creates a new bot.
//
//	@Summary		Create a new bot.
//	@Description	Creates a new bot with the specified parameters.
//	@Security		Bearer
//	@Accept			json
//	@Produce		json
//	@Tags			Bot
//	@Param			data	body		CreateBotRequest	true	"Bot creation parameters"
//	@Success		201		{object}	ginx.Response[Bot]
//	@Failure		403		{object}	errors.Error	"you are not authorized to access this resource"
//	@Router			/bots [post]
func (ctrl *Controller) CreateBot(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "CreateBot"))
	defer span.End()

	var req CreateBotRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	res, err := ctrl.botSvc.Create(ctx, botsservice.CreateBotRequest{
		AccountID: req.AccountID,
		Symbol:    req.Symbol,
		Tag:       req.Tag,
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResCreated(c, FromBotService(res))
}

// GetBot retrieves a bot by ID.
//
//	@Summary		Retrieve a bot by ID.
//	@Description	Retrieve a bot with the specified ID.
//	@Security		Bearer
//	@Accept			json
//	@Produce		json
//	@Tags			Bot
//	@Param			botId	path		string	true	"Bot ID for retrieval"	format(uuid)	example(1dfc543a2-a5d6-445e-be53-f2b07d109823)
//	@Success		200		{object}	ginx.Response[Bot]
//	@Failure		403		{object}	errors.Error	"you are not authorized to access this resource"
//	@Router			/bots/{botId} [get]
//
// GetBot retrieve a bot by ID.
func (ctrl *Controller) GetBot(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "GetBot"))
	defer span.End()

	botID := c.Param("botId")
	if botID == "" {
		ginx.ResError(c, errors.BadRequest(codes.Parameters, "botId is required"))
		return
	}

	res, err := ctrl.botSvc.Get(ctx, botID)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResSuccess(c, FromBotService(res))
}

// UpdateBotRequest represents the request parameters for updating an existing bot.
//
//	@Description	Request parameters for updating an existing bot by providing an account ID, symbol, region or status.
type UpdateBotRequest struct {
	// Account ID associated with the bot
	AccountID string `json:"accountId" example:"8dfc86a3-a9f7-445e-be53-f2b07d109829"  validate:"optional,uuid" format:"uuid" extensions:"x-order=0"`
	// Symbol associated with the bot
	Symbol string `json:"symbol" example:"BTCUSD"  validate:"optional" extensions:"x-order=1"`
	// RegionID information for the bot
	RegionID string `json:"regionId" example:"us-east-2"  validate:"optional" extensions:"x-order=2"`
	// Tag used to identify the bot
	Tag string `json:"tag" example:"atomic-mm-mercado-bitcoin"  validate:"optional" extensions:"x-order=3"`
} //@name UpdateBotRequest

// Deprecated: updating a bot is current not viable due the simplicity of our current business
//
// UpdateBot updates an existing bot by ID.
//
//	@Summary		Update an existing bot by ID.
//	@Description	Update an existing bot with the specified ID by providing account ID, symbol, region, or status.
//	@Description	You need to ensure that at least one field is provided in the update request.
//	@Security		Bearer
//	@Accept			json
//	@Produce		json
//	@Tags			Bot
//	@Param			botId	path		string				true	"Bot ID to be updated"	format(uuid)	example(1dfc543a2-a5d6-445e-be53-f2b07d109823)
//	@Param			data	body		UpdateBotRequest	true	"Bot update parameters"
//	@Success		200		{object}	ginx.Response[Bot]
//	@Failure		403		{object}	errors.Error	"you are not authorized to access this resource"
//	@Router			/bots/{botId} [patch]
func (ctrl *Controller) UpdateBot(c *gin.Context) {
	ginx.ResNotImplemented(c)
	//ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "UpdateBot"))
	//defer span.End()
	//
	//botID := c.Param("botId")
	//
	//var req UpdateBotRequest
	//if err := ginx.ParseJSON(c, &req); err != nil {
	//	ginx.ResError(c, err)
	//	return
	//}
	//
	//res, err := ctrl.botSvc.Update(ctx, botsservice.UpdateBotRequest{
	//	BotID: botID,
	//	Tag:   req.Tag,
	//})
	//if err != nil {
	//	ginx.ResError(c, err)
	//	return
	//}
	//
	//ginx.ResSuccess(c, FromBotService(res))
}

// Deprecated: deleting a bot is current not viable due the simplicity of our current business
//
// DeleteBot deletes a bot by ID.
//
//	@Summary		Delete a bot by ID.
//	@Description	Delete a bot with the specified ID.
//	@Security		Bearer
//	@Tags			Bot
//	@Param			botId	path	string	true	"Bot ID to be deleted"	format(uuid)	example(1dfc543a2-a5d6-445e-be53-f2b07d109823)
//	@Success		204		"No Content"
//	@Failure		403		{object}	errors.Error	"you are not authorized to access this resource"
//	@Router			/bots/{botId} [delete]
func (ctrl *Controller) DeleteBot(c *gin.Context) {
	ginx.ResNotImplemented(c)
	//ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "DeleteBot"))
	//defer span.End()
	//
	//botID := c.Param("botId")
	//
	//if err := ctrl.botSvc.Delete(ctx, botID); err != nil {
	//	ginx.ResError(c, err)
	//	return
	//}
	//
	//ginx.ResNoContent(c)
}

// ListBotsRequest represents the request parameters for listing bots.
//
//	@Description	Request parameters for listing bots with optional filtering and pagination parameters.
type ListBotsRequest struct {
	ginx.PaginationParam
	// Indicates whether deleted items should be included.
	IsDeleted bool `form:"isDeleted" validate:"optional" example:"true" default:"false" extensions:"x-order=30"`
	// Specifies the field by which the results should be ordered.
	OrderBy string `form:"orderBy" validate:"optional" enums:"account_id,symbol,region,status,created_at" default:"created_at" example:"created_at" extensions:"x-order=31"`
	// AccountID filter bots by Account ID
	AccountID string `form:"accountId" validate:"optional" example:"8dfc86a3-a9f7-445e-be53-f2b07d109829" format:"uuid" extensions:"x-order=0"`
	// Symbol filter bots by its symbol
	Symbol string `form:"symbol" validate:"optional" example:"BTCUSD" extensions:"x-order=1"`
	// RegionID filter bots by is region
	RegionID string `form:"regionId" validate:"optional" example:"us-east-2" extensions:"x-order=2"`
	// Status filter bots by status
	Status string `form:"status" validate:"optional" example:"running" extensions:"x-order=3"`
	// Active filter bots by his state
	Active string `form:"active" validate:"optional" example:"true" extensions:"x-order=4"`
} //@name ListBotsRequest

// ListBots lists bots with custom filters.
//
//	@Summary		Lists bots with custom filters.
//	@Description	Retrieves a list of bots with optional filtering and pagination parameters.
//	@Security		Bearer
//	@Tags			Bot
//	@Accept			json
//	@Produce		json
//	@Param			params	query		ListBotsRequest				true	"Filter bots with optional parameters."
//	@Success		200		{object}	ginx.PageResponse[[]Bot]	"List of Bots"
//	@Failure		403		{object}	errors.Error				"you are not authorized to access this resource"
//	@Router			/bots [get]
func (ctrl *Controller) ListBots(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ListBots"))
	defer span.End()

	// Extract filter parameters from query parameters and apply them to the filters.
	var req ListBotsRequest
	if err := ginx.ParseQuery(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	if req.OrderDirection == "" {
		req.OrderDirection = "DESC"
	}

	req.OrderDirection = strings.ToUpper(req.OrderDirection)

	result, err := ctrl.botSvc.List(ctx, botsservice.ListBotsRequest{
		PaginationParam: req.PaginationParam,
		OrderBy:         req.OrderBy,
		IsDeleted:       req.IsDeleted,
		AccountID:       req.AccountID,
		Symbol:          req.Symbol,
		Region:          req.RegionID,
		Status:          types.ToStatus(req.Status),
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	var res = make([]*Bot, len(result.Bots))
	for i, bot := range result.Bots {
		res[i] = FromBotService(bot)
	}

	ginx.ResPage(c, res, &ginx.PaginationResult{
		Total:  result.Total,
		Offset: req.Offset,
		Limit:  req.Limit,
	})
}

// StartBot is used to create a bot.
//
//	@Summary		StartBot create a new bot if not exists.
//	@Description	It creates a bot based on the botId.
//	@Security		Bearer
//	@Tags			Bot
//	@Accept			json
//	@Produce		json
//	@Param			botId	path	string	true	"Bot ID for retrieval"	format(uuid)	example(1dfc543a2-a5d6-445e-be53-f2b07d109823)
//	@Success		200		"Success"
//	@Failure		403		{object}	errors.Error	"you are not authorized to access this resource"
//	@Router			/bots/{botId}/start [post]
func (ctrl *Controller) StartBot(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "StartBot"))
	defer span.End()

	botID := c.Param("botId")
	if botID == "" {
		ginx.ResError(c, errors.BadRequest(codes.Parameters, "botId is required"))
		return
	}

	if err := ctrl.botSvc.StartBot(ctx, botID); err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResOK(c)
}

// StopBot is used to stopping a bot.
//
//	@Summary		StopBot stop a bot if exists.
//	@Description	It stops a bot based on the botId.
//	@Security		Bearer
//	@Tags			Bot
//	@Accept			json
//	@Produce		json
//	@Param			botId	path	string	true	"Bot ID for retrieval"	format(uuid)	example(1dfc543a2-a5d6-445e-be53-f2b07d109823)
//	@Success		200		"Success"
//	@Failure		403		{object}	errors.Error	"you are not authorized to access this resource"
//	@Router			/bots/{botId}/stop [post]
func (ctrl *Controller) StopBot(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "StopBot"))
	defer span.End()

	botID := c.Param("botId")
	if botID == "" {
		ginx.ResError(c, errors.BadRequest(codes.Parameters, "botId is required"))
		return
	}

	if err := ctrl.botSvc.StopBot(ctx, botID); err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResOK(c)
}

// ListBotsStateResponse represents the state of a single bot in a list.
//
//	@Description	Contains the detailed state information for a bot, including its ID, operational data, parameters, and the time the state was captured.
type ListBotsStateResponse struct {
	// The unique identifier of the bot.
	BotID string `json:"botId" example:"1dfc543a2-a5d6-445e-be53-f2b07d109823"`
	// A map containing the bot's current operational data.
	Data map[string]interface{} `json:"data"`
	// A map containing the bot's configuration parameters.
	Params map[string]interface{} `json:"params"`
	// The timestamp when the state was recorded.
	Time time.Time `json:"time" example:"2023-08-01T14:30:00Z" format:"date-time"`
} //@name ListBotsStateResponse

// ListBotsState retrieves the state of all bots.
//
//	@Summary		Get bots state.
//	@Description	Retrieves a cached state of all bots. The data is populated by a background worker.
//	@Security		Bearer
//	@Accept			json
//	@Produce		json
//	@Tags			Bot
//	@Success		200	{object}	ginx.Response[[]ListBotsStateResponse]	"Successful response with bot state data"
//	@Failure		404	{object}	errors.Error		"No state data available"
//	@Failure		500	{object}	errors.Error		"Internal server error"
//	@Router			/bots/state [get]
func (ctrl *Controller) ListBotsState(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ListBotsState"))
	defer span.End()

	statesData, found := ctrl.botSvc.ListBotsState(ctx)
	if !found {
		ginx.ResError(c, errors.NotFound(codes.Bots, "bots state not available"))
		return
	}

	// Type assert the cached data to the correct proto slice
	states, ok := statesData.([]*proto.State)
	if !ok {
		ginx.ResError(c, errors.InternalServerError(codes.Bots, "invalid state data format in cache"))
		return
	}

	// Transform the list of proto states into a list of generic DTOs.
	response := make([]ListBotsStateResponse, len(states))
	for i, state := range states {
		response[i] = ListBotsStateResponse{
			BotID:  state.GetBotId(),
			Data:   state.GetData().AsMap(),
			Params: state.GetParams().AsMap(),
			Time:   state.GetTime().AsTime(),
		}
	}

	ginx.ResSuccess(c, response)
}

// GetBotStateResponse represents the state of a single bot.
//
//	@Description	Contains the detailed state information for a specific bot, including its ID, operational data, parameters, and the time the state was captured.
type GetBotStateResponse struct {
	// The unique identifier of the bot.
	BotID string `json:"botId" example:"1dfc543a2-a5d6-445e-be53-f2b07d109823"`
	// A map containing the bot's current operational data.
	Data map[string]interface{} `json:"data"`
	// A map containing the bot's configuration parameters.
	Params map[string]interface{} `json:"params"`
	// The timestamp when the state was recorded.
	Time time.Time `json:"time" example:"2023-08-01T14:30:00Z" format:"date-time"`
} //@name GetBotStateResponse

// GetBotState retrieves the state of a specific bot.
//
//	@Summary		Get bot state.
//	@Description	Retrieves the current state of a specific bot from the botregional service. It uses a short-lived cache to reduce load.
//	@Security		Bearer
//	@Accept			json
//	@Produce		json
//	@Tags			Bot
//	@Param			botId	path		string				true	"Bot ID"	format(uuid)	example(1dfc543a2-a5d6-445e-be53-f2b07d109823)
//	@Success		200	{object}	ginx.Response[GetBotStateResponse]	"Successful response with bot state"
//	@Failure		400	{object}	errors.Error			"Invalid bot ID format"
//	@Failure		404	{object}	errors.Error			"Bot not found"
//	@Failure		500	{object}	errors.Error			"Internal server error"
//	@Router			/bots/{botId}/state [get]
func (ctrl *Controller) GetBotState(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "GetBotState"))
	defer span.End()

	botID := c.Param("botId")
	if botID == "" {
		ginx.ResError(c, errors.BadRequest(codes.Parameters, "id is required"))
		return
	}

	state, err := ctrl.botSvc.GetBotState(ctx, botID)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	response := GetBotStateResponse{
		BotID:  state.GetBotId(),
		Data:   state.GetData().AsMap(),
		Params: state.GetParams().AsMap(),
		Time:   state.GetTime().AsTime(),
	}

	ginx.ResSuccess(c, response)
}
