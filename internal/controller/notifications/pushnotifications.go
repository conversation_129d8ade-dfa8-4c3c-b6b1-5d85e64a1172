package notifications

import (
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/services/notificationsmanagerservice"
)

// PushNotification represents the push notification model.
// @Description Model for a push notification.
type PushNotification struct {
	// ID of the push notification
	ID string `json:"id" example:"1" extensions:"x-order=0"`
	// User ID associated with the push notification
	UserID string `json:"userId" example:"e7cf3a21-9c6b-4a01-af13-2c8e8f91a6bb" format:"uuid" extensions:"x-order=1"`
	// Device token for the push notification
	DeviceToken string `json:"deviceToken" example:"dkYBSBhG1jCVUwJ6vdOn-R" extensions:"x-order=2"`
	// Platform of the device (web, ios, android)
	Platform string `json:"platform" example:"ios" extensions:"x-order=3"`
	// Time when the push notification was created
	CreatedAt time.Time `json:"createdAt" example:"2024-02-10T12:30:45Z" format:"date-time" extensions:"x-order=4"`
	// Time when the push notification was last updated
	UpdatedAt time.Time `json:"updatedAt" example:"2024-02-10T12:35:00Z" format:"date-time" extensions:"x-order=5"`
} //@name PushNotification

// CreatePushNotificationRequest for creating or updating a push notification.
// @Description Request payload for creating or updating a push notification.
type CreatePushNotificationRequest struct {
	// Device token for the push notification
	DeviceToken string `json:"deviceToken" validate:"required" example:"dkYBSBhG1jCVUwJ6vdOn-R" extensions:"x-order=0"`
	// Platform of the device (web, ios, android)
	Platform string `json:"platform" validate:"required,oneof=web ios android" example:"ios" extensions:"x-order=1"`
} //@name CreatePushNotificationRequest

// CreateOrUpdatePushNotification register a new push notification device.
//
// @Summary Register or update a push notification device.
// @Description Register or update a push notification device by providing the device token and platform.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Notification
// @Param data body CreatePushNotificationRequest true "Device Token and Platform"
// @Success 201 {object} ginx.Response[PushNotification]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /notifications/push [post]
func (ctrl *Controller) CreateOrUpdatePushNotification(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "CreateOrUpdatePushNotification"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	var req CreatePushNotificationRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	newPushNotify, err := ctrl.notifyMngSvc.CreateOrUpdatePushNotification(ctx, &notificationsmanagerservice.CreatePushNotificationRequest{
		UserID:      userID,
		DeviceToken: req.DeviceToken,
		Platform:    req.Platform,
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResCreated(c, PushNotification{
		ID:          newPushNotify.ID,
		UserID:      newPushNotify.UserID,
		DeviceToken: newPushNotify.DeviceToken,
		Platform:    newPushNotify.Platform,
		CreatedAt:   newPushNotify.CreatedAt,
		UpdatedAt:   newPushNotify.UpdatedAt,
	})
}

// DeletePushNotification delete a push notification device by device token.
//
// @Summary Delete a push notification device.
// @Description Delete a push notification device by providing the device token.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Notification
// @Param deviceToken path string true "Device Token to Delete" example(dkYBSBhG1jCVUwJ6vdOn-R)
// @Success 204 {string} string "No Content"
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /notifications/push/{deviceToken} [delete]
func (ctrl *Controller) DeletePushNotification(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "DeletePushNotification"))
	defer span.End()

	deviceToken := c.Param("deviceToken")
	if deviceToken == "" {
		ginx.ResError(c, errors.BadRequest(codes.Parameters, "deviceToken is required"))
	}

	if err := ctrl.notifyMngSvc.DeletePushNotificationByDeviceTokenID(ctx, deviceToken); err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResNoContent(c)
}

// ListPushNotificationsRequest for listing push notifications
// @Description Request payload for listing push notifications.
type ListPushNotificationsRequest struct {
	ginx.PaginationParam
	// Specifies the field by which the results should be ordered.
	OrderBy string `form:"orderBy" validate:"optional" enums:"platform,created_at" default:"created_at" example:"created_at" extensions:"x-order=31"`

	// Platform which the token was subscribed
	Platform string `form:"platform" validate:"optional" example:"ios" extensions:"x-order=0"`
} //@name ListPushNotificationsRequest

// ListPushNotifications list registered push notifications of a user.
//
// @Summary List push notifications.
// @Description List registered push notifications of a user with optional filtering and ordering.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Notification
// @Param data query ListPushNotificationsRequest true "Query parameters for listing push notifications"
// @Success 200 {object} ginx.PageResponse[[]PushNotification]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /notifications/push [get]
func (ctrl *Controller) ListPushNotifications(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "DeletePushNotification"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	var req ListPushNotificationsRequest
	if err := ginx.ParseQuery(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	if req.OrderDirection == "" {
		req.OrderDirection = "DESC"
	}
	req.OrderDirection = strings.ToUpper(req.OrderDirection)

	// Extract filter parameters from query parameters and apply them to the filters.
	filters := notificationsmanagerservice.ListPushNotificationsRequest{
		PaginationParam: req.PaginationParam,
		OrderBy:         req.OrderBy,
		UserID:          userID,
		Platform:        req.Platform,
	}

	pushNtfs, err := ctrl.notifyMngSvc.ListPushNotifications(ctx, filters)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	var res = make([]PushNotification, len(pushNtfs.Notifications))
	for i, pn := range pushNtfs.Notifications {
		res[i] = PushNotification{
			ID:          pn.ID,
			UserID:      pn.UserID,
			DeviceToken: pn.DeviceToken,
			Platform:    pn.Platform,
			CreatedAt:   pn.CreatedAt,
			UpdatedAt:   pn.UpdatedAt,
		}
	}

	ginx.ResPage(c, res, &ginx.PaginationResult{
		Total:  int32(pushNtfs.Total),
		Offset: req.Offset,
		Limit:  req.Limit,
	})
}
