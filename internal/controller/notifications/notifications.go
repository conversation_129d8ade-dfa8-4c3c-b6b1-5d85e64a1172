package notifications

import (
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/internal/pkg/validate"
	"github.com/herenow/atomic-bm/internal/services/notificationsmanagerservice"
)

// NotificationSubscription represents the notification subscriptions model.
// @Description Model for a notification subscription.
type NotificationSubscription struct {
	// ID of the notification subscription
	ID int `json:"id" example:"1" extensions:"x-order=0"`
	// Type of event associated with the subscription
	EventType string `json:"eventType" example:"new_notification" extensions:"x-order=1"`
	// User ID linked to the subscription
	UserID string `json:"userId" example:"user123" extensions:"x-order=2"`
	// Resource ID connected to the subscription
	ResourceID string `json:"resourceId" example:"12345" extensions:"x-order=3"`
	// Time when the subscription was created
	CreatedAt time.Time `json:"createdAt" example:"2024-02-10T12:30:45Z" format:"date-time" extensions:"x-order=4"`
} //@name NotificationSubscription

// CreateNotificationSubscriptionRequest for creating a notification subscription
// @Description Request payload for creating a notification subscription.
type CreateNotificationSubscriptionRequest struct {
	Subscriptions []struct {
		// Type of event for the subscription
		EventType string `json:"eventType" example:"bot:offline" extensions:"x-order=0"`
		// Resource ID associated with the subscription
		ResourceID string `json:"resourceID" example:"12345" extensions:"x-order=1"`
	} `json:"subscriptions"`
} //@name CreateNotificationSubscriptionRequest

// CreateNotificationSubscriptions create a new notification subscription.
//
// @Summary Create a notification subscription.
// @Description Create a new notification subscription by specifying the event type and resource ID.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Notification
// @Param data body CreateNotificationSubscriptionRequest true "Event Type and Resource ID"
// @Success 201 {object} ginx.Response[[]NotificationSubscription]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /notifications/subscribe [post]
func (ctrl *Controller) CreateNotificationSubscriptions(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "CreateNotificationSubscriptions"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	var req CreateNotificationSubscriptionRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	var subsToCreate = make([]notificationsmanagerservice.SubscribeEventsRequest, len(req.Subscriptions))
	for i, sub := range req.Subscriptions {
		subsToCreate[i] = notificationsmanagerservice.SubscribeEventsRequest{
			UserID:     userID,
			EventType:  sub.EventType,
			ResourceID: sub.ResourceID,
		}
	}

	newSubs, err := ctrl.notifyMngSvc.SubscribeEvents(ctx, subsToCreate)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	var res = make([]NotificationSubscription, len(newSubs))
	for i, s := range newSubs {
		res[i] = NotificationSubscription{
			ID:         s.ID,
			EventType:  s.EventType,
			UserID:     s.UserID,
			ResourceID: s.ResourceID,
			CreatedAt:  s.CreatedAt,
		}
	}

	ginx.ResSuccess(c, res)
}

// DeleteNotificationSubscriptionRequest for deleting notification subscriptions
// @Description Request payload for deleting notification subscriptions.
type DeleteNotificationSubscriptionRequest struct {
	// Array of subscription IDs to be deleted
	SubscriptionIDs []int `json:"subscriptionIds" validate:"required,gt=0" example:"1,2,3" extensions:"x-order=0"`
} //@name DeleteNotificationSubscriptionRequest

// DeleteNotificationSubscriptions delete notification subscriptions.
//
// @Summary Delete notification subscriptions.
// @Description Delete notification subscriptions by providing an array of subscription IDs.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Notification
// @Param data body DeleteNotificationSubscriptionRequest true "Subscription IDs to Delete"
// @Success 204 {string} string "No Content"
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /notifications/subscribe/bulkDelete [post]
func (ctrl *Controller) DeleteNotificationSubscriptions(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "DeleteNotificationSubscriptions"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	var req DeleteNotificationSubscriptionRequest
	if err := ginx.ParseJSON(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	if err := validate.Validator.ValidateStruct(req); err != nil {
		ginx.ResError(c, err)
		return
	}

	if err := ctrl.notifyMngSvc.UnsubscribeEvents(ctx, notificationsmanagerservice.UnsubscribeEventRequest{
		UserID:           userID,
		SubscriptionsIDs: req.SubscriptionIDs,
	}); err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResNoContent(c)
}

// ListNotificationSubscriptionsRequest for listing notification subscriptions
// @Description Request payload for listing notification subscriptions.
type ListNotificationSubscriptionsRequest struct {
	ginx.PaginationParam
	// Specifies the field by which the results should be ordered.
	OrderBy string `form:"orderBy" validate:"optional" enums:"eventType,created_at" default:"created_at" example:"created_at" extensions:"x-order=31"`

	// EventType specifies which kind of event the user will receive.
	EventType string `form:"eventType" validate:"optional" example:"bot:offline" extensions:"x-order=0"`
	// ResourceID is the entity associated with the EventType this is, a bot or account.
	ResourceID string `form:"resourceId" validate:"optional" example:"1dfc543a2-a5d6-445e-be53-f2b07d109823" extensions:"x-order=1"`
} //@name ListNotificationSubscriptionsRequest

// ListNotificationSubscriptions list notification subscriptions.
//
// @Summary List notification subscriptions.
// @Description List notification subscriptions with optional filtering and ordering.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Notification
// @Param data query ListNotificationSubscriptionsRequest true "Query parameters for listing subscriptions"
// @Success 200 {object} ginx.PageResponse[[]NotificationSubscription]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /notifications/subscribe [get]
func (ctrl *Controller) ListNotificationSubscriptions(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ListNotificationSubscriptions"))
	defer span.End()

	userID := ctrl.session.UserIDFromContext(c)
	if userID == "" {
		ginx.ResError(c, errors.ForbiddenResourceError)
		return
	}

	var req ListNotificationSubscriptionsRequest
	if err := ginx.ParseQuery(c, &req); err != nil {
		ginx.ResError(c, err)
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	if req.OrderDirection == "" {
		req.OrderDirection = "DESC"
	}
	req.OrderDirection = strings.ToUpper(req.OrderDirection)

	events, err := ctrl.notifyMngSvc.ListSubscribeEvents(ctx, notificationsmanagerservice.ListNotificationSubscriptionsRequest{
		PaginationParam: req.PaginationParam,
		EventType:       req.EventType,
		UserID:          userID,
		ResourceID:      req.ResourceID,
	})
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	var res = make([]NotificationSubscription, len(events.NotificationSubscriptions))
	for i, s := range events.NotificationSubscriptions {
		res[i] = NotificationSubscription{
			ID:         s.ID,
			EventType:  s.EventType,
			UserID:     s.UserID,
			ResourceID: s.ResourceID,
			CreatedAt:  s.CreatedAt,
		}
	}

	ginx.ResPage(c, res, &ginx.PaginationResult{
		Total:  int32(events.Total),
		Offset: req.Offset,
		Limit:  req.Limit,
	})

}
