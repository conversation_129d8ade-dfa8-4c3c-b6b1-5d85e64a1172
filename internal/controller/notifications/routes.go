package notifications

import (
	"github.com/herenow/atomic-bm/cmd/app/dependencies"
	"github.com/herenow/atomic-bm/internal/app"
	"github.com/uptrace/bun"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/auth/session"
	"github.com/herenow/atomic-bm/internal/db/repo/botrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/notificationsrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/pushnotificationrepo"
	"github.com/herenow/atomic-bm/internal/services/notificationsmanagerservice"
)

var _spanName = "NotificationsController/%s"

const _ScopeName = "NotificationsController"

type Controller struct {
	session *session.Manager
	tracer  trace.Tracer

	notifyMngSvc notificationsmanagerservice.INotificationsService
}

func New(db bun.IDB, session *session.Manager) *Controller {
	ntfBotRepo := notificationsrepo.NewNotificationSubscriptions(db)
	ntfPushRepo := pushnotificationrepo.New(db)
	botRepo := botrepo.New(db)

	return &Controller{
		session:      session,
		tracer:       otel.Tracer(_ScopeName),
		notifyMngSvc: notificationsmanagerservice.New(ntfBotRepo, ntfPushRepo, botRepo),
	}
}

func Register(app *app.App, dp *dependencies.Container) {
	notifyHandler := New(app.DB(), app.Session())

	g := app.APIGroup().Group("/notifications")
	{
		// Push Notifications
		{
			p := g.Group("/push")
			p.PATCH("", notifyHandler.CreateOrUpdatePushNotification)
			p.DELETE("/:deviceToken", notifyHandler.DeletePushNotification)
			p.GET("", notifyHandler.ListPushNotifications)
		}

		// Notifications Subscriptions
		{
			s := g.Group("/subscribe")
			s.POST("", notifyHandler.CreateNotificationSubscriptions)
			s.POST("/bulkDelete", notifyHandler.DeleteNotificationSubscriptions)
			s.GET("", notifyHandler.ListNotificationSubscriptions)
		}
	}
}
