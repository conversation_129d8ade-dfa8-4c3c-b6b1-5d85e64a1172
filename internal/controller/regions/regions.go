package regions

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"

	_ "github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
)

// Region represents the region model.
type Region struct {
	// Region ID
	ID string `json:"id" example:"aws-br" extensions:"x-order=0"`
	// Description about this region
	Description string `json:"description" example:"aws-br" extensions:"x-order=1"`
	// Secret used by this region
	SecretKey string `json:"secretKey" example:"8dfc86a3-a9f7-445e-be53-f2b07d109829" extensions:"x-order=2"`
	// When the region was created
	CreatedAt time.Time `json:"createdAt" example:"2024-17-03T10:11:24.758443321Z" extensions:"x-order=3"`
} // @name Region

// GetRegion get a region by its id.
//
// @Summary Fetch a region by its id.
// @Description Fetch a region by its id.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Region
// @Param regionId path string true "Region ID for retrieval" format(uuid) example(1dfc543a2-a5d6-445e-be53-f2b07d109823)
// @Success 201 {object} ginx.Response[Region]
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /regions/{regionId} [get]
func (ctrl *Controller) GetRegion(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "GetRegion"))
	defer span.End()

	regionID := c.Param("regionId")
	if regionID == "" {
		ginx.ResError(c, errors.BadRequest(codes.Parameters, "regionId is required"))
		return
	}

	reg, err := ctrl.regionRepo.Get(ctx, regionID)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	ginx.ResSuccess(c, Region{
		ID:          reg.ID,
		Description: reg.Description,
		SecretKey:   reg.SecretKey,
		CreatedAt:   reg.CreatedAt,
	})
}

// ListRegions list all regions.
//
// @Summary Fetch a list of regions.
// @Description List regions.
// @Security Bearer
// @Accept json
// @Produce json
// @Tags Region
// @Success 200 {object} ginx.PageResponse[[]Region] "Regions"
// @Failure 403 {object} errors.Error "you are not authorized to access this resource"
// @Router /regions [get]
func (ctrl *Controller) ListRegions(c *gin.Context) {
	ctx, span := ctrl.tracer.Start(c.Request.Context(), fmt.Sprintf(_spanName, "ListRegions"))
	defer span.End()

	regionList, err := ctrl.regionRepo.List(ctx)
	if err != nil {
		ginx.ResError(c, err)
		return
	}

	res := make([]Region, len(regionList))
	for i, r := range regionList {
		res[i] = Region{
			ID:          r.ID,
			Description: r.Description,
			SecretKey:   r.SecretKey,
			CreatedAt:   r.CreatedAt,
		}
	}

	ginx.ResPage(c, res, &ginx.PaginationResult{
		Total:  int32(len(res)),
		Offset: 0,
		Limit:  0,
	})
}
