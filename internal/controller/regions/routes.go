package regions

import (
	"github.com/herenow/atomic-bm/cmd/app/dependencies"
	"github.com/herenow/atomic-bm/internal/app"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/db/repo/regionsrepo"
)

var _spanName = "RegionController/%s"

const _ScopeName = "RegionController"

type Controller struct {
	tracer trace.Tracer

	regionRepo regionsrepo.IRegionRepo
}

func New(regionRepo regionsrepo.IRegionRepo) *Controller {
	return &Controller{
		regionRepo: regionRepo,
		tracer:     otel.Tracer(_ScopeName),
	}
}

func Register(app *app.App, dp *dependencies.Container) {
	accountHandler := New(dp.RegionRepo)

	g := app.APIGroup().Group("/regions")
	g.GET("/:regionId", accountHandler.GetRegion)
	g.GET("/", accountHandler.ListRegions)
}
