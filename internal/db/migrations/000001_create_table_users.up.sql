-- Create the new_users_table with the desired structure
CREATE TABLE IF NOT EXISTS users (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     name VA<PERSON><PERSON><PERSON>(255),
     email VARCHAR(255) UNIQUE,
     password VARCHAR(255),
     role VA<PERSON>HAR(255),
     created_at TIMESTAMP DEFAULT NOW(),
     updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS users_auth_id_idx ON users (id);

CREATE INDEX IF NOT EXISTS users_email_idx ON users (email);

-- Create a trigger function that updates the updated_at field
CREATE OR REPLACE FUNCTION update_updated_at()
    RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER users_updated_at_trigger
    BEFORE UPDATE ON users
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at();
