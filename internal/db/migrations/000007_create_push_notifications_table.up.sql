CREATE TABLE IF NOT EXISTS push_notifications(
    id           UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    user_id      UUID NOT NULL,
    platform     TEXT NOT NULL,
    device_token TEXT UNIQUE NOT NULL,
    created_at   TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at   TIMESTAMP DEFAULT NOW(),
    CONSTRAINT fk_notifications_user_id FOREIGN KEY (user_id) REFERENCES users (id),
    CONSTRAINT unique_user_device_platform UNIQUE (user_id, device_token, platform)
);

CREATE OR REPLACE TRIGGER push_notifications_updated_at_trigger
    BEFORE UPDATE ON push_notifications
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at();
