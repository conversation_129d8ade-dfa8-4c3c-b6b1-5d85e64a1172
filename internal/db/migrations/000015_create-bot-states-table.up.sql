CREATE TABLE IF NOT EXISTS bot_states (
  bot_id UUID NOT NULL,
  data JSONB NOT NULL,
  params JSONB NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT fk_bot_states_bot_id FOREIGN KEY (bot_id) REFERENCES bots (id),
  CONSTRAINT unique_bot_timestamp UNIQUE (bot_id, created_at)
);

CREATE OR REPLACE VIEW latest_bot_states AS
SELECT DISTINCT ON (bot_id)
    bot_id,
    data,
    params,
    created_at
FROM bot_states
ORDER BY bot_id, created_at DESC;
