CREATE TABLE IF NOT EXISTS exchanges (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

INSERT INTO exchanges (id, name) VALUES ('latoken', 'LATOKEN');
INSERT INTO exchanges (id, name) VALUES ('huobi', 'Huobi');
INSERT INTO exchanges (id, name) VALUES ('bitfinex', 'Bitfinex');
INSERT INTO exchanges (id, name) VALUES ('binance', 'Binance');
INSERT INTO exchanges (id, name) VALUES ('binancefutures', 'Binance Futures');
INSERT INTO exchanges (id, name) VALUES ('bitforex', 'BitForex');
INSERT INTO exchanges (id, name) VALUES ('ripio', 'Ripio');
INSERT INTO exchanges (id, name) VALUES ('currencylayer', 'CurrencyLayer');
INSERT INTO exchanges (id, name) VALUES ('bitmex', 'BitMEX');
INSERT INTO exchanges (id, name) VALUES ('upbit', 'Upbit');
INSERT INTO exchanges (id, name) VALUES ('mercadobitcoin', 'Mercado Bitcoin');
INSERT INTO exchanges (id, name) VALUES ('bitcointrade', 'BitcoinTrade');
INSERT INTO exchanges (id, name) VALUES ('probit', 'ProBit');
INSERT INTO exchanges (id, name) VALUES ('exmarkets', 'ExMarkets');
INSERT INTO exchanges (id, name) VALUES ('p2pb2b', 'P2PB2B');
INSERT INTO exchanges (id, name) VALUES ('chiliz', 'Chiliz');
INSERT INTO exchanges (id, name) VALUES ('gateio', 'Gate.io');
INSERT INTO exchanges (id, name) VALUES ('bitso', 'Bitso');
INSERT INTO exchanges (id, name) VALUES ('foxbit', 'Foxbit');
INSERT INTO exchanges (id, name) VALUES ('novadax', 'NovaDAX');
INSERT INTO exchanges (id, name) VALUES ('gemini', 'Gemini');
INSERT INTO exchanges (id, name) VALUES ('mexc', 'MEXC');
INSERT INTO exchanges (id, name) VALUES ('pancakeswap', 'PancakeSwap');
INSERT INTO exchanges (id, name) VALUES ('digifinex', 'DigiFinex');
INSERT INTO exchanges (id, name) VALUES ('bigone', 'BigONE');
INSERT INTO exchanges (id, name) VALUES ('lbank', 'LBank');
INSERT INTO exchanges (id, name) VALUES ('bitmart', 'BitMart');
INSERT INTO exchanges (id, name) VALUES ('kucoin', 'KuCoin');
INSERT INTO exchanges (id, name) VALUES ('xt', 'XT.com');
INSERT INTO exchanges (id, name) VALUES ('bitglobal', 'BitGlobal');
INSERT INTO exchanges (id, name) VALUES ('bitrue', 'Bitrue');
INSERT INTO exchanges (id, name) VALUES ('coinstore', 'Coinstore');
INSERT INTO exchanges (id, name) VALUES ('digitra', 'Digitra');
INSERT INTO exchanges (id, name) VALUES ('hotcoin', 'Hotcoin');
INSERT INTO exchanges (id, name) VALUES ('biconomy', 'Biconomy');
INSERT INTO exchanges (id, name) VALUES ('whitebit', 'WhiteBIT');
INSERT INTO exchanges (id, name) VALUES ('fastforex', 'fastFOREX');
INSERT INTO exchanges (id, name) VALUES ('coinbase', 'Coinbase');
INSERT INTO exchanges (id, name) VALUES ('deribit', 'Deribit');
INSERT INTO exchanges (id, name) VALUES ('cryptocom', 'Crypto.com');
INSERT INTO exchanges (id, name) VALUES ('kraken', 'Kraken');
INSERT INTO exchanges (id, name) VALUES ('bitstamp', 'Bitstamp');
INSERT INTO exchanges (id, name) VALUES ('okx', 'OKX');
INSERT INTO exchanges (id, name) VALUES ('bybit', 'Bybit');
INSERT INTO exchanges (id, name) VALUES ('bitget', 'Bitget');
INSERT INTO exchanges (id, name) VALUES ('bit', 'BIT');
INSERT INTO exchanges (id, name) VALUES ('coinsbit', 'Coinsbit');
INSERT INTO exchanges (id, name) VALUES ('azbit', 'Azbit');
INSERT INTO exchanges (id, name) VALUES ('bitypreco', 'BityPreco');
INSERT INTO exchanges (id, name) VALUES ('coinsph', 'Coins.ph');
INSERT INTO exchanges (id, name) VALUES ('onetrading', 'OneTrading');
INSERT INTO exchanges (id, name) VALUES ('cryptomkt', 'CryptoMarket');

ALTER TABLE IF EXISTS accounts ADD CONSTRAINT fk_accounts_exchange_id FOREIGN KEY (exchange_id) REFERENCES exchanges(id);
