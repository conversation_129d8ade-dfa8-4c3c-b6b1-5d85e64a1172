CREATE TABLE IF NOT EXISTS param_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    created_by_user_id UUID NOT NULL,
    deleted_by_user_id UUID,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,
    CONSTRAINT fk_param_groups_created_by_user FOREIGN KEY (created_by_user_id) REFERENCES users(id),
    CONSTRAINT fk_param_groups_deleted_by_user FOREIGN KEY (deleted_by_user_id) REFERENCES users(id)
);

CREATE UNIQUE INDEX unique_param_group_name
    ON param_groups(name)
    WHERE deleted_at IS NULL;

CREATE TABLE IF NOT EXISTS bot_param_groups (
    bot_id UUID NOT NULL,
    param_groups_id UUID NOT NULL,
    created_by_user_id UUID NOT NULL,
    deleted_by_user_id UUID,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,
    CONSTRAINT fk_bot_param_groups_bot FOREIGN KEY (bot_id) REFERENCES bots(id),
    CONSTRAINT fk_bot_param_groups_param_groups FOREIGN KEY (param_groups_id) REFERENCES param_groups(id),
    CONSTRAINT fk_bot_param_groups_created_by_user FOREIGN KEY (created_by_user_id) REFERENCES users(id),
    CONSTRAINT fk_bot_param_groups_deleted_by_user FOREIGN KEY (deleted_by_user_id) REFERENCES users(id)
);

CREATE UNIQUE INDEX unique_bot_param_group
    ON bot_param_groups(bot_id, param_groups_id)
    WHERE deleted_at IS NULL;

-- We are adding the deleting functionality so the value is na nullable.
ALTER TABLE IF EXISTS params_log ALTER COLUMN value DROP NOT NULL;
ALTER TABLE IF EXISTS params_log ALTER COLUMN value SET DEFAULT NULL;

-- Update params view, now params_view will hidden when tha latest value of a scope is null
DROP VIEW IF EXISTS params_view;

CREATE OR REPLACE VIEW params_view AS
SELECT * FROM (
    SELECT DISTINCT ON (p.scope_id, p.scope, p.key)
        p.scope_id,
        p.scope,
        p.key,
        p.value,
        p.is_encrypted,
        p.secret_key_version,
        p.version,
        p.created_by_id,
        p.created_at
    FROM params_log p
    ORDER BY p.scope_id, p.scope, p.key, p.version DESC
) AS latest_params WHERE latest_params.value IS NOT NULL;
