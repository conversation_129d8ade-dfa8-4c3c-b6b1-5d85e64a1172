CREATE TABLE IF NOT EXISTS regions (
    id TEXT PRIMARY KEY NOT NULL,
    description TEXT NOT NULL DEFAULT '',
    secret_key TEXT NOT NULL DEFAULT gen_random_uuid()::TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

INSERT INTO regions (id, description) VALUES ('local', 'Hosts bots in Local Host for demo purpose') ON CONFLICT (id) DO NOTHING;
INSERT INTO regions (id, description) VALUES ('aws-br', 'Hosts bots in Sao Paulo, Brazil') ON CONFLICT (id) DO NOTHING;
INSERT INTO regions (id, description) VALUES ('aws-sgp', 'Hosts bots in Singapore') ON CONFLICT (id) DO NOTHING;
INSERT INTO regions (id, description) VALUES ('aws-ohio', 'Hosts bots in Ohio, USA') ON CONFLICT (id) DO NOTHING;
INSERT INTO regions (id, description) VALUES ('aws-virginia', 'Hosts bots in Virginia, USA') ON CONFLICT (id) DO NOTHING;
INSERT INTO regions (id, description) VALUES ('aws-tokyo', 'Hosts bots in Tokyo, Japan') ON CONFLICT (id) DO NOTHING;
INSERT INTO regions (id, description) VALUES ('aws-london', 'Hosts bots in London, UK') ON CONFLICT (id) DO NOTHING;

ALTER TABLE IF EXISTS bots RENAME COLUMN region TO region_id;
ALTER TABLE IF EXISTS bots ADD CONSTRAINT fk_region FOREIGN KEY (region_id) REFERENCES regions(id);
