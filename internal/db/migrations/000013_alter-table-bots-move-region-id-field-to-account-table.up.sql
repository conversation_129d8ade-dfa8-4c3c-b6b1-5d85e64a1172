-- 1. Add the `region_id` column to the `accounts` table
ALTER TABLE IF EXISTS accounts ADD COLUMN IF NOT EXISTS region_id text;

-- 2. Populate the `region_id` column in the `accounts` table
-- Using DISTINCT to ensure only unique combinations of account_id and region_id are selected
WITH FirstBotRegion AS (
    SELECT DISTINCT ON (b.account_id) b.account_id, b.region_id
    FROM bots b
    ORDER BY b.account_id, b.id
)
UPDATE accounts a
    SET region_id = fbr.region_id
    FROM FirstBotRegion fbr
    WHERE a.id = fbr.account_id;

-- 3. Drop the `region_id` column from the `bots` table
ALTER TABLE IF EXISTS bots DROP COLUMN IF EXISTS region_id;

-- 4. Add foreign key constraint to `region_id` in the `accounts` table
ALTER TABLE IF EXISTS accounts
    ADD CONSTRAINT fk_region_id FOREIGN KEY (region_id) REFERENCES regions(id);

-- 5. Adjust unique account constraint to append region_id as dependency
DROP INDEX IF EXISTS accounts_unique_exchange_and_tag_null;

-- 6. Create a new unique index that includes region_id
CREATE UNIQUE INDEX IF NOT EXISTS accounts_unique_exchange_region_and_tag_null
    ON accounts (exchange_id, region_id, tag) WHERE (deleted_at IS NULL);

-- 7. Drop bot index that uses region
DROP INDEX IF EXISTS bots_unique_account_symbol_region_and_tag_null;

-- 8. Adjust bot unique index removing the region
CREATE UNIQUE INDEX IF NOT EXISTS bots_unique_account_symbol_and_tag_null
    ON bots(account_id, symbol, tag) WHERE deleted_at IS NULL;