-- 1. Add the `region_id` column back to the `bots` table
ALTER TABLE IF EXISTS bots ADD COLUMN IF NOT EXISTS region_id text;

-- 2. Populate the `region_id` column in the `bots` table
-- Using the region_id from the accounts table
UPDATE bots b
SET region_id = a.region_id
FROM accounts a
WHERE b.account_id = a.id AND a.region_id IS NOT NULL;

-- 3. Drop the `region_id` column from the `accounts` table
ALTER TABLE IF EXISTS accounts DROP COLUMN IF EXISTS region_id;

-- 4. Drop the foreign key constraint from the `accounts` table
ALTER TABLE IF EXISTS accounts DROP CONSTRAINT IF EXISTS fk_region_id;

-- 5. Adjust unique account constraint to remove region_id as dependency
DROP INDEX IF EXISTS accounts_unique_exchange_region_and_tag_null;

-- 6. Create a new unique index that Removes
CREATE UNIQUE INDEX IF NOT EXISTS accounts_unique_exchange_and_tag_null
    ON accounts (exchange_id, tag) WHERE (deleted_at IS NULL);

-- 7. Drop bots unique account sym and tag index
DROP INDEX IF EXISTS bots_unique_account_symbol_and_tag_null;

-- 8. <PERSON>reate unique dex for account, symbol, region and tag for bot
CREATE UNIQUE INDEX IF NOT EXISTS bots_unique_account_symbol_region_and_tag_null
    ON bots(account_id, symbol, region_id, tag) WHERE deleted_at IS NULL;
