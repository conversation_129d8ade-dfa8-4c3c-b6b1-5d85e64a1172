CREATE TABLE IF NOT EXISTS notification_subscriptions (
    id SERIAL PRIMARY KEY NOT NULL,
    event_type TEXT NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id),
    resource_id TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    CONSTRAINT unique_user_event_resource UNIQUE(user_id, event_type, resource_id)
);

CREATE TABLE IF NOT EXISTS notification_logs (
    id SERIAL PRIMARY KEY NOT NULL,
    event_type TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id),
    metadata JSONB,
    sent_at TIMESTAMP NOT NULL
);

CREATE TABLE IF NOT EXISTS notification_events (
    id SERIAL PRIMARY KEY NOT NULL,
    event_type TEXT NOT NULL,
    event_key TEXT NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id)
);

