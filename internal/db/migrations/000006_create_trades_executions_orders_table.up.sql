CREATE TABLE trades (
    id          SERIAL PRIMARY KEY NOT NULL,
    trade_id    TEXT,
    exchange_id TEXT,
    symbol      TEXT,
    base        TEXT,
    quote       TEXT,
    side        TEXT,
    price       NUMERIC(38, 18),
    amount      NUMERIC(38, 18),
    "time"      TIMESTAMP,
    CONSTRAINT trades_unique_exchange_trade_id UNIQUE (exchange_id, symbol, trade_id)
);

CREATE INDEX trades_amount ON trades USING btree (amount);
CREATE INDEX trades_exchange_id ON trades USING btree (exchange_id);
CREATE INDEX trades_symbol ON trades USING btree (symbol);
CREATE INDEX trades_base ON trades USING btree (base);
CREATE INDEX trades_quote ON trades USING btree (quote);
CREATE INDEX trades_trade_id ON trades USING btree (trade_id);
CREATE INDEX trades_price ON trades USING btree (price);
CREATE INDEX trades_side ON trades USING btree (side);
CREATE INDEX trades_time ON trades USING btree ("time");

CREATE TABLE IF NOT EXISTS executions (
    id          SERIAL PRIMARY KEY,
    bot_id      UUID,
    account_id  UUID,
    trade_id    TEXT,
    order_id    TEXT,
    exchange_id TEXT,
    symbol      TEXT,
    base        TEXT,
    quote       TEXT,
    side        TEXT,
    fee_asset   TEXT,
    fee         NUMERIC(38, 18) NOT NULL DEFAULT 0,
    amount      NUMERIC(38, 18) NOT NULL DEFAULT 0,
    price       NUMERIC(38, 18) NOT NULL DEFAULT 0,
    tags        TEXT[],
    "time"      TIMESTAMP,
    CONSTRAINT fk_executions_account_id FOREIGN KEY (account_id) REFERENCES accounts (id),
    CONSTRAINT fk_executions_bot_id FOREIGN KEY (bot_id) REFERENCES bots (id)
);

CREATE INDEX executions_order_id ON executions USING btree (order_id);
CREATE INDEX executions_trade_id ON executions USING btree (trade_id);
CREATE INDEX executions_bot_id ON executions USING btree (bot_id);
CREATE INDEX executions_account_id ON executions USING btree (account_id);
CREATE INDEX executions_exchange ON executions USING btree (exchange_id);
CREATE INDEX executions_market ON executions USING btree (symbol);
CREATE INDEX executions_base ON executions USING btree (base);
CREATE INDEX executions_quote ON executions USING btree (quote);
CREATE INDEX executions_price ON executions USING btree (price);
CREATE INDEX executions_amount ON executions USING btree (amount);
CREATE INDEX executions_side ON executions USING btree (side);
CREATE INDEX executions_time ON executions USING btree ("time");
CREATE INDEX executions_tags ON executions USING gin (tags);
CREATE INDEX executions_fee ON executions USING btree (fee);
CREATE INDEX executions_fee_asset ON executions USING btree (fee_asset);
