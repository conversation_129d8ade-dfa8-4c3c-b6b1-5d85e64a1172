CREATE TABLE IF NOT EXISTS accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exchange_id TEXT NOT NULL,
    tag TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP
);

CREATE UNIQUE INDEX accounts_unique_exchange_and_tag_null
    ON accounts(exchange_id, tag)
    WHERE deleted_at IS NULL;

CREATE OR REPLACE TRIGGER accounts_updated_at_trigger
    BEFORE UPDATE ON accounts
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

CREATE OR REPLACE FUNCTION gen_random_tag()
    RETURNS TEXT AS $$
BEGIN
    RETURN substr(md5(random()::text), 1, 8);
END;
$$ LANGUAGE plpgsql;

CREATE TABLE IF NOT EXISTS bots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL,
    symbol TEXT NOT NULL,
    region TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT '',
    tag TEXT NOT NULL DEFAULT gen_random_tag(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,
    CONSTRAINT fk_bots_account_id FOREIGN KEY (account_id) REFERENCES accounts (id)
);

CREATE UNIQUE INDEX bots_unique_account_symbol_region_and_tag_null
    ON bots(account_id, symbol, region, tag)
    WHERE deleted_at IS NULL;

CREATE OR REPLACE TRIGGER bots_updated_at_trigger
    BEFORE UPDATE ON bots
    FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

CREATE TABLE IF NOT EXISTS params_log (
    scope_id UUID NOT NULL,
    scope TEXT NOT NULL DEFAULT 'bot',
    key TEXT NOT NULL DEFAULT '',
    value TEXT NOT NULL DEFAULT '',
    is_encrypted BOOLEAN DEFAULT NULL,
    secret_key_version INT DEFAULT NULL,
    version INT NOT NULL DEFAULT 1,
    created_by_id UUID NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    PRIMARY KEY (scope_id, scope, key, version),
    CONSTRAINT fk_params_log_created_by_id FOREIGN KEY (created_by_id) REFERENCES users(id)
);

-- Function to automatically update the version when inserting a new record
CREATE OR REPLACE FUNCTION increment_params_version()
    RETURNS TRIGGER AS $$
BEGIN
    -- Increment the version based on existing records for the given scope_id, scope, and key
    NEW.version := COALESCE((SELECT MAX(version) FROM params_log WHERE scope_id = NEW.scope_id AND scope = NEW.scope AND key = NEW.key), 0) + 1;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to invoke the increment_params_version function before inserting into params_log
CREATE TRIGGER before_insert_increment_version
    BEFORE INSERT ON params_log
    FOR EACH ROW
EXECUTE FUNCTION increment_params_version();

CREATE OR REPLACE VIEW params_view AS
SELECT DISTINCT ON (p.scope_id, p.scope, p.key)
    p.scope_id,
    p.scope,
    p.key,
    p.value,
    p.is_encrypted,
    p.secret_key_version,
    p.version,
    p.created_by_id,
    p.created_at
FROM params_log p
ORDER BY p.scope_id, p.scope, p.key, p.version DESC;
