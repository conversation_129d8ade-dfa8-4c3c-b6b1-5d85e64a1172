DROP VIEW IF EXISTS params_view;

CREATE OR REPLACE VIEW params_view AS
SELECT DISTINCT ON (p.scope_id, p.scope, p.key)
    p.scope_id,
    p.scope,
    p.key,
    p.value,
    p.is_encrypted,
    p.secret_key_version,
    p.version,
    p.created_by_id,
    p.created_at
FROM params_log p
ORDER BY p.scope_id, p.scope, p.key, p.version DESC;

DROP INDEX IF EXISTS unique_bot_param_group;

DROP TABLE IF EXISTS bot_param_groups;

DROP INDEX IF EXISTS unique_param_group_name;

DROP TABLE IF EXISTS param_groups;
