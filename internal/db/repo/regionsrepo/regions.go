package regionsrepo

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/uptrace/bun"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
)

var _spanName = "RegionRepository/%s"

const _ScopeName = "RegionRepository"

var (
	RegionNotFoundError = errors.NotFound(codes.Regions, "region not found")
)

// Region represents the region model.
type Region struct {
	bun.BaseModel `bun:"regions,alias:r"`

	ID          string    `bun:",pk"`
	Description string    `bun:","`
	SecretKey   string    `bun:",type:uuid,default:gen_random_uuid,notnull"`
	CreatedAt   time.Time `bun:",notnull,default:current_timestamp"`
}

type IRegionRepo interface {
	Get(ctx context.Context, regionID string) (*Region, error)
	List(ctx context.Context) ([]*Region, error)
}

type RegionRepo struct {
	db     bun.IDB
	tracer trace.Tracer
}

func New(db bun.IDB) *RegionRepo {
	return &RegionRepo{db: db, tracer: otel.Tracer(_ScopeName)}
}

func (r *RegionRepo) Get(ctx context.Context, regionID string) (*Region, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "Get"))
	defer span.End()

	reg := &Region{ID: regionID}
	if err := r.db.NewSelect().
		Model(reg).
		WherePK().
		Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, RegionNotFoundError
		}
		return nil, errors.Wrap(err, "get region")
	}

	return reg, nil
}

func (r *RegionRepo) List(ctx context.Context) ([]*Region, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "List"))
	defer span.End()

	var regs []*Region
	if err := r.db.NewSelect().
		Model(&regs).
		Scan(ctx); err != nil {
		return nil, errors.Wrap(err, "list regions")
	}

	return regs, nil
}
