package botrepo

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/uptrace/bun"
	"github.com/uptrace/bun/driver/pgdriver"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/db/repo/accountrepo"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
)

var _spanName = "BotConfigRepository/%s"

const _ScopeName = "BotRepository"

var (
	ErrBotNotFound = errors.NotFound(codes.Bots, "bot not found")

	// ErrUniqueBotGroupConstraint is a constant error for the unique_bot_group constraint violation.
	ErrUniqueBotGroupConstraint = errors.BadRequest(codes.Bots, "bot with the same combination of account, symbol and tag already exists")
)

// Bot represents the bot model.
type Bot struct {
	bun.BaseModel `bun:"bots,alias:b"`

	ID            string     `bun:",pk,type:uuid,default:gen_random_uuid"`
	AccountID     string     `bun:",unique:unique_bot_group,notnull"`
	Symbol        string     `bun:",unique:unique_bot_group,notnull"`
	Tag           string     `bun:",unique:unique_bot_group,notnull,default:gen_random_tag"`
	Status        string     `bun:",notnull"`
	DesiredStatus string     `bun:",notnull"`
	LastError     string     `bun:",notnull"`
	CreatedAt     time.Time  `bun:",notnull,default:current_timestamp"`
	UpdatedAt     time.Time  `bun:",notnull,default:current_timestamp"`
	DeletedAt     *time.Time `bun:",soft_delete,nullzero"`

	Account accountrepo.Account `bun:"rel:belongs-to,join:account_id=id"`
}

// IBots defines the methods for managing bots.
type IBots interface {
	// Create adds a new bot.
	Create(ctx context.Context, bot *Bot) error

	// Get retrieves a bot by its ID.
	Get(ctx context.Context, botID string, withAccount bool) (*Bot, error)

	// Update modifies an existing bot.
	Update(ctx context.Context, bot *Bot) error

	// Deprecated: due business decision we will not delete bots
	//
	// Delete soft-deletes a bot.
	Delete(ctx context.Context, botID string) error

	// List retrieves a list of bots with optional filters, offset-based pagination, and a custom limit.
	List(ctx context.Context, filter ListBotsRequest) (*ListBotsResponse, error)
}

type BotsRepo struct {
	db     bun.IDB
	tracer trace.Tracer
}

// New creates a new instance of BotsRepo.
func New(db bun.IDB) *BotsRepo {
	return &BotsRepo{db: db, tracer: otel.Tracer(_ScopeName)}
}

// Create adds a new bot.
func (b *BotsRepo) Create(ctx context.Context, bot *Bot) error {
	ctx, span := b.tracer.Start(ctx, fmt.Sprintf(_spanName, "Create"))
	defer span.End()

	_, err := b.db.NewInsert().
		Model(bot).
		Returning("*").
		Exec(ctx, bot)
	if err != nil {
		var pgErr pgdriver.Error
		if errors.As(err, &pgErr) && pgErr.IntegrityViolation() {
			switch pgErr.Field('n') {
			case "bots_unique_account_symbol_and_tag_null":
				return ErrUniqueBotGroupConstraint
			}
		}
		return errors.Wrap(err, "create new bot")
	}
	return nil
}

// Get retrieves a bot by its ID.
func (b *BotsRepo) Get(ctx context.Context, botID string, withAccount bool) (*Bot, error) {
	ctx, span := b.tracer.Start(ctx, fmt.Sprintf(_spanName, "Get"))
	defer span.End()

	bc := &Bot{ID: botID}
	query := b.db.NewSelect().
		Model(bc).
		WhereAllWithDeleted().
		WherePK()

	// Conditionally apply the relation
	if withAccount {
		query.Relation("Account")
	}

	if err := query.Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrBotNotFound
		}
		return nil, errors.Wrap(err, "get bot")
	}

	return bc, nil
}

// Update modifies an existing bot.
func (b *BotsRepo) Update(ctx context.Context, bot *Bot) error {
	ctx, span := b.tracer.Start(ctx, fmt.Sprintf(_spanName, "Update"))
	defer span.End()

	if _, err := b.db.NewUpdate().
		Model(bot).
		WherePK().
		Exec(ctx); err != nil {
		return errors.Wrap(err, "update existing bot")
	}

	return nil
}

// Delete soft-deletes a bot.
func (b *BotsRepo) Delete(ctx context.Context, botID string) error {
	return errors.Errorf("not implemented")

	//ctx, span := b.tracer.Start(ctx, fmt.Sprintf(_spanName, "Delete"))
	//defer span.End()
	//
	//bot := &Bot{ID: botID}
	//r, err := b.db.NewDelete().
	//	Model(bot).
	//	Where("id = ?", botID).
	//	Exec(ctx)
	//if err != nil {
	//	return errors.Wrap(err, "deleting bot")
	//}
	//
	//return util.EnsureRowsAffected(r)
}

// ListBotsRequest represents the request parameters for listing bots with filters.
type ListBotsRequest struct {
	ginx.PaginationParam

	AccountID     string
	Symbol        string
	Status        string
	DesiredStatus string
	OrderBy       string
	IsDeleted     bool
	WithAccount   bool
}

// ListBotsResponse represents the response body for listing bots.
type ListBotsResponse struct {
	Bots  []*Bot `json:"bots"`
	Total int    `json:"total"`
}

// List retrieves a list of bots with optional filters, offset-based pagination, and a custom limit.
func (b *BotsRepo) List(ctx context.Context, filter ListBotsRequest) (*ListBotsResponse, error) {
	ctx, span := b.tracer.Start(ctx, fmt.Sprintf(_spanName, "List"))
	defer span.End()

	var bots []*Bot
	query := b.db.NewSelect().
		Model(&bots).
		Limit(filter.Limit).
		Offset(filter.Offset)

	// Conditionally apply the relation
	if filter.WithAccount {
		query.Relation("Account")
	}

	if filter.IsDeleted {
		query.WhereAllWithDeleted()
	}

	if filter.DesiredStatus != "" {
		query.Where("? ILIKE ?", bun.Ident("desired_status"), fmt.Sprintf("%%%s%%", filter.DesiredStatus))
	}

	// Apply default ordering
	defaultOrder := "created_at DESC"
	if filter.OrderBy != "" {
		defaultOrder = fmt.Sprintf("%s %s", filter.OrderBy, filter.OrderDirection)
	}
	query.OrderExpr(defaultOrder)

	if filter.AccountID != "" {
		query.Where("account_id = ?", filter.AccountID)
	}

	// Apply search-like filtering
	if filter.Symbol != "" {
		query.Where("? ILIKE ?", bun.Ident("symbol"), fmt.Sprintf("%%%s%%", filter.Symbol))
	}

	if filter.Status != "" && filter.Status != "unknown" {
		query.Where("? ILIKE ?", bun.Ident("status"), fmt.Sprintf("%%%s%%", filter.Status))
	}

	count, err := query.ScanAndCount(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list bots")
	}

	return &ListBotsResponse{Bots: bots, Total: count}, nil
}
