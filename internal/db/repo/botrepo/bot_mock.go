// Code generated by mockery. DO NOT EDIT.

package botrepo

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// IBotsMocked is an autogenerated mock type for the IBots type
type IBotsMocked struct {
	mock.Mock
}

type IBotsMocked_Expecter struct {
	mock *mock.Mock
}

func (_m *IBotsMocked) EXPECT() *IBotsMocked_Expecter {
	return &IBotsMocked_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, bot
func (_m *IBotsMocked) Create(ctx context.Context, bot *Bot) error {
	ret := _m.Called(ctx, bot)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *Bot) error); ok {
		r0 = rf(ctx, bot)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotsMocked_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type IBotsMocked_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - bot *Bot
func (_e *IBotsMocked_Expecter) Create(ctx interface{}, bot interface{}) *IBotsMocked_Create_Call {
	return &IBotsMocked_Create_Call{Call: _e.mock.On("Create", ctx, bot)}
}

func (_c *IBotsMocked_Create_Call) Run(run func(ctx context.Context, bot *Bot)) *IBotsMocked_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*Bot))
	})
	return _c
}

func (_c *IBotsMocked_Create_Call) Return(_a0 error) *IBotsMocked_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotsMocked_Create_Call) RunAndReturn(run func(context.Context, *Bot) error) *IBotsMocked_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: ctx, botID
func (_m *IBotsMocked) Delete(ctx context.Context, botID string) error {
	ret := _m.Called(ctx, botID)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, botID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotsMocked_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type IBotsMocked_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - botID string
func (_e *IBotsMocked_Expecter) Delete(ctx interface{}, botID interface{}) *IBotsMocked_Delete_Call {
	return &IBotsMocked_Delete_Call{Call: _e.mock.On("Delete", ctx, botID)}
}

func (_c *IBotsMocked_Delete_Call) Run(run func(ctx context.Context, botID string)) *IBotsMocked_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *IBotsMocked_Delete_Call) Return(_a0 error) *IBotsMocked_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotsMocked_Delete_Call) RunAndReturn(run func(context.Context, string) error) *IBotsMocked_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, botID, withAccount
func (_m *IBotsMocked) Get(ctx context.Context, botID string, withAccount bool) (*Bot, error) {
	ret := _m.Called(ctx, botID, withAccount)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *Bot
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) (*Bot, error)); ok {
		return rf(ctx, botID, withAccount)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, bool) *Bot); ok {
		r0 = rf(ctx, botID, withAccount)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Bot)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, bool) error); ok {
		r1 = rf(ctx, botID, withAccount)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IBotsMocked_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type IBotsMocked_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - botID string
//   - withAccount bool
func (_e *IBotsMocked_Expecter) Get(ctx interface{}, botID interface{}, withAccount interface{}) *IBotsMocked_Get_Call {
	return &IBotsMocked_Get_Call{Call: _e.mock.On("Get", ctx, botID, withAccount)}
}

func (_c *IBotsMocked_Get_Call) Run(run func(ctx context.Context, botID string, withAccount bool)) *IBotsMocked_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(bool))
	})
	return _c
}

func (_c *IBotsMocked_Get_Call) Return(_a0 *Bot, _a1 error) *IBotsMocked_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IBotsMocked_Get_Call) RunAndReturn(run func(context.Context, string, bool) (*Bot, error)) *IBotsMocked_Get_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: ctx, filter
func (_m *IBotsMocked) List(ctx context.Context, filter ListBotsRequest) (*ListBotsResponse, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 *ListBotsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ListBotsRequest) (*ListBotsResponse, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ListBotsRequest) *ListBotsResponse); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ListBotsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ListBotsRequest) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IBotsMocked_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type IBotsMocked_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx context.Context
//   - filter ListBotsRequest
func (_e *IBotsMocked_Expecter) List(ctx interface{}, filter interface{}) *IBotsMocked_List_Call {
	return &IBotsMocked_List_Call{Call: _e.mock.On("List", ctx, filter)}
}

func (_c *IBotsMocked_List_Call) Run(run func(ctx context.Context, filter ListBotsRequest)) *IBotsMocked_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ListBotsRequest))
	})
	return _c
}

func (_c *IBotsMocked_List_Call) Return(_a0 *ListBotsResponse, _a1 error) *IBotsMocked_List_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IBotsMocked_List_Call) RunAndReturn(run func(context.Context, ListBotsRequest) (*ListBotsResponse, error)) *IBotsMocked_List_Call {
	_c.Call.Return(run)
	return _c
}

// Update provides a mock function with given fields: ctx, bot
func (_m *IBotsMocked) Update(ctx context.Context, bot *Bot) error {
	ret := _m.Called(ctx, bot)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *Bot) error); ok {
		r0 = rf(ctx, bot)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotsMocked_Update_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Update'
type IBotsMocked_Update_Call struct {
	*mock.Call
}

// Update is a helper method to define mock.On call
//   - ctx context.Context
//   - bot *Bot
func (_e *IBotsMocked_Expecter) Update(ctx interface{}, bot interface{}) *IBotsMocked_Update_Call {
	return &IBotsMocked_Update_Call{Call: _e.mock.On("Update", ctx, bot)}
}

func (_c *IBotsMocked_Update_Call) Run(run func(ctx context.Context, bot *Bot)) *IBotsMocked_Update_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*Bot))
	})
	return _c
}

func (_c *IBotsMocked_Update_Call) Return(_a0 error) *IBotsMocked_Update_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotsMocked_Update_Call) RunAndReturn(run func(context.Context, *Bot) error) *IBotsMocked_Update_Call {
	_c.Call.Return(run)
	return _c
}

// NewIBotsMocked creates a new instance of IBotsMocked. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIBotsMocked(t interface {
	mock.TestingT
	Cleanup(func())
}) *IBotsMocked {
	mock := &IBotsMocked{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
