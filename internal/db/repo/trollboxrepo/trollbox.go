package trollboxrepo

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"

	"github.com/uptrace/bun"
)

var _spanName = "TrollboxRepository/%s"

const _ScopeName = "TrollboxRepository"

var (
	ErrMessagesNotFound    = errors.NotFound(codes.Trollbox, "message not found")
	ErrInvalidCursorFormat = errors.BadRequest(codes.Parameters, "invalid cursor format")
)

type TrollBox struct {
	bun.BaseModel `bun:"trollbox_messages,alias:tb"`

	ID        string    `json:"id" bun:",pk"`
	Sequence  int64     `json:"seq" bun:",nullzero,notnull"`
	UserID    string    `json:"userId" bun:",nullzero,notnull"`
	Content   string    `json:"content" bun:",nullzero,notnull"`
	Timestamp time.Time `json:"timestamp" bun:",nullzero,notnull"`
}

type Cursor struct {
	Timestamp time.Time
	Sequence  int64
}

func Encode(timestamp time.Time) (string, error) {
	return base64.StdEncoding.EncodeToString([]byte(timestamp.Format(time.RFC3339Nano))), nil
}

func Decode(curStr string) (*Cursor, error) {
	decodedBytes, err := base64.StdEncoding.DecodeString(curStr)
	if err != nil {
		return &Cursor{}, err
	}

	cursorStr := string(decodedBytes)

	timestamp, err := time.Parse(time.RFC3339Nano, cursorStr)
	if err != nil {
		return &Cursor{}, errors.Wrap(err, "parse message timestamp")
	}

	return &Cursor{
		Timestamp: timestamp,
	}, nil
}

type ITrollBoxRepository interface {
	// LogMessage store a new message into the database
	LogMessage(ctx context.Context, tb *TrollBox) error

	// GetMessagesByCursor retrieves a page of TrollBox messages
	// with cursor-based pagination.
	// It fetches a specified number of messages from the chat
	// history based on the provided cursor and page size.
	// The cursor is used to determine the starting
	// point for the page, and it can be a nil to fetch the first page.
	GetMessagesByCursor(ctx context.Context, curEncoded string, pageSize int) ([]*TrollBox, bool, string, error)

	// GetMessagesByTimeRange fetches chat history within a specific time range.
	GetMessagesByTimeRange(ctx context.Context, startTime, endTime time.Time) ([]*TrollBox, error)

	// GetMessagesByContent searches chat history by content.
	GetMessagesByContent(ctx context.Context, searchContent string) ([]*TrollBox, error)
}

type TrollBoxRepo struct {
	db     bun.IDB
	tracer trace.Tracer
}

func New(db bun.IDB) ITrollBoxRepository {
	return &TrollBoxRepo{db: db, tracer: otel.Tracer(_ScopeName)}
}

func (r *TrollBoxRepo) LogMessage(ctx context.Context, tb *TrollBox) error {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "LogMessage"))
	defer span.End()

	if _, err := r.db.
		NewInsert().
		Model(tb).
		Returning("*").
		Exec(ctx, tb); err != nil {
		return errors.Wrap(err, "log trollbox message")
	}

	return nil
}

func (r *TrollBoxRepo) GetMessagesByCursor(ctx context.Context, curEncoded string, pageSize int) ([]*TrollBox, bool, string, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "GetMessagesByCursor"))
	defer span.End()

	var cur *Cursor
	if curEncoded != "" {
		decodedCur, err := Decode(curEncoded)
		if err != nil {
			return nil, false, "", ErrInvalidCursorFormat
		}
		cur = decodedCur
	}

	query := r.db.NewSelect().
		Model(&TrollBox{}).
		Limit(pageSize + 1)
	if cur != nil {
		query = query.
			Where("timestamp < ?", cur.Timestamp).
			OrderExpr("timestamp DESC, sequence DESC")
	} else {
		query = query.OrderExpr("timestamp DESC, sequence DESC")
	}

	var messages []*TrollBox
	if err := query.Scan(ctx, &messages); err != nil {
		return nil, false, "", errors.Wrap(err, "scan trollbox query")
	}

	var (
		hasNextPage bool
		nextCur     string
	)
	if len(messages) > pageSize {
		lastMessage := messages[len(messages)-2]

		var err error
		nextCur, err = Encode(lastMessage.Timestamp)
		if err != nil {
			return nil, false, "", errors.Wrap(err, "encode next cursor")
		}
		hasNextPage = true

		// Remove the extra item fetched.
		messages = messages[:len(messages)-1]
	}

	return messages, hasNextPage, nextCur, nil
}

func (r *TrollBoxRepo) GetMessagesByTimeRange(ctx context.Context, startTime, endTime time.Time) ([]*TrollBox, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "GetMessagesByTimeRange"))
	defer span.End()

	query := r.db.NewSelect().Model(&TrollBox{}).
		Where("timestamp >= ?", startTime).
		Where("timestamp <= ?", endTime).
		OrderExpr("timestamp DESC")

	var messages []*TrollBox
	if err := query.Scan(ctx, &messages); err != nil {
		return nil, errors.Wrap(err, "scan trollbox message")
	}

	return messages, nil
}

func (r *TrollBoxRepo) GetMessagesByContent(ctx context.Context, searchContent string) ([]*TrollBox, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "GetMessagesByContent"))
	defer span.End()

	query := r.db.NewSelect().Model(&TrollBox{}).
		Where("LOWER(content) LIKE LOWER(?)", "%"+searchContent+"%").
		OrderExpr("timestamp DESC")

	var messages []*TrollBox
	if _, err := query.ScanAndCount(ctx, &messages); err != nil {
		return nil, errors.Wrap(err, "get trollbox messages")
	}

	return messages, nil
}
