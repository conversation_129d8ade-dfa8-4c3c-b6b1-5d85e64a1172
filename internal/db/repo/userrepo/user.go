package userrepo

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"go.opentelemetry.io/otel"

	"github.com/uptrace/bun"
	"go.opentelemetry.io/otel/trace"
)

var _spanName = "UserRepository/%s"

const _ScopeName = "UserRepository"

var (
	ErrUsersNotFound = errors.NotFound(codes.Users, "user not found")
)

type User struct {
	bun.BaseModel `bun:"users,alias:u"`

	ID        string    `bun:",pk,type:uuid,default:gen_random_uuid()"`
	Name      string    `bun:",nullzero"`
	Email     string    `bun:",unique,nullzero"`
	Password  string    `bun:",nullzero"`
	Role      string    `bun:",nullzero"`
	CreatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
	UpdatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
}

type IUserRepository interface {
	Get(ctx context.Context, userID string) (*User, error)
	ByEmail(ctx context.Context, email string) (*User, error)
	Create(ctx context.Context, user *User) error
	List(ctx context.Context) ([]User, error)
	UpdatePassword(ctx context.Context, userID string, newPassword string) error
}

type UserRepo struct {
	db    bun.IDB
	trace trace.Tracer
}

func New(db bun.IDB) *UserRepo {
	return &UserRepo{db: db, trace: otel.Tracer(_ScopeName)}
}

func (u *UserRepo) Get(ctx context.Context, userID string) (*User, error) {
	ctx, span := u.trace.Start(ctx, fmt.Sprintf(_spanName, "Get"))
	defer span.End()

	user := new(User)
	if err := u.db.NewSelect().
		Model(user).
		Where("id = ?", userID).
		Scan(ctx); err != nil {
		return nil, errors.Wrap(err, "get user")
	}
	return user, nil
}

func (u *UserRepo) ByEmail(ctx context.Context, email string) (*User, error) {
	ctx, span := u.trace.Start(ctx, fmt.Sprintf(_spanName, "ByEmail"))
	defer span.End()

	user := new(User)
	if err := u.db.NewSelect().
		Model(user).
		Where("email = ?", email).
		Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrUsersNotFound
		}
		return nil, errors.Wrap(err, "get user by email")
	}
	return user, nil
}

func (u *UserRepo) Create(ctx context.Context, user *User) error {
	ctx, span := u.trace.Start(ctx, fmt.Sprintf(_spanName, "Create"))
	defer span.End()

	if _, err := u.db.NewInsert().
		Model(user).
		Exec(ctx); err != nil {
		return errors.Wrap(err, "create user")
	}
	return nil
}

func (u *UserRepo) List(ctx context.Context) ([]User, error) {
	ctx, span := u.trace.Start(ctx, fmt.Sprintf(_spanName, "List"))
	defer span.End()

	var users []User
	if _, err := u.db.NewSelect().
		Model(&users).
		ScanAndCount(ctx); err != nil {
		return nil, errors.Wrap(err, "list users info")
	}

	return users, nil
}

func (u *UserRepo) UpdatePassword(ctx context.Context, userID string, newPassword string) error {
	ctx, span := u.trace.Start(ctx, fmt.Sprintf(_spanName, "UpdatePassword"))
	defer span.End()

	_, err := u.db.NewUpdate().
		Model(&User{}).
		Set("password = ?", newPassword).
		Where("id = ?", userID).
		Exec(ctx)
	if err != nil {
		return errors.Wrap(err, "update password")
	}

	return nil
}
