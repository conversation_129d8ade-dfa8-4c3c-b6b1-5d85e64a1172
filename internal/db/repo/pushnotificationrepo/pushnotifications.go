package pushnotificationrepo

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/uptrace/bun"
	"github.com/uptrace/bun/driver/pgdriver"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/pkg/util"
)

var _spanName = "PushNotificationsRepository/%s"

const _ScopeName = "PushNotificationsRepository"

var (
	ErrNotificationNotFound   = errors.NotFound(codes.PushNotifications, "push notification device not found")
	ErrNotificationNotDeleted = errors.NotFound(codes.PushNotifications, "push notification device not deleted")
	ErrDuplicatedDeviceToken  = errors.BadRequest(codes.PushNotifications, "duplicate key value violates unique device_token constraint")
	ErrInvalidUserID          = errors.BadRequest(codes.PushNotifications, "user notification device not exists")
)

// PushNotification represents the push notification model.
type PushNotification struct {
	bun.BaseModel `bun:"push_notifications,alias:pn"`

	ID          string    `bun:",pk,type:uuid,default:gen_random_uuid"`
	UserID      string    `bun:",type:uuid,notnull"`
	DeviceToken string    `bun:",notnull"`
	Platform    string    `bun:",notnull"` // web, ios, android
	CreatedAt   time.Time `bun:",notnull,default:current_timestamp"`
	UpdatedAt   time.Time `bun:",notnull,default:current_timestamp"`
}

// INotificationsRepo defines the methods for managing push notification device tokens.
type INotificationsRepo interface {
	// CreateOrUpdate adds or update a new notification.
	CreateOrUpdate(ctx context.Context, notification *PushNotification) error

	// GetByDeviceToken retrieves a push notification by its device token.
	GetByDeviceToken(ctx context.Context, deviceToken string) (*PushNotification, error)

	// DeleteByDeviceToken hard-delete a push notification by device token.
	DeleteByDeviceToken(ctx context.Context, deviceToken string) error

	// List retrieves a list of push notifications with optional filters,
	// offset-based pagination, and a custom limit.
	List(ctx context.Context, filter ListPushNotificationsRequest) (*ListPushNotificationsResponse, error)

	// ListByUserIDs retrieves push notifications for a given array of user IDs.
	ListByUserIDs(ctx context.Context, userIDs []string) ([]*PushNotification, error)
}

type PushNotificationsRepo struct {
	db     bun.IDB
	tracer trace.Tracer
}

// New creates a new instance of PushNotificationsRepo.
func New(db bun.IDB) *PushNotificationsRepo {
	return &PushNotificationsRepo{
		db:     db,
		tracer: otel.Tracer(_ScopeName),
	}
}

// CreateOrUpdate adds or update a new push notification device.
func (b *PushNotificationsRepo) CreateOrUpdate(ctx context.Context, notification *PushNotification) error {
	ctx, span := b.tracer.Start(ctx, fmt.Sprintf(_spanName, "CreateOrUpdate"))
	defer span.End()

	_, err := b.db.NewInsert().
		Model(notification).
		On("CONFLICT (device_token, user_id, platform) DO UPDATE").
		Set("platform = EXCLUDED.platform").
		Returning("*").
		Exec(ctx, notification)
	if err != nil {
		var pgErr pgdriver.Error
		if ok := errors.As(err, &pgErr); ok {
			switch pgErr.Field('C') {
			case "23503": // user id not exists
				return ErrInvalidUserID
			case "23505": // duplicated key
				return ErrDuplicatedDeviceToken
			}
		}
		return errors.Wrap(err, "create or update push notification device")
	}
	return nil
}

// GetByDeviceToken retrieves a push notification by its device token.
func (b *PushNotificationsRepo) GetByDeviceToken(ctx context.Context, deviceToken string) (*PushNotification, error) {
	ctx, span := b.tracer.Start(ctx, fmt.Sprintf(_spanName, "GetByDeviceToken"))
	defer span.End()

	var notification PushNotification
	err := b.db.NewSelect().
		Model(&notification).
		Where("device_token = ?", deviceToken).
		Limit(1).
		Scan(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrNotificationNotFound
		}
		return nil, errors.Wrap(err, "get push notification device")
	}

	return &notification, nil
}

// DeleteByDeviceToken hard-delete a push notification by device token.
func (b *PushNotificationsRepo) DeleteByDeviceToken(ctx context.Context, deviceToken string) error {
	ctx, span := b.tracer.Start(ctx, fmt.Sprintf(_spanName, "DeleteByDeviceToken"))
	defer span.End()

	var deviceToBeDeleted *PushNotification
	if _, err := b.db.NewSelect().
		Model(deviceToBeDeleted).
		Where("device_token = ?", deviceToken).
		Exec(ctx); err != nil {
		return errors.Wrap(err, "select device token")
	}

	if deviceToBeDeleted == nil {
		return ErrNotificationNotFound
	}

	r, err := b.db.NewDelete().
		Model(&PushNotification{}).
		Where("device_token = ?", deviceToken).
		Exec(ctx)
	if err != nil {
		return errors.Wrap(err, "delete device token")
	}

	return util.EnsureRowsAffected(r)
}

// ListPushNotificationsRequest represents the request parameters for listing notifications with filters.
type ListPushNotificationsRequest struct {
	UserID         string
	Platform       string
	OrderBy        string
	OrderDirection string
	Offset         int
	Limit          int
}

// ListPushNotificationsResponse represents the response body for listing push notifications.
type ListPushNotificationsResponse struct {
	Notifications []PushNotification `json:"pushNotifications"`
	Total         int                `json:"total"`
}

// List retrieves a list of notifications with optional filters, offset-based pagination, and a custom limit.
func (b *PushNotificationsRepo) List(ctx context.Context, filter ListPushNotificationsRequest) (*ListPushNotificationsResponse, error) {
	ctx, span := b.tracer.Start(ctx, fmt.Sprintf(_spanName, "List"))
	defer span.End()

	var notifications []PushNotification
	query := b.db.NewSelect().
		Model(&notifications).
		Where("user_id = ?", filter.UserID).
		Limit(filter.Limit).
		Offset(filter.Offset)

	// Apply default ordering
	defaultOrder := "created_at DESC"
	if filter.OrderBy != "" {
		defaultOrder = fmt.Sprintf("%s %s", filter.OrderBy, filter.OrderDirection)
	}
	query.OrderExpr(defaultOrder)

	// Apply search-like filtering
	if filter.Platform != "" {
		query.Where("platform = ?", filter.Platform)
	}

	count, err := query.ScanAndCount(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list push notification devices")
	}

	return &ListPushNotificationsResponse{Notifications: notifications, Total: count}, nil
}

// ListByUserIDs retrieves push notifications for a given array of user IDs.
func (b *PushNotificationsRepo) ListByUserIDs(ctx context.Context, userIDs []string) ([]*PushNotification, error) {
	ctx, span := b.tracer.Start(ctx, fmt.Sprintf(_spanName, "ListByUserIDs"))
	defer span.End()

	var notifications []*PushNotification
	err := b.db.NewSelect().
		Model(&notifications).
		Where("user_id IN ?", bun.In(userIDs)).
		Scan(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list push notification devices")
	}

	return notifications, nil
}
