package sessionrepo

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/auth/session"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/pkg/util"

	"github.com/uptrace/bun"
)

var _spanName = "SessionRepository/%s"

const _ScopeName = "SessionRepository"

var (
	ErrSessionNotFound  = errors.NotFound(codes.Sessions, "session not found")
	ErrNoActiveSessions = errors.NotFound(codes.Sessions, "no active sessions")
	ErrExpiredSession   = errors.NotFound(codes.Sessions, "session expired or deleted")
)

type ISessionRepository interface {
	ListByUserID(ctx context.Context, userID string) (*[]session.Session, error)
	DeleteAll(ctx context.Context, userID string) error
	Delete(ctx context.Context, userID string, id int32) error
	Get(ctx context.Context, userID string, id int32) (*session.Session, error)
}

// SessionRepo represents the Session store.
type SessionRepo struct {
	db     bun.IDB
	tracer trace.Tracer
}

func New(db bun.IDB) ISessionRepository {
	return &SessionRepo{
		db:     db,
		tracer: otel.Tracer(_ScopeName),
	}
}

// ListByUserID returns the list of sessions for a given userID from the SessionRepo instance.
// If the Session token is not found or is expired, ErrSessionNotFound will be returned.
func (sr *SessionRepo) ListByUserID(ctx context.Context, userID string) (*[]session.Session, error) {
	ctx, span := sr.tracer.Start(ctx, fmt.Sprintf(_spanName, "ListByUserID"))
	defer span.End()

	ss := &[]session.Session{}
	if _, err := sr.db.NewSelect().
		Model(ss).
		Where("user_id = ? AND expiry >= ?", userID, time.Now()).
		ScanAndCount(ctx); err != nil {
		return &[]session.Session{}, errors.Wrap(err, "failed to list sessions")
	}

	return ss, nil
}

// DeleteAll expiry all Session's by given userID.
func (sr *SessionRepo) DeleteAll(ctx context.Context, userID string) error {
	ctx, span := sr.tracer.Start(ctx, fmt.Sprintf(_spanName, "DeleteAll"))
	defer span.End()

	r, err := sr.db.NewUpdate().
		Model(&session.Session{}).
		Set("expiry = ?", time.Now()).
		Where("user_id = ? AND expiry >= ?", userID, time.Now()).
		Exec(ctx)
	if err != nil {
		return errors.Wrap(err, "delete sessions")
	}

	return util.EnsureRowsAffected(r)
}

// Delete expiry a Session's by given session id.
func (sr *SessionRepo) Delete(ctx context.Context, userID string, id int32) error {
	ctx, span := sr.tracer.Start(ctx, fmt.Sprintf(_spanName, "Delete"))
	defer span.End()

	r, err := sr.db.NewUpdate().
		Model(&session.Session{}).
		Set("expiry = ?", time.Now()).
		Where("user_id = ? AND id = ? AND expiry >= ?", userID, id, time.Now()).
		Exec(ctx)
	if err != nil {
		return errors.Wrap(err, "delete session")
	}

	return util.EnsureRowsAffected(r)
}

// Get returns the data for a given Session id.
// If the Session id is not found, returns ErrSessionNotFound or
//
//	if it is expired, ErrExpiredSession will be returned.
func (sr *SessionRepo) Get(ctx context.Context, userID string, id int32) (*session.Session, error) {
	ctx, span := sr.tracer.Start(ctx, fmt.Sprintf(_spanName, "Get"))
	defer span.End()

	s := &session.Session{}
	_, err := sr.db.NewSelect().
		Model(s).
		Where("user_id = ? AND id = ?", userID, id).
		Exec(ctx, s)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return &session.Session{}, ErrSessionNotFound
		}
		return &session.Session{}, errors.Wrap(err, "get session")
	}

	if s.Expiry.Unix() <= time.Now().Unix() {
		return &session.Session{}, ErrExpiredSession
	}

	return s, nil
}
