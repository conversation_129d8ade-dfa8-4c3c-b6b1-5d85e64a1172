package notificationsrepo

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/pkg/util"

	"github.com/uptrace/bun"
	"github.com/uptrace/bun/driver/pgdriver"
)

var _subscriptionsSpanName = "NotificationSubscriptionsRepository/%s"

const _SubScopeName = "NotificationSubscriptionsRepository"

var (
	ErrNotificationSubscriptionNotFound = errors.NotFound(codes.NotificationSubscriptions, "notification subscriptions not found")
)

// NotificationSubscription represents the notification subscriptions model.
type NotificationSubscription struct {
	bun.BaseModel `bun:"notification_subscriptions,alias:ns"`

	ID         int       `bun:",pk,notnull,autoincrement"`
	EventType  string    `bun:",notnull,unique:unique_user_event_resource"`
	UserID     string    `bun:",notnull,unique:unique_user_event_resource"`
	ResourceID string    `bun:",notnull,unique:unique_user_event_resource"`
	CreatedAt  time.Time `bun:",notnull,default:current_timestamp"`
}

// INotificationSubscriptionsRepo defines the methods for managing notification subscriptions.
type INotificationSubscriptionsRepo interface {
	// Create adds a new notification subscription.
	Create(ctx context.Context, notifications *NotificationSubscription) error

	// BulkCreate adds multiple new notification subscriptions.
	BulkCreate(ctx context.Context, notifications *[]NotificationSubscription) error

	// Get retrieves a notification subscription by its ID.
	Get(ctx context.Context, userID string, subID int) (*NotificationSubscription, error)

	// List retrieves a list of notification subscriptions with optional filters, offset-based pagination, and a custom limit.
	List(ctx context.Context, filter ListNotificationSubscriptionsRequest) (*ListNotificationSubscriptionsResponse, error)

	// Delete deletes a user notification subscription by subscription id.
	Delete(ctx context.Context, userID string, subID int) error

	// BulkDelete deletes multiple user notification subscriptions by its ids.
	BulkDelete(ctx context.Context, userID string, subIDs []int) error
}

type NotificationSubscriptionsRepo struct {
	db     bun.IDB
	tracer trace.Tracer
}

// NewNotificationSubscriptions creates a new instance of NotificationSubscriptionsRepo.
func NewNotificationSubscriptions(db bun.IDB) *NotificationSubscriptionsRepo {
	return &NotificationSubscriptionsRepo{
		db:     db,
		tracer: otel.Tracer(_SubScopeName),
	}
}

// Create adds a new notification subscription.
func (n *NotificationSubscriptionsRepo) Create(ctx context.Context, sub *NotificationSubscription) error {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_subscriptionsSpanName, "Create"))
	defer span.End()

	if _, err := n.db.NewInsert().
		Model(sub).
		Exec(ctx, sub); err != nil {
		var pgErr pgdriver.Error
		if errors.As(err, &pgErr) && pgErr.IntegrityViolation() {
			switch pgErr.Field('n') {
			case "unique_user_event_resource":
				return errors.BadRequest(codes.NotificationSubscriptions, "user with the same combination of event and resource already exists")
			}
		}
		return errors.Wrap(err, "create user notification subscription")
	}

	return nil
}

// BulkCreate adds multiple new notification subscriptions.
func (n *NotificationSubscriptionsRepo) BulkCreate(ctx context.Context, subs *[]NotificationSubscription) error {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_subscriptionsSpanName, "BulkCreate"))
	defer span.End()

	if _, err := n.db.NewInsert().
		Model(subs).
		Returning("*").
		Exec(ctx); err != nil {
		return errors.Wrap(err, "create user notification subscriptions")
	}

	return nil
}

// Get retrieves a notification by its ID.
func (n *NotificationSubscriptionsRepo) Get(ctx context.Context, userID string, id int) (*NotificationSubscription, error) {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_subscriptionsSpanName, "Get"))
	defer span.End()

	sub := &NotificationSubscription{ID: id}
	if err := n.db.NewSelect().
		Where("user_id = ?", userID).
		Model(sub).
		Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrNotificationSubscriptionNotFound
		}
		return nil, errors.Wrap(err, "get user notification subscription")
	}

	return sub, nil
}

// ListNotificationSubscriptionsRequest represents the request parameters for listing notification subscriptions with filters.
type ListNotificationSubscriptionsRequest struct {
	OrderBy        string
	OrderDirection string
	Offset         int
	Limit          int
	ID             int
	EventType      string
	UserID         string
	ResourceID     string
}

// ListNotificationSubscriptionsResponse represents the response body for listing notification subscriptions.
type ListNotificationSubscriptionsResponse struct {
	NotificationSubscriptions []*NotificationSubscription `json:"notificationSubscriptions"`
	Total                     int                         `json:"total"`
}

// List retrieves a list of notification subscriptions with optional filters, offset-based pagination, and a custom limit.
func (n *NotificationSubscriptionsRepo) List(ctx context.Context, filter ListNotificationSubscriptionsRequest) (*ListNotificationSubscriptionsResponse, error) {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_subscriptionsSpanName, "List"))
	defer span.End()

	var subs []*NotificationSubscription
	query := n.db.NewSelect().
		Model(&subs).
		Where("user_id = ?", filter.UserID).
		Limit(filter.Limit).
		Offset(filter.Offset)

	// Apply default ordering
	defaultOrder := "created_at DESC"
	if filter.OrderBy != "" {
		defaultOrder = fmt.Sprintf("%s %s", filter.OrderBy, filter.OrderDirection)
	}
	query.OrderExpr(defaultOrder)

	if filter.ID > 0 {
		query.Where("id = ?", filter.ID)
	}

	// Apply search-like filtering
	if filter.EventType != "" {
		query.Where("event_type = ?", filter.EventType)
	}

	if filter.ResourceID != "" {
		query.Where("resource_id = ?", filter.ResourceID)
	}

	count, err := query.ScanAndCount(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list user notification subscriptions")
	}

	return &ListNotificationSubscriptionsResponse{NotificationSubscriptions: subs, Total: count}, nil
}

// Delete deletes a user notification subscription by subscription id.
func (n *NotificationSubscriptionsRepo) Delete(ctx context.Context, userID string, subID int) error {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_subscriptionsSpanName, "Delete"))
	defer span.End()

	var sub = &NotificationSubscription{ID: subID}
	r, err := n.db.NewDelete().
		Model(sub).
		Where("user_id = ?", userID).
		WherePK().
		Exec(ctx)
	if err != nil {
		return errors.Wrap(err, "delete user notification subscription")
	}

	return util.EnsureRowsAffected(r)
}

// BulkDelete deletes multiple user notification subscriptions by its ids.
func (n *NotificationSubscriptionsRepo) BulkDelete(ctx context.Context, userID string, subIDs []int) error {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_subscriptionsSpanName, "BulkDelete"))
	defer span.End()

	var subs = make([]*NotificationSubscription, len(subIDs))
	for i, id := range subIDs {
		subs[i] = &NotificationSubscription{ID: id}
	}

	r, err := n.db.NewDelete().
		Model(&subs).
		Where("user_id = ?", userID).
		WherePK().
		Exec(ctx)
	if err != nil {
		return errors.Wrap(err, "delete user notification subscriptions")
	}

	return util.EnsureRowsAffected(r)
}
