package notificationsrepo

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/uptrace/bun"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/pkg/util"
)

var _logSpanName = "NotificationLogRepository/%s"

const _LogScopeName = "NotificationLogRepository"

var ErrNotificationLogNotFound = errors.NotFound(codes.NotificationLogs, "notification log not found")

// NotificationLog represents the notification log model.
type NotificationLog struct {
	bun.BaseModel `bun:"notification_logs,alias:nl"`

	ID        int            `json:"id" bun:",pk,notnull,autoincrement"`
	EventType string         `json:"eventType" bun:",notnull"`
	Title     string         `json:"title" bun:",notnull"`
	Content   string         `json:"content" bun:",notnull"`
	UserID    string         `json:"userId" bun:",notnull"`
	Metadata  map[string]any `json:"metadata" bun:",type:jsonb"`
	SentAt    time.Time      `json:"sentAt" bun:",notnull"`
}

// INotificationLogsRepo defines the methods for managing notification logs.
type INotificationLogsRepo interface {
	// Create adds a new notification log.
	Create(ctx context.Context, notificationLog *NotificationLog) error

	// BulkCreate adds multiple new notification logs.
	BulkCreate(ctx context.Context, notificationLogs []*NotificationLog) error

	// Get retrieves a notification log by its ID.
	Get(ctx context.Context, notificationLogID int) (*NotificationLog, error)

	// List retrieves a list of notification logs with optional filters, offset-based pagination, and a custom limit.
	List(ctx context.Context, filter ListNotificationLogsRequest) (*ListNotificationLogsResponse, error)

	// Delete deletes a notification log.
	Delete(ctx context.Context, notificationLogID int) error
}

type NotificationLogsRepo struct {
	db     bun.IDB
	tracer trace.Tracer
}

// NewNotificationLogs creates a new instance of NotificationLogsRepo.
func NewNotificationLogs(db bun.IDB) *NotificationLogsRepo {
	return &NotificationLogsRepo{
		db:     db,
		tracer: otel.Tracer(_LogScopeName),
	}
}

// Create adds a new notification log.
func (n *NotificationLogsRepo) Create(ctx context.Context, notificationLog *NotificationLog) error {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_logSpanName, "Create"))
	defer span.End()

	if _, err := n.db.NewInsert().
		Model(notificationLog).
		Returning("*").
		Exec(ctx); err != nil {
		return errors.Wrap(err, "create new log")
	}

	return nil
}

// BulkCreate adds multiple new notification logs.
func (n *NotificationLogsRepo) BulkCreate(ctx context.Context, notificationLogs []*NotificationLog) error {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_logSpanName, "BulkCreate"))
	defer span.End()

	if _, err := n.db.NewInsert().
		Model(notificationLogs).
		Returning("*").
		Exec(ctx); err != nil {
		return errors.Wrap(err, "create new logs")
	}

	return nil
}

// Get retrieves a notification log by its ID.
func (n *NotificationLogsRepo) Get(ctx context.Context, notificationLogID int) (*NotificationLog, error) {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_logSpanName, "Get"))
	defer span.End()

	notificationLog := &NotificationLog{ID: notificationLogID}
	if err := n.db.NewSelect().
		Model(notificationLog).
		Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrNotificationLogNotFound
		}
		return nil, errors.Wrap(err, "get log")
	}

	return notificationLog, nil
}

// ListNotificationLogsRequest represents the request parameters for listing notification logs with filters.
type ListNotificationLogsRequest struct {
	EventType      string
	UserID         string
	OrderBy        string
	OrderDirection string
	Offset         int
	Limit          int
}

// ListNotificationLogsResponse represents the response body for listing notification logs.
type ListNotificationLogsResponse struct {
	Notifications []*NotificationLog `json:"notifications"`
	Total         int                `json:"total"`
}

// List retrieves a list of notification logs with optional filters, offset-based pagination, and a custom limit.
func (n *NotificationLogsRepo) List(ctx context.Context, filter ListNotificationLogsRequest) (*ListNotificationLogsResponse, error) {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_logSpanName, "List"))
	defer span.End()

	var notificationLogs []*NotificationLog
	query := n.db.NewSelect().
		Model(&notificationLogs).
		Limit(filter.Limit).
		Offset(filter.Offset)

	// Apply default ordering
	defaultOrder := "sent_at DESC"
	if filter.OrderBy != "" {
		defaultOrder = fmt.Sprintf("%s %s", filter.OrderBy, filter.OrderDirection)
	}
	query.OrderExpr(defaultOrder)

	// Apply search-like filtering
	if filter.EventType != "" {
		query.Where("event_type = ?", filter.EventType)
	}

	if filter.UserID != "" {
		query.Where("user_id = ?", filter.UserID)
	}

	count, err := query.ScanAndCount(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list logs")

	}

	return &ListNotificationLogsResponse{Notifications: notificationLogs, Total: count}, nil
}

// Delete deletes a notification log.
func (n *NotificationLogsRepo) Delete(ctx context.Context, notificationLogID int) error {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_logSpanName, "Delete"))
	defer span.End()

	notificationLog := &NotificationLog{ID: notificationLogID}
	r, err := n.db.NewDelete().
		Model(notificationLog).
		WherePK().
		Exec(ctx)
	if err != nil {
		return errors.Wrap(err, "deleting log")
	}

	return util.EnsureRowsAffected(r)
}
