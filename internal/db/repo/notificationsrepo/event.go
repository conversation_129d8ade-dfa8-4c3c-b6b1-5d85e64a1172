package notificationsrepo

import (
	"context"
	"fmt"

	"github.com/uptrace/bun"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/pkg/util"
)

var _eventSpanName = "NotificationEventRepository/%s"

const _EventScopeName = "NotificationEventRepository"

// NotificationEvent represents the notification event model.
type NotificationEvent struct {
	bun.BaseModel `bun:"notification_events,alias:ne"`

	ID        int    `json:"id" bun:",pk,notnull,autoincrement"`
	EventType string `json:"eventType" bun:",notnull"`
	EventKey  string `json:"eventKey" bun:",notnull"`
	UserID    string `json:"userId" bun:",notnull"`
}

// INotificationEventsRepo defines the methods for managing notification events.
type INotificationEventsRepo interface {
	// BulkCreate adds new notification events.
	BulkCreate(ctx context.Context, notificationEvents *[]NotificationEvent) error

	// List retrieves a list of notification events with optional filters, offset-based pagination, and a custom limit.
	List(ctx context.Context, filter ListNotificationEventsRequest) (*ListNotificationEventsResponse, error)

	// Delete deletes a notification event.
	Delete(ctx context.Context, notificationEventID int) error

	// ListUnnoticedEvents retrieves unmatched notification events based on the provided ListUnnoticedEventsReq values.
	ListUnnoticedEvents(ctx context.Context, events []ListUnnoticedEventsReq) ([]NotificationEvent, error)
}

type NotificationEventsRepo struct {
	db     bun.IDB
	tracer trace.Tracer
}

// NewNotificationEvents creates a new instance of NotificationEventsRepo.
func NewNotificationEvents(db bun.IDB) *NotificationEventsRepo {
	return &NotificationEventsRepo{
		db:     db,
		tracer: otel.Tracer(_EventScopeName),
	}
}

// BulkCreate adds a new notification event.
func (n *NotificationEventsRepo) BulkCreate(ctx context.Context, notificationEvents *[]NotificationEvent) error {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_eventSpanName, "BulkCreate"))
	defer span.End()

	if _, err := n.db.NewInsert().
		Model(notificationEvents).
		Returning("*").
		Exec(ctx); err != nil {
		return errors.Wrap(err, "create notification events")
	}

	return nil
}

// ListNotificationEventsRequest represents the request parameters for listing notification events with filters.
type ListNotificationEventsRequest struct {
	EventType      string
	UserID         string
	OrderBy        string
	OrderDirection string
	Offset         int
	Limit          int
}

// ListNotificationEventsResponse represents the response body for listing notification events.
type ListNotificationEventsResponse struct {
	NotificationEvents []*NotificationEvent `json:"notificationEvents"`
	Total              int                  `json:"total"`
}

// List retrieves a list of notification events with optional filters, offset-based pagination, and a custom limit.
func (n *NotificationEventsRepo) List(ctx context.Context, filter ListNotificationEventsRequest) (*ListNotificationEventsResponse, error) {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_eventSpanName, "List"))
	defer span.End()

	var notificationEvents []*NotificationEvent
	query := n.db.NewSelect().
		Model(&notificationEvents).
		Limit(filter.Limit).
		Offset(filter.Offset)

	// Apply default ordering
	defaultOrder := "id DESC"
	if filter.OrderBy != "" {
		defaultOrder = fmt.Sprintf("%s %s", filter.OrderBy, filter.OrderDirection)
	}
	query.OrderExpr(defaultOrder)

	// Apply search-like filtering
	if filter.EventType != "" {
		query.Where("event_type = ?", filter.EventType)
	}

	if filter.UserID != "" {
		query.Where("user_id = ?", filter.UserID)
	}

	count, err := query.ScanAndCount(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list notification events")
	}

	return &ListNotificationEventsResponse{NotificationEvents: notificationEvents, Total: count}, nil
}

// Delete deletes a notification event.
func (n *NotificationEventsRepo) Delete(ctx context.Context, notificationEventID int) error {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_eventSpanName, "Delete"))
	defer span.End()

	notificationEvent := &NotificationEvent{ID: notificationEventID}
	r, err := n.db.NewDelete().
		Model(notificationEvent).
		WherePK().
		Exec(ctx)
	if err != nil {
		return errors.Wrap(err, "delete notification event")
	}

	return util.EnsureRowsAffected(r)
}

type ListUnnoticedEventsReq struct {
	EventType string `json:"eventType" bun:","`
	EventKey  string `json:"eventKey" bun:","`
	UserID    string `json:"userId" bun:",type:UUID"`
}

// ListUnnoticedEvents retrieves unmatched notification events based on the provided values.
func (n *NotificationEventsRepo) ListUnnoticedEvents(ctx context.Context, evs []ListUnnoticedEventsReq) ([]NotificationEvent, error) {
	ctx, span := n.tracer.Start(ctx, fmt.Sprintf(_eventSpanName, "ListUnmatchedEvents"))
	defer span.End()

	var unnoticedEvents []NotificationEvent
	if _, err := n.db.NewSelect().
		With("ue", n.db.NewValues(&evs)).
		Column("ue.event_type").
		Column("ue.user_id").
		Column("ue.event_key").
		Table("ue").
		Join("LEFT JOIN notification_events as ne").
		JoinOn("ue.user_id = ne.user_id").
		JoinOn("ue.event_key = ne.event_key").
		JoinOn("ue.event_type = ne.event_type").
		Where("ne.user_id isnull AND ne.event_key isnull").
		Exec(ctx, &unnoticedEvents); err != nil {
		return nil, errors.Wrap(err, "listing unnoticed notification events")
	}

	return unnoticedEvents, nil
}
