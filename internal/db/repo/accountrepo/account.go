package accountrepo

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/uptrace/bun"
	"github.com/uptrace/bun/driver/pgdriver"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/db/repo/exchangerepo"
	"github.com/herenow/atomic-bm/internal/db/repo/regionsrepo"
	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
)

var _spanName = "AccountRepository/%s"

const _ScopeName = "AccountRepository"

var (
	ErrAccountNotFound = errors.NotFound(codes.Accounts, "account not found")

	// ErrUniqueAccountGroupConstraint is a constant error for the unique_bot_group constraint violation.
	ErrUniqueAccountGroupConstraint = errors.BadRequest(codes.Accounts, "account with this region and tag already exists")
)

// Account represents the account model
// accounts are the responsible for managing
// the bot credentials and exchanges.
type Account struct {
	bun.BaseModel `bun:"accounts,alias:a"`

	ID         string     `bun:",pk,type:uuid,default:gen_random_uuid"`
	ExchangeID string     `bun:",unique:accounts_unique_exchange_and_region_tag_null,notnull"`
	Tag        string     `bun:",unique:accounts_unique_exchange_and_region_tag_null,notnull"`
	RegionID   string     `bun:",unique:accounts_unique_exchange_and_region_tag_null,notnull"`
	CreatedAt  time.Time  `bun:",notnull,default:current_timestamp"`
	UpdatedAt  time.Time  `bun:",notnull,default:current_timestamp"`
	DeletedAt  *time.Time `bun:",soft_delete,nullzero"`

	Exchange exchangerepo.Exchange `bun:"rel:belongs-to,join:exchange_id=id"`
	Region   regionsrepo.Region    `bun:"rel:belongs-to,join:region_id=id"`
}

// IAccountsRepo defines the methods for managing accounts.
type IAccountsRepo interface {
	// Create adds a new account.
	Create(ctx context.Context, acc *Account) error

	// Get retrieves an account by its ID.
	Get(ctx context.Context, accID string) (*Account, error)

	// List retrieves a list of accounts.
	List(ctx context.Context, filter ListAccountsRequest) (*ListAccountsResponse, error)

	// Delete soft-deletes a account.
	Delete(ctx context.Context, accID string) error
}

type AccountRepo struct {
	db     bun.IDB
	tracer trace.Tracer
}

// New creates a new instance of BotParamsRepo.
func New(db bun.IDB) *AccountRepo {
	return &AccountRepo{db: db, tracer: otel.Tracer(_ScopeName)}
}

// Create add a new account.
func (a *AccountRepo) Create(ctx context.Context, acc *Account) error {
	ctx, span := a.tracer.Start(ctx, fmt.Sprintf(_spanName, "Create"))
	defer span.End()

	if _, err := a.db.NewInsert().
		Model(acc).
		Returning("*").
		Exec(ctx, acc); err != nil {
		var pgErr pgdriver.Error
		if errors.As(err, &pgErr) && pgErr.IntegrityViolation() {
			switch pgErr.Field('n') {
			case "accounts_unique_exchange_region_and_tag_null":
				return ErrUniqueAccountGroupConstraint
			}
		}
		return err
	}

	return nil
}

// Get retrieves an account by its ID.
func (a *AccountRepo) Get(ctx context.Context, accID string) (*Account, error) {
	ctx, span := a.tracer.Start(ctx, fmt.Sprintf(_spanName, "Get"))
	defer span.End()

	acc := &Account{ID: accID}
	if err := a.db.NewSelect().
		Model(acc).
		WhereAllWithDeleted().
		WherePK().
		Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrAccountNotFound
		}
		return nil, err
	}

	if acc == nil {
		return nil, ErrAccountNotFound
	}

	return acc, nil
}

// ListAccountsRequest represents the request parameters for listing accounts with filters.
type ListAccountsRequest struct {
	ginx.PaginationParam
	IsDeleted bool
	OrderBy   string

	ExchangeID string
	RegionID   string
	Tag        string
}

// ListAccountsResponse represents the response body for listing accounts.
type ListAccountsResponse struct {
	Accounts []*Account `json:"accounts"`
	Total    int32      `json:"total"`
}

// List retrieves a list of accounts with optional filters, offset-based pagination, and a custom limit.
func (a *AccountRepo) List(ctx context.Context, filter ListAccountsRequest) (*ListAccountsResponse, error) {
	ctx, span := a.tracer.Start(ctx, fmt.Sprintf(_spanName, "List"))
	defer span.End()

	var accs []*Account
	query := a.db.NewSelect().
		Model(&accs).
		Limit(filter.Limit).
		Offset(filter.Offset)

	if filter.IsDeleted {
		query.WhereAllWithDeleted()
	}

	// Apply default ordering
	defaultOrder := "created_at DESC"
	if filter.OrderBy != "" {
		defaultOrder = fmt.Sprintf("%s %s", filter.OrderBy, filter.OrderDirection)
	}
	query.OrderExpr(defaultOrder)

	// Apply search-like filtering
	if filter.ExchangeID != "" {
		query.Where("? ILIKE ?", bun.Ident("exchange_id"), fmt.Sprintf("%%%s%%", filter.ExchangeID))
	}

	if filter.RegionID != "" {
		query.Where("? ILIKE ?", bun.Ident("region_id"), fmt.Sprintf("%%%s%%", filter.RegionID))
	}

	if filter.Tag != "" {
		query.Where("? ILIKE ?", bun.Ident("tag"), fmt.Sprintf("%%%s%%", filter.Tag))
	}

	count, err := query.ScanAndCount(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list accounts")
	}

	return &ListAccountsResponse{Accounts: accs, Total: int32(count)}, nil
}

// Delete soft-deletes an account.
func (a *AccountRepo) Delete(ctx context.Context, accId string) error {
	ctx, span := a.tracer.Start(ctx, fmt.Sprintf(_spanName, "Delete"))
	defer span.End()

	acc := &Account{ID: accId}
	r, err := a.db.NewDelete().
		Model(acc).
		Where("id = ?", accId).
		Exec(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return ErrAccountNotFound
		}
		return err
	}

	if affected, _ := r.RowsAffected(); affected == 0 {
		return ErrAccountNotFound
	}

	return nil
}
