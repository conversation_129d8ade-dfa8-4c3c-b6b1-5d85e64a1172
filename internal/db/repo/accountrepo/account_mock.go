// Code generated by mockery. DO NOT EDIT.

package accountrepo

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// IAccountsMocked is an autogenerated mock type for the IAccountsRepo type
type IAccountsMocked struct {
	mock.Mock
}

type IAccountsMocked_Expecter struct {
	mock *mock.Mock
}

func (_m *IAccountsMocked) EXPECT() *IAccountsMocked_Expecter {
	return &IAccountsMocked_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, acc
func (_m *IAccountsMocked) Create(ctx context.Context, acc *Account) error {
	ret := _m.Called(ctx, acc)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *Account) error); ok {
		r0 = rf(ctx, acc)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IAccountsMocked_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type IAccountsMocked_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - acc *Account
func (_e *IAccountsMocked_Expecter) Create(ctx interface{}, acc interface{}) *IAccountsMocked_Create_Call {
	return &IAccountsMocked_Create_Call{Call: _e.mock.On("Create", ctx, acc)}
}

func (_c *IAccountsMocked_Create_Call) Run(run func(ctx context.Context, acc *Account)) *IAccountsMocked_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*Account))
	})
	return _c
}

func (_c *IAccountsMocked_Create_Call) Return(_a0 error) *IAccountsMocked_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IAccountsMocked_Create_Call) RunAndReturn(run func(context.Context, *Account) error) *IAccountsMocked_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: ctx, accID
func (_m *IAccountsMocked) Delete(ctx context.Context, accID string) error {
	ret := _m.Called(ctx, accID)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, accID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IAccountsMocked_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type IAccountsMocked_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - accID string
func (_e *IAccountsMocked_Expecter) Delete(ctx interface{}, accID interface{}) *IAccountsMocked_Delete_Call {
	return &IAccountsMocked_Delete_Call{Call: _e.mock.On("Delete", ctx, accID)}
}

func (_c *IAccountsMocked_Delete_Call) Run(run func(ctx context.Context, accID string)) *IAccountsMocked_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *IAccountsMocked_Delete_Call) Return(_a0 error) *IAccountsMocked_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IAccountsMocked_Delete_Call) RunAndReturn(run func(context.Context, string) error) *IAccountsMocked_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: ctx, accID
func (_m *IAccountsMocked) Get(ctx context.Context, accID string) (*Account, error) {
	ret := _m.Called(ctx, accID)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 *Account
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*Account, error)); ok {
		return rf(ctx, accID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *Account); ok {
		r0 = rf(ctx, accID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Account)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IAccountsMocked_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type IAccountsMocked_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - ctx context.Context
//   - accID string
func (_e *IAccountsMocked_Expecter) Get(ctx interface{}, accID interface{}) *IAccountsMocked_Get_Call {
	return &IAccountsMocked_Get_Call{Call: _e.mock.On("Get", ctx, accID)}
}

func (_c *IAccountsMocked_Get_Call) Run(run func(ctx context.Context, accID string)) *IAccountsMocked_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *IAccountsMocked_Get_Call) Return(_a0 *Account, _a1 error) *IAccountsMocked_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IAccountsMocked_Get_Call) RunAndReturn(run func(context.Context, string) (*Account, error)) *IAccountsMocked_Get_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: ctx, filter
func (_m *IAccountsMocked) List(ctx context.Context, filter ListAccountsRequest) (*ListAccountsResponse, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 *ListAccountsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ListAccountsRequest) (*ListAccountsResponse, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ListAccountsRequest) *ListAccountsResponse); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ListAccountsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ListAccountsRequest) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IAccountsMocked_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type IAccountsMocked_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx context.Context
//   - filter ListAccountsRequest
func (_e *IAccountsMocked_Expecter) List(ctx interface{}, filter interface{}) *IAccountsMocked_List_Call {
	return &IAccountsMocked_List_Call{Call: _e.mock.On("List", ctx, filter)}
}

func (_c *IAccountsMocked_List_Call) Run(run func(ctx context.Context, filter ListAccountsRequest)) *IAccountsMocked_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ListAccountsRequest))
	})
	return _c
}

func (_c *IAccountsMocked_List_Call) Return(_a0 *ListAccountsResponse, _a1 error) *IAccountsMocked_List_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IAccountsMocked_List_Call) RunAndReturn(run func(context.Context, ListAccountsRequest) (*ListAccountsResponse, error)) *IAccountsMocked_List_Call {
	_c.Call.Return(run)
	return _c
}

// NewIAccountsMocked creates a new instance of IAccountsMocked. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIAccountsMocked(t interface {
	mock.TestingT
	Cleanup(func())
}) *IAccountsMocked {
	mock := &IAccountsMocked{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
