package paramrepo

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/uptrace/bun"
	"github.com/uptrace/bun/driver/pgdriver"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
	"github.com/herenow/atomic-bm/pkg/util"
)

var (
	ErrParamGroupNotFound = errors.NotFound(codes.Params, "param group not found")

	ErrUniqueParamGroupNameConstraint = errors.BadRequest(codes.Params, "param group with this name already exists")

	ErrUniqueBotParamGroupConstraint = errors.BadRequest(codes.Params, "bot param group relation already exists")
)

// ParamGroup represents the param group model.
type ParamGroup struct {
	bun.BaseModel `bun:"param_groups,alias:pg"`

	ID              string     `bun:",pk,type:uuid,default:gen_random_uuid"`
	Name            string     `bun:",notnull"`
	Priority        int32      `bun:",notnull"`
	CreatedByUserID string     `bun:",type:uuid,notnull"`
	DeletedByUserID string     `bun:",type:uuid,nullzero"`
	CreatedAt       time.Time  `bun:",notnull,default:current_timestamp"`
	DeletedAt       *time.Time `bun:",soft_delete,nullzero"`
}

// BotParamGroup represents the bot param group relation.
type BotParamGroup struct {
	bun.BaseModel `bun:"bot_param_groups,alias:bg"`

	BotID           string     `bun:",pk,type:uuid,notnull"`
	ParamGroupsID   string     `bun:",pk,type:uuid,notnull"`
	CreatedByUserID string     `bun:",type:uuid,notnull"`
	DeletedByUserID string     `bun:",type:uuid,nullzero"`
	CreatedAt       time.Time  `bun:",notnull,default:current_timestamp"`
	DeletedAt       *time.Time `bun:",soft_delete,nullzero"`
}

type CreateGroupRequest struct {
	Name            string
	Priority        int32
	CreatedByUserID string
}

func (r *ParamsRepo) CreateParamGroup(ctx context.Context, req CreateGroupRequest) (*ParamGroup, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "CreateParamGroup"))
	defer span.End()

	paramGroup := &ParamGroup{
		Name:            req.Name,
		Priority:        req.Priority,
		CreatedByUserID: req.CreatedByUserID,
	}

	if _, err := r.db.NewInsert().
		Model(paramGroup).
		Returning("*").
		Exec(ctx); err != nil {
		var pgErr pgdriver.Error
		if errors.As(err, &pgErr) && pgErr.IntegrityViolation() {
			switch pgErr.Field('n') {
			case "unique_param_group_name":
				return nil, ErrUniqueParamGroupNameConstraint
			}
		}
		return nil, errors.Wrap(err, "create new param group")
	}

	return paramGroup, nil
}

func (r *ParamsRepo) GetParamGroup(ctx context.Context, groupID string) (*ParamGroup, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "Get"))
	defer span.End()

	paramGroup := &ParamGroup{ID: groupID}
	if err := r.db.NewSelect().
		Model(paramGroup).
		WherePK().
		Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrParamGroupNotFound
		}
		return nil, errors.Wrap(err, "get param group")
	}

	return paramGroup, nil
}

type ListParamGroupsRequest struct {
	ginx.PaginationParam
	OrderBy string

	Name            string
	CreatedByUserID string
}

type ListParamGroupsResponse struct {
	Total       int
	ParamGroups []ParamGroup
}

func (r *ParamsRepo) ListParamGroups(ctx context.Context, req ListParamGroupsRequest) (*ListParamGroupsResponse, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "ListParamGroups"))
	defer span.End()

	var paramGroups []ParamGroup
	query := r.db.NewSelect().
		Model(&paramGroups).
		Limit(req.Limit).
		Offset(req.Offset)

	// Apply default ordering
	defaultOrder := "created_at DESC"
	if req.OrderBy != "" {
		defaultOrder = fmt.Sprintf("%s %s", req.OrderBy, req.OrderDirection)
	}
	query.OrderExpr(defaultOrder)

	// Apply search-like filtering
	if req.Name != "" {
		query.Where("? ILIKE ?", bun.Ident("name"), fmt.Sprintf("%%%s%%", req.Name))
	}

	if req.CreatedByUserID != "" {
		query.Where("? = ?", bun.Ident("created_by_user_id"), req.CreatedByUserID)
	}

	count, err := query.ScanAndCount(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list param groups")
	}

	return &ListParamGroupsResponse{ParamGroups: paramGroups, Total: count}, nil
}

type DeleteParamGroupRequest struct {
	UserID  string
	GroupID string
}

// DeleteParamGroup deletes a param group by its id.
func (r *ParamsRepo) DeleteParamGroup(ctx context.Context, req DeleteParamGroupRequest) error {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "DeleteParamGroup"))
	defer span.End()

	paramGroup := &ParamGroup{ID: req.GroupID, DeletedByUserID: req.UserID}
	res, err := r.db.NewDelete().
		Model(paramGroup).
		WherePK().
		Exec(ctx)
	if err != nil {
		return errors.Wrap(err, "deleting param group")
	}

	return util.EnsureRowsAffected(res)
}

// CreateBotParamGroupRelation creates a bot and param group relations.
func (r *ParamsRepo) CreateBotParamGroupRelation(ctx context.Context, req BotParamGroup) error {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "CreateBotParamGroupRelation"))
	defer span.End()

	if _, err := r.db.NewInsert().
		Model(&req).
		Returning("*").
		Exec(ctx); err != nil {
		var pgErr pgdriver.Error
		if errors.As(err, &pgErr) && pgErr.IntegrityViolation() {
			switch pgErr.Field('n') {
			case "unique_bot_param_group":
				return ErrUniqueBotParamGroupConstraint
			}
		}
		return errors.Wrap(err, "create bot param group relation")
	}

	return nil
}

type ListBotParamGroupsRelationsRequest struct {
	ginx.PaginationParam
	OrderBy string

	CreatedByUserID string
	GroupID         string
	BotIDs          []string
}

type ListBotParamGroupsResponse struct {
	Total          int
	BotParamGroups []BotParamGroup
}

func (r *ParamsRepo) ListBotParamGroupsRelations(ctx context.Context, req ListBotParamGroupsRelationsRequest) (*ListBotParamGroupsResponse, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "ListBotParamGroupsRelations"))
	defer span.End()

	var botParamGroups []BotParamGroup
	query := r.db.NewSelect().
		Model(&botParamGroups).
		Limit(req.Limit).
		Offset(req.Offset)

	// Apply default ordering
	defaultOrder := "created_at DESC"
	if req.OrderBy != "" {
		defaultOrder = fmt.Sprintf("%s %s", req.OrderBy, req.OrderDirection)
	}
	query.OrderExpr(defaultOrder)

	if len(req.BotIDs) > 0 {
		query.Where("bot_id IN (?)", bun.In(req.BotIDs))
	}

	if req.CreatedByUserID != "" {
		query.Where("? = ?", bun.Ident("created_by_user_id"), req.CreatedByUserID)
	}

	if req.GroupID != "" {
		query.Where("? = ?", bun.Ident("param_groups_id"), req.GroupID)
	}

	count, err := query.ScanAndCount(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list bot param groups")
	}

	return &ListBotParamGroupsResponse{BotParamGroups: botParamGroups, Total: count}, nil
}

func (r *ParamsRepo) DeleteBotParamGroupRelation(ctx context.Context, req BotParamGroup) error {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "DeleteBotParamGroupRelation"))
	defer span.End()

	res, err := r.db.NewDelete().
		Model(&req).
		Where("bg.param_groups_id = ? AND bg.bot_id = ?", req.ParamGroupsID, req.BotID).
		Exec(ctx)
	if err != nil {
		return errors.Wrap(err, "deleting bot param group relations")
	}

	return util.EnsureRowsAffected(res)
}
