package paramrepo

import (
	"context"
	"fmt"
	"time"

	"github.com/uptrace/bun"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
)

var _spanName = "ParamsRepository/%s"

const _ScopeName = "ParamsRepository"

// Param represents the parameters model.
type Param struct {
	bun.BaseModel `bun:"params_log,select:params_view,alias:pl"`

	ScopeID          string    `bun:",pk,type:uuid,notnull"`
	Scope            string    `bun:",pk,notnull,default:'bot'"`
	Key              string    `bun:",pk,notnull"`
	Value            string    `bun:",default:null"`
	IsEncrypted      bool      `bun:","`
	SecretKeyVersion int       `bun:","`
	Version          int       `bun:",pk,notnull"`
	CreatedByID      string    `bun:",type:uuid,notnull"`
	CreatedAt        time.Time `bun:",notnull,default:current_timestamp"`
}

// IParams defines the methods for managing parameters
type IParams interface {
	// Create adds multiple parameters in bulk.
	Create(ctx context.Context, params []Param) error

	// List retrieves a list of params by list params request
	List(ctx context.Context, filter ListParamsRequest) (*ListParamsResponse, error)

	// CreateParamGroup create a param group
	CreateParamGroup(ctx context.Context, req CreateGroupRequest) (*ParamGroup, error)

	// GetParamGroup retrieve a param group by his id
	GetParamGroup(ctx context.Context, groupID string) (*ParamGroup, error)

	// ListParamGroups retrieves a list of param groups
	ListParamGroups(ctx context.Context, req ListParamGroupsRequest) (*ListParamGroupsResponse, error)

	// DeleteParamGroup deletes a param group by its id
	DeleteParamGroup(ctx context.Context, req DeleteParamGroupRequest) error

	// CreateBotParamGroupRelation creates a bot and param group relation.
	CreateBotParamGroupRelation(ctx context.Context, req BotParamGroup) error

	// ListBotParamGroupsRelations retrieves a list of bot param group relations
	ListBotParamGroupsRelations(ctx context.Context, req ListBotParamGroupsRelationsRequest) (*ListBotParamGroupsResponse, error)

	// DeleteBotParamGroupRelation deletes one or more bot param group relation
	DeleteBotParamGroupRelation(ctx context.Context, req BotParamGroup) error
}

type ParamsRepo struct {
	db     bun.IDB
	tracer trace.Tracer
}

// New creates a new instance of ParamsRepo
func New(db bun.IDB) *ParamsRepo {
	return &ParamsRepo{
		db:     db,
		tracer: otel.Tracer(_ScopeName),
	}
}

// Create add one or many params.
func (r *ParamsRepo) Create(ctx context.Context, params []Param) error {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "Create"))
	defer span.End()

	if _, err := r.db.NewInsert().
		Model(&params).
		Returning("*").
		Exec(ctx, &params); err != nil {
		return errors.Wrap(err, "create param")
	}

	return nil
}

type ListParamsRequest struct {
	ginx.PaginationParam
	OrderBy string

	Scope   []string
	ScopeID []string
}

type ListParamsResponse struct {
	Params []Param
	Total  int
}

// List retrieves a list of params by list params request.
func (r *ParamsRepo) List(ctx context.Context, filter ListParamsRequest) (*ListParamsResponse, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "List"))
	defer span.End()

	var params []Param
	query := r.db.NewSelect().
		Model(&params).
		Limit(filter.Limit).
		Offset(filter.Offset)

	defaultOrder := "created_at DESC"
	if filter.OrderBy != "" {
		defaultOrder = fmt.Sprintf("%s %s", filter.OrderBy, filter.OrderDirection)
	}
	query.OrderExpr(defaultOrder)

	if len(filter.Scope) > 0 {
		for i, scope := range filter.Scope {
			if i < len(filter.ScopeID) {
				query.WhereOr("scope = ? AND scope_id = ?", scope, filter.ScopeID[i])
			} else {
				query.WhereOr("scope = ?", scope)
			}
		}
	}

	count, err := query.ScanAndCount(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list params")
	}

	return &ListParamsResponse{Params: params, Total: count}, nil
}
