// Code generated by mockery. DO NOT EDIT.

package paramrepo

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// IParamsMocked is an autogenerated mock type for the IParams type
type IParamsMocked struct {
	mock.Mock
}

type IParamsMocked_Expecter struct {
	mock *mock.Mock
}

func (_m *IParamsMocked) EXPECT() *IParamsMocked_Expecter {
	return &IParamsMocked_Expecter{mock: &_m.Mock}
}

// Create provides a mock function with given fields: ctx, params
func (_m *IParamsMocked) Create(ctx context.Context, params []Param) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []Param) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IParamsMocked_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type IParamsMocked_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
//   - params []Param
func (_e *IParamsMocked_Expecter) Create(ctx interface{}, params interface{}) *IParamsMocked_Create_Call {
	return &IParamsMocked_Create_Call{Call: _e.mock.On("Create", ctx, params)}
}

func (_c *IParamsMocked_Create_Call) Run(run func(ctx context.Context, params []Param)) *IParamsMocked_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]Param))
	})
	return _c
}

func (_c *IParamsMocked_Create_Call) Return(_a0 error) *IParamsMocked_Create_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IParamsMocked_Create_Call) RunAndReturn(run func(context.Context, []Param) error) *IParamsMocked_Create_Call {
	_c.Call.Return(run)
	return _c
}

// CreateBotParamGroupRelation provides a mock function with given fields: ctx, req
func (_m *IParamsMocked) CreateBotParamGroupRelation(ctx context.Context, req BotParamGroup) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateBotParamGroupRelation")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, BotParamGroup) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IParamsMocked_CreateBotParamGroupRelation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateBotParamGroupRelation'
type IParamsMocked_CreateBotParamGroupRelation_Call struct {
	*mock.Call
}

// CreateBotParamGroupRelation is a helper method to define mock.On call
//   - ctx context.Context
//   - req BotParamGroup
func (_e *IParamsMocked_Expecter) CreateBotParamGroupRelation(ctx interface{}, req interface{}) *IParamsMocked_CreateBotParamGroupRelation_Call {
	return &IParamsMocked_CreateBotParamGroupRelation_Call{Call: _e.mock.On("CreateBotParamGroupRelation", ctx, req)}
}

func (_c *IParamsMocked_CreateBotParamGroupRelation_Call) Run(run func(ctx context.Context, req BotParamGroup)) *IParamsMocked_CreateBotParamGroupRelation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(BotParamGroup))
	})
	return _c
}

func (_c *IParamsMocked_CreateBotParamGroupRelation_Call) Return(_a0 error) *IParamsMocked_CreateBotParamGroupRelation_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IParamsMocked_CreateBotParamGroupRelation_Call) RunAndReturn(run func(context.Context, BotParamGroup) error) *IParamsMocked_CreateBotParamGroupRelation_Call {
	_c.Call.Return(run)
	return _c
}

// CreateParamGroup provides a mock function with given fields: ctx, req
func (_m *IParamsMocked) CreateParamGroup(ctx context.Context, req CreateGroupRequest) (*ParamGroup, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateParamGroup")
	}

	var r0 *ParamGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CreateGroupRequest) (*ParamGroup, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CreateGroupRequest) *ParamGroup); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ParamGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, CreateGroupRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IParamsMocked_CreateParamGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateParamGroup'
type IParamsMocked_CreateParamGroup_Call struct {
	*mock.Call
}

// CreateParamGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - req CreateGroupRequest
func (_e *IParamsMocked_Expecter) CreateParamGroup(ctx interface{}, req interface{}) *IParamsMocked_CreateParamGroup_Call {
	return &IParamsMocked_CreateParamGroup_Call{Call: _e.mock.On("CreateParamGroup", ctx, req)}
}

func (_c *IParamsMocked_CreateParamGroup_Call) Run(run func(ctx context.Context, req CreateGroupRequest)) *IParamsMocked_CreateParamGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CreateGroupRequest))
	})
	return _c
}

func (_c *IParamsMocked_CreateParamGroup_Call) Return(_a0 *ParamGroup, _a1 error) *IParamsMocked_CreateParamGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IParamsMocked_CreateParamGroup_Call) RunAndReturn(run func(context.Context, CreateGroupRequest) (*ParamGroup, error)) *IParamsMocked_CreateParamGroup_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteBotParamGroupRelation provides a mock function with given fields: ctx, req
func (_m *IParamsMocked) DeleteBotParamGroupRelation(ctx context.Context, req BotParamGroup) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for DeleteBotParamGroupRelation")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, BotParamGroup) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IParamsMocked_DeleteBotParamGroupRelation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteBotParamGroupRelation'
type IParamsMocked_DeleteBotParamGroupRelation_Call struct {
	*mock.Call
}

// DeleteBotParamGroupRelation is a helper method to define mock.On call
//   - ctx context.Context
//   - req BotParamGroup
func (_e *IParamsMocked_Expecter) DeleteBotParamGroupRelation(ctx interface{}, req interface{}) *IParamsMocked_DeleteBotParamGroupRelation_Call {
	return &IParamsMocked_DeleteBotParamGroupRelation_Call{Call: _e.mock.On("DeleteBotParamGroupRelation", ctx, req)}
}

func (_c *IParamsMocked_DeleteBotParamGroupRelation_Call) Run(run func(ctx context.Context, req BotParamGroup)) *IParamsMocked_DeleteBotParamGroupRelation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(BotParamGroup))
	})
	return _c
}

func (_c *IParamsMocked_DeleteBotParamGroupRelation_Call) Return(_a0 error) *IParamsMocked_DeleteBotParamGroupRelation_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IParamsMocked_DeleteBotParamGroupRelation_Call) RunAndReturn(run func(context.Context, BotParamGroup) error) *IParamsMocked_DeleteBotParamGroupRelation_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteParamGroup provides a mock function with given fields: ctx, req
func (_m *IParamsMocked) DeleteParamGroup(ctx context.Context, req DeleteParamGroupRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for DeleteParamGroup")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, DeleteParamGroupRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IParamsMocked_DeleteParamGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteParamGroup'
type IParamsMocked_DeleteParamGroup_Call struct {
	*mock.Call
}

// DeleteParamGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - req DeleteParamGroupRequest
func (_e *IParamsMocked_Expecter) DeleteParamGroup(ctx interface{}, req interface{}) *IParamsMocked_DeleteParamGroup_Call {
	return &IParamsMocked_DeleteParamGroup_Call{Call: _e.mock.On("DeleteParamGroup", ctx, req)}
}

func (_c *IParamsMocked_DeleteParamGroup_Call) Run(run func(ctx context.Context, req DeleteParamGroupRequest)) *IParamsMocked_DeleteParamGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(DeleteParamGroupRequest))
	})
	return _c
}

func (_c *IParamsMocked_DeleteParamGroup_Call) Return(_a0 error) *IParamsMocked_DeleteParamGroup_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IParamsMocked_DeleteParamGroup_Call) RunAndReturn(run func(context.Context, DeleteParamGroupRequest) error) *IParamsMocked_DeleteParamGroup_Call {
	_c.Call.Return(run)
	return _c
}

// GetParamGroup provides a mock function with given fields: ctx, groupID
func (_m *IParamsMocked) GetParamGroup(ctx context.Context, groupID string) (*ParamGroup, error) {
	ret := _m.Called(ctx, groupID)

	if len(ret) == 0 {
		panic("no return value specified for GetParamGroup")
	}

	var r0 *ParamGroup
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*ParamGroup, error)); ok {
		return rf(ctx, groupID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *ParamGroup); ok {
		r0 = rf(ctx, groupID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ParamGroup)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, groupID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IParamsMocked_GetParamGroup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetParamGroup'
type IParamsMocked_GetParamGroup_Call struct {
	*mock.Call
}

// GetParamGroup is a helper method to define mock.On call
//   - ctx context.Context
//   - groupID string
func (_e *IParamsMocked_Expecter) GetParamGroup(ctx interface{}, groupID interface{}) *IParamsMocked_GetParamGroup_Call {
	return &IParamsMocked_GetParamGroup_Call{Call: _e.mock.On("GetParamGroup", ctx, groupID)}
}

func (_c *IParamsMocked_GetParamGroup_Call) Run(run func(ctx context.Context, groupID string)) *IParamsMocked_GetParamGroup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *IParamsMocked_GetParamGroup_Call) Return(_a0 *ParamGroup, _a1 error) *IParamsMocked_GetParamGroup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IParamsMocked_GetParamGroup_Call) RunAndReturn(run func(context.Context, string) (*ParamGroup, error)) *IParamsMocked_GetParamGroup_Call {
	_c.Call.Return(run)
	return _c
}

// List provides a mock function with given fields: ctx, filter
func (_m *IParamsMocked) List(ctx context.Context, filter ListParamsRequest) (*ListParamsResponse, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for List")
	}

	var r0 *ListParamsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ListParamsRequest) (*ListParamsResponse, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ListParamsRequest) *ListParamsResponse); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ListParamsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ListParamsRequest) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IParamsMocked_List_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'List'
type IParamsMocked_List_Call struct {
	*mock.Call
}

// List is a helper method to define mock.On call
//   - ctx context.Context
//   - filter ListParamsRequest
func (_e *IParamsMocked_Expecter) List(ctx interface{}, filter interface{}) *IParamsMocked_List_Call {
	return &IParamsMocked_List_Call{Call: _e.mock.On("List", ctx, filter)}
}

func (_c *IParamsMocked_List_Call) Run(run func(ctx context.Context, filter ListParamsRequest)) *IParamsMocked_List_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ListParamsRequest))
	})
	return _c
}

func (_c *IParamsMocked_List_Call) Return(_a0 *ListParamsResponse, _a1 error) *IParamsMocked_List_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IParamsMocked_List_Call) RunAndReturn(run func(context.Context, ListParamsRequest) (*ListParamsResponse, error)) *IParamsMocked_List_Call {
	_c.Call.Return(run)
	return _c
}

// ListBotParamGroupsRelations provides a mock function with given fields: ctx, req
func (_m *IParamsMocked) ListBotParamGroupsRelations(ctx context.Context, req ListBotParamGroupsRelationsRequest) (*ListBotParamGroupsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListBotParamGroupsRelations")
	}

	var r0 *ListBotParamGroupsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ListBotParamGroupsRelationsRequest) (*ListBotParamGroupsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ListBotParamGroupsRelationsRequest) *ListBotParamGroupsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ListBotParamGroupsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ListBotParamGroupsRelationsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IParamsMocked_ListBotParamGroupsRelations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListBotParamGroupsRelations'
type IParamsMocked_ListBotParamGroupsRelations_Call struct {
	*mock.Call
}

// ListBotParamGroupsRelations is a helper method to define mock.On call
//   - ctx context.Context
//   - req ListBotParamGroupsRelationsRequest
func (_e *IParamsMocked_Expecter) ListBotParamGroupsRelations(ctx interface{}, req interface{}) *IParamsMocked_ListBotParamGroupsRelations_Call {
	return &IParamsMocked_ListBotParamGroupsRelations_Call{Call: _e.mock.On("ListBotParamGroupsRelations", ctx, req)}
}

func (_c *IParamsMocked_ListBotParamGroupsRelations_Call) Run(run func(ctx context.Context, req ListBotParamGroupsRelationsRequest)) *IParamsMocked_ListBotParamGroupsRelations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ListBotParamGroupsRelationsRequest))
	})
	return _c
}

func (_c *IParamsMocked_ListBotParamGroupsRelations_Call) Return(_a0 *ListBotParamGroupsResponse, _a1 error) *IParamsMocked_ListBotParamGroupsRelations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IParamsMocked_ListBotParamGroupsRelations_Call) RunAndReturn(run func(context.Context, ListBotParamGroupsRelationsRequest) (*ListBotParamGroupsResponse, error)) *IParamsMocked_ListBotParamGroupsRelations_Call {
	_c.Call.Return(run)
	return _c
}

// ListParamGroups provides a mock function with given fields: ctx, req
func (_m *IParamsMocked) ListParamGroups(ctx context.Context, req ListParamGroupsRequest) (*ListParamGroupsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ListParamGroups")
	}

	var r0 *ListParamGroupsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ListParamGroupsRequest) (*ListParamGroupsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ListParamGroupsRequest) *ListParamGroupsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ListParamGroupsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ListParamGroupsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IParamsMocked_ListParamGroups_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListParamGroups'
type IParamsMocked_ListParamGroups_Call struct {
	*mock.Call
}

// ListParamGroups is a helper method to define mock.On call
//   - ctx context.Context
//   - req ListParamGroupsRequest
func (_e *IParamsMocked_Expecter) ListParamGroups(ctx interface{}, req interface{}) *IParamsMocked_ListParamGroups_Call {
	return &IParamsMocked_ListParamGroups_Call{Call: _e.mock.On("ListParamGroups", ctx, req)}
}

func (_c *IParamsMocked_ListParamGroups_Call) Run(run func(ctx context.Context, req ListParamGroupsRequest)) *IParamsMocked_ListParamGroups_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ListParamGroupsRequest))
	})
	return _c
}

func (_c *IParamsMocked_ListParamGroups_Call) Return(_a0 *ListParamGroupsResponse, _a1 error) *IParamsMocked_ListParamGroups_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IParamsMocked_ListParamGroups_Call) RunAndReturn(run func(context.Context, ListParamGroupsRequest) (*ListParamGroupsResponse, error)) *IParamsMocked_ListParamGroups_Call {
	_c.Call.Return(run)
	return _c
}

// NewIParamsMocked creates a new instance of IParamsMocked. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIParamsMocked(t interface {
	mock.TestingT
	Cleanup(func())
}) *IParamsMocked {
	mock := &IParamsMocked{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
