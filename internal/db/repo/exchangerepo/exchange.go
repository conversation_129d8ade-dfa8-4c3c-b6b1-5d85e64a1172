package exchangerepo

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/uptrace/bun"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
)

var _spanName = "ExchangeRepository/%s"

const _ScopeName = "ExchangeRepository"

var (
	ErrExchangeNotFound = errors.NotFound(codes.Exchanges, "exchange not found")
)

// Exchange represents the exchange model.
type Exchange struct {
	bun.BaseModel `bun:"exchanges,alias:exg"`

	ID        string    `bun:",pk,notnull"`
	Name      string    `bun:",notnull"`
	CreatedAt time.Time `bun:",notnull,default:current_timestamp"`
}

type IExchange interface {
	Get(ctx context.Context, exchangeID string) (*Exchange, error)

	List(ctx context.Context, request ListExchangesRequest) (*ListExchangesResponse, error)
}

type ExchangeRepo struct {
	db     bun.IDB
	tracer trace.Tracer
}

// New creates a new instance of ExchangeRepo
func New(db bun.IDB) *ExchangeRepo {
	return &ExchangeRepo{
		db:     db,
		tracer: otel.Tracer(_ScopeName),
	}
}

type ListExchangesRequest struct {
	ginx.PaginationParam
	OrderBy string

	ExchangeID string
	Name       string
}

type ListExchangesResponse struct {
	Exchanges []Exchange
	Total     int
}

func (r ExchangeRepo) List(ctx context.Context, req ListExchangesRequest) (*ListExchangesResponse, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "List"))
	defer span.End()

	var exgs []Exchange
	query := r.db.NewSelect().
		Model(&exgs).
		Limit(req.Limit).
		Offset(req.Offset)

	defaultOrder := "created_at DESC"
	if req.OrderBy != "" {
		defaultOrder = fmt.Sprintf("%s %s", req.OrderBy, req.OrderDirection)
	}
	query.OrderExpr(defaultOrder)

	// Apply search-like filtering
	if req.ExchangeID != "" {
		query.Where("? ILIKE ?", bun.Ident("id"), fmt.Sprintf("%%%s%%", req.ExchangeID))
	}

	if req.Name != "" {
		query.Where("? = ?", bun.Ident("name"), req.Name)
	}

	count, err := query.ScanAndCount(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list exchanges")
	}

	return &ListExchangesResponse{Exchanges: exgs, Total: count}, nil
}

func (r ExchangeRepo) Get(ctx context.Context, exchangeID string) (*Exchange, error) {
	ctx, span := r.tracer.Start(ctx, fmt.Sprintf(_spanName, "Get"))
	defer span.End()

	exg := &Exchange{ID: exchangeID}
	if err := r.db.NewSelect().
		Model(exg).
		WherePK().
		Scan(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrExchangeNotFound
		}
		return nil, errors.Wrap(err, "get exchange")
	}

	return exg, nil
}
