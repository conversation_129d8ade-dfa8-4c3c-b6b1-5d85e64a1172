package tasks

import (
	"context"
	"fmt"
	"strings"
	"time"

	"firebase.google.com/go/v4/messaging"
	"github.com/hibiken/asynq"
	"github.com/pkg/errors"
	"github.com/uptrace/bun"

	"github.com/herenow/atomic-bm/internal/db/repo/notificationsrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/pushnotificationrepo"
	notify "github.com/herenow/atomic-bm/pkg/firebase/fcm"
)

const TypeBotsOffline = "bot:offline"

type TaskBotsOffline struct {
	db               bun.IDB
	notifyBotRepo    notificationsrepo.INotificationSubscriptionsRepo
	notifyLogRepo    notificationsrepo.INotificationLogsRepo
	notifyEvRepo     notificationsrepo.INotificationEventsRepo
	pushNotifyRepo   pushnotificationrepo.INotificationsRepo
	pushNotifyClient *notify.Client
}

// TODO(gabriel): remove this struct example with real struct from repository

type OfflineBotInfo struct {
	BotID          string
	LastTimeOnline time.Time
}

// New creates a new TaskBotsOffline instance.
func New(db bun.IDB, pushNotifyClient *notify.Client) (*TaskBotsOffline, error) {
	if db == nil || pushNotifyClient == nil {
		return nil, errors.New("db and push notification client cannot be nil")
	}

	return &TaskBotsOffline{
		db:               db,
		notifyBotRepo:    notificationsrepo.NewNotificationSubscriptions(db),
		notifyLogRepo:    notificationsrepo.NewNotificationLogs(db),
		notifyEvRepo:     notificationsrepo.NewNotificationEvents(db),
		pushNotifyRepo:   pushnotificationrepo.New(db),
		pushNotifyClient: pushNotifyClient,
	}, nil
}

// HandleBotsOffline is the main entry point for handling offline bots.
func (o *TaskBotsOffline) HandleBotsOffline(ctx context.Context, _ *asynq.Task) error {
	subs, err := o.getSubscribers(ctx)
	if err != nil {
		return err
	}

	offlineBots := o.getOfflineBots()

	possibleEventsByUser := o.getEventsByUser(subs, offlineBots)

	eventsToSend, err := o.notifyEvRepo.ListUnnoticedEvents(ctx, possibleEventsByUser)
	if err != nil {
		return err
	}

	userBotsGroup := o.groupEventsByUser(eventsToSend, offlineBots)

	logsToSave := o.createNotificationLogs(userBotsGroup)

	if err = o.saveNotificationLogsAndEvents(ctx, logsToSave, eventsToSend); err != nil {
		return err
	}

	if err = o.sendPushNotifications(ctx, logsToSave); err != nil {
		return err
	}

	return nil
}

// getSubscribers retrieves subscribers interested in Offline Bots notifications.
func (o *TaskBotsOffline) getSubscribers(ctx context.Context) ([]*notificationsrepo.NotificationSubscription, error) {
	botList, err := o.notifyBotRepo.List(ctx, notificationsrepo.ListNotificationSubscriptionsRequest{EventType: TypeBotsOffline})
	if err != nil {
		return nil, err
	}
	return botList.NotificationSubscriptions, nil
}

// TODO(gabriel): refactor the code to use a real data
// getOfflineBots retrieves a list of offline bots.
func (o *TaskBotsOffline) getOfflineBots() map[string]time.Time {
	// Simulated data for offline bots
	return map[string]time.Time{
		"uuid":  time.Now().Add(-time.Minute * 5),
		"uuid1": time.Now().Add(-time.Minute * 10),
	}
}

// getEventsByUser creates a list of potential events for each user.
func (o *TaskBotsOffline) getEventsByUser(subs []*notificationsrepo.NotificationSubscription, offlineBots map[string]time.Time) []notificationsrepo.ListUnnoticedEventsReq {
	var possibleEventsByUser []notificationsrepo.ListUnnoticedEventsReq

	for botID, lastTimeOnline := range offlineBots {
		for _, sub := range subs {
			if sub.ResourceID == botID {
				possibleEventsByUser = append(possibleEventsByUser, notificationsrepo.ListUnnoticedEventsReq{
					EventType: TypeBotsOffline,
					EventKey:  fmt.Sprintf("%s:%s", botID, lastTimeOnline),
					UserID:    sub.UserID,
				})
			}
		}
	}

	return possibleEventsByUser
}

// groupEventsByUser groups events by user.
func (o *TaskBotsOffline) groupEventsByUser(events []notificationsrepo.NotificationEvent, offlineBots map[string]time.Time) map[string][]OfflineBotInfo {
	userBotsGroup := make(map[string][]OfflineBotInfo)

	for _, userEv := range events {
		for botID, lastTimeOnline := range offlineBots {
			eventKey := fmt.Sprintf("%s:%s", botID, lastTimeOnline)
			if eventKey == userEv.EventKey {
				bot := OfflineBotInfo{
					BotID:          botID,
					LastTimeOnline: lastTimeOnline,
				}
				userBotsGroup[userEv.UserID] = append(userBotsGroup[userEv.UserID], bot)
			}
		}
	}

	return userBotsGroup
}

// // createNotificationLogs creates notification logs for each user.
func (o *TaskBotsOffline) createNotificationLogs(userBotsGroup map[string][]OfflineBotInfo) []*notificationsrepo.NotificationLog {
	var logsToSave []*notificationsrepo.NotificationLog
	now := time.Now()

	for userID, bots := range userBotsGroup {
		title, content := createTitleAndContent(bots)

		logsToSave = append(logsToSave, &notificationsrepo.NotificationLog{
			EventType: TypeBotsOffline,
			Title:     title,
			Content:   content,
			UserID:    userID,
			Metadata: map[string]any{
				"bots": bots,
			},
			SentAt: now,
		})
	}

	return logsToSave
}

func createTitleAndContent(bots []OfflineBotInfo) (string, string) {
	var title, content string
	if len(bots) == 1 {
		bot := bots[0]
		title = fmt.Sprintf("Bot '%s' is offline", bot.BotID)
		content = fmt.Sprintf("The bot was last online at %s and is currently offline.", bot.LastTimeOnline)
	} else {
		var botList strings.Builder
		for i, bot := range bots {
			if i > 0 {
				botList.WriteString(", ")
			}
			botList.WriteString(fmt.Sprintf("%s", bot.BotID))
		}
		title = fmt.Sprintf("There are %v bots offline", len(bots))
		content = fmt.Sprintf("The following bots are currently offline: %s", botList.String())
	}
	return title, content
}

// saveNotificationLogsAndEvents saves logs and events in a transaction.
func (o *TaskBotsOffline) saveNotificationLogsAndEvents(ctx context.Context, logsToSave []*notificationsrepo.NotificationLog, eventsToSend []notificationsrepo.NotificationEvent) error {
	return o.db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
		txNtfLogRepo := notificationsrepo.NewNotificationLogs(tx)
		txNtfEvRepo := notificationsrepo.NewNotificationEvents(tx)

		if err := txNtfLogRepo.BulkCreate(ctx, logsToSave); err != nil {
			return err
		}

		if err := txNtfEvRepo.BulkCreate(ctx, &eventsToSend); err != nil {
			return err
		}

		return nil
	})
}

// sendPushNotifications sends push notifications for the logs.
func (o *TaskBotsOffline) sendPushNotifications(ctx context.Context, logsToSave []*notificationsrepo.NotificationLog) error {
	var userIDs []string
	for _, log := range logsToSave {
		userIDs = append(userIDs, log.UserID)
	}

	userDevices, err := o.pushNotifyRepo.ListByUserIDs(ctx, userIDs)
	if err != nil {
		return err
	}

	var msgsToSend []*messaging.Message
	for _, log := range logsToSave {
		var device *pushnotificationrepo.PushNotification
		for _, d := range userDevices {
			if d.UserID == log.UserID {
				device = d
				break
			}
		}

		if device != nil {
			msg := o.newMessaging(log.Title, log.Content, TypeBotsOffline, device.DeviceToken, nil)
			msgsToSend = append(msgsToSend, msg)
		}
	}

	for _, msg := range msgsToSend {
		_, err := o.pushNotifyClient.SendNotification(ctx, msg)
		if err != nil {
			return err
		}
	}

	return nil
}

// newMessaging creates a new messaging instance.
func (o *TaskBotsOffline) newMessaging(title, content, tag, deviceToken string, data map[string]string) *messaging.Message {
	timestamp := time.Now().Unix()

	msg := &messaging.Message{
		Data: data,
		Notification: &messaging.Notification{
			Title: title,
			Body:  content,
		},
		Webpush: &messaging.WebpushConfig{
			Notification: &messaging.WebpushNotification{
				Language:           "en-US",
				RequireInteraction: true,
				Tag:                tag,
				TimestampMillis:    &timestamp,
				Vibrate:            []int{200, 100, 200},
			},
		},
		Token: deviceToken,
	}

	return msg
}
