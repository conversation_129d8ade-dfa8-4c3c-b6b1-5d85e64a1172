package ws

import (
	"context"
	"net/http"

	"github.com/centrifugal/centrifuge"
	"github.com/gin-gonic/gin"
	"github.com/herenow/atomic-bm/internal/auth/casbin"
	"github.com/herenow/atomic-bm/internal/auth/session"
	"github.com/herenow/atomic-bm/internal/db/repo/userrepo"
	"github.com/herenow/atomic-bm/internal/middlewares"
	"github.com/segmentio/encoding/json"
	"go.uber.org/zap"
)

type ClientInfo struct {
	Name  string `json:"name"`
	Token string `json:"-"`
}

func Init(ctx context.Context,
	policy *casbin.PolicyEnforcer,
	api *gin.RouterGroup,
	session *session.Manager,
	userRepo userrepo.IUserRepository,
	logger *zap.Logger,
) *WebsocketClient {
	wsClient := NewWebsocketClient(policy, logger)

	// Here we can configure configurations about websocket protocol
	wsCfg := centrifuge.WebsocketConfig{
		ReadBufferSize:     1024,
		UseWriteBufferPool: true,

		// There are many others properties that we could configure
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
		WriteBufferSize:    0,
		MessageSizeLimit:   0,
		WriteTimeout:       0,
		Compression:        false,
		CompressionLevel:   0,
		CompressionMinSize: 0,
		PingPongConfig:     centrifuge.PingPongConfig{},
	}

	wsHandler := centrifuge.NewWebsocketHandler(wsClient.GetNode(), wsCfg)

	api.GET("", gin.WrapH(authWsMiddleware(wsHandler, session, userRepo)))

	return wsClient
}

func authWsMiddleware(h http.Handler, session *session.Manager, userRepo userrepo.IUserRepository) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		gc, err := middlewares.GinFromContext(ctx)
		if err != nil {
			zap.L().Error("ws: retrieve gin context", zap.Error(err), zap.Any("context", ctx))
			return
		}

		s := session.FromContext(gc)
		if s.UserID == "" {
			zap.L().Error("ws: retrieve user from context", zap.Any("context", ctx))
			return
		}

		usr, err := userRepo.Get(ctx, s.UserID)
		if err != nil {
			zap.L().Error("ws: retrieve user from database", zap.Error(err), zap.Any("context", ctx))
			return
		}

		clientInfoB, err := json.Marshal(&ClientInfo{
			Name:  usr.Name,
			Token: s.Token,
		})
		if err != nil {
			zap.L().Error("ws: marshall ws client info", zap.Error(err), zap.Any("context", ctx))
			return
		}

		newCtx := centrifuge.SetCredentials(ctx, &centrifuge.Credentials{
			UserID:   s.UserID,
			Info:     clientInfoB,
			ExpireAt: s.Expiry.Unix(),
		})

		r = r.WithContext(newCtx)
		h.ServeHTTP(w, r)
	})
}
