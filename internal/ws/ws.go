package ws

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/centrifugal/centrifuge"
	"github.com/panjf2000/ants/v2"
	"github.com/pkg/errors"
	"github.com/segmentio/encoding/json"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/herenow/atomic-bm/internal/auth/casbin"
)

type WebsocketClient struct {
	policy      *casbin.PolicyEnforcer
	node        *centrifuge.Node
	workerPool  *ants.Pool
	isClosed    bool
	mu          sync.RWMutex
	tracer      trace.Tracer
	hookManager *HookManager

	logger *zap.Logger

	// Session tracking
	userSessions     map[string]string // map[sessionToken]connectionID
	tokenExpiredChan chan string
}

func NewWebsocketClient(policy *casbin.PolicyEnforcer, logger *zap.Logger) *WebsocketClient {
	workerPool, err := ants.NewPool(250)
	if err != nil {
		zap.L().Fatal("failed to instantiate worker pool", zap.Error(err))
	}

	wsClient := &WebsocketClient{
		policy:           policy,
		userSessions:     make(map[string]string),
		tokenExpiredChan: make(chan string),
		logger:           logger,
		workerPool:       workerPool,
		tracer:           otel.Tracer("websockets"),
		hookManager:      newHookManager(),
	}

	node, err := centrifuge.New(centrifuge.Config{
		LogLevel:                         mapZapToCentrifuge(zap.L().Level()),
		LogHandler:                       wsClient.handleLog,
		UseSingleFlight:                  true,
		NodeInfoMetricsAggregateInterval: 0, // 60 seconds
		ClientPresenceUpdateInterval:     0, // 27 seconds
		ClientExpiredCloseDelay:          0, // 25 seconds
		ClientExpiredSubCloseDelay:       0, // 25 seconds
		ClientStaleCloseDelay:            0, // 15 seconds
		ClientChannelPositionCheckDelay:  0, // 40 seconds
		ClientQueueMaxSize:               0, // Default (1MB)
		ClientChannelLimit:               0, // 128 Channels (Max Users Channels Subscribed)
		UserConnectionLimit:              0, // Unlimited (Max Users Sessions Simultaneous)
		ChannelMaxLength:                 0, // 255 Chars (Channel Name)
		HistoryMaxPublicationLimit:       0, // No Limit
		RecoveryMaxPublicationLimit:      0, // No Limit
		HistoryMetaTTL:                   0, // 30 Days
	})
	if err != nil {
		zap.L().Fatal("failed to start centrifuge websocket node", zap.Error(err))
	}
	wsClient.node = node

	wsClient.startNode()

	return wsClient
}

// startNode configures and runs the Centrifuge node and its event handlers.
func (w *WebsocketClient) startNode() {
	w.node.OnConnecting(func(ctx context.Context, e centrifuge.ConnectEvent) (centrifuge.ConnectReply, error) {
		cred, ok := centrifuge.GetCredentials(ctx)
		if !ok {
			return centrifuge.ConnectReply{}, centrifuge.ErrorBadRequest
		}
		return centrifuge.ConnectReply{Credentials: cred}, nil
	})

	w.node.OnConnect(func(client *centrifuge.Client) {
		ctx := client.Context()
		log := w.logger.With(zap.String("userID", client.UserID()))

		log.Debug("new connection establish")

		cred, ok := centrifuge.GetCredentials(client.Context())
		if !ok {
			log.Error("failed to get user credentials from context")
			client.Disconnect(centrifuge.DisconnectServerError)
		}

		var userInfo ClientInfo
		if err := json.Unmarshal(cred.Info, &userInfo); err != nil {
			log.Error("failed to unmarshal cred info into ClientInfo", zap.Error(err), zap.Any("context", ctx))
			client.Disconnect(centrifuge.DisconnectServerError)
		}

		w.trackSession(userInfo.Token, client.ID())

		client.OnSubscribe(func(e centrifuge.SubscribeEvent, cb centrifuge.SubscribeCallback) {
			if err := w.workerPool.Submit(func() {
				log = w.logger.With(zap.String("channel", e.Channel))

				if ctx.Err() != nil {
					log.Debug("client disconnected before subscribe task could run", zap.Error(ctx.Err()))
					return
				}

				if !w.checkPermission(ctx, client, e, cb) {
					return
				}

				if w.invokeDynamicSubscribeHooks(ctx, client, e, cb) {
					return
				}

				w.invokeStaticSubscribeHandler(ctx, client, e, cb)
			}); err != nil {
				w.logger.Error("failed to submit subscribe task to worker pool", zap.Error(err))
			}
		})

		client.OnUnsubscribe(func(e centrifuge.UnsubscribeEvent) {
			if err := w.workerPool.Submit(func() {
				if w.invokeDynamicUnsubscribeHooks(ctx, client, e) {
					return
				}

				w.invokeStaticUnsubscribeHandler(ctx, client, e)
			}); err != nil {
				log.Error("on unsubscribe handler", zap.Error(err))
			}
		})

		client.OnPublish(func(e centrifuge.PublishEvent, cb centrifuge.PublishCallback) {
			if err := w.workerPool.Submit(func() {
				if !client.IsSubscribed(e.Channel) {
					cb(centrifuge.PublishReply{}, centrifuge.ErrorPermissionDenied)
					return
				}

				allowed, err := w.policy.Enforce(client.UserID(), e.Channel, http.MethodPost)
				if err != nil {
					log.Error("failed when enforcing user permission for publishing", zap.Error(err))
					cb(centrifuge.PublishReply{}, centrifuge.ErrorInternal)
					return
				}

				if !allowed {
					cb(centrifuge.PublishReply{}, centrifuge.ErrorPermissionDenied)
					return
				}

				handler, exists := w.hookManager.publishHooks[e.Channel]
				if !exists {
					log.Error(fmt.Sprintf("failed when calling publish handler for %s channel", e.Channel))
					cb(centrifuge.PublishReply{}, centrifuge.ErrorMethodNotFound)
					return
				}

				handler(ctx, client, e, cb)
			}); err != nil {
				log.Error("on publish handler", zap.Error(err))
			}
		})

		client.OnRPC(func(e centrifuge.RPCEvent, cb centrifuge.RPCCallback) {
			if err := w.workerPool.Submit(func() {
				allowed, err := w.policy.Enforce(client.UserID(), e.Method, "CMD")
				if err != nil {
					log.Error("failed when enforcing user permission for RPC", zap.Error(err))
					cb(centrifuge.RPCReply{}, centrifuge.ErrorInternal)
					return
				}

				if !allowed {
					cb(centrifuge.RPCReply{}, centrifuge.ErrorPermissionDenied)
					return
				}

				handler, exists := w.hookManager.rpcHooks[e.Method]
				if !exists {
					log.Error(fmt.Sprintf("failed when calling rpc handler for %s method", e.Method))
					cb(centrifuge.RPCReply{}, centrifuge.ErrorMethodNotFound)
					return
				}

				handler(ctx, client, e, cb)
			}); err != nil {
				log.Error("on rpc handler", zap.Error(err))
			}
		})

		client.OnPresence(func(e centrifuge.PresenceEvent, cb centrifuge.PresenceCallback) {
			if err := w.workerPool.Submit(func() {
				log.Debug("calls presence", zap.String("channel", e.Channel))

				if !client.IsSubscribed(e.Channel) {
					cb(centrifuge.PresenceReply{}, centrifuge.ErrorPermissionDenied)
					return
				}

				handler, exists := w.hookManager.presenceHooks[e.Channel]
				if !exists {
					log.Error(fmt.Sprintf("failed when calling presence handler for %s channel", e.Channel))
					cb(centrifuge.PresenceReply{}, centrifuge.ErrorMethodNotFound)
					return
				}

				handler(ctx, client, e, cb)
			}); err != nil {
				log.Error("on presence handler", zap.Error(err))
			}
		})

		client.OnHistory(func(e centrifuge.HistoryEvent, cb centrifuge.HistoryCallback) {
			if err := w.workerPool.Submit(func() {
				log.Debug("calls history", zap.String("channel", e.Channel))

				handler, exists := w.hookManager.historyHooks[e.Channel]
				if !exists {
					log.Error(fmt.Sprintf("failed when calling history handler for %s channel", e.Channel))
					cb(centrifuge.HistoryReply{}, centrifuge.ErrorMethodNotFound)
					return
				}

				handler(ctx, client, e, cb)
			}); err != nil {
				log.Error("on history handler", zap.Error(err))
			}
		})

		client.OnMessage(func(e centrifuge.MessageEvent) {
			log.Debug("message received", zap.String("data", string(e.Data)))
		})

		client.OnAlive(func() {
			log.Debug("connection is still active")
		})

		client.OnDisconnect(func(e centrifuge.DisconnectEvent) {
			log.Debug("client disconnected", zap.String("reason", e.Reason))

			w.untrackSession(userInfo.Token)
		})
	})

	if err := w.node.Run(); err != nil {
		w.logger.Fatal("failed to start websocket node server", zap.Error(err))
	}

	go w.disconnectExpiredTokens()
}

func (w *WebsocketClient) checkPermission(ctx context.Context, client *centrifuge.Client, e centrifuge.SubscribeEvent, cb centrifuge.SubscribeCallback) bool {
	spanCtx, span := w.tracer.Start(ctx, "checkPermission")
	defer span.End()

	allowed, err := w.policy.Enforce(client.UserID(), e.Channel, http.MethodGet)
	if err != nil {
		w.logger.Error("failed when enforcing user permission for subscribing", zap.Error(err), zap.Any("context", spanCtx))
		cb(centrifuge.SubscribeReply{}, centrifuge.ErrorInternal)
		return false
	}

	if !allowed {
		cb(centrifuge.SubscribeReply{}, centrifuge.ErrorPermissionDenied)
		return false
	}

	return true
}

func (w *WebsocketClient) Close() {
	w.mu.Lock()
	if !w.isClosed {
		close(w.tokenExpiredChan)
		w.workerPool.Release()
		w.isClosed = true
	}
	w.mu.Unlock()

	if w.node != nil {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		w.node.Shutdown(shutdownCtx)
	}
}

// NotifyTokenExpirationAndDisconnect sends a notification of token expiration to the WebSocketClient's
// tokenExpiredChan. If the channel is closed and cannot accept the token, it logs an error.
// The token expiration notification is used to disconnect the associated websocket client.
func (w *WebsocketClient) NotifyTokenExpirationAndDisconnect(ctx context.Context, token string) {
	w.mu.RLock()
	defer w.mu.RUnlock()
	if w.isClosed {
		w.logger.Error("attempted to notify token expiration in a closed channel", zap.Any("context", ctx))
		return
	}
	w.tokenExpiredChan <- token
}

// GetNode returns the underlying Centrifuge node.
func (w *WebsocketClient) GetNode() *centrifuge.Node {
	if w.node == nil {
		w.logger.Fatal("websocket: centrifuge node is nil")
	}
	return w.node
}

// Publish sends data to a channel.
func (w *WebsocketClient) Publish(ctx context.Context, channel string, data []byte) error {
	_, span := w.tracer.Start(ctx, "Publish")
	defer span.End()

	_, err := w.node.Publish(channel, data)
	return err
}

// PublishWithResult sends data to a channel.
func (w *WebsocketClient) PublishWithResult(ctx context.Context, channel string, data []byte) (*centrifuge.PublishResult, error) {
	_, span := w.tracer.Start(ctx, "PublishWithResult")
	defer span.End()

	res, err := w.node.Publish(channel, data)
	if err != nil {
		return nil, errors.Wrap(err, "websocket: failed to publish message")
	}
	return &res, nil
}

// ClientCount returns the number of clients subscribed to a channel.
func (w *WebsocketClient) ClientCount(channel string) (int, error) {
	p, err := w.node.Presence(channel)
	if err != nil {
		return 0, err
	}
	return len(p.Presence), nil
}

// untrackSession disassociates a session token from a connection ID.
func (w *WebsocketClient) untrackSession(sessionToken string) {
	w.mu.Lock()
	defer w.mu.Unlock()
	delete(w.userSessions, sessionToken)
}

// trackSession associates a session token with a connection ID.
func (w *WebsocketClient) trackSession(sessionToken, connectionID string) {
	w.mu.Lock()
	defer w.mu.Unlock()
	w.userSessions[sessionToken] = connectionID
}

// disconnectExpiredTokens listens for expired tokens and disconnects the associated clients.
func (w *WebsocketClient) disconnectExpiredTokens() {
	for expiredToken := range w.tokenExpiredChan {
		w.mu.Lock()
		connectionID, exists := w.userSessions[expiredToken]
		if !exists {
			w.mu.Unlock()
			continue
		}
		delete(w.userSessions, expiredToken)
		w.mu.Unlock()

		conns := w.node.Hub().Connections()
		client, exists := conns[connectionID]
		if !exists {
			w.logger.Debug("client connection not found")
			continue
		}

		w.logger.Debug("client disconnected",
			zap.String("userID", client.UserID()),
			zap.String("connectionID", connectionID),
			zap.String("sessionToken", expiredToken))
		client.Disconnect(centrifuge.DisconnectExpired)
	}

	w.isClosed = true
}

// invokeStaticSubscribeHandler calls the registered static handler for a subscription event.
func (w *WebsocketClient) invokeStaticSubscribeHandler(ctx context.Context, client *centrifuge.Client, e centrifuge.SubscribeEvent, cb centrifuge.SubscribeCallback) {
	spanCtx, span := w.tracer.Start(ctx, "invokeStaticSubscribeHandler")
	defer span.End()

	handler, exists := w.hookManager.subscribeHooks[e.Channel]
	if !exists {
		w.logger.Error(fmt.Sprintf("subscribe handler not found for channel %s", e.Channel), zap.Any("context", spanCtx))
		cb(centrifuge.SubscribeReply{}, centrifuge.ErrorMethodNotFound)
		return
	}
	handler(ctx, client, e, cb)
}

// invokeDynamicSubscribeHooks calls the first matching dynamic handler for a subscription event.
func (w *WebsocketClient) invokeDynamicSubscribeHooks(ctx context.Context, client *centrifuge.Client, e centrifuge.SubscribeEvent, cb centrifuge.SubscribeCallback) bool {
	spanCtx, span := w.tracer.Start(ctx, "invokeDynamicSubscribeHooks")
	defer span.End()

	for _, dynamicHook := range w.hookManager.dynamicSubscribeHooks {
		if matched, params := matchDynamicPattern(dynamicHook.pattern, e.Channel); matched {
			dynamicHook.handler(spanCtx, client, e, cb, params)
			return true
		}
	}
	return false
}

// invokeDynamicUnsubscribeHooks calls the first matching dynamic handler for an unsubscription event.
func (w *WebsocketClient) invokeDynamicUnsubscribeHooks(ctx context.Context, client *centrifuge.Client, e centrifuge.UnsubscribeEvent) bool {
	spanCtx, span := w.tracer.Start(ctx, "invokeDynamicUnsubscribeHooks")
	defer span.End()

	for _, dynamicHook := range w.hookManager.dynamicUnsubscribeHooks {
		if matched, params := matchDynamicPattern(dynamicHook.pattern, e.Channel); matched {
			dynamicHook.handler(spanCtx, client, e, params)
			return true
		}
	}
	return false
}

// invokeStaticUnsubscribeHandler calls the registered static handler for an unsubscription event.
func (w *WebsocketClient) invokeStaticUnsubscribeHandler(ctx context.Context, client *centrifuge.Client, e centrifuge.UnsubscribeEvent) {
	spanCtx, span := w.tracer.Start(ctx, "invokeStaticUnsubscribeHandler")
	defer span.End()

	handler, exists := w.hookManager.unsubscribeHooks[e.Channel]
	if !exists {
		w.logger.Warn(fmt.Sprintf("unsubscribe handler not found for channel %s", e.Channel), zap.Any("context", spanCtx))
		return
	}
	handler(ctx, client, e)
}

// RegisterSubscribeHook registers a handler for the subscribe event on a specific channel.
func (w *WebsocketClient) RegisterSubscribeHook(channel string, handler SubscribeHook) {
	w.hookManager.RegisterSubscribeHook(channel, handler)
}

// RegisterUnsubscribeHook registers a handler for the unsubscribe event on a specific channel.
func (w *WebsocketClient) RegisterUnsubscribeHook(channel string, handler UnsubscribeHook) {
	w.hookManager.RegisterUnsubscribeHook(channel, handler)
}

// RegisterPublishHook registers a handler for the publish event on a specific channel.
func (w *WebsocketClient) RegisterPublishHook(channel string, handler PublishHook) {
	w.hookManager.RegisterPublishHook(channel, handler)
}

// RegisterRPCHook registers a handler for an RPC event with a specific method.
func (w *WebsocketClient) RegisterRPCHook(method string, handler RPCHook) {
	w.hookManager.RegisterRPCHook(method, handler)
}

// RegisterPresenceHook registers a handler for the presence event on a specific channel.
func (w *WebsocketClient) RegisterPresenceHook(channel string, handler PresenceHook) {
	w.hookManager.RegisterPresenceHook(channel, handler)
}

// RegisterHistoryHook registers a handler for the history event on a specific channel.
func (w *WebsocketClient) RegisterHistoryHook(channel string, handler HistoryHook) {
	w.hookManager.RegisterHistoryHook(channel, handler)
}

// RegisterDynamicSubscribeHook registers a handler for a dynamic channel pattern.
func (w *WebsocketClient) RegisterDynamicSubscribeHook(pattern string, handler DynamicSubscribeHook) {
	w.hookManager.RegisterDynamicSubscribeHook(pattern, handler)
}

// RegisterDynamicUnsubscribeHook registers a handler for a dynamic channel pattern.
func (w *WebsocketClient) RegisterDynamicUnsubscribeHook(pattern string, handler DynamicUnsubscribeHook) {
	w.hookManager.RegisterDynamicUnsubscribeHook(pattern, handler)
}

// matchDynamicPattern matches a channel to a dynamic pattern (e.g., "bots/:id/state") and extracts parameters.
func matchDynamicPattern(pattern, channel string) (bool, map[string]string) {
	patternParts := strings.Split(pattern, "/")
	channelParts := strings.Split(channel, "/")

	if len(patternParts) != len(channelParts) {
		return false, nil
	}

	params := make(map[string]string)
	for i, part := range patternParts {
		if strings.HasPrefix(part, ":") {
			paramName := part[1:]
			params[paramName] = channelParts[i]
		} else if part != channelParts[i] {
			return false, nil
		}
	}

	return true, params
}

// handleLog adapts Centrifuge logs to the application's logger.
func (w *WebsocketClient) handleLog(e centrifuge.LogEntry) {
	zapLevel := mapCentrifugeToZap(e.Level)
	w.logger.Log(zapLevel, e.Message, zap.Any("fields", e.Fields))
}

// mapCentrifugeToZap maps Centrifuge log levels to Zap log levels.
func mapCentrifugeToZap(cLogLevel centrifuge.LogLevel) zapcore.Level {
	switch cLogLevel {
	case centrifuge.LogLevelNone:
		return zapcore.DebugLevel
	case centrifuge.LogLevelTrace:
		return zapcore.DebugLevel
	case centrifuge.LogLevelDebug:
		return zapcore.DebugLevel
	case centrifuge.LogLevelInfo:
		return zapcore.InfoLevel
	case centrifuge.LogLevelWarn:
		return zapcore.WarnLevel
	case centrifuge.LogLevelError:
		return zapcore.ErrorLevel
	default:
		return zapcore.InfoLevel
	}
}

// mapZapToCentrifuge maps Zap log levels to Centrifuge log levels.
func mapZapToCentrifuge(zapLevel zapcore.Level) centrifuge.LogLevel {
	switch zapLevel {
	case zapcore.DebugLevel:
		return centrifuge.LogLevelDebug
	case zapcore.InfoLevel:
		return centrifuge.LogLevelInfo
	case zapcore.WarnLevel:
		return centrifuge.LogLevelWarn
	case zapcore.ErrorLevel:
		return centrifuge.LogLevelError
	default:
		return centrifuge.LogLevelInfo
	}
}
