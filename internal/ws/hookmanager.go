package ws

import (
	"context"
	"sync"

	"github.com/centrifugal/centrifuge"
)

// SubscribeHook is a function type for subscribed event hooks.
type SubscribeHook func(ctx context.Context, client *centrifuge.Client, event centrifuge.SubscribeEvent, cb centrifuge.SubscribeCallback)

// DynamicSubscribeHook is a function type for dynamic subscribe event hooks.
type DynamicSubscribeHook func(ctx context.Context, client *centrifuge.Client, event centrifuge.SubscribeEvent, cb centrifuge.SubscribeCallback, params map[string]string)

// UnsubscribeHook is a function type for unsubscribed event hooks.
type UnsubscribeHook func(ctx context.Context, client *centrifuge.Client, event centrifuge.UnsubscribeEvent)

// DynamicUnsubscribeHook is a function type for dynamic unsubscribe event hooks.
type DynamicUnsubscribeHook func(ctx context.Context, client *centrifuge.Client, event centrifuge.UnsubscribeEvent, params map[string]string)

// PublishHook is a function type for publication event hooks.
type PublishHook func(ctx context.Context, client *centrifuge.Client, event centrifuge.PublishEvent, cb centrifuge.PublishCallback)

// RPCHook is a function type for RPC event hooks.
type RPCHook func(ctx context.Context, client *centrifuge.Client, event centrifuge.RPCEvent, cb centrifuge.RPCCallback)

// PresenceHook is a function type for presence event hooks.
type PresenceHook func(ctx context.Context, client *centrifuge.Client, event centrifuge.PresenceEvent, cb centrifuge.PresenceCallback)

// HistoryHook is a function type for history event hooks.
type HistoryHook func(ctx context.Context, client *centrifuge.Client, event centrifuge.HistoryEvent, cb centrifuge.HistoryCallback)

// HookManager manages registration and invocation of hooks.
type HookManager struct {
	mu sync.RWMutex // Mutex for handling concurrent access

	unsubscribeHooks map[string]UnsubscribeHook
	subscribeHooks   map[string]SubscribeHook
	publishHooks     map[string]PublishHook
	rpcHooks         map[string]RPCHook
	presenceHooks    map[string]PresenceHook
	historyHooks     map[string]HistoryHook
	// DynamicSubscribeHooks stores handlers for dynamic channel patterns.
	dynamicSubscribeHooks []struct {
		pattern string
		handler DynamicSubscribeHook
	}
	// DynamicUnsubscribeHooks stores handlers for dynamic channel patterns.
	dynamicUnsubscribeHooks []struct {
		pattern string
		handler DynamicUnsubscribeHook
	}
}

// newHookManager creates a new HookManager instance.
func newHookManager() *HookManager {
	return &HookManager{
		mu:               sync.RWMutex{},
		subscribeHooks:   make(map[string]SubscribeHook),
		unsubscribeHooks: make(map[string]UnsubscribeHook),
		publishHooks:     make(map[string]PublishHook),
		rpcHooks:         make(map[string]RPCHook),
		presenceHooks:    make(map[string]PresenceHook),
		historyHooks:     make(map[string]HistoryHook),
		dynamicSubscribeHooks: make([]struct {
			pattern string
			handler DynamicSubscribeHook
		}, 0),
		dynamicUnsubscribeHooks: make([]struct {
			pattern string
			handler DynamicUnsubscribeHook
		}, 0),
	}
}

// RegisterDynamicUnsubscribeHook registers a handler function for a dynamic channel pattern.
func (h *HookManager) RegisterDynamicUnsubscribeHook(pattern string, handler DynamicUnsubscribeHook) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.dynamicUnsubscribeHooks = append(h.dynamicUnsubscribeHooks, struct {
		pattern string
		handler DynamicUnsubscribeHook
	}{pattern: pattern, handler: handler})
}

// RegisterUnsubscribeHook registers a handler function for the subscribe event on a specific channel.
func (h *HookManager) RegisterUnsubscribeHook(channel string, handler UnsubscribeHook) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.unsubscribeHooks[channel] = handler
}

// RegisterDynamicSubscribeHook registers a handler function for a dynamic channel pattern.
func (h *HookManager) RegisterDynamicSubscribeHook(pattern string, handler DynamicSubscribeHook) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.dynamicSubscribeHooks = append(h.dynamicSubscribeHooks, struct {
		pattern string
		handler DynamicSubscribeHook
	}{pattern: pattern, handler: handler})
}

// RegisterSubscribeHook registers a handler function for the subscribe event on a specific channel.
func (h *HookManager) RegisterSubscribeHook(channel string, handler SubscribeHook) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.subscribeHooks[channel] = handler
}

// RegisterPublishHook registers a handler function for the publish event on a specific channel.
func (h *HookManager) RegisterPublishHook(channel string, handler PublishHook) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.publishHooks[channel] = handler
}

// RegisterRPCHook registers a handler function for the RPC event with a specific method.
func (h *HookManager) RegisterRPCHook(method string, handler RPCHook) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.rpcHooks[method] = handler
}

// RegisterPresenceHook registers a handler function for the presence event on a specific channel.
func (h *HookManager) RegisterPresenceHook(channel string, handler PresenceHook) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.presenceHooks[channel] = handler
}

// RegisterHistoryHook registers a handler function for the history event on a specific channel.
func (h *HookManager) RegisterHistoryHook(channel string, handler HistoryHook) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.historyHooks[channel] = handler
}
