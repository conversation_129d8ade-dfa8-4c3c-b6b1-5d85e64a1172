package app

import (
	"context"
	"fmt"
	"sync/atomic"

	"github.com/gin-gonic/gin"
	atomicbm "github.com/herenow/atomic-bm"
	"github.com/herenow/atomic-bm/internal/auth/casbin"
	"github.com/herenow/atomic-bm/internal/auth/session"
	"github.com/herenow/atomic-bm/internal/pkg/cache"
	"github.com/herenow/atomic-bm/internal/pkg/cronjob"
	"github.com/herenow/atomic-bm/internal/ws"
	pushnotify "github.com/herenow/atomic-bm/pkg/firebase/fcm"
	"github.com/herenow/atomic-bm/pkg/logger"
	"github.com/herenow/atomic-bm/pkg/otel/otellogs"
	"github.com/herenow/atomic-bm/pkg/otel/otelmetrics"
	"github.com/herenow/atomic-bm/pkg/otel/oteltraces"
	"github.com/uptrace/bun"
	"go.opentelemetry.io/otel/metric"
	noopmetric "go.opentelemetry.io/otel/metric/noop"
	"go.opentelemetry.io/otel/trace"
	nooptrace "go.opentelemetry.io/otel/trace/noop"
	"go.uber.org/zap"
)

const (
	CreateUserCMD             = "create-user"
	ServerCMD                 = "server"
	LocalBotRegionalServerCMD = "br-local-server"

	ServerName = "botmanager"
	_ScopeName = "main"
)

// appCtxKey is an unexported type for the context key to avoid collisions.
type appCtxKey struct{}

// ContextWithApp stores the App instance in the context.
func ContextWithApp(ctx context.Context, app *App) context.Context {
	return context.WithValue(ctx, appCtxKey{}, app)
}

// App holds all the components of the application.
type App struct {
	// rootCtx is the original context, which does not get canceled by Stop().
	// It's used for running shutdown hooks.
	rootCtx context.Context
	// appCtx is the main application context. It is canceled when app.Stop() is called.
	appCtx    context.Context
	ctxCancel func()

	cfg *Config

	stopped uint32

	onStop appHooks

	router   *gin.Engine
	apiGroup *gin.RouterGroup
	wsGroup  *gin.RouterGroup

	logger      *zap.Logger
	otelMetrics metric.MeterProvider
	otelTraces  trace.TracerProvider

	tracer trace.Tracer

	policy  *casbin.PolicyEnforcer
	session *session.Manager

	pushNotifyClient *pushnotify.Client
	cronJobManager   *cronjob.Manager

	wsClient *ws.WebsocketClient

	db    *bun.DB
	cache cache.ICache
}

// New creates and initializes a new App instance with the given configuration.
// It sets up core, command-agnostic components like the logger, telemetry, and database connection.
func New(cfg *Config, logger *zap.Logger) (*App, error) {
	app := &App{
		rootCtx: context.Background(),
		cfg:     cfg,
		logger:  logger,
	}
	app.appCtx, app.ctxCancel = context.WithCancel(app.rootCtx)

	if err := app.initTelemetry(); err != nil {
		if app.logger != nil {
			app.logger.Warn("failed to initialize telemetry", zap.Error(err))
		}
	}

	app.tracer = app.otelTraces.Tracer(_ScopeName)
	ctx := ContextWithApp(app.appCtx, app)

	spanCtx, span := app.tracer.Start(ctx, _ScopeName)
	defer span.End()
	app.appCtx = spanCtx

	app.initDB()

	if err := app.initCache(); err != nil {
		app.logger.Fatal("failed to initialize cache", zap.Error(err))
	}

	return app, nil
}

// initTelemetry sets up OpenTelemetry for tracing and metrics.
func (app *App) initTelemetry() error {
	if app.cfg.OpenTelemetry.Traces.Enabled {
		tp, err := oteltraces.NewTracerProvider(app.rootCtx, oteltraces.Config{
			ServiceName: ServerName,
			BearerToken: app.cfg.OpenTelemetry.BearerToken,
			Endpoint:    app.cfg.OpenTelemetry.ExporterURL,
			SampleRate:  app.cfg.OpenTelemetry.Traces.SampleRate,
			Insecure:    app.cfg.OpenTelemetry.Traces.Insecure,
		})
		if err != nil {
			app.otelTraces = nooptrace.NewTracerProvider()
			return fmt.Errorf("failed to init otel tracing: %w", err)
		}
		app.otelTraces = tp
		app.OnStop("otelTracing.Shutdown", func(ctx context.Context, app *App) error {
			if tp != nil {
				return tp.Shutdown(ctx)
			}
			return nil
		})
	} else {
		app.otelTraces = nooptrace.NewTracerProvider()
	}

	if app.cfg.OpenTelemetry.Metrics.Enabled {
		mp, err := otelmetrics.NewProvider(app.rootCtx, otelmetrics.Config{
			ServiceName: ServerName,
			Endpoint:    app.cfg.OpenTelemetry.ExporterURL,
			BearerToken: app.cfg.OpenTelemetry.BearerToken,
			Insecure:    app.cfg.OpenTelemetry.Metrics.Insecure,
		})
		if err != nil {
			app.otelMetrics = noopmetric.NewMeterProvider()
			return fmt.Errorf("failed to init otel metrics: %w", err)
		}

		app.otelMetrics = mp
		app.OnStop("otelMetrics.Shutdown", func(ctx context.Context, app *App) error {
			if mp != nil {
				return mp.Shutdown(ctx)
			}
			return nil
		})
	} else {
		app.otelMetrics = noopmetric.NewMeterProvider()
	}

	if app.cfg.OpenTelemetry.Logs.Enabled {
		otelCore, otelShutdown, err := otellogs.NewCore(app.appCtx, otellogs.Config{
			ServiceName:    ServerName,
			ServiceVersion: atomicbm.Version,
			OtelEndpoint:   app.cfg.OpenTelemetry.ExporterURL,
			BearerToken:    app.cfg.OpenTelemetry.BearerToken,
			Insecure:       app.cfg.OpenTelemetry.Metrics.Insecure,
		})
		if err != nil {
			app.logger.Warn("could not initialize OpenTelemetry logging, continuing without it", zap.Error(err))
		} else {
			app.logger = logger.WithCore(app.logger, otelCore)
			app.logger.Info("OpenTelemetry logging enabled")

			app.OnStop("otelLogs.Shutdown", func(ctx context.Context, app *App) error {
				app.logger.Sync()
				return otelShutdown(ctx)
			})
		}
	}

	return nil
}

// InitSession initializes the session manager.
func (app *App) InitSession() {
	_, span := app.tracer.Start(app.appCtx, "main/initSession")
	defer span.End()
	app.session = session.NewManager(app.DB(), app.cfg.SessionManager)
}

// InitPushNotifyClient initializes the Firebase push notification client.
func (app *App) InitPushNotifyClient() {
	ctx, span := app.tracer.Start(app.appCtx, "main/initPushNotifyClient")
	defer span.End()

	var err error
	app.pushNotifyClient, err = pushnotify.NewPushNotifyClient(ctx, &app.Config().PushNotify)
	if err != nil {
		app.logger.Fatal("Failed to init push notify client service", zap.Error(err))
	}
}

// InitPolicies initializes the policy enforcer.
func (app *App) InitPolicies() {
	ctx, span := app.tracer.Start(app.appCtx, "main/initPolicies")
	defer span.End()
	app.policy = casbin.NewPolicyEnforcer(ctx, app.DB(), app.Logger())
}

// Logger returns the application's logger instance.
func (app *App) Logger() *zap.Logger {
	return app.logger
}

// Stop gracefully shuts down the application by canceling the main context
// and running all registered shutdown hooks.
func (app *App) Stop() {
	if !atomic.CompareAndSwapUint32(&app.stopped, 0, 1) {
		return // Stop can only be called once.
	}
	if app.wsClient != nil {
		app.WebSocketClient().Close()
	}
	app.ctxCancel()
	if err := app.onStop.Run(app.rootCtx, app); err != nil {
		app.logger.Error("Error during shutdown", zap.Error(err))
	}
}

// OnStop registers a function to be called when the application is stopping.
func (app *App) OnStop(name string, fn HookFunc) {
	app.onStop.Add(newHook(name, fn))
}

// Context returns the main, cancellable application context.
func (app *App) Context() context.Context {
	return app.appCtx
}

// Done returns a channel that's closed when the application is shutting down.
func (app *App) Done() <-chan struct{} {
	return app.appCtx.Done()
}

// Config returns the application's configuration.
func (app *App) Config() *Config {
	return app.cfg
}

// Debugging returns true if the application is in debug mode.
func (app *App) Debugging() bool {
	return app.cfg.Debug
}

// NotifyClient returns the push notification client.
func (app *App) NotifyClient() *pushnotify.Client {
	if app.pushNotifyClient == nil {
		app.logger.Fatal("Push notify client is not initialized")
	}
	return app.pushNotifyClient
}

// RegisterCronJobService sets the cron job manager for the application.
func (app *App) RegisterCronJobService(manager *cronjob.Manager) {
	app.cronJobManager = manager
}

// CronJobManager returns the cron job manager.
func (app *App) CronJobManager() *cronjob.Manager {
	if app.cronJobManager == nil {
		app.logger.Fatal("Cron job manager is not initialized")
	}
	return app.cronJobManager
}

// RegisterWebsocketClient sets the websocket client for the application.
func (app *App) RegisterWebsocketClient(client *ws.WebsocketClient) {
	app.wsClient = client
}

// WebSocketClient returns the websocket client.
func (app *App) WebSocketClient() *ws.WebsocketClient {
	if app.wsClient == nil {
		app.logger.Fatal("Websocket client is not initialized")
	}
	return app.wsClient
}

// Session returns the session manager.
func (app *App) Session() *session.Manager {
	return app.session
}

// Policy returns the policy enforcer.
func (app *App) Policy() *casbin.PolicyEnforcer {
	return app.policy
}

// initCache initializes the Ristretto cache.
func (app *App) initCache() error {
	var err error
	app.cache, err = cache.New(app.cfg.Cache)
	if err != nil {
		return err
	}

	app.OnStop("cache.Close", func(ctx context.Context, app *App) error {
		app.cache.Close()
		return nil
	})

	return nil
}

// Cache returns the application's cache instance.
func (app *App) Cache() cache.ICache {
	return app.cache
}
