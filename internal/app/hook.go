package app

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"go.uber.org/multierr"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
)

// HookFunc is the function signature for a lifecycle hook. It receives the
// application's root context and a pointer to the App instance.
type HookFunc func(ctx context.Context, app *App) error

// appHooks manages a collection of lifecycle hooks (e.g., for startup or shutdown).
type appHooks struct {
	mu    sync.Mutex
	hooks []appHook
}

// Add appends a new named hook to the collection.
func (hs *appHooks) Add(hook appHook) {
	hs.mu.Lock()
	defer hs.mu.Unlock()

	hs.hooks = append(hs.hooks, hook)
}

// Run executes all registered hooks concurrently. It waits for all hooks
// to complete and aggregates any errors that occur.
func (hs *appHooks) Run(ctx context.Context, app *App) error {
	hs.mu.Lock()
	defer hs.mu.Unlock()

	// Use an error group to manage concurrent execution and error collection.
	g, gCtx := errgroup.WithContext(ctx)
	var allErrors error

	for _, h := range hs.hooks {
		// Capture the hook variable to ensure the correct value is used in the goroutine.
		hook := h
		g.Go(func() error {
			// Run the hook and capture the error.
			err := hook.run(gCtx, app)
			if err != nil {
				// Use multierr to aggregate all errors from the hooks.
				allErrors = multierr.Append(allErrors, fmt.Errorf("hook %q failed: %w", hook.name, err))
			}
			return nil // errgroup itself doesn't need to fail fast.
		})
	}

	// Wait for all hooks to finish.
	_ = g.Wait()
	return allErrors
}

// appHook represents a single named lifecycle hook.
type appHook struct {
	name string
	fn   HookFunc
}

// newHook creates a new hook with a given name and function.
func newHook(name string, fn HookFunc) appHook {
	return appHook{
		name: name,
		fn:   fn,
	}
}

// run executes a single hook with a dedicated timeout.
func (h appHook) run(ctx context.Context, app *App) error {
	// Each hook gets a 30-second timeout to complete.
	const timeout = 30 * time.Second
	hookCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// Use a channel to communicate the result of the hook execution.
	done := make(chan error, 1)

	go func() {
		start := time.Now()
		app.logger.Debug("Running hook", zap.String("hook", h.name))

		// Execute the hook function and send the result to the channel.
		done <- h.fn(hookCtx, app)

		// Log if the hook took an unusually long time to complete.
		if d := time.Since(start); d > time.Second {
			app.logger.Info("Hook executed",
				zap.String("hook", h.name),
				zap.Duration("duration", d),
			)
		}
	}()

	// Wait for either the hook to complete or the timeout to be reached.
	select {
	case err := <-done:
		// The hook completed.
		if err != nil {
			app.logger.Error("Hook returned an error",
				zap.String("hook", h.name),
				zap.Error(err),
			)
			return err
		}
		app.logger.Debug("Hook finished successfully", zap.String("hook", h.name))
		return nil
	case <-hookCtx.Done():
		// The hook timed out or the parent context was canceled.
		err := hookCtx.Err()
		if errors.Is(err, context.DeadlineExceeded) {
			app.logger.Error("Hook timed out",
				zap.String("hook", h.name),
				zap.Duration("timeout", timeout),
			)
			return fmt.Errorf("hook %q timed out after %s", h.name, timeout)
		}
		// The parent context was canceled, which is expected during shutdown.
		app.logger.Warn("Hook canceled",
			zap.String("hook", h.name),
			zap.Error(err),
		)
		return err
	}
}
