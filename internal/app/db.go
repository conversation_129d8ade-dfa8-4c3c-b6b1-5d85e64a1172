package app

import (
	"database/sql"
	"os"

	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/pgdialect"
	"github.com/uptrace/bun/driver/pgdriver"
	"github.com/uptrace/bun/extra/bundebug"
)

func (app *App) initDB() {
	_, span := app.tracer.Start(app.appCtx, "main/initDB")
	defer span.End()

	cfg := app.cfg.Database

	var options []pgdriver.Option

	if cfg.DSN != "" {
		options = append(options, pgdriver.WithDSN(cfg.DSN))
	}
	options = append(options, pgdriver.WithInsecure(true))

	pgdb := sql.OpenDB(pgdriver.NewConnector(options...))
	db := bun.NewDB(pgdb, pgdialect.New())

	_ = os.Setenv("BUNDEBUG", app.cfg.BunDebug)
	db.AddQueryHook(bundebug.NewQueryHook(
		bundebug.WithEnabled(app.Debugging()),
		bundebug.FromEnv("BUNDEBUG"),
	))

	app.db = db
}

func (app *App) DB() *bun.DB {
	return app.db
}
