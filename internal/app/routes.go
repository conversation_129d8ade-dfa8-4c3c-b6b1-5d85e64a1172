package app

import (
	"net/http"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"

	_ "github.com/herenow/atomic-bm/docs"
	"github.com/herenow/atomic-bm/internal/middlewares"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
	"github.com/herenow/atomic-bm/internal/pkg/ginx"
)

//	@title			Bot Manager API
//	@version		1.10.5
//	@description	API for managing cryptocurrency trading bots on multiple exchanges.

//	@host		api-term.htz-fsn-02.liquidbooks.io
//	@BasePath	/api/v1

//	@securityDefinitions.apikey	Bearer
//	@in							header
//	@name						Authorization
//	@description				Type "Bearer" followed by a space and the token.

// InitRouter initializes the Gin router and API groups.
func (app *App) InitRouter() {
	_, span := app.tracer.Start(app.appCtx, "main/initRouter")
	defer span.End()

	app.router = gin.New()
	if app.Debugging() {
		gin.SetMode(gin.DebugMode)
		pprof.Register(app.router, "debug")
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	app.router.NoMethod(func(c *gin.Context) {
		ginx.ResError(c, errors.MethodNotAllowed("", http.StatusText(http.StatusMethodNotAllowed)))
	})

	app.router.NoRoute(func(c *gin.Context) {
		ginx.ResError(c, errors.NotFound("", http.StatusText(http.StatusNotFound)))
	})

	app.router.ForwardedByClientIP = true
	app.router.RemoveExtraSlash = true

	app.router.Use(cors.New(cors.Config{
		AllowWebSockets:  true,
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization", gin.PlatformCloudflare},
		AllowCredentials: true,
		AllowAllOrigins:  true,
		MaxAge:           12,
		AllowWildcard:    true,
	}))

	app.router.Use(func(c *gin.Context) {
		c.Set("app", app)
		c.Next()
	})

	app.apiGroup = app.router.Group("/api/v1")
	app.router.Use(otelgin.Middleware(ServerName, otelgin.WithFilter(func(req *http.Request) bool {
		if req.URL.Path == "/api/v1/health" {
			return false
		}
		return true
	})))
	app.apiGroup.Use(middlewares.Logger(app.Logger()))
	app.apiGroup.Use(app.session.LoadAndSave(), app.Authorizer())

	app.wsGroup = app.router.Group("/ws")
	app.wsGroup.Use(middlewares.Logger(app.Logger()))
	app.wsGroup.Use(app.session.LoadFromQueryParam(), app.Authorizer(), middlewares.GinToContextMiddleware())
}

func (app *App) HTTPHandler() http.Handler {
	return app.router
}

func (app *App) APIGroup() *gin.RouterGroup {
	return app.apiGroup
}

func (app *App) WSRouter() *gin.RouterGroup {
	return app.wsGroup
}
