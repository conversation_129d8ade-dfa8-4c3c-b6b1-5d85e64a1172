import type { BaseNotesResponse, Note } from '$types';

/**
 * Client for interacting with the notes API.
 * @returns An object containing functions for fetching, creating, and deleting notes.
 */
export function notesClient() {
	/**
	 * Fetches all notes for a given bot.
	 * @param bot_id The ID of the bot to fetch notes for.
	 * @returns A promise that resolves to a BaseNotesResponse object containing the fetched notes or an error.
	 */
	async function fetchNotes(bot_id: string): Promise<BaseNotesResponse> {
		try {
			const req = await fetch(`/api/notes/${bot_id}`);
			if (!req.ok) {
				throw new Error(await req.text());
			}

			const res: Note[] = await req.json();
			return {
				notes: res,
				err: null
			};
		} catch (err) {
			return {
				notes: [],
				err: err as Error
			};
		}
	}

	/**
	 * Creates a new note for a given bot.
	 * @param bot_id The ID of the bot to create the note for.
	 * @param note The note object to create.
	 * @returns A promise that resolves to null if the note was created successfully, or an error if the creation failed.
	 */
	async function createNote(bot_id: string, note: Note): Promise<null | Error> {
		try {
			const req = await fetch(`/api/notes/${bot_id}`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(note)
			});
			if (!req.ok) {
				throw new Error(await req.text());
			}

			return null;
		} catch (err) {
			return err as Error;
		}
	}

	/**
	 * Deletes a note for a given bot.
	 * @param bot_id The ID of the bot to delete the note for.
	 * @param note_id The ID of the note to delete.
	 * @returns A promise that resolves to null if the note was deleted successfully, or an error if the deletion failed.
	 */
	async function deleteNote(bot_id: string, note_id: string): Promise<null | Error> {
		try {
			const req = await fetch(`/api/notes/${bot_id}?note_id=${note_id}`, {
				method: 'DELETE'
			});
			if (!req.ok) {
				throw new Error(await req.text());
			}

			return null;
		} catch (err) {
			return err as Error;
		}
	}

	return {
		fetchNotes,
		createNote,
		deleteNote
	};
}
