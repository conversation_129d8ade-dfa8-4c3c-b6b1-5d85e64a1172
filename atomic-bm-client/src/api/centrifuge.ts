import { updateWebsocketStore } from '$stores/websocket-store';
// import { userStore } from '$lib/store/user.store';
// import { updateTrollboxStore, trollboxStore } from '$lib/store/trollbox.store';
import {
	Centrifuge as CentrifugeClass,
	type Centrifuge as CentrifugeTypes,
	type Subscription
} from 'centrifuge';
import { convertTimestampToISO, getUnixTimestamp, transformToObjectArray, toast } from '$utils';
// import { updateUIStore } from '$lib/store/ui.store';
import { browser } from '$app/environment';
import { getToken } from '$stores/auth-store';

export class AtomicWS {
	hasFailed: boolean = false;
	wsUrl: string;
	manager: CentrifugeTypes | undefined;
	trollboxSub: Subscription | undefined;
	botManagerSub: Subscription | undefined;
	botSummarySub: Subscription | undefined;
	botStateSub: Subscription | undefined;

	constructor(wsUrl: string) {
		this.wsUrl = wsUrl;
	}

	/**
	 * Handles WebSocket connection errors.
	 * @param {Object} ctx - The context object containing connection details.
	 */
	handleConnectionError(ctx: any) {
		console.log('failed to connect/disconnect centrifuge, error:', ctx);
		if (ctx.error.message === 'transport closed' && !this.hasFailed && !navigator.onLine) {
			this.hasFailed = true;
			// updateUIStore({
			// 	wsFailed: true
			// });
		}
	}

	/**
	 * Initializes the WebSocket connection with the user's token.
	 * @remarks
	 * This method should be called after obtaining the user's token.
	 */
	initWs() {
		if (browser) {
			this.manager = new CentrifugeClass(`${this.wsUrl}?token=${getToken()}`);
		}
		// userStore.subscribe((user: any) => {
		// 	if (user.token) {
		// 		this.manager = new CentrifugeClass(`${this.wsUrl}?token=${user.token}`);
		// 	}
		// });
	}

	/**
	 * Connects to the WebSocket server.
	 */
	connectWs() {
		this.manager?.connect();

		this.manager?.on('connecting', (ctx) => {
			updateWebsocketStore({
				connectState: 'connecting'
			});
		});

		this.manager?.on('connected', (ctx) => {
			updateWebsocketStore({
				connectState: 'connected'
			});
			// updateUIStore({
			// 	wsFailed: false
			// });
			if (this.hasFailed) {
				this.hasFailed = false;
			}
		});

		this.manager?.on('error', this.handleConnectionError.bind(this));
	}

	/**
	 * Disconnects from the WebSocket server.
	 */
	disconnectWs() {
		this.manager?.disconnect();
		/**
		 * Event handler for when the WebSocket is disconnected.
		 * Updates the WebSocket connection state in the system store to 'disconnected'.
		 * @param {Object} ctx - The context object containing connection details.
		 */
		this.manager?.on('disconnected', (ctx) => {
			updateWebsocketStore({
				connectState: 'disconnected'
			});
		});

		this.manager?.on('error', this.handleConnectionError.bind(this));
	}

	/**
	 * Initializes a subscription to the trollbox chat room.
	 */
	connectTrollbox() {
		this.trollboxSub = this.manager?.newSubscription('chat/trollbox');
		this.trollboxSub?.subscribe();

		this.trollboxSub?.on('subscribing', (ctx) => {
			updateWebsocketStore({
				trollboxState: 'connecting'
			});
		});

		this.trollboxSub?.on('subscribed', (ctx) => {
			updateWebsocketStore({
				trollboxState: 'connected'
			});
		});

		this.trollboxSub?.on('publication', (ctx) => {
			if (ctx.data.userId) {
				updateWebsocketStore({
					newTrollboxMessage: ctx.data
				})
			}
		});

		this.trollboxSub?.on('join', (ctx) => {
			this.getUsersPressence();
		});

		this.trollboxSub?.on('leave', (ctx) => {
			this.getUsersPressence();
		});

		this.trollboxSub?.on('error', this.handleConnectionError.bind(this));
	}

	/**
	 * Sends a message to the trollbox chat room.
	 * @param {string} message - The message to be sent.
	 */
	sendMessage(message: string) {
		const newMessage = {
			timestamp: getUnixTimestamp(new Date()),
			content: message
		};

		this.trollboxSub?.publish(newMessage);
	}

	/**
	 * Gets the list of users currently in the trollbox chat room.
	 * @remarks
	 * This method should be called after successfully subscribing to the trollbox chat room.
	 * @returns An array of users currently in the trollbox chat room.
	 */
	getUsersPressence() {
		this.manager?.presence('chat/trollbox').then(
			function (resp) {
				// updateTrollboxStore({
				// 	onlineUsers: transformToObjectArray(resp.clients)
				// });
			},
			function (err) {
				console.log('presence error', err);
			}
		);
	}

	// * Initializes a subscription to the bot state.
	connectBotStateByBotID(botID: string) {
		// * Disconnects the bot state if it is already connected to prevent multiple connections.
		if (this.botStateSub) {
			this.disconnectBotState();
		}
		this.botStateSub = this.manager?.newSubscription(`bots/${botID}/state`);
		this.botStateSub?.subscribe();

		this.botStateSub?.on('subscribing', (ctx) => {
			console.log('bot manager subscribing', ctx);
		});

		this.botStateSub?.on('subscribed', (ctx) => {
			console.log('bot manager subscribed', ctx);
		});

		this.botStateSub?.on('publication', (ctx) => {
			updateWebsocketStore({
				botState: ctx.data
			});
		});

		this.botStateSub?.on('error', (ctx) => {
			console.log(ctx.error, 'its calling error now.');
			toast('Failed to load bot market data', 'error');
		});
	}

	//  * Initializes a unsubscription to the bot state.
	disconnectBotState() {
		this.manager?.removeSubscription(this.botStateSub as Subscription);
		this.botStateSub?.unsubscribe();

		this.botStateSub?.on('unsubscribed', (ctx) => {
			console.log('bot manager unsubscribed', ctx);
		});

		this.botStateSub?.on('publication', (ctx) => {
			updateWebsocketStore({
				botState: null
			});
		});

		this.botStateSub?.on('error', (ctx) => {
			console.log(ctx.error);
			toast('Failed disconnect market data', 'error');
		});
	}

	// * Initializes a subscription to the bot summary.
	connectBotSummary() {
		this.botSummarySub = this.manager?.newSubscription('bots/summary');
		this.botSummarySub?.subscribe();

		this.botSummarySub?.on('subscribing', (ctx) => {
			console.log('bot manager subscribing', ctx);
		});

		this.botSummarySub?.on('subscribed', (ctx) => {
			console.log('bot manager subscribed', ctx);
		});

		this.botSummarySub?.on('publication', (ctx) => {
			updateWebsocketStore({
				botSummary: ctx.data
			});
		});

		this.botSummarySub?.on('error', (ctx) => {
			console.log(ctx.error);
			toast('Failed to load bot summary', 'error');
		});

		this.botManagerSub?.on('error', this.handleConnectionError.bind(this));
	}
}

export const atws = new AtomicWS(`${import.meta.env.VITE_ATOMIC_WS_BASEURL}`);
