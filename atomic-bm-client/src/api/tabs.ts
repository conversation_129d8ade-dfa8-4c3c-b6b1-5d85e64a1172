import type {
	Tab,
	BaseTabsResponse,
	BaseTabResponse,
	TabResponse,
	TabsResponse,
	ReorderedTab
} from '$types';

/**
 * Client for interacting with the tabs API.
 * @param user_id The ID of the user to interact with.
 * @returns An object containing functions for fetching, creating, updating, and deleting tabs.
 */
export function tabsClient(user_id: string) {
	/**
	 * Fetches all tabs for a given user.
	 * @returns A promise that resolves to a BaseTabsResponse object containing the fetched tabs or an error.
	 */
	async function fetchTabs(): Promise<BaseTabsResponse> {
		try {
			const req = await fetch(`/api/tabs/${user_id}`);
			if (!req.ok) {
				const err = await req.json();
				throw new Error(err.message);
			}

			const res: TabsResponse = await req.json();
			return {
				tabs: res.tabs,
				last_tab_id: res.last_tab_id,
				err: null
			};
		} catch (err) {
			return {
				tabs: [],
				last_tab_id: '',
				err: err as Error
			};
		}
	}

	/**
	 * Fetches a specific tab for a given user.
	 * @param tab_id The ID of the tab to fetch.
	 * @returns A promise that resolves to a BaseTabResponse object containing the fetched tab or an error.
	 */
	async function fetchTab(tab_id: string): Promise<BaseTabResponse> {
		try {
			const req = await fetch(`/api/tabs/${user_id}/${tab_id}`);
			if (!req.ok) {
				throw new Error(await req.text());
			}

			const res: TabResponse = await req.json();
			return {
				tab: res.tab,
				last_tab_id: res.last_tab_id,
				err: null
			};
		} catch (err) {
			return {
				tab: {
					name: '',
					path: ''
				},
				last_tab_id: '',
				err: err as Error
			};
		}
	}

	/**
	 * Creates a new tab for a given user.
	 * @param tab The tab object to create.
	 * @returns A promise that resolves to null if the tab was created successfully, or an error if the creation failed.
	 */
	async function createTab(tab_id: string, tab: Tab): Promise<null | Error> {
		try {
			const req = await fetch(`/api/tabs/${user_id}?tab_id=${tab_id}`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(tab)
			});
			if (!req.ok) {
				throw new Error(await req.text());
			}

			const res = await req.json();
			return res;
		} catch (err) {
			return err as Error;
		}
	}

	/**
	 * Updates an existing tab for a given user.
	 * @param tab_id The ID of the tab to update.
	 * @param tab The updated tab object.
	 * @returns A promise that resolves to null if the tab was updated successfully, or an error if the update failed.
	 */
	async function updateTab(tab_id: string, tab: Tab): Promise<null | Error> {
		try {
			const req = await fetch(`/api/tabs/${user_id}?tab_id=${tab_id}`, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(tab)
			});
			if (!req.ok) {
				throw new Error(await req.text());
			}

			const res = await req.json();
			return res;
		} catch (err) {
			return err as Error;
		}
	}

	/**
	 * Updates the last tab ID for a given user.
	 * @param last_tab_id The ID of the last tab.
	 * @returns A promise that resolves to null if the update was successful, or an error if the update failed.
	 */
	async function updateLastTabID(last_tab_id: string): Promise<Error | null> {
		try {
			const req = await fetch(`/api/tabs/${user_id}?last_tab_id=${last_tab_id}`, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				}
			});
			if (!req.ok) {
				throw new Error(await req.text());
			}

			const res = await req.json();
			return res;
		} catch (err) {
			return err as Error;
		}
	}

	/**
	 * Deletes a specific tab for a given user.
	 * @param tab_id The ID of the tab to delete.
	 * @returns A promise that resolves to null if the tab was deleted successfully, or an error if the deletion failed.
	 */
	async function deleteTab(tab_id: string): Promise<null | Error> {
		try {
			const req = await fetch(`/api/tabs/${user_id}?tab_id=${tab_id}`, {
				method: 'DELETE'
			});
			if (!req.ok) {
				throw new Error(await req.text());
			}

			const res = await req.json();
			return res;
		} catch (err) {
			return err as Error;
		}
	}

	async function reorderTabs(newOrder: ReorderedTab[]): Promise<null | Error> {
		try {
			const req = await fetch(`/api/tabs/${user_id}/order`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					newOrder
				})
			});
			if (!req.ok) {
				throw new Error(await req.text());
			}

			const res = await req.json();
			return res;
		} catch (err) {
			return err as Error;
		}
	}

	return {
		fetchTabs,
		fetchTab,
		createTab,
		updateTab,
		deleteTab,
		updateLastTabID,
		reorderTabs
	};
}
