import type { BaseAliasResponse, AliasResponse } from '$types';

export function aliasClient(user_id: string) {
	async function fetchAliases(): Promise<BaseAliasResponse> {
		try {
			const req = await fetch(`/api/aliases/${user_id}`);
			if (!req.ok) {
				const err = await req.json();
				throw new Error(err.message);
			}

			const res: AliasResponse = await req.json();
			return {
				aliases: res.aliases,
				err: null
			};
		} catch (err) {
			return {
				aliases: [],
				err: err as Error
			};
		}
	}

	async function createAlias(newAlias: any, user_id: string): Promise<null | Error> {
		try {
			const req = await fetch(`/api/aliases/${user_id}`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(newAlias)
			});
			if (!req.ok) {
				throw new Error(await req.text());
			}

			return null;
		} catch (err) {
			return err as Error;
		}
	}

	async function removeAlias(alias_id: string): Promise<null | Error> {
		try {
			const req = await fetch(`/api/aliases/${user_id}?alias_id=${alias_id}`, {
				method: 'DELETE'
			});
			if (!req.ok) {
				throw new Error(await req.text());
			}

			return null;
		} catch (err) {
			return err as Error;
		}
	}

	return {
		fetchAliases,
		createAlias,
		removeAlias
	};
}
