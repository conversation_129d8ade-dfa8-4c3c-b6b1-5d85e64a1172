<script lang="ts">
	import * as Drawer from '$lib/components/ui/drawer';
	import { cn } from '$lib/utils';
	import { createEventDispatcher } from 'svelte';

	const d = createEventDispatcher();
	interface Props {
		title?: string;
		description?: string;
		titleClass?: string;
		descriptionClass?: string;
		areaClass?: string;
		wrapperClass?: string;
		footerClass?: string;
		contentClass?: string;
		direction?: 'left' | 'right' | 'top' | 'bottom';
		closeOnClickOutside?: boolean;
		closeOnEscape?: boolean;
		open?: boolean;
		area?: import('svelte').Snippet;
		footer?: import('svelte').Snippet;
	}

	let {
		title = '',
		description = '',
		titleClass = '',
		descriptionClass = '',
		areaClass = '',
		wrapperClass = '',
		footerClass = '',
		contentClass = '',
		direction = 'bottom',
		open = false,
		area,
		footer
	}: Props = $props();
</script>

<Drawer.Root {open} {direction} onOpenChange={(e: any) => d('openChange', e)}>
	<Drawer.Content class={cn('', contentClass)}>
		<div class={cn('mx-auto w-full max-w-sm', wrapperClass)}>
			<Drawer.Header>
				<Drawer.Title class={titleClass}>{title}</Drawer.Title>
				<Drawer.Description class={descriptionClass}>{description}</Drawer.Description>
			</Drawer.Header>
			<div class={cn('', areaClass)}>
				{@render area?.()}
			</div>
			{#if footer}
				<Drawer.Footer class={footerClass}>
					{@render footer?.()}
				</Drawer.Footer>
			{/if}
		</div>
	</Drawer.Content>
</Drawer.Root>
