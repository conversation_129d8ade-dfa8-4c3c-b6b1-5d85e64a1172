<!-- @migration-task Error while migrating Svelte code: $$props is used together with named props in a way that cannot be automatically migrated. -->
<script>
	import { cn } from '$lib/utils';
	import Icon from './ui/icon.svelte';

	export let title = '',
		titleClass = '',
		icon = 'line-md:loading-twotone-loop',
		iconClass = '';
</script>

<div class={cn('flex h-[150px] flex-col items-center justify-center gap-2', $$props.class)}>
	{#if title}
		<h1 class={cn('tracking-widest', titleClass)}>{title}</h1>
	{/if}
	<Icon {icon} class={cn('text-3xl', iconClass)} />
</div>
