<script lang="ts">
	import CommandCard from './command-card.svelte';
	import type { Account } from '$client/api.schemas';
	import { formatCommonDate } from '$utils';
	import Button from '../ui/button.svelte';

	interface Props {
		command?: string;
		pathname?: string;
		title?: string;
		accounts_list?: Account[];
	}

	let { command = '', pathname = '', title = '', accounts_list = [] }: Props = $props();
</script>

<CommandCard {command} {pathname} {title} on:openDocs>
	<div class="mt-1 flex flex-col gap-0.5">
		{#each accounts_list as account}
			<div class="bg-dark-7 max-w-[460px] p-2">
				<div class="flex justify-between text-xs">
					<div class="font-light">
						<h2>ID</h2>
						<h2>Exchange ID</h2>
						<h2>Tag</h2>
					</div>
					<div class="flex flex-col items-end justify-end">
						<h2>{account.id}</h2>
						<h2>{account.exchangeId}</h2>
						<h2>{account.tag}</h2>
					</div>
				</div>
				<div class="mt-2 flex items-end justify-between">
					<Button small>View</Button>
					<h2 class="text-[0.6rem]">{formatCommonDate(account.createdAt)}</h2>
				</div>
			</div>
		{:else}
			<div class="w-[460px] bg-dark-7 p-2 text-xs">No results</div>
		{/each}
	</div>
</CommandCard>
