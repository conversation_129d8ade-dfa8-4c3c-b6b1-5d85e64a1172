<script lang="ts">
	import { formatCommonDate } from '$utils';
	import CommandCard from './command-card.svelte';
	import type { Bot } from '$client/api.schemas';
	import Button from '../ui/button.svelte';

	interface Props {
		command?: string;
		pathname?: string;
		title?: string;
		bot_list?: Bot[];
	}

	let { command = '', pathname = '', title = '', bot_list = [] }: Props = $props();
</script>

<CommandCard {command} {pathname} {title} on:openDocs>
	<div class="mt-1 flex flex-col gap-0.5">
		{#each bot_list as bot}
			<div class="bg-dark-7 w-[460px] p-2">
				<div class="flex justify-between text-xs">
					<div class="font-light">
						<h2>Bot ID</h2>
						<h2>Account ID</h2>
						<h2>Symbol</h2>
						<h2>Region</h2>
						<h2>Tag</h2>
					</div>
					<div class="flex flex-col items-end justify-end">
						<h2>{bot.id}</h2>
						<h2>{bot.accountId}</h2>
						<h2>{bot.symbol}</h2>
						<!-- <h2>{bot.region}</h2> -->
						<h2>{bot.tag}</h2>
					</div>
				</div>
				<div class="mt-2 flex items-end justify-between">
					<Button small>View</Button>
					<h2 class="text-[0.6rem]">{formatCommonDate(bot.createdAt)}</h2>
				</div>
			</div>
		{:else}
			<div class="w-[460px] bg-dark-7 p-2 text-xs">No results</div>
		{/each}
	</div>
</CommandCard>
