<script lang="ts">
	import CommandCard from './command-card.svelte';
	// import type { ParamsBotParamGroup } from '$client/api.schemas';
	import Button from '$lib/components/ui/button.svelte';
	import { formatCommonDate } from '$utils';

	interface Props {
		command?: string;
		pathname?: string;
		title?: string;
		bot_groups_list?: any[];
	}

	let { command = '', pathname = '', title = '', bot_groups_list = [] }: Props = $props();
</script>

<CommandCard {command} {pathname} {title} on:openDocs>
	<div class="mt-1 flex flex-col gap-0.5">
		{#each bot_groups_list as groups}
			<div class="bg-dark-7 w-[460px] p-2">
				<div class="flex justify-between text-xs">
					<div class="font-light">
						<h2>Bot ID</h2>
						<h2>Group ID</h2>
						<h2>Created By</h2>
					</div>
					<div class="flex flex-col items-end justify-end">
						<h2>{groups.botId}</h2>
						<h2>{groups.groupId}</h2>
						<h2>{groups.createdByUserID}</h2>
					</div>
				</div>
				<div class="mt-2 flex items-end justify-between">
					<Button small>View</Button>
					<h2 class="text-[0.6rem]">{formatCommonDate(groups.createdAt)}</h2>
				</div>
			</div>
		{:else}
			<div class="w-[460px] bg-dark-7 p-2 text-xs">No results</div>
		{/each}
	</div>
</CommandCard>
