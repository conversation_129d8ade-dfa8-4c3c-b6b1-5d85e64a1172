<script lang="ts">
	import Icon from '$lib/components/ui/icon.svelte';
	import { createEventDispatcher } from 'svelte';

	const d = createEventDispatcher();
	interface Props {
		pathname: string;
		command: string;
		title: string;
		children?: import('svelte').Snippet;
	}

	let { pathname, command, title, children }: Props = $props();
</script>

<div class="bg-dark-6 flex justify-between p-2">
	<div>
		<p class="text-[0.6rem] font-light">
			{pathname}
		</p>
		<p class="mt-2 text-xs font-bold">
			{title} ({command})
		</p>
		<div class="text-sm">
			{@render children?.()}
		</div>
	</div>
	<div>
		<Icon icon="simple-icons:googledocs" size="small" btn on:click={() => d('openDocs', command)} />
	</div>
</div>
