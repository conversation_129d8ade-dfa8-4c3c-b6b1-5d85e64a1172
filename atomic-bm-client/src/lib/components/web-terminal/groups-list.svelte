<script lang="ts">
	import CommandCard from './command-card.svelte';
	import { formatCommonDate } from '$utils';
	import Button from '$lib/components/ui/button.svelte';

	interface Props {
		command?: string;
		pathname?: string;
		title?: string;
		groups_list?: any[];
	}

	let { command = '', pathname = '', title = '', groups_list = [] }: Props = $props();
</script>

<CommandCard {command} {pathname} {title} on:openDocs>
	<div class="mt-1 flex flex-col gap-0.5">
		{#each groups_list as group}
			<div class="bg-dark-7 w-[460px] p-2">
				<div class="flex justify-between text-xs">
					<div class="font-light">
						<h2>ID</h2>
						<h2>Name</h2>
						<h2>Created By</h2>
					</div>
					<div class="flex flex-col items-end justify-end">
						<h2>{group.id}</h2>
						<h2>{group.name}</h2>
						<h2>{group.createdByUserID}</h2>
					</div>
				</div>
				<div class="mt-2 flex items-end justify-between">
					<Button small>View</Button>
					<h2 class="text-[0.6rem]">{formatCommonDate(group.createdAt)}</h2>
				</div>
			</div>
		{:else}
			<div class="w-[460px] bg-dark-7 p-2 text-xs">No results</div>
		{/each}
	</div>
</CommandCard>
