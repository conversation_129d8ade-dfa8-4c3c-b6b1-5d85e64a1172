<script lang="ts">
	//@ts-nocheck

	import CommandCard from './command-card.svelte';
	import type { Param } from '$client/api.schemas';
	import { formatCommonDate } from '$utils';
	import Button from '$lib/components/ui/button.svelte';

	interface Props {
		command?: string;
		pathname?: string;
		title?: string;
		params_list?: Param[];
	}

	let { command = '', pathname = '', title = '', params_list = [] }: Props = $props();
</script>

<CommandCard {command} {pathname} {title} on:openDocs>
	<div class="mt-1 flex flex-col gap-0.5">
		{#each params_list as params}
			<div class="bg-dark-7 w-[460px] p-2">
				<div class="flex justify-between text-xs">
					<div class="font-light">
						<h2>Key</h2>
						<h2>Value</h2>
					</div>
					<div class="flex flex-col items-end justify-end">
						<h2>{params.Key}</h2>
						<h2>{params.Value}</h2>
					</div>
				</div>
				<div class="mt-2 flex items-end justify-between">
					<Button small>View</Button>
					<h2 class="text-[0.6rem]">{formatCommonDate(params.CreatedAt)}</h2>
				</div>
			</div>
		{:else}
			<div class="w-[460px] bg-dark-7 p-2 text-xs">No results</div>
		{/each}
	</div>
</CommandCard>
