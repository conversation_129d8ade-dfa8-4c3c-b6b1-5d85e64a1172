<script lang="ts">
	import { run, preventDefault } from 'svelte/legacy';

	import Icon from '$lib/components/ui/icon.svelte';
	import ErrorCard from './error-card.svelte';
	import EmptyCommands from './empty-commands.svelte';
	import TerminalInput from './terminal-input.svelte';
	import * as Card from './cards';
	import { clickOutside, toast } from '$utils';
	import { fly } from 'svelte/transition';
	import { page } from '$app/stores';
	import {
		scrollToTheBottom,
		getStorage,
		saveStorage,
		deepFilter,
		insertValueToCommandCommon,
		focusToInput
	} from '$utils';
	import { commands } from './commands';
	import { onMount } from 'svelte';
	import { selectCommand, commandNotFound, clearCommandsHistory } from './terminal-actions';
	import type { CommandViewData } from '$types';
	import TerminalDocs from './terminal-docs.svelte';

	let terminalMessages: CommandViewData[] = $state([]);
	let insertingCommandValue = $state('');
	let loading = $state(false);
	let typing = $state(false);
	let selectedCommand = $state(false);
	let isInsertingCommandValue = $state(false);
	let showDocs = $state(false);
	let loadingGeneratedCommand = $state(false);
	let hoveredDocCommand = $state('');
	let newCommand = $state('');
	let splittedCommandsIndex = $state(0);

	let pathname = $derived($page.url.pathname);
	let currCommandIndex = $state(0);

	let terminalCommands = $derived(deepFilter(commands, newCommand));
	let splittedCommands = $derived(newCommand?.split(' ') as any[]);

	run(() => {
		if ($page.url.searchParams.get('openDocs') === 'true') {
			showDocs = true;
		}
	});

	run(() => {
		if ($page.url.searchParams.get('focusTerminal') === 'true') {
			focusToInput('terminal-input');
		}
	});

	// dinamically load command components based on title
	let commandCards = $derived((option: string) => {
		switch (option) {
			case 'Bots list':
				return Card.Bot;
			case 'Accounts list':
				return Card.Account;
			case 'Bot groups':
				return Card.BotGroup;
			case 'Groups list':
				return Card.Group;
			case 'Params list':
				return Card.Param;
			case 'Create bot':
				return Card.Command;
			case 'Single bot':
				return Card.SingleBot;
			case 'Single account':
				return Card.SingleAccount;
			default:
				return Card.Command;
		}
	});

	async function generateAIResponse() {
		try {
			loadingGeneratedCommand = true;
			const res = await fetch('/api/openai', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ question: newCommand })
			});
			const data = await res.json();
			newCommand = data.message.content;
			selectedCommand = true;
		} catch (error) {
			console.error(error);
		} finally {
			loadingGeneratedCommand = false;
		}
	}

	async function sendCommand() {
		loading = true;

		if (selectedCommand) {
			newCommand = splittedCommands.join(' ');
			selectedCommand = false;
		}

		if (newCommand.includes('clear')) {
			terminalMessages = clearCommandsHistory();
			newCommand = '';
			loading = false;
			return;
		}

		if (newCommand.includes('help')) {
			newCommand = '';
			loading = false;
			showDocs = true;
			return;
		}

		let newTerminalMessage: CommandViewData | null;
		newTerminalMessage = await selectCommand(newCommand);
		if (newTerminalMessage) {
			terminalMessages = [...terminalMessages, newTerminalMessage];
		} else {
			terminalMessages = commandNotFound(terminalMessages, newCommand);
		}

		saveStorage('terminalMessages', terminalMessages);
		setTimeout(() => scrollToTheBottom('terminal'), 10);

		newCommand = '';
		loading = false;
		isInsertingCommandValue = false;
		splittedCommandsIndex = 0;
	}

	async function handleCommandExectuion() {
		if (!newCommand.includes('#')) {
			await sendCommand();
			return;
		}

		await generateAIResponse();
	}

	function handleArrowKeyPress(e: KeyboardEvent) {
		if (e.key === 'ArrowUp' && !selectedCommand) {
			if (currCommandIndex > 0) {
				currCommandIndex -= 1;
			}
		} else if (e.key === 'ArrowDown' && terminalCommands) {
			if (currCommandIndex < terminalCommands?.length - 1) {
				currCommandIndex += 1;
			}
		} else if (
			terminalCommands &&
			e.key === 'ArrowRight' &&
			newCommand &&
			terminalCommands.length > 0 &&
			!selectedCommand
		) {
			newCommand = terminalCommands.find((c, i) => i === currCommandIndex)?.command;
			selectedCommand = true;
		} else if (e.key === 'Backspace') {
			selectedCommand = false;
			splittedCommandsIndex = 0;
			isInsertingCommandValue = false;
		} else if (e.key === 'ArrowLeft' && selectedCommand) {
			if (splittedCommandsIndex > 0) {
				splittedCommandsIndex -= 1;
			}
		} else if (e.key === 'ArrowRight' && selectedCommand && splittedCommands) {
			if (splittedCommandsIndex < splittedCommands.length - 1) {
				splittedCommandsIndex += 1;
			}
		} else if (e.key === 'ArrowUp' && selectedCommand) {
			isInsertingCommandValue = true;
			setTimeout(() => {
				focusToInput('inserting-input');
			}, 50);
		} else if (e.key === 'Delete' && selectedCommand && splittedCommands) {
			splittedCommands = splittedCommands.filter((_, i) => i !== splittedCommandsIndex);
			newCommand = splittedCommands.join(' ');
		}

		scrollToId(currCommandIndex.toString());
	}

	function handleInsertingCommandKeyPress(e: CustomEvent) {
		if (e.detail.key === 'Escape') {
			focusToInput('terminal-input');
		}
	}

	function parseCommandsToTerminalInput() {
		if (insertingCommandValue.includes(' ')) {
			toast('Value cannot contain spaces.', 'error');
			return;
		}
		const hoveredValue = splittedCommands[splittedCommandsIndex];
		const newCommandValue = insertValueToCommandCommon(hoveredValue, insertingCommandValue);
		splittedCommands[splittedCommandsIndex] = newCommandValue;
		isInsertingCommandValue = false;
		insertingCommandValue = '';
		splittedCommandsIndex = splittedCommandsIndex + 1;

		focusToInput('terminal-input');
	}

	function scrollToId(id: string) {
		const el = document.getElementById(id);
		if (el) {
			el.scrollIntoView({ behavior: 'smooth' });
		}
	}

	onMount(() => {
		scrollToTheBottom('terminal');
		terminalMessages = getStorage('terminalMessages') || [];
	});
</script>

<div class="absolute -bottom-[4px] w-full">
	<!-- TODO: fix custom event handler -->
	<!-- <form
		class="mx-auto w-[97.5%] space-y-0.5"
		use:clickOutside
		onclick_outside={() => {
			if (typing) typing = false;
		}}
		onsubmit={preventDefault(handleCommandExectuion)}
	> -->
	<form class="mx-auto w-[97.5%] space-y-0.5" onsubmit={preventDefault(handleCommandExectuion)}>
		<div class="mx-auto flex h-[30px] items-center justify-between bg-black px-2">
			<p class="text-xs font-semibold text-[#7785cc]">{pathname}</p>
			<button type="button" class="flex items-center" onclick={() => (showDocs = !showDocs)}>
				<Icon icon="simple-icons:googledocs" btn class="text-xs" />
			</button>
		</div>
		<div
			class="h-transition mx-auto overflow-hidden bg-black"
			class:h-0={!typing}
			class:h-[200px]={typing}
			in:fly={{ y: 10, duration: 100 }}
		>
			<div class="flex h-full flex-col gap-1 overflow-y-auto" id="terminal">
				{#each terminalMessages as item}
					{#if item.error}
						<ErrorCard
							title={item.title}
							command={item.command}
							{pathname}
							error={item.error}
							on:openDocs={(e) => {
								showDocs = true;
								hoveredDocCommand = e.detail;
							}}
						/>
					{:else}
						{@const SvelteComponent = commandCards(item.title)}
						<SvelteComponent
							command={item.command}
							title={item.title}
							{pathname}
							bot_list={item.result}
							accounts_list={item.result}
							bot_groups_list={item.result}
							groups_list={item.result}
							params_list={item.result}
							bot_info={item.result}
							account_info={item.result}
							on:openDocs={(e) => {
								showDocs = true;
								hoveredDocCommand = e.detail;
							}}
						>
							{item.result}
						</SvelteComponent>
					{/if}
				{:else}
					<EmptyCommands />
				{/each}
				{#if loading}
					<div class="flex items-center px-2 pb-1">
						<p class="text-[0.6rem]">Loading</p>
						<Icon icon="eos-icons:three-dots-loading" />
					</div>
				{/if}
			</div>
		</div>
		<TerminalInput
			id="terminal-input"
			{typing}
			{selectedCommand}
			{terminalCommands}
			{currCommandIndex}
			{isInsertingCommandValue}
			{splittedCommands}
			{splittedCommandsIndex}
			generatingCommand={newCommand.includes('#')}
			loading={loadingGeneratedCommand}
			on:sendValue={parseCommandsToTerminalInput}
			on:insertionKeyUp={handleInsertingCommandKeyPress}
			on:blur={() => {
				isInsertingCommandValue = false;
				insertingCommandValue = '';
			}}
			on:focus={() => {
				typing = true;
				scrollToTheBottom('terminal');
			}}
			on:keyup={(e: any) => handleArrowKeyPress(e)}
			bind:value={newCommand}
			bind:insertValue={insertingCommandValue}
		/>
	</form>
</div>

<TerminalDocs
	open={showDocs}
	on:openChange={(e) => {
		showDocs = e.detail;
		if (!showDocs) hoveredDocCommand = '';
	}}
	{hoveredDocCommand}
/>
