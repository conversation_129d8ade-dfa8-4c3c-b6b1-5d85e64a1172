import { getStorage, removeStorage, terminalMessagesToCommon } from '$utils';
import type { CommandViewData } from '$types';
import { getBots, postBots, deleteBotsBotId, patchBotsBotId, getBotsBotId } from '$client/bot';
import {
	getAccounts,
	postAccounts,
	deleteAccountsAccountId,
	getAccountsAccountId
} from '$client/account';
import { getGroups, postGroups, deleteGroupsGroupId } from '$client/param-groups';
import { getParams, postParamsBulkDelete, postParams } from '$client/params';
import { clearApp } from '$utils';

export async function selectCommand(newCommand: string): Promise<CommandViewData | null> {
	if (newCommand.includes('bot list')) {
		return await loadBotsMessage(newCommand);
	}

	if (newCommand.includes('bot single')) {
		return await loadBotByIdMessage(newCommand);
	}

	if (newCommand.includes('account list')) {
		return await loadAccountsMessage(newCommand);
	}

	if (newCommand.includes('account single')) {
		return await loadAccountByIdMessage(newCommand);
	}

	if (newCommand.includes('group list')) {
		return await loadGroupsMessage(newCommand);
	}

	if (newCommand.includes('params new')) {
		return await loadCreateParamMessage(newCommand);
	}

	if (newCommand.includes('params list')) {
		return await loadParamsMessage(newCommand);
	}

	if (newCommand.includes('params remove')) {
		return await loadDeleteParamsMessage(newCommand);
	}

	if (newCommand.includes('bot new')) {
		return await loadCreateBotMessage(newCommand);
	}

	if (newCommand.includes('bot update')) {
		return await loadUpdateBotMessage(newCommand);
	}

	if (newCommand.includes('bot remove')) {
		return await loadDeleteBotMessage(newCommand);
	}

	if (newCommand.includes('account new')) {
		return await loadCreateAccountMessage(newCommand);
	}

	if (newCommand.includes('account remove')) {
		return await loadDeleteAccountMessage(newCommand);
	}

	if (newCommand.includes('group new')) {
		return await loadCreateGroupsMessage(newCommand);
	}

	if (newCommand.includes('group remove')) {
		return await loadDeleteGroupMessage(newCommand);
	}

	if (newCommand.includes('logout')) {
		clearApp(true);
	}

	return null;
}

export async function loadBotsMessage(command: string): Promise<CommandViewData> {
	try {
		const res = await getBots(undefined, {
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		});

		return returnSuccessCommand('Bots list', res.data.data, command);
	} catch (error: any) {
		return returnFailedCommand(
			'Bots list',
			'error',
			error?.message || 'Something went wrong',
			command
		);
	}
}

async function loadBotByIdMessage(command: string): Promise<CommandViewData> {
	const { botId } = terminalMessagesToCommon(command);
	if (!botId) {
		return returnFailedCommand('Single bot', 'warning', 'Missing: Bot ID', command);
	}

	try {
		const res = await getBotsBotId(botId, {
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		});

		return returnSuccessCommand('Single bot', res.data.data, command);
	} catch (error: any) {
		return returnFailedCommand(
			'Single bot',
			'error',
			error?.message || 'Something went wrong',
			command
		);
	}
}

async function loadCreateBotMessage(command: string): Promise<CommandViewData> {
	try {
		const { accountId, region, symbol, tag } = terminalMessagesToCommon(command);
		if (!accountId || !region || !symbol || !tag) {
			const missingField = !accountId
				? 'Account ID'
				: !region
					? 'Region'
					: !symbol
						? 'Symbol'
						: 'Tag';

			return returnFailedCommand('Create bot', 'warning', `Missing: ${missingField}`, command);
		}

		await postBots(
			{
				accountId,
				region,
				symbol,
				tag
			},
			{
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			}
		);

		return returnSuccessCommand('Create bot', 'Bot created successfully', command);
	} catch (error: any) {
		return returnFailedCommand(
			'Create bot',
			'error',
			error?.message || 'Something went wrong',
			command
		);
	}
}

async function loadUpdateBotMessage(command: string): Promise<CommandViewData> {
	try {
		const { botId, accountId, region, symbol, tag } = terminalMessagesToCommon(command);
		if (!botId || !accountId || !region || !symbol || !tag) {
			const missingField = !botId
				? 'Bot ID'
				: !accountId
					? 'Account ID'
					: !region
						? 'Region'
						: !symbol
							? 'Symbol'
							: 'Tag';
			return returnFailedCommand('Update bot', 'warning', `Missing: ${missingField}`, command);
		}

		await patchBotsBotId(
			botId,
			{
				accountId,
				region,
				symbol,
				tag
			},
			{
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			}
		);

		return returnSuccessCommand('Update bot', 'Bot updated.', command);
	} catch (error: any) {
		return returnFailedCommand(
			'Update bot',
			'error',
			error?.message || 'Failed to update bot.',
			command
		);
	}
}

async function loadDeleteBotMessage(command: string): Promise<CommandViewData> {
	try {
		const { botId } = terminalMessagesToCommon(command);
		if (!botId) {
			return returnFailedCommand('Bot removal', 'warning', 'Missing: Bot ID', command);
		}

		await deleteBotsBotId(botId, {
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		});

		return returnSuccessCommand('Bot removal', 'Bot deleted.', command);
	} catch (error: any) {
		return returnFailedCommand(
			'Bot removal',
			'error',
			error?.message || 'Failed to delete bot.',
			command
		);
	}
}

async function loadAccountsMessage(command: string): Promise<CommandViewData> {
	try {
		const res = await getAccounts(undefined, {
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		});

		return returnSuccessCommand('Accounts list', res.data.data, command);
	} catch (error: any) {
		return returnFailedCommand(
			'Accounts list',
			'error',
			error?.message || 'Something went wrong',
			command
		);
	}
}

async function loadAccountByIdMessage(command: string): Promise<CommandViewData> {
	try {
		const { accountId } = terminalMessagesToCommon(command);
		if (!accountId) {
			return returnFailedCommand('Single account', 'warning', 'Missing: Account ID', command);
		}

		const res = await getAccountsAccountId(accountId, {
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		});

		return returnSuccessCommand('Single account', res.data.data, command);
	} catch (error: any) {
		return returnFailedCommand(
			'Single account',
			'error',
			error?.message || 'Something went wrong',
			command
		);
	}
}

async function loadCreateAccountMessage(command: string): Promise<CommandViewData> {
	try {
		const { exchangeId, tag } = terminalMessagesToCommon(command);
		if (!exchangeId || !tag) {
			const missingField = !exchangeId ? 'Exchange ID' : 'Tag';
			return returnFailedCommand(
				'Account creation',
				'warning',
				`Missing: ${missingField}`,
				command
			);
		}

		await postAccounts(
			{
				exchangeId,
				tag
			},
			{
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			}
		);

		return returnSuccessCommand('Account creation', 'Account created successfully', command);
	} catch (error: any) {
		return returnFailedCommand(
			'Account creation',
			'error',
			error?.message || 'Failed to create account.',
			command
		);
	}
}

async function loadDeleteAccountMessage(command: string): Promise<CommandViewData> {
	try {
		const { accountId } = terminalMessagesToCommon(command);
		if (!accountId) {
			return returnFailedCommand('Account removal', 'warning', 'Missing: Account ID', command);
		}

		await deleteAccountsAccountId(accountId, {
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		});

		return returnSuccessCommand('Account removal', 'Account deleted.', command);
	} catch (error: any) {
		return returnFailedCommand(
			'Account removal',
			'error',
			error?.message || 'Failed to delete account.',
			command
		);
	}
}

async function loadGroupsMessage(command: string): Promise<CommandViewData> {
	try {
		const res = await getGroups(undefined, {
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		});

		return returnSuccessCommand('Groups list', res.data.data, command);
	} catch (error: any) {
		return returnFailedCommand(
			'Groups list',
			'error',
			error?.message || 'Something went wrong',
			command
		);
	}
}

async function loadCreateGroupsMessage(command: string): Promise<CommandViewData> {
	try {
		const { name } = terminalMessagesToCommon(command);
		if (!name) {
			return returnFailedCommand('Group creation', 'warning', 'Missing: Group name', command);
		}

		await postGroups(
			{
				name
			},
			{
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			}
		);

		return returnSuccessCommand('Group creation', 'Group created successfully', command);
	} catch (error: any) {
		return returnFailedCommand(
			'Group creation',
			'error',
			error?.message || 'Failed to create group.',
			command
		);
	}
}

async function loadParamsMessage(command: string): Promise<CommandViewData> {
	const { scope: paramScope, scopeId } = terminalMessagesToCommon(command);
	if (!paramScope || !scopeId) {
		const missingField = !paramScope ? 'Scope' : 'Scope ID';
		return returnFailedCommand('Param list', 'warning', `Missing: ${missingField}`, command);
	}

	try {
		const res = await getParams(
			{
				scope: [paramScope],
				scopeId: scopeId
			},
			{
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			}
		);

		return returnSuccessCommand('Params list', res.data.data, command);
	} catch (error: any) {
		return returnFailedCommand(
			'Param list',
			'error',
			error?.message || 'Something went wrong',
			command
		);
	}
}

async function loadDeleteGroupMessage(command: string): Promise<CommandViewData> {
	try {
		const { groupId } = terminalMessagesToCommon(command);
		if (!groupId) {
			return returnFailedCommand('Group removal', 'warning', 'Missing: Group ID', command);
		}

		await deleteGroupsGroupId(groupId, {
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		});

		return returnSuccessCommand('Group removal', 'Group deleted.', command);
	} catch (error: any) {
		return returnFailedCommand(
			'Group removal',
			'error',
			error?.message || 'Failed to delete group.',
			command
		);
	}
}

async function loadCreateParamMessage(command: string): Promise<CommandViewData> {
	const { scope: paramScope, scopeId, key, value } = terminalMessagesToCommon(command);
	if (!paramScope || !scopeId || !key || !value) {
		const missingField = !paramScope ? 'Scope' : !scopeId ? 'Scope ID' : !key ? 'Key' : 'Value';
		return returnFailedCommand('Param removal', 'error', `Missing: ${missingField}`, command);
	}

	try {
		await postParams(
			{
				scope: paramScope,
				scopeId: scopeId,
				params: [
					{
						key,
						value
					}
				]
			},
			{
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			}
		);

		return returnSuccessCommand('Create param', 'Param created.', command);
	} catch (error: any) {
		return returnFailedCommand(
			'Create param',
			'error',
			error?.message || 'Failed to create param',
			command
		);
	}
}

async function loadDeleteParamsMessage(command: string): Promise<CommandViewData> {
	const { scope: paramScope, scopeId, key } = terminalMessagesToCommon(command);
	if (!paramScope || !scopeId || !key) {
		const missingField = !paramScope ? 'Scope' : !scopeId ? 'Scope ID' : 'Keys';
		return returnFailedCommand('Param removal', 'error', `Missing: ${missingField}`, command);
	}

	try {
		await postParamsBulkDelete(
			{
				scope: paramScope,
				scopeId: scopeId,
				keys: [key]
			},
			{
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			}
		);

		return returnSuccessCommand('Param removal', 'Params deleted.', command);
	} catch (error: any) {
		return returnFailedCommand(
			'Param removal',
			'error',
			error?.message || 'Failed to delete params.',
			command
		);
	}
}

export function commandNotFound(
	terminalMessages: CommandViewData[],
	newCommand: string
): CommandViewData[] {
	return [
		...terminalMessages,
		{
			command: newCommand,
			title: 'Command not found',
			result: null,
			error: {
				type: 'warning',
				message: 'Command not found'
			}
		}
	];
}

function returnFailedCommand(
	title: string,
	type: 'error' | 'warning',
	message: string,
	command: string
): CommandViewData {
	return {
		command,
		title,
		result: null,
		error: {
			type,
			message
		}
	};
}

function returnSuccessCommand(title: string, result: any, command: string): CommandViewData {
	return {
		command,
		title,
		result,
		error: null
	};
}

export function clearCommandsHistory(): CommandViewData[] {
	removeStorage('terminalMessages');
	return [];
}
