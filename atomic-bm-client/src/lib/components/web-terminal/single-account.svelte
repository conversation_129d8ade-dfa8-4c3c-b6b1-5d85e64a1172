<script lang="ts">
	import CommandCard from './command-card.svelte';
	import type { Account } from '$client/api.schemas';
	import Button from '$lib/components/ui/button.svelte';
	import { formatCommonDate } from '$utils';

	interface Props {
		command?: string;
		pathname?: string;
		title?: string;
		account_info?: Account | null;
	}

	let { command = '', pathname = '', title = '', account_info = null }: Props = $props();
</script>

<CommandCard {command} {pathname} {title} on:openDocs>
	<div class="mt-1 flex flex-col gap-0.5">
		{#if account_info}
			<div class="bg-dark-7 w-[460px] p-2">
				<div class="flex justify-between text-xs">
					<div class="font-light">
						<h2>ID</h2>
						<h2>Exchange ID</h2>
						<h2>Tag</h2>
					</div>
					<div class="flex flex-col items-end justify-end">
						<h2>{account_info.id}</h2>
						<h2>{account_info.exchangeId}</h2>
						<h2>{account_info.tag}</h2>
					</div>
				</div>
				<div class="mt-2 flex items-end justify-between">
					<Button small>View</Button>
					<h2 class="text-[0.6rem]">{formatCommonDate(account_info.createdAt)}</h2>
				</div>
			</div>
		{:else}
			<div class="bg-dark-7 w-[460px] p-2 text-xs">No results</div>
		{/if}
	</div>
</CommandCard>
