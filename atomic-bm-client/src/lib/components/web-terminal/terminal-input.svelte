<script lang="ts">
	import { createBubbler } from 'svelte/legacy';

	const bubble = createBubbler();
	import Icon from '$lib/components/ui/icon.svelte';
	import CommandSuggestion from './command-suggestion.svelte';
	import CommandValueInsertion from './command-value-insertion.svelte';
	import { createEventDispatcher } from 'svelte';

	const d = createEventDispatcher();

	interface Props {
		value?: string;
		insertValue?: string;
		id?: string;
		terminalCommands?: any[];
		currCommandIndex?: number;
		splittedCommands?: any[];
		splittedCommandsIndex?: number;
		typing?: boolean;
		selectedCommand?: boolean;
		generatingCommand?: boolean;
		loading?: boolean;
		isInsertingCommandValue?: boolean;
	}

	let {
		value = $bindable(''),
		insertValue = $bindable(''),
		id = '',
		terminalCommands = [],
		currCommandIndex = 0,
		splittedCommands = [],
		splittedCommandsIndex = 0,
		typing = false,
		selectedCommand = false,
		generatingCommand = false,
		loading = false,
		isInsertingCommandValue = false
	}: Props = $props();
</script>

<div
	class="relative mx-auto flex items-center gap-2 overflow-visible bg-black px-2 py-1"
	class:!bg-gray-800={generatingCommand}
>
	{#if value && typing && !selectedCommand && !generatingCommand}
		<CommandSuggestion {terminalCommands} {currCommandIndex} />
	{/if}
	{#if isInsertingCommandValue && !generatingCommand}
		<CommandValueInsertion
			id="inserting-input"
			on:sendValue={() => d('sendValue')}
			on:keyup={(e) => d('insertionKeyUp', e)}
			on:blur
			bind:insertValue
		/>
	{/if}
	<Icon icon="material-symbols:terminal-sharp" class="text-lg" />
	<input
		{id}
		autocomplete="off"
		required
		placeholder="Type '#' to ask AI to generate a command"
		type="text"
		class="w-full bg-inherit text-sm outline-none placeholder:text-xs"
		class:opacity-0={selectedCommand}
		class:font-bold={generatingCommand}
		class:opacity-40={loading}
		class:pointer-events-none={loading}
		onfocus={bubble('focus')}
		onkeyup={bubble('keyup')}
		bind:value
	/>
	<div class="absolute ml-[26px] flex gap-1 opacity-0" class:!opacity-100={selectedCommand}>
		{#each splittedCommands as command, i (i)}
			<p class="text-sm" class:bg-gray-700={splittedCommandsIndex === i}>{command}</p>
		{/each}
	</div>
</div>
