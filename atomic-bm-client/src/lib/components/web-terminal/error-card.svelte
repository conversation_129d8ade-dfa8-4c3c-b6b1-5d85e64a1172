<script lang="ts">
	import Icon from '$lib/components/ui/icon.svelte';
	import { createEventDispatcher } from 'svelte';
	import type { CommandError } from '$types';

	const d = createEventDispatcher();
	interface Props {
		error: CommandError;
		pathname: string;
		command: string;
		title: string;
	}

	let { error, pathname, command, title }: Props = $props();
</script>

<div
	class:bg-red-700={error.type === 'error'}
	class:bg-yellow-700={error.type === 'warning'}
	class="flex justify-between p-2"
>
	<div>
		<p class="text-[0.6rem] font-light">
			{pathname}
		</p>
		<p class="mt-2 text-xs font-bold">
			{title} ({command})
		</p>
		<p class="text-xs">
			{error.message}
		</p>
	</div>
	<div>
		<Icon icon="simple-icons:googledocs" size="small" btn on:click={() => d('openDocs', command)} />
	</div>
</div>
