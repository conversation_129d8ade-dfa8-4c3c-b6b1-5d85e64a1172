export const commands = [
	{
		name: 'List detailed param data',
		description: 'List parameters with filters',
		command: 'params list <scope> <scopeId> <orderBy> <orderDirection> <limit> <offset>'
	},
	{
		name: 'List param data',
		description: 'List parameters',
		command: 'params list <scope> <scopeId>'
	},
	{
		name: 'New param',
		description: 'Add parameters to the given scope and scopeId',
		command: 'params new <scope> <scopeId> <key> <value>'
	},
	{
		name: 'Remove a param',
		description: 'Add parameters to the given scope and scopeId',
		command: 'params new <scope> <scopeId> <key>'
	},
	{
		name: 'List detailed bot data',
		description: 'List all bots with filters',
		command:
			'bot list <accountId> <symbol> <region> <status> <isDeleted=false> <orderBy=created_at> <orderDirection=DESC> <limit=10> <offset>'
	},
	{
		name: 'List bot data',
		description: 'List all bots',
		command: 'bot list'
	},
	{
		name: 'Get single bot',
		description: 'Get a single bot by providing botId',
		command: 'bot single <botId>'
	},
	{
		name: 'Remove bot',
		description: 'Remove bot by botId',
		command: 'bot remove <botId>'
	},
	{
		name: 'Update bot',
		description: 'Update bot by botId',
		command: 'bot update <botId> <accountId> <symbol> <region> <tag>'
	},
	{
		name: 'New bot',
		description: 'Create new bot',
		command: 'bot new <accountId> <symbol> <region> <tag>'
	},
	{
		name: 'List account data',
		description: 'List all available accounts',
		command: 'account list'
	},
	{
		name: 'Get single account',
		description: 'Get a single account',
		command: 'account single <accountId>'
	},
	{
		name: 'List detailed account data',
		description: 'List available accounts with filters',
		command:
			'account list <exchangeId> <tag> <isDeleted> <orderBy> <orderDirection> <limit> <offset>'
	},
	{
		name: 'New account',
		description: 'Create new account',
		command: 'account new <exchangeId> <tag>'
	},
	{
		name: 'Remove account',
		description: 'Remove account by accountId',
		command: 'account remove <accountId>'
	},
	{
		name: 'List group data',
		description: 'Lists all available groups',
		command: 'group list'
	},
	{
		name: 'New group',
		description: 'Creates a new group name',
		command: 'group new'
	},
	{
		name: 'Remove group',
		description: 'Remove group by groupId',
		command: 'group remove'
	},
	{
		name: 'Logout',
		description: 'Logout from current tab',
		command: 'logout'
	},
	{
		name: 'Clear commands',
		description: 'Clear terminal',
		command: 'clear'
	},
	{
		name: 'Help',
		description: 'Display command docs',
		command: 'help'
	}
];
