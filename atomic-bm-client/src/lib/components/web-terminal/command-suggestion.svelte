<script lang="ts">
	import { flip } from 'svelte/animate';

	interface Props {
		terminalCommands?: any[];
		currCommandIndex?: number;
	}

	let { terminalCommands = [], currCommandIndex = 0 }: Props = $props();
</script>

<div
	class="absolute -top-[105px] flex h-[100px] w-[420px] flex-col overflow-y-auto border-gray-700 bg-gray-900"
>
	<div class="flex flex-1 flex-col gap-1.5">
		{#each terminalCommands as command, i (i)}
			<div
				class="flex justify-between gap-2 p-1"
				class:bg-gray-700={currCommandIndex === i}
				id={i.toString()}
				animate:flip={{
					duration: 200
				}}
			>
				<h2 class="w-full flex-1 truncate text-xs font-bold">
					{command.command}
				</h2>
				<h2 class="truncate text-xs font-light">
					{command.description}
				</h2>
			</div>
		{:else}
			<div class="flex items-center justify-center text-xs size-full">
				<h2>Command not found.</h2>
			</div>
		{/each}
	</div>
</div>
