<script lang="ts">
	import { preventDefault } from 'svelte/legacy';

	import Input from '$lib/components/ui/input.svelte';
	import { createEventDispatcher } from 'svelte';

	const d = createEventDispatcher();

	interface Props {
		insertValue?: string;
		id?: string;
	}

	let { insertValue = $bindable(''), id = '' }: Props = $props();
</script>

<form
	class="absolute -top-[50px] flex w-[420px] flex-col overflow-y-auto border-gray-700 bg-gray-900"
	onsubmit={preventDefault(() => d('sendValue'))}
>
	<Input required {id} placeholder="Inser value here." on:keyup on:blur bind:value={insertValue} />
</form>
