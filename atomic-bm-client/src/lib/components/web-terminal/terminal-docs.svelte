<script lang="ts">
	import { run } from 'svelte/legacy';

	import { createEventDispatcher } from 'svelte';
	import { commands } from './commands';
	import { copyToClipboard } from '$utils';
	import { browser } from '$app/environment';
	import BaseDrawer from '../base-drawer.svelte';
	import Button from '../ui/button.svelte';

	const d = createEventDispatcher();
	interface Props {
		open?: boolean;
		hoveredDocCommand?: string;
	}

	let { open = false, hoveredDocCommand = '' }: Props = $props();
	let hoveredDocCommandIndex = $state(-1);

	function generateRegex(command: any) {
		const placeholderPattern = /<[^>]+>/g;
		const regexPattern = command.replace(placeholderPattern, '[^\\s]+');
		return new RegExp(`^${regexPattern}$`);
	}

	const commandPatterns = commands.map((cmd) => ({
		...cmd,
		regex: generateRegex(cmd.command)
	}));

	function findMatchingCommand(input: string) {
		let index = -1;
		for (const [i, cmd] of commandPatterns.entries()) {
			if (cmd.regex.test(input)) {
				return i;
			}
		}
		return null;
	}

	run(() => {
		setTimeout(() => {
			if (hoveredDocCommand) {
				hoveredDocCommandIndex = findMatchingCommand(hoveredDocCommand) || 0;
				if (hoveredDocCommandIndex && browser && !!window) {
					const el = document.getElementById(hoveredDocCommandIndex.toString());
					if (el) {
						el.scrollIntoView({ behavior: 'smooth' });
					}
				}
			}
		}, 50);
	});

	run(() => {
		if (open === false) {
			hoveredDocCommandIndex = -1;
		}
	});
</script>

<BaseDrawer
	{open}
	direction="right"
	contentClass="max-w-[420px]"
	wrapperClass="h-full flex flex-col"
	areaClass="flex-1 h-full"
	on:openChange={(e: any) => d('openChange', e.detail)}
	footerClass="w-full"
	title="Terminal Documentation"
	description="Scroll through to learn more about the terminal"
>
	{#snippet area()}
		<div class="h-[99%] overflow-y-auto px-2 pb-16">
			<h1 class="font-bold">Commands list</h1>
			<div class="mt-2 flex flex-col gap-2">
				{#each commands as c, i (i)}
					<div
						class="bg-dark-6 p-2"
						id={i.toString()}
						class:border-2={hoveredDocCommandIndex === i}
					>
						<h1 class="text-sm font-semibold">{c.name}</h1>
						<h2 class="text-xs font-medium">{c.description}</h2>
						<h2 class="code-font mt-4 text-xs font-semibold">{c.command}</h2>
						<Button
							small
							full
							icon="line-md:clipboard-twotone"
							class="mt-3"
							on:click={() => copyToClipboard(c.command, 'Copied command to clipboard')}
							>Copy</Button
						>
					</div>
				{/each}
			</div>
		</div>
	{/snippet}
</BaseDrawer>

<style>
	@import url('https://fonts.googleapis.com/css2?family=Source+Code+Pro:ital,wght@0,200..900;1,200..900&display=swap');

	.code-font {
		font-family: 'Source Code Pro', monospace;
	}
</style>
