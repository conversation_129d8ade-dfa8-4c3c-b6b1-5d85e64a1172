<script lang="ts">
	import Button from './ui/button.svelte';
	import Icon from './ui/icon.svelte';
	import { createEventDispatcher } from 'svelte';

	const dispatch = createEventDispatcher();
	interface Props {
		currentPage?: number;
		pageLength?: number;
		filters?: any;
	}

	let { currentPage = 1, pageLength = 0, filters = {
			limit: <number | undefined>10,
			offset: <number | undefined>0
		} }: Props = $props();
</script>

<div class="flex items-start justify-between">
	<div class="text-[0.65rem] opacity-70">
		<p>Page: <strong>({currentPage})</strong></p>
		<p>
			Showing:
			<strong>{pageLength} of {filters.limit ?? 0}</strong>
		</p>
	</div>
	<div class="flex items-center gap-2">
		<Button disabled={filters.offset === 0} on:click={() => dispatch('prev')}>
			<Icon icon="ic:outline-first-page" />
			<span>Prev</span>
		</Button>
		<Button on:click={() => dispatch('next')}>
			<span>Next</span>
			<Icon icon="ic:outline-last-page" />
		</Button>
	</div>
</div>
