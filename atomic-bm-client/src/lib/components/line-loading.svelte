<div class="loader-line"></div>

<style>
	.loader-line {
		width: 100%;
		height: 3px;
		position: relative;
		overflow: hidden;
		-webkit-border-radius: 20px;
		-moz-border-radius: 20px;
		border-radius: 20px;
        background-color: #14161f
	}

	.loader-line:before {
		content: '';
		position: absolute;
		left: -50%;
		height: 3px;
		width: 40%;
		background-color: #a8a3a8;
		-webkit-animation: lineAnim 1s linear infinite;
		-moz-animation: lineAnim 1s linear infinite;
		animation: lineAnim 1s linear infinite;
		-webkit-border-radius: 20px;
		-moz-border-radius: 20px;
		border-radius: 20px;
	}

	@keyframes lineAnim {
		0% {
			left: -40%;
		}
		50% {
			left: 20%;
			width: 80%;
		}
		100% {
			left: 100%;
			width: 100%;
		}
	}
</style>
