<script lang="ts">
	import Button from '$lib/components/ui/button.svelte';
	import BaseModal from './base-modal.svelte';
	import { createEventDispatcher } from 'svelte';
	import { cn } from '$lib/utils';

	const d = createEventDispatcher();
	interface Props {
		open?: boolean;
		description?: string;
		cancelText?: string;
		confirmText?: string;
		contentClass?: string;
		title?: string;
	}

	let {
		open = false,
		description = '',
		cancelText = 'Cancel',
		confirmText = 'Confirm',
		contentClass = '',
		title = ''
	}: Props = $props();
</script>

<BaseModal
	{open}
	on:openChange={(e) => d('openChange', e.detail)}
	contentClass={cn('max-w-[320px] rounded-xl', contentClass)}
	{title}
>
	<p>{@html description}</p>
	<div class="mt-5 flex items-center justify-end gap-2">
		<Button on:click={() => d('cancel')}>{cancelText}</Button>
		<Button theme="danger" on:click={() => d('confirm')}>{confirmText}</Button>
	</div>
</BaseModal>
