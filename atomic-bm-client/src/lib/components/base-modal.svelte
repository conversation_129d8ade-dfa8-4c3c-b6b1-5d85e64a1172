<!-- @migration-task Error while migrating Svelte code: This migration would change the name of a slot (title to title_1) making the component unusable -->
<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import { createEventDispatcher } from 'svelte';
	import { cn, flyAndScale } from '$lib/utils';

	const d = createEventDispatcher();
	export let open = false,
		empty = false,
		closeOnOutsideClick = true,
		closeOnEscape = true,
		noCloseBtn = false,
		areaClass = '',
		transition = flyAndScale,
		transitionDuration = 200,
		titleSlot = false,
		title = '',
		contentClass = '';
</script>

<Dialog.Root {open} onOpenChange={(e) => d('openChange', e)}>
	<Dialog.Content class={cn('bg-dark-9 max-w-[320px]', contentClass)}>
		{#if !empty}
			<Dialog.Header>
				<Dialog.Title>
					{#if titleSlot}
						<slot name="title" />
					{:else}
						{@html title}
					{/if}
				</Dialog.Title>
			</Dialog.Header>
			<Dialog.Description class={cn('', areaClass)}>
				{#if titleSlot}
					<slot name="area" />
				{:else}
					<slot />
				{/if}
			</Dialog.Description>
		{:else}
			<slot />
		{/if}
	</Dialog.Content>
</Dialog.Root>
