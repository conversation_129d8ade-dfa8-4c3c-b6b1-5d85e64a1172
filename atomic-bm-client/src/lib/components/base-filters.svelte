<script lang="ts">
	import { run } from 'svelte/legacy';

	import { createEventDispatcher } from 'svelte';
	import type { GetBotsOrderBy, GetAccountsOrderBy, GetGroupsOrderBy } from '$client/api.schemas';
	import { toast } from '$utils';
	import Popover from './ui/popover.svelte';
	import Button from './ui/button.svelte';
	import Card from './ui/card/card.svelte';
	import Input from './ui/input.svelte';

	type FilterType = 'accounts' | 'bots' | 'groups';

	interface Props {
		filterType?: FilterType;
		folded?: boolean;
		filterByFocused?: boolean;
		orderByFocused?: boolean;
		directionFocused?: boolean;
		pageLimitFocused?: boolean;
		activeStateFocused?: boolean;
		pageLimit?: number | undefined;
		botsOrderBy?: GetBotsOrderBy | undefined;
		accountsOrderBy?: GetAccountsOrderBy | undefined;
		groupsOrderBy?: GetGroupsOrderBy | undefined;
		directionOption?: 'ASC' | 'DESC' | undefined;
		activeState?: boolean;
		accountId?: string | undefined;
		symbol?: string | undefined;
		region?: string | undefined;
		status?: string | undefined;
		exchangeId?: string | undefined;
		tag?: string | undefined;
		name?: string | undefined;
		createdByUserID?: string | undefined;
	}

	let {
		filterType = 'bots',
		folded = false,
		filterByFocused = $bindable(false),
		orderByFocused = $bindable(false),
		directionFocused = $bindable(false),
		pageLimitFocused = $bindable(false),
		activeStateFocused = $bindable(false),
		pageLimit = $bindable(undefined),
		botsOrderBy = $bindable(undefined),
		accountsOrderBy = $bindable(undefined),
		groupsOrderBy = $bindable(undefined),
		directionOption = $bindable(undefined),
		activeState = $bindable(false),
		accountId = $bindable(undefined),
		symbol = $bindable(undefined),
		region = $bindable(undefined),
		status = $bindable(undefined),
		exchangeId = $bindable(undefined),
		tag = $bindable(undefined),
		name = $bindable(undefined),
		createdByUserID = $bindable(undefined)
	}: Props = $props();

	let refreshed = $state(false);
	run(() => {
		if (refreshed) {
			setTimeout(() => {
				refreshed = false;
			}, 500);
		}
	});

	const d = createEventDispatcher();

	function selectBotsOrderBy(order: GetBotsOrderBy) {
		if (botsOrderBy === order) {
			botsOrderBy = undefined;
		} else {
			botsOrderBy = order;
			d('botsOrderBy', order);
		}
	}

	function selectAccountOrderBy(order: GetAccountsOrderBy) {
		if (accountsOrderBy === order) {
			accountsOrderBy = undefined;
		} else {
			accountsOrderBy = order;
			d('accountsOrderBy', order);
		}
	}

	function selectGroupOrderBy(order: GetGroupsOrderBy) {
		if (groupsOrderBy === order) {
			groupsOrderBy = undefined;
		} else {
			groupsOrderBy = order;
			d('groupsOrderBy', order);
		}
	}

	function selectDirection(direction: 'ASC' | 'DESC') {
		if (directionOption === direction) {
			directionOption = undefined;
		} else {
			directionOption = direction;
			d('direction', directionOption);
		}
	}

	function selectActiveState(state: boolean) {
		if (activeState === state) {
			return;
		}
		activeState = state;
		d('activeState', activeState);
	}

	function confirmFilterOptions() {
		if (filterType === 'bots') {
			if (!accountId && !symbol && !region && !status) {
				toast('Select at least one filter option', 'error');
			} else {
				d('filterOptions', {
					accountId,
					symbol,
					region,
					status
				});
			}
		} else if (filterType === 'accounts') {
			if (!exchangeId && !tag) {
				toast('Select at least one filter option', 'error');
			} else {
				d('filterOptions', {
					exchangeId,
					tag
				});
			}
		}
	}
</script>

{#if !folded}
	<div class="flex flex-wrap gap-1">
		<Popover open={filterByFocused} on:openChange={(e) => (filterByFocused = e.detail)}>
			{#snippet trigger()}
				<Button
					focused={filterByFocused}
					icon="bi:caret-down"
					small
					class="!text-[0.65rem]"
					iconClass="text-xs">Filter</Button
				>
			{/snippet}
			{#snippet content()}
				<Card freeSize class="px-3 py-2.5">
					<h2 class="text-sm font-bold">Select filter options</h2>
					<p class="mt-1 max-w-[150px] text-xs">
						{#if filterType === 'bots'}
							Search by account, symbol, region, or status.
						{:else if filterType === 'accounts'}
							Search by exchange or tag.
						{/if}
					</p>
					<div class="mt-3 space-y-2">
						{#if filterType === 'bots'}
							<Input
								label="Account"
								placeholder="Insert an account"
								pasteBtn
								on:paste={(e) => {
									accountId = e.detail;
								}}
								bind:value={accountId}
							/>
							<Input
								label="Symbol"
								placeholder="Insert a symbol"
								pasteBtn
								on:paste={(e) => {
									symbol = e.detail;
								}}
								bind:value={symbol}
							/>
							<Input
								label="Region"
								placeholder="Insert a region"
								pasteBtn
								on:paste={(e) => {
									region = e.detail;
								}}
								bind:value={region}
							/>
							<Input
								label="Status"
								placeholder="Insert a status"
								pasteBtn
								on:paste={(e) => {
									status = e.detail;
								}}
								bind:value={status}
							/>
						{:else if filterType === 'accounts'}
							<Input
								label="Exchange"
								placeholder="Insert an exchange"
								pasteBtn
								on:paste={(e) => {
									exchangeId = e.detail;
								}}
								bind:value={exchangeId}
							/>
							<Input label="Tag" placeholder="Insert a tag" pasteBtn on:paste bind:value={tag} />
						{:else if filterType === 'groups'}
							<Input
								label="Name"
								placeholder="Insert a group name"
								pasteBtn
								on:paste={(e) => {
									name = e.detail;
								}}
								bind:value={name}
							/>
							<Input
								label="Created by user ID"
								placeholder="Insert a user ID"
								pasteBtn
								on:paste={(e) => {
									createdByUserID = e.detail;
								}}
								bind:value={createdByUserID}
							/>
						{/if}
					</div>
					<div class="mt-5 flex justify-end gap-2">
						<Button on:click={() => d('clearFilterOptions')}>Clear</Button>
						<Button on:click={confirmFilterOptions}>Filter</Button>
					</div>
				</Card>
			{/snippet}
		</Popover>
		<Popover open={orderByFocused} on:openChange={(e) => (orderByFocused = e.detail)}>
			{#snippet trigger()}
				<Button
					focused={orderByFocused}
					icon="bi:caret-down"
					small
					class="!text-[0.65rem]"
					iconClass="text-xs">Order</Button
				>
			{/snippet}
			{#snippet content()}
				<Card freeSize class="px-3 py-2.5">
					<h2 class="text-sm font-bold">Select order option</h2>
					<p class="mt-1 max-w-[150px] text-xs">Filter your results based on the selected order.</p>
					<div class="mt-3 space-y-2">
						{#if filterType === 'bots'}
							<Button
								full
								focused={botsOrderBy === 'created_at'}
								on:click={() => selectBotsOrderBy('created_at')}>Created At</Button
							>
							<Button
								full
								focused={botsOrderBy === 'account_id'}
								on:click={() => selectBotsOrderBy('account_id')}>Account</Button
							>
							<Button
								full
								focused={botsOrderBy === 'symbol'}
								on:click={() => selectBotsOrderBy('symbol')}>Symbol</Button
							>
							<Button
								full
								focused={botsOrderBy === 'region'}
								on:click={() => selectBotsOrderBy('region')}>Region</Button
							>
							<Button
								full
								focused={botsOrderBy === 'status'}
								on:click={() => selectBotsOrderBy('status')}>Status</Button
							>
						{:else if filterType === 'accounts'}
							<Button
								full
								focused={accountsOrderBy === 'created_at'}
								on:click={() => selectAccountOrderBy('created_at')}>Created At</Button
							>
							<Button
								full
								focused={accountsOrderBy === 'exchange_id'}
								on:click={() => selectAccountOrderBy('exchange_id')}>Exchange</Button
							>
							<Button
								full
								focused={accountsOrderBy === 'tag'}
								on:click={() => selectAccountOrderBy('tag')}>Tag</Button
							>
							<Button
								full
								focused={accountsOrderBy === 'updated_at'}
								on:click={() => selectAccountOrderBy('updated_at')}>Last Updated</Button
							>
							<Button
								full
								focused={accountsOrderBy === 'deleted_at'}
								on:click={() => selectAccountOrderBy('deleted_at')}>Deleted At</Button
							>
						{:else if filterType === 'groups'}
							<Button
								full
								focused={groupsOrderBy === 'name'}
								on:click={() => selectGroupOrderBy('name')}>Created At</Button
							>
							<Button
								full
								focused={groupsOrderBy === 'created_at'}
								on:click={() => selectGroupOrderBy('created_at')}>Name</Button
							>
							<Button
								full
								focused={groupsOrderBy === 'created_by_user_id'}
								on:click={() => selectGroupOrderBy('created_by_user_id')}>User ID</Button
							>
						{/if}
					</div>
				</Card>
			{/snippet}
		</Popover>
		<Popover open={directionFocused} on:openChange={(e) => (directionFocused = e.detail)}>
			{#snippet trigger()}
				<Button
					focused={directionFocused}
					icon="bi:caret-down"
					small
					class="!text-[0.65rem]"
					iconClass="text-xs">Direction</Button
				>
			{/snippet}
			{#snippet content()}
				<Card freeSize class="px-3 py-2.5">
					<h2 class="text-sm font-bold">Select direction</h2>
					<p class="mt-1 max-w-[150px] text-xs">Define the displayed direction of the results.</p>
					<div class="mt-3 space-y-2">
						<Button full focused={directionOption === 'ASC'} on:click={() => selectDirection('ASC')}
							>Crescent</Button
						>
						<Button
							full
							focused={directionOption === 'DESC'}
							on:click={() => selectDirection('DESC')}>Decrescent</Button
						>
					</div>
				</Card>
			{/snippet}
		</Popover>
		<Popover open={pageLimitFocused} on:openChange={(e) => (pageLimitFocused = e.detail)}>
			{#snippet trigger()}
				<Button
					focused={pageLimitFocused}
					icon="bi:caret-down"
					small
					class="!text-[0.65rem]"
					iconClass="text-xs">Page limit</Button
				>
			{/snippet}
			{#snippet content()}
				<Card
					freeSize
					asForm
					class="px-3 py-2.5"
					on:submit={() => {
						d('pageLimit', pageLimit);
						pageLimitFocused = false;
					}}
				>
					<h2 class="text-sm font-bold">Select a page limit</h2>
					<p class="mt-1 max-w-[250px] text-xs">Define the number of results per page.</p>
					<div class="mt-3 space-y-2">
						<Input required label="Limit" placeholder="Insert a limit" bind:value={pageLimit} />
					</div>
					<div class="mt-5 flex justify-end gap-2">
						<Button
							on:click={() => {
								d('pageLimit', undefined);
								pageLimit = undefined;
							}}>Reset</Button
						>
						<Button type="submit">Filter</Button>
					</div>
				</Card>
			{/snippet}
		</Popover>
		{#if filterType !== 'groups'}
			<Popover open={activeStateFocused} on:openChange={(e) => (activeStateFocused = e.detail)}>
				{#snippet trigger()}
					<Button
						focused={activeStateFocused}
						icon="bi:caret-down"
						small
						class="!text-[0.65rem]"
						iconClass="text-xs">Active state</Button
					>
				{/snippet}
				{#snippet content()}
					<Card freeSize class="px-3 py-2.5">
						<h2 class="text-sm font-bold">Select active state</h2>
						<p class="mt-1 max-w-[150px] text-xs">
							Filter your results based on the selected state.
						</p>
						<div class="mt-3 space-y-2">
							<Button full focused={!activeState} on:click={() => selectActiveState(false)}
								>Active</Button
							>
							<Button full focused={activeState} on:click={() => selectActiveState(true)}
								>Deleted</Button
							>
						</div>
					</Card>
				{/snippet}
			</Popover>
		{/if}
		{#snippet trigger()}
			<Button
				iconBtn
				icon="material-symbols:refresh"
				iconClass="text-sm"
				small
				on:click={() => {
					d('refresh');
					refreshed = true;
				}}
			/>
		{/snippet}
	</div>
{:else}
	{#snippet trigger()}
		<Button iconBtn icon="mi:filter" iconClass="text-sm" small />
	{/snippet}
{/if}
