<script lang="ts">
	import * as Table from '$lib/shadcn/ui/table';
	import { createEventDispatcher } from 'svelte';
	import Icon from './ui/icon.svelte';
	import Popover from './ui/popover.svelte';
	import Pagination from './pagination.svelte';

	const dispatch = createEventDispatcher();

	type T = $$Generic;
	type HeaderOptions = {
		class?: string;
		title: string;
	};

	interface Props {
		loading?: boolean;
		title?: string;
		name?: string;
		data?: Array<T>;
		dataHeaders?: Array<HeaderOptions>;
		hideActions?: boolean;
		hideDetails?: boolean;
		currentPage?: number;
		pageLength?: number;
		filters?: any;
		children?: import('svelte').Snippet<[any]>;
	}

	let {
		loading = false,
		title = '',
		name = '',
		data = [],
		dataHeaders = [],
		hideActions = false,
		hideDetails = false,
		currentPage = 1,
		pageLength = 0,
		filters = {
			limit: <number | undefined>10,
			offset: <number | undefined>0
		},
		children
	}: Props = $props();

	const TableCell = Table.Cell;
</script>

<div class="max-h-[710px] overflow-y-auto">
	<Table.Root class="border-b border-gray-900">
		<Table.Caption>{title}</Table.Caption>
		<Table.Header>
			<Table.Row class="border-gray-900 bg-dark-3 hover:bg-dark-3">
				{#each dataHeaders as header}
					<Table.Head class={header.class}>{header.title}</Table.Head>
				{/each}
				{#if !hideActions}
					<Table.Head class="text-right opacity-0 pointer-events-none">actions</Table.Head>
				{/if}
			</Table.Row>
		</Table.Header>
		<Table.Body>
			{#if loading}
				<Table.Row class="border-gray-900 hover:bg-dark-6">
					<Table.Cell class="text-center" colspan={5}>Loading...</Table.Cell>
				</Table.Row>
			{:else}
				{#each data as item, i (i)}
					<Table.Row class="border-gray-900 hover:bg-dark-6">
						{@render children?.({ TableCell, item, i, })}
						<Table.Cell class="text-right">
							<div class="flex items-center justify-end">
								<Popover distance={1} forceClose>
									{#snippet trigger()}
																		<div  class="flex items-center">
											<Icon icon="pepicons-pop:dots-y" class="text-xl" />
										</div>
																	{/snippet}
									{#snippet content()}
																		<div  class="w-[150px] rounded-md border border-gray-950">
											{#if !hideDetails}
												<button
													class="flex items-center w-full gap-1 p-2 font-extrabold border-b border-gray-950 hover:bg-dark-6"
													onclick={() => dispatch('update')}
												>
													<Icon icon="icon-park-solid:setting-computer" />
													<span class="text-xs"><span class="capitalize">{name}</span> details</span>
												</button>
											{/if}
											<button
												class="flex items-center w-full gap-1 p-2 font-extrabold hover:bg-red-950"
												onclick={() => dispatch('delete', item)}
											>
												<Icon icon="icon-park-outline:disabled-computer" class="text-red-500" />
												<span class="text-xs text-red-500">Remove {name}</span>
											</button>
										</div>
																	{/snippet}
								</Popover>
							</div>
						</Table.Cell>
					</Table.Row>
				{:else}
					<Table.Row class="border-gray-900 hover:bg-dark-6">
						<Table.Cell class="text-center" colspan={5}>No {name}s found</Table.Cell>
					</Table.Row>
				{/each}
			{/if}
		</Table.Body>
	</Table.Root>
	<Pagination
		on:next={() => dispatch('next')}
		on:prev={() => dispatch('prev')}
		{currentPage}
		{pageLength}
		{filters}
	/>
</div>
