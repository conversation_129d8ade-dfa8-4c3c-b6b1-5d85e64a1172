<!-- @migration-task Error while migrating Svelte code: $$props is used together with named props in a way that cannot be automatically migrated. -->
<script lang="ts">
	import { cn } from '$lib/utils';
	import { goto } from '$app/navigation';

	export let icon: string = '',
		size: 'small' | 'medium' | 'large' | 'extraLarge' = 'medium',
		color: 'default' | 'success' | 'dark' | 'danger' | 'warning' | 'white' = 'default',
		title = '',
		id = '',
		href = '',
		btn = false;

	const sizes = {
			small: 'text-xs',
			medium: 'text-base',
			large: 'text-2xl',
			extraLarge: 'text-3xl'
		},
		colors = {
			default: 'text-white',
			success: 'text-green-500',
			dark: 'text-dark-2',
			danger: 'text-red-500',
			warning: 'text-yellow-500',
			white: 'text-white'
		};
</script>

<!-- svelte-ignore a11y-click-events-have-key-events -->
<!-- svelte-ignore a11y-no-static-element-interactions -->
{#if href}
	<!-- svelte-ignore element_invalid_self_closing_tag -->
	<iconify-icon
		{id}
		{title}
		{icon}
		class={cn(`${sizes[size]} ${colors[color]}`, $$props.class)}
		class:cursor-pointer={btn}
		class:p-0.5={btn}
		class:hover:bg-gray-900={btn}
		class:transition-all={btn}
		on:click={() => goto(href)}
	/>
{:else}
	<!-- svelte-ignore element_invalid_self_closing_tag -->
	<iconify-icon
		{id}
		{title}
		{icon}
		class={cn(`${sizes[size]} ${colors[color]}`, $$props.class)}
		class:cursor-pointer={btn}
		class:p-0.5={btn}
		class:hover:bg-gray-900={btn}
		class:transition-all={btn}
		on:click
	/>
{/if}
