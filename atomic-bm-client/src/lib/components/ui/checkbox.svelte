<!-- @migration-task Error while migrating Svelte code: $$props is used together with named props in a way that cannot be automatically migrated. -->
<script lang="ts">
	import { Checkbox, Label } from 'bits-ui';
	import { createEventDispatcher } from 'svelte';
	import { cn } from '$lib/utils';
	import Icon from './icon.svelte';

	const d = createEventDispatcher();
	export let label = '',
		id = '',
		labelClass = '',
		rootClass = '',
		labelSlot = false;
</script>

<div class={cn('flex items-center gap-0.5', $$props.class)}>
	<Checkbox.Root
		{id}
		aria-labelledby="terms-label"
		class={cn(
			'active:scale-98 data-[state=unchecked]:border-border-input data-[state=unchecked]:hover:border-dark-40 peer inline-flex size-[25px] items-center justify-center rounded-md border border-muted transition-all duration-150 ease-in-out',
			rootClass
		)}
		checked="indeterminate"
		onCheckedChange={(e) => d('checked', e)}
	>
		<Checkbox.Indicator
			let:isChecked
			let:isIndeterminate
			class="inline-flex h-[20px] w-[20px] items-center justify-center"
		>
			{#if isChecked}
				<Icon icon="fluent:checkbox-checked-20-filled" class="text-xl" />
			{:else if isIndeterminate}
				<Icon icon="fluent:checkbox-unchecked-20-filled" class="text-xl" />
			{:else}
				<Icon icon="fluent:checkbox-unchecked-20-filled" class="text-xl" />
			{/if}
		</Checkbox.Indicator>
	</Checkbox.Root>
	<Label.Root
		id="terms-label"
		for={id}
		class={cn(
			'text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
			labelClass
		)}
	>
		{#if labelSlot}
			<slot />
		{:else}
			{label}
		{/if}
	</Label.Root>
</div>
