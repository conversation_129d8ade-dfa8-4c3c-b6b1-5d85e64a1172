<!-- @migration-task Error while migrating Svelte code: $$props is used together with named props in a way that cannot be automatically migrated. -->
<script lang="ts">
	import { cn } from '$lib/utils';
	import Icon from './icon.svelte';

	export let href = '',
		title = '',
		icon = '',
		iconClass = '',
		iconType: 'download' | 'upload' | 'copy' | 'share' | 'save' | 'remove' | 'add' | 'edit' | '' =
			'',
		type: 'button' | 'submit' | 'reset' = 'button',
		disabled = false,
		iconBtn = false,
		focused = false,
		full = false,
		small = false,
		blank = false;

	const iconTypes = {
		save: 'material-symbols:save',
		remove: 'ph:trash',
		add: 'material-symbols:add',
		edit: 'bx:edit',
		share: 'material-symbols:share',
		copy: 'zondicons:copy',
		upload: 'mingcute:upload-line',
		download: 'uil:cloud-download'
	};
</script>

{#if href}
	<a
		{title}
		{href}
		target={blank ? '_blank' : undefined}
		class={cn(
			`group flex items-center justify-center rounded-md border border-gray-800 bg-dark-7 px-4 py-2 text-xs font-semibold tracking-wide shadow-md transition-all hover:border-gray-700`,
			$$props.class
		)}
		class:gap-1.5={icon || iconType}
		class:opacity-50={disabled}
		class:poiter-events-none={disabled}
		class:p-1={iconBtn}
		class:!border-gray-600={focused}
		class:!w-full={full}
		class:!py-1.5={small}
		class:!px-3={small}
		class:!text-xs={small}
		on:click
	>
		{#if icon}
			<Icon {icon} class={cn('', iconClass)} />
		{:else if iconType}
			<Icon icon={iconTypes[iconType]} class={cn('', iconClass)} />
		{/if}
		<slot />
	</a>
{:else}
	<button
		{title}
		{disabled}
		{type}
		class={cn(
			`group flex items-center justify-center rounded-md border border-gray-800 bg-dark-7 px-4 py-2 text-xs font-semibold tracking-wide shadow-md transition-all hover:border-gray-700`,
			$$props.class
		)}
		class:gap-1.5={icon || iconType}
		class:opacity-50={disabled}
		class:poiter-events-none={disabled}
		class:!p-1={iconBtn}
		class:!border-gray-600={focused}
		class:w-full={full}
		class:!py-1.5={small}
		class:!px-3={small}
		class:!text-xs={small}
		on:click
	>
		{#if icon}
			<Icon {title} {icon} class={cn('', iconClass)} />
		{:else if iconType}
			<Icon {title} icon={iconTypes[iconType]} class={cn('', iconClass)} />
		{/if}
		<slot />
	</button>
{/if}
