<!-- @migration-task Error while migrating Svelte code: $$props is used together with named props in a way that cannot be automatically migrated. -->
<script lang="ts">
	import { cn } from '$lib/utils';

	type TSizes = 'sm' | 'md' | 'lg' | 'xl';
	export let asButton = false,
		asForm = false,
		freeSize = false,
		border = true,
		id = '',
		size: TSizes = 'sm';

	const cardSizes = {
		sm: 'max-w-[280px] h-[140px]',
		md: 'max-w-[380px] h-[170px]',
		lg: 'max-w-[620px] h-[380px]',
		xl: 'max-w-[860px] h-[620px]'
	};
</script>

{#if asForm}
	<form
		{id}
		class={cn(
			`w-full rounded-md bg-dark-9 shadow-lg ${!freeSize && cardSizes[size]}`,
			$$props.class
		)}
		class:border
		class:border-gray-800={border}
		on:submit|preventDefault
	>
		<slot />
	</form>
{:else if asButton}
	<!-- svelte-ignore a11y-no-static-element-interactions -->
	<!-- svelte-ignore a11y-click-events-have-key-events -->
	<div
		{id}
		class={cn(
			`w-full cursor-pointer rounded-md bg-dark-9 shadow-md transition-all hover:opacity-70 ${!freeSize && cardSizes[size]}`,
			$$props.class
		)}
		class:border
		class:border-gray-800={border}
		on:click
	>
		<slot />
	</div>
{:else}
	<div
		{id}
		class={cn(
			`w-full rounded-md bg-dark-9 shadow-md ${!freeSize && cardSizes[size]}`,
			$$props.class
		)}
		class:border
		class:border-gray-800={border}
	>
		<slot />
	</div>
{/if}
