<!-- @migration-task Error while migrating Svelte code: $$props is used together with named props in a way that cannot be automatically migrated. -->
<script>
	import { cn } from '$lib/utils';
	import { createEventDispatcher } from 'svelte';
	import Icon from '../icon.svelte';

	const d = createEventDispatcher();
	export let empty = false,
		headerIcon = 'vaadin:close-small',
		iconClass = '',
		titleClass = '',
		hideIcon = false,
		title = '';
</script>

{#if empty}
	<div class={cn('border-b border-gray-800 p-3', $$props.class)}>
		<slot />
	</div>
{:else}
	<div
		class={cn(
			'flex h-[30px] items-center justify-between border-b border-gray-800 p-3',
			$$props.class
		)}
	>
		<h2 class={cn('text-sm font-medium', titleClass)}>{title}</h2>
		{#if !hideIcon}
			<Icon
				icon={headerIcon}
				btn
				class={cn('text-gray-400', iconClass)}
				on:click={() => d('iconClick')}
			/>
		{/if}
	</div>
{/if}
