<!-- @migration-task Error while migrating Svelte code: $$props is used together with named props in a way that cannot be automatically migrated. -->
<script lang="ts">
	import { Combobox } from 'bits-ui';
	import Icon from './icon.svelte';
	import { createEventDispatcher } from 'svelte';
	import { fade } from 'svelte/transition';
	import { twMerge } from 'tailwind-merge';
	import Button from './button.svelte';

	const dispatch = createEventDispatcher();
	export let placeholder = '';
	export let label = '';
	export let defaultSelected: any = '';
	export let inputValue = '';
	export let hasSelected = false;
	export let disabled = false;
	export let showCreateBtn = true;
	export let required = false;
	export let contentClass = '';
	export let itemClass = '';
	export let description = '';
	export let id = '';

	interface options {
		value: string | number;
		label: string;
	}
	export let options: options[] = [];

	function exportSelectedValue(s: any) {
		dispatch('selected', {
			label: s.label,
			value: s.value
		});
		hasSelected = true;
	}
	function clearSelectedValue() {
		dispatch('unselected', {
			label: '',
			value: ''
		});
		hasSelected = false;
		inputValue = '';
	}

	$: filteredOptions = inputValue
		? options.filter(
				(item) => typeof item.value === 'string' && item.value.includes(inputValue.toLowerCase())
			)
		: options;
</script>

<div class={twMerge('flex w-full flex-col gap-1.5', $$props.class)}>
	<div class="flex flex-col">
		<span class="text-xs opacity-90"
			>{label}
			{#if required}
				<span class="text-xs text-red-900">*</span>
			{/if}
		</span>
		<span class="text-[0.64rem] opacity-70"> {description} </span>
	</div>
	<Combobox.Root
		{required}
		items={filteredOptions}
		bind:inputValue
		onSelectedChange={exportSelectedValue}
		selected={defaultSelected}
	>
		<div class="relative">
			<Combobox.Input
				{id}
				{required}
				class="flex w-full items-center rounded-md bg-inherit px-2 py-[11px] text-xs outline-none ring-1 ring-gray-800 placeholder:text-xs"
				{placeholder}
				aria-label={placeholder}
			/>
			{#if hasSelected || inputValue}
				<button class="absolute right-6 top-0 z-20 mr-2 mt-2" on:click={clearSelectedValue}>
					<Icon icon="grommet-icons:clear" />
				</button>
			{/if}
			<div class="absolute right-0 top-0 z-20 mr-2 mt-2">
				<Icon class="ml-auto" icon="ph:caret-down" />
			</div>
		</div>
		<Combobox.Content
			class={twMerge(
				'z-50 max-h-[250px] w-full overflow-y-auto rounded-md bg-black font-semibold text-white shadow-popover outline-none ring-1 ring-dark-6',
				contentClass
			)}
			transition={fade}
			transitionConfig={{
				duration: 50
			}}
			sideOffset={8}
		>
			{#if hasSelected}
				<div class="my-3 flex flex-col items-center justify-center gap-3 text-xs">
					<p>
						<span class="font-extrabold">Selected:</span>
						<span class="bg-dark-6 px-1 py-0.5">
							{inputValue}
						</span>
					</p>
					<Button on:click={clearSelectedValue}>Select again</Button>
				</div>
			{:else}
				{#each filteredOptions as item (item.value)}
					<Combobox.Item
						class={twMerge(
							'flex h-10 w-full cursor-pointer select-none items-center px-2.5 py-3 text-xs outline-none transition-all duration-75 data-[highlighted]:bg-primary data-[highlighted]:text-white',
							itemClass
						)}
						value={item.value}
						label={item.label}
						{disabled}
					>
						{item.label}
						<Combobox.ItemIndicator class="ml-auto" asChild={false}>
							<Icon icon="uis:check" class="text-primary" />
						</Combobox.ItemIndicator>
					</Combobox.Item>
				{:else}
					<div class="flex flex-col items-center justify-center">
						<span class="block px-5 py-2 text-sm text-muted-foreground"> No results found </span>
					</div>
				{/each}
				{#if showCreateBtn}
					<div class="my-3 flex items-center justify-center">
						<Button
							on:click={() => {
								dispatch('create');
							}}>Create</Button
						>
					</div>
				{/if}
			{/if}
		</Combobox.Content>
		<Combobox.HiddenInput name="searcher" />
	</Combobox.Root>
</div>
