<!-- @migration-task Error while migrating Svelte code: This migration would change the name of a slot (content to content_1) making the component unusable -->
<script lang="ts">
	import { cn } from '$lib/utils';
	import { Tooltip } from 'bits-ui';
	import { createEventDispatcher } from 'svelte';
	import { scale } from 'svelte/transition';

	const d = createEventDispatcher();
	export let triggerClass = '',
		open = false,
		contentSlot = false,
		closeOnPointerClick = true,
		content = '',
		placement: 'top' | 'right' | 'bottom' | 'left' = 'bottom',
		distance = 15,
		alignSide = 0,
		arrowSize = 10,
		arrowClass = '',
		openDelay = 150,
		openDuration = 50,
		contentClass = '';
</script>

<Tooltip.Root
	{openDelay}
	{open}
	closeOnPointerDown={closeOnPointerClick}
	onOpenChange={(e) => d('openChange', e)}
>
	<Tooltip.Trigger class={cn('', triggerClass)}>
		<slot name="trigger" />
	</Tooltip.Trigger>
	<Tooltip.Content
		side={placement}
		sideOffset={distance}
		alignOffset={alignSide}
		transition={scale}
		transitionConfig={{ duration: openDuration }}
	>
		<div class="bg-background">
			<Tooltip.Arrow
				size={arrowSize}
				class={cn('rounded-[1px] border-l border-t border-gray-200', arrowClass)}
			/>
		</div>
		<div
			class={cn(
				'flex items-center justify-center overflow-hidden rounded-md bg-white p-1.5 text-xs font-medium shadow-md ring-1 ring-gray-200',
				contentClass
			)}
		>
			{#if contentSlot}
				<slot name="content" />
			{:else}
				{content}
			{/if}
		</div>
	</Tooltip.Content>
</Tooltip.Root>
