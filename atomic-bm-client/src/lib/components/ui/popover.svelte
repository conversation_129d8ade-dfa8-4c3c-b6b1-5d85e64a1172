<script lang="ts">
	import { cn } from '$lib/utils';
	import { Popover } from 'bits-ui';
	import { fade } from 'svelte/transition';
	import { createEventDispatcher } from 'svelte';

	const d = createEventDispatcher();
	interface Props {
		contentClass?: string;
		triggerClass?: string;
		open?: boolean;
		placement?: 'top' | 'right' | 'bottom' | 'left';
		distance?: number;
		alignSide?: number;
		forceClose?: boolean;
		trigger?: import('svelte').Snippet;
		content?: import('svelte').Snippet;
	}

	let {
		contentClass = '',
		triggerClass = '',
		open = false,
		placement = 'bottom',
		distance = 5,
		alignSide = 0,
		forceClose = false,
		trigger,
		content
	}: Props = $props();
</script>

<Popover.Root
	disableFocusTrap
	closeOnEscape
	{open}
	onOpenChange={(e) => {
		d('openChange', e);
	}}
>
	<Popover.Trigger class={triggerClass}>
		{@render trigger?.()}
	</Popover.Trigger>
	<Popover.Content
		side={placement}
		sideOffset={distance}
		alignOffset={alignSide}
		class={cn('bg-dark-9 z-50 overflow-hidden rounded-md text-white shadow-lg', contentClass)}
		transition={fade}
		transitionConfig={{ duration: 100 }}
	>
		{#if forceClose}
			<Popover.Close>
				{@render content?.()}
			</Popover.Close>
		{:else}
			{@render content?.()}
		{/if}
	</Popover.Content>
</Popover.Root>
