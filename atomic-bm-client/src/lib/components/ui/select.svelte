<script lang="ts">
	import { Select } from 'bits-ui';
	import Icon from './icon.svelte';
	import { createEventDispatcher } from 'svelte';
	import { fade } from 'svelte/transition';

	const dispatch = createEventDispatcher();


	interface Theme {
		value: string;
		label: string;
	}
	interface Props {
		placeholder?: string;
		label?: string;
		defaultSelected?: any;
		options?: Theme[];
	}

	let {
		placeholder = '',
		label = '',
		defaultSelected = '',
		options = []
	}: Props = $props();

	function exportSelectedValue(s: any) {
		dispatch('selected', {
			label: s.label,
			value: s.value
		});
	}
</script>

<div class="flex w-full flex-col gap-1">
	<span class="text-xs">{label}</span>
	<Select.Root items={options} selected={defaultSelected} onSelectedChange={exportSelectedValue}>
		<Select.Trigger
			class="flex h-[35px] w-full items-center rounded-md border border-gray-800 bg-inherit px-2 font-light"
			aria-label={placeholder}
		>
			<Select.Value class="text-xs text-gray-300" {placeholder} />
			<Icon class="ml-auto" icon="ph:caret-down" />
		</Select.Trigger>
		<Select.Content
			class="max-h-[150px] w-full overflow-y-auto rounded-md border border-gray-800 bg-dark-9 p-1 text-white shadow-popover outline-none"
			transition={fade}
			transitionConfig={{
				duration: 50
			}}
			sideOffset={8}
		>
			{#each options as theme}
				<Select.Item
					class="rounded-button flex h-10 w-full cursor-pointer select-none items-center py-3 pl-5 pr-1.5 text-sm outline-none transition-all duration-75 data-[highlighted]:bg-blue-1"
					value={theme.value}
					label={theme.label}
				>
					{theme.label}
					<Select.ItemIndicator class="ml-auto flex items-center" asChild={false}>
						<Icon icon="uis:check" class="text-white" />
					</Select.ItemIndicator>
				</Select.Item>
			{/each}
		</Select.Content>
		<Select.Input name="favoriteFruit" />
	</Select.Root>
</div>
