<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import Card from './ui/card/card.svelte';
	import Icon from './ui/icon.svelte';
	interface Props {
		tabs?: import('svelte').Snippet;
	}

	let { tabs }: Props = $props();

	const d = createEventDispatcher();
</script>

<Card freeSize class="flex h-[40px] items-center justify-between gap-1.5 px-2">
	<Icon icon="mingcute:react-line" class="text-lg animate-spin text-blue-1" />
	<div class="flex-1 pt-0.5 overflow-hidden">
		{@render tabs?.()}
	</div>
	<Icon icon="entypo:menu" btn class="text-lg" on:click={() => d('openMenu')} />
</Card>
