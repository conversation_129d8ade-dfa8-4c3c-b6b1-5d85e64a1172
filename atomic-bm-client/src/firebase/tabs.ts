import { doc, getDoc, setDoc, deleteDoc, updateDoc, collection, getDocs } from 'firebase/firestore';
import type {
	Tab,
	ReorderedTab,
	BaseTabResponse,
	BaseTabsResponse,
	BaseHigherOrderResponse
} from '$types';
import { userService } from './user';

/**
 * Service for managing tabs associated with a user.
 * @param {string} id - The user ID.
 * @returns {Object} An object containing functions for interacting with tabs.
 */
export function tabService(id: string) {
	/**
	 * Retrieves a single tab by its ID.
	 * @param {string} tab_id - The ID of the tab.
	 * @returns {Object} An object containing the tab and any potential error.
	 */
	async function getTab(tab_id: string): Promise<BaseTabResponse> {
		try {
			const { userRef, err } = await userService(id).getUserInstance();
			if (err) {
				throw new Error(err.message);
			}

			const tabRef = doc(userRef, 'tabs', tab_id);
			const tabSnapshot = await getDoc(tabRef);
			if (!tabSnapshot.exists()) {
				await setDoc(tabRef, {});
			}

			const userSnapshot = await getDoc(userRef);
			const last_tab_id = userSnapshot.data()?.last_tab_id ?? '';

			return {
				tab: tabSnapshot.data() as Tab,
				last_tab_id,
				err: null
			};
		} catch (error) {
			console.error('getTab error: ', error);
			return {
				tab: {} as Tab,
				last_tab_id: '',
				err: error as Error
			};
		}
	}

	/**
	 * Retrieves all tabs associated with a user.
	 * @returns {Object} An object containing the tabs and any potential error.
	 */
	async function getTabs(): Promise<BaseTabsResponse> {
		try {
			const { userRef, err } = await userService(id).getUserInstance();
			if (err) {
				throw new Error(err.message);
			}

			const tabsRef = collection(userRef, 'tabs');
			const tabsSnapshot = await getDocs(tabsRef);
			const tabs: Tab[] = [];
			tabsSnapshot.forEach((tab) => {
				tabs.push(tab.data() as Tab);
			});

			const userSnapshot = await getDoc(userRef);
			const last_tab_id = userSnapshot.data()?.last_tab_id ?? '';

			return {
				tabs,
				last_tab_id,
				err: null
			};
		} catch (error) {
			console.error('getTabs error: ', error);
			return {
				tabs: [],
				last_tab_id: '',
				err: error as Error
			};
		}
	}

	/**
	 * Retrieves all tabs associated with a user and returns them in ascending order based on their "order" property.
	 * @returns {Object} An object containing the ordered tabs and any potential error.
	 */
	async function getOrderedTabs(): Promise<BaseTabsResponse> {
		try {
			const { tabs, last_tab_id, err } = await getTabs();
			if (err) {
				throw new Error(err.message);
			}

			return {
				tabs: tabs.sort((a, b) => (a.order ?? 0) - (b.order ?? 0)),
				last_tab_id,
				err: null
			};
		} catch (error) {
			console.error('getOrderedTabs error: ', error);
			return {
				tabs: [],
				last_tab_id: '',
				err: error as Error
			};
		}
	}

	/**
	 * Retrieves the highest order value among all tabs associated with a user.
	 * @returns {Object} An object containing the highest order value and any potential error.
	 */
	async function getHigherOrder(): Promise<BaseHigherOrderResponse> {
		try {
			const { tabs, err } = await getTabs();
			if (err) {
				throw new Error(err.message);
			}

			let higherOrder = 0;
			tabs.forEach((tab) => {
				if (tab.order && tab.order > higherOrder) {
					higherOrder = tab.order;
				}
			});

			return {
				higherOrder,
				err: null
			};
		} catch (error) {
			console.error('getHighOrder error: ', error);
			return {
				higherOrder: 0,
				err: error as Error
			};
		}
	}

	/**
	 * Reorders the tabs based on the provided order.
	 * @param {ReorderedTab[]} reorderedTabs - An array of objects containing the ID and new order of each tab.
	 * @returns {Promise<Error | null>} A promise that resolves to null if the reordering is successful, or an error if it fails.
	 */
	async function reorderTabs(reorderedTabs: ReorderedTab[]): Promise<Error | null> {
		try {
			const { userRef, err } = await userService(id).getUserInstance();
			if (err) {
				throw new Error(err.message);
			}

			const batch: Promise<void>[] = [];
			reorderedTabs.forEach((reorderedTab) => {
				const tabRef = doc(userRef, 'tabs', reorderedTab.id);
				batch.push(updateDoc(tabRef, { order: reorderedTab.order }));
			});

			await Promise.all(batch);
			return null;
		} catch (error) {
			console.error('reorderTabs error: ', error);
			return error as Error;
		}
	}

	/**
	 * Creates a new tab and associates it with a user.
	 * @param {string} tab_id - The ID of the new tab.
	 * @param {Tab} tab - The tab object containing the tab data.
	 * @returns {Promise<Error | null>} A promise that resolves to null if the creation is successful, or an error if it fails.
	 */
	async function createTab(tab_id: string, tab: Tab): Promise<Error | null> {
		try {
			const { userRef, err } = await userService(id).getUserInstance();
			if (err) {
				throw new Error(err.message);
			}

			const { higherOrder, err: error } = await getHigherOrder();
			if (error) {
				throw new Error(error.message);
			}

			const newTab = {
				...tab,
				order: higherOrder + 1
			};
			const tabRef = doc(userRef, 'tabs', tab_id);
			await setDoc(tabRef, newTab);

			return null;
		} catch (error) {
			console.error('createTab error: ', error);
			return error as Error;
		}
	}

	/**
	 * Updates a tab with new data.
	 * @param {string} tab_id - The ID of the tab to update.
	 * @param {Tab} tab - The updated tab object.
	 * @returns {Promise<Error | null>} A promise that resolves to null if the update is successful, or an error if it fails.
	 */
	async function updateTab(tab_id: string, tab: Tab): Promise<Error | null> {
		try {
			const { userRef, err } = await userService(id).getUserInstance();
			if (err) {
				throw new Error(err.message);
			}

			const tabRef = doc(userRef, 'tabs', tab_id);
			await updateDoc(tabRef, tab as { [x: string]: any });

			return null;
		} catch (error) {
			console.error('updateTab error: ', error);
			return error as Error;
		}
	}

	/**
	 * Removes a tab associated with a user.
	 * @param {string} tabId - The ID of the tab to remove.
	 * @returns {Promise<Error | null>} A promise that resolves to null if the removal is successful, or an error if it fails.
	 */
	async function removeTab(tabId: string): Promise<Error | null> {
		try {
			const { userRef, err } = await userService(id).getUserInstance();
			if (err) {
				throw new Error(err.message);
			}

			const tabRef = doc(userRef, 'tabs', tabId);
			await deleteDoc(tabRef);

			return null;
		} catch (error) {
			console.error('removeTab error: ', error);
			return error as Error;
		}
	}

	async function updateLastTabID(last_tab_id: string): Promise<Error | null> {
		try {
			const { userRef, err } = await userService(id).getUserInstance();
			if (err) {
				throw new Error(err.message);
			}

			await updateDoc(userRef, { last_tab_id });

			return null;
		} catch (error) {
			console.error('updateLastTabID error: ', error);
			return error as Error;
		}
	}

	return {
		getTabs,
		getOrderedTabs,
		getTab,
		createTab,
		updateTab,
		removeTab,
		getHigherOrder,
		reorderTabs,
		updateLastTabID
	};
}
