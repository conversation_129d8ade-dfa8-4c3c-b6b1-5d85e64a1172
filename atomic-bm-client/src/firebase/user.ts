import type { BaseUserResponse } from '$types';
import { db } from './firebase';
import {
	doc,
	getDoc,
	setDoc,
	DocumentReference,
	type DocumentData,
	DocumentSnapshot
} from 'firebase/firestore';

/**
 * Service for managing user data.
 * @param {string} id - The user ID.
 * @returns {Object} An object containing functions for interacting with user data.
 */
export function userService(id: string) {
	/**
	 * Retrieves the user instance from the database.
	 * If the user does not exist, it creates an empty document for the user.
	 * @returns A promise that resolves to the user reference, user document, and any error that occurred.
	 */
	async function getUserInstance(): Promise<BaseUserResponse> {
		try {
			const userRef = doc(db, 'users', id);
			const userDoc = await getDoc(userRef);
			if (!userDoc.exists()) {
				// if not exists, create a new user with a collection of tabs
				await setDoc(userRef, {});
			}

			return {
				userRef,
				userDoc,
				err: null
			};
		} catch (error) {
			console.error('getUserInstance error: ', error);
			return {
				userRef: null as unknown as DocumentReference<DocumentData, DocumentData>,
				userDoc: null as unknown as DocumentSnapshot<DocumentData, DocumentData>,
				err: error as Error
			};
		}
	}

	return {
		getUserInstance
	};
}
