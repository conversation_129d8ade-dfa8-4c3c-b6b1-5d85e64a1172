import { db } from './firebase';
import {
	doc,
	getDoc,
	setDoc,
	DocumentReference,
	type DocumentData,
	DocumentSnapshot
} from 'firebase/firestore';
import type { BaseNoteInstanceResponse, BaseNotesResponse, Note } from '$types';

/**
 * Service for managing notes associated with a bot.
 * @param {string} id - The bot ID.
 * @returns {Object} An object containing functions for interacting with notes.
 */
export function noteService(id: string) {
	/**
	 * Retrieves the instance of the notes document from the database.
	 * @returns {Promise<BaseNoteInstanceResponse>} A promise that resolves to the notes document instance.
	 */
	async function getNotesInstance(): Promise<BaseNoteInstanceResponse> {
		try {
			const notesRef = doc(db, 'notes', id);
			const notesDoc = await getDoc(notesRef);
			if (!notesDoc.exists()) {
				setDoc(notesRef, { notes: [] });
			}

			return {
				notesRef,
				notesDoc,
				err: null
			};
		} catch (error) {
			console.error('getNotesInstance error: ', error);
			return {
				notesRef: null as unknown as DocumentReference<DocumentData, DocumentData>,
				notesDoc: null as unknown as DocumentSnapshot<DocumentData, DocumentData>,
				err: error as Error
			};
		}
	}

	/**
	 * Retrieves the notes from the notes document in the database.
	 * @returns {Promise<BaseNotesResponse>} A promise that resolves to the notes and any potential error.
	 */
	async function getNotes(): Promise<BaseNotesResponse> {
		try {
			const { notesDoc, err } = await getNotesInstance();
			if (err) {
				throw new Error(err.message);
			}

			return {
				notes: notesDoc.data()?.notes as Note[],
				err: null
			};
		} catch (error) {
			console.error('getNotes error: ', error);
			return {
				notes: [] as Note[],
				err: error as Error
			};
		}
	}

	/**
	 * Creates a new note and adds it to the existing notes in the database.
	 *
	 * @param note - The note object to be created.
	 * @returns A Promise that resolves to `null` if the note is created successfully, or an Error object if there is an error.
	 */
	async function createNote(note: Note): Promise<Error | null> {
		try {
			const { notesDoc, err } = await getNotesInstance();
			if (err) {
				throw new Error(err.message);
			}

			const existingNotes = notesDoc.data()?.notes || [];
			const updatedNotes = [...existingNotes, note];
			await setDoc(notesDoc.ref, { notes: updatedNotes });

			return null;
		} catch (error) {
			console.error('createNote error: ', error);
			return error as Error;
		}
	}

	/**
	 * Deletes a note with the specified ID from the notes collection.
	 *
	 * @param id - The ID of the note to delete.
	 * @returns If an error occurs during the deletion, it returns an Error object. Otherwise, it returns null.
	 */
	async function deleteNote(id: string) {
		try {
			const { notesDoc, err } = await getNotesInstance();
			if (err) {
				throw new Error(err.message);
			}

			const existingNotes = notesDoc.data()?.notes || [];
			const noteToDelete = existingNotes.find((note: Note) => note.id === id);
			if (!noteToDelete) {
				throw new Error(`Note with ID ${id} not found.`);
			}

			const updatedNotes = existingNotes.filter((note: Note) => note.id !== id);
			await setDoc(notesDoc.ref, { notes: updatedNotes });

			return null;
		} catch (error) {
			console.error('deleteNote error: ', error);
			return error as Error;
		}
	}

	return {
		getNotes,
		createNote,
		deleteNote
	};
}
