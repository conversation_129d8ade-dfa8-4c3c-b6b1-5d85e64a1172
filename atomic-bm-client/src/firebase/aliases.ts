import { doc, setDoc, deleteDoc, collection, getDocs } from 'firebase/firestore';
import type { Tab, BaseAliasResponse } from '$types';
import { userService } from './user';

export function aliasService(id: string) {
	/**
	 * Retrieves all tabs associated with a user.
	 * @returns {Object} An object containing the tabs and any potential error.
	 */
	async function getAliases(): Promise<BaseAliasResponse> {
		try {
			const { userRef, err } = await userService(id).getUserInstance();
			if (err) {
				throw new Error(err.message);
			}

			const aliasRef = collection(userRef, 'alias');
			const aliasSnapshot = await getDocs(aliasRef);
			const aliases: any[] = [];
			aliasSnapshot.forEach((tab) => {
				aliases.push(tab.data() as Tab);
			});

			return {
				aliases,
				err: null
			};
		} catch (error) {
			console.error('getAliases error: ', error);
			return {
				aliases: [],
				err: error as Error
			};
		}
	}

	/**
	 * A function that creates a new alias.
	 *
	 * @param {any} newAlias - The new alias to be created.
	 * @return {Promise<Error | null>} A promise that resolves to null if the alias creation is successful, or an error if it fails.
	 */
	async function createAlias(newAlias: any): Promise<Error | null> {
		console.log("🚀 ~ createAlias ~ newAlias:", newAlias)
		try {
			const { userRef, err } = await userService(id).getUserInstance();
			if (err) {
				throw new Error(err.message);
			}

			const tabRef = doc(userRef, 'alias', newAlias.id);
			await setDoc(tabRef, newAlias);

			return null;
		} catch (error) {
			console.error('createAlias error: ', error);
			return error as Error;
		}
	}

	/**
	 * A function that removes an alias.
	 *
	 * @param {string} aliasID - The ID of the alias to be removed.
	 * @return {Promise<Error | null>} A promise that resolves to null if the removal is successful, or an error if it fails.
	 */
	async function removeAlias(aliasID: string): Promise<Error | null> {
		try {
			const { userRef, err } = await userService(id).getUserInstance();
			if (err) {
				throw new Error(err.message);
			}

			const tabRef = doc(userRef, 'alias', aliasID);
			await deleteDoc(tabRef);

			return null;
		} catch (error) {
			console.error('removeAlias error: ', error);
			return error as Error;
		}
	}

	return {
		getAliases,
		createAlias,
		removeAlias
	};
}
