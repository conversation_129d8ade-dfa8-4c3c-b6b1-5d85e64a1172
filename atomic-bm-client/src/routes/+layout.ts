import { getToken } from '$stores/auth-store';
import { setProfile } from '$stores/profile-store';
import { browser } from '$app/environment';
import type { LayoutLoad } from './$types';
import { handleErr } from '$utils';

export const load = (async () => {
	try {
		if (browser) {
			const token = getToken();
			if (token) {
				await setProfile();
			}
		}
	} catch (e) {
		handleErr(e);
	}
}) satisfies LayoutLoad;
