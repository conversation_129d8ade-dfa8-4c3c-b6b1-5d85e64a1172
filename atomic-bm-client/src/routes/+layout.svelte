<!-- @migration task: review uses of `navigating` -->
<script lang="ts">
	import { Toaster } from 'svelte-french-toast';
	import 'iconify-icon';
	import '../app.css';
	import { browser } from '$app/environment';
	import { QueryClient, QueryClientProvider } from '@tanstack/svelte-query';
	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	const queryClient = new QueryClient({
		defaultOptions: {
			queries: {
				enabled: browser
			}
		}
	});
</script>

<QueryClientProvider client={queryClient}>
	<main
		class="animate-bg-move-effect bg-dark-6 bg-stars max-[1600px]:animate-bg-move-effect-longer h-svh w-full overflow-x-hidden bg-[50%] bg-no-repeat text-white"
	>
		<Toaster />
		<!-- TODO: Fix navigation line animation. -->
		<!-- {#if navigating}
			<div class="fixed top-0 w-full bg-gray-100">
				<LineLoading />
			</div>
		{/if} -->
		{@render children?.()}
	</main>
</QueryClientProvider>
