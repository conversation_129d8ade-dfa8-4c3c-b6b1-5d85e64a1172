import { json, error } from '@sveltejs/kit';
import { noteService } from '$firebase/notes.js';

export async function GET({ params }) {
	try {
		const { notes, err } = await noteService(params.bot_id).getNotes();
		if (err) {
			throw new Error(err.message);
		}

		return json(notes);
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}

export async function POST({ request, params }) {
	try {
		const { id, created_at, created_user_id, note, priority, title, snapshot } =
			await request.json();
		const res = await noteService(params.bot_id).createNote({
			id,
			note,
			priority,
			created_at,
			created_user_id,
			title,
			snapshot: snapshot || ''
		});
		if (res !== null) {
			throw new Error(res.message);
		}

		return json(res);
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}

export async function DELETE({ params, url }) {
	try {
		const res = await noteService(params.bot_id).deleteNote(
			url.searchParams.get('note_id') as string
		);
		if (res !== null) {
			throw new Error(res.message);
		}

		return json(res);
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}
