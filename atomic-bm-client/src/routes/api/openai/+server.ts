import { json } from '@sveltejs/kit';
import { OPENAI_APIKEY } from '$env/static/private';
import { generateAICommandSuggestion } from '$api/openai';

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
	try {
		const body = await request.json();
		const result = await generateAICommandSuggestion(body.question, OPENAI_APIKEY);
		return json(result);
	} catch (error) {
		console.error('Error:', error);
	}
}
