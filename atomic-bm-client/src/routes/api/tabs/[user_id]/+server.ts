import { json, error } from '@sveltejs/kit';
import { tabService } from '$firebase/tabs.js';

export async function GET({ params }) {
	try {
		const { tabs, last_tab_id, err } = await tabService(params.user_id).getOrderedTabs();
		if (err) {
			throw new Error(err.message);
		}

		return json({
			tabs,
			last_tab_id
		});
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}

export async function POST({ request, params, url }) {
	try {
		const { id, name, path } = await request.json();
		const res = await tabService(params.user_id).createTab(
			url.searchParams.get('tab_id') as string,
			{
				id,
				name,
				path
			}
		);
		if (res !== null) {
			throw new Error(res.message);
		}

		return json(res);
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}

export async function PUT({ request, params }) {
	try {
		const { order } = await request.json();
		const res = await tabService(params.user_id).reorderTabs(order);
		if (res !== null) {
			throw new Error(res.message);
		}

		return json(res);
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}

export async function PATCH({ request, params, url }) {
	try {
		const tabID = url.searchParams.get('tab_id');
		if (tabID) {
			const { name, path } = await request.json();
			const res = await tabService(params.user_id).updateTab(
				url.searchParams.get('tab_id') as string,
				{
					name,
					path
				}
			);
			if (res !== null) {
				throw new Error(res.message);
			}
			
			return json(res);
		} else {
			const res = await tabService(params.user_id).updateLastTabID(
				url.searchParams.get('last_tab_id') as string
			);
			if (res !== null) {
				throw new Error(res.message);
			}

			return json(res);
		}
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}

export async function DELETE({ params, url }) {
	try {
		const res = await tabService(params.user_id).removeTab(
			url.searchParams.get('tab_id') as string
		);
		if (res !== null) {
			throw new Error(res.message);
		}

		return json(res);
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}
