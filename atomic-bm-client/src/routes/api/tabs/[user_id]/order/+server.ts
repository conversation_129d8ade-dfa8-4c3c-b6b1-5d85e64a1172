import { json, error } from '@sveltejs/kit';
import { tabService } from '$firebase/tabs.js';

export async function POST({ request, params }) {
	try {
		const { newOrder } = await request.json();
		const res = await tabService(params.user_id).reorderTabs(newOrder);
		if (res !== null) {
			throw new Error(res.message);
		}

		return json(res);
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}
