import ImageKit from 'imagekit';
import { json, error } from '@sveltejs/kit';

const imagekit = new ImageKit({
	publicKey: 'public_ZSZb0FaoqGa3GN/Ur3Em0c+76sQ=',
	privateKey: 'private_Ot3H907g7pGYBFJryS1Vc4cEHVo=',
	urlEndpoint: 'https://ik.imagekit.io/97q72hphb/'
});

export async function POST({ request, url }) {
	try {
		const res = await request.blob();
		const buffer = await res.arrayBuffer();
		const fileBuffer = Buffer.from(buffer);
		const response = await imagekit.upload({
			file: fileBuffer,
			fileName: `orderbook-snapshow-${url.searchParams.get('createdAt')}`
		});
		return json(response);
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}
