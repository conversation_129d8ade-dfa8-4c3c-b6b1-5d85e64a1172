import { json, error } from '@sveltejs/kit';
import { aliasService } from '$firebase/aliases.js';

export async function GET({ params }) {
	try {
		const { aliases, err } = await aliasService(params.user_id).getAliases();
		if (err) {
			throw new Error(err.message);
		}

		return json({
			aliases
		});
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}

export async function POST({ request, params }) {
	try {
		const { id, alias, command, createdAt } = await request.json();
		const res = await aliasService(params.user_id).createAlias({
			id,
			alias,
			command,
			createdAt
		});
		if (res !== null) {
			throw new Error(res.message);
		}

		return json(res);
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}

export async function DELETE({ params, url }) {
	try {
		const res = await aliasService(params.user_id).removeAlias(
			url.searchParams.get('alias_id') as string
		);
		if (res !== null) {
			throw new Error(res.message);
		}

		return json(res);
	} catch (err) {
		const errorBody = err as Error;
		return error(400, {
			message: errorBody.message
		});
	}
}
