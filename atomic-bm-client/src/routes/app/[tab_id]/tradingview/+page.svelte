<script lang="ts">
	import { run } from 'svelte/legacy';

	// @ts-nocheck
	import { onMount, onDestroy } from 'svelte';
	import { appStore } from '$stores/app-store';

	let { options = Object } = $props();
	const SCRIPT_ID = 'tradingview-widget';
	let CONTAINER_ID = $state('');

	let exist = false;


	onMount(() => {
		$appStore.currentPageTitle = 'TradingView';

		CONTAINER_ID = options && options.container_id ? options.container_id : 'svelte-widget-trading';
		appendScript(initWidget);
	});

	onDestroy(() => {
		const script = document.getElementById(SCRIPT_ID);
		if (script) {
			script.remove();
		}
	});

	function initWidget() {
		if (typeof TradingView !== 'undefined') {
			new window.TradingView.widget(
				Object.assign(
					{ container_id: CONTAINER_ID },
					{
						autosize: true,
						symbol: 'NASDAQ:AAPL',
						timezone: 'America/Sao_Paulo',
						theme: 'dark',
						style: '1',
						locale: 'en',
						gridColor: 'rgba(0, 0, 0, 0.06)',
						withdateranges: true,
						range: 'YTD',
						hide_side_toolbar: false,
						allow_symbol_change: true,
						// watchlist: ['BINANCE:BTCUSDT'],
						details: true,
						hotlist: true,
						calendar: false,
						support_host: 'https://www.tradingview.com'
					}
				)
			);
		}
	}

	function appendScript(onload) {
		if (document.getElementById(SCRIPT_ID) === null) {
			const script = document.createElement('script');
			script.id = SCRIPT_ID;
			script.type = 'text/javascript';
			script.async = true;
			script.src = 'https://s3.tradingview.com/tv.js';
			script.onload = onload;
			document.getElementsByTagName('head')[0].appendChild(script);
		} else {
			const script = document.getElementById(SCRIPT_ID);
			const oldOnload = script.onload;
			return (script.onload = () => {
				oldOnload();
				onload();
			});
		}
	}
	run(() => {
		if (exist) {
			initWidget();
		}
	});
</script>

<svelte:head>
	<title>Atomic Fund | TradingView</title>
</svelte:head>

<section class="w-full h-full p-4">
	<div id={CONTAINER_ID} class="h-[95%] w-full"></div>
</section>
