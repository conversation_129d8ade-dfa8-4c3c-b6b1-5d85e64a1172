<script lang="ts">
	import { preventDefault } from 'svelte/legacy';

	import BaseLoading from '$components/base-loading.svelte';
	import Button from '$components/ui/button.svelte';
	import Select from '$components/ui/select.svelte';
	import BaseModal from '$components/base-modal.svelte';
	import Input from '$components/ui/input.svelte';
	import * as Card from '$components/ui/card';
	import type { Note } from '$types';
	import { page } from '$app/stores';
	import { formatCommonDate, getPersistData, persistData, tabNavigate, toast } from '$utils';
	import { profile } from '$stores/profile-store';
	import { appStore } from '$stores/app-store';
	import { onMount } from 'svelte';
	import { notesClient } from '$api/notes';
	import Icon from '$components/ui/icon.svelte';
	import ConfirmModal from '$components/confirm-modal.svelte';

	let { data } = $props();

	let displayUser = $derived((id: string) => {
		const user = data?.users?.find((u: any) => u.id === id);
		if (user) {
			return user.name;
		} else {
			return 'Not found';
		}
	});

	let displayPriority = $derived((priority: string) => {
		if (priority === 'high') {
			return 'flat-color-icons:high-priority';
		} else if (priority === 'medium') {
			return 'flat-color-icons:medium-priority';
		} else {
			return 'flat-color-icons:low-priority';
		}
	});

	let checkingNote = $state(false);
	let selectedNote: Note = $state({
		title: '',
		priority: '',
		created_at: '',
		id: '',
		created_user_id: '',
		note: ''
	});
	let creatingNote = $state(false);
	let deletetingNote = $state(false);
	let deletetingNoteId = $state('');
	let loading = $state(true);
	let notes: Note[] = $state([]);
	let note_title = $state('');
	let note_content = $state('');
	let note_priority = $state({
		label: '',
		value: ''
	});
	const priorityOptions = [
		{
			label: 'Low',
			value: 'low'
		},
		{
			label: 'Medium',
			value: 'medium'
		},
		{
			label: 'High',
			value: 'high'
		}
	];

	async function loadBotNotes() {
		try {
			const { notes: data, err } = await notesClient().fetchNotes($page.params.bot_id);
			if (err) {
				throw new Error(err.message);
			}

			notes = data;
		} catch (error) {
			console.error(error);
		} finally {
			loading = false;
		}
	}

	async function confirmCreateNote() {
		try {
			if (note_priority.value === '' || note_priority.label === '') {
				toast('Set a priority for the note', 'error');
				return;
			}

			const newNote: Note = {
				id: window.crypto.randomUUID(),
				created_user_id: $profile.id,
				title: note_title,
				note: note_content,
				priority: note_priority.value as Note['priority'],
				created_at: new Date().toLocaleString()
			};

			const res = await notesClient().createNote($page.params.bot_id, newNote);
			if (res !== null) {
				throw new Error(res.message);
			}

			creatingNote = false;
			note_content = '';
			note_title = '';
			note_priority = { label: '', value: '' };

			toast('Note created', 'success');
			loadBotNotes();
		} catch (error) {
			console.error(error);
		}
	}

	async function confirmDeleteNote() {
		try {
			const res = await notesClient().deleteNote($page.params.bot_id, deletetingNoteId);
			if (res !== null) {
				throw new Error(res.message);
			}

			deletetingNote = false;
			toast('Note deleted', 'success');
			loadBotNotes();
		} catch (error) {
			console.error(error);
		}
	}

	onMount(() => {
		$appStore.currentPageTitle = 'Bot Notes';
		loadBotNotes();
	});
</script>

<svelte:head>
	<title>Bot notes | Atomic Bot Manager</title>
</svelte:head>

<section class="h-full">
	{#if loading}
		<div class="flex items-center justify-center h-full">
			<BaseLoading title="Loading notes..." />
		</div>
	{:else if !notes.length}
		<div class="flex flex-col items-center justify-center h-full gap-2">
			<div class="text-center">
				<h1 class="text-2xl font-semibold">Notes not found</h1>
				<p>You can create notes to store information about the bot.</p>
			</div>
			<Button on:click={() => (creatingNote = true)}>Create first note</Button>
		</div>
	{:else}
		<div>
			<h1 class="font-semibold">Bot Notes</h1>
			<p class="text-xs text-gray-400">
				You can store relevant information about the bot by creating a note.
			</p>
			<Button class="mt-2" on:click={() => (creatingNote = true)}>Create note</Button>
		</div>
		<div class="flex flex-wrap gap-2 mt-3">
			{#each notes as item}
				<Card.Main freeSize class="max-w-[420px]">
					<Card.Header empty class="flex items-center justify-between px-3 py-1">
						<h1 class="max-w-[410px] truncate font-semibold">{item.title}</h1>
						<div class="flex">
							<Icon
								icon="majesticons:open"
								on:click={() => {
									checkingNote = true;
									selectedNote = item;
								}}
								btn
							/>
							<Icon
								icon="uil:trash"
								on:click={() => {
									deletetingNote = true;
									deletetingNoteId = item.id;
								}}
								btn
							/>
						</div>
					</Card.Header>
					<Card.Body class="pt-1 mt-0 text-sm">
						<div class="relative h-[100px] space-y-2 overflow-hidden break-all">
							<p>
								{item.note}
							</p>
							{#if item?.snapshot}
								<img src={item?.snapshot} alt="snapshot-list" />
							{/if}
						</div>
					</Card.Body>
					<Card.Footer empty class="flex justify-between text-[0.7rem] text-gray-200">
						<Icon
							icon={displayPriority(item.priority)}
							class="text-lg"
							title="Priority: {item.priority}"
						/>
						<h2>
							<span class="capitalize">{displayUser(item.created_user_id)}</span>, at: {item.created_at}
						</h2>
					</Card.Footer>
				</Card.Main>
			{/each}
		</div>
	{/if}
</section>

<BaseModal
	open={creatingNote}
	on:openChange={(e) => (creatingNote = e.detail)}
	title="New bot note"
	contentClass="max-w-[460px]"
	transitionDuration={0}
>
	<p class="text-xs text-gray-400">
		You can store relevant information about the bot by creating a note.
	</p>
	<form class="mt-3 space-y-3" onsubmit={preventDefault(confirmCreateNote)}>
		<Input
			required
			label="Note title"
			description="The title of the note"
			placeholder="ex: My note"
			wrapperClass="mt-2"
			bind:value={note_title}
		/>
		<Input
			type="textarea"
			label="Note content"
			description="The content of the note"
			placeholder="ex: This is my note"
			bind:value={note_content}
		/>
		<Select
			defaultSelected={note_priority}
			options={priorityOptions}
			label="Priority"
			placeholder="Select the priority of the note"
			on:selected={(e) => {
				note_priority = e.detail;
			}}
		/>
		<div class="flex justify-end gap-2 mt-5">
			<Button>Cancel</Button>
			<Button type="submit">Create</Button>
		</div>
	</form>
</BaseModal>

<BaseModal
	open={checkingNote}
	on:openChange={(e) => (checkingNote = e.detail)}
	title={selectedNote.title}
	contentClass="max-w-fit"
	transitionDuration={0}
>
	<div class="max-h-[400px] space-y-2 overflow-y-auto break-all pr-2 text-sm text-gray-300">
		<p>
			{selectedNote.note}
		</p>
		{#if selectedNote?.snapshot}
			<img src={selectedNote?.snapshot} alt="snapshot-selected" />
		{/if}
	</div>
	<div class="flex justify-between mt-5">
		<Icon
			icon={displayPriority(selectedNote.priority)}
			class="text-lg"
			title="Priority: {selectedNote.priority}"
		/>
		<h2 class="text-xs">
			<span class="capitalize">{displayUser(selectedNote.created_user_id)}</span>, at: {selectedNote.created_at}
		</h2>
	</div>
</BaseModal>

<ConfirmModal
	open={deletetingNote}
	title={selectedNote.title}
	description="Are you sure you want to delete this note? This action cannot be undone."
	on:openChange={(e) => (deletetingNote = e.detail)}
	on:cancel={() => (deletetingNote = false)}
	on:confirm={confirmDeleteNote}
/>
