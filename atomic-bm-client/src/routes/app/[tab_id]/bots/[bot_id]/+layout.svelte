<script lang="ts">
	import Button from '$lib/components/ui/button.svelte';
	import Icon from '$lib/components/ui/icon.svelte';
	import ConfirmModal from '$lib/components/confirm-modal.svelte';
	import { writable, type Writable } from 'svelte/store';
	import { page } from '$app/state';
	import { botOptions, appStore } from '$stores/app-store';
	import { getStorage, handleErr, tabNavigate, toast } from '$utils';
	import { deleteBotsBotId } from '$client/bot';
	import { setContext, getContext } from 'svelte';
	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	interface PageContext {
		creatingBotSnapshotNote: Writable<boolean>;
	}

	setContext<PageContext>('pageContext', {
		creatingBotSnapshotNote: writable<boolean>(false)
	});

	const { creatingBotSnapshotNote } = getContext<PageContext>('pageContext');

	let deletingBot = $state(false);

	async function confirmDeleteBot() {
		try {
			await deleteBotsBotId(page.params.bot_id as string, {
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			});

			deletingBot = false;
			toast('Bot deleted', 'success');
			tabNavigate('Bots', '/bots');
		} catch (error) {
			handleErr(error);
		}
	}
</script>

<main class="flex h-full w-full flex-col">
	<div class="flex justify-between border-b border-b-gray-900">
		<div class="flex">
			{#each $botOptions as setting}
				<Button
					class="w-[120px] rounded-none border-b-0 border-l-0 border-r-0 border-t-0 bg-inherit text-gray-500 {$appStore.currentPageTitle ===
						setting.tabName && 'border-b !border-b-gray-300 text-gray-100'}"
					icon={setting.icon}
					iconClass="text-gray-500 {$appStore.currentPageTitle === setting.tabName
						? 'text-gray-100'
						: ''}"
					on:click={() =>
						tabNavigate('', `/bots/${page.params.bot_id}${setting.path}`, false, true)}
				>
					{setting.name}
				</Button>
			{/each}
		</div>
		<div class="flex items-center">
			{#if page.route.id === '/app/[tab_id]/bots/[bot_id]'}
				<Icon
					btn
					icon="material-symbols:heap-snapshot-multiple-outline"
					class="p-2"
					on:click={() => ($creatingBotSnapshotNote = true)}
				/>
			{/if}
			<Icon
				btn
				icon="uil:edit"
				class="p-2"
				on:click={() =>
					tabNavigate('', `/bots/${page.params.bot_id}/edit?keepBot=true`, false, true)}
			/>
			<Icon btn icon="uil:trash" class="p-2" on:click={() => (deletingBot = true)} />
			<Icon
				btn
				icon="majesticons:arrow-left"
				class="p-2"
				on:click={() => tabNavigate('Bots', '/bots')}
			/>
		</div>
	</div>
	<div class="flex-1 px-4 py-2">
		{@render children?.()}
	</div>
</main>

<ConfirmModal
	open={deletingBot}
	title="Delete bot"
	description="Are you sure you want to delete this bot? This action cannot be undone."
	on:openChange={(e) => (deletingBot = e.detail)}
	on:cancel={() => (deletingBot = false)}
	on:confirm={confirmDeleteBot}
/>
