import type { LayoutLoad } from '../[bot_id]/$types';
import { getBotsBotId } from '$client/bot';
import { getStorage, handleErr } from '$utils';
import { getAccounts } from '$client/account';

export const load = (async ({ params, depends }) => {
	try {
		depends('reloadbot:reloadbot');
		const bot = await getBotsBotId(params.bot_id, {
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		});
		const accounts = await getAccounts(undefined, {
			headers: { Authorization: `Bearer ${getStorage('token')}` }
		});

		return { bot, accounts };
	} catch (error) {
		handleErr(error);
	}
}) satisfies LayoutLoad;
