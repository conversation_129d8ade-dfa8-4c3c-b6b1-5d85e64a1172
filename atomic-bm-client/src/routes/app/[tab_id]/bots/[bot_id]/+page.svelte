<script lang="ts">
	import { run, preventDefault } from 'svelte/legacy';

	import BaseLoading from '$lib/components/base-loading.svelte';
	import Input from '$lib/components/ui/input.svelte';
	import Select from '$lib/components/ui/select.svelte';
	import BaseModal from '$lib/components/base-modal.svelte';
	import Button from '$lib/components/ui/button.svelte';
	import * as Card from '$lib/components/ui/card';
	import { appStore } from '$stores/app-store';
	import { onMount } from 'svelte';
	import { toPng } from 'html-to-image';
	import { nodeURLToCommonBlob, toast, uploadSnapshot } from '$utils';
	import { profile } from '$stores/profile-store';
	import type { Note } from '$types';
	import { notesClient } from '$api/notes';
	import { page } from '$app/stores';
	import { getContext } from 'svelte';
	import type { Writable } from 'svelte/store';

	let temporaryImage = $state('');
	let loading = $state(false);
	let note_title = $state('');
	let note_content = $state('');
	let note_priority = $state({
		label: '',
		value: ''
	});

	const priorityOptions = [
		{
			label: 'Low',
			value: 'low'
		},
		{
			label: 'Medium',
			value: 'medium'
		},
		{
			label: 'High',
			value: 'high'
		}
	];

	interface PageContext {
		creatingBotSnapshotNote: Writable<boolean>;
	}

	const { creatingBotSnapshotNote } = getContext<PageContext>('pageContext');

	async function createTemporaryImage() {
		temporaryImage = await toPng(document.getElementById('bot_data') as HTMLDivElement, {
			width: 1380,
			height: 720
		});
	}

	run(() => {
		if ($creatingBotSnapshotNote === true) {
			createTemporaryImage();
		}
	});

	async function confirmSnapshotUpload() {
		const { result, error } = await uploadSnapshot(
			nodeURLToCommonBlob(temporaryImage),
			new Date().getTime().toString()
		);

		if (error) {
			toast(error as string, 'error');
		} else if (result) {
			return result.url;
		}
	}

	async function confirmCreateNote() {
		loading = true;
		if (note_priority.value === '' || note_priority.label === '') {
			toast('Set a priority for the note', 'error');
			loading = false;
			return;
		}

		const newNote: Note = {
			id: window.crypto.randomUUID(),
			snapshot: await confirmSnapshotUpload(),
			created_user_id: $profile.id,
			title: note_title,
			note: note_content || '',
			priority: note_priority.value as Note['priority'],
			created_at: new Date().toLocaleString()
		};

		const res = await notesClient().createNote($page.params.bot_id as string, newNote);
		if (res !== null) {
			toast(res.message, 'error');
			loading = false;
			return;
		}

		$creatingBotSnapshotNote = false;
		note_content = '';
		note_title = '';
		note_priority = { label: '', value: '' };

		toast('Note created', 'success');
		loading = false;
	}

	onMount(() => {
		$appStore.currentPageTitle = 'Bot data';
	});
</script>

<svelte:head>
	<title>Bot data | Atomic Bot Manager</title>
</svelte:head>

<section class="mt-2 flex flex-wrap gap-2" id="bot_data">
	<Card.Main freeSize class="max-w-[450px]">
		<Card.Header title="Order book" empty class="flex items-center justify-between py-1">
			<h1 class="text-xs font-semibold">Order book</h1>
		</Card.Header>
		<Card.Body class="mt-1 space-y-2 px-1.5 pb-3 pt-1">
			<div class="overflow-x-auto">
				<table class="min-w-full">
					<thead>
						<tr>
							<th class="px-1.5 text-left text-xs">Ref Spread</th>
							<th class="px-1.5 text-left text-xs">Top Spread</th>
							<th class="px-1.5 text-left text-xs">Price</th>
							<th class="px-1.5 text-left text-xs">Amount</th>
							<th class="px-1.5 text-left text-xs">Total</th>
						</tr>
					</thead>
					<tbody class="text-green-600">
						<tr>
							<td class="px-1.5 text-xs">5.00</td>
							<td class="px-1.5 text-xs">51.03</td>
							<td class="px-1.5 text-xs">54.48</td>
							<td class="px-1.5 text-xs">6.89</td>
							<td class="px-1.5 text-xs">6.89</td>
						</tr>
						<tr>
							<td class="px-1.5 text-xs">5.00</td>
							<td class="px-1.5 text-xs">51.03</td>
							<td class="px-1.5 text-xs">54.48</td>
							<td class="px-1.5 text-xs">6.89</td>
							<td class="px-1.5 text-xs">6.89</td>
						</tr>
					</tbody>
					<tbody class="text-red-600">
						<tr>
							<td class="px-1.5 text-xs">5.00</td>
							<td class="px-1.5 text-xs">51.03</td>
							<td class="px-1.5 text-xs">54.48</td>
							<td class="px-1.5 text-xs">6.89</td>
							<td class="px-1.5 text-xs">6.89</td>
						</tr>
						<tr>
							<td class="px-1.5 text-xs">5.00</td>
							<td class="px-1.5 text-xs">51.03</td>
							<td class="px-1.5 text-xs">54.48</td>
							<td class="px-1.5 text-xs">6.89</td>
							<td class="px-1.5 text-xs">6.89</td>
						</tr>
					</tbody>
				</table>
			</div>
		</Card.Body>
	</Card.Main>
</section>

<BaseModal
	open={$creatingBotSnapshotNote}
	on:openChange={(e) => ($creatingBotSnapshotNote = e.detail)}
	title="New snapshot note"
	contentClass="max-w-fit"
	transitionDuration={0}
>
	{#if !loading}
		<p class="text-xs text-gray-400">
			You can store orderbook snapshots as notes for future reference.
		</p>
		<div class="h-[100px] w-full overflow-hidden">
			<img src={temporaryImage} alt="temporary" />
		</div>
		<form class="mt-3 space-y-3" onsubmit={preventDefault(confirmCreateNote)}>
			<Input
				required
				label="Note title"
				description="The title of the note"
				placeholder="ex: My note"
				wrapperClass="mt-2"
				bind:value={note_title}
			/>
			<Input
				type="textarea"
				label="Note content"
				description="The content of the note"
				placeholder="ex: This is my note"
				bind:value={note_content}
			/>
			<Select
				defaultSelected={note_priority}
				options={priorityOptions}
				label="Priority"
				placeholder="Select the priority of the note"
				on:selected={(e) => {
					note_priority = e.detail;
				}}
			/>
			<div class="mt-5 flex justify-end gap-2">
				<Button on:click={() => ($creatingBotSnapshotNote = false)}>Cancel</Button>
				<Button type="submit">Create</Button>
			</div>
		</form>
	{:else}
		<BaseLoading title="Creating note..." />
	{/if}
</BaseModal>
