<script lang="ts">
	import { run, createBubbler, stopPropagation } from 'svelte/legacy';

	const bubble = createBubbler();
	import {
		getStorage,
		clearApp,
		tabNavigate,
		getPersistData,
		persistData,
		clearPersistData,
		checkObjectKeys,
		formatCommonDate,
		toast,
		handleErr
	} from '$utils';
	import type { Bot } from '$client/api.schemas.ts';
	import { getAccountsAccountId } from '$client/account';
	import { appStore } from '$stores/app-store';
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { browser } from '$app/environment';
	import { createGetBots, deleteBotsBotId } from '$client/bot';
	import type { GetBotsParams } from '$client/api.schemas';
	import BaseLoading from '$lib/components/base-loading.svelte';
	import Input from '$lib/components/ui/input.svelte';
	import Button from '$lib/components/ui/button.svelte';
	import BaseFilters from '$lib/components/base-filters.svelte';
	import Popover from '$lib/components/ui/popover.svelte';
	import Icon from '$lib/components/ui/icon.svelte';
	import ConfirmModal from '$lib/components/confirm-modal.svelte';
	import * as Card from '$lib/components/ui/card';

	let searchBots = $state('');
	let deletingBot = $state(false);
	let selectedBotID = $state('');
	let filters: GetBotsParams = $state({
		accountId: undefined,
		symbol: undefined,
		region: undefined,
		status: undefined,
		isDeleted: undefined,
		orderBy: undefined,
		orderDirection: undefined,
		limit: undefined,
		offset: undefined
	});

	run(() => {
		if (browser && $page) {
			searchBots = getPersistData('searchbots');
			filters.limit = getPersistData('limitbots');
			filters.orderDirection = getPersistData('directionbots');
			filters.orderBy = getPersistData('orderbybots');
			filters.isDeleted = getPersistData('isdeletedbots');
			filters.status = getPersistData('statusbots');
			filters.symbol = getPersistData('symbolbots');
			filters.accountId = getPersistData('accountidbots');
		}
	});

	let bots = $derived(
		createGetBots(
			{
				isDeleted: filters.isDeleted,
				accountId: filters.accountId,
				symbol: filters.symbol,
				status: filters.status,
				orderBy: filters.orderBy,
				orderDirection: filters.orderDirection,
				limit: filters.limit,
				offset: filters.offset
			},
			{
				axios: {
					headers: {
						Authorization: `Bearer ${getStorage('token')}`
					}
				}
			}
		)
	);

	let filteredBots = $derived(
		$bots.data?.data?.data?.filter((bot) => {
			if (searchBots) {
				return (
					bot?.symbol?.toLowerCase().includes(searchBots?.toLowerCase()) ||
					bot?.accountId?.toLowerCase().includes(searchBots?.toLowerCase()) ||
					bot?.status?.toLowerCase().includes(searchBots?.toLowerCase()) ||
					bot?.tag?.toLowerCase().includes(searchBots?.toLowerCase())
				);
			} else {
				return bot;
			}
		})
	);
	run(() => {
		$bots.isError && $bots.error.status === 403 && clearApp();
	});
	run(() => {
		$bots.failureReason?.response?.status === 403 && clearApp();
	});

	async function openBotDetails(bots: Bot, newTab?: boolean, isEditing?: boolean) {
		const account = await getAccountsAccountId(bots.accountId, {
			headers: { Authorization: `Bearer ${getStorage('token')}` }
		});
		tabNavigate(
			`${account.data.data.exchangeId} / --missingRegionHere`,
			`/bots/${bots.id}${isEditing ? '/edit' : ''}`,
			newTab
		);
	}

	async function confirmDeleteBot() {
		try {
			await deleteBotsBotId(selectedBotID, {
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			});

			toast('Bot deleted', 'success');
			deletingBot = false;
			$bots.refetch();
		} catch (error) {
			handleErr(error);
		}
	}

	onMount(async () => {
		$appStore.currentPageTitle = 'Bots';
	});
</script>

<svelte:head>
	<title>Bots | Atomic Bot Manager</title>
</svelte:head>

<section class="flex h-full flex-col overflow-y-auto p-1.5">
	<div>
		<div class="flex justify-between">
			<div class="flex flex-1 flex-wrap items-end gap-2">
				<div>
					<Input
						id="bot-search"
						placeholder="Search for by region, symbol, accountId, status, tag"
						on:input={async (e) => {
							searchBots = await persistData(e, 'searchbots');
						}}
						value={searchBots}
					/>
				</div>
				<BaseFilters
					pageLimit={filters.limit}
					directionOption={filters.orderDirection}
					botsOrderBy={filters.orderBy}
					activeState={filters.isDeleted}
					accountId={filters.accountId}
					symbol={filters.symbol}
					status={filters.status}
					on:pageLimit={async (e) => {
						await persistData(undefined, 'limitbots', true, e.detail);
						filters.limit = e.detail;
					}}
					on:direction={async (e) => {
						await persistData(undefined, 'directionbots', true, e.detail);
						filters.orderDirection = e.detail;
					}}
					on:botsOrderBy={async (e) => {
						await persistData(undefined, 'orderbybots', true, e.detail);
						filters.orderBy = e.detail;
					}}
					on:activeState={async (e) => {
						await persistData(undefined, 'isdeletedbots', true, e.detail);
						filters.isDeleted = e.detail;
					}}
					on:filterOptions={async (e) => {
						e.detail.accountId &&
							(await persistData(undefined, 'accountidbots', true, e.detail.accountId));
						e.detail.symbol && (await persistData(undefined, 'symbolbots', true, e.detail.symbol));
						e.detail.status && (await persistData(undefined, 'statusbots', true, e.detail.status));
						filters.accountId = e.detail.accountId;
						filters.symbol = e.detail.symbol;
						filters.status = e.detail.status;
					}}
					on:refresh={() => $bots.refetch()}
				/>
			</div>
			<div>
				<Button on:click={() => tabNavigate('New bot', '/bots/create')}>New bot</Button>
			</div>
		</div>
		{#if checkObjectKeys(filters)}
			<div class="mt-1.5 flex max-w-fit items-center gap-2 px-1">
				<h2 class="text-[0.65rem] font-semibold">Active filters:</h2>
				<div class="flex gap-1">
					{#if filters.accountId}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.accountId = undefined;
								clearPersistData('accountidbots');
							}}>Account</Button
						>
					{/if}
					{#if filters.symbol}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.symbol = undefined;
								clearPersistData('symbolbots');
							}}>Symbol</Button
						>
					{/if}
					{#if filters.status}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.status = undefined;
								clearPersistData('statusbots');
							}}>Status</Button
						>
					{/if}
					{#if filters.isDeleted}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.isDeleted = undefined;
								clearPersistData('isdeletedbots');
							}}
							>{#if filters.isDeleted}Deleted{:else}Active{/if}</Button
						>
					{/if}
					{#if filters.orderBy}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.orderBy = undefined;
								clearPersistData('orderbybots');
							}}>Order by: <strong>{filters.orderBy}</strong></Button
						>
					{/if}
					{#if filters.orderDirection}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.orderDirection = undefined;
								clearPersistData('orderbots');
							}}>Order direction: <strong>{filters.orderDirection}</strong></Button
						>
					{/if}
					{#if filters.limit}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={() => {
								filters.limit = undefined;
								clearPersistData('limitbots');
							}}>Limit: <strong>{filters.limit}</strong></Button
						>
					{/if}
				</div>
			</div>
		{/if}
	</div>
	<div class="mt-2 flex-1 overflow-y-auto">
		{#if $bots.isLoading}
			<div class="flex h-full items-center justify-center">
				<BaseLoading title="Loading bots..." />
			</div>
		{:else if $bots.isError}
			<div class="flex h-full flex-col items-center justify-center">
				<h1 class="text-2xl font-semibold">Failed to load bots</h1>
				{$bots.failureReason?.message}
				<Button class="mt-2" on:click={() => $bots.refetch()}>Retry</Button>
			</div>
		{:else if filteredBots}
			<div class="items- flex h-full flex-wrap gap-2">
				{#each filteredBots as bots}
					<Card.Main
						asButton
						freeSize
						class="h-fit max-w-[200px]"
						on:click={() => openBotDetails(bots)}
					>
						<Card.Header empty class="flex items-center justify-between px-2 py-1 text-xs">
							<h1 class="font-semibold">
								{#await getAccountsAccountId( bots.accountId, { headers: { Authorization: `Bearer ${getStorage('token')}` } } ) then account}
									<span class="capitalize">{account.data.data.exchangeId}</span>
								{:catch err}
									failed: {err}
								{/await}
								/
							</h1>
							<button onclick={stopPropagation(bubble('click'))}>
								<Popover triggerClass="flex items-center" forceClose>
									{#snippet trigger()}
										<Icon btn icon="mingcute:more-2-fill" />
									{/snippet}
									{#snippet content()}
										<Card.Main freeSize>
											<Button
												small
												icon="uil:edit"
												full
												class="flex justify-start rounded-none"
												on:click={() => openBotDetails(bots, false, true)}>Edit bot</Button
											>
											<Button
												small
												icon="material-symbols:open-in-new"
												full
												class="flex justify-start rounded-none"
												on:click={() => openBotDetails(bots, true)}>Open on new tab</Button
											>
											<Button
												small
												icon="uil:trash"
												iconClass="text-red-600"
												full
												class="flex justify-start rounded-none text-red-600"
												on:click={() => {
													deletingBot = true;
													selectedBotID = bots.id;
												}}>Remove bot</Button
											>
										</Card.Main>
									{/snippet}
								</Popover>
							</button>
						</Card.Header>
						<Card.Body class="mt-1.5 px-2 pb-1 pt-0 text-[0.65rem]">
							<h2>Open orders: 0</h2>
							<h2>Stock balance: 0</h2>
							<h2>Top pxs: 0</h2>
							<h2>Ref spread: 0</h2>
							<h2>Position: 0</h2>
							<div class="mt-3 flex justify-end opacity-70">
								{formatCommonDate(bots.updatedAt, true)}
							</div>
						</Card.Body>
					</Card.Main>
				{:else}
					<div class="flex flex-col items-center justify-center w-full h-full gap-2">
						<div class="text-center">
							<h1 class="text-2xl font-semibold">Bots not found</h1>
							<p>You can manage your bots here.</p>
						</div>
						<Button on:click={() => tabNavigate('New bot', '/bots/create')}>New bot</Button>
					</div>
				{/each}
			</div>
		{/if}
	</div>
</section>

<ConfirmModal
	open={deletingBot}
	title="Delete bot"
	description="Are you sure you want to delete this bot? This action cannot be undone."
	on:openChange={(e) => {
		deletingBot = e.detail;
		if (!deletingBot) selectedBotID = '';
	}}
	on:cancel={() => (deletingBot = false)}
	on:confirm={confirmDeleteBot}
/>
