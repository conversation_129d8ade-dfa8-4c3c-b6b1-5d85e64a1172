import type { LayoutLoad } from './$types';
import { getStorage, handleErr } from '$utils';
import { getAccounts } from '$client/account';

export const load = (async () => {
	try {
		const accounts = await getAccounts(undefined, {
			headers: { Authorization: `Bearer ${getStorage('token')}` }
		});

		return { accounts };
	} catch (error) {
		handleErr(error);
	}
}) satisfies LayoutLoad;

export const ssr = false;