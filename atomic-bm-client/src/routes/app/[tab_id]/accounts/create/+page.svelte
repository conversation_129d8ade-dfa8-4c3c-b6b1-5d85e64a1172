<script lang="ts">
	import { run } from 'svelte/legacy';

	import { postAccounts } from '$client/account';
	import { page } from '$app/stores';
	import * as Card from '$components/ui/card';
	import Input from '$components/ui/input.svelte';
	import Searcher from '$components/ui/searcher.svelte';
	import { onMount } from 'svelte';
	import {
		tabNavigate,
		getPersistData,
		persistData,
		clearPersistData,
		handleErr,
		toast,
		getStorage
	} from '$utils';
	import { browser } from '$app/environment';
	import { createGetExchanges } from '$client/exchanges';

	let exchange = $state({
		label: '',
		value: ''
	});
	let tag = $state('');

	run(() => {
		if (browser && $page) {
			const exchangeFromPersist = getPersistData('newaccountaccount');
			if (exchangeFromPersist) {
				exchange = exchangeFromPersist;
			} else {
				exchange = { label: '', value: '' };
			}
			tag = getPersistData('newaccounttag');
		}
	});

	function clearFieldsData() {
		clearPersistData('newaccountaccount');
		clearPersistData('newaccountsymbol');
		clearPersistData('newaccountregion');
		clearPersistData('newaccounttag');
	}

	function returnToAccounts() {
		clearFieldsData();
		const isReturnToBots = $page.url.searchParams.get('returnToBots');
		if (!isReturnToBots) {
			tabNavigate('Accounts', '/accounts');
		} else {
			tabNavigate('New bot', '/bots/create');
		}
	}

	let exchanges = $derived(createGetExchanges(undefined, {
		axios: {
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		}
	}));

	let exchangeOptions = $derived($exchanges.data?.data.data.map((exchange) => {
		return { label: exchange.name, value: exchange.exchangeId };
	}));

	async function confirmCreateAccount() {
		try {
			if (exchange.value === '' || exchange.label === '') {
				toast('Please select an exchange', 'error');
				return;
			}

			await postAccounts(
				{
					exchangeId: exchange.value,
					tag
				},
				{
					headers: {
						Authorization: `Bearer ${getStorage('token')}`
					}
				}
			);

			toast('Account created', 'success');
			const isReturnToBots = $page.url.searchParams.get('returnToBots');
			if (!isReturnToBots) {
				tabNavigate('Accounts', '/accounts');
			} else {
				tabNavigate('New bot', `/bots/create?accInfo=["${exchange.label}","${exchange.value}"]`);
			}
			clearFieldsData();
		} catch (error) {
			handleErr(error);
		}
	}

	onMount(async () => {
	});
</script>

<svelte:head>
	<title>New account | Atomic Bot Manager</title>
</svelte:head>

<section class="flex items-center justify-center h-full">
	<Card.Main freeSize class="max-w-[400px]" asForm on:submit={confirmCreateAccount}>
		<Card.Header title="New account" on:iconClick={returnToAccounts} />
		<Card.Body class="space-y-2">
			<Searcher
				hasSelected={exchange.label !== '' && exchange.value !== ''}
				options={exchangeOptions}
				defaultSelected={exchange}
				showCreateBtn={false}
				id="exchanges"
				label="Exchange"
				description="Select the exchange the will be linked to the account"
				placeholder="ex: Binance"
				on:selected={async (e) => {
					await persistData(undefined, 'newaccountexchange', true, e.detail);
					exchange = e.detail;
				}}
			/>
			<Input
				id="tag"
				required
				label="Tag"
				description="The tag of the bot"
				placeholder="Insert a Tag"
				on:input={async (e) => {
					tag = await persistData(e, 'newaccounttag');
				}}
				value={tag}
			/>
		</Card.Body>
		<Card.Footer on:cancel={returnToAccounts} confirmBtnType="submit" />
	</Card.Main>
</section>
