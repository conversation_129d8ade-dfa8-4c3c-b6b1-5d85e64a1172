<script lang="ts">
	import { run } from 'svelte/legacy';

	import Input from '$components/ui/input.svelte';
	import BaseFilters from '$components/base-filters.svelte';
	import Button from '$components/ui/button.svelte';
	import BaseTable from '$components/base-table.svelte';
	import Icon from '$components/ui/icon.svelte';
	import ConfirmModal from '$components/confirm-modal.svelte';
	import BaseLoading from '$components/base-loading.svelte';
	import { appStore } from '$stores/app-store';
	import { onMount, tick } from 'svelte';
	import { page } from '$app/stores';
	import {
		getStorage,
		saveStorage,
		clearApp,
		tabNavigate,
		persistData,
		getPersistData,
		formatCommonDate,
		toast,
		handleErr,
		checkObjectKeys,
		clearPersistData
	} from '$utils';
	import { browser } from '$app/environment';
	import { createGetAccounts, deleteAccountsAccountId } from '$client/account';
	import type { GetAccountsParams } from '$client/api.schemas';

	let currentPage = $state(1);
	let deletingAccount = $state(false);
	let selectedAccountID = $state('');
	let searchAccounts = $state('');
	let filters: GetAccountsParams = $state({
		exchangeId: undefined,
		tag: undefined,
		isDeleted: undefined,
		orderBy: undefined,
		orderDirection: undefined,
		limit: undefined,
		offset: undefined
	});

	run(() => {
		if (browser) {
			const storageValue = getStorage($page.params.tab_id);
			if (storageValue) {
				searchAccounts = storageValue;
			} else {
				searchAccounts = '';
			}
			filters.limit = getPersistData('limitaccount') ?? undefined;
			filters.orderDirection = getPersistData('directionaccount') ?? undefined;
			filters.orderBy = getPersistData('orderbyaccount') ?? undefined;
			filters.isDeleted = getPersistData('isdeletedaccount') ?? undefined;
		}
	});

	let accounts = $derived(createGetAccounts(
		{
			exchangeId: filters.exchangeId,
			tag: filters.tag,
			isDeleted: filters.isDeleted,
			orderBy: filters.orderBy,
			orderDirection: filters.orderDirection,
			limit: filters.limit,
			offset: filters.offset
		},
		{
			axios: {
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			}
		}
	));
	let filteredAccounts = $derived($accounts?.data?.data?.data.filter((acc) => {
		if (searchAccounts) {
			return (
				acc?.tag?.toLowerCase().includes(searchAccounts?.toLowerCase()) ||
				acc?.id?.toLowerCase().includes(searchAccounts?.toLowerCase())
			);
		} else {
			return acc;
		}
	}));

	run(() => {
		$accounts.isError && $accounts.error.status === 403 && clearApp();
	});
	run(() => {
		$accounts.failureReason?.response?.status === 403 && clearApp();
	});

	async function saveInputValue(e: Event) {
		const target = e.target as HTMLInputElement;
		searchAccounts = target.value;
		await tick();
		saveStorage($page.params.tab_id, target.value);
	}

	async function confirmDeleteAccount() {
		try {
			await deleteAccountsAccountId(selectedAccountID, {
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			});

			toast('Account deleted', 'success');
			deletingAccount = false;
			$accounts.refetch();
		} catch (error) {
			handleErr(error);
		}
	}

	async function handlePagination(type: 'prev' | 'next') {
		if (type === 'next' && filters.offset === undefined && filters.limit === undefined) {
			filters.limit = 10;
			filters.offset = 10;
			currentPage++;
			return;
		}

		if (type === 'next' && filters.offset === 0 && filters.limit) {
			filters.offset += filters.limit;
			currentPage++;
			return;
		}

		if (filters.offset && filters.limit) {
			if (type === 'next') {
				filters.offset += filters.limit;
				currentPage++;
			} else if (type === 'prev') {
				filters.offset -= filters.limit;
				currentPage--;
			}

			$accounts.refetch();
		}
	}

	async function setFilterOptions(e: CustomEvent) {
		e.detail.exchangeId &&
			(await persistData(undefined, 'accountidaccount', true, e.detail.exchangeId));
		e.detail.tag && (await persistData(undefined, 'tagaccount', true, e.detail.tag));
		filters.exchangeId = e.detail.accountId;
		filters.tag = e.detail.tag;
	}

	async function handleFilterSelection(e: CustomEvent, filterType: string) {
		switch (filterType) {
			case 'limit':
				await persistData(undefined, 'limitaccount', true, e.detail);
				filters.limit = e.detail;
				break;
			case 'direction':
				await persistData(undefined, 'directionaccount', true, e.detail);
				filters.orderDirection = e.detail;
				break;
			case 'orderBy':
				await persistData(undefined, 'orderbyaccount', true, e.detail);
				filters.orderBy = e.detail;
				break;
			case 'isDeleted':
				await persistData(undefined, 'isdeletedaccount', true, e.detail);
				filters.isDeleted = e.detail;
				break;
			case 'options':
				setFilterOptions(e);
				break;
		}
	}

	onMount(async () => {
		$appStore.currentPageTitle = 'Accounts';
	});
</script>

<svelte:head>
	<title>Accounts | Atomic Bot Manager</title>
</svelte:head>

<section class="flex h-full flex-col p-1.5">
	<div class="flex justify-between">
		<div class="flex flex-wrap items-end flex-1 gap-2">
			<div>
				<Input
					placeholder="Search for an account"
					on:input={saveInputValue}
					value={searchAccounts}
				/>
			</div>
			<BaseFilters
				filterType="accounts"
				tag={filters.tag}
				pageLimit={filters.limit}
				directionOption={filters.orderDirection}
				accountsOrderBy={filters.orderBy}
				activeState={filters.isDeleted}
				exchangeId={filters.exchangeId}
				on:pageLimit={(e) => handleFilterSelection(e, 'limit')}
				on:direction={(e) => handleFilterSelection(e, 'direction')}
				on:accountsOrderBy={(e) => handleFilterSelection(e, 'orderBy')}
				on:activeState={(e) => handleFilterSelection(e, 'isDeleted')}
				on:filterOptions={(e) => handleFilterSelection(e, 'options')}
				on:refresh={() => $accounts.refetch()}
			/>
		</div>
		<div>
			<Button on:click={() => tabNavigate('New account', '/accounts/create')}>New account</Button>
		</div>
	</div>
	<div class="flex-1 mt-2">
		{#if checkObjectKeys(filters)}
			<div class="mb-2 mt-1.5 flex max-w-fit items-center gap-2 px-1">
				<h2 class="text-[0.65rem] font-semibold">Active filters:</h2>
				<div class="flex gap-1">
					{#if filters.exchangeId}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.exchangeId = undefined;
								clearPersistData('accountidaccount');
							}}>Account</Button
						>
					{/if}
					{#if filters.isDeleted}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.isDeleted = undefined;
								clearPersistData('isdeletedaccount');
							}}
							>{#if filters.isDeleted}Deleted{:else}Active{/if}</Button
						>
					{/if}
					{#if filters.orderBy}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.orderBy = undefined;
								clearPersistData('orderbyaccount');
							}}>Order by: <strong>{filters.orderBy}</strong></Button
						>
					{/if}
					{#if filters.orderDirection}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.orderDirection = undefined;
								clearPersistData('orderaccount');
							}}>Order direction: <strong>{filters.orderDirection}</strong></Button
						>
					{/if}
					{#if filters.limit && filters.limit > 10}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={() => {
								filters.limit = undefined;
								clearPersistData('limitaccount');
							}}>Limit: <strong>{filters.limit}</strong></Button
						>
					{/if}
				</div>
			</div>
		{/if}
		{#if $accounts.isLoading}
			<BaseLoading title="Loading accounts data..." />
		{:else if $accounts.isError}
			<div class="flex flex-col items-center justify-center h-full">
				<h1 class="text-2xl font-semibold">Failed to load accounts</h1>
				{$accounts.failureReason?.message}
				<Button class="mt-2" on:click={() => $accounts.refetch()}>Retry</Button>
			</div>
		{:else}
			<BaseTable
				title="Accounts list"
				name="Account"
				
				
				hideDetails={true}
				data={filteredAccounts}
				dataHeaders={[
					{
						title: 'Tag',
						class: 'w-[100px]'
					},
					{
						title: 'ID'
					},
					{
						title: 'Updated at'
					},
					{
						title: 'Created at'
					}
				]}
				on:delete={(e) => {
					deletingAccount = true;
					selectedAccountID = e.detail.id;
				}}
				{currentPage}
				pageLength={filteredAccounts?.length}
				filters={{
					limit: filters.limit,
					offset: filters.offset
				}}
				on:next={() => handlePagination('next')}
				on:prev={() => handlePagination('prev')}
			>
				{#snippet children({ item, TableCell })}
										<TableCell>{item.tag}</TableCell>
					<TableCell
						><div class="flex items-center gap-1">
							{item.id}
							<Icon icon="mingcute:copy-line" />
						</div></TableCell
					>
					<TableCell>{formatCommonDate(item.updatedAt)}</TableCell>
					<TableCell>{formatCommonDate(item.createdAt)}</TableCell>
													{/snippet}
								</BaseTable>
		{/if}
	</div>
</section>

<ConfirmModal
	open={deletingAccount}
	title="Delete account"
	description="Are you sure you want to delete this account? This action cannot be undone."
	on:openChange={(e) => {
		deletingAccount = e.detail;
		if (!deletingAccount) selectedAccountID = '';
	}}
	on:cancel={() => (deletingAccount = false)}
	on:confirm={confirmDeleteAccount}
/>
