<script lang="ts">
	import { run, preventDefault } from 'svelte/legacy';

	import { profile } from '$stores/profile-store';
	import Button from '$components/ui/button.svelte';
	import Input from '$components/ui/input.svelte';
	import Icon from '$components/ui/icon.svelte';
	import type { Message } from '$client/api.schemas';
	import { websocketStore } from '$stores/websocket-store';
	import { atws } from '$api/centrifuge';
	import { appStore } from '$stores/app-store';
	import { onMount } from 'svelte';
	import { createGetTrollboxHistory } from '$client/trollbox';
	import { formatCommonDate, getStorage, viewport, scrollToTheBottom } from '$utils';

	let { data } = $props();

	let displayCorrectUser = $derived((id: string) => {
		if (id === $profile?.id) {
			return 'You';
		} else {
			const user = data?.users?.find((u: any) => u.id === id);
			return user ? user.name : 'Not found';
		}
	});

	let newMsg = $state('');
	let trollboxFilters = $state({
		cursor: undefined,
		pageSize: 10
	});
	let trollboxMessages: Message[] = $state([]);
	let pageLength = $state(0);
	let isIncreasingPageSize = $state(false);
	let reachedMessagesEnd = $state(false);

	let trollboxHistory = $derived(createGetTrollboxHistory(
		{
			cursor: trollboxFilters.cursor,
			pageSize: trollboxFilters.pageSize
		},
		{
			axios: {
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			}
		}
	));

	run(() => {
		if ($trollboxHistory?.data?.data?.data.messages) {
			trollboxMessages = [];
			trollboxMessages = [...trollboxMessages, ...$trollboxHistory?.data?.data?.data.messages];
			pageLength = $trollboxHistory?.data?.data?.data.messages.length;
		}
	});

	function increasePageSize() {
		if (reachedMessagesEnd || trollboxMessages.length < 10) return;

		const oldPageLength = pageLength;
		isIncreasingPageSize = true;

		setTimeout(async () => {
			if (isIncreasingPageSize) {
				trollboxFilters.pageSize += 10;
				$trollboxHistory.refetch();

				setTimeout(() => {
					if (oldPageLength === pageLength) {
						reachedMessagesEnd = true;
					}
				}, 500);
			}
		}, 1000);
	}

	function updateTrollboxMessages(newMessage: Message) {
		trollboxMessages = [newMessage, ...trollboxMessages];
	}

	run(() => {
		if ($websocketStore.newTrollboxMessage) {
			updateTrollboxMessages($websocketStore.newTrollboxMessage);
			$websocketStore.newTrollboxMessage = null;
		}
	});

	function submitMsg() {
		atws.sendMessage(newMsg);
		newMsg = '';
		setTimeout(() => {
			scrollToTheBottom('trollbox');
		}, 10);
	}

	onMount(() => {
		$appStore.currentPageTitle = 'Trollbox';
		scrollToTheBottom('trollbox');
	});
</script>

<svelte:head>
	<title>Trollbox | Atomic Bot Manager</title>
</svelte:head>

<section class="flex justify-center h-full p-2">
	<div class="flex h-full max-h-[700px] w-full flex-col bg-dark-6">
		<div class="flex justify-between p-2 text-xs border-b border-gray-800 bg-dark-9">
			<div class="flex items-center gap-1">
				<Icon icon="wpf:online" size="medium" class="text-green-600" />
				<strong class="text-green-600">Online</strong>
			</div>
			<!-- <Icon icon="mage:filter-fill" size="medium" btn /> -->
		</div>
		<div class="flex h-[80%] flex-1 flex-col">
			<div class="flex flex-col-reverse gap-2 px-2 overflow-y-auto" id="trollbox">
				{#each trollboxMessages as item, i (i)}
					<div class="p-2 space-y-1 bg-dark-7">
						<div class="flex justify-between">
							<span class="text-sm font-bold">
								{displayCorrectUser(item.userId)}
							</span>
							<span class="text-xs font-light">{formatCommonDate(item.timestamp, true)}</span>
						</div>
						<p class="text-sm break-all">
							{item.content}
						</p>
					</div>
				{/each}
				<div
					class="flex items-center justify-center"
					use:viewport
					onenterViewport={increasePageSize}
					onexitViewport={() => (isIncreasingPageSize = false)}
				>
					{#if isIncreasingPageSize}
						<span class="text-sm font-light">Loading messages...</span>
					{:else if reachedMessagesEnd}
						<span class="text-sm font-light"> No more messages </span>
					{/if}
				</div>
			</div>
		</div>
		<form class="flex gap-0.5 p-2" onsubmit={preventDefault(submitMsg)}>
			<Input required placeholder="Type a message" bind:value={newMsg} />
			<Button type="submit" icon="solar:map-arrow-right-line-duotone" class="w-[50px]" iconBtn />
		</form>
	</div>
</section>
