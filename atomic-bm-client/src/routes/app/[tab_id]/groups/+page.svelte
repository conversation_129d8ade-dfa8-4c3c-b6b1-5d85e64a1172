<script lang="ts">
	import { run } from 'svelte/legacy';

	import { appStore } from '$stores/app-store';
	import { onMount, tick } from 'svelte';
	import { page } from '$app/stores';
	import {
		getStorage,
		saveStorage,
		clearApp,
		tabNavigate,
		persistData,
		getPersistData,
		formatCommonDate,
		toast,
		handleErr,
		checkObjectKeys,
		clearPersistData
	} from '$utils';
	import { browser } from '$app/environment';

	import { createGetGroups, deleteGroupsGroupId } from '$client/param-groups';

	import type { GetGroupsParams } from '$client/api.schemas';
	import Input from '$components/ui/input.svelte';
	import BaseFilters from '$components/base-filters.svelte';
	import Button from '$components/ui/button.svelte';
	import BaseTable from '$components/base-table.svelte';
	import Icon from '$components/ui/icon.svelte';
	import ConfirmModal from '$components/confirm-modal.svelte';
	import BaseLoading from '$components/base-loading.svelte';

	let currentPage = $state(1);
	let deletingGroup = $state(false);
	let selectedGroupID = $state('');
	let searchGroups = $state('');
	let filters: GetGroupsParams = $state({
		name: undefined,
		createdByUserId: undefined,
		orderBy: undefined,
		orderDirection: undefined,
		limit: undefined,
		offset: undefined
	});

	run(() => {
		if (browser) {
			const storageValue = getStorage($page.params.tab_id);
			if (storageValue) {
				searchGroups = storageValue;
			} else {
				searchGroups = '';
			}
			filters.limit = getPersistData('limitgroup') ?? undefined;
			filters.orderDirection = getPersistData('directiongroup') ?? undefined;
			filters.orderBy = getPersistData('orderbygroup') ?? undefined;
		}
	});

	let groups = $derived(createGetGroups(
		{
			name: filters.name,
			orderBy: filters.orderBy,
			orderDirection: filters.orderDirection,
			limit: filters.limit,
			offset: filters.offset
		},
		{
			axios: {
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			}
		}
	));
	let filteredGroups = $derived($groups?.data?.data?.data.filter((group) => {
		if (searchGroups) {
			return (
				group?.name?.toLowerCase().includes(searchGroups?.toLowerCase()) ||
				group?.id?.toLowerCase().includes(searchGroups?.toLowerCase())
			);
		} else {
			return group;
		}
	}));

	run(() => {
		$groups.isError && $groups.error.status === 403 && clearApp();
	});
	run(() => {
		$groups.failureReason?.response?.status === 403 && clearApp();
	});

	async function saveInputValue(e: Event) {
		const target = e.target as HTMLInputElement;
		searchGroups = target.value;
		await tick();
		saveStorage($page.params.tab_id, target.value);
	}

	async function confirmDeleteGroup() {
		try {
			await deleteGroupsGroupId(selectedGroupID, {
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			});

			toast('Group deleted', 'success');
			deletingGroup = false;
			$groups.refetch();
		} catch (error) {
			handleErr(error);
		}
	}

	async function handlePagination(type: 'prev' | 'next') {
		if (type === 'next' && filters.offset === undefined && filters.limit === undefined) {
			filters.limit = 10;
			filters.offset = 10;
			currentPage++;
			return;
		}

		if (type === 'next' && filters.offset === 0 && filters.limit) {
			filters.offset += filters.limit;
			currentPage++;
			return;
		}

		if (filters.offset && filters.limit) {
			if (type === 'next') {
				filters.offset += filters.limit;
				currentPage++;
			} else if (type === 'prev') {
				filters.offset -= filters.limit;
				currentPage--;
			}

			$groups.refetch();
		}
	}

	async function setFilterOptions(e: CustomEvent) {
		e.detail.name && (await persistData(undefined, 'namegroup', true, e.detail.exchangeId));
		e.detail.created_by_user_id &&
			(await persistData(undefined, 'createdbyuseridgroup', true, e.detail.tag));
		filters.name = e.detail.name;
		filters.createdByUserId = e.detail.created_by_user_id;
	}

	async function handleFilterSelection(e: CustomEvent, filterType: string) {
		switch (filterType) {
			case 'limit':
				await persistData(undefined, 'limitgroup', true, e.detail);
				filters.limit = e.detail;
				break;
			case 'direction':
				await persistData(undefined, 'directiongroup', true, e.detail);
				filters.orderDirection = e.detail;
				break;
			case 'orderBy':
				await persistData(undefined, 'orderbygroup', true, e.detail);
				filters.orderBy = e.detail;
				break;
			case 'options':
				setFilterOptions(e);
				break;
		}
	}

	onMount(async () => {
		$appStore.currentPageTitle = 'Groups';
	});
</script>

<svelte:head>
	<title>Groups | Atomic Bot Manager</title>
</svelte:head>

<section class="flex h-full flex-col p-1.5">
	<div class="flex justify-between">
		<div class="flex items-end flex-1 gap-2">
			<div>
				<Input placeholder="Search for a group" on:input={saveInputValue} value={searchGroups} />
			</div>
			<BaseFilters
				filterType="groups"
				pageLimit={filters.limit}
				directionOption={filters.orderDirection}
				on:pageLimit={(e) => handleFilterSelection(e, 'limit')}
				on:direction={(e) => handleFilterSelection(e, 'direction')}
				on:accountsOrderBy={(e) => handleFilterSelection(e, 'orderBy')}
				on:activeState={(e) => handleFilterSelection(e, 'isDeleted')}
				on:filterOptions={(e) => handleFilterSelection(e, 'options')}
				on:refresh={() => $groups.refetch()}
			/>
		</div>
		<div>
			<Button on:click={() => tabNavigate('New group', '/groups/create')}>New group</Button>
		</div>
	</div>
	<div class="flex-1 mt-2">
		{#if checkObjectKeys(filters)}
			<div class="mb-2 mt-1.5 flex max-w-fit items-center gap-2 px-1">
				<h2 class="text-[0.65rem] font-semibold">Active filters:</h2>
				<div class="flex gap-1">
					{#if filters.orderBy}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.orderBy = undefined;
								clearPersistData('orderbygroup');
							}}>Order by: <strong>{filters.orderBy}</strong></Button
						>
					{/if}
					{#if filters.orderDirection}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={async () => {
								filters.orderDirection = undefined;
								clearPersistData('ordergroup');
							}}>Order direction: <strong>{filters.orderDirection}</strong></Button
						>
					{/if}
					{#if filters.limit && filters.limit > 10}
						<Button
							icon="material-symbols:close"
							small
							class="!text-[0.65rem] font-normal"
							on:click={() => {
								filters.limit = undefined;
								clearPersistData('limitgroup');
							}}>Limit: <strong>{filters.limit}</strong></Button
						>
					{/if}
				</div>
			</div>
		{/if}
		{#if $groups.isLoading}
			<BaseLoading title="Loading group data..." />
		{:else if $groups.isError}
			<div class="flex flex-col items-center justify-center h-full">
				<h1 class="text-2xl font-semibold">Failed to load groups</h1>
				{$groups.failureReason?.message}
				<Button class="mt-2" on:click={() => $groups.refetch()}>Retry</Button>
			</div>
		{:else}
			<BaseTable
				title="Groups list"
				name="Group"
				
				
				data={filteredGroups}
				dataHeaders={[
					{
						title: 'Tag',
						class: 'w-[100px]'
					},
					{
						title: 'ID'
					},
					{
						title: 'Updated at'
					},
					{
						title: 'Created at'
					}
				]}
				on:delete={(e) => {
					deletingGroup = true;
					selectedGroupID = e.detail.id;
				}}
				{currentPage}
				pageLength={filteredGroups?.length}
				filters={{
					limit: filters.limit,
					offset: filters.offset
				}}
				on:next={() => handlePagination('next')}
				on:prev={() => handlePagination('prev')}
			>
				{#snippet children({ item, TableCell })}
										<TableCell
						><div class="flex items-center gap-1">
							{item.id}
							<Icon icon="mingcute:copy-line" />
						</div></TableCell
					>
					<TableCell>{item.name}</TableCell>
					<TableCell>{formatCommonDate(item.createdAt)}</TableCell>
													{/snippet}
								</BaseTable>
		{/if}
	</div>
</section>

<ConfirmModal
	open={deletingGroup}
	title="Delete group"
	description="Are you sure you want to delete this group? This action cannot be undone."
	on:openChange={(e) => {
		deletingGroup = e.detail;
		if (!deletingGroup) selectedGroupID = '';
	}}
	on:cancel={() => (deletingGroup = false)}
	on:confirm={confirmDeleteGroup}
/>
