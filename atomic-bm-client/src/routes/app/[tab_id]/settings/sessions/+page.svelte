<script lang="ts">
	import { run } from 'svelte/legacy';

	import { appStore } from '$stores/app-store';
	import { onMount } from 'svelte';
	import { getStorage, clearApp, handleErr } from '$utils';
	import {
		createGetUsersSessionsActive,
		postUsersSessionsBulkDelete,
		deleteUsersSessionsSessionId
	} from '$client/user';
	import * as Card from '$components/ui/card';
	import Button from '$components/ui/button.svelte';
	import ConfirmModal from '$components/confirm-modal.svelte';
	import { flip } from 'svelte/animate';

	let removingAllSessions = $state(false);
	let removingSession = $state(false);
	let selectedSessionID: number = $state(0);
	let sessions = $derived(createGetUsersSessionsActive({
		axios: {
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		}
	}));
	run(() => {
		$sessions.isError && $sessions.error.status === 403 && clearApp();
	});
	run(() => {
		$sessions.failureReason?.response?.status === 403 && clearApp();
	});

	async function confirmRemoveAllSessions() {
		try {
			await postUsersSessionsBulkDelete({
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			});

			$sessions.refetch();
		} catch (error) {
			handleErr(error);
		} finally {
			removingAllSessions = false;
		}
	}

	async function confirmRemoveSessionID(sessionID: number) {
		try {
			await deleteUsersSessionsSessionId(sessionID, {
				headers: {
					Authorization: `Bearer ${getStorage('token')}`
				}
			});

			$sessions.refetch();
		} catch (error) {
			handleErr(error);
		} finally {
			removingSession = false;
		}
	}

	onMount(async () => {
		$appStore.currentPageTitle = 'Sessions';
	});
</script>

<svelte:head>
	<title>Sessions | Atomic Bot Manager</title>
</svelte:head>

<section>
	<h1 class="text-base font-bold">Active sessions</h1>
	<p class="text-sm">
		Manage your active sessions here by removing a selected session or all sessions.
	</p>
	{#if $sessions.isLoading}
		coe
	{:else if $sessions.isError}
		{$sessions.failureReason?.message}
	{:else}
		<div class="mt-3 flex flex-wrap gap-2">
			{#each $sessions.data.data.data as session (session.id)}
				<div class="w-full max-w-[280px]" animate:flip={{ duration: 200 }}>
					<Card.Main freeSize class="max-w-[280px] px-3 pb-2 pt-1">
						<h1 class="font-medium">
							{session.details.deviceName} on {session.details.platformType}
							{session.details.deviceType}
						</h1>
						<h2 class="text-xs">
							{session.details.location.area}, {session.details.location.city}, {session.details
								.location.country}
						</h2>
						<h2 class="text-xs mt-1.5">
							{session.details.ip}
						</h2>
						<h2 class="text-xs">
							First Access: <strong>{session.details.ip}</strong>
						</h2>
						<h2 class="text-xs">
							Last Access: <strong>{session.details.ip}</strong>
						</h2>
						<div class="flex justify-end">
							<Button
								class="mt-2 p-1 px-3"
								on:click={() => {
									selectedSessionID = session.id;
									removingSession = true;
								}}>Remove</Button
							>
						</div>
					</Card.Main>
				</div>
			{:else}
				no sessions
			{/each}
		</div>
	{/if}
	<Button class="mt-2" on:click={() => (removingAllSessions = true)}>Remove all sessions</Button>
</section>

<ConfirmModal
	title="Remove all sessions"
	description="Are you sure you want to remove all sessions? This action cannot be undone and you will be <strong> automatically logged out</strong>."
	open={removingAllSessions}
	on:openChange={(e) => (removingAllSessions = e.detail)}
	on:cancel={() => (removingAllSessions = false)}
	on:confirm={confirmRemoveAllSessions}
/>

<ConfirmModal
	title="Removing session"
	description="Are you sure you want to remove this session? This action cannot be undone and if this is your current active session, you will be <strong> automatically logged out </strong>."
	open={removingSession}
	on:openChange={(e) => {
		removingSession = e.detail;
		if (!removingSession) selectedSessionID = 0;
	}}
	on:cancel={() => {
		removingSession = false;
		selectedSessionID = 0;
	}}
	on:confirm={() => confirmRemoveSessionID(selectedSessionID)}
/>
