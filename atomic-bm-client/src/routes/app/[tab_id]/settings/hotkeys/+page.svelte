<script lang="ts">
	import { appStore } from '$stores/app-store';
	import { onMount } from 'svelte';

	onMount(() => {
		$appStore.currentPageTitle = 'Hotkeys';
	});
</script>

<svelte:head>
	<title>Hotkeys | Atomic Bot Manager</title>
</svelte:head>

<section class="flex flex-col gap-2">
	<div>
		<h1 class="font-bold">Hotkeys</h1>
		<p class="text-sm">Scroll through to see available hotkeys.</p>
	</div>
	<div class="mt-2 flex items-center divide-x-2 bg-dark-6 py-1.5 text-sm w-fit">
		<h1 class="px-2">Open terminal docs</h1>
		<h1 class="px-2 text-xs font-semibold">Ctrl + Shift + A</h1>
	</div>
	<div class="flex items-center divide-x-2 bg-dark-6 py-1.5 text-sm w-fit">
		<h1 class="px-2">Focus terminal input</h1>
		<h1 class="px-2 text-xs font-semibold">Ctrl + Shift + R</h1>
	</div>
	<div class="flex items-center divide-x-2 bg-dark-6 py-1.5 text-sm w-fit">
		<h1 class="px-2">Exit the app</h1>
		<h1 class="px-2 text-xs font-semibold">Ctrl + Shift + Q</h1>
	</div>
</section>
