<script lang="ts">
	import { preventDefault } from 'svelte/legacy';

	import { appStore } from '$stores/app-store';
	import { onMount } from 'svelte';
	import { profile } from '$stores/profile-store';
	import { formatCommonDate, getStorage, handleErr, toast } from '$utils';
	import Icon from '$components/ui/icon.svelte';
	import Button from '$components/ui/button.svelte';
	import BaseModal from '$components/base-modal.svelte';
	import Input from '$components/ui/input.svelte';
	import { patchUsersPassword } from '$client/user';

	let updatingPassword = $state(false);
	let passwordData = $state({
		currentPassword: '',
		newPassword: '',
		confirmPassword: ''
	});

	async function confirmUpdatePassword() {
		try {
			if (passwordData.newPassword !== passwordData.confirmPassword) {
				toast('Passwords do not match', 'error');
				return;
			}
			await patchUsersPassword(
				{
					currentPassword: passwordData.currentPassword,
					newPassword: passwordData.newPassword
				},
				{
					headers: {
						Authorization: `Bearer ${getStorage('token')}`
					}
				}
			);
			$appStore.currentPageTitle = 'Profile';
			updatingPassword = false;
			toast('Password updated', 'success');
			clearStates();
		} catch (err) {
			handleErr(err);
		}
	}

	function clearStates() {
		passwordData = {
			currentPassword: '',
			newPassword: '',
			confirmPassword: ''
		};
	}

	onMount(() => {
		$appStore.currentPageTitle = 'Profile';
	});
</script>

<svelte:head>
	<title>Profile | Atomic Bot Manager</title>
</svelte:head>

<section>
	<div class="flex justify-between">
		<div>
			<div class="flex items-center gap-1">
				<Icon icon="iconamoon:profile-circle" class="text-xl" />
				<h1 class="text-base font-bold capitalize">
					{$profile.name}
				</h1>
			</div>
			<h2 class="text-sm font-normal">
				{$profile.email}
			</h2>
			<Button icon="mdi:password-reset" class="mt-2" on:click={() => (updatingPassword = true)}
				>Update password</Button
			>
		</div>
		<div class="mt-1 flex items-end flex-col text-xs">
			<h2>
				Created: <strong>{formatCommonDate($profile.createdAt, true)}</strong>
			</h2>
			<h2>
				Last update: <strong>{formatCommonDate($profile.updatedAt, true)}</strong>
			</h2>
		</div>
	</div>
</section>

<BaseModal
	open={updatingPassword}
	on:openChange={(e) => {
		updatingPassword = e.detail;
		if (!updatingPassword) clearStates();
	}}
	title="Password update"
>
	<form onsubmit={preventDefault(confirmUpdatePassword)}>
		<div class="space-y-2">
			<Input
				id="currentPassword"
				required
				placeholder="********"
				type="password"
				label="Current password"
				bind:value={passwordData.currentPassword}
			/>
			<Input
				id="newPassword"
				required
				placeholder="********"
				type="password"
				label="New password"
				bind:value={passwordData.newPassword}
			/>
			<Input
				id="confirmPassword"
				required
				placeholder="********"
				type="password"
				label="Confirm new password"
				bind:value={passwordData.confirmPassword}
			/>
		</div>
		<Button type="submit" full class="mt-5">Update</Button>
	</form>
</BaseModal>
