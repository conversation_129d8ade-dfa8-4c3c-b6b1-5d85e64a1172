<script lang="ts">
	import { run } from 'svelte/legacy';

	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import { createTabStore } from '$stores/tab-store';
	import { appStore, menuOptions } from '$stores/app-store';
	import { tabNavigate } from '$utils';
	import { browser } from '$app/environment';
	import { invalidate } from '$app/navigation';
	import { profile } from '$stores/profile-store';
	import { postUsersLogout } from '$client/user';
	import { clearApp, loadWsInstances } from '$utils';
	import * as Card from '$lib/components/ui/card';
	import type { MenuOptions } from '$types';
	import { hotkeys } from '$utils/hotkeys';
	import AppHeader from '$lib/components/app-header.svelte';
	import AppTabs from '$lib/components/app-tabs.svelte';
	import BaseDrawer from '$lib/components/base-drawer.svelte';
	import BaseLoading from '$lib/components/base-loading.svelte';
	import Button from '$lib/components/ui/button.svelte';
	import ConfirmModal from '$lib/components/confirm-modal.svelte';
	import Terminal from '$lib/components/web-terminal/terminal.svelte';
	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	let openMenu = $state(false);
	let loggingOut = $state(false);
	let loading = $state(true);

	export const tbs = createTabStore(
		{
			tabs: [],
			lastTabID: ''
		},
		$profile.id
	);

	function checkTabChange() {
		const name = $page.url.searchParams.get('name') as string;
		const path = $page.url.searchParams.get('path') as string;
		const isNew = $page.url.searchParams.get('new') as string;
		const keepName = $page.url.searchParams.get('keepName') as string;
		if (isNew && name && path) {
			tbs.openNewTab(name, path);
		} else if (name && path && !keepName) {
			tbs.navigateOnTabChange({
				name,
				path
			});
		} else {
			tbs.navigateOnTabChange(
				{
					name,
					path
				},
				keepName === 'true'
			);
		}
	}

	run(() => {
		if (browser) {
			$page.url.searchParams.get('navigating') !== null && checkTabChange();
		}
	});

	function navigateOnMenu(item: MenuOptions) {
		const name = item.name === 'Settings' ? item.tabName : (item.name as string);
		if (name) tabNavigate(name, item.path);
		openMenu = false;
	}

	async function confirmLogout() {
		postUsersLogout();
		loggingOut = false;
		clearApp(true);
	}

	typeof window !== 'undefined' && window.addEventListener('keydown', hotkeys.setupEventListeners);

	run(() => {
		if ($page.url.searchParams.get('quit') === 'true') {
			loggingOut = true;
		}
	});

	onMount(async () => {
		invalidate('navigation:navigation');
		await tbs.initializeTabs();
		loading = false;
		loadWsInstances();
	});
</script>

<section class="flex h-full flex-col gap-1 p-[0.3rem]">
	<AppHeader on:openMenu={() => (openMenu = true)}>
		{#snippet tabs()}
			<AppTabs
				tabs={$tbs.tabs}
				lastTabID={$tbs.lastTabID}
				{loading}
				on:tabClick={(e) => tbs.switchTab(e.detail)}
				on:tabClose={(e) => tbs.removeTab(e.detail)}
				on:reorderTabs={(e) => tbs.updateTabsOrder(e.detail)}
				on:tabCreate={() => tbs.openNewTab()}
			/>
		{/snippet}
	</AppHeader>
	<div class="relative flex-1 overflow-visible">
		<Card.Main freeSize class="bg-dark-7 flex h-full flex-col overflow-y-auto">
			{#if loading}
				<div class="flex w-full flex-1 items-center justify-center">
					<BaseLoading title="Loading info..." />
				</div>
			{:else}
				<Card.Header empty class="px-2 py-1">
					<h1 class="text-sm font-semibold">
						{$appStore.currentPageTitle}
					</h1>
				</Card.Header>
				<Card.Body class="mt-0 flex-1 p-0">
					{@render children?.()}
				</Card.Body>
			{/if}
		</Card.Main>
		{#if !$page.url.pathname.includes('trollbox') && !$page.url.pathname.includes('tradingview')}
			<Terminal />
		{/if}
	</div>
</section>

<BaseDrawer
	open={openMenu}
	direction="right"
	contentClass="max-w-[220px]"
	on:openChange={(e) => (openMenu = e.detail)}
	footerClass="w-full"
	title="Menu"
	description="Select your menu option"
>
	{#snippet area()}
		<div class="mt-3 space-y-1.5">
			{#each $menuOptions as item}
				<Button
					class="flex-row-reverse justify-between"
					full
					icon={item.icon}
					on:click={() => navigateOnMenu(item)}>{item.name}</Button
				>
			{/each}
		</div>
	{/snippet}
	{#snippet footer()}
		<div>
			<Button
				class="flex-row-reverse justify-between"
				full
				icon="mdi:location-exit"
				on:click={() => {
					openMenu = false;
					loggingOut = true;
				}}
			>
				Exit app</Button
			>
		</div>
	{/snippet}
</BaseDrawer>

<ConfirmModal
	open={loggingOut}
	title="Confirm logout"
	description="Are you sure you want to logout?"
	on:openChange={(e) => (loggingOut = e.detail)}
	on:cancel={() => (loggingOut = false)}
	on:confirm={confirmLogout}
/>
