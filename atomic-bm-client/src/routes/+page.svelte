<script lang="ts">
	import * as Card from '$components/ui/card';
	import Icon from '$components/ui/icon.svelte';
	import Input from '$components/ui/input.svelte';
	import Button from '$components/ui/button.svelte';
	import BaseLoading from '$components/base-loading.svelte';
	import { persisted } from 'svelte-persisted-store';
	import { setToken } from '$stores/auth-store';
	import { handleErr, toast } from '$utils';
	import { goto } from '$app/navigation';
	import { postUsersLogin } from '$client/user';
	import { setProfile } from '$stores/profile-store';
	import { page } from '$app/stores';
	import { onMount } from 'svelte';

	let loading = $state(false);
	let email = persisted('email', '');
	let password = $state('');

	async function login() {
		loading = true;
		try {
			const res = await postUsersLogin({
				email: $email,
				password
			});
			if (res.status === 400) {
				toast('Invalid credentials', 'error');
				return;
			}

			setToken(res.data.data.token, res.data.data.expiry);
			await setProfile();
			toast('Login successful', 'success');
			goto('/app');
		} catch (e) {
			handleErr(e);
		} finally {
			loading = false;
		}
	}

	onMount(() => {
		if ($page.url.searchParams.get('unauthorized')) {
			toast('You are not authorized to access this page, please make sure to login', 'error');
			goto('/');
		} else if ($page.url.searchParams.get('logout')) {
			toast('You have been logged out', 'success');
			goto('/');
		}
	});
</script>

<svelte:head>
	<title>Atomic Bot Manager</title>
</svelte:head>

<section class="flex h-full items-center justify-center">
	<Card.Main freeSize class="max-w-[400px]" asForm on:submit={login}>
		{#if loading}
			<BaseLoading title="Logging in..." />
		{:else}
			<Card.Header empty>
				<div class="flex items-center gap-1.5">
					<Icon icon="mingcute:react-line" class="bg-blue-1 text-lg" />
					<h1 class="text-sm font-semibold">Bot manager</h1>
				</div>
			</Card.Header>
			<Card.Body class="space-y-3">
				<Input
					id="email"
					required
					label="Email"
					placeholder="<EMAIL>"
					bind:value={$email}
				/>
				<Input
					id="password"
					required
					type="password"
					label="Password"
					placeholder="********"
					bind:value={password}
				/>
			</Card.Body>
			<Card.Footer empty>
				<Button type="submit" icon="line-md:login" full>Login</Button>
			</Card.Footer>
		{/if}
	</Card.Main>
</section>
