@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 224 71.4% 4.1%;

		--muted: 220 14.3% 95.9%;
		--muted-foreground: 220 8.9% 46.1%;

		--popover: 0 0% 100%;
		--popover-foreground: 224 71.4% 4.1%;

		--card: 0 0% 100%;
		--card-foreground: 224 71.4% 4.1%;

		--border: 220 13% 91%;
		--input: 220 13% 91%;

		--primary: 220.9 39.3% 11%;
		--primary-foreground: 210 20% 98%;

		--secondary: 220 14.3% 95.9%;
		--secondary-foreground: 220.9 39.3% 11%;

		--accent: 220 14.3% 95.9%;
		--accent-foreground: 220.9 39.3% 11%;

		--destructive: 0 72.2% 50.6%;
		--destructive-foreground: 210 20% 98%;

		--ring: 224 71.4% 4.1%;

		--radius: 0.5rem;
	}

	.dark {
		--background: 224 71.4% 4.1%;
		--foreground: 210 20% 98%;

		--muted: 215 27.9% 16.9%;
		--muted-foreground: 217.9 10.6% 64.9%;

		--popover: 224 71.4% 4.1%;
		--popover-foreground: 210 20% 98%;

		--card: 224 71.4% 4.1%;
		--card-foreground: 210 20% 98%;

		--border: 215 27.9% 16.9%;
		--input: 215 27.9% 16.9%;

		--primary: 210 20% 98%;
		--primary-foreground: 220.9 39.3% 11%;

		--secondary: 215 27.9% 16.9%;
		--secondary-foreground: 210 20% 98%;

		--accent: 215 27.9% 16.9%;
		--accent-foreground: 210 20% 98%;

		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 210 20% 98%;

		--ring: 216 12.2% 83.9%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

::-webkit-scrollbar {
	width: 0.3rem;
	height: 0.3rem;
}

::-webkit-scrollbar-thumb {
	transition: 0.3s ease all;
	border-color: transparent;
	background-color: #7289da;
	z-index: 40;
}
::-webkit-scrollbar-background {
	background-color: #282b30;
}

.h-transition {
	transition: height 0.2s ease-in-out;
}