/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.10.5
 */
import { createQuery } from '@tanstack/svelte-query';
import type {
	CreateQueryOptions,
	CreateQueryResult,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type {
	Error,
	GetTrollboxHistoryParams,
	ResponseArrayMessage,
	ResponseMessageHistoryResponse,
	SearchMessagesByContentRequest,
	SearchMessagesByTimeRangeRequest
} from './api.schemas';

/**
 * Retrieve messages that fall within the specified time range.
 * @summary Search messages by time range.
 */
export const postTrollboxFilter = (
	searchMessagesByTimeRangeRequest: SearchMessagesByTimeRangeRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseArrayMessage>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/trollbox/filter`,
		searchMessagesByTimeRangeRequest,
		options
	);
};

/**
 * Retrieve chat message history with optional pagination parameters.
 * @summary Get chat message history.
 */
export const getTrollboxHistory = (
	params?: GetTrollboxHistoryParams,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseMessageHistoryResponse>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/trollbox/history`, {
		...options,
		params: { ...params, ...options?.params }
	});
};

const getGetTrollboxHistoryQueryKey = (params?: GetTrollboxHistoryParams) => {
	return [
		`${import.meta.env.VITE_ATOMIC_BASEURL}/trollbox/history`,
		...(params ? [params] : [])
	] as const;
};

export const getGetTrollboxHistoryQueryOptions = <
	TData = Awaited<ReturnType<typeof getTrollboxHistory>>,
	TError = AxiosError<Error>
>(
	params?: GetTrollboxHistoryParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getTrollboxHistory>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetTrollboxHistoryQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getTrollboxHistory>>> = () =>
		getTrollboxHistory(params, axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getTrollboxHistory>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetTrollboxHistoryQueryResult = NonNullable<
	Awaited<ReturnType<typeof getTrollboxHistory>>
>;
export type GetTrollboxHistoryQueryError = AxiosError<Error>;

/**
 * @summary Get chat message history.
 */
export const createGetTrollboxHistory = <
	TData = Awaited<ReturnType<typeof getTrollboxHistory>>,
	TError = AxiosError<Error>
>(
	params?: GetTrollboxHistoryParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getTrollboxHistory>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetTrollboxHistoryQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Retrieve messages that match the specified content.
 * @summary Search messages by content.
 */
export const postTrollboxSearch = (
	searchMessagesByContentRequest: SearchMessagesByContentRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseArrayMessage>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/trollbox/search`,
		searchMessagesByContentRequest,
		options
	);
};
