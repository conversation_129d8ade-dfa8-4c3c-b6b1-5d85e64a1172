/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.10.5
 */
import { createQuery } from '@tanstack/svelte-query';
import type {
	CreateQueryOptions,
	CreateQueryResult,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type {
	CreateAccountRequest,
	Error,
	GetAccountsParams,
	PageResponseArrayAccount,
	ResponseAccount
} from './api.schemas';

/**
 * List accounts.
 * @summary Get a list of accounts.
 */
export const getAccounts = (
	params?: GetAccountsParams,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<PageResponseArrayAccount>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/accounts`, {
		...options,
		params: { ...params, ...options?.params }
	});
};

const getGetAccountsQueryKey = (params?: GetAccountsParams) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/accounts`, ...(params ? [params] : [])] as const;
};

export const getGetAccountsQueryOptions = <
	TData = Awaited<ReturnType<typeof getAccounts>>,
	TError = AxiosError<Error>
>(
	params?: GetAccountsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getAccounts>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetAccountsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getAccounts>>> = () =>
		getAccounts(params, axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getAccounts>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetAccountsQueryResult = NonNullable<Awaited<ReturnType<typeof getAccounts>>>;
export type GetAccountsQueryError = AxiosError<Error>;

/**
 * @summary Get a list of accounts.
 */
export const createGetAccounts = <
	TData = Awaited<ReturnType<typeof getAccounts>>,
	TError = AxiosError<Error>
>(
	params?: GetAccountsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getAccounts>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetAccountsQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Create an account by giving exchangeID and tag.
 * @summary Create an account.
 */
export const postAccounts = (
	createAccountRequest: CreateAccountRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseAccount>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/accounts`,
		createAccountRequest,
		options
	);
};

/**
 * Fetch an account by its ID.
 * @summary Get an account.
 */
export const getAccountsAccountId = (
	accountId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseAccount>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/accounts/${accountId}`, options);
};

const getGetAccountsAccountIdQueryKey = (accountId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/accounts/${accountId}`] as const;
};

export const getGetAccountsAccountIdQueryOptions = <
	TData = Awaited<ReturnType<typeof getAccountsAccountId>>,
	TError = AxiosError<Error>
>(
	accountId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getAccountsAccountId>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetAccountsAccountIdQueryKey(accountId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getAccountsAccountId>>> = () =>
		getAccountsAccountId(accountId, axiosOptions);

	return { queryKey, queryFn, enabled: !!accountId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getAccountsAccountId>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetAccountsAccountIdQueryResult = NonNullable<
	Awaited<ReturnType<typeof getAccountsAccountId>>
>;
export type GetAccountsAccountIdQueryError = AxiosError<Error>;

/**
 * @summary Get an account.
 */
export const createGetAccountsAccountId = <
	TData = Awaited<ReturnType<typeof getAccountsAccountId>>,
	TError = AxiosError<Error>
>(
	accountId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getAccountsAccountId>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetAccountsAccountIdQueryOptions(accountId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Delete an account by its ID.
 * @summary Delete an account.
 */
export const deleteAccountsAccountId = (
	accountId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<void>> => {
	return axios.delete(`${import.meta.env.VITE_ATOMIC_BASEURL}/accounts/${accountId}`, options);
};
