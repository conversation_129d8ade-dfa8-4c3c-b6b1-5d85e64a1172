/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.10.5
 */
import { createQuery } from '@tanstack/svelte-query';
import type {
	CreateQueryOptions,
	CreateQueryResult,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type {
	CreateParamGroupRequest,
	Error,
	GetGroupsParams,
	GetParamsGroupsBotsParams,
	ResponseArrayBotParamGroup,
	ResponseArrayParamGroup,
	ResponseParamGroup
} from './api.schemas';

/**
 * List param groups with optional filters and pagination
 * @summary List param groups
 */
export const getGroups = (
	params?: GetGroupsParams,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseArrayParamGroup>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/groups`, {
		...options,
		params: { ...params, ...options?.params }
	});
};

const getGetGroupsQueryKey = (params?: GetGroupsParams) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/groups`, ...(params ? [params] : [])] as const;
};

export const getGetGroupsQueryOptions = <
	TData = Awaited<ReturnType<typeof getGroups>>,
	TError = AxiosError<Error>
>(
	params?: GetGroupsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getGroups>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetGroupsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getGroups>>> = () =>
		getGroups(params, axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getGroups>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetGroupsQueryResult = NonNullable<Awaited<ReturnType<typeof getGroups>>>;
export type GetGroupsQueryError = AxiosError<Error>;

/**
 * @summary List param groups
 */
export const createGetGroups = <
	TData = Awaited<ReturnType<typeof getGroups>>,
	TError = AxiosError<Error>
>(
	params?: GetGroupsParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getGroups>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetGroupsQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Creates a param group by providing a name
 * @summary Create a param group
 */
export const postGroups = (
	createParamGroupRequest: CreateParamGroupRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseParamGroup>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/groups`,
		createParamGroupRequest,
		options
	);
};

/**
 * Delete a param group by providing its ID
 * @summary Delete a param group
 */
export const deleteGroupsGroupId = (
	groupId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<void>> => {
	return axios.delete(`${import.meta.env.VITE_ATOMIC_BASEURL}/groups/${groupId}`, options);
};

/**
 * List bot param group relations with optional filters and pagination
 * @summary List bot param group relations
 */
export const getParamsGroupsBots = (
	params?: GetParamsGroupsBotsParams,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseArrayBotParamGroup>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/params/groups/bots`, {
		...options,
		params: { ...params, ...options?.params }
	});
};

const getGetParamsGroupsBotsQueryKey = (params?: GetParamsGroupsBotsParams) => {
	return [
		`${import.meta.env.VITE_ATOMIC_BASEURL}/params/groups/bots`,
		...(params ? [params] : [])
	] as const;
};

export const getGetParamsGroupsBotsQueryOptions = <
	TData = Awaited<ReturnType<typeof getParamsGroupsBots>>,
	TError = AxiosError<Error>
>(
	params?: GetParamsGroupsBotsParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getParamsGroupsBots>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetParamsGroupsBotsQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getParamsGroupsBots>>> = () =>
		getParamsGroupsBots(params, axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getParamsGroupsBots>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetParamsGroupsBotsQueryResult = NonNullable<
	Awaited<ReturnType<typeof getParamsGroupsBots>>
>;
export type GetParamsGroupsBotsQueryError = AxiosError<Error>;

/**
 * @summary List bot param group relations
 */
export const createGetParamsGroupsBots = <
	TData = Awaited<ReturnType<typeof getParamsGroupsBots>>,
	TError = AxiosError<Error>
>(
	params?: GetParamsGroupsBotsParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getParamsGroupsBots>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetParamsGroupsBotsQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Creates relation between bot and param groups by providing bot and param group IDs
 * @summary Create bot param group relation
 */
export const postParamsGroupsGroupIdBotsBotId = (
	groupId: string,
	botId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseArrayBotParamGroup>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/params/groups/${groupId}/bots/${botId}`,
		undefined,
		options
	);
};

/**
 * Delete bot param group relations specified in the request payload
 * @summary Delete bot param group relations
 */
export const deleteParamsGroupsGroupIdBotsBotId = (
	groupId: string,
	botId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<void>> => {
	return axios.delete(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/params/groups/${groupId}/bots/${botId}`,
		options
	);
};
