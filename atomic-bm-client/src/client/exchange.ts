/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.10.5
 */
import { createQuery } from '@tanstack/svelte-query';
import type {
	CreateQueryOptions,
	CreateQueryResult,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type {
	Error,
	GetExchangesParams,
	ResponseArrayExchange,
	ResponseExchange
} from './api.schemas';

/**
 * List exchanges with optional filters and pagination
 * @summary List exchanges.
 */
export const getExchanges = (
	params?: GetExchangesParams,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseArrayExchange>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/exchanges`, {
		...options,
		params: { ...params, ...options?.params }
	});
};

const getGetExchangesQueryKey = (params?: GetExchangesParams) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/exchanges`, ...(params ? [params] : [])] as const;
};

export const getGetExchangesQueryOptions = <
	TData = Awaited<ReturnType<typeof getExchanges>>,
	TError = AxiosError<Error>
>(
	params?: GetExchangesParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getExchanges>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetExchangesQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getExchanges>>> = () =>
		getExchanges(params, axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getExchanges>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetExchangesQueryResult = NonNullable<Awaited<ReturnType<typeof getExchanges>>>;
export type GetExchangesQueryError = AxiosError<Error>;

/**
 * @summary List exchanges.
 */
export const createGetExchanges = <
	TData = Awaited<ReturnType<typeof getExchanges>>,
	TError = AxiosError<Error>
>(
	params?: GetExchangesParams,
	options?: {
		query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getExchanges>>, TError, TData>>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetExchangesQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Fetch an exchange by providing its exchangeID
 * @summary Fetch an exchange
 */
export const getExchangesExchangeId = (
	exchangeId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseExchange>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/exchanges/${exchangeId}`, options);
};

const getGetExchangesExchangeIdQueryKey = (exchangeId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/exchanges/${exchangeId}`] as const;
};

export const getGetExchangesExchangeIdQueryOptions = <
	TData = Awaited<ReturnType<typeof getExchangesExchangeId>>,
	TError = AxiosError<Error>
>(
	exchangeId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getExchangesExchangeId>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetExchangesExchangeIdQueryKey(exchangeId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getExchangesExchangeId>>> = () =>
		getExchangesExchangeId(exchangeId, axiosOptions);

	return { queryKey, queryFn, enabled: !!exchangeId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getExchangesExchangeId>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetExchangesExchangeIdQueryResult = NonNullable<
	Awaited<ReturnType<typeof getExchangesExchangeId>>
>;
export type GetExchangesExchangeIdQueryError = AxiosError<Error>;

/**
 * @summary Fetch an exchange
 */
export const createGetExchangesExchangeId = <
	TData = Awaited<ReturnType<typeof getExchangesExchangeId>>,
	TError = AxiosError<Error>
>(
	exchangeId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getExchangesExchangeId>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetExchangesExchangeIdQueryOptions(exchangeId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};
