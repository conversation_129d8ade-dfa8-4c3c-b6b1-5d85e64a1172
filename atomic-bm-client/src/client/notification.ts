/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.10.5
 */
import { createQuery } from '@tanstack/svelte-query';
import type {
	CreateQueryOptions,
	CreateQueryResult,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type {
	CreateNotificationSubscriptionRequest,
	CreatePushNotificationRequest,
	DeleteNotificationSubscriptionRequest,
	Error,
	GetNotificationsPushParams,
	GetNotificationsSubscribeParams,
	PageResponseArrayNotificationSubscription,
	PageResponseArrayPushNotification,
	ResponseArrayNotificationSubscription,
	ResponsePushNotification
} from './api.schemas';

/**
 * List registered push notifications of a user with optional filtering and ordering.
 * @summary List push notifications.
 */
export const getNotificationsPush = (
	params?: GetNotificationsPushParams,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<PageResponseArrayPushNotification>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/push`, {
		...options,
		params: { ...params, ...options?.params }
	});
};

const getGetNotificationsPushQueryKey = (params?: GetNotificationsPushParams) => {
	return [
		`${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/push`,
		...(params ? [params] : [])
	] as const;
};

export const getGetNotificationsPushQueryOptions = <
	TData = Awaited<ReturnType<typeof getNotificationsPush>>,
	TError = AxiosError<Error>
>(
	params?: GetNotificationsPushParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getNotificationsPush>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetNotificationsPushQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getNotificationsPush>>> = () =>
		getNotificationsPush(params, axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getNotificationsPush>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetNotificationsPushQueryResult = NonNullable<
	Awaited<ReturnType<typeof getNotificationsPush>>
>;
export type GetNotificationsPushQueryError = AxiosError<Error>;

/**
 * @summary List push notifications.
 */
export const createGetNotificationsPush = <
	TData = Awaited<ReturnType<typeof getNotificationsPush>>,
	TError = AxiosError<Error>
>(
	params?: GetNotificationsPushParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getNotificationsPush>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetNotificationsPushQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Register or update a push notification device by providing the device token and platform.
 * @summary Register or update a push notification device.
 */
export const postNotificationsPush = (
	createPushNotificationRequest: CreatePushNotificationRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponsePushNotification>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/push`,
		createPushNotificationRequest,
		options
	);
};

/**
 * Delete a push notification device by providing the device token.
 * @summary Delete a push notification device.
 */
export const deleteNotificationsPushDeviceToken = (
	deviceToken: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<string>> => {
	return axios.delete(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/push/${deviceToken}`,
		options
	);
};

/**
 * List notification subscriptions with optional filtering and ordering.
 * @summary List notification subscriptions.
 */
export const getNotificationsSubscribe = (
	params?: GetNotificationsSubscribeParams,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<PageResponseArrayNotificationSubscription>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/subscribe`, {
		...options,
		params: { ...params, ...options?.params }
	});
};

const getGetNotificationsSubscribeQueryKey = (params?: GetNotificationsSubscribeParams) => {
	return [
		`${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/subscribe`,
		...(params ? [params] : [])
	] as const;
};

export const getGetNotificationsSubscribeQueryOptions = <
	TData = Awaited<ReturnType<typeof getNotificationsSubscribe>>,
	TError = AxiosError<Error>
>(
	params?: GetNotificationsSubscribeParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getNotificationsSubscribe>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetNotificationsSubscribeQueryKey(params);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getNotificationsSubscribe>>> = () =>
		getNotificationsSubscribe(params, axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getNotificationsSubscribe>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetNotificationsSubscribeQueryResult = NonNullable<
	Awaited<ReturnType<typeof getNotificationsSubscribe>>
>;
export type GetNotificationsSubscribeQueryError = AxiosError<Error>;

/**
 * @summary List notification subscriptions.
 */
export const createGetNotificationsSubscribe = <
	TData = Awaited<ReturnType<typeof getNotificationsSubscribe>>,
	TError = AxiosError<Error>
>(
	params?: GetNotificationsSubscribeParams,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getNotificationsSubscribe>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetNotificationsSubscribeQueryOptions(params, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Create a new notification subscription by specifying the event type and resource ID.
 * @summary Create a notification subscription.
 */
export const postNotificationsSubscribe = (
	createNotificationSubscriptionRequest: CreateNotificationSubscriptionRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseArrayNotificationSubscription>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/subscribe`,
		createNotificationSubscriptionRequest,
		options
	);
};

/**
 * Delete notification subscriptions by providing an array of subscription IDs.
 * @summary Delete notification subscriptions.
 */
export const postNotificationsSubscribeBulkDelete = (
	deleteNotificationSubscriptionRequest: DeleteNotificationSubscriptionRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<string>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/notifications/subscribe/bulkDelete`,
		deleteNotificationSubscriptionRequest,
		options
	);
};
