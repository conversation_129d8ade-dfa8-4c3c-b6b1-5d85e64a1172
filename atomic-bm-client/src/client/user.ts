/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.10.5
 */
import { createQuery } from '@tanstack/svelte-query';
import type {
	CreateQueryOptions,
	CreateQueryResult,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type {
	ChangeUserPasswordRequest,
	Error,
	LoginRequest,
	ResponseArraySession,
	ResponseArrayUser,
	ResponseLoginResponse,
	ResponseUser
} from './api.schemas';

/**
 * Retrieves a list of users.
 * @summary Get a list of users.
 */
export const getUsersInfo = (
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseArrayUser>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/users/info`, options);
};

const getGetUsersInfoQueryKey = () => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/users/info`] as const;
};

export const getGetUsersInfoQueryOptions = <
	TData = Awaited<ReturnType<typeof getUsersInfo>>,
	TError = AxiosError<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getUsersInfo>>, TError, TData>>;
	axios?: AxiosRequestConfig;
}) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUsersInfoQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUsersInfo>>> = () =>
		getUsersInfo(axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getUsersInfo>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetUsersInfoQueryResult = NonNullable<Awaited<ReturnType<typeof getUsersInfo>>>;
export type GetUsersInfoQueryError = AxiosError<Error>;

/**
 * @summary Get a list of users.
 */
export const createGetUsersInfo = <
	TData = Awaited<ReturnType<typeof getUsersInfo>>,
	TError = AxiosError<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getUsersInfo>>, TError, TData>>;
	axios?: AxiosRequestConfig;
}): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetUsersInfoQueryOptions(options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Authenticates a user based on email and password, generating a session token.
 * @summary User login.
 */
export const postUsersLogin = (
	loginRequest: LoginRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseLoginResponse>> => {
	return axios.post(`${import.meta.env.VITE_ATOMIC_BASEURL}/users/login`, loginRequest, options);
};

/**
 * Terminates the user's session, logs them out, and disconnects any associated WebSocket connection.
 * @summary User logout.
 */
export const postUsersLogout = (options?: AxiosRequestConfig): Promise<AxiosResponse<void>> => {
	return axios.post(`${import.meta.env.VITE_ATOMIC_BASEURL}/users/logout`, undefined, options);
};

/**
 * Retrieves details of the authenticated user.
 * @summary Get user details.
 */
export const getUsersMe = (options?: AxiosRequestConfig): Promise<AxiosResponse<ResponseUser>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/users/me`, options);
};

const getGetUsersMeQueryKey = () => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/users/me`] as const;
};

export const getGetUsersMeQueryOptions = <
	TData = Awaited<ReturnType<typeof getUsersMe>>,
	TError = AxiosError<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getUsersMe>>, TError, TData>>;
	axios?: AxiosRequestConfig;
}) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUsersMeQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUsersMe>>> = () =>
		getUsersMe(axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getUsersMe>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetUsersMeQueryResult = NonNullable<Awaited<ReturnType<typeof getUsersMe>>>;
export type GetUsersMeQueryError = AxiosError<Error>;

/**
 * @summary Get user details.
 */
export const createGetUsersMe = <
	TData = Awaited<ReturnType<typeof getUsersMe>>,
	TError = AxiosError<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getUsersMe>>, TError, TData>>;
	axios?: AxiosRequestConfig;
}): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetUsersMeQueryOptions(options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Changes the user's password by verifying the old password,
validating the new password, and updating it in the database.
 * @summary Change user's password.
 */
export const patchUsersPassword = (
	changeUserPasswordRequest: ChangeUserPasswordRequest,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<void>> => {
	return axios.patch(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/users/password`,
		changeUserPasswordRequest,
		options
	);
};

/**
 * Retrieves a list of sessions associated with the authenticated user.
 * @summary Get user active sessions.
 */
export const getUsersSessionsActive = (
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseArraySession>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/users/sessions/active`, options);
};

const getGetUsersSessionsActiveQueryKey = () => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/users/sessions/active`] as const;
};

export const getGetUsersSessionsActiveQueryOptions = <
	TData = Awaited<ReturnType<typeof getUsersSessionsActive>>,
	TError = AxiosError<Error>
>(options?: {
	query?: Partial<
		CreateQueryOptions<Awaited<ReturnType<typeof getUsersSessionsActive>>, TError, TData>
	>;
	axios?: AxiosRequestConfig;
}) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetUsersSessionsActiveQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getUsersSessionsActive>>> = () =>
		getUsersSessionsActive(axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getUsersSessionsActive>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetUsersSessionsActiveQueryResult = NonNullable<
	Awaited<ReturnType<typeof getUsersSessionsActive>>
>;
export type GetUsersSessionsActiveQueryError = AxiosError<Error>;

/**
 * @summary Get user active sessions.
 */
export const createGetUsersSessionsActive = <
	TData = Awaited<ReturnType<typeof getUsersSessionsActive>>,
	TError = AxiosError<Error>
>(options?: {
	query?: Partial<
		CreateQueryOptions<Awaited<ReturnType<typeof getUsersSessionsActive>>, TError, TData>
	>;
	axios?: AxiosRequestConfig;
}): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetUsersSessionsActiveQueryOptions(options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Terminates all sessions associated with the authenticated user, logging them out,
and disconnecting any associated WebSocket connections.
 * @summary Delete all user sessions.
 */
export const postUsersSessionsBulkDelete = (
	options?: AxiosRequestConfig
): Promise<AxiosResponse<void>> => {
	return axios.post(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/users/sessions/bulkDelete`,
		undefined,
		options
	);
};

/**
 * Deletes a user session identified by the provided session ID.
 * @summary Delete a user session.
 */
export const deleteUsersSessionsSessionId = (
	sessionId: number,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<void>> => {
	return axios.delete(
		`${import.meta.env.VITE_ATOMIC_BASEURL}/users/sessions/${sessionId}`,
		options
	);
};
