/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.10.5
 */
export type GetTrollboxHistoryParams = {
	/**
	 * Cursor for pagination
	 */
	cursor?: string;
	/**
	 * Page size for pagination
	 */
	pageSize?: number;
};

export type GetParamsGroupsBotsOrderDirection =
	(typeof GetParamsGroupsBotsOrderDirection)[keyof typeof GetParamsGroupsBotsOrderDirection];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetParamsGroupsBotsOrderDirection = {
	DESC: 'DESC',
	ASC: 'ASC'
} as const;

export type GetParamsGroupsBotsOrderBy =
	(typeof GetParamsGroupsBotsOrderBy)[keyof typeof GetParamsGroupsBotsOrderBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetParamsGroupsBotsOrderBy = {
	param_group_id: 'param_group_id',
	created_by_user_id: 'created_by_user_id',
	created_at: 'created_at'
} as const;

export type GetParamsGroupsBotsParams = {
	/**
	 * Filter relations by the user who created them
	 */
	createdByUserId?: string;
	/**
	 * Filter relations by Bot ID
	 */
	botId?: string;
	/**
	 * Filter relations by param Group ID
	 */
	groupId?: string;
	/**
	 * Specifies the field by which the results should be ordered
	 */
	orderBy?: GetParamsGroupsBotsOrderBy;
	/**
	 * Sets the order direction for sorting results (ASC or DESC).
	 */
	orderDirection?: GetParamsGroupsBotsOrderDirection;
	/**
	 * Specifies the maximum number of items to return per page.
	 */
	limit?: number;
	/**
	 * Specifies the starting position of the items to be retrieved.
	 */
	offset?: number;
};

export type GetParamsOrderDirection =
	(typeof GetParamsOrderDirection)[keyof typeof GetParamsOrderDirection];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetParamsOrderDirection = {
	DESC: 'DESC',
	ASC: 'ASC'
} as const;

export type GetParamsOrderBy = (typeof GetParamsOrderBy)[keyof typeof GetParamsOrderBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetParamsOrderBy = {
	scope: 'scope',
	key: 'key',
	created_by_id: 'created_by_id',
	created_at: 'created_at'
} as const;

export type GetParamsParams = {
	/**
	 * Array of scope values for filtering
	 */
	scope?: string[];
	/**
	 * Array of scope IDs for filtering
	 */
	scopeId?: string[];
	/**
	 * Specifies the field by which the results should be ordered.
	 */
	orderBy?: GetParamsOrderBy;
	/**
	 * Sets the order direction for sorting results (ASC or DESC).
	 */
	orderDirection?: GetParamsOrderDirection;
	/**
	 * Specifies the maximum number of items to return per page.
	 */
	limit?: number;
	/**
	 * Specifies the starting position of the items to be retrieved.
	 */
	offset?: number;
};

export type GetNotificationsSubscribeOrderDirection =
	(typeof GetNotificationsSubscribeOrderDirection)[keyof typeof GetNotificationsSubscribeOrderDirection];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetNotificationsSubscribeOrderDirection = {
	DESC: 'DESC',
	ASC: 'ASC'
} as const;

export type GetNotificationsSubscribeOrderBy =
	(typeof GetNotificationsSubscribeOrderBy)[keyof typeof GetNotificationsSubscribeOrderBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetNotificationsSubscribeOrderBy = {
	eventType: 'eventType',
	created_at: 'created_at'
} as const;

export type GetNotificationsSubscribeParams = {
	/**
	 * EventType specifies which kind of event the user will receive.
	 */
	eventType?: string;
	/**
	 * ResourceID is the entity associated with the EventType this is, a bot or account.
	 */
	resourceId?: string;
	/**
	 * Specifies the field by which the results should be ordered.
	 */
	orderBy?: GetNotificationsSubscribeOrderBy;
	/**
	 * Sets the order direction for sorting results (ASC or DESC).
	 */
	orderDirection?: GetNotificationsSubscribeOrderDirection;
	/**
	 * Specifies the maximum number of items to return per page.
	 */
	limit?: number;
	/**
	 * Specifies the starting position of the items to be retrieved.
	 */
	offset?: number;
};

export type GetNotificationsPushOrderDirection =
	(typeof GetNotificationsPushOrderDirection)[keyof typeof GetNotificationsPushOrderDirection];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetNotificationsPushOrderDirection = {
	DESC: 'DESC',
	ASC: 'ASC'
} as const;

export type GetNotificationsPushOrderBy =
	(typeof GetNotificationsPushOrderBy)[keyof typeof GetNotificationsPushOrderBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetNotificationsPushOrderBy = {
	platform: 'platform',
	created_at: 'created_at'
} as const;

export type GetNotificationsPushParams = {
	/**
	 * Platform which the token was subscribed
	 */
	platform?: string;
	/**
	 * Specifies the field by which the results should be ordered.
	 */
	orderBy?: GetNotificationsPushOrderBy;
	/**
	 * Sets the order direction for sorting results (ASC or DESC).
	 */
	orderDirection?: GetNotificationsPushOrderDirection;
	/**
	 * Specifies the maximum number of items to return per page.
	 */
	limit?: number;
	/**
	 * Specifies the starting position of the items to be retrieved.
	 */
	offset?: number;
};

export type GetGroupsOrderDirection =
	(typeof GetGroupsOrderDirection)[keyof typeof GetGroupsOrderDirection];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetGroupsOrderDirection = {
	DESC: 'DESC',
	ASC: 'ASC'
} as const;

export type GetGroupsOrderBy = (typeof GetGroupsOrderBy)[keyof typeof GetGroupsOrderBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetGroupsOrderBy = {
	name: 'name',
	created_by_user_id: 'created_by_user_id',
	created_at: 'created_at'
} as const;

export type GetGroupsParams = {
	/**
	 * Filter param groups by Name
	 */
	name?: string;
	/**
	 * Filter by user that created this group
	 */
	createdByUserId?: string;
	/**
	 * Specifies the field by which the results should be ordered.
	 */
	orderBy?: GetGroupsOrderBy;
	/**
	 * Sets the order direction for sorting results (ASC or DESC).
	 */
	orderDirection?: GetGroupsOrderDirection;
	/**
	 * Specifies the maximum number of items to return per page.
	 */
	limit?: number;
	/**
	 * Specifies the starting position of the items to be retrieved.
	 */
	offset?: number;
};

export type GetExchangesOrderDirection =
	(typeof GetExchangesOrderDirection)[keyof typeof GetExchangesOrderDirection];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetExchangesOrderDirection = {
	DESC: 'DESC',
	ASC: 'ASC'
} as const;

export type GetExchangesOrderBy = (typeof GetExchangesOrderBy)[keyof typeof GetExchangesOrderBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetExchangesOrderBy = {
	exchange_id: 'exchange_id',
	name: 'name',
	created_at: 'created_at'
} as const;

export type GetExchangesParams = {
	/**
	 * Exchange id to filter the results
	 */
	exchangeId?: string;
	/**
	 * Name to filter the results
	 */
	name?: string;
	/**
	 * Field, by which the results should be ordered
	 */
	orderBy?: GetExchangesOrderBy;
	/**
	 * Sets the order direction for sorting results (ASC or DESC).
	 */
	orderDirection?: GetExchangesOrderDirection;
	/**
	 * Specifies the maximum number of items to return per page.
	 */
	limit?: number;
	/**
	 * Specifies the starting position of the items to be retrieved.
	 */
	offset?: number;
};

export type GetBotsOrderDirection =
	(typeof GetBotsOrderDirection)[keyof typeof GetBotsOrderDirection];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetBotsOrderDirection = {
	DESC: 'DESC',
	ASC: 'ASC'
} as const;

export type GetBotsOrderBy = (typeof GetBotsOrderBy)[keyof typeof GetBotsOrderBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetBotsOrderBy = {
	account_id: 'account_id',
	symbol: 'symbol',
	region: 'region',
	status: 'status',
	created_at: 'created_at'
} as const;

export type GetBotsParams = {
	/**
	 * AccountID filter bots by Account ID
	 */
	accountId?: string;
	/**
	 * Symbol filter bots by its symbol
	 */
	symbol?: string;
	/**
	 * RegionID filter bots by is region
	 */
	regionId?: string;
	/**
	 * Status filter bots by status
	 */
	status?: string;
	/**
	 * Indicates whether deleted items should be included.
	 */
	isDeleted?: boolean;
	/**
	 * Specifies the field by which the results should be ordered.
	 */
	orderBy?: GetBotsOrderBy;
	/**
	 * Sets the order direction for sorting results (ASC or DESC).
	 */
	orderDirection?: GetBotsOrderDirection;
	/**
	 * Specifies the maximum number of items to return per page.
	 */
	limit?: number;
	/**
	 * Specifies the starting position of the items to be retrieved.
	 */
	offset?: number;
	/**
	 * Active filter bots by his state
	 */
	active?: string;
};

export type GetAccountsOrderDirection =
	(typeof GetAccountsOrderDirection)[keyof typeof GetAccountsOrderDirection];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetAccountsOrderDirection = {
	DESC: 'DESC',
	ASC: 'ASC'
} as const;

export type GetAccountsOrderBy = (typeof GetAccountsOrderBy)[keyof typeof GetAccountsOrderBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GetAccountsOrderBy = {
	exchange_id: 'exchange_id',
	tag: 'tag',
	created_at: 'created_at',
	updated_at: 'updated_at',
	deleted_at: 'deleted_at'
} as const;

export type GetAccountsParams = {
	/**
	 * Specifies the identifier of the exchange (e.g., "Binance").
	 */
	exchangeId?: string;
	/**
	 * RegionID region where account operates
	 */
	regionId?: string;
	/**
	 * Filter accounts based on a specific tag (e.g., "atomic-mm").
	 */
	tag?: string;
	/**
	 * Indicates whether deleted items should be included.
	 */
	isDeleted?: boolean;
	/**
	 * Specifies the field by which the results should be ordered.
	 */
	orderBy?: GetAccountsOrderBy;
	/**
	 * Sets the order direction for sorting results (ASC or DESC).
	 */
	orderDirection?: GetAccountsOrderDirection;
	/**
	 * Specifies the maximum number of items to return per page.
	 */
	limit?: number;
	/**
	 * Specifies the starting position of the items to be retrieved.
	 */
	offset?: number;
};

/**
 * User information about individuals.
 */
export interface User {
	/** Time when the user was created */
	createdAt: string;
	/** User's email address */
	email: string;
	/** User ID */
	id: string;
	/** User's full name */
	name: string;
	/** User's role in the system */
	role: string;
	/** Last time when the user was modified */
	updatedAt: string;
}

/**
 * Request parameters for updating an existing bot by providing an account ID, symbol, region or status.
 */
export interface UpdateBotRequest {
	/** Account ID associated with the bot */
	accountId?: string;
	/** RegionID information for the bot */
	regionId?: string;
	/** Symbol associated with the bot */
	symbol?: string;
	/** Tag used to identify the bot */
	tag?: string;
}

/**
 * User session details including session ID, user ID, device information, and location.
 */
export interface Session {
	/** Time when the session was created */
	createdAt: string;
	/** Session details including IP, device type, device name, platform type, and location */
	details: Details;
	/** Session expiration time */
	expiry: string;
	/** Session ID */
	id: number;
	/** Last time when the session was updated */
	updatedAt: string;
	/** User ID associated with the session */
	userID: string;
}

/**
 * SearchMessagesByTimeRangeRequest is used to specify the time range for filtering messages.
 */
export interface SearchMessagesByTimeRangeRequest {
	/** End time of the search range */
	endTime: string;
	/** Start time of the search range */
	startTime: string;
}

/**
 * SearchMessagesByContentRequest is used to specify the content for filtering messages.
 */
export interface SearchMessagesByContentRequest {
	/** Content to search for in messages */
	content: string;
}

/**
 * Payload containing the scope ID and parameters for a single scope.
 */
export interface ScopeParamsPayload {
	/** Array of parameter key-value pairs */
	params: ParamRequest[];
	/** Scope ID for the parameters */
	scopeId: string;
}

export interface ResponseArrayUser {
	/** The Data field carries the payload of the API response. */
	data: User[];
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseArraySession {
	/** The Data field carries the payload of the API response. */
	data: Session[];
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseArrayParamGroup {
	/** The Data field carries the payload of the API response. */
	data: ParamGroup[];
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseArrayParam {
	/** The Data field carries the payload of the API response. */
	data: Param[];
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseArrayNotificationSubscription {
	/** The Data field carries the payload of the API response. */
	data: NotificationSubscription[];
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseArrayMessage {
	/** The Data field carries the payload of the API response. */
	data: Message[];
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseArrayListBotsStateResponse {
	/** The Data field carries the payload of the API response. */
	data: ListBotsStateResponse[];
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseArrayExchange {
	/** The Data field carries the payload of the API response. */
	data: Exchange[];
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseArrayBotParamGroup {
	/** The Data field carries the payload of the API response. */
	data: BotParamGroup[];
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseUser {
	/** The Data field carries the payload of the API response. */
	data: User;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseRegion {
	/** The Data field carries the payload of the API response. */
	data: Region;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponsePushNotification {
	/** The Data field carries the payload of the API response. */
	data: PushNotification;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseParamGroup {
	/** The Data field carries the payload of the API response. */
	data: ParamGroup;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseMessageHistoryResponse {
	/** The Data field carries the payload of the API response. */
	data: MessageHistoryResponse;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseLoginResponse {
	/** The Data field carries the payload of the API response. */
	data: LoginResponse;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseGetBotStateResponse {
	/** The Data field carries the payload of the API response. */
	data: GetBotStateResponse;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseExchange {
	/** The Data field carries the payload of the API response. */
	data: Exchange;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseBot {
	/** The Data field carries the payload of the API response. */
	data: Bot;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface ResponseAccount {
	/** The Data field carries the payload of the API response. */
	data: Account;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
}

export interface Region {
	/** When the region was created */
	createdAt: string;
	/** Description about this region */
	description: string;
	/** Region ID */
	id: string;
	/** Secret used by this region */
	secretKey: string;
}

/**
 * Model for a push notification.
 */
export interface PushNotification {
	/** Time when the push notification was created */
	createdAt: string;
	/** Device token for the push notification */
	deviceToken: string;
	/** ID of the push notification */
	id: string;
	/** Platform of the device (web, ios, android) */
	platform: string;
	/** Time when the push notification was last updated */
	updatedAt: string;
	/** User ID associated with the push notification */
	userId: string;
}

/**
 * Request payload for creating a parameter.
 */
export interface ParamRequest {
	/** Key of the parameter */
	key: string;
	/** Value of the parameter */
	value: string;
}

export interface ParamGroup {
	createdAt: string;
	createdByUserID: string;
	id: string;
	name: string;
	priority: number;
}

/**
 * Model for parameters.
 */
export interface Param {
	/** Time when the parameter was created */
	createdAt: string;
	/** ID of the user who created the parameter */
	createdById: string;
	/** Key of the parameter */
	key: string;
	/** Scope of the parameter (bot, account, gateway) */
	scope: string;
	/** Scope ID associated with the parameter */
	scopeId: string;
	/** Value of the parameter */
	value: string;
	/** Version of the parameter */
	version: number;
}

export interface PageResponseArrayRegion {
	/** The Data field carries the payload of the API response. */
	data: Region[];
	/** Limit is the size of items in the data response. */
	limit: number;
	/** Offset is the starting index of items in the data response.
It represents the position from which items are retrieved in a paginated result set. */
	offset: number;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
	/** Total indicates the total number of items available, if applicable. */
	total: number;
}

export interface PageResponseArrayPushNotification {
	/** The Data field carries the payload of the API response. */
	data: PushNotification[];
	/** Limit is the size of items in the data response. */
	limit: number;
	/** Offset is the starting index of items in the data response.
It represents the position from which items are retrieved in a paginated result set. */
	offset: number;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
	/** Total indicates the total number of items available, if applicable. */
	total: number;
}

export interface PageResponseArrayParam {
	/** The Data field carries the payload of the API response. */
	data: Param[];
	/** Limit is the size of items in the data response. */
	limit: number;
	/** Offset is the starting index of items in the data response.
It represents the position from which items are retrieved in a paginated result set. */
	offset: number;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
	/** Total indicates the total number of items available, if applicable. */
	total: number;
}

export interface PageResponseArrayBot {
	/** The Data field carries the payload of the API response. */
	data: Bot[];
	/** Limit is the size of items in the data response. */
	limit: number;
	/** Offset is the starting index of items in the data response.
It represents the position from which items are retrieved in a paginated result set. */
	offset: number;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
	/** Total indicates the total number of items available, if applicable. */
	total: number;
}

export interface PageResponseArrayAccount {
	/** The Data field carries the payload of the API response. */
	data: Account[];
	/** Limit is the size of items in the data response. */
	limit: number;
	/** Offset is the starting index of items in the data response.
It represents the position from which items are retrieved in a paginated result set. */
	offset: number;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
	/** Total indicates the total number of items available, if applicable. */
	total: number;
}

/**
 * Model for a notification subscription.
 */
export interface NotificationSubscription {
	/** Time when the subscription was created */
	createdAt: string;
	/** Type of event associated with the subscription */
	eventType: string;
	/** ID of the notification subscription */
	id: number;
	/** Resource ID connected to the subscription */
	resourceId: string;
	/** User ID linked to the subscription */
	userId: string;
}

export interface PageResponseArrayNotificationSubscription {
	/** The Data field carries the payload of the API response. */
	data: NotificationSubscription[];
	/** Limit is the size of items in the data response. */
	limit: number;
	/** Offset is the starting index of items in the data response.
It represents the position from which items are retrieved in a paginated result set. */
	offset: number;
	/** The Status field denotes the HTTP status code of the response. */
	status: number;
	/** Total indicates the total number of items available, if applicable. */
	total: number;
}

/**
 * Message structure for chat messages
 */
export interface Message {
	/** Content of the message */
	content: string;
	/** Message ID */
	id: string;
	/** Sequence number of the message */
	seq: number;
	/** Timestamp when the message was sent */
	timestamp: string;
	/** User ID associated with the message */
	userId: string;
}

/**
 * MessageHistoryResponse is the response structure for retrieving message history.
 */
export interface MessageHistoryResponse {
	/** Indicates whether there is more history available */
	hashNext: boolean;
	/** List of messages in the history */
	messages: Message[];
	/** Cursor for the next page of history */
	nextCursor: string;
}

/**
 * Response payload for a successful user login.
 */
export interface LoginResponse {
	/** Token expiration time */
	expiry: string;
	/** Session token */
	token: string;
}

/**
 * Request payload for user login.
 */
export interface LoginRequest {
	/** User's email address */
	email: string;
	/** User's password */
	password: string;
}

/**
 * Location details including country, city, and area.
 */
export interface Location {
	/** Area name */
	area: string;
	/** City name */
	city: string;
	/** Country name */
	country: string;
}

/**
 * A map containing the bot's configuration parameters.
 */
export type ListBotsStateResponseParams = { [key: string]: unknown };

/**
 * A map containing the bot's current operational data.
 */
export type ListBotsStateResponseData = { [key: string]: unknown };

/**
 * Contains the detailed state information for a bot, including its ID, operational data, parameters, and the time the state was captured.
 */
export interface ListBotsStateResponse {
	/** The unique identifier of the bot. */
	botId: string;
	/** A map containing the bot's current operational data. */
	data: ListBotsStateResponseData;
	/** A map containing the bot's configuration parameters. */
	params: ListBotsStateResponseParams;
	/** The timestamp when the state was recorded. */
	time: string;
}

/**
 * A map containing the bot's configuration parameters.
 */
export type GetBotStateResponseParams = { [key: string]: unknown };

/**
 * A map containing the bot's current operational data.
 */
export type GetBotStateResponseData = { [key: string]: unknown };

/**
 * Contains the detailed state information for a specific bot, including its ID, operational data, parameters, and the time the state was captured.
 */
export interface GetBotStateResponse {
	/** The unique identifier of the bot. */
	botId: string;
	/** A map containing the bot's current operational data. */
	data: GetBotStateResponseData;
	/** A map containing the bot's configuration parameters. */
	params: GetBotStateResponseParams;
	/** The timestamp when the state was recorded. */
	time: string;
}

/**
 * Model for exchange
 */
export interface Exchange {
	/** Time when the parameter was created */
	createdAt: string;
	/** Exchange identifier */
	exchangeId: string;
	/** Pretty name of the exchange */
	name: string;
}

/**
 * Details field for additional error details
 */
export type ErrorDetails = { [key: string]: string };

/**
 * Error is the basic structure used by our API
 */
export interface Error {
	/** Code is a section to identifier the module of the error */
	code: string;
	/** Details field for additional error details */
	details?: ErrorDetails;
	/** Message describing the error response if exists */
	message: string;
	/** Status is the HTTP status code */
	status: number;
}

/**
 * Type of platform (e.g., iOS, Android, Web)
 */
export type DetailsPlatformType = (typeof DetailsPlatformType)[keyof typeof DetailsPlatformType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DetailsPlatformType = {
	ios: 'ios',
	android: 'android',
	web: 'web'
} as const;

/**
 * Type of device (e.g., mobile, desktop)
 */
export type DetailsDeviceType = (typeof DetailsDeviceType)[keyof typeof DetailsDeviceType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DetailsDeviceType = {
	mobile: 'mobile',
	desktop: 'desktop'
} as const;

/**
 * Additional details about a user session, including IP, device type, device name, platform type, and location.
 */
export interface Details {
	/** Name of the device */
	deviceName: string;
	/** Type of device (e.g., mobile, desktop) */
	deviceType: DetailsDeviceType;
	/** IP address of the device */
	ip: string;
	/** Location details including country, city, and area */
	location: Location;
	/** Type of platform (e.g., iOS, Android, Web) */
	platformType: DetailsPlatformType;
}

/**
 * Request payload for deleting parameters for a specific scope.
 */
export interface DeleteParamsRequest {
	/** Array of parameter requests */
	keys: string[];
	/** Scope of the parameters (bot, account, gateway) */
	scope: string;
	/** Scope ID for the parameters */
	scopeId: string;
}

/**
 * Request payload for deleting notification subscriptions.
 */
export interface DeleteNotificationSubscriptionRequest {
	/** Array of subscription IDs to be deleted */
	subscriptionIds: number[];
}

/**
 * Platform of the device (web, ios, android)
 */
export type CreatePushNotificationRequestPlatform =
	(typeof CreatePushNotificationRequestPlatform)[keyof typeof CreatePushNotificationRequestPlatform];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CreatePushNotificationRequestPlatform = {
	web: 'web',
	ios: 'ios',
	android: 'android'
} as const;

/**
 * Request payload for creating or updating a push notification.
 */
export interface CreatePushNotificationRequest {
	/** Device token for the push notification */
	deviceToken: string;
	/** Platform of the device (web, ios, android) */
	platform: CreatePushNotificationRequestPlatform;
}

/**
 * Request payload for creating parameters, organized by scope.
 */
export interface CreateParamsRequest {
	/** Parameters for the 'account' scope */
	account?: ScopeParamsPayload;
	/** Parameters for the 'bot' scope */
	bot?: ScopeParamsPayload;
	/** Parameters for the 'gateway' scope */
	gateway?: ScopeParamsPayload;
	/** Parameters for the 'param_group' scope */
	param_group?: ScopeParamsPayload;
}

/**
 * Request payload for creating a param group
 */
export interface CreateParamGroupRequest {
	/** Name of the param group */
	name: string;
	/** Priority is used to define the precedence order when there two groups with same key value */
	priority: number;
}

export type CreateNotificationSubscriptionRequestSubscriptionsItem = {
	/** Type of event for the subscription */
	eventType: string;
	/** Resource ID associated with the subscription */
	resourceID: string;
};

/**
 * Request payload for creating a notification subscription.
 */
export interface CreateNotificationSubscriptionRequest {
	subscriptions: CreateNotificationSubscriptionRequestSubscriptionsItem[];
}

/**
 * Request payload for creating a new bot with account ID, symbol, region, and tag.
 */
export interface CreateBotRequest {
	/** Account ID associated with the bot */
	accountId: string;
	/** Symbol associated with the bot */
	symbol: string;
	/** Tag used to identify the bot */
	tag: string;
}

/**
 * Request payload for creating an account.
 */
export interface CreateAccountRequest {
	/** Exchange id that is managed by the account */
	exchangeId: string;
	/** RegionID region where account operates */
	regionId: string;
	/** Tag used to identify a unique account with an exchange */
	tag: string;
}

/**
 * Request payload for changing a user's password.
 */
export interface ChangeUserPasswordRequest {
	/** User's current password */
	currentPassword: string;
	/** User's new password must be greater than eight characters */
	newPassword: string;
}

export interface BotParamGroup {
	botId: string;
	createdAt: string;
	createdByUserID: string;
	groupId: string;
}

/**
 * Bot entity with details such as ID, account ID, symbol, region, tag, status, and dates.
 */
export interface Bot {
	/** Account ID associated with the bot */
	accountId: string;
	/** Date when the bot was created */
	createdAt: string;
	/** Date when the bot was deleted (if applicable) */
	deletedAt?: string;
	/** DesiredStatus used to identify the desired state of the bot */
	desiredStatus: string;
	/** Bot ID */
	id: string;
	/** Status of the bot */
	status: string;
	/** Symbol associated with the bot */
	symbol: string;
	/** Tag used to identify the bot */
	tag: string;
	/** Date when the bot was last updated */
	updatedAt: string;
}

/**
 * Accounts is responsible for managing exchanges
 */
export interface Account {
	/** When an account was created */
	createdAt: string;
	/** Time when the account was deleted */
	deletedAt?: string;
	/** Exchange id that is managed by the account */
	exchangeId: string;
	/** Account UUID */
	id: string;
	/** RegionID information about which region this account operates */
	regionId: string;
	/** Tag used to identify a unique account with an exchange */
	tag: string;
	/** Last time when the account was modified */
	updatedAt: string;
}
