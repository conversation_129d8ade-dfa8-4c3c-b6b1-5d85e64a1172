/**
 * Generated by orval v6.31.0 🍺
 * Do not edit manually.
 * Bot Manager API
 * API for managing cryptocurrency trading bots on multiple exchanges.
 * OpenAPI spec version: 1.10.5
 */
import { createQuery } from '@tanstack/svelte-query';
import type {
	CreateQueryOptions,
	CreateQueryResult,
	QueryFunction,
	QueryKey
} from '@tanstack/svelte-query';
import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { Error, PageResponseArrayRegion, ResponseRegion } from './api.schemas';

/**
 * List regions.
 * @summary Fetch a list of regions.
 */
export const getRegions = (
	options?: AxiosRequestConfig
): Promise<AxiosResponse<PageResponseArrayRegion>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/regions`, options);
};

const getGetRegionsQueryKey = () => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/regions`] as const;
};

export const getGetRegionsQueryOptions = <
	TData = Awaited<ReturnType<typeof getRegions>>,
	TError = AxiosError<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getRegions>>, TError, TData>>;
	axios?: AxiosRequestConfig;
}) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetRegionsQueryKey();

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getRegions>>> = () =>
		getRegions(axiosOptions);

	return { queryKey, queryFn, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getRegions>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetRegionsQueryResult = NonNullable<Awaited<ReturnType<typeof getRegions>>>;
export type GetRegionsQueryError = AxiosError<Error>;

/**
 * @summary Fetch a list of regions.
 */
export const createGetRegions = <
	TData = Awaited<ReturnType<typeof getRegions>>,
	TError = AxiosError<Error>
>(options?: {
	query?: Partial<CreateQueryOptions<Awaited<ReturnType<typeof getRegions>>, TError, TData>>;
	axios?: AxiosRequestConfig;
}): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetRegionsQueryOptions(options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};

/**
 * Fetch a region by its id.
 * @summary Fetch a region by its id.
 */
export const getRegionsRegionId = (
	regionId: string,
	options?: AxiosRequestConfig
): Promise<AxiosResponse<ResponseRegion>> => {
	return axios.get(`${import.meta.env.VITE_ATOMIC_BASEURL}/regions/${regionId}`, options);
};

const getGetRegionsRegionIdQueryKey = (regionId: string) => {
	return [`${import.meta.env.VITE_ATOMIC_BASEURL}/regions/${regionId}`] as const;
};

export const getGetRegionsRegionIdQueryOptions = <
	TData = Awaited<ReturnType<typeof getRegionsRegionId>>,
	TError = AxiosError<Error>
>(
	regionId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getRegionsRegionId>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
) => {
	const { query: queryOptions, axios: axiosOptions } = options ?? {};

	const queryKey = queryOptions?.queryKey ?? getGetRegionsRegionIdQueryKey(regionId);

	const queryFn: QueryFunction<Awaited<ReturnType<typeof getRegionsRegionId>>> = () =>
		getRegionsRegionId(regionId, axiosOptions);

	return { queryKey, queryFn, enabled: !!regionId, ...queryOptions } as CreateQueryOptions<
		Awaited<ReturnType<typeof getRegionsRegionId>>,
		TError,
		TData
	> & { queryKey: QueryKey };
};

export type GetRegionsRegionIdQueryResult = NonNullable<
	Awaited<ReturnType<typeof getRegionsRegionId>>
>;
export type GetRegionsRegionIdQueryError = AxiosError<Error>;

/**
 * @summary Fetch a region by its id.
 */
export const createGetRegionsRegionId = <
	TData = Awaited<ReturnType<typeof getRegionsRegionId>>,
	TError = AxiosError<Error>
>(
	regionId: string,
	options?: {
		query?: Partial<
			CreateQueryOptions<Awaited<ReturnType<typeof getRegionsRegionId>>, TError, TData>
		>;
		axios?: AxiosRequestConfig;
	}
): CreateQueryResult<TData, TError> & { queryKey: QueryKey } => {
	const queryOptions = getGetRegionsRegionIdQueryOptions(regionId, options);

	const query = createQuery(queryOptions) as CreateQueryResult<TData, TError> & {
		queryKey: QueryKey;
	};

	query.queryKey = queryOptions.queryKey;

	return query;
};
