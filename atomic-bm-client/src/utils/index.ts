import t from 'svelte-french-toast';
import { page } from '$app/stores';
import { goto } from '$app/navigation';
import { get } from 'svelte/store';
import { setToken } from '$stores/auth-store';
import type { AxiosError } from 'axios';
import { tick } from 'svelte';
import { atws } from '$api/centrifuge';

/**
 * Displays a toast notification with an atomic design style.
 *
 * @param {string} message - The message to display in the toast.
 * @param {string} type - The type of toast ('success', 'error', 'warning', 'info').
 */
export function toast(message: string, type: string) {
	const colors: { [key: string]: string } = {
		success: '#66d998',
		error: '#f87171',
		warning: '#faad6c',
		info: '#a3bffe'
	};

	t(message, {
		style: `background: #14161f; color: #fff; font-size: 0.9rem; border: 1px solid ${colors[type]}; padding: 0.1rem; border-radius: 0px; color: ${colors[type]};`
	});
}

/**
 * Navigates to a new tab with the specified name and path.
 *
 * @param {string} name - The name of the tab.
 * @param {string} path - The path to navigate to.
 * @param {boolean} [newTab] - Whether to open the tab in a new window (optional).
 * @param {boolean} [keepName] - Whether to keep the current tab name (optional).
 * @return {void} This function does not return anything.
 */
export function tabNavigate(name: string, path: string, newTab?: boolean, keepName?: boolean) {
	goto(
		`${get(page).url.pathname}?navigating=true&name=${name}&path=${path}${newTab ? '&new=true' : ''}${keepName ? '&keepName=true' : ''}`
	);
}

/**
 * Asynchronously retrieves the content from the clipboard.
 *
 * @return {Promise<string | null>} A promise that resolves with the clipboard text if the permission is granted or prompted,
 * and rejects with an error if the permission is denied. If an error occurs during the retrieval process, it logs the error
 * and returns null.
 */
export async function getContentFromClipboard(): Promise<string | null> {
	try {
		const permission = await navigator.permissions.query({
			name: 'clipboard-read' as PermissionName
		});
		if (permission.state === 'granted' || permission.state === 'prompt') {
			const clipboardText = await navigator.clipboard.readText();
			return clipboardText;
		} else {
			throw new Error('Permission to read from clipboard denied');
		}
	} catch (error) {
		console.error('Failed to read from clipboard:', error);
		return null;
	}
}

/**
 * Updates the order numbers of the elements in the given array.
 *
 * @param {any[]} array - The array to update the order numbers of.
 * @return {any[]} - The updated array with the order numbers updated.
 */
export function updateOrderNumbers(array: any): any[] {
	array.forEach((item: any, index: any) => {
		item.order = index + 1;
	});
	return array;
}

/**
 * Saves the provided data to the local storage with the specified key.
 *
 * @param {string} key - The key to associate the data with in the local storage.
 * @param {any} data - The data to be saved in the local storage.
 * @return {void} This function does not return anything.
 */
export function saveStorage(key: string, data: any): void {
	localStorage.setItem(key, JSON.stringify(data));
}

/**
 * Retrieves the value associated with the specified key from the local storage and parses it as JSON.
 *
 * @param {string} key - The key to retrieve the value from the local storage.
 * @return {any} The parsed JSON value associated with the specified key, or null if the key does not exist in the local storage.
 */
export function getStorage(key: string): any {
	return JSON.parse(localStorage.getItem(key) as string);
}

/**
 * Removes the value associated with the specified key from the local storage.
 *
 * @param {string} key - The key to remove the value from the local storage.
 * @return {void} This function does not return anything.
 */
export function removeStorage(key: string): void {
	localStorage.removeItem(key);
}

/**
 * Clears the token, navigates to the unauthorized page, and removes the token and expiry from local storage.
 *
 * @param {boolean} [isLogout=false] - Indicates if the logout is initiated by the user.
 * @return {void} This function does not return anything.
 */
export function clearApp(isLogout: boolean = false): void {
	const gotoMessage = isLogout ? '/?logout=true' : '/?unauthorized=true';
	setToken('', '');
	goto(gotoMessage);
	removeStorage('token');
	removeStorage('expiry');
}

/**
 * Checks the authentication status by retrieving the token and clearing the app if the token is not present.
 *
 * @return {void} This function does not return anything.
 */
export function checkAuth(): void {
	const token = getStorage('token');
	if (!token) {
		clearApp();
	}
}

/**
 * Handles an error by displaying a toast message and performing additional actions based on the error status.
 *
 * @param {unknown} e - The error object to handle.
 * @param {string} [toastMsg] - An optional toast message to display.
 * @return {void} This function does not return anything.
 */
export function handleErr(e: unknown, toastMsg?: string) {
	const err = e as AxiosError;
	if (err.code === 'ERR_NETWORK') {
		clearApp();
		toast('Network error, please try again or check your internet connection', 'error');
		return;
	}
	toastMsg && toast(toastMsg, 'error');
	if (err?.response?.status === 403) {
		clearApp();
	} else if (err?.response?.status === 400) {
		// @ts-ignore
		toast(err?.response?.data?.message, 'error');
	} else if (err?.response?.status === 500) {
		toast('Internal server error', 'error');
	}
}

/**
 * Formats a common date string into a specific format.
 *
 * @param {string} dateString - The date string to be formatted.
 * @param {boolean} [fullDate] - Optional. If true, includes the hours and minutes in the format. Defaults to false.
 * @return {string} The formatted date string in the format "HH:MM - DD/MM/YYYY" or "DD/MM/YYYY".
 */
export function formatCommonDate(dateString: string, fullDate?: boolean) {
	// Parse the date string to a Date object
	let date: Date;
	if (!dateString?.includes('T')) {
		date = new Date(parseInt(dateString) * 1000);
	} else {
		date = new Date(dateString);
	}

	// Extract components
	const hours = date.getUTCHours().toString().padStart(2, '0');
	const minutes = date.getUTCMinutes().toString().padStart(2, '0');
	const day = date.getUTCDate().toString().padStart(2, '0');
	const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
	const year = date.getUTCFullYear();

	// Format the date as "HH:MM - DD/MM/YYYY"
	return fullDate ? `${hours}:${minutes} - ${day}/${month}/${year}` : `${day}/${month}/${year}`;
}

/**
 * Persists the value of an input field to local storage.
 *
 * @param {Event} e - The event object triggered by the input field.
 * @param {string} keyName - The name of the key to be used in local storage.
 * @return {Promise<string>} - A promise that resolves to the value of the input field.
 */
export async function persistData(
	e: Event | undefined,
	keyName: string,
	isAnyValue?: boolean,
	value?: any
): Promise<string> {
	const target = e?.target as HTMLInputElement;
	await tick();
	if (isAnyValue) {
		saveStorage(`${get(page).params.tab_id}-${keyName}`, value);
	} else {
		saveStorage(`${get(page).params.tab_id}-${keyName}`, target.value);
	}

	return isAnyValue ? value : target.value;
}

/**
 * Clears the persisted data associated with a specific key from local storage.
 *
 * @param {string} keyName - The name of the key to be cleared.
 * @return {void} This function does not return anything.
 */
export function clearPersistData(keyName: string) {
	removeStorage(`${get(page).params.tab_id}-${keyName}`);
}

/**
 * Retrieves the persisted data associated with a specific key from local storage.
 *
 * @param {string} keyName - The name of the key to retrieve the value from local storage.
 * @return {any} The value associated with the specified key, or undefined if the key does not exist.
 */
export function getPersistData(keyName: string) {
	const accountStorageValue = getStorage(`${get(page).params.tab_id}-${keyName}`);
	if (accountStorageValue) return accountStorageValue;
}

/**
 * Checks if any key in the given object has a truthy value.
 *
 * @param {any} obj - The object to check.
 * @return {boolean} True if any key has a truthy value, false otherwise.
 */
export function checkObjectKeys(obj: any) {
	for (let key in obj) {
		if (obj.hasOwnProperty(key) && key !== 'offset' && key !== 'limit' && obj[key]) {
			return true;
		}
	}
	return false;
}

/**
 * Adds an event listener to the document that triggers a custom event when a click
 * occurs outside of the specified node. The custom event is dispatched to the node.
 *
 * @param {any} node - The node to check for clicks outside of.
 * @return {Object} An object with a `destroy` method that removes the event listener.
 */
export function clickOutside(node: any) {
	const handleClick = (event: any) => {
		if (node && !node.contains(event.target) && !event.defaultPrevented) {
			node.dispatchEvent(new CustomEvent('click_outside', node));
		}
	};
	document.addEventListener('click', handleClick, true);

	return {
		destroy() {
			document.removeEventListener('click', handleClick, true);
		}
	};
}

/**
 * Adds the specified number of minutes to the current date and returns the resulting date in a formatted string.
 *
 * @param {string} minutesAsString - The number of minutes to add as a string.
 * @return {string} The formatted string representing the new date.
 */
export function addMinutesToCurrentDate(minutesAsString: string): string {
	const minutesToAdd = parseInt(minutesAsString, 10);

	if (isNaN(minutesToAdd)) {
		throw new Error('Invalid input: the provided string is not a valid number.');
	}

	const currentDate = new Date();
	const newDate = new Date(currentDate.getTime() + minutesToAdd * 60000);
	const year = newDate.getFullYear();
	const month = ('0' + (newDate.getMonth() + 1)).slice(-2);
	const day = ('0' + newDate.getDate()).slice(-2);
	const hours = ('0' + newDate.getHours()).slice(-2);
	const minutes = ('0' + newDate.getMinutes()).slice(-2);
	const seconds = ('0' + newDate.getSeconds()).slice(-2);

	const newDateString = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;

	return newDateString;
}

/**
 * Converts a data URL to a Blob object.
 *
 * @param {string} dataUrl - The data URL to convert.
 * @return {Blob} The resulting Blob object.
 */
export function nodeURLToCommonBlob(dataUrl: string): Blob {
	const arr = dataUrl.split(',');
	const mime = arr[0].match(/:(.*?);/)![1];
	const bstr = atob(arr[1]);
	let n = bstr.length;
	const u8arr = new Uint8Array(n);

	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}

	return new Blob([u8arr], { type: mime });
}

export async function uploadSnapshot(file: Blob, createdAt: string) {
	try {
		const res = await fetch(`/api/imagekit?createdAt=${createdAt}`, {
			method: 'POST',
			body: file,
			headers: {
				'Content-Type': 'application/blob'
			}
		});
		if (res.ok) {
			const data = await res.json();
			return {
				result: data,
				error: null
			};
		} else {
			return {
				result: null,
				error: `Failed to upload image - ${await res.text()}`
			};
		}
	} catch (error) {
		console.error(error);
		return {
			result: null,
			error
		};
	}
}

/**
 * Converts a timestamp to an ISO string representation of the date.
 *
 * @param {number} timestamp - The timestamp to convert.
 * @return {string} The ISO string representation of the converted date.
 */
export function convertTimestampToISO(timestamp: number) {
	const date = new Date(timestamp * 1000);
	return date.toISOString();
}

/**
 * Returns the Unix timestamp of a given date.
 * @param targetDate - The date to convert to Unix timestamp.
 * @returns The Unix timestamp as a string.
 */
export function getUnixTimestamp(targetDate: any): string {
	const formattedTime = (targetDate / 1000).toFixed(6).toString();
	return formattedTime;
}

/**
 * Transforms an input object into an array of its values.
 * @param inputObject - The input object to transform.
 * @returns An array of the values of the input object.
 */
export function transformToObjectArray(inputObject: any) {
	const result: any = [];
	for (const key in inputObject) {
		result.push(inputObject[key]);
	}

	return result;
}

export function loadWsInstances() {
	atws.initWs();
	atws.connectWs();
	atws.connectTrollbox();
	atws.connectBotSummary();
}

//@ts-ignore
let intersectionObserver;

function ensureIntersectionObserver() {
	//@ts-ignore
	if (intersectionObserver) return;

	intersectionObserver = new IntersectionObserver((entries) => {
		entries.forEach((entry) => {
			const eventName = entry.isIntersecting ? 'enterViewport' : 'exitViewport';
			entry.target.dispatchEvent(new CustomEvent(eventName));
		});
	});
}

//@ts-ignore
export function viewport(element) {
	ensureIntersectionObserver();

	//@ts-ignore
	intersectionObserver.observe(element);

	return {
		destroy() {
			//@ts-ignore
			intersectionObserver.unobserve(element);
		}
	};
}

/**
 * Scrolls the element with the given id.
 *
 * @return {void} This function does not return anything.
 */
export function scrollToTheBottom(id: string) {
	const element = document.getElementById(id);
	if (element) {
		element.scrollTop = element.scrollHeight;
	}
}

/**
 * Captures key-value pairs from a string and returns them as an object.
 * @param {string} inputString - The input string containing key-value pairs.
 * @returns {Object} - An object with keys and corresponding values.
 */
export function terminalMessagesToCommon(inputString: string) {
	// Define a regular expression to match key-value pairs in the format key=value.
	const regex = /(\w+[-\w]*)=([\w-]+)/g;

	// Use matchAll to find all matches in the input string.
	const matches = inputString.matchAll(regex);

	// Initialize an empty object to store the captured key-value pairs.
	const result: any = {};

	// Iterate over the matches and populate the result object.
	for (const match of matches) {
		// Destructure the match array into key and value variables.
		const [, key, value] = match;

		// Add the key and value to the result object.
		result[key] = value;
	}

	// Return the final object containing the captured key-value pairs.
	return result;
}

/**
 * Escapes special characters in a regex string.
 *
 * @param {string} string - The input string to escape.
 * @return {string} The escaped string.
 */
function escapeRegex(string: string) {
	const specialCharacters = /[.*+?^${}()|[\]\\]/g;
	if (specialCharacters.test(string)) {
		return string.replace(specialCharacters, '\\$&');
	}
	return string;
}

/**
 * Filters an array of commands based on a regular expression pattern applied to the 'command' property of each command object.
 *
 * @param {any[]} commands - The array of commands to filter.
 * @param {string} regexPattern - The regular expression pattern to match against the 'command' property of each command object.
 * @return {any[]} - The filtered array of commands.
 */
export function deepFilter(commands: any[], regexPattern: string) {
	const escapedPattern = escapeRegex(regexPattern);
	const regex = new RegExp(escapedPattern, 'i');

	return commands.filter((command) => regex.test(command.command));
}

/**
 * Inserts a target value into the initial format string based on the presence of '=' or placeholder tags.
 *
 * @param {any} initialFormat - The initial format string that may contain placeholders or assignments.
 * @param {any} targetValue - The value to insert into the initial format string.
 * @return {any} The formatted string with the target value inserted.
 */
export function insertValueToCommandCommon(initialFormat: any, targetValue: any) {
	if (initialFormat.includes('=')) {
		const parts = initialFormat.split('=');
		const placeholder = parts[0].trim();
		return `${placeholder}=${targetValue}`;
	}

	const match = initialFormat.match(/<([^>]+)>/);
	if (match) {
		const placeholder = match[1];
		return `${placeholder}=${targetValue}`;
	} else {
		// Handle case where there is no brackets
		const trimmedFormat = initialFormat.trim();
		return `${trimmedFormat}=${targetValue}`;
	}
}

/**
 * Focuses on the input element with the specified id.
 *
 * @param {string} id - The id of the input element to focus on.
 */
export function focusToInput(id: string) {
	const terminalInput = document.getElementById(id);
	if (terminalInput) {
		terminalInput.focus();
	}
}

/**
 * Copies the specified text to the clipboard and shows a success toast message.
 *
 * @param {string} text - The text to be copied to the clipboard.
 * @return {void} No return value.
 */
export function copyToClipboard(text: string, message: string = 'Copied to clipboard') {
	navigator.clipboard.writeText(text);
	toast(message, 'success');
}

export function formatCommandsForContent(commands: any) {
	let formattedCommands = '';
	commands.forEach((command: any) => {
		formattedCommands += `**${command.name}**\n`;
		formattedCommands += `${command.description}\n`;
		formattedCommands += `Command: ${command.command}\n\n`;
	});
	return formattedCommands;
}
