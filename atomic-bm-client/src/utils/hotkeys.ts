import { goto } from '$app/navigation';
import { browser } from '$app/environment';

export class KeyboardShortcuts {
	shortcuts: { key: string; modifier: string; action: () => void }[];
	constructor() {
		this.shortcuts = [
			{
				key: 'q',
				modifier: 'Ctrl+Alt',
				action: () => {
					if (browser) {
						const url = new URL(window.location.href);
						goto(`${url}?quit=true`);
						setTimeout(() => goto(url), 100);
					}
				}
			},
			{
				key: 'a',
				modifier: 'Ctrl+Alt',
				action: () => {
					if (browser) {
						const url = new URL(window.location.href);
						goto(`${url}?openDocs=true`);
						setTimeout(() => goto(url), 100);
					}
				}
			},
			{
				key: 'r',
				modifier: 'Ctrl+Alt',
				action: () => {
					if (browser) {
						const url = new URL(window.location.href);
						goto(`${url}?focusTerminal=true`);
						setTimeout(() => goto(url), 100);
					}
				}
			}
		];

		this.handleKeyPress = this.handleKeyPress.bind(this);
		this.setupEventListeners();
	}

	setupEventListeners() {
		typeof document !== 'undefined' && document.addEventListener('keydown', this.handleKeyPress);
	}

	handleKeyPress(event: KeyboardEvent) {
		for (const shortcut of this.shortcuts) {
			if (
				(event.ctrlKey || event.metaKey) &&
				event.altKey &&
				event.key === shortcut.key &&
				event.code === `Key${shortcut.key.toUpperCase()}`
			) {
				event.preventDefault();
				event.stopPropagation();
				shortcut.action();
			}
		}
	}
}

export const hotkeys = new KeyboardShortcuts();
