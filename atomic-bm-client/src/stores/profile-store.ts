import { getUsersMe } from '$client/user';
import { getStorage, handleErr } from '$utils';
import { get, writable } from 'svelte/store';

interface UserProfile {
	id: string;
	name: string;
	email: string;
	role: string;
	createdAt: string;
	updatedAt: string;
}

export const profile = writable({
	id: '',
	name: '',
	email: '',
	role: '',
	createdAt: '',
	updatedAt: ''
} as UserProfile);

export async function setProfile() {
	try {
		const res = await getUsersMe({
			headers: {
				Authorization: `Bearer ${getStorage('token')}`
			}
		});

		profile.set(res.data.data);
	} catch (e) {
		handleErr(e);
	}
}

export function getUserID() {
	return get(profile).id;
}
