import { get, writable } from 'svelte/store';

interface ReminderState {
	intervalId: number | null;
	futureDate: Date | null;
}

interface ReminderStore {
	subscribe: (run: (value: ReminderState) => void) => () => void;
	startTimer: (futureDateStr: string, message?: string) => void;
	stopTimer: () => void;
	getExpiryDate: () => string | null;
	getExpiryMessage: () => string | null;
}

function createReminderStore(): ReminderStore {
	const { subscribe, set, update } = writable<ReminderState>({
		intervalId: null,
		futureDate: null
	});
	const expiryDate = writable<Date | null>(null);
	const reminderMessage = writable<string | null>(null);

	function startTimer(futureDateStr: string, message?: string): void {
		const futureDate = new Date(futureDateStr);
		if (isNaN(futureDate.getTime())) {
			console.error('Invalid date format');
			return;
		}
		stopTimer();
		const intervalId = window.setInterval(() => {
			const currentDate = new Date();
			if (currentDate >= futureDate) {
				fireNotification(message ?? 'Time is up!');
				stopTimer();
			}
		}, 1000);
		set({ intervalId, futureDate });
		expiryDate.set(futureDate);

		if (message) {
			reminderMessage.set(message);
		}
	}

	function stopTimer(): void {
		update((state) => {
			if (state.intervalId !== null) {
				clearInterval(state.intervalId);
			}
			return { intervalId: null, futureDate: null };
		});
		expiryDate.set(null);
		reminderMessage.set(null);
	}

	function getExpiryDate(): string | null {
		const state = get(expiryDate);
		if (state) {
			return state.toLocaleString();
		}

		return null;
	}

	function getExpiryMessage(): string | null {
		const state = get(reminderMessage);
		if (state) {
			return state;
		}

		return null;
	}

	function fireNotification(message: string) {
		if (!('Notification' in window)) {
			return;
		}

		if (Notification.permission === 'granted') {
			new Notification(message, {
				requireInteraction: true
			});
		}

		if (Notification.permission !== 'granted') {
			Notification.requestPermission().then((permission) => {
				if (permission === 'granted') {
					new Notification(message);
				}
			});
		}
	}

	return {
		subscribe,
		startTimer,
		stopTimer,
		getExpiryDate,
		getExpiryMessage
	};
}

export const reminderStore: ReminderStore = createReminderStore();
