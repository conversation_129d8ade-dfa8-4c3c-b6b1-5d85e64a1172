import { type Writable, writable } from 'svelte/store';

type ConnectionState = 'disconnected' | 'connecting' | 'connected';

interface Order {
	id: string;
	state: string;
	type: string;
	side: string;
	feeAsset: string;
	price: number;
	avgPrice: number;
	amount: number;
	filledAmount: number;
	fee: number;
	postOnly: boolean;
}

interface OrderBookEntry {
	side: string;
	price: string;
	amount: string;
	topSpread: string;
	refSpread: string;
	cumTotal: string;
	hasOpenOrders: boolean;
}

interface OrderBook {
	asks: OrderBookEntry[];
	bids: OrderBookEntry[];
}

interface DepthBps {
	bidDepth: number;
	bidDepthValue: number;
	askDepth: number;
	askDepthValue: number;
}

interface Depth {
	bestBid: number;
	bestAsk: number;
	'50Bps': DepthBps;
	'100Bps': DepthBps;
	'200Bps': DepthBps;
	'500Bps': DepthBps;
}

interface Inventory {
	stock: {
		available: number;
		total: number;
	};
	money: {
		available: number;
		total: number;
	};
}

interface MarketData {
	id: string;
	type: string;
	openOrders: Order[];
	inventory: Inventory;
	depth: Depth;
	orderbook: OrderBook;
	time: string;
}

interface IWebsocketStore {
	connectState?: ConnectionState;
	trollboxState?: ConnectionState;
	newTrollboxMessage?: any;
	botState?: MarketData | null;
	botSummary?: MarketData | null;
}

export const websocketStore: Writable<IWebsocketStore> = writable({
	connectState: 'disconnected',
	trollboxState: 'disconnected',
	newTrollboxMessage: null,
	botState: null,
	botSummary: null
});

export function updateWebsocketStore(ws: IWebsocketStore) {
	websocketStore.update((store) => {
		return {
			...store,
			...ws
		};
	});
}
