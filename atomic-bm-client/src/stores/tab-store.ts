import { tabsClient } from '$api/tabs';
import { get, writable, type Updater } from 'svelte/store';
import type { Tab, BaseTabsResponse, TabStore, ReorderedTab } from '$types';
import { toast } from '$utils';
import { goto } from '$app/navigation';

export function createTabStore(
	initValue: TabStore,
	user_id: string,
	onChange?: (value: TabStore) => void
) {
	const client = tabsClient(user_id);
	const tabStore = writable(initValue);

	/**
	 * Updates the state of the tab store by applying the given updater function.
	 *
	 * @param {Updater<TabStore>} fn - The updater function that receives the current state and returns the new state.
	 * @return {void} This function does not return a value.
	 */
	function update(fn: Updater<TabStore>) {
		tabStore.update((curr) => {
			const next = fn(curr);
			onChange?.(next);
			return next;
		});
	}

	/**
	 * Asynchronously loads tabs from the client and returns a Promise that resolves to a BaseTabsResponse object.
	 *
	 * @return {Promise<BaseTabsResponse>} A Promise that resolves to a BaseTabsResponse object containing the loaded tabs, last tab ID, and any error that occurred during the loading process.
	 * @throws {Error} If an error occurs during the loading process, an Error object is thrown with the error message.
	 */
	async function loadTabs(): Promise<BaseTabsResponse> {
		try {
			const { tabs, last_tab_id, err } = await client.fetchTabs();
			if (err) {
				throw new Error(err.message);
			}

			return {
				tabs,
				last_tab_id,
				err: null
			};
		} catch (err) {
			console.error(err);
			return {
				tabs: [],
				last_tab_id: '',
				err: err as Error
			};
		}
	}

	/**
	 * Asynchronously initializes tabs by attempting to load them from the client.
	 * If the tabs cannot be loaded or if there are no tabs, it attempts to create a new tab.
	 * If the creation of a new tab fails, it retries up to a maximum number of attempts.
	 * If the tabs are successfully loaded or created, it updates the last tab ID and navigation status.
	 *
	 * @param {number} [attempt=0] - The number of attempts made to load the tabs.
	 * @return {Promise<Array<Tab>>} - A Promise that resolves to an array of tabs if they are successfully loaded or created, or an empty array if the loading or creation fails.
	 */
	async function initializeTabs(attempt = 0) {
		const MAX_ATTEMPTS = 5;

		const { tabs, last_tab_id, err } = await loadTabs();
		if (err) {
			toast('failed to load tabs', 'error');
			return [];
		}

		if (!tabs.length) {
			if (attempt >= MAX_ATTEMPTS) {
				toast('limit time reached, failed to load tabs', 'error');
				return [];
			}

			const newTabID = window.crypto.randomUUID();
			handleTabNavigation(newTabID, '/bots');
			const res = await createTab({
				name: 'Bots',
				path: '/bots',
				id: newTabID
			});
			if (res !== null) {
				toast('failed to create tab', 'error');
				return [];
			}

			return initializeTabs(attempt + 1);
		}

		if (!last_tab_id) {
			update((curr) => {
				return {
					...curr,
					lastTabID: tabs[0].id as string
				};
			});
		} else {
			update((curr) => {
				return {
					...curr,
					lastTabID: last_tab_id
				};
			});
		}

		updateNavigationStatus(tabs, get(tabStore).lastTabID);
		update((curr) => {
			return {
				...curr,
				tabs
			};
		});
	}

	/**
	 * Switches the active tab and updates the navigation status.
	 *
	 * @param {string | undefined} last_tab_id - The ID of the previously active tab.
	 * @return {Promise<void>} A promise that resolves when the tab switch is complete.
	 */
	async function switchTab(last_tab_id: string | undefined) {
		update((curr) => {
			return {
				...curr,
				lastTabID: last_tab_id ?? ''
			};
		});
		updateNavigationStatus(get(tabStore).tabs, last_tab_id as string);

		const res = await client.updateLastTabID(last_tab_id ?? '');
		if (res !== null) {
			toast('Failed to switch tab', 'error');
			const lastTab = get(tabStore).tabs.find((tab) => tab.id === get(tabStore).lastTabID) as Tab;
			if (lastTab) {
				handleTabNavigation(lastTab.id as string, lastTab.path);
			}

			update((curr) => {
				return {
					...curr,
					lastTabID: lastTab.id as string
				};
			});
		}
	}

	/**
	 * Asynchronously creates a new tab using the provided Tab object.
	 *
	 * @param {Tab} tab - The Tab object representing the tab to be created.
	 * @return {Promise<Error | null>} A Promise that resolves to null if the tab was created successfully,
	 * or an error if the creation failed.
	 */
	async function createTab(tab: Tab): Promise<Error | null> {
		try {
			const res = await client.createTab(tab.id as string, tab);

			if (res !== null) {
				throw new Error(res.message);
			}

			return res;
		} catch (err) {
			console.error(err);
			toast('Failed to create tab', 'error');
			return err as Error;
		}
	}

	/**
	 * Asynchronously updates the information of a tab with the given ID using the provided Tab object.
	 *
	 * @param {string} tab_id - The ID of the tab to be updated.
	 * @param {Tab} tab - The Tab object containing the updated information.
	 * @return {Promise<Error | null>} A Promise that resolves to null if the tab was updated successfully,
	 * or an error if the update failed.
	 */
	async function updateTabInfo(tab_id: string, tab: Tab): Promise<Error | null> {
		try {
			const res = await client.updateTab(tab_id, tab);
			if (res !== null) {
				toast('Failed to update tab', 'error');
				return res;
			}

			return res;
		} catch (err) {
			console.error(err);
			toast('Failed to update tab', 'error');
			return err as Error;
		}
	}

	/**
	 * Asynchronously navigates to a new tab and updates the tab information.
	 *
	 * @param {Tab} tab - The new tab object.
	 * @return {Promise<void>} A promise that resolves when the navigation and tab update are complete.
	 */
	async function navigateOnTabChange(tab: Tab, keepName?: boolean) {
		handleTabNavigation(get(tabStore).lastTabID, tab.path);
		let previousName: string;
		let previousPath: string;
		const updatedTabs = get(tabStore).tabs.map((t) => {
			previousName = get(tabStore).tabs.find((tab) => tab.id === get(tabStore).lastTabID)
				?.name as string;
			previousPath = get(tabStore).tabs.find((tab) => tab.id === get(tabStore).lastTabID)
				?.path as string;

			if (!keepName) {
				return {
					...t,
					name: t.id === get(tabStore).lastTabID ? tab.name : t.name,
					path: t.id === get(tabStore).lastTabID ? tab.path : t.path
				};
			} else {
				return {
					...t,
					name: t.id === get(tabStore).lastTabID ? previousName : t.name,
					path: t.id === get(tabStore).lastTabID ? tab.path : t.path
				};
			}
		}) as Tab[];

		update((curr) => {
			return {
				...curr,
				tabs: updatedTabs
			};
		});

		const res = await updateTabInfo(get(tabStore).lastTabID, {
			...tab,
			name: keepName
				? (get(tabStore).tabs.find((tab) => tab.id === get(tabStore).lastTabID)?.name as string)
				: tab.name
		});
		if (res !== null) {
			toast('Failed to update tab', 'error');

			const updatedTabs = get(tabStore).tabs.map((t) => {
				if (!keepName) {
					return {
						...t,
						name: t.id === get(tabStore).lastTabID ? previousName : t.name,
						path: t.id === get(tabStore).lastTabID ? previousPath : t.path
					};
				} else {
					return {
						...t,
						name: previousName,
						path: t.id === get(tabStore).lastTabID ? previousPath : t.path
					};
				}
			});

			update((curr) => {
				return {
					...curr,
					tabs: updatedTabs
				};
			});
		}
	}

	/**
	 * Asynchronously opens a new tab with a randomly generated ID and adds it to the list of tabs.
	 * Switches to the newly created tab and creates it on the server. If the creation fails, it displays
	 * an error message and switches to the penultimate tab.
	 *
	 * @return {Promise<void>} A promise that resolves when the new tab is created and switched to.
	 */
	async function openNewTab(name?: string, path?: string) {
		const newTabID = window.crypto.randomUUID();
		const newTab = {
			id: newTabID,
			name: name ?? 'Bots',
			path: path ?? '/bots'
		};
		update((curr) => {
			return {
				lastTabID: newTab.id,
				tabs: [...curr.tabs, newTab]
			};
		});

		await switchTab(newTab.id);
		const res = await createTab({
			name: newTab.name,
			path: newTab.path,
			id: newTab.id
		});
		if (res !== null) {
			toast('failed to create tab', 'error');
			const penultimateTab = get(tabStore).tabs[get(tabStore).tabs.length - 2];
			if (penultimateTab) {
				switchTab(penultimateTab.id);
			}
		}
	}

	/**
	 * Removes a tab associated with a user.
	 *
	 * @param {string | undefined} tab_id - The ID of the tab to remove. If undefined, an empty string is used.
	 * @return {Promise<void>} A promise that resolves when the tab is successfully removed. If the removal fails, an error toast is displayed.
	 */
	async function removeTab(tab_id: string | undefined) {
		const res = await client.deleteTab(tab_id ?? '');
		if (res !== null) {
			toast('Failed to remove tab', 'error');
			return;
		}

		const indexFromTabID = get(tabStore).tabs.findIndex(
			(tab) => tab.id === get(tabStore).lastTabID
		);
		const lastIndexFromTab = get(tabStore).tabs.length - 1;
		if (get(tabStore).lastTabID === tab_id && indexFromTabID === lastIndexFromTab) {
			const penultimateTab = get(tabStore).tabs[get(tabStore).tabs.length - 2];
			if (penultimateTab) {
				switchTab(penultimateTab.id);
			}
		} else if (get(tabStore).lastTabID === tab_id) {
			const targetTab = get(tabStore).tabs[indexFromTabID + 1];
			if (targetTab) {
				switchTab(targetTab.id);
			}
		}

		update((curr) => {
			return {
				...curr,
				tabs: curr.tabs.filter((tab) => tab.id !== tab_id)
			};
		});
	}

	async function updateTabsOrder(newOrder: ReorderedTab[]) {
		const res = await client.reorderTabs(newOrder);
		if (res !== null) {
			toast('Failed to reorder tabs', 'error');
		}

		const { tabs, err } = await loadTabs();
		if (err) {
			toast('Failed to load tabs', 'error');
			return;
		}

		update((curr) => {
			return {
				...curr,
				tabs
			};
		});
	}

	/**
	 * Updates the navigation status based on the provided tabs and last tab ID.
	 *
	 * @param {Tab[]} tabs - The array of tabs to search for the last tab ID.
	 * @param {string} last_tab_id - The ID of the last tab.
	 */
	function updateNavigationStatus(tabs: Tab[], last_tab_id: string) {
		const foundTab = tabs.find((tab) => tab.id === last_tab_id);
		if (foundTab) {
			handleTabNavigation(foundTab.id as string, foundTab.path);
		}
	}

	/**
	 * Navigates to the specified tab path with the given ID.
	 *
	 * @param {string} id - The ID of the tab.
	 * @param {string} path - The path of the tab.
	 * @return {void} This function does not return a value.
	 */
	function handleTabNavigation(id: string, path: string) {
		goto(`/app/${id}${path}`);
	}

	return {
		...tabStore,
		update,
		createTab,
		handleTabNavigation,
		initializeTabs,
		switchTab,
		openNewTab,
		removeTab,
		navigateOnTabChange,
		updateTabsOrder
	};
}
