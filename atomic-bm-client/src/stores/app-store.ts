import { writable, readable } from 'svelte/store';

export const appStore = writable({
	currentPageTitle: ''
});

export const menuOptions = readable([
	{ name: '<PERSON><PERSON>', icon: 'gravity-ui:face-robot', path: '/bots' },
	{ name: 'Accounts', icon: 'mdi:text-account', path: '/accounts' },
	{ name: 'Groups', icon: 'ep:connection', path: '/groups' },
	{ name: 'TradingView', icon: 'arcticons:tradingview', path: '/tradingview' },
	{ name: 'Trollbox', icon: 'lets-icons:chat-alt', path: '/trollbox' },
	{ name: 'Settings', tabName: 'Profile', icon: 'mdi:cog', path: '/settings' }
]);

export const settingsOptions = readable([
	{
		name: 'Profile',
		tabName: 'Profile',
		icon: 'iconamoon:profile-circle',
		path: '/settings'
	},
	{
		name: 'Sessions',
		tabName: 'Sessions',
		icon: 'eos-icons:compare-states',
		path: '/settings/sessions'
	},
	{
		name: 'Aliases',
		tabName: 'Aliases',
		icon: 'tabler:table-alias',
		path: '/settings/aliases'
	},
	{
		name: 'Hotkeys',
		tabName: 'Hotkeys',
		icon: 'mingcute:hotkey-line',
		path: '/settings/hotkeys'
	},
	{
		name: 'Changelog',
		tabName: 'Changelog',
		icon: 'material-symbols-light:deployed-code-update-rounded',
		path: '/settings/changelog'
	}
]);

export const botOptions = readable([
	{
		name: 'Bot data',
		tabName: 'Bot data',
		icon: 'tdesign:data',
		path: '/'
	},
	{
		name: 'Params',
		tabName: 'Bot Params',
		icon: 'codicon:symbol-parameter',
		path: '/params'
	},
	{
		name: 'Notes',
		tabName: 'Bot Notes',
		icon: 'ic:round-edit-note',
		path: '/notes'
	},
	{
		name: 'Reminders',
		tabName: 'Bot Reminders',
		icon: 'fluent-mdl2:mail-reminder',
		path: '/reminders'
	},
]);
