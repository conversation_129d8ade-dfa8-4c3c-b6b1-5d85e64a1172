import type { DocumentReference, DocumentData, DocumentSnapshot } from 'firebase/firestore';

export interface Tab {
	readonly id?: string;
	name: string;
	path: string;
	order?: number;
}

export interface TabsResponse {
	tabs: Tab[];
	last_tab_id: string;
}

export interface AliasResponse {
	aliases: any[];
}

export interface TabResponse {
	tab: Tab;
	last_tab_id: string;
}

export interface ReorderedTab {
	id: string;
	order: number;
}

export interface Note {
	snapshot?: string;
	id: string;
	created_at: string;
	created_user_id: string;
	note: string;
	title: string;
	priority: 'low' | 'medium' | 'high' | '';
}

export interface BaseTabResponse {
	tab: Tab;
	last_tab_id: string;
	err: Error | null;
}

export interface BaseTabsResponse {
	tabs: Tab[];
	last_tab_id: string;
	err: Error | null;
}

export interface BaseAliasResponse {
	aliases: any[];
	err: Error | null;
}

export interface BaseHigherOrderResponse {
	higherOrder: number;
	err: Error | null;
}

export interface BaseUserResponse {
	userRef: DocumentReference<DocumentData, DocumentData>;
	userDoc: DocumentSnapshot<DocumentData, DocumentData>;
	err: Error | null;
}

export interface BaseNoteInstanceResponse {
	notesRef: DocumentReference<DocumentData, DocumentData>;
	notesDoc: DocumentSnapshot<DocumentData, DocumentData>;
	err: Error | null;
}

export interface BaseNotesResponse {
	notes: Note[];
	err: Error | null;
}

export interface MenuOptions {
	name: string;
	icon: string;
	path: string;
	tabName?: string;
}

export interface TabStore {
	tabs: Tab[];
	lastTabID: string;
}

export type CommandError = {
	type: 'error' | 'warning';
	message: string;
};

export interface CommandViewData {
	command: string;
	title: string;
	result: any;
	error: CommandError | null;
}
