import { defineConfig } from 'orval';

export default defineConfig({
	botmanager: {
		input: {
			target: '../docs/swagger.json',
			converterOptions: {
				refSiblings: 'preserve',
				resolveInternal: true,
				components: true,
				debug: true
			}
		},
		output: {
			baseUrl: '${import.meta.env.VITE_ATOMIC_BASEURL}',
			mode: 'tags',
			prettier: true,
			clean: true,
			headers: true,
			target: './src/client/api.ts',
			client: 'svelte-query',
			mock: false,
			override: {
				query: {
					useQuery: true,
					usePrefetch: false,
					useInfinite: false,
					useMutation: false,
					signal: false,
					useInfiniteQueryParam: 'false',
					useSuspenseInfiniteQuery: false,
					shouldExportQueryKey: false,
					shouldExportHttpClient: false,
					shouldExportMutatorHooks: false,
					version: 5
				}
			}
		}
	}
});
