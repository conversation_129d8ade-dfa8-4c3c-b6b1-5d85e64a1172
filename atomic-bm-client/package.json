{"name": "atomic-bm-client", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "test": "npm run test:integration && npm run test:unit", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write .", "test:integration": "playwright test", "test:unit": "vitest run", "prepare": "husky install", "commit": "cz", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "init-orval": "orval --config ./orval.config.ts"}, "devDependencies": {"@commitlint/cli": "^17.6.7", "@commitlint/config-conventional": "^17.6.7", "@lucide/svelte": "^0.482.0", "@playwright/test": "^1.28.1", "@storybook/addon-essentials": "^7.3.2", "@storybook/addon-interactions": "^7.3.2", "@storybook/addon-links": "^7.3.2", "@storybook/addon-styling": "^1.3.7", "@storybook/addon-svelte-csf": "^3.0.9", "@storybook/blocks": "^7.3.2", "@storybook/svelte": "^7.3.2", "@storybook/sveltekit": "^7.3.2", "@storybook/testing-library": "^0.2.0", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.5.27", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@types/eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "autoprefixer": "^10.4.19", "bits-ui": "^1.8.0", "commitizen": "^4.0.3", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-storybook": "^0.6.13", "eslint-plugin-svelte": "^2.45.1", "husky": "^8.0.3", "mode-watcher": "^1.1.0", "openapi-typescript-codegen": "^0.27.0", "orval": "^6.29.1", "paneforge": "1.0.0-next.6", "postcss": "^8.4.38", "postcss-load-config": "^4.0.1", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.2.6", "prettier-plugin-tailwindcss": "^0.5.14", "storybook": "^7.3.2", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-dnd-action": "^0.9.44", "svelte-sonner": "^1.0.5", "sveltekit-search-params": "^2.1.2", "tailwindcss": "^3.4.3", "tslib": "^2.4.1", "typescript": "^5.5.0", "vaul-svelte": "1.0.0-next.7", "vite": "^5.4.4", "vitest": "^1.2.0"}, "type": "module", "dependencies": {"@marcioecom/atomic-command-engine": "1.0.1", "@sveltestack/svelte-query": "^1.6.0", "@tanstack/svelte-query": "^4.24.9", "axios": "^1.7.2", "centrifuge": "^4.1.0", "clsx": "^2.1.1", "firebase": "^10.12.2", "html-to-image": "^1.11.11", "iconify-icon": "^1.0.8", "imagekit": "^5.0.1", "lucide-svelte": "^0.379.0", "openai": "^4.28.4", "openapi-typescript": "^6.7.6", "svelte-french-toast": "^1.2.0", "svelte-persisted-store": "^0.9.4", "tailwind-merge": "^2.3.0", "tailwind-variants": "^0.2.1", "tailwindcss-animate": "^1.0.7"}}