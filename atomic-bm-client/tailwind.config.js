import { fontFamily } from 'tailwindcss/defaultTheme';
import tailwindcssAnimate from "tailwindcss-animate";

/** @type {import('tailwindcss').Config} */
const config = {
	darkMode: ['class'],
	content: ['./src/**/*.{html,js,svelte,ts}'],
	safelist: ['dark'],
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			backgroundImage: {
				stars:
					'url("https://res.cloudinary.com/domwy2hmn/image/upload/v1691624246/stars-bg_pje65w.svg")'
			},
			keyframes: {
				'bg-move': {
					'0%': { 'background-position': '0% 50%' },
					'50%': { 'background-position': '100% 50%' },
					'100%': { 'background-position': '0% 50%' }
				},
				'skeleton-loading': {
					'0%': { opacity: 0.5 },
					'50%': { opacity: 1 },
					'100%': { opacity: 0.5 }
				}
			},
			animation: {
				spin: 'spin 16s linear infinite',
				'spin-fast': 'spin 1s linear infinite',
				'bg-move-effect': 'bg-move 30s linear infinite',
				'bg-move-effect-longer': 'bg-move 280s linear infinite',
				'skeleton-loading': 'skeleton-loading 1.5s ease-in-out infinite'
			},
			colors: {
				dark: {
					1: '#1e2124',
					2: '#121416',
					3: '#282b30',
					4: '#36393e',
					5: '#424549',
					6: '#14161f',
					7: '#00000071',
					8: '#0000004d',
					9: '#000000b2'
				},
				blue: {
					1: '#7289da'
				},
				border: 'hsl(var(--border) / <alpha-value>)',
				input: 'hsl(var(--input) / <alpha-value>)',
				ring: 'hsl(var(--ring) / <alpha-value>)',
				background: 'hsl(var(--background) / <alpha-value>)',
				foreground: 'hsl(var(--foreground) / <alpha-value>)',
				primary: {
					DEFAULT: 'hsl(var(--primary) / <alpha-value>)',
					foreground: 'hsl(var(--primary-foreground) / <alpha-value>)'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary) / <alpha-value>)',
					foreground: 'hsl(var(--secondary-foreground) / <alpha-value>)'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive) / <alpha-value>)',
					foreground: 'hsl(var(--destructive-foreground) / <alpha-value>)'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted) / <alpha-value>)',
					foreground: 'hsl(var(--muted-foreground) / <alpha-value>)'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent) / <alpha-value>)',
					foreground: 'hsl(var(--accent-foreground) / <alpha-value>)'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover) / <alpha-value>)',
					foreground: 'hsl(var(--popover-foreground) / <alpha-value>)'
				},
				card: {
					DEFAULT: 'hsl(var(--card) / <alpha-value>)',
					foreground: 'hsl(var(--card-foreground) / <alpha-value>)'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			fontFamily: {
				sans: [...fontFamily.sans]
			}
		}
	},
	plugins: [tailwindcssAnimate]	
};

export default config;
