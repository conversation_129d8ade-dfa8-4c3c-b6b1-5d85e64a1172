// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/accounts": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "List accounts.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Account"
                ],
                "summary": "Get a list of accounts.",
                "parameters": [
                    {
                        "type": "string",
                        "example": "Binance",
                        "x-order": "0",
                        "description": "Specifies the identifier of the exchange (e.g., \"Binance\").",
                        "name": "exchangeId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "us-east-2",
                        "x-order": "1",
                        "description": "RegionID region where account operates",
                        "name": "regionId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "atomic-mm",
                        "x-order": "2",
                        "description": "Filter accounts based on a specific tag (e.g., \"atomic-mm\").",
                        "name": "tag",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "default": false,
                        "example": true,
                        "x-order": "30",
                        "description": "Indicates whether deleted items should be included.",
                        "name": "isDeleted",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "exchange_id",
                            "tag",
                            "created_at",
                            "updated_at",
                            "deleted_at"
                        ],
                        "type": "string",
                        "default": "created_at",
                        "example": "created_at",
                        "x-order": "31",
                        "description": "Specifies the field by which the results should be ordered.",
                        "name": "orderBy",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "DESC",
                            "ASC"
                        ],
                        "type": "string",
                        "default": "DESC",
                        "example": "DESC",
                        "x-order": "32",
                        "description": "Sets the order direction for sorting results (ASC or DESC).",
                        "name": "orderDirection",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "example": 10,
                        "x-order": "33",
                        "description": "Specifies the maximum number of items to return per page.",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "example": 0,
                        "x-order": "34",
                        "description": "Specifies the starting position of the items to be retrieved.",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Accounts",
                        "schema": {
                            "$ref": "#/definitions/PageResponse-array_Account"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Create an account by giving exchangeID and tag.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Account"
                ],
                "summary": "Create an account.",
                "parameters": [
                    {
                        "description": "Exchange and Tag",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateAccountRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/Response-Account"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/accounts/{accountId}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Fetch an account by its ID.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Account"
                ],
                "summary": "Get an account.",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "example": "8dfc86a3-a9f7-445e-be53-f2b07d109829",
                        "description": "Account ID",
                        "name": "accountId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Account",
                        "schema": {
                            "$ref": "#/definitions/Response-Account"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Delete an account by its ID.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Account"
                ],
                "summary": "Delete an account.",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "example": "8dfc86a3-a9f7-445e-be53-f2b07d109829",
                        "description": "Account ID",
                        "name": "accountId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/bots": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Retrieves a list of bots with optional filtering and pagination parameters.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Bot"
                ],
                "summary": "Lists bots with custom filters.",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "example": "8dfc86a3-a9f7-445e-be53-f2b07d109829",
                        "x-order": "0",
                        "description": "AccountID filter bots by Account ID",
                        "name": "accountId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "BTCUSD",
                        "x-order": "1",
                        "description": "Symbol filter bots by its symbol",
                        "name": "symbol",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "us-east-2",
                        "x-order": "2",
                        "description": "RegionID filter bots by is region",
                        "name": "regionId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "running",
                        "x-order": "3",
                        "description": "Status filter bots by status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "default": false,
                        "example": true,
                        "x-order": "30",
                        "description": "Indicates whether deleted items should be included.",
                        "name": "isDeleted",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "account_id",
                            "symbol",
                            "region",
                            "status",
                            "created_at"
                        ],
                        "type": "string",
                        "default": "created_at",
                        "example": "created_at",
                        "x-order": "31",
                        "description": "Specifies the field by which the results should be ordered.",
                        "name": "orderBy",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "DESC",
                            "ASC"
                        ],
                        "type": "string",
                        "default": "DESC",
                        "example": "DESC",
                        "x-order": "32",
                        "description": "Sets the order direction for sorting results (ASC or DESC).",
                        "name": "orderDirection",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "example": 10,
                        "x-order": "33",
                        "description": "Specifies the maximum number of items to return per page.",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "example": 0,
                        "x-order": "34",
                        "description": "Specifies the starting position of the items to be retrieved.",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "true",
                        "x-order": "4",
                        "description": "Active filter bots by his state",
                        "name": "active",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of Bots",
                        "schema": {
                            "$ref": "#/definitions/PageResponse-array_Bot"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Creates a new bot with the specified parameters.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Bot"
                ],
                "summary": "Create a new bot.",
                "parameters": [
                    {
                        "description": "Bot creation parameters",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateBotRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/Response-Bot"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/bots/state": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Retrieves a cached state of all bots. The data is populated by a background worker.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Bot"
                ],
                "summary": "Get bots state.",
                "responses": {
                    "200": {
                        "description": "Successful response with bot state data",
                        "schema": {
                            "$ref": "#/definitions/Response-array_ListBotsStateResponse"
                        }
                    },
                    "404": {
                        "description": "No state data available",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/bots/{botId}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Retrieve a bot with the specified ID.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Bot"
                ],
                "summary": "Retrieve a bot by ID.",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823",
                        "description": "Bot ID for retrieval",
                        "name": "botId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Response-Bot"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Delete a bot with the specified ID.",
                "tags": [
                    "Bot"
                ],
                "summary": "Delete a bot by ID.",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823",
                        "description": "Bot ID to be deleted",
                        "name": "botId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Update an existing bot with the specified ID by providing account ID, symbol, region, or status.\nYou need to ensure that at least one field is provided in the update request.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Bot"
                ],
                "summary": "Update an existing bot by ID.",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823",
                        "description": "Bot ID to be updated",
                        "name": "botId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Bot update parameters",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UpdateBotRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Response-Bot"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/bots/{botId}/start": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "It creates a bot based on the botId.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Bot"
                ],
                "summary": "StartBot create a new bot if not exists.",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823",
                        "description": "Bot ID for retrieval",
                        "name": "botId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success"
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/bots/{botId}/state": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Retrieves the current state of a specific bot from the botregional service. It uses a short-lived cache to reduce load.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Bot"
                ],
                "summary": "Get bot state.",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823",
                        "description": "Bot ID",
                        "name": "botId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successful response with bot state",
                        "schema": {
                            "$ref": "#/definitions/Response-GetBotStateResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid bot ID format",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    },
                    "404": {
                        "description": "Bot not found",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/bots/{botId}/stop": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "It stops a bot based on the botId.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Bot"
                ],
                "summary": "StopBot stop a bot if exists.",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823",
                        "description": "Bot ID for retrieval",
                        "name": "botId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success"
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/exchanges": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "List exchanges with optional filters and pagination",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Exchange"
                ],
                "summary": "List exchanges.",
                "parameters": [
                    {
                        "type": "string",
                        "example": "binance",
                        "x-order": "0",
                        "description": "Exchange id to filter the results",
                        "name": "exchangeId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "Binance",
                        "x-order": "1",
                        "description": "Name to filter the results",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "exchange_id",
                            "name",
                            "created_at"
                        ],
                        "type": "string",
                        "default": "created_at",
                        "example": "created_at",
                        "x-order": "31",
                        "description": "Field, by which the results should be ordered",
                        "name": "orderBy",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "DESC",
                            "ASC"
                        ],
                        "type": "string",
                        "default": "DESC",
                        "example": "DESC",
                        "x-order": "32",
                        "description": "Sets the order direction for sorting results (ASC or DESC).",
                        "name": "orderDirection",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "example": 10,
                        "x-order": "33",
                        "description": "Specifies the maximum number of items to return per page.",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "example": 0,
                        "x-order": "34",
                        "description": "Specifies the starting position of the items to be retrieved.",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Response-array_Exchange"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/exchanges/{exchangeId}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Fetch an exchange by providing its exchangeID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Exchange"
                ],
                "summary": "Fetch an exchange",
                "parameters": [
                    {
                        "type": "string",
                        "example": "binance",
                        "x-order": "0",
                        "description": "Exchange id to be fetched",
                        "name": "exchangeId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Response-Exchange"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/groups": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "List param groups with optional filters and pagination",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Param Groups"
                ],
                "summary": "List param groups",
                "parameters": [
                    {
                        "type": "string",
                        "example": "highRiskGroup",
                        "x-order": "0",
                        "description": "Filter param groups by Name",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "e7cf3a21-9c6b-4a01-af13-2c8e8f91a6bb",
                        "x-order": "1",
                        "description": "Filter by user that created this group",
                        "name": "createdByUserId",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "name",
                            "created_by_user_id",
                            "created_at"
                        ],
                        "type": "string",
                        "default": "created_at",
                        "example": "created_at",
                        "x-order": "31",
                        "description": "Specifies the field by which the results should be ordered.",
                        "name": "orderBy",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "DESC",
                            "ASC"
                        ],
                        "type": "string",
                        "default": "DESC",
                        "example": "DESC",
                        "x-order": "32",
                        "description": "Sets the order direction for sorting results (ASC or DESC).",
                        "name": "orderDirection",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "example": 10,
                        "x-order": "33",
                        "description": "Specifies the maximum number of items to return per page.",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "example": 0,
                        "x-order": "34",
                        "description": "Specifies the starting position of the items to be retrieved.",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Response-array_ParamGroup"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Creates a param group by providing a name",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Param Groups"
                ],
                "summary": "Create a param group",
                "parameters": [
                    {
                        "description": "Param Group Name",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateParamGroupRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/Response-ParamGroup"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/groups/{groupId}": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Delete a param group by providing its ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Param Groups"
                ],
                "summary": "Delete a param group",
                "parameters": [
                    {
                        "type": "string",
                        "example": "123e4567-e89b-12d3-a456-************",
                        "x-order": "0",
                        "description": "ID of the param group to be deleted",
                        "name": "groupId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/notifications/push": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "List registered push notifications of a user with optional filtering and ordering.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notification"
                ],
                "summary": "List push notifications.",
                "parameters": [
                    {
                        "type": "string",
                        "example": "ios",
                        "x-order": "0",
                        "description": "Platform which the token was subscribed",
                        "name": "platform",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "platform",
                            "created_at"
                        ],
                        "type": "string",
                        "default": "created_at",
                        "example": "created_at",
                        "x-order": "31",
                        "description": "Specifies the field by which the results should be ordered.",
                        "name": "orderBy",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "DESC",
                            "ASC"
                        ],
                        "type": "string",
                        "default": "DESC",
                        "example": "DESC",
                        "x-order": "32",
                        "description": "Sets the order direction for sorting results (ASC or DESC).",
                        "name": "orderDirection",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "example": 10,
                        "x-order": "33",
                        "description": "Specifies the maximum number of items to return per page.",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "example": 0,
                        "x-order": "34",
                        "description": "Specifies the starting position of the items to be retrieved.",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/PageResponse-array_PushNotification"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Register or update a push notification device by providing the device token and platform.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notification"
                ],
                "summary": "Register or update a push notification device.",
                "parameters": [
                    {
                        "description": "Device Token and Platform",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreatePushNotificationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/Response-PushNotification"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/notifications/push/{deviceToken}": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Delete a push notification device by providing the device token.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notification"
                ],
                "summary": "Delete a push notification device.",
                "parameters": [
                    {
                        "type": "string",
                        "example": "dkYBSBhG1jCVUwJ6vdOn-R",
                        "description": "Device Token to Delete",
                        "name": "deviceToken",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/notifications/subscribe": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "List notification subscriptions with optional filtering and ordering.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notification"
                ],
                "summary": "List notification subscriptions.",
                "parameters": [
                    {
                        "type": "string",
                        "example": "bot:offline",
                        "x-order": "0",
                        "description": "EventType specifies which kind of event the user will receive.",
                        "name": "eventType",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823",
                        "x-order": "1",
                        "description": "ResourceID is the entity associated with the EventType this is, a bot or account.",
                        "name": "resourceId",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "eventType",
                            "created_at"
                        ],
                        "type": "string",
                        "default": "created_at",
                        "example": "created_at",
                        "x-order": "31",
                        "description": "Specifies the field by which the results should be ordered.",
                        "name": "orderBy",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "DESC",
                            "ASC"
                        ],
                        "type": "string",
                        "default": "DESC",
                        "example": "DESC",
                        "x-order": "32",
                        "description": "Sets the order direction for sorting results (ASC or DESC).",
                        "name": "orderDirection",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "example": 10,
                        "x-order": "33",
                        "description": "Specifies the maximum number of items to return per page.",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "example": 0,
                        "x-order": "34",
                        "description": "Specifies the starting position of the items to be retrieved.",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/PageResponse-array_NotificationSubscription"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Create a new notification subscription by specifying the event type and resource ID.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notification"
                ],
                "summary": "Create a notification subscription.",
                "parameters": [
                    {
                        "description": "Event Type and Resource ID",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateNotificationSubscriptionRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/Response-array_NotificationSubscription"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/notifications/subscribe/bulkDelete": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Delete notification subscriptions by providing an array of subscription IDs.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notification"
                ],
                "summary": "Delete notification subscriptions.",
                "parameters": [
                    {
                        "description": "Subscription IDs to Delete",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/DeleteNotificationSubscriptionRequest"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/params": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "List parameters with optional filtering and ordering based on scope or scopeId.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Params"
                ],
                "summary": "List parameters.",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "example": [
                            "bot",
                            "account",
                            "gateway",
                            "param_group"
                        ],
                        "x-order": "0",
                        "description": "Array of scope values for filtering",
                        "name": "scope",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "example": [
                            "123e4567-e89b-12d3-a456-************"
                        ],
                        "x-order": "1",
                        "description": "Array of scope IDs for filtering",
                        "name": "scopeId",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "scope",
                            "key",
                            "created_by_id",
                            "created_at"
                        ],
                        "type": "string",
                        "default": "created_at",
                        "example": "created_at",
                        "x-order": "31",
                        "description": "Specifies the field by which the results should be ordered.",
                        "name": "orderBy",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "DESC",
                            "ASC"
                        ],
                        "type": "string",
                        "default": "DESC",
                        "example": "DESC",
                        "x-order": "32",
                        "description": "Sets the order direction for sorting results (ASC or DESC).",
                        "name": "orderDirection",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "example": 10,
                        "x-order": "33",
                        "description": "Specifies the maximum number of items to return per page.",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "example": 0,
                        "x-order": "34",
                        "description": "Specifies the starting position of the items to be retrieved.",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/PageResponse-array_Param"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Creates parameters for one or more scopes (gateway, account, bot, etc.) in a single request.\nWhen the Scope is 'gateway' or 'account', the scopeId must be a valid accountId.\nWhen the Scope is 'bot', the scopeId must be a valid botId.\nWhen the Scope is 'param_group', the scopeId must be a valid paramGroupId.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Params"
                ],
                "summary": "Create parameters.",
                "parameters": [
                    {
                        "description": "JSON object with scopes (gateway, account) containing scopeId and params",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateParamsRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/Response-array_Param"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/params/bulkDelete": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Delete parameters by their keys and scope with the specified scopeID.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Params"
                ],
                "summary": "Delete parameters.",
                "parameters": [
                    {
                        "description": "Scope ID, Scope, and Keys",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/DeleteParamsRequest"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "403": {
                        "description": "You are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/params/groups/bots": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "List bot param group relations with optional filters and pagination",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Param Groups"
                ],
                "summary": "List bot param group relations",
                "parameters": [
                    {
                        "type": "string",
                        "example": "123e4567-e89b-12d3-a456-426614174002",
                        "x-order": "0",
                        "description": "Filter relations by the user who created them",
                        "name": "createdByUserId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "123e4567-e89b-12d3-a456-************",
                        "x-order": "1",
                        "description": "Filter relations by Bot ID",
                        "name": "botId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "123e4567-e89b-12d3-a456-426614174002",
                        "x-order": "2",
                        "description": "Filter relations by param Group ID",
                        "name": "groupId",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "param_group_id",
                            "created_by_user_id",
                            "created_at"
                        ],
                        "type": "string",
                        "default": "created_at",
                        "example": "created_at",
                        "x-order": "31",
                        "description": "Specifies the field by which the results should be ordered",
                        "name": "orderBy",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "DESC",
                            "ASC"
                        ],
                        "type": "string",
                        "default": "DESC",
                        "example": "DESC",
                        "x-order": "32",
                        "description": "Sets the order direction for sorting results (ASC or DESC).",
                        "name": "orderDirection",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "example": 10,
                        "x-order": "33",
                        "description": "Specifies the maximum number of items to return per page.",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 0,
                        "example": 0,
                        "x-order": "34",
                        "description": "Specifies the starting position of the items to be retrieved.",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Response-array_BotParamGroup"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/params/groups/{groupId}/bots/{botId}": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Creates relation between bot and param groups by providing bot and param group IDs",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Param Groups"
                ],
                "summary": "Create bot param group relation",
                "parameters": [
                    {
                        "type": "string",
                        "example": "123e4567-e89b-12d3-a456-************",
                        "x-order": "0",
                        "description": "ID of the bot to create the relation with group",
                        "name": "botId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "123e4567-e89b-12d3-a456-426614174002",
                        "x-order": "1",
                        "description": "ID of the param group",
                        "name": "groupId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/Response-array_BotParamGroup"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Delete bot param group relations specified in the request payload",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Param Groups"
                ],
                "summary": "Delete bot param group relations",
                "parameters": [
                    {
                        "type": "string",
                        "example": "123e4567-e89b-12d3-a456-************",
                        "x-order": "0",
                        "description": "ID of the bot to be removed from a group",
                        "name": "botId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "123e4567-e89b-12d3-a456-426614174002",
                        "x-order": "1",
                        "description": "ID of the param group",
                        "name": "groupId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/params/{botId}/activeParams": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Gets the active parameters based on botId.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Params"
                ],
                "summary": "Get active parameters.",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823",
                        "description": "Bot ID for retrieval",
                        "name": "botId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/PageResponse-array_Param"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/regions": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "List regions.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Region"
                ],
                "summary": "Fetch a list of regions.",
                "responses": {
                    "200": {
                        "description": "Regions",
                        "schema": {
                            "$ref": "#/definitions/PageResponse-array_Region"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/regions/{regionId}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Fetch a region by its id.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Region"
                ],
                "summary": "Fetch a region by its id.",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823",
                        "description": "Region ID for retrieval",
                        "name": "regionId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/Response-Region"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/trollbox/filter": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Retrieve messages that fall within the specified time range.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Trollbox"
                ],
                "summary": "Search messages by time range.",
                "parameters": [
                    {
                        "description": "Time Range",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/SearchMessagesByTimeRangeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Matching Messages",
                        "schema": {
                            "$ref": "#/definitions/Response-array_Message"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/trollbox/history": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Retrieve chat message history with optional pagination parameters.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Trollbox"
                ],
                "summary": "Get chat message history.",
                "parameters": [
                    {
                        "type": "string",
                        "example": "MjAyMy0wOS0xMVQxODoxNDoyMy43MTg2MzVa",
                        "x-order": "0",
                        "description": "Cursor for pagination",
                        "name": "cursor",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "example": 30,
                        "x-order": "1",
                        "description": "Page size for pagination",
                        "name": "pageSize",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Message History",
                        "schema": {
                            "$ref": "#/definitions/Response-MessageHistoryResponse"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/trollbox/search": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Retrieve messages that match the specified content.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Trollbox"
                ],
                "summary": "Search messages by content.",
                "parameters": [
                    {
                        "description": "Search Content",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/SearchMessagesByContentRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Matching Messages",
                        "schema": {
                            "$ref": "#/definitions/Response-array_Message"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/users/info": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Retrieves a list of users.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get a list of users.",
                "responses": {
                    "200": {
                        "description": "List of Users",
                        "schema": {
                            "$ref": "#/definitions/Response-array_User"
                        }
                    },
                    "401": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/users/login": {
            "post": {
                "description": "Authenticates a user based on email and password, generating a session token.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "User login.",
                "parameters": [
                    {
                        "description": "Login information",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Login successful",
                        "schema": {
                            "$ref": "#/definitions/Response-LoginResponse"
                        }
                    },
                    "401": {
                        "description": "invalid email or password",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/users/logout": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Terminates the user's session, logs them out, and disconnects any associated WebSocket connection.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "User logout.",
                "responses": {
                    "204": {
                        "description": "Logout successful"
                    },
                    "401": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/users/me": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Retrieves details of the authenticated user.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get user details.",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/Response-User"
                        }
                    },
                    "403": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/users/password": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Changes the user's password by verifying the old password,\nvalidating the new password, and updating it in the database.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Change user's password.",
                "parameters": [
                    {
                        "description": "Change Password Information",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/ChangeUserPasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "401": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/users/sessions/active": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Retrieves a list of sessions associated with the authenticated user.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get user active sessions.",
                "responses": {
                    "200": {
                        "description": "List of User Sessions",
                        "schema": {
                            "$ref": "#/definitions/Response-array_Session"
                        }
                    },
                    "401": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/users/sessions/bulkDelete": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Terminates all sessions associated with the authenticated user, logging them out,\nand disconnecting any associated WebSocket connections.",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Delete all user sessions.",
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "401": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        },
        "/users/sessions/{sessionId}": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "Deletes a user session identified by the provided session ID.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Delete a user session.",
                "parameters": [
                    {
                        "type": "integer",
                        "example": 13,
                        "description": "Session ID to be deleted",
                        "name": "sessionId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "401": {
                        "description": "you are not authorized to access this resource",
                        "schema": {
                            "$ref": "#/definitions/Error"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "Account": {
            "description": "Accounts is responsible for managing exchanges",
            "type": "object",
            "required": [
                "createdAt",
                "exchangeId",
                "id",
                "regionId",
                "tag",
                "updatedAt"
            ],
            "properties": {
                "id": {
                    "description": "Account UUID",
                    "type": "string",
                    "x-order": "0",
                    "example": "8dfc86a3-a9f7-445e-be53-f2b07d109829"
                },
                "exchangeId": {
                    "description": "Exchange id that is managed by the account",
                    "type": "string",
                    "x-order": "1",
                    "example": "Binance"
                },
                "regionId": {
                    "description": "RegionID information about which region this account operates",
                    "type": "string",
                    "x-order": "2",
                    "example": "aws-tokyo"
                },
                "tag": {
                    "description": "Tag used to identify a unique account with an exchange",
                    "type": "string",
                    "x-order": "3",
                    "example": "MM@BTC_BRL@Binance"
                },
                "createdAt": {
                    "description": "When an account was created",
                    "type": "string",
                    "x-order": "4",
                    "example": "2023-08-19T14:11:07.84483Z"
                },
                "updatedAt": {
                    "description": "Last time when the account was modified",
                    "type": "string",
                    "x-order": "5",
                    "example": "2024-02-04T19:41:44.008444328Z"
                },
                "deletedAt": {
                    "description": "Time when the account was deleted",
                    "type": "string",
                    "x-omitempty": true,
                    "x-order": "6",
                    "example": "2024-17-03T10:11:24.758443321Z"
                }
            }
        },
        "Bot": {
            "description": "Bot entity with details such as ID, account ID, symbol, region, tag, status, and dates.",
            "type": "object",
            "required": [
                "accountId",
                "createdAt",
                "desiredStatus",
                "id",
                "status",
                "symbol",
                "tag",
                "updatedAt"
            ],
            "properties": {
                "id": {
                    "description": "Bot ID",
                    "type": "string",
                    "format": "uuid",
                    "x-order": "0",
                    "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823"
                },
                "accountId": {
                    "description": "Account ID associated with the bot",
                    "type": "string",
                    "format": "uuid",
                    "x-order": "1",
                    "example": "8dfc86a3-a9f7-445e-be53-f2b07d109829"
                },
                "symbol": {
                    "description": "Symbol associated with the bot",
                    "type": "string",
                    "x-order": "2",
                    "example": "BTCUSD"
                },
                "tag": {
                    "description": "Tag used to identify the bot",
                    "type": "string",
                    "x-order": "4",
                    "example": "atomic-mm-mercado-bitcoin"
                },
                "status": {
                    "description": "Status of the bot",
                    "type": "string",
                    "x-order": "5",
                    "example": "running"
                },
                "desiredStatus": {
                    "description": "DesiredStatus used to identify the desired state of the bot",
                    "type": "string",
                    "x-order": "6",
                    "example": "running"
                },
                "createdAt": {
                    "description": "Date when the bot was created",
                    "type": "string",
                    "format": "date-time",
                    "x-order": "7",
                    "example": "2023-01-15T12:30:45Z"
                },
                "updatedAt": {
                    "description": "Date when the bot was last updated",
                    "type": "string",
                    "format": "date-time",
                    "x-order": "8",
                    "example": "2023-02-20T15:45:30Z"
                },
                "deletedAt": {
                    "description": "Date when the bot was deleted (if applicable)",
                    "type": "string",
                    "format": "date-time",
                    "x-omitempty": true,
                    "x-order": "9",
                    "example": "2023-03-25T09:20:15Z"
                }
            }
        },
        "BotParamGroup": {
            "type": "object",
            "required": [
                "botId",
                "createdAt",
                "createdByUserID",
                "groupId"
            ],
            "properties": {
                "botId": {
                    "type": "string",
                    "x-order": "0",
                    "example": "123e4567-e89b-12d3-a456-************"
                },
                "groupId": {
                    "type": "string",
                    "x-order": "1",
                    "example": "123e4567-e89b-12d3-a456-426614174002"
                },
                "createdByUserID": {
                    "type": "string",
                    "x-order": "2",
                    "example": "123e4567-e89b-12d3-a456-************"
                },
                "createdAt": {
                    "type": "string",
                    "x-order": "3",
                    "example": "2024-02-10T12:30:45Z"
                }
            }
        },
        "ChangeUserPasswordRequest": {
            "description": "Request payload for changing a user's password.",
            "type": "object",
            "required": [
                "currentPassword",
                "newPassword"
            ],
            "properties": {
                "currentPassword": {
                    "description": "User's current password",
                    "type": "string",
                    "x-order": "0",
                    "example": "********"
                },
                "newPassword": {
                    "description": "User's new password must be greater than eight characters",
                    "type": "string",
                    "x-order": "1",
                    "example": "********"
                }
            }
        },
        "CreateAccountRequest": {
            "description": "Request payload for creating an account.",
            "type": "object",
            "required": [
                "exchangeId",
                "regionId",
                "tag"
            ],
            "properties": {
                "exchangeId": {
                    "description": "Exchange id that is managed by the account",
                    "type": "string",
                    "x-order": "0",
                    "example": "Binance"
                },
                "regionId": {
                    "description": "RegionID region where account operates",
                    "type": "string",
                    "x-order": "1",
                    "example": "us-east-2"
                },
                "tag": {
                    "description": "Tag used to identify a unique account with an exchange",
                    "type": "string",
                    "x-order": "2",
                    "example": "MM@BTC_BRL@Binance"
                }
            }
        },
        "CreateBotRequest": {
            "description": "Request payload for creating a new bot with account ID, symbol, region, and tag.",
            "type": "object",
            "required": [
                "accountId",
                "symbol",
                "tag"
            ],
            "properties": {
                "accountId": {
                    "description": "Account ID associated with the bot",
                    "type": "string",
                    "format": "uuid",
                    "x-order": "0",
                    "example": "8dfc86a3-a9f7-445e-be53-f2b07d109829"
                },
                "symbol": {
                    "description": "Symbol associated with the bot",
                    "type": "string",
                    "x-order": "1",
                    "example": "BTCUSD"
                },
                "tag": {
                    "description": "Tag used to identify the bot",
                    "type": "string",
                    "x-order": "3",
                    "example": "atomic-mm-mercado-bitcoin"
                }
            }
        },
        "CreateNotificationSubscriptionRequest": {
            "description": "Request payload for creating a notification subscription.",
            "type": "object",
            "required": [
                "subscriptions"
            ],
            "properties": {
                "subscriptions": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": [
                            "eventType",
                            "resourceID"
                        ],
                        "properties": {
                            "eventType": {
                                "description": "Type of event for the subscription",
                                "type": "string",
                                "x-order": "0",
                                "example": "bot:offline"
                            },
                            "resourceID": {
                                "description": "Resource ID associated with the subscription",
                                "type": "string",
                                "x-order": "1",
                                "example": "12345"
                            }
                        }
                    }
                }
            }
        },
        "CreateParamGroupRequest": {
            "description": "Request payload for creating a param group",
            "type": "object",
            "required": [
                "name",
                "priority"
            ],
            "properties": {
                "name": {
                    "description": "Name of the param group",
                    "type": "string",
                    "x-order": "0",
                    "example": "highRiskStrategy"
                },
                "priority": {
                    "description": "Priority is used to define the precedence order when there two groups with same key value",
                    "type": "integer",
                    "x-order": "1",
                    "example": 1
                }
            }
        },
        "CreateParamsRequest": {
            "description": "Request payload for creating parameters, organized by scope.",
            "type": "object",
            "properties": {
                "account": {
                    "description": "Parameters for the 'account' scope",
                    "allOf": [
                        {
                            "$ref": "#/definitions/ScopeParamsPayload"
                        }
                    ]
                },
                "bot": {
                    "description": "Parameters for the 'bot' scope",
                    "allOf": [
                        {
                            "$ref": "#/definitions/ScopeParamsPayload"
                        }
                    ]
                },
                "gateway": {
                    "description": "Parameters for the 'gateway' scope",
                    "allOf": [
                        {
                            "$ref": "#/definitions/ScopeParamsPayload"
                        }
                    ]
                },
                "param_group": {
                    "description": "Parameters for the 'param_group' scope",
                    "allOf": [
                        {
                            "$ref": "#/definitions/ScopeParamsPayload"
                        }
                    ]
                }
            }
        },
        "CreatePushNotificationRequest": {
            "description": "Request payload for creating or updating a push notification.",
            "type": "object",
            "required": [
                "deviceToken",
                "platform"
            ],
            "properties": {
                "deviceToken": {
                    "description": "Device token for the push notification",
                    "type": "string",
                    "x-order": "0",
                    "example": "dkYBSBhG1jCVUwJ6vdOn-R"
                },
                "platform": {
                    "description": "Platform of the device (web, ios, android)",
                    "type": "string",
                    "enum": [
                        "web",
                        "ios",
                        "android"
                    ],
                    "x-order": "1",
                    "example": "ios"
                }
            }
        },
        "DeleteNotificationSubscriptionRequest": {
            "description": "Request payload for deleting notification subscriptions.",
            "type": "object",
            "required": [
                "subscriptionIds"
            ],
            "properties": {
                "subscriptionIds": {
                    "description": "Array of subscription IDs to be deleted",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    },
                    "x-order": "0",
                    "example": [
                        1,
                        2,
                        3
                    ]
                }
            }
        },
        "DeleteParamsRequest": {
            "description": "Request payload for deleting parameters for a specific scope.",
            "type": "object",
            "required": [
                "keys",
                "scope",
                "scopeId"
            ],
            "properties": {
                "scopeId": {
                    "description": "Scope ID for the parameters",
                    "type": "string",
                    "x-order": "0",
                    "example": "123e4567-e89b-12d3-a456-************"
                },
                "scope": {
                    "description": "Scope of the parameters (bot, account, gateway)",
                    "type": "string",
                    "x-order": "1",
                    "example": "bot"
                },
                "keys": {
                    "description": "Array of parameter requests",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "x-order": "2",
                    "example": [
                        "apikey",
                        "minSize",
                        "minSpread"
                    ]
                }
            }
        },
        "Details": {
            "description": "Additional details about a user session, including IP, device type, device name, platform type, and location.",
            "type": "object",
            "required": [
                "deviceName",
                "deviceType",
                "ip",
                "location",
                "platformType"
            ],
            "properties": {
                "ip": {
                    "description": "IP address of the device",
                    "type": "string",
                    "x-order": "0",
                    "example": "***********"
                },
                "deviceType": {
                    "description": "Type of device (e.g., mobile, desktop)",
                    "type": "string",
                    "enum": [
                        "mobile",
                        "desktop"
                    ],
                    "x-order": "1",
                    "example": "mobile"
                },
                "deviceName": {
                    "description": "Name of the device",
                    "type": "string",
                    "x-order": "2",
                    "example": "John's iPhone"
                },
                "platformType": {
                    "description": "Type of platform (e.g., iOS, Android, Web)",
                    "type": "string",
                    "enum": [
                        "ios",
                        "android",
                        "web"
                    ],
                    "x-order": "3",
                    "example": "ios"
                },
                "location": {
                    "description": "Location details including country, city, and area",
                    "allOf": [
                        {
                            "$ref": "#/definitions/Location"
                        }
                    ],
                    "x-order": "4"
                }
            }
        },
        "Error": {
            "description": "Error is the basic structure used by our API",
            "type": "object",
            "required": [
                "code",
                "message",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "Status is the HTTP status code",
                    "type": "integer",
                    "x-order": "0",
                    "example": 403
                },
                "code": {
                    "description": "Code is a section to identifier the module of the error",
                    "type": "string",
                    "x-order": "1",
                    "example": "Forbidden"
                },
                "message": {
                    "description": "Message describing the error response if exists",
                    "type": "string",
                    "x-order": "2",
                    "example": "you are not authorized to access this resource"
                },
                "details": {
                    "description": "Details field for additional error details",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    },
                    "x-omitempty": true,
                    "x-order": "3",
                    "example": {
                        "auth": "missing Authorization header token"
                    }
                }
            }
        },
        "Exchange": {
            "description": "Model for exchange",
            "type": "object",
            "required": [
                "createdAt",
                "exchangeId",
                "name"
            ],
            "properties": {
                "exchangeId": {
                    "description": "Exchange identifier",
                    "type": "string",
                    "x-order": "0",
                    "example": "binance"
                },
                "name": {
                    "description": "Pretty name of the exchange",
                    "type": "string",
                    "x-order": "1",
                    "example": "Binance"
                },
                "createdAt": {
                    "description": "Time when the parameter was created",
                    "type": "string",
                    "format": "date-time",
                    "x-order": "2",
                    "example": "2024-02-10T12:30:45Z"
                }
            }
        },
        "GetBotStateResponse": {
            "description": "Contains the detailed state information for a specific bot, including its ID, operational data, parameters, and the time the state was captured.",
            "type": "object",
            "required": [
                "botId",
                "data",
                "params",
                "time"
            ],
            "properties": {
                "botId": {
                    "description": "The unique identifier of the bot.",
                    "type": "string",
                    "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823"
                },
                "data": {
                    "description": "A map containing the bot's current operational data.",
                    "type": "object",
                    "additionalProperties": true
                },
                "params": {
                    "description": "A map containing the bot's configuration parameters.",
                    "type": "object",
                    "additionalProperties": true
                },
                "time": {
                    "description": "The timestamp when the state was recorded.",
                    "type": "string",
                    "format": "date-time",
                    "example": "2023-08-01T14:30:00Z"
                }
            }
        },
        "ListBotsStateResponse": {
            "description": "Contains the detailed state information for a bot, including its ID, operational data, parameters, and the time the state was captured.",
            "type": "object",
            "required": [
                "botId",
                "data",
                "params",
                "time"
            ],
            "properties": {
                "botId": {
                    "description": "The unique identifier of the bot.",
                    "type": "string",
                    "example": "1dfc543a2-a5d6-445e-be53-f2b07d109823"
                },
                "data": {
                    "description": "A map containing the bot's current operational data.",
                    "type": "object",
                    "additionalProperties": true
                },
                "params": {
                    "description": "A map containing the bot's configuration parameters.",
                    "type": "object",
                    "additionalProperties": true
                },
                "time": {
                    "description": "The timestamp when the state was recorded.",
                    "type": "string",
                    "format": "date-time",
                    "example": "2023-08-01T14:30:00Z"
                }
            }
        },
        "Location": {
            "description": "Location details including country, city, and area.",
            "type": "object",
            "required": [
                "area",
                "city",
                "country"
            ],
            "properties": {
                "country": {
                    "description": "Country name",
                    "type": "string",
                    "x-order": "0",
                    "example": "United States"
                },
                "city": {
                    "description": "City name",
                    "type": "string",
                    "x-order": "1",
                    "example": "New York"
                },
                "area": {
                    "description": "Area name",
                    "type": "string",
                    "x-order": "2",
                    "example": "Manhattan"
                }
            }
        },
        "LoginRequest": {
            "description": "Request payload for user login.",
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "description": "User's email address",
                    "type": "string",
                    "x-order": "0",
                    "example": "<EMAIL>"
                },
                "password": {
                    "description": "User's password",
                    "type": "string",
                    "x-order": "1",
                    "example": "P@ssw0rd"
                }
            }
        },
        "LoginResponse": {
            "description": "Response payload for a successful user login.",
            "type": "object",
            "required": [
                "expiry",
                "token"
            ],
            "properties": {
                "token": {
                    "description": "Session token",
                    "type": "string",
                    "x-order": "0",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ"
                },
                "expiry": {
                    "description": "Token expiration time",
                    "type": "string",
                    "x-order": "1",
                    "example": "2024-02-07T15:45:00.000Z"
                }
            }
        },
        "Message": {
            "description": "Message structure for chat messages",
            "type": "object",
            "required": [
                "content",
                "id",
                "seq",
                "timestamp",
                "userId"
            ],
            "properties": {
                "id": {
                    "description": "Message ID",
                    "type": "string",
                    "format": "uuid",
                    "x-order": "0",
                    "example": "7ae09324-87d3-4029-b96c-8a0769f300cd"
                },
                "userId": {
                    "description": "User ID associated with the message",
                    "type": "string",
                    "format": "uuid",
                    "x-order": "1",
                    "example": "df5e7fdc-6ad1-42ee-98b4-b6159d9bd232"
                },
                "seq": {
                    "description": "Sequence number of the message",
                    "type": "integer",
                    "x-order": "2",
                    "example": 1
                },
                "content": {
                    "description": "Content of the message",
                    "type": "string",
                    "x-order": "3",
                    "example": "Hello, World."
                },
                "timestamp": {
                    "description": "Timestamp when the message was sent",
                    "type": "string",
                    "x-order": "4",
                    "example": "2023-09-11T18:35:32.891411Z"
                }
            }
        },
        "MessageHistoryResponse": {
            "description": "MessageHistoryResponse is the response structure for retrieving message history.",
            "type": "object",
            "required": [
                "hashNext",
                "messages",
                "nextCursor"
            ],
            "properties": {
                "hashNext": {
                    "description": "Indicates whether there is more history available",
                    "type": "boolean",
                    "x-order": "0",
                    "example": true
                },
                "nextCursor": {
                    "description": "Cursor for the next page of history",
                    "type": "string",
                    "x-order": "1",
                    "example": "MjAyMy0wOS0xMVQxODoxNDoyMy43MTg2MzVa"
                },
                "messages": {
                    "description": "List of messages in the history",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Message"
                    },
                    "x-order": "2"
                }
            }
        },
        "NotificationSubscription": {
            "description": "Model for a notification subscription.",
            "type": "object",
            "required": [
                "createdAt",
                "eventType",
                "id",
                "resourceId",
                "userId"
            ],
            "properties": {
                "id": {
                    "description": "ID of the notification subscription",
                    "type": "integer",
                    "x-order": "0",
                    "example": 1
                },
                "eventType": {
                    "description": "Type of event associated with the subscription",
                    "type": "string",
                    "x-order": "1",
                    "example": "new_notification"
                },
                "userId": {
                    "description": "User ID linked to the subscription",
                    "type": "string",
                    "x-order": "2",
                    "example": "user123"
                },
                "resourceId": {
                    "description": "Resource ID connected to the subscription",
                    "type": "string",
                    "x-order": "3",
                    "example": "12345"
                },
                "createdAt": {
                    "description": "Time when the subscription was created",
                    "type": "string",
                    "format": "date-time",
                    "x-order": "4",
                    "example": "2024-02-10T12:30:45Z"
                }
            }
        },
        "PageResponse-array_Account": {
            "type": "object",
            "required": [
                "data",
                "limit",
                "offset",
                "status",
                "total"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "total": {
                    "description": "Total indicates the total number of items available, if applicable.",
                    "type": "integer",
                    "x-order": "1",
                    "example": 100
                },
                "offset": {
                    "description": "Offset is the starting index of items in the data response.\nIt represents the position from which items are retrieved in a paginated result set.",
                    "type": "integer",
                    "x-order": "2",
                    "example": 3
                },
                "limit": {
                    "description": "Limit is the size of items in the data response.",
                    "type": "integer",
                    "x-order": "3",
                    "example": 10
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Account"
                    },
                    "x-order": "4"
                }
            }
        },
        "PageResponse-array_Bot": {
            "type": "object",
            "required": [
                "data",
                "limit",
                "offset",
                "status",
                "total"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "total": {
                    "description": "Total indicates the total number of items available, if applicable.",
                    "type": "integer",
                    "x-order": "1",
                    "example": 100
                },
                "offset": {
                    "description": "Offset is the starting index of items in the data response.\nIt represents the position from which items are retrieved in a paginated result set.",
                    "type": "integer",
                    "x-order": "2",
                    "example": 3
                },
                "limit": {
                    "description": "Limit is the size of items in the data response.",
                    "type": "integer",
                    "x-order": "3",
                    "example": 10
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Bot"
                    },
                    "x-order": "4"
                }
            }
        },
        "PageResponse-array_NotificationSubscription": {
            "type": "object",
            "required": [
                "data",
                "limit",
                "offset",
                "status",
                "total"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "total": {
                    "description": "Total indicates the total number of items available, if applicable.",
                    "type": "integer",
                    "x-order": "1",
                    "example": 100
                },
                "offset": {
                    "description": "Offset is the starting index of items in the data response.\nIt represents the position from which items are retrieved in a paginated result set.",
                    "type": "integer",
                    "x-order": "2",
                    "example": 3
                },
                "limit": {
                    "description": "Limit is the size of items in the data response.",
                    "type": "integer",
                    "x-order": "3",
                    "example": 10
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/NotificationSubscription"
                    },
                    "x-order": "4"
                }
            }
        },
        "PageResponse-array_Param": {
            "type": "object",
            "required": [
                "data",
                "limit",
                "offset",
                "status",
                "total"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "total": {
                    "description": "Total indicates the total number of items available, if applicable.",
                    "type": "integer",
                    "x-order": "1",
                    "example": 100
                },
                "offset": {
                    "description": "Offset is the starting index of items in the data response.\nIt represents the position from which items are retrieved in a paginated result set.",
                    "type": "integer",
                    "x-order": "2",
                    "example": 3
                },
                "limit": {
                    "description": "Limit is the size of items in the data response.",
                    "type": "integer",
                    "x-order": "3",
                    "example": 10
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Param"
                    },
                    "x-order": "4"
                }
            }
        },
        "PageResponse-array_PushNotification": {
            "type": "object",
            "required": [
                "data",
                "limit",
                "offset",
                "status",
                "total"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "total": {
                    "description": "Total indicates the total number of items available, if applicable.",
                    "type": "integer",
                    "x-order": "1",
                    "example": 100
                },
                "offset": {
                    "description": "Offset is the starting index of items in the data response.\nIt represents the position from which items are retrieved in a paginated result set.",
                    "type": "integer",
                    "x-order": "2",
                    "example": 3
                },
                "limit": {
                    "description": "Limit is the size of items in the data response.",
                    "type": "integer",
                    "x-order": "3",
                    "example": 10
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/PushNotification"
                    },
                    "x-order": "4"
                }
            }
        },
        "PageResponse-array_Region": {
            "type": "object",
            "required": [
                "data",
                "limit",
                "offset",
                "status",
                "total"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "total": {
                    "description": "Total indicates the total number of items available, if applicable.",
                    "type": "integer",
                    "x-order": "1",
                    "example": 100
                },
                "offset": {
                    "description": "Offset is the starting index of items in the data response.\nIt represents the position from which items are retrieved in a paginated result set.",
                    "type": "integer",
                    "x-order": "2",
                    "example": 3
                },
                "limit": {
                    "description": "Limit is the size of items in the data response.",
                    "type": "integer",
                    "x-order": "3",
                    "example": 10
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Region"
                    },
                    "x-order": "4"
                }
            }
        },
        "Param": {
            "description": "Model for parameters.",
            "type": "object",
            "required": [
                "createdAt",
                "createdById",
                "key",
                "scope",
                "scopeId",
                "value",
                "version"
            ],
            "properties": {
                "scopeId": {
                    "description": "Scope ID associated with the parameter",
                    "type": "string",
                    "x-order": "0",
                    "example": "123e4567-e89b-12d3-a456-************"
                },
                "scope": {
                    "description": "Scope of the parameter (bot, account, gateway)",
                    "type": "string",
                    "x-order": "1",
                    "example": "bot"
                },
                "key": {
                    "description": "Key of the parameter",
                    "type": "string",
                    "x-order": "2",
                    "example": "param_key"
                },
                "value": {
                    "description": "Value of the parameter",
                    "type": "string",
                    "x-order": "3",
                    "example": "param_value"
                },
                "version": {
                    "description": "Version of the parameter",
                    "type": "integer",
                    "x-order": "6",
                    "example": 1
                },
                "createdById": {
                    "description": "ID of the user who created the parameter",
                    "type": "string",
                    "x-order": "7",
                    "example": "user123"
                },
                "createdAt": {
                    "description": "Time when the parameter was created",
                    "type": "string",
                    "format": "date-time",
                    "x-order": "8",
                    "example": "2024-02-10T12:30:45Z"
                }
            }
        },
        "ParamGroup": {
            "type": "object",
            "required": [
                "createdAt",
                "createdByUserID",
                "id",
                "name",
                "priority"
            ],
            "properties": {
                "id": {
                    "type": "string",
                    "x-order": "0",
                    "example": "123e4567-e89b-12d3-a456-************"
                },
                "name": {
                    "type": "string",
                    "x-order": "1",
                    "example": "highRiskGroup"
                },
                "priority": {
                    "type": "integer",
                    "x-order": "1",
                    "example": 1
                },
                "createdByUserID": {
                    "type": "string",
                    "x-order": "2",
                    "example": "123e4567-e89b-12d3-a456-************"
                },
                "createdAt": {
                    "type": "string",
                    "x-order": "3",
                    "example": "2024-02-10T12:30:45Z"
                }
            }
        },
        "ParamRequest": {
            "description": "Request payload for creating a parameter.",
            "type": "object",
            "required": [
                "key",
                "value"
            ],
            "properties": {
                "key": {
                    "description": "Key of the parameter",
                    "type": "string",
                    "x-order": "0",
                    "example": "paramKey"
                },
                "value": {
                    "description": "Value of the parameter",
                    "type": "string",
                    "x-order": "1",
                    "example": "paramValue"
                }
            }
        },
        "PushNotification": {
            "description": "Model for a push notification.",
            "type": "object",
            "required": [
                "createdAt",
                "deviceToken",
                "id",
                "platform",
                "updatedAt",
                "userId"
            ],
            "properties": {
                "id": {
                    "description": "ID of the push notification",
                    "type": "string",
                    "x-order": "0",
                    "example": "1"
                },
                "userId": {
                    "description": "User ID associated with the push notification",
                    "type": "string",
                    "format": "uuid",
                    "x-order": "1",
                    "example": "e7cf3a21-9c6b-4a01-af13-2c8e8f91a6bb"
                },
                "deviceToken": {
                    "description": "Device token for the push notification",
                    "type": "string",
                    "x-order": "2",
                    "example": "dkYBSBhG1jCVUwJ6vdOn-R"
                },
                "platform": {
                    "description": "Platform of the device (web, ios, android)",
                    "type": "string",
                    "x-order": "3",
                    "example": "ios"
                },
                "createdAt": {
                    "description": "Time when the push notification was created",
                    "type": "string",
                    "format": "date-time",
                    "x-order": "4",
                    "example": "2024-02-10T12:30:45Z"
                },
                "updatedAt": {
                    "description": "Time when the push notification was last updated",
                    "type": "string",
                    "format": "date-time",
                    "x-order": "5",
                    "example": "2024-02-10T12:35:00Z"
                }
            }
        },
        "Region": {
            "type": "object",
            "required": [
                "createdAt",
                "description",
                "id",
                "secretKey"
            ],
            "properties": {
                "id": {
                    "description": "Region ID",
                    "type": "string",
                    "x-order": "0",
                    "example": "aws-br"
                },
                "description": {
                    "description": "Description about this region",
                    "type": "string",
                    "x-order": "1",
                    "example": "aws-br"
                },
                "secretKey": {
                    "description": "Secret used by this region",
                    "type": "string",
                    "x-order": "2",
                    "example": "8dfc86a3-a9f7-445e-be53-f2b07d109829"
                },
                "createdAt": {
                    "description": "When the region was created",
                    "type": "string",
                    "x-order": "3",
                    "example": "2024-17-03T10:11:24.758443321Z"
                }
            }
        },
        "Response-Account": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/Account"
                        }
                    ],
                    "x-order": "1"
                }
            }
        },
        "Response-Bot": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/Bot"
                        }
                    ],
                    "x-order": "1"
                }
            }
        },
        "Response-Exchange": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/Exchange"
                        }
                    ],
                    "x-order": "1"
                }
            }
        },
        "Response-GetBotStateResponse": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/GetBotStateResponse"
                        }
                    ],
                    "x-order": "1"
                }
            }
        },
        "Response-LoginResponse": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/LoginResponse"
                        }
                    ],
                    "x-order": "1"
                }
            }
        },
        "Response-MessageHistoryResponse": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/MessageHistoryResponse"
                        }
                    ],
                    "x-order": "1"
                }
            }
        },
        "Response-ParamGroup": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/ParamGroup"
                        }
                    ],
                    "x-order": "1"
                }
            }
        },
        "Response-PushNotification": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/PushNotification"
                        }
                    ],
                    "x-order": "1"
                }
            }
        },
        "Response-Region": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/Region"
                        }
                    ],
                    "x-order": "1"
                }
            }
        },
        "Response-User": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "allOf": [
                        {
                            "$ref": "#/definitions/User"
                        }
                    ],
                    "x-order": "1"
                }
            }
        },
        "Response-array_BotParamGroup": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/BotParamGroup"
                    },
                    "x-order": "1"
                }
            }
        },
        "Response-array_Exchange": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Exchange"
                    },
                    "x-order": "1"
                }
            }
        },
        "Response-array_ListBotsStateResponse": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/ListBotsStateResponse"
                    },
                    "x-order": "1"
                }
            }
        },
        "Response-array_Message": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Message"
                    },
                    "x-order": "1"
                }
            }
        },
        "Response-array_NotificationSubscription": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/NotificationSubscription"
                    },
                    "x-order": "1"
                }
            }
        },
        "Response-array_Param": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Param"
                    },
                    "x-order": "1"
                }
            }
        },
        "Response-array_ParamGroup": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/ParamGroup"
                    },
                    "x-order": "1"
                }
            }
        },
        "Response-array_Session": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Session"
                    },
                    "x-order": "1"
                }
            }
        },
        "Response-array_User": {
            "type": "object",
            "required": [
                "data",
                "status"
            ],
            "properties": {
                "status": {
                    "description": "The Status field denotes the HTTP status code of the response.",
                    "type": "integer",
                    "x-order": "0",
                    "example": 200
                },
                "data": {
                    "description": "The Data field carries the payload of the API response.",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/User"
                    },
                    "x-order": "1"
                }
            }
        },
        "ScopeParamsPayload": {
            "description": "Payload containing the scope ID and parameters for a single scope.",
            "type": "object",
            "required": [
                "params",
                "scopeId"
            ],
            "properties": {
                "params": {
                    "description": "Array of parameter key-value pairs",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/ParamRequest"
                    }
                },
                "scopeId": {
                    "description": "Scope ID for the parameters",
                    "type": "string",
                    "example": "cc015665-8f5c-4429-b48f-4b3743bb391e"
                }
            }
        },
        "SearchMessagesByContentRequest": {
            "description": "SearchMessagesByContentRequest is used to specify the content for filtering messages.",
            "type": "object",
            "required": [
                "content"
            ],
            "properties": {
                "content": {
                    "description": "Content to search for in messages",
                    "type": "string",
                    "x-order": "0",
                    "example": "Hello, World."
                }
            }
        },
        "SearchMessagesByTimeRangeRequest": {
            "description": "SearchMessagesByTimeRangeRequest is used to specify the time range for filtering messages.",
            "type": "object",
            "required": [
                "endTime",
                "startTime"
            ],
            "properties": {
                "startTime": {
                    "description": "Start time of the search range",
                    "type": "string",
                    "x-order": "0",
                    "example": "1694456065.811983"
                },
                "endTime": {
                    "description": "End time of the search range",
                    "type": "string",
                    "x-order": "1",
                    "example": "1694456065.811983"
                }
            }
        },
        "Session": {
            "description": "User session details including session ID, user ID, device information, and location.",
            "type": "object",
            "required": [
                "createdAt",
                "details",
                "expiry",
                "id",
                "updatedAt",
                "userID"
            ],
            "properties": {
                "id": {
                    "description": "Session ID",
                    "type": "integer",
                    "x-order": "0",
                    "example": 12345
                },
                "userID": {
                    "description": "User ID associated with the session",
                    "type": "string",
                    "format": "uuid",
                    "x-order": "1",
                    "example": "e7cf3a21-9c6b-4a01-af13-2c8e8f91a6bb"
                },
                "details": {
                    "description": "Session details including IP, device type, device name, platform type, and location",
                    "allOf": [
                        {
                            "$ref": "#/definitions/Details"
                        }
                    ],
                    "x-order": "2"
                },
                "expiry": {
                    "description": "Session expiration time",
                    "type": "string",
                    "x-order": "3",
                    "example": "2024-02-07T15:45:00.000Z"
                },
                "createdAt": {
                    "description": "Time when the session was created",
                    "type": "string",
                    "x-order": "4",
                    "example": "2023-08-19T14:11:07.84483Z"
                },
                "updatedAt": {
                    "description": "Last time when the session was updated",
                    "type": "string",
                    "x-order": "5",
                    "example": "2024-02-04T19:41:44.008444328Z"
                }
            }
        },
        "UpdateBotRequest": {
            "description": "Request parameters for updating an existing bot by providing an account ID, symbol, region or status.",
            "type": "object",
            "properties": {
                "accountId": {
                    "description": "Account ID associated with the bot",
                    "type": "string",
                    "format": "uuid",
                    "x-order": "0",
                    "example": "8dfc86a3-a9f7-445e-be53-f2b07d109829"
                },
                "symbol": {
                    "description": "Symbol associated with the bot",
                    "type": "string",
                    "x-order": "1",
                    "example": "BTCUSD"
                },
                "regionId": {
                    "description": "RegionID information for the bot",
                    "type": "string",
                    "x-order": "2",
                    "example": "us-east-2"
                },
                "tag": {
                    "description": "Tag used to identify the bot",
                    "type": "string",
                    "x-order": "3",
                    "example": "atomic-mm-mercado-bitcoin"
                }
            }
        },
        "User": {
            "description": "User information about individuals.",
            "type": "object",
            "required": [
                "createdAt",
                "email",
                "id",
                "name",
                "role",
                "updatedAt"
            ],
            "properties": {
                "id": {
                    "description": "User ID",
                    "type": "string",
                    "format": "uuid",
                    "x-order": "0",
                    "example": "e7cf3a21-9c6b-4a01-af13-2c8e8f91a6bb"
                },
                "name": {
                    "description": "User's full name",
                    "type": "string",
                    "x-order": "1",
                    "example": "John Doe"
                },
                "email": {
                    "description": "User's email address",
                    "type": "string",
                    "x-order": "2",
                    "example": "<EMAIL>"
                },
                "role": {
                    "description": "User's role in the system",
                    "type": "string",
                    "x-order": "3",
                    "example": "role_admin"
                },
                "createdAt": {
                    "description": "Time when the user was created",
                    "type": "string",
                    "x-order": "4",
                    "example": "2023-08-19T14:11:07.84483Z"
                },
                "updatedAt": {
                    "description": "Last time when the user was modified",
                    "type": "string",
                    "x-order": "5",
                    "example": "2024-02-04T19:41:44.008444328Z"
                }
            }
        }
    },
    "securityDefinitions": {
        "Bearer": {
            "description": "Type \"Bearer\" followed by a space and the token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.10.5",
	Host:             "api-term.htz-fsn-02.liquidbooks.io",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "Bot Manager API",
	Description:      "API for managing cryptocurrency trading bots on multiple exchanges.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
