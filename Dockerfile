# syntax=docker/dockerfile:experimental
# =================================================================
# Base Stage
# Sets up the Go environment and caches dependencies.
# =================================================================
FROM golang:1.24-bullseye AS build-base

WORKDIR /app

ENV GO111MODULE=on
ENV CGO_ENABLED=0
ENV GOPRIVATE=github.com/herenow

# Configure Git to use SSH for private repositories and add GitHub's SSH key.
RUN git config --global url.ssh://**************/.insteadOf https://github.com/ && \
    mkdir /root/.ssh && ssh-keyscan github.com >> /root/.ssh/known_hosts

# Use cache mounts to speed up dependency downloads on subsequent builds.
# This step runs only when go.mod or go.sum change.
RUN --mount=type=cache,target=/go/pkg/mod \
  --mount=type=cache,target=/root/.cache/go-build \
  --mount=type=bind,source=go.sum,target=go.sum \
  --mount=type=bind,source=go.mod,target=go.mod \
  --mount=type=secret,id=sshKey,dst=/root/.ssh/id_rsa \
  go mod download

# =================================================================
# Development Stage
# Used for local development with hot-reloading (air).
# =================================================================
FROM build-base AS dev

WORKDIR /app

# Install air for hot reload. Delve for debugging is commented out but available.
RUN go install github.com/cosmtrek/air@v1.51.0 && go install github.com/go-delve/delve/cmd/dlv@v1.25.0

COPY . .

# Ensure all dependencies are tidy.
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    --mount=type=secret,id=sshKey,dst=/root/.ssh/id_rsa \
    go mod tidy

# Build the application for the dev environment.
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    --mount=type=secret,id=sshKey,dst=/root/.ssh/id_rsa \
    go build -gcflags="all=-N -l" -o /usr/bin/botmanager ./cmd/app/ && chmod +x /usr/bin/botmanager

# The default command runs air for hot-reloading.
CMD  ["air", "-c", ".air.toml"]

# =================================================================
# Production Build Stage
# Compiles the final, optimized binary for production.
# =================================================================
FROM build-base AS prod-build

WORKDIR /app

COPY . .

# <<-- ADJUSTMENT: Accept VERSION as a build argument -->>
# This argument will be passed in from the Makefile or CI/CD pipeline.
ARG VERSION=dev

# Compile the application, injecting the version string using ldflags.
# The '-w -s' flags strip debug information to reduce binary size.
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    --mount=type=secret,id=sshKey,dst=/root/.ssh/id_rsa \
    go build -a -tags netgo -ldflags="-s -w -X 'github.com/herenow/atomic-bm.Version=${VERSION}'" -o botmanager ./cmd/app/

# =================================================================
# Final Production Stage
# Creates the minimal, secure final image.
# =================================================================
FROM ubuntu:22.04 AS prod

# Install required certificates for HTTPS requests.
RUN apt-get update && apt-get install -y --no-install-recommends ca-certificates && rm -rf /var/lib/apt/lists/*

WORKDIR /usr/bin/

# Copy the compiled application binary from the production build stage.
COPY --from=prod-build /app/botmanager botmanager

# Create a non-root user for security and make the binary executable.
RUN useradd --system --uid 1001 nonroot \
    && chmod +x botmanager

# Switch to the non-root user.
USER nonroot

# Set gin mode to release for production performance.
ENV GIN_MODE=release

# Expose the application port.
EXPOSE 8000

# Specifies the default executable to run when the container starts.
ENTRYPOINT ["/usr/bin/botmanager"]

# Default command for the entrypoint.
CMD ["server"]
