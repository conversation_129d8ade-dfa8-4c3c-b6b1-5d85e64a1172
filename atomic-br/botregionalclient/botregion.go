package botregionalclient

import (
	"context"
	"fmt"
	"sync"

	"github.com/herenow/atomic-protocols/gen/atomic/api/botregion/v1"
	"github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1"
	"github.com/herenow/atomic-protocols/rpc"
	"google.golang.org/protobuf/types/known/structpb"
)

// IBotRegionalClient defines the interface for the bot regional client.
type IBotRegionalClient interface {
	NewBot(ctx context.Context, regionID string, bot *proto.Bot) error
	ListBots(ctx context.Context, regionID string) ([]*proto.Bot, error)
	GetBot(ctx context.Context, regionID string, botID string) (*proto.Bot, error)
	GetBotState(ctx context.Context, regionID string, botID string) (*proto.State, error)
	StartBot(ctx context.Context, regionID string, botID string) error
	StopBot(ctx context.Context, regionID string, botID string) error
	UpdateBotParams(ctx context.Context, regionID string, req []BotParams) error
	UpdateGtwOpts(ctx context.Context, regionID string, req []GtwOpts) error
	StopAllBots(ctx context.Context, regionID string) error
	StartAllBots(ctx context.Context, regionID string) error
	ListBotsState(ctx context.Context, regionID string) ([]*proto.State, error)
}

type Client struct {
	regionMap map[string]string
	clients   map[string]botregion.BotRegionServiceClient
	mu        sync.RWMutex
}

// New creates a pool of botregion clients it receives a map which expects the key
// to be the regionID and the value to be the actual address to the server.
func New(regionMap map[string]string) (IBotRegionalClient, error) {
	c := &Client{
		regionMap: regionMap,
		clients:   make(map[string]botregion.BotRegionServiceClient),
	}
	return c, nil
}

func (c *Client) resolveClient(regionID string) (botregion.BotRegionServiceClient, error) {
	c.mu.RLock()
	client, ok := c.clients[regionID]
	c.mu.RUnlock()

	if ok {
		return client, nil
	}

	// If the client doesn't exist, create it and store it
	c.mu.Lock()
	defer c.mu.Unlock()

	addr, ok := c.regionMap[regionID]
	if !ok {
		return nil, fmt.Errorf("invalid region ID: %s", regionID)
	}

	conn, err := rpc.NewClient(addr, nil, nil, nil)
	if err != nil {
		return nil, err
	}

	client = botregion.NewBotRegionServiceClient(conn)
	c.clients[regionID] = client
	return client, nil
}

func (c *Client) NewBot(ctx context.Context, regionID string, bot *proto.Bot) error {
	api, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	_, err = api.NewBot(ctx, &botregion.NewBotRequest{Bot: bot})
	return err
}

func (c *Client) GetBot(ctx context.Context, regionID string, botID string) (*proto.Bot, error) {
	api, err := c.resolveClient(regionID)
	if err != nil {
		return nil, err
	}

	res, err := api.GetBot(ctx, &botregion.GetBotRequest{BotId: botID})
	if err != nil {
		return nil, err
	}

	return res.GetBot(), err
}

func (c *Client) StartBot(ctx context.Context, regionID string, botID string) error {
	api, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	_, err = api.StartBot(ctx, &botregion.StartBotRequest{BotId: botID})
	return err
}

func (c *Client) StopBot(ctx context.Context, regionID string, botID string) error {
	api, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	_, err = api.StopBot(ctx, &botregion.StopBotRequest{BotId: botID})
	return err
}

func (c *Client) ListBots(ctx context.Context, regionID string) ([]*proto.Bot, error) {
	api, err := c.resolveClient(regionID)
	if err != nil {
		return nil, err
	}

	res, err := api.ListBots(ctx, nil)
	if err != nil {
		return nil, err
	}
	return res.GetBots(), nil
}

func (c *Client) GetBotState(ctx context.Context, regionID string, botID string) (*proto.State, error) {
	api, err := c.resolveClient(regionID)
	if err != nil {
		return nil, err
	}

	res, err := api.GetBotState(ctx, &botregion.GetBotStateRequest{BotId: botID})
	if err != nil {
		return nil, err
	}
	return res.GetState(), nil
}

type BotParams struct {
	BotID  string
	Params *structpb.Struct
}

func (c *Client) UpdateBotParams(ctx context.Context, regionID string, req []BotParams) error {
	api, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	var params []*botregion.BotParamsUpdate
	for _, param := range req {
		params = append(params, &botregion.BotParamsUpdate{
			BotId:  param.BotID,
			Params: param.Params,
		})
	}

	_, err = api.UpdateBotParams(ctx, &botregion.UpdateBotParamsRequest{
		BotUpdates: params,
	})
	return err
}

func (c *Client) StartAllBots(ctx context.Context, regionID string) error {
	api, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	_, err = api.StartAllBots(ctx, nil)
	return err
}

func (c *Client) StopAllBots(ctx context.Context, regionID string) error {
	api, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	_, err = api.StopAllBots(ctx, nil)
	return err
}

type GtwOpts struct {
	BotID   string
	GtwOpts *structpb.Struct
}

func (c *Client) UpdateGtwOpts(ctx context.Context, regionID string, req []GtwOpts) error {
	api, err := c.resolveClient(regionID)
	if err != nil {
		return err
	}

	var gtwOpts []*botregion.UpdateGatewayOptions
	for _, param := range req {
		gtwOpts = append(gtwOpts, &botregion.UpdateGatewayOptions{
			BotId:      param.BotID,
			GtwOptions: param.GtwOpts,
		})
	}

	_, err = api.UpdateGatewayOpts(ctx, &botregion.UpdateGatewayOptionsRequest{
		GtwUpdates: gtwOpts,
	})

	return err
}

func (c *Client) ListBotsState(ctx context.Context, regionID string) ([]*proto.State, error) {
	api, err := c.resolveClient(regionID)
	if err != nil {
		return nil, err
	}
	res, err := api.ListBotsState(ctx, nil)
	if err != nil {
		return nil, err
	}
	return res.GetBotsState(), nil
}
