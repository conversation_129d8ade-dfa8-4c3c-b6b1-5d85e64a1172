// Code generated by mockery. DO NOT EDIT.

package botregionalclient

import (
	context "context"

	proto "github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1"
	mock "github.com/stretchr/testify/mock"
)

// IBotRegionalClientMocked is an autogenerated mock type for the IBotRegionalClient type
type IBotRegionalClientMocked struct {
	mock.Mock
}

type IBotRegionalClientMocked_Expecter struct {
	mock *mock.Mock
}

func (_m *IBotRegionalClientMocked) EXPECT() *IBotRegionalClientMocked_Expecter {
	return &IBotRegionalClientMocked_Expecter{mock: &_m.Mock}
}

// GetBot provides a mock function with given fields: ctx, regionID, botID
func (_m *IBotRegionalClientMocked) GetBot(ctx context.Context, regionID string, botID string) (*proto.Bot, error) {
	ret := _m.Called(ctx, regionID, botID)

	if len(ret) == 0 {
		panic("no return value specified for GetBot")
	}

	var r0 *proto.Bot
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*proto.Bot, error)); ok {
		return rf(ctx, regionID, botID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *proto.Bot); ok {
		r0 = rf(ctx, regionID, botID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*proto.Bot)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, regionID, botID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IBotRegionalClientMocked_GetBot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBot'
type IBotRegionalClientMocked_GetBot_Call struct {
	*mock.Call
}

// GetBot is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - botID string
func (_e *IBotRegionalClientMocked_Expecter) GetBot(ctx interface{}, regionID interface{}, botID interface{}) *IBotRegionalClientMocked_GetBot_Call {
	return &IBotRegionalClientMocked_GetBot_Call{Call: _e.mock.On("GetBot", ctx, regionID, botID)}
}

func (_c *IBotRegionalClientMocked_GetBot_Call) Run(run func(ctx context.Context, regionID string, botID string)) *IBotRegionalClientMocked_GetBot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_GetBot_Call) Return(_a0 *proto.Bot, _a1 error) *IBotRegionalClientMocked_GetBot_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IBotRegionalClientMocked_GetBot_Call) RunAndReturn(run func(context.Context, string, string) (*proto.Bot, error)) *IBotRegionalClientMocked_GetBot_Call {
	_c.Call.Return(run)
	return _c
}

// GetBotState provides a mock function with given fields: ctx, regionID, botID
func (_m *IBotRegionalClientMocked) GetBotState(ctx context.Context, regionID string, botID string) (*proto.State, error) {
	ret := _m.Called(ctx, regionID, botID)

	if len(ret) == 0 {
		panic("no return value specified for GetBotState")
	}

	var r0 *proto.State
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*proto.State, error)); ok {
		return rf(ctx, regionID, botID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *proto.State); ok {
		r0 = rf(ctx, regionID, botID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*proto.State)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, regionID, botID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IBotRegionalClientMocked_GetBotState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBotState'
type IBotRegionalClientMocked_GetBotState_Call struct {
	*mock.Call
}

// GetBotState is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - botID string
func (_e *IBotRegionalClientMocked_Expecter) GetBotState(ctx interface{}, regionID interface{}, botID interface{}) *IBotRegionalClientMocked_GetBotState_Call {
	return &IBotRegionalClientMocked_GetBotState_Call{Call: _e.mock.On("GetBotState", ctx, regionID, botID)}
}

func (_c *IBotRegionalClientMocked_GetBotState_Call) Run(run func(ctx context.Context, regionID string, botID string)) *IBotRegionalClientMocked_GetBotState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_GetBotState_Call) Return(_a0 *proto.State, _a1 error) *IBotRegionalClientMocked_GetBotState_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *IBotRegionalClientMocked_GetBotState_Call) RunAndReturn(run func(context.Context, string, string) (*proto.State, error)) *IBotRegionalClientMocked_GetBotState_Call {
	_c.Call.Return(run)
	return _c
}

// NewBot provides a mock function with given fields: ctx, regionID, bot
func (_m *IBotRegionalClientMocked) NewBot(ctx context.Context, regionID string, bot *proto.Bot) error {
	ret := _m.Called(ctx, regionID, bot)

	if len(ret) == 0 {
		panic("no return value specified for NewBot")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *proto.Bot) error); ok {
		r0 = rf(ctx, regionID, bot)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotRegionalClientMocked_NewBot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewBot'
type IBotRegionalClientMocked_NewBot_Call struct {
	*mock.Call
}

// NewBot is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - bot *proto.Bot
func (_e *IBotRegionalClientMocked_Expecter) NewBot(ctx interface{}, regionID interface{}, bot interface{}) *IBotRegionalClientMocked_NewBot_Call {
	return &IBotRegionalClientMocked_NewBot_Call{Call: _e.mock.On("NewBot", ctx, regionID, bot)}
}

func (_c *IBotRegionalClientMocked_NewBot_Call) Run(run func(ctx context.Context, regionID string, bot *proto.Bot)) *IBotRegionalClientMocked_NewBot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*proto.Bot))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_NewBot_Call) Return(_a0 error) *IBotRegionalClientMocked_NewBot_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotRegionalClientMocked_NewBot_Call) RunAndReturn(run func(context.Context, string, *proto.Bot) error) *IBotRegionalClientMocked_NewBot_Call {
	_c.Call.Return(run)
	return _c
}

// StartBot provides a mock function with given fields: ctx, regionID, botID
func (_m *IBotRegionalClientMocked) StartBot(ctx context.Context, regionID string, botID string) error {
	ret := _m.Called(ctx, regionID, botID)

	if len(ret) == 0 {
		panic("no return value specified for StartBot")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, regionID, botID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotRegionalClientMocked_StartBot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartBot'
type IBotRegionalClientMocked_StartBot_Call struct {
	*mock.Call
}

// StartBot is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - botID string
func (_e *IBotRegionalClientMocked_Expecter) StartBot(ctx interface{}, regionID interface{}, botID interface{}) *IBotRegionalClientMocked_StartBot_Call {
	return &IBotRegionalClientMocked_StartBot_Call{Call: _e.mock.On("StartBot", ctx, regionID, botID)}
}

func (_c *IBotRegionalClientMocked_StartBot_Call) Run(run func(ctx context.Context, regionID string, botID string)) *IBotRegionalClientMocked_StartBot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_StartBot_Call) Return(_a0 error) *IBotRegionalClientMocked_StartBot_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotRegionalClientMocked_StartBot_Call) RunAndReturn(run func(context.Context, string, string) error) *IBotRegionalClientMocked_StartBot_Call {
	_c.Call.Return(run)
	return _c
}

// StopBot provides a mock function with given fields: ctx, regionID, botID
func (_m *IBotRegionalClientMocked) StopBot(ctx context.Context, regionID string, botID string) error {
	ret := _m.Called(ctx, regionID, botID)

	if len(ret) == 0 {
		panic("no return value specified for StopBot")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, regionID, botID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotRegionalClientMocked_StopBot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopBot'
type IBotRegionalClientMocked_StopBot_Call struct {
	*mock.Call
}

// StopBot is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - botID string
func (_e *IBotRegionalClientMocked_Expecter) StopBot(ctx interface{}, regionID interface{}, botID interface{}) *IBotRegionalClientMocked_StopBot_Call {
	return &IBotRegionalClientMocked_StopBot_Call{Call: _e.mock.On("StopBot", ctx, regionID, botID)}
}

func (_c *IBotRegionalClientMocked_StopBot_Call) Run(run func(ctx context.Context, regionID string, botID string)) *IBotRegionalClientMocked_StopBot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_StopBot_Call) Return(_a0 error) *IBotRegionalClientMocked_StopBot_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotRegionalClientMocked_StopBot_Call) RunAndReturn(run func(context.Context, string, string) error) *IBotRegionalClientMocked_StopBot_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateBotParam provides a mock function with given fields: ctx, regionID, batchReq
func (_m *IBotRegionalClientMocked) UpdateBotParam(ctx context.Context, regionID string, batchReq []BotParams) error {
	ret := _m.Called(ctx, regionID, batchReq)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBotParam")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []BotParams) error); ok {
		r0 = rf(ctx, regionID, batchReq)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IBotRegionalClientMocked_UpdateBotParam_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateBotParam'
type IBotRegionalClientMocked_UpdateBotParam_Call struct {
	*mock.Call
}

// UpdateBotParam is a helper method to define mock.On call
//   - ctx context.Context
//   - regionID string
//   - batchReq []BotParams
func (_e *IBotRegionalClientMocked_Expecter) UpdateBotParam(ctx interface{}, regionID interface{}, batchReq interface{}) *IBotRegionalClientMocked_UpdateBotParam_Call {
	return &IBotRegionalClientMocked_UpdateBotParam_Call{Call: _e.mock.On("UpdateBotParam", ctx, regionID, batchReq)}
}

func (_c *IBotRegionalClientMocked_UpdateBotParam_Call) Run(run func(ctx context.Context, regionID string, batchReq []BotParams)) *IBotRegionalClientMocked_UpdateBotParam_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].([]BotParams))
	})
	return _c
}

func (_c *IBotRegionalClientMocked_UpdateBotParam_Call) Return(_a0 error) *IBotRegionalClientMocked_UpdateBotParam_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *IBotRegionalClientMocked_UpdateBotParam_Call) RunAndReturn(run func(context.Context, string, []BotParams) error) *IBotRegionalClientMocked_UpdateBotParam_Call {
	_c.Call.Return(run)
	return _c
}

// NewIBotRegionalClientMocked creates a new instance of IBotRegionalClientMocked. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIBotRegionalClientMocked(t interface {
	mock.TestingT
	Cleanup(func())
}) *IBotRegionalClientMocked {
	mock := &IBotRegionalClientMocked{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
