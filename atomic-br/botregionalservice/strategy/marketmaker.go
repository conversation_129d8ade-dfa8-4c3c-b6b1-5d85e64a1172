package strategy

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	_ "github.com/herenow/atomic-gtw/mapper"
	"github.com/herenow/atomic-tools/pkg/marketdata"
	"github.com/herenow/ordermanager/ordermanager"
	"github.com/herenow/ordermanager/repository"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/structpb"
)

type MarketMakerState struct {
	// Inventory
	BaseInventory  AssetInventory `json:"baseInventory"`
	QuoteInventory AssetInventory `json:"quoteInventory"`
	InventoryPct   float64        `json:"inventoryPct"`

	// Live Orders from OrderManager
	OpenOrders []ordermanager.OrderView `json:"openOrders"`

	// Market & Pricing
	BestBid        float64 `json:"bestBid"`
	BestAsk        float64 `json:"bestAsk"`
	ReferencePrice float64 `json:"referencePrice"`
	QuotedSpread   float64 `json:"quotedSpread"`

	// Executions & PnL
	TimeWindowedStats map[string]ExecutionStatsView `json:"timeWindowedStats"`
	LatestFills       []FillInfo                    `json:"latestFills"`

	// Health
	LastUpdate time.Time `json:"lastUpdate"`
	LastError  string    `json:"lastError,omitempty"`
}

type AssetInventory struct {
	Asset     string  `json:"asset"`
	Available float64 `json:"available"`
	Total     float64 `json:"total"`
}

type FillInfo struct {
	Timestamp time.Time    `json:"timestamp"`
	Side      gateway.Side `json:"side"`
	Amount    float64      `json:"amount"`
	Price     float64      `json:"price"`
	Fee       float64      `json:"fee"`
	FeeAsset  string       `json:"feeAsset"`
}

type ExecutionStatsView struct {
	TimeFrame   string  `json:"timeFrame"`
	TotalVolume float64 `json:"totalVolume"`
	NetPosition float64 `json:"netPosition"`
	Pnl         float64 `json:"pnl"`
	AvgSpread   float64 `json:"avgSpread"`
	BuyCount    int64   `json:"buyCount"`
	SellCount   int64   `json:"sellCount"`
}

// Status is a type local to the botsservice package.
type Status int

const (
	Starting Status = iota
	Running
	Stopping
	Stopped
	Paused
	Error
	Pending // Bot created, configuration needs validation.
	Invalid // Bot has a configuration error (e.g., missing required params).
	Unknown
)

func (s Status) String() string {
	return [...]string{"starting", "running", "stopping", "stopped", "paused", "error", "pending", "invalid", "unknown"}[s]
}

// ToStatus converts a string to a Status enum.
func ToStatus(s string) Status {
	switch s {
	case "running":
		return Running
	case "starting":
		return Starting
	case "stopping":
		return Stopping
	case "stopped":
		return Stopped
	case "paused":
		return Paused
	case "error":
		return Error
	case "pending":
		return Pending
	case "invalid":
		return Invalid
	default:
		return Unknown
	}
}

type MarketMakerBot struct {
	opts   Params
	runCtx context.Context
	cancel context.CancelFunc
	logger *zap.Logger

	gtw     gateway.Gateway
	gtwOpts gateway.Options
	market  gateway.Market

	orderMgr     ordermanager.OrderManager
	orderMgrRepo repository.Repository

	execMgr *ExecutionManager
	mktdMgr marketdata.Manager

	status   Status
	lastErr  error
	mu       sync.Mutex
	balances []gateway.Balance
}

// NewMarketMakerBot constructor is updated to accept the marketdata.Manager.
func NewMarketMakerBot(
	logger *zap.Logger,
	gtwOpts gateway.Options,
	params Params,
	mom ordermanager.OrderManager,
	gtw gateway.Gateway,
	mdm marketdata.Manager, // Accept the shared marketdata manager
) *MarketMakerBot {
	botLogger := logger.With(zap.String("botID", params.BotID), zap.String("symbol", params.Symbol))
	botLogger.Info("Creating new MarketMakerBot object")
	return &MarketMakerBot{
		opts:     params,
		status:   Stopped,
		gtwOpts:  gtwOpts,
		logger:   botLogger,
		orderMgr: mom,
		gtw:      gtw,
		mktdMgr:  mdm,
	}
}

func (m *MarketMakerBot) Start() error {
	m.mu.Lock()
	if m.status == Running || m.status == Starting {
		m.mu.Unlock()
		m.logger.Warn("Start command ignored, bot is already running or starting", zap.String("status", m.status.String()))
		return nil
	}
	m.logger.Info("Starting bot")
	m.status = Starting
	m.lastErr = nil
	m.mu.Unlock()

	// Create a cancellable context for the bot's lifecycle
	m.runCtx, m.cancel = context.WithCancel(context.Background())

	if err := m.initialize(); err != nil {
		m.mu.Lock()
		m.status = Error
		m.lastErr = err
		m.mu.Unlock()
		m.logger.Error("Bot initialization failed", zap.Error(err))
		return err
	}

	go m.run()

	go m.periodicBalanceRefresh(m.runCtx)

	m.mu.Lock()
	m.status = Running
	m.mu.Unlock()
	m.logger.Info("Bot started successfully")
	return nil
}

// initialize sets up the bot's private gateway, order manager, and subscribes to public data.
func (m *MarketMakerBot) initialize() error {
	m.logger.Info("Initializing bot...")

	dbPath := fmt.Sprintf("./data/mom_%s", m.opts.BotID)
	m.logger.Debug("Creating repository for bot", zap.String("dbPath", dbPath))

	repo, err := repository.NewRepository(dbPath)
	if err != nil {
		return fmt.Errorf("failed to create order manager repository for bot %s: %w", m.opts.BotID, err)
	}
	m.orderMgrRepo = repo

	m.logger.Debug("Creating gateway for private actions", zap.String("exchange", m.opts.Exchange))
	exchange, ok := gateway.ExchangeBySymbol(m.opts.Exchange)
	if !ok {
		return fmt.Errorf("exchange %s not found", m.opts.Exchange)
	}

	gtw, ok := gateway.NewByExchange(exchange, m.gtwOpts)
	if !ok {
		return fmt.Errorf("failed to create gateway for exchange %s", m.opts.Exchange)
	}
	m.gtw = gtw

	m.logger.Debug("Connecting to private gateway...")
	if err = m.gtw.Connect(); err != nil {
		return fmt.Errorf("failed to connect to gateway: %w", err)
	}
	m.logger.Debug("Private gateway connected")

	m.logger.Debug("Creating order manager")
	mom := ordermanager.New(exchange, m.gtw.AccountGateway(), m.logger, m.orderMgrRepo)
	m.orderMgr = mom

	m.logger.Debug("Fetching market details...")
	markets, err := m.gtw.GetMarkets()
	if err != nil {
		return fmt.Errorf("failed to get markets: %w", err)
	}

	for _, market := range markets {
		if market.Symbol == m.opts.Symbol {
			m.market = market
			break
		}
	}
	if m.market.Symbol == "" {
		return fmt.Errorf("market %s not found", m.opts.Symbol)
	}
	m.logger.Debug("Market details fetched", zap.Any("market", m.market))

	m.logger.Debug("Initializing order manager...")
	if err = m.orderMgr.Init(context.Background()); err != nil {
		return fmt.Errorf("failed to initialize order manager: %w", err)
	}
	m.logger.Debug("Order manager initialized")

	m.execMgr = NewExecutionManager(m.market, m.logger)
	m.logger.Debug("Execution manager created")

	m.logger.Debug("Setting gateway options on shared marketdata manager")
	m.mktdMgr.SetGatewayOptions(exchange, m.gtwOpts)

	m.logger.Debug("Subscribing to market data via MarketData Manager...")
	if err = m.mktdMgr.SubscribeMarkets([]gateway.Market{m.market}); err != nil {
		return fmt.Errorf("failed to subscribe to market via marketdata manager: %w", err)
	}
	m.logger.Debug("Subscribed to market data")

	m.logger.Debug("Performing initial state update...")
	if err = m.updateLocalState(); err != nil {
		return fmt.Errorf("failed to update initial local state: %w", err)
	}
	m.logger.Info("Bot initialization complete")
	return nil
}

func (m *MarketMakerBot) updateLocalState() error {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.logger.Debug("Updating local state (balances)")

	balances, err := m.gtw.AccountGateway().Balances()
	if err != nil {
		m.logger.Error("Failed to get balances", zap.Error(err))
		return fmt.Errorf("failed to get balances: %w", err)
	}

	m.balances = balances
	m.logger.Debug("Balances updated", zap.Any("balances", balances))

	return nil
}

func (m *MarketMakerBot) periodicBalanceRefresh(ctx context.Context) {
	if m.opts.BalanceSyncInterval == 0 {
		m.opts.BalanceSyncInterval = time.Second * 5
	}

	ticker := time.NewTicker(m.opts.BalanceSyncInterval)
	defer ticker.Stop()
	m.logger.Info("Starting periodic balance refresh", zap.Duration("interval", m.opts.BalanceSyncInterval))

	for {
		select {
		case <-ctx.Done():
			m.logger.Info("Stopping periodic balance refresh")
			return
		case <-ticker.C:
			m.mu.Lock()
			balances, err := m.gtw.AccountGateway().Balances()
			if err != nil {
				m.logger.Error("Periodic balance refresh failed", zap.Error(err))
			} else {
				m.balances = balances
			}
			m.mu.Unlock()
		}
	}
}

// run is the main loop that processes gateway ticks and market data updates.
func (m *MarketMakerBot) run() {
	m.logger.Info("Starting bot's main processing loop")
	// The bot now also listens to updates from the marketdata manager
	marketUpdateCh := m.mktdMgr.SubscribeMarketUpdate(m.market)

	for {
		select {
		case <-m.runCtx.Done():
			m.logger.Info("Stopping bot's main processing loop")
			return
		case tick := <-m.gtw.Tick():
			// Ticks are now only for private data (fills, order updates)
			m.orderMgr.ProcessTick(tick)
			m.processPrivateTickEvents(tick)
		case update, ok := <-marketUpdateCh:
			if !ok {
				m.logger.Warn("Market data channel closed, stopping.")
				m.Stop() // Trigger a graceful stop if the data feed dies
				return
			}
			// A public market update (book change, trade, etc.) has occurred.
			// This is our new trigger to update orders.
			if update.IsSnapshot || len(update.Bids) > 0 || len(update.Asks) > 0 {
				m.updateOrders()
			}
		}
	}
}

// processPrivateTickEvents handles events from the gateway tick that are relevant to the bot's own state.
func (m *MarketMakerBot) processPrivateTickEvents(tick gateway.Tick) {
	for _, event := range tick.EventLog {
		switch event.Type {
		case gateway.FillEvent:
			fill := event.Data.(gateway.Fill)
			m.logger.Info("Received fill event", zap.Any("fill", fill))
			m.execMgr.AddExecution(gateway.Execution{
				Market:   m.market,
				TradeID:  fill.ID,
				OrderID:  fill.OrderID,
				Side:     fill.Side,
				Amount:   fill.Amount,
				Price:    fill.Price,
				Time:     fill.Timestamp,
				Fee:      fill.Fee,
				FeeAsset: fill.FeeAsset,
			})
			m.updateOrders() // Re-evaluate orders after a fill
		case gateway.OrderUpdateEvent:
			m.updateOrders() // Re-evaluate orders after an update
		}
	}
}

func (m *MarketMakerBot) getBaseAssetBalance() float64 {
	for _, balance := range m.balances {
		if balance.Asset == m.market.Pair.Base {
			return balance.Available
		}
	}
	return 0.0
}

// calculateSizingFactors calculates the adjusted spreads and order sizes based on inventory and market conditions.
func (m *MarketMakerBot) calculateSizingFactors(baseAssetBalance, refPrice, bestBid, bestAsk float64) (bidPrice, askPrice, bidSize, askSize float64) {
	bidPrice = refPrice * (1 - m.opts.MinSpread)
	askPrice = refPrice * (1 + m.opts.MinSpread)
	bidSize = m.opts.OrderSize
	askSize = m.opts.OrderSize

	if m.opts.MaxPosition > 0 && m.opts.SpreadSizingBuy > 0 {
		inventoryRatio := baseAssetBalance / m.opts.MaxPosition
		if inventoryRatio < 0 {
			inventoryRatio = 0
		}
		if inventoryRatio > 1 {
			inventoryRatio = 1
		}

		extraBuySpread := inventoryRatio * m.opts.SpreadSizingBuy
		adjustedBuySpread := m.opts.MinSpread + extraBuySpread
		bidPrice = refPrice * (1 - adjustedBuySpread)
	}

	if m.opts.OrderSizing == "by-spread" && m.opts.OrderSizingMultiplier > 0 && bestBid > 0 && m.opts.MinSpread > 0 {
		currentMarketSpread := (bestAsk / bestBid) - 1

		spreadFactor := (m.opts.MinSpread - currentMarketSpread) / m.opts.MinSpread
		sizeAdjustment := spreadFactor * m.opts.OrderSizingMultiplier

		bidSize = m.opts.OrderSize * (1 + sizeAdjustment)
		askSize = m.opts.OrderSize * (1 + sizeAdjustment)
	}

	if m.opts.MaxOrderSize > 0 && bidSize > m.opts.MaxOrderSize {
		bidSize = m.opts.MaxOrderSize
	}
	if m.opts.MinOrderSize > 0 && bidSize < m.opts.MinOrderSize {
		bidSize = m.opts.MinOrderSize
	}
	if bidSize < m.market.MinimumOrderSize {
		bidSize = m.market.MinimumOrderSize
	}

	if m.opts.MaxOrderSize > 0 && askSize > m.opts.MaxOrderSize {
		askSize = m.opts.MaxOrderSize
	}
	if m.opts.MinOrderSize > 0 && askSize < m.opts.MinOrderSize {
		askSize = m.opts.MinOrderSize
	}
	if askSize < m.market.MinimumOrderSize {
		askSize = m.market.MinimumOrderSize
	}

	return bidPrice, askPrice, bidSize, askSize
}

// updateOrders now gets book data from the MarketData Manager.
func (m *MarketMakerBot) updateOrders() {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Get the current state, including the book, from the marketdata manager
	marketState, ok := m.mktdMgr.GetMarketState(m.market)
	if !ok || !marketState.IsReady() {
		m.logger.Warn("Cannot update orders, market state is not ready or available.")
		return
	}

	book := marketState.Book()
	bestBid, hasBid := book.Bids.Top()
	bestAsk, hasAsk := book.Asks.Top()

	if !hasBid || !hasAsk || bestBid.Value == 0 || bestAsk.Value == 0 {
		m.logger.Warn("Cannot update orders, order book is incomplete", zap.Float64("bestBid", bestBid.Value), zap.Float64("bestAsk", bestAsk.Value))
		return
	}

	openOrders := m.orderMgr.GetOpenOrders()
	refPrice := (bestBid.Value + bestAsk.Value) / 2
	baseAssetBalance := m.getBaseAssetBalance()
	bidPrice, askPrice, bidSize, askSize := m.calculateSizingFactors(baseAssetBalance, refPrice, bestBid.Value, bestAsk.Value)

	if bidSize < m.market.MinimumOrderSize || askSize < m.market.MinimumOrderSize {
		m.logger.Warn("Calculated order size is below market minimum, skipping update. Will cancel existing orders.",
			zap.Float64("bidSize", bidSize),
			zap.Float64("askSize", askSize),
			zap.Float64("marketMinSize", m.market.MinimumOrderSize))
		var desiredOrders []gateway.Order
		m.reconcileOrders(openOrders, desiredOrders)
		return
	}

	desiredOrders := []gateway.Order{
		{Market: m.market, Side: gateway.Bid, Price: bidPrice, Amount: bidSize},
		{Market: m.market, Side: gateway.Ask, Price: askPrice, Amount: askSize},
	}

	m.reconcileOrders(openOrders, desiredOrders)
}

// reconcileOrders compares the current open orders with the desired orders and takes action.
// This function was extracted from updateOrders to improve readability.
func (m *MarketMakerBot) reconcileOrders(openOrders []*ordermanager.ManagedOrder, desiredOrders []gateway.Order) {
	desiredSideExists := make(map[gateway.Side]bool)

	for _, managedOrder := range openOrders {
		openOrder := managedOrder.View()
		isDesired := false
		for _, desiredOrder := range desiredOrders {
			if openOrder.Side == desiredOrder.Side {
				isDesired = true
				break
			}
		}

		if !isDesired {
			m.logger.Info("Cancelling undesired order", zap.String("internalID", openOrder.InternalID))
			go m.cancelOrderAsync(managedOrder)
		} else {
			desiredSideExists[openOrder.Side] = true
		}
	}

	for _, desiredOrder := range desiredOrders {
		if !desiredSideExists[desiredOrder.Side] {
			params := ordermanager.SendOrderParams{
				Market: m.market,
				Side:   desiredOrder.Side,
				Price:  desiredOrder.Price,
				Amount: desiredOrder.Amount,
				Tag:    "market-maker",
			}
			go m.sendOrderAsync(params)
		}
	}
}

// sendOrderAsync sends an order and waits for its confirmation asynchronously.
func (m *MarketMakerBot) sendOrderAsync(params ordermanager.SendOrderParams) {
	mgtOrder, err := m.orderMgr.SendOrder(context.Background(), params)
	if err != nil {
		m.logger.Error("Failed to send order", zap.Any("params", params), zap.Error(err))
		return
	}

	timeout := 5 * time.Second
	if m.opts.OrderConfirmationTimeoutMs > 0 {
		timeout = time.Duration(m.opts.OrderConfirmationTimeoutMs) * time.Millisecond
	}

	select {
	case err = <-mgtOrder.Confirmed():
		if err != nil {
			m.logger.Error("Order confirmation failed", zap.String("internalID", mgtOrder.View().InternalID), zap.Error(err))
		} else {
			m.logger.Info("Order confirmed successfully", zap.String("internalID", mgtOrder.View().InternalID), zap.String("exchangeOrderID", mgtOrder.View().OrderID))
		}
	case <-time.After(timeout):
		m.logger.Warn("Timeout waiting for order confirmation", zap.String("internalID", mgtOrder.View().InternalID))
	}
}

// cancelOrderAsync cancels an order and waits for its confirmation asynchronously.
func (m *MarketMakerBot) cancelOrderAsync(orderToCancel *ordermanager.ManagedOrder) {
	cancelledOrder, err := m.orderMgr.CancelOrder(context.Background(), orderToCancel.View().InternalID)
	if err != nil {
		m.logger.Error("Failed to initiate order cancellation", zap.String("internalID", orderToCancel.View().InternalID), zap.Error(err))
		return
	}

	timeout := 5 * time.Second
	if m.opts.OrderConfirmationTimeoutMs > 0 {
		timeout = time.Duration(m.opts.OrderConfirmationTimeoutMs) * time.Millisecond
	}

	select {
	case err = <-cancelledOrder.Cancelled():
		if err != nil {
			m.logger.Error("Failed to cancel order", zap.String("internalID", cancelledOrder.View().InternalID), zap.Error(err))
		} else {
			m.logger.Info("Successfully cancelled order", zap.String("internalID", cancelledOrder.View().InternalID))
		}
	case <-time.After(timeout):
		m.logger.Warn("Timeout waiting for order cancellation confirmation", zap.String("internalID", cancelledOrder.View().InternalID))
	}
}

func (m *MarketMakerBot) Stop() error {
	m.mu.Lock()
	if m.status == Stopped || m.status == Stopping {
		m.mu.Unlock()
		m.logger.Warn("Stop command ignored, bot is already stopped or stopping", zap.String("status", m.status.String()))
		return nil
	}
	m.logger.Info("Stopping bot...")
	m.status = Stopping
	m.mu.Unlock()

	if m.cancel != nil {
		m.cancel()
	}

	if m.gtw != nil {
		m.logger.Debug("Closing gateway connection...")
		if err := m.gtw.Close(); err != nil {
			m.logger.Error("failed to close gateway", zap.Error(err))
		}
	}

	m.mu.Lock()
	m.status = Stopped
	m.mu.Unlock()

	m.logger.Info("Bot has been successfully stopped")
	return nil
}

func (m *MarketMakerBot) Close() error {
	m.logger.Info("Closing bot resources (repository)")
	if m.orderMgrRepo != nil {
		return m.orderMgrRepo.Close()
	}
	return nil
}

func (m *MarketMakerBot) GetStatus() (Status, error) {
	m.logger.Debug("Getting bot status", zap.String("status", m.status.String()))
	return m.status, m.lastErr
}

func (m *MarketMakerBot) UpdateParams(params Params) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.logger.Info("Updating bot parameters", zap.Any("newParams", params))
	m.opts = params
	return nil
}

func (m *MarketMakerBot) GetParams() Params {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.opts
}

// GetState now gets book data from the MarketData Manager.
func (m *MarketMakerBot) GetState() (*structpb.Struct, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	var bestBid, bestAsk float64
	marketState, ok := m.mktdMgr.GetMarketState(m.market)
	if ok && marketState.IsReady() {
		book := marketState.Book()
		topBid, hasBid := book.Bids.Top()
		topAsk, hasAsk := book.Asks.Top()
		if hasBid {
			bestBid = topBid.Value
		}
		if hasAsk {
			bestAsk = topAsk.Value
		}
	}

	baseInventory := AssetInventory{Asset: m.market.Pair.Base}
	quoteInventory := AssetInventory{Asset: m.market.Pair.Quote}
	for _, balance := range m.balances {
		if balance.Asset == m.market.Pair.Base {
			baseInventory.Available = balance.Available
			baseInventory.Total = balance.Total
		} else if balance.Asset == m.market.Pair.Quote {
			quoteInventory.Available = balance.Available
			quoteInventory.Total = balance.Total
		}
	}

	var inventoryPct float64
	if m.opts.MaxPosition > 0 {
		inventoryPct = (baseInventory.Total / m.opts.MaxPosition) * 100
	}

	var openOrderViews []ordermanager.OrderView
	if m.orderMgr != nil {
		openManagedOrders := m.orderMgr.GetOpenOrders()
		openOrderViews = make([]ordermanager.OrderView, len(openManagedOrders))
		for i, o := range openManagedOrders {
			openOrderViews[i] = o.View()
		}
	}

	// Populate Time-Windowed Stats
	timeWindowedStats := make(map[string]ExecutionStatsView)
	if m.execMgr != nil {
		for _, tf := range m.execMgr.TimeFrames() {
			stats := m.execMgr.TimeFrameStats(tf)
			timeWindowedStats[tf.String()] = ExecutionStatsView{
				TimeFrame:   tf.String(),
				TotalVolume: stats.MoneyVolume(),
				NetPosition: stats.NetPosition(),
				Pnl:         stats.PnL(),
				AvgSpread:   stats.AvgSpread(),
				BuyCount:    stats.Buy.Count,
				SellCount:   stats.Sell.Count,
			}
		}
	}

	// Populate Latest Fills
	var latestFills []FillInfo
	if m.execMgr != nil {
		const fillLimit = 10
		recentExecs := m.execMgr.GetExecutions(fillLimit)
		latestFills = make([]FillInfo, len(recentExecs))
		for i, exec := range recentExecs {
			latestFills[i] = FillInfo{
				Timestamp: exec.Time,
				Side:      exec.Side,
				Amount:    exec.Amount,
				Price:     exec.Price,
				Fee:       exec.Fee,
				FeeAsset:  exec.FeeAsset,
			}
		}
	}

	state := MarketMakerState{
		BaseInventory:     baseInventory,
		QuoteInventory:    quoteInventory,
		InventoryPct:      inventoryPct,
		OpenOrders:        openOrderViews,
		BestBid:           bestBid,
		BestAsk:           bestAsk,
		TimeWindowedStats: timeWindowedStats,
		LatestFills:       latestFills,
		LastUpdate:        time.Now(),
	}

	if bestBid > 0 && bestAsk > 0 {
		state.ReferencePrice = (bestBid + bestAsk) / 2
		state.QuotedSpread = (bestAsk / bestBid) - 1
	}

	if m.lastErr != nil {
		state.LastError = m.lastErr.Error()
	}

	stateBytes, err := json.Marshal(state)
	if err != nil {
		m.logger.Error("Failed to marshal state", zap.Error(err))
		return nil, fmt.Errorf("failed to marshal state: %w", err)
	}

	var stateStruct structpb.Struct
	if err = json.Unmarshal(stateBytes, &stateStruct); err != nil {
		m.logger.Error("Failed to unmarshal state to structpb", zap.Error(err))
		return nil, fmt.Errorf("failed to unmarshal state to structpb: %w", err)
	}

	return &stateStruct, nil
}

func (m *MarketMakerBot) GetGatewayOpts() gateway.Options {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.gtwOpts
}

func (m *MarketMakerBot) UpdateBotGatewayOpts(opts gateway.Options) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.logger.Info("Updating gateway options", zap.Any("newOpts", opts))
	m.gtwOpts = opts
	// Note: A full restart of the bot would be required for these to take effect.
	m.logger.Warn("Gateway options updated, but a bot restart is required for changes to take effect.")
	return nil
}
