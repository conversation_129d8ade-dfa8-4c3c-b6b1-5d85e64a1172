package strategy

import (
	"time"
)

type Params struct {
	BotID     string `json:"botID" description:"Bot identification on DB"`
	AccountID string `json:"accountID" description:"Account identification on DB"`
	Exchange  string `json:"exchange" description:"Exchange to mm in"`
	Symbol    string `json:"symbol" description:"Market to operate"`

	// Bot misc params
	Proxies string `json:"proxies,omitempty" description:"Comma separated list of proxy servers to use"`

	// Market maker parameters
	MinSpread     float64  `json:"minSpread,omitempty" description:"Minimum spread to place orders, the spread is calculated from a given reference price, either from top price, or from ref price. See --spreadFrom."`
	MinSpreadBuy  *float64 `json:"minSpreadBuy,omitempty" description:"Set a custom minimum buy spread."`
	MinSpreadSell *float64 `json:"minSpreadSell,omitempty" description:"Set a custom sell spread."`

	MaxSpread     float64  `json:"maxSpread,omitempty" description:"Max orders to place orders, from the given top/ref price spread."`
	MaxSpreadBuy  *float64 `json:"maxSpreadBuy,omitempty" description:"Max orders to place orders on the bid side."`
	MaxSpreadSell *float64 `json:"maxSpreadSell,omitempty" description:"Max orders to place orders on the ask side."`

	MinSpreadBetween     float64  `json:"minSpreadBetween,omitempty" description:"Minimum spread between orders we place."`
	MinSpreadBetweenBuy  *float64 `json:"minSpreadBetweenBuy,omitempty" description:"Min spread between for the bid side."`
	MinSpreadBetweenSell *float64 `json:"minSpreadBetweenSell,omitempty" description:"Min spread between for the sell side."`

	OrderSize     float64  `json:"orderSize,omitempty" description:"Basis order size. The real order size will depend of other parameters."`
	OrderSizeBuy  *float64 `json:"orderSizeBuy,omitempty" description:"Basis order size for bids"`
	OrderSizeSell *float64 `json:"orderSizeSell,omitempty" description:"Basis order size for asks"`
	MinOrderSize  float64  `json:"minOrderSize,omitempty" description:"Don't place orders less than this size. Useful for manually overiding the markets minimum order size/notional value."`

	MaxOrders     int  `json:"maxOrders,omitempty" description:"Max orders to keep on the order orders" default:"1"`
	MaxOrdersBuy  *int `json:"maxOrdersBuy,omitempty" description:"Max orders for the bid side"`
	MaxOrdersSell *int `json:"maxOrdersSell,omitempty" description:"Max orders for the sell side"`

	ScoreMultiplier float64 `json:"scoreMultiplier,omitempty" description:"Multiplier constant to power multiply the score by when ranking positions on the orderbook"`

	MaxBookDepth      float64 `json:"maxBookDepth,omitempty" description:"Maximum orders depth in stock to rank positions. Don't open orders, after this orders depth."`
	MaxBookLevels     int     `json:"maxBookLevels,omitempty" description:"Maximum orders levels to rank positions. Don't open orders after this many levels."`
	DisplayBookLevels int     `json:"displayBookLevels,omitempty" description:"Levels of the order orders to display on the screen. Doesn't affect trading decisions."`

	MaxPosition      float64 `json:"maxPosition,omitempty" description:"Maximum stock inventory to hold"`
	MinPosition      float64 `json:"minPosition,omitempty" description:"Minimum stock inventory to hold"`
	MaxPositionValue float64 `json:"maxPositionValue,omitempty" description:"Maximum stock inventory in quote currency to hold"`
	MinPositionValue float64 `json:"minPositionValue,omitempty" description:"Minimum stock inventory in quote currency to hold"`

	MaxBalance float64 `json:"maxBalance,omitempty" description:"Minimum quote inventory to hold"`
	MinBalance float64 `json:"minBalance,omitempty" description:"Maximum quote inventory to hold"`

	MinUpdateIntervalMs int     `json:"minUpdateIntervalMs,omitempty" description:"Update open orders at most every N milliseconds"`
	FrontrunByTick      float64 `json:"frontrunByTick,omitempty" description:"Tick increment when frontruning a price level"`

	ExecutionsAfter      string `json:"executionsAfter,omitempty" description:"Only consider executions from given time, when calculating pnl, RFC3339 time format: 2006-01-02T15:04:05Z07:00"`
	ExecutionsTimeFrames string `json:"executionsTimeFrames,omitempty" description:"Display executions stats for given time frames, comma separated" default:"5m,1h,24h,168h"`
	TradesTimeFrames     string `json:"tradesTimeFrames,omitempty" description:"Display executions stats for given time frames, comma separated" default:"1h,24h"`

	RefPriceStrat    string   `json:"refPriceStrat,omitempty" description:"Strategy for calculating or ref price"`
	RefPriceOffset   float64  `json:"refPriceOffset,omitempty" description:"Offset to apply to all ref prices, this should be a positive or negative number"`
	RefPriceFixed    float64  `json:"refPriceFixed,omitempty" description:"When --refPriceStrat=fixed we must set the ref price value manually"`
	RefOnQuoteAsset  bool     `json:"refOnQuoteAsset,omitempty" description:"Allow to find ref paths using the quote asset instead of the base asset"`
	RefPriceBuy      float64  `json:"refPriceBuy,omitempty" description:"Fixes the ref price for buy side"`
	RefPriceSell     float64  `json:"refPriceSell,omitempty" description:"Fixes the ref price for sell side"`
	RefExchanges     string   `json:"refExchanges,omitempty" description:"Comma separated list of exchanges"`
	RefIgnoreSelf    bool     `json:"refIgnoreSelf,omitempty" description:"Don't consider our own orderbook when setting the best ref price"`
	RefIgnoreMarkets string   `json:"refIgnoreMarkets,omitempty" description:"Comma separated list of markets to ignore when calculating the ref price"`
	RefOrderSize     float64  `json:"refOrderSize,omitempty" description:"GetOrder size to use when calculating ref price"`
	RefOrderSizeBuy  *float64 `json:"refOrderSizeBuy,omitempty" description:"GetOrder size to use when calculating buy ref price"`
	RefOrderSizeSell *float64 `json:"refOrderSizeSell,omitempty" description:"GetOrder size to use when calculating sell ref price"`

	MarketsMapping string `json:"marketsMapping,omitempty" description:"When looking at other exchanges, they might use different symbols for the same asset. We must manually translate them with this option. Ex: Binance:USDT=Gemini:GUSD,Bitfinex:BTC/USDT=Binance:BTC/BUSD"`
	FxAllMarkets   bool   `json:"fxAllMarkets,omitempty" description:"Allow to look in other exchanges for fx conversion"`

	OrderSizing           string  `json:"orderSizing,omitempty" description:"GetOrder sizing strategy, options:\n by-spread : also pass an orderMultiplier to multiply the order size by given spread"`
	OrderSizingMultiplier float64 `json:"orderSizingMultiplier,omitempty" description:"Parameter for the order sizing strategy."`
	MaxOrderSize          float64 `json:"maxOrderSize,omitempty" description:"Max order size when calcuating the order size"`

	SpreadSizing           string  `json:"spreadSizing,omitempty" description:"Spread sizing strategy: options:\n bid-by-inventory : Use the following formula to add a bid spread:\n (POSITION / MAX_POSITION) * SPREAD\n Set --spreadSizingParam to 2.5 to add a 2.5% spread relative to the max positin. If we are at 100% max position, it will add the full 2.5% extra spread."` // TODO: Legacy, remove when migrated to new SpreadSizingBuy
	SpreadSizingMultiplier float64 `json:"spreadSizingMultiplier,omitempty" description:"Parameter for the spread sizing strategy"`                                                                                                                                                                                                                                                   // TODO: Legacy, remove when migrated to new SpreadSizingBuy
	SpreadSizingBuy        float64 `json:"spreadSizingBuy,omitempty" description:"Increase buy spread as we reach our max position, formula: (POSITION / MAX_POSITION) * SPREAD. Example: --spreadSizingBuy=2.5 adds a total of 2.5% buy spread as we reach our maximum position, 1.25% if we are at 50% of our maximum position."`
	SpreadSizingSell       float64 `json:"spreadSizingSell,omitempty" description:"Increase sell spread as we reach our min position, formula: (POSITION / MIN_POSITION) * SPREAD. Example: --spreadSizingSell=2.5 adds a total of 2.5% sell spread as we reach our minimum position, 1.25% if we are at 50% of our minimum position."`
	SpreadFrom             string  `json:"spreadFrom,omitempty" description:"Params:\n ref : Use the ref price to calculate the spread of a given price\n top : Use the top price (best bid or best ask) to calculate the spread of a given price"`

	AllowTradeEmptyBook bool   `json:"allowTradeEmptyBook,omitempty" description:"Allow to trade on empty books, normally we wait for the orders to have at least 1 bid/ask, so we know we received the initial orderbook"`
	AllowCancelErrs     string `json:"allowCancelErrs,omitempty" description:"List of order ids to allow error when trying to cancel"`

	TakerBuySpread        float64       `json:"takerBuySpread,omitempty" description:"Spread to send taker buy orders to agress the order orders"`
	TakerBuySpreadSizing  float64       `json:"takerBuySpreadSizing,omitempty" description:"Spread sizing increment to apply to the taker buy spread, this will be relative to the inventory" default:"0"`
	TakerSellSpread       float64       `json:"takerSellSpread,omitempty" description:"Spread to send taker sell orders to agress the order orders"`
	TakerSellSpreadSizing float64       `json:"takerSellSpreadSizing,omitempty" description:"Spread sizing increment to apply to the taker buy spread, this will be relative to the inventory" default:"0"`
	TakerOnlyNotice       bool          `json:"takerOnlyNotice,omitempty" description:"Only send notice about possible arbs, dont execute them"`
	TakerMaxSize          float64       `json:"takerMaxSize,omitempty" description:"Maximum arb amount to execute in stock"`
	TakerMaxValue         float64       `json:"takerMaxValue,omitempty" description:"Maximum arb value (amount * price) to execute in the books currency"`
	TakerArbInterval      time.Duration `json:"takerArbInterval,omitempty" description:"Minimum interval between taker arb executions"`

	ArbNoticeInterval time.Duration `json:"arbNoticeInterval,omitempty" description:"Interval between arb opportunities notifications"`
	ArbNoticeMinPnl   float64       `json:"arbNoticeMinPnl,omitempty" description:"Minimum pnl to send arb opportunities notifications"`

	WarmupPeriod     time.Duration `json:"warmupPeriod,omitempty" description:"Orderbooks warmup periods before starting to trade" default:"1s"`
	MaxLossTimeFrame time.Duration `json:"maxLossTimeFrame,omitempty" description:"Time frame to use when calculating the max loss the bot has have during a given period" default:"1h"`
	MaxLoss          float64       `json:"maxLoss,omitempty" description:"Max loss during given time frame --maxLossTimeFrame"`
	DisableRanker    string        `json:"disableRanker,omitempty" description:"Disable position ranker, this will make the bot posts orders only obeying the min spreads" default:"false"`

	CancelOrdersOnStartup  string `json:"cancelOrdersOnStartup,omitempty" description:"Don't find and cancel all open orders when booting the bot, default: true"`
	CancelOrdersOnShutdown string `json:"cancelOrdersOnShutdown,omitempty" description:"Don't find and cancel all open orders when shutting down the bot, default: true"`
	CancelMode             string `json:"cancelMode,omitempty" description:"Cancel all modes in \"all\" or only orders with our CID in \"tagged-cids\" mode" default:"all"`

	BalanceSyncInterval        time.Duration `json:"balanceSyncInterval,omitempty" description:"Interval to sync balances with the exchange" default:"5s"`
	MaxWaitStartTrading        time.Duration `json:"maxWaitStartTrading,omitempty" description:"Max time to wait for the initial positions update" default:"10s"`
	MaxBookInactivityTime      time.Duration `json:"maxBookInactivityTime,omitempty" description:"Max time for the main orderbook to be without updates for the market data to be considered bad, and the bot stops trading" default:"5m"`
	OrderConfirmationTimeoutMs int           `json:"orderConfirmationTimeoutMs,omitempty" description:"Max order timeout"`

	StatsReporterInterval time.Duration `json:"statsReporterInterval,omitempty" description:"Interval to write stats update to db" default:"1m"`
	PaintInterval         time.Duration `json:"paintInterval,omitempty" description:"Interval to paintBook the screen" default:"1s"`
}
