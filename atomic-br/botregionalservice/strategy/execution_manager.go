package strategy

import (
	"log"
	"math"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	_ "github.com/lib/pq"
	"go.uber.org/zap"
)

type ExecutionManager struct {
	execBuffer      []gateway.Execution
	execBufferMutex sync.RWMutex
	market          gateway.Market
	stats           map[time.Duration]ExecutionStats
	statsMutex      sync.Mutex
	timeFrames      []time.Duration
	logger          *zap.Logger
}

type ExecutionStatsSide struct {
	Volume      float64 `json:"volume"`
	MoneyVolume float64 `json:"money_volume"`
	AvgPrice    float64 `json:"avg_price"`
	Count       int64   `json:"count"`
}

func NewExecutionStatsSide(volume, moneyVolume float64, count int64) ExecutionStatsSide {
	avgPrice := 0.0
	if volume != 0 {
		avgPrice = moneyVolume / volume
	}

	return ExecutionStatsSide{
		Volume:      volume,
		MoneyVolume: moneyVolume,
		AvgPrice:    avgPrice,
		Count:       count,
	}
}

type ExecutionStats struct {
	TimeFrame time.Duration      `json:"time_frame"`
	Buy       ExecutionStatsSide `json:"buy"`
	Sell      ExecutionStatsSide `json:"sell"`
}

func (e ExecutionStats) Volume() float64 {
	return e.Buy.Volume + e.Sell.Volume
}

func (e ExecutionStats) MoneyVolume() float64 {
	return e.Buy.MoneyVolume + e.Sell.MoneyVolume
}

func (e ExecutionStats) NetPosition() float64 {
	return e.Buy.Volume - e.Sell.Volume
}

func (e ExecutionStats) PnL() float64 {
	if e.Buy.AvgPrice == 0 || e.Sell.AvgPrice == 0 {
		return 0.0
	}

	volume := math.Min(e.Buy.Volume, e.Sell.Volume)
	return (volume * e.Sell.AvgPrice) - (volume * e.Buy.AvgPrice)
}

func (e ExecutionStats) AvgSpread() float64 {
	if e.Buy.AvgPrice == 0 || e.Sell.AvgPrice == 0 {
		return 0.0
	}

	return (e.Sell.AvgPrice / e.Buy.AvgPrice) - 1.0
}

func NewExecutionManager(market gateway.Market, logger *zap.Logger) *ExecutionManager {
	emLogger := logger.With(zap.String("component", "ExecutionManager"), zap.String("market", market.Symbol))
	emLogger.Info("Creating new ExecutionManager")
	return &ExecutionManager{
		execBuffer: make([]gateway.Execution, 0),
		market:     market,
		stats:      make(map[time.Duration]ExecutionStats),
		timeFrames: make([]time.Duration, 0),
		logger:     emLogger,
	}
}

func (m *ExecutionManager) AddTimeFrame(t time.Duration) {
	m.logger.Info("Adding new time frame for execution stats", zap.Duration("timeFrame", t))

	m.execBufferMutex.RLock()

	afterTime := time.Now().Add(-t)
	var recentExecs []gateway.Execution
	for _, exec := range m.execBuffer {
		if exec.Time.After(afterTime) {
			recentExecs = append(recentExecs, exec)
		}
	}
	m.execBufferMutex.RUnlock()

	asks := filterExecutionsBySide(gateway.Ask, recentExecs)
	bids := filterExecutionsBySide(gateway.Bid, recentExecs)

	askVolume, askMoneyVolume := calculateExecutionsAvgPx(asks)
	bidVolume, bidMoneyVolume := calculateExecutionsAvgPx(bids)

	stats := ExecutionStats{
		TimeFrame: t,
		Sell:      NewExecutionStatsSide(askVolume, askMoneyVolume, int64(len(asks))),
		Buy:       NewExecutionStatsSide(bidVolume, bidMoneyVolume, int64(len(bids))),
	}

	m.statsMutex.Lock()
	m.stats[t] = stats
	m.timeFrames = append(m.timeFrames, t)
	m.statsMutex.Unlock()

	m.logger.Debug("Initializing stats for new time frame", zap.Duration("timeFrame", t), zap.Any("initialStats", stats))

	for _, exec := range asks {
		go m.expireExecAfter(t, exec)
	}
	for _, exec := range bids {
		go m.expireExecAfter(t, exec)
	}
}

func (m *ExecutionManager) TimeFrameStats(t time.Duration) ExecutionStats {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()
	m.logger.Debug("Retrieving stats for time frame", zap.Duration("timeFrame", t))
	return m.stats[t]
}

func (m *ExecutionManager) TimeFrames() []time.Duration {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()
	m.logger.Debug("Retrieving all active time frames")
	return m.timeFrames
}

// HasTimeFrame checks if already has time frame, capped by seconds
func (m *ExecutionManager) HasTimeFrame(t time.Duration) (bool, time.Duration) {
	for _, tt := range m.timeFrames {
		if t.Seconds() == tt.Seconds() {
			return true, tt
		}
	}
	return false, 0
}

func (m *ExecutionManager) AddExecution(exec gateway.Execution) {
	if exec.Market.Symbol != m.market.Symbol || exec.Market.Exchange != m.market.Exchange {
		m.logger.Warn("Ignoring execution from different market", zap.Any("execMarket", exec.Market), zap.Any("managerMarket", m.market))
		return
	}

	if exec.Side == "" {
		// Using standard log as logger might not be initialized yet in some contexts.
		log.Printf("ExecutionManager received exec [%f] [%f] without valid side [%s], ignoring...", exec.Price, exec.Amount, exec.Side)
		m.logger.Error("Received execution with invalid side", zap.Any("execution", exec))
		return
	}
	m.logger.Debug("Adding new execution", zap.Any("execution", exec))

	m.execBufferMutex.Lock()
	m.execBuffer = append(m.execBuffer, exec)
	m.execBufferMutex.Unlock()

	m.statsMutex.Lock()
	for _, timeFrame := range m.timeFrames {
		afterTime := time.Now().Add(-timeFrame)

		if exec.Time.After(afterTime) {
			m.logger.Debug("Updating stats for time frame with new execution", zap.Duration("timeFrame", timeFrame))
			m.updateExecStats(timeFrame, exec)
			go m.expireExecAfter(timeFrame, exec)
		}
	}
	m.statsMutex.Unlock()
}

func (m *ExecutionManager) expireExecAfter(execTimeFrame time.Duration, exec gateway.Execution) {
	expiresAfter := exec.Time.Add(execTimeFrame)
	expiresIn := time.Until(expiresAfter)
	m.logger.Debug("Execution will expire for time frame", zap.Duration("timeFrame", execTimeFrame), zap.String("tradeID", exec.TradeID), zap.Duration("expiresIn", expiresIn))

	time.Sleep(expiresIn)

	m.statsMutex.Lock()
	m.logger.Debug("Expiring execution, updating stats", zap.Duration("timeFrame", execTimeFrame), zap.String("tradeID", exec.TradeID))
	m.updateExecStats(execTimeFrame, gateway.Execution{
		Side:   exec.Side,
		Price:  exec.Price,
		Amount: -exec.Amount,
	})
	m.statsMutex.Unlock()
}

func (m *ExecutionManager) updateExecStats(execTimeFrame time.Duration, exec gateway.Execution) {
	stats := m.stats[execTimeFrame]
	m.logger.Debug("Updating execution stats", zap.Duration("timeFrame", execTimeFrame), zap.Any("execution", exec))

	// Determine if we are adding or removing a trade for the count
	var countChange int64 = 0
	if exec.Amount > 0 {
		countChange = 1
	} else if exec.Amount < 0 {
		countChange = -1
	}

	if exec.Side == gateway.Bid {
		volume, moneyVolume := calculateExecutionsAvgPx([]gateway.Execution{
			{Price: stats.Buy.AvgPrice, Amount: stats.Buy.Volume},
			{Price: exec.Price, Amount: exec.Amount},
		})
		stats.Buy = NewExecutionStatsSide(volume, moneyVolume, stats.Buy.Count+countChange) // <-- UPDATE THIS
	} else {
		volume, moneyVolume := calculateExecutionsAvgPx([]gateway.Execution{
			{Price: stats.Sell.AvgPrice, Amount: stats.Sell.Volume},
			{Price: exec.Price, Amount: exec.Amount},
		})
		stats.Sell = NewExecutionStatsSide(volume, moneyVolume, stats.Sell.Count+countChange) // <-- UPDATE THIS
	}

	m.stats[execTimeFrame] = stats
	m.logger.Debug("Execution stats updated", zap.Duration("timeFrame", execTimeFrame), zap.Any("newStats", stats))
}

func calculateExecutionsAvgPx(executions []gateway.Execution) (volume, moneyVolume float64) {
	for _, exec := range executions {
		volume += exec.Amount
		moneyVolume += exec.Amount * exec.Price
	}

	return volume, moneyVolume
}

func filterExecutionsBySide(side gateway.Side, executions []gateway.Execution) []gateway.Execution {
	execs := make([]gateway.Execution, 0)

	for _, exec := range executions {
		if exec.Side == side {
			execs = append(execs, exec)
		}
	}

	return execs
}

func (m *ExecutionManager) GetExecutions(limit int) []gateway.Execution {
	m.execBufferMutex.RLock()
	defer m.execBufferMutex.RUnlock()

	numExecs := len(m.execBuffer)
	if limit <= 0 || limit > numExecs {
		limit = numExecs
	}

	// Return a copy of the last 'limit' executions
	start := numExecs - limit
	result := make([]gateway.Execution, limit)
	copy(result, m.execBuffer[start:])

	// Reverse for chronological descending order
	for i, j := 0, len(result)-1; i < j; i, j = i+1, j-1 {
		result[i], result[j] = result[j], result[i]
	}
	return result
}
