package botregionalservice

import (
	"context"
	"strings"
	"time"

	"github.com/go-viper/mapstructure/v2"
	"github.com/herenow/atomic-bm/atomic-br/botregionalclient"
	"github.com/herenow/atomic-bm/atomic-br/botregionalservice/strategy"
	"github.com/herenow/atomic-bm/pkg/logger"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-protocols/gen/atomic/api/botregion/v1"
	"github.com/herenow/atomic-protocols/gen/atomic/api/enums/v1"
	"github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// BotRegionalService implements the gRPC service for managing trading bots.
// It acts as a bridge between the gRPC transport layer and the core bot management logic.
type BotRegionalService struct {
	manager *Manager
	logger  *zap.Logger

	botregion.UnimplementedBotRegionServiceServer
}

// NewBotRegionalService creates and returns a new BotRegionalService instance,
func NewBotRegionalService(ctx context.Context) (*BotRegionalService, error) {
	log := logger.New(zap.DebugLevel.String())

	log.Info("BotRegionalService starting...")

	return &BotRegionalService{
		manager: NewManager(log),
		logger:  log,
	}, nil
}

func (s *BotRegionalService) NewBot(ctx context.Context, req *botregion.NewBotRequest) (*emptypb.Empty, error) {
	botID := req.GetBot().GetId()
	s.logger.Info("gRPC NewBot request received", zap.String("botID", botID))

	gatewayOpts, err := botregionalclient.ToGoStruct[gateway.Options](req.GetBot().GetGatewayOptions())
	if err != nil {
		s.logger.Error("Failed to parse gateway options", zap.String("botID", botID), zap.Error(err))
		return nil, status.Errorf(codes.InvalidArgument, "failed to parse gateway options: %v", err)
	}

	params, err := botregionalclient.ToGoStruct[strategy.Params](req.GetBot().GetParams())
	if err != nil {
		s.logger.Error("Failed to parse strategy params", zap.String("botID", botID), zap.Error(err))
		return nil, status.Errorf(codes.InvalidArgument, "failed to parse strategy params: %v", err)
	}

	// Add core identifiers to the params struct
	params.BotID = botID
	params.Exchange = req.GetBot().GetExchangeId()
	params.AccountID = req.GetBot().GetAccountId()
	params.Symbol = req.GetBot().GetSymbol()

	s.logger.Debug("Creating bot with params", zap.String("botID", botID), zap.Any("params", params))

	if _, err = s.manager.CreateBot(gatewayOpts, params); err != nil {
		s.logger.Error("Failed to create bot in manager", zap.String("botID", botID), zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to create bot: %v", err)
	}

	s.logger.Info("gRPC NewBot request processed successfully", zap.String("botID", botID))
	return &emptypb.Empty{}, nil
}

func (s *BotRegionalService) GetBot(ctx context.Context, req *botregion.GetBotRequest) (*botregion.GetBotResponse, error) {
	botID := req.GetBotId()
	s.logger.Info("gRPC GetBot request received", zap.String("botID", botID))

	b, err := s.manager.GetBot(botID)
	if err != nil {
		s.logger.Error("Failed to get bot from manager", zap.String("botID", botID), zap.Error(err))
		return nil, status.Errorf(codes.NotFound, "failed to get bot: %v", err.Error())
	}

	params := b.GetParams()
	paramsStruct, err := botregionalclient.FromGoStruct(params)
	if err != nil {
		s.logger.Error("Failed to convert params to struct", zap.String("botID", botID), zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to convert params to struct: %v", err)
	}

	gtwOpts := b.GetGatewayOpts()
	gtwStruct, err := botregionalclient.FromGoStruct(gtwOpts)
	if err != nil {
		s.logger.Error("Failed to convert gateway options to struct", zap.String("botID", botID), zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to convert gtw params to struct: %v", err)
	}

	var lastErrStr string
	botStatus, lastErr := b.GetStatus()
	if lastErr != nil {
		lastErrStr = lastErr.Error()
	}

	s.logger.Debug("gRPC GetBot request processed successfully", zap.String("botID", botID))
	return &botregion.GetBotResponse{
		Bot: &proto.Bot{
			Id:             params.BotID,
			ExchangeId:     params.Exchange,
			AccountId:      params.AccountID,
			Symbol:         params.Symbol,
			Status:         convertStatusToProto(botStatus),
			LastError:      lastErrStr,
			GatewayOptions: gtwStruct,
			Type:           enums.BotType_BOT_TYPE_MARKET_MAKER,
			Params:         paramsStruct,
		},
	}, nil
}

// Helper function to convert the internal status to the proto enum
func convertStatusToProto(s strategy.Status) enums.BotStatus {
	switch s {
	case strategy.Starting:
		return enums.BotStatus_BOT_STATUS_STARTING
	case strategy.Running:
		return enums.BotStatus_BOT_STATUS_RUNNING
	case strategy.Stopping:
		return enums.BotStatus_BOT_STATUS_STOPPING
	case strategy.Stopped:
		return enums.BotStatus_BOT_STATUS_STOPPED
	case strategy.Error:
		return enums.BotStatus_BOT_STATUS_ERROR
	default:
		return enums.BotStatus_BOT_STATUS_UNSPECIFIED
	}
}

func (s *BotRegionalService) StartBot(ctx context.Context, req *botregion.StartBotRequest) (*emptypb.Empty, error) {
	botID := req.GetBotId()
	s.logger.Info("gRPC StartBot request received", zap.String("botID", botID))

	if err := s.manager.StartBot(botID); err != nil {
		s.logger.Error("Failed to start bot", zap.String("botID", botID), zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to start bot: %v", err)
	}

	s.logger.Info("gRPC StartBot request processed successfully", zap.String("botID", botID))
	return &emptypb.Empty{}, nil
}

func (s *BotRegionalService) StopBot(ctx context.Context, req *botregion.StopBotRequest) (*emptypb.Empty, error) {
	botID := req.GetBotId()
	s.logger.Info("gRPC StopBot request received", zap.String("botID", botID))
	if err := s.manager.StopBot(req.GetBotId()); err != nil {
		s.logger.Error("Failed to stop bot", zap.String("botID", botID), zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to stop bot: %v", err)
	}
	s.logger.Info("gRPC StopBot request processed successfully", zap.String("botID", botID))
	return &emptypb.Empty{}, nil
}

func (s *BotRegionalService) ListBots(ctx context.Context, _ *emptypb.Empty) (*botregion.ListBotsResponse, error) {
	s.logger.Info("gRPC ListBots request received")
	bots := s.manager.ListBots()
	resp := &botregion.ListBotsResponse{
		Bots: make([]*proto.Bot, 0, len(bots)),
	}

	s.logger.Debug("Manager returned bots", zap.Int("count", len(bots)))
	if len(bots) == 0 {
		s.logger.Warn("Manager returned an empty list of bots.")
	}

	for _, b := range bots {
		params := b.GetParams()
		parsedParams, err := botregionalclient.FromGoStruct(params)
		if err != nil {
			s.logger.Error("Failed to convert params to struct for bot", zap.String("botID", params.BotID), zap.Error(err))
			return nil, status.Errorf(codes.Internal, "failed to convert params to struct: %v", err)
		}

		gtwParams := b.GetGatewayOpts()
		parsedGatewayOpts, err := botregionalclient.FromGoStruct(gtwParams)
		if err != nil {
			s.logger.Error("Failed to convert gateway options to struct for bot", zap.String("botID", params.BotID), zap.Error(err))
			return nil, status.Errorf(codes.Internal, "failed to convert gateway to struct: %v", err)
		}

		botStatus, lastErr := b.GetStatus()
		var lastErrStr string
		if lastErr != nil {
			lastErrStr = lastErr.Error()
		}

		resp.Bots = append(resp.Bots, &proto.Bot{
			Id:             params.BotID,
			ExchangeId:     params.Exchange,
			AccountId:      params.AccountID,
			Symbol:         params.Symbol,
			Status:         convertStatusToProto(botStatus),
			LastError:      lastErrStr,
			Type:           enums.BotType_BOT_TYPE_MARKET_MAKER,
			GatewayOptions: parsedGatewayOpts,
			Params:         parsedParams,
		})
	}

	s.logger.Info("gRPC ListBots request processed successfully")
	return resp, nil
}

func (s *BotRegionalService) UpdateBotParams(ctx context.Context, req *botregion.UpdateBotParamsRequest) (*emptypb.Empty, error) {
	s.logger.Info("gRPC UpdateBotParams request received", zap.Int("update_count", len(req.GetBotUpdates())))

	for _, update := range req.GetBotUpdates() {
		botID := update.GetBotId()
		s.logger.Debug("Updating params for bot", zap.String("botID", botID))

		bot, err := s.manager.GetBot(botID)
		if err != nil {
			s.logger.Error("Bot not found for param update", zap.String("botID", botID), zap.Error(err))
			return nil, status.Errorf(codes.NotFound, "bot with ID '%s' not found", botID)
		}

		currentParams := bot.GetParams()
		if err = botregionalclient.MergeToExistingStruct(&currentParams, update.GetParams()); err != nil {
			s.logger.Error("Failed to merge params for update", zap.String("botID", botID), zap.Error(err))
			return nil, status.Errorf(codes.InvalidArgument, "failed to parse params: %v", err)
		}

		if err = s.manager.UpdateBotParams(botID, currentParams); err != nil {
			s.logger.Error("Failed to update bot params in manager", zap.String("botID", botID), zap.Error(err))
			return nil, status.Errorf(codes.Internal, "failed to update bot options: %v", err)
		}
	}

	s.logger.Info("gRPC UpdateBotParams request processed successfully")
	return &emptypb.Empty{}, nil
}

func (s *BotRegionalService) UpdateGatewayOpts(ctx context.Context, req *botregion.UpdateGatewayOptionsRequest) (*emptypb.Empty, error) {
	s.logger.Info("gRPC UpdateGatewayOpts request received", zap.Int("update_count", len(req.GetGtwUpdates())))

	for _, update := range req.GetGtwUpdates() {
		botID := update.GetBotId()
		s.logger.Debug("Updating gateway options for bot", zap.String("botID", botID))

		bot, err := s.manager.GetBot(botID)
		if err != nil {
			s.logger.Error("Bot not found for gateway options update", zap.String("botID", botID), zap.Error(err))
			return nil, status.Errorf(codes.NotFound, "bot with ID '%s' not found", botID)
		}

		currentGtwOpts := bot.GetGatewayOpts()
		if err = botregionalclient.MergeToExistingStruct(&currentGtwOpts, update.GetGtwOptions()); err != nil {
			s.logger.Error("Failed to merge gateway options for update", zap.String("botID", botID), zap.Error(err))
			return nil, status.Errorf(codes.InvalidArgument, "failed to parse gateway options: %v", err)
		}

		if err = s.manager.UpdateBotGatewayOpts(botID, currentGtwOpts); err != nil {
			s.logger.Error("Failed to update bot gateway options in manager", zap.String("botID", botID), zap.Error(err))
			return nil, status.Errorf(codes.Internal, "failed to update bot gateway options: %v", err)
		}
	}

	s.logger.Info("gRPC UpdateGatewayOpts request processed successfully")
	return &emptypb.Empty{}, nil
}

func (s *BotRegionalService) parseParamsToOpts(params map[string]string, opts *strategy.Params) error {
	config := &mapstructure.DecoderConfig{
		Result:           opts,
		TagName:          "json",
		WeaklyTypedInput: true,
	}
	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return status.Errorf(codes.Internal, "failed to create decoder: %v", err)
	}
	if err = decoder.Decode(params); err != nil {
		return status.Errorf(codes.InvalidArgument, "failed to parse params: %v", err)
	}
	return nil
}

func (s *BotRegionalService) GetBotState(ctx context.Context, req *botregion.GetBotStateRequest) (*botregion.GetBotStateResponse, error) {
	botID := req.GetBotId()
	s.logger.Info("gRPC GetBotState request received", zap.String("botID", botID))

	bot, err := s.manager.GetBot(req.GetBotId())
	if err != nil {
		s.logger.Error("Failed to get bot for state retrieval", zap.String("botID", botID), zap.Error(err))
		return nil, status.Errorf(codes.NotFound, "bot with ID '%s' not found", req.GetBotId())
	}

	state, err := bot.GetState()
	if err != nil {
		s.logger.Error("Failed to get bot state from bot", zap.String("botID", botID), zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to get bot state: %v", err)
	}

	params := bot.GetParams()
	parsedParams, err := botregionalclient.FromGoStruct(params)
	if err != nil {
		s.logger.Error("Failed to convert params to struct for state response", zap.String("botID", botID), zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to convert params to struct: %v", err)
	}

	s.logger.Debug("gRPC GetBotState request processed successfully", zap.String("botID", botID))
	return &botregion.GetBotStateResponse{
		State: &proto.State{
			BotId:  params.BotID,
			Data:   state,
			Params: parsedParams,
			Time:   timestamppb.New(time.Now()),
		},
	}, nil
}

func (s *BotRegionalService) StopAllBots(ctx context.Context, req *emptypb.Empty) (*emptypb.Empty, error) {
	s.logger.Info("gRPC StopAllBots request received")
	if errs := s.manager.StopAllBots(); len(errs) > 0 {
		var errorMessages []string
		for _, err := range errs {
			errorMessages = append(errorMessages, err.Error())
		}
		errMsg := strings.Join(errorMessages, "; ")
		s.logger.Error("Failed to stop all bots", zap.String("errors", errMsg))
		return nil, status.Errorf(codes.Internal, "failed to stop all bots: %s", errMsg)
	}
	s.logger.Info("gRPC StopAllBots request processed successfully")
	return &emptypb.Empty{}, nil
}

func (s *BotRegionalService) StartAllBots(ctx context.Context, req *emptypb.Empty) (*emptypb.Empty, error) {
	s.logger.Info("gRPC StartAllBots request received")
	if errs := s.manager.StartAllBots(); len(errs) > 0 {
		var errorMessages []string
		for _, err := range errs {
			errorMessages = append(errorMessages, err.Error())
		}
		errMsg := strings.Join(errorMessages, "; ")
		s.logger.Error("Failed to start all bots", zap.String("errors", errMsg))
		return nil, status.Errorf(codes.Internal, "failed to start all bots: %s", errMsg)
	}
	s.logger.Info("gRPC StartAllBots request processed successfully")
	return &emptypb.Empty{}, nil
}

func (s *BotRegionalService) ListBotsState(ctx context.Context, req *emptypb.Empty) (*botregion.ListBotsStateResponse, error) {
	s.logger.Info("gRPC ListBotsState request received")

	bots := s.manager.ListBots()

	s.logger.Debug("Manager returned bots", zap.Int("count", len(bots)))
	if len(bots) == 0 {
		s.logger.Warn("Manager returned an empty list of bots.")
	}

	states := make([]*proto.State, 0, len(bots))
	for _, bot := range bots {
		botID := bot.GetParams().BotID
		state, err := bot.GetState()
		if err != nil {
			s.logger.Error("Failed to get bot state from bot", zap.String("botID", botID), zap.Error(err))
			continue
		}

		params := bot.GetParams()
		parsedParams, err := botregionalclient.FromGoStruct(params)
		if err != nil {
			s.logger.Error("Failed to convert params to struct for state response", zap.String("botID", botID), zap.Error(err))
			return nil, status.Errorf(codes.Internal, "failed to convert params to struct: %v", err)
		}

		overview := &proto.State{
			BotId:  botID,
			Data:   state,
			Params: parsedParams,
			Time:   timestamppb.New(time.Now()),
		}
		states = append(states, overview)
	}

	s.logger.Info("gRPC ListBotsState request processed successfully")
	return &botregion.ListBotsStateResponse{
		BotsState: states,
	}, nil
}
