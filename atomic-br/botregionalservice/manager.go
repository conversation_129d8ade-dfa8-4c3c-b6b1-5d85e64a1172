package botregionalservice

import (
	"context"
	"fmt"
	"os"
	"sync"

	"github.com/herenow/atomic-bm/atomic-br/botregionalservice/strategy"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/marketdata"
	"github.com/herenow/atomic-tools/pkg/safegwbuilder"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/structpb"
)

// Bot is the interface that all trading bots must implement.
type Bot interface {
	Start() error
	Stop() error
	GetStatus() (strategy.Status, error)
	UpdateParams(opts strategy.Params) error
	GetParams() strategy.Params
	GetGatewayOpts() gateway.Options
	UpdateBotGatewayOpts(opts gateway.Options) error
	GetState() (*structpb.Struct, error)
	Close() error
}

// ManagedBot wraps a Bot.
type ManagedBot struct {
	Bot
}

// Manager is the central controller for all trading bots.
type Manager struct {
	bots              map[string]ManagedBot
	marketdataManager marketdata.Manager
	mu                sync.RWMutex
	logger            *zap.Logger
}

// NewManager creates and returns a new Manager instance.
func NewManager(logger *zap.Logger) *Manager {
	logger.Info("Creating new bot manager with a shared marketdata manager")

	builder := safegwbuilder.New(&safegwbuilder.Config{
		BinaryPath: "../bin",
	})
	if err := builder.EnsureBinary(); err != nil {
		panic(fmt.Errorf("failed to ensure safegateway binary: %w", err))
	}

	os.Setenv("SAFEGATEWAY_BIN_PATH", builder.BinaryPath())

	mdm := marketdata.NewManager(context.Background(), logger.Named("marketdata"), nil, gateway.Options{})

	return &Manager{
		bots:              make(map[string]ManagedBot),
		logger:            logger,
		marketdataManager: mdm,
	}
}

// CreateBot instantiates a new bot. It no longer configures the marketdata manager.
func (m *Manager) CreateBot(gtwOpts gateway.Options, opts strategy.Params) (Bot, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.logger.Debug("Attempting to create bot", zap.String("botID", opts.BotID))

	if _, exists := m.bots[opts.BotID]; exists {
		m.logger.Error("Bot already exists", zap.String("botID", opts.BotID))
		return nil, fmt.Errorf("bot with ID '%s' already exists", opts.BotID)
	}

	bot := strategy.NewMarketMakerBot(m.logger, gtwOpts, opts, nil, nil, m.marketdataManager)
	m.logger.Info("Bot object created successfully", zap.String("botID", opts.BotID))

	m.bots[opts.BotID] = ManagedBot{Bot: bot}

	return bot, nil
}

// GetBot retrieves a bot by its ID.
func (m *Manager) GetBot(botID string) (Bot, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	m.logger.Debug("Attempting to get bot", zap.String("botID", botID))

	managedBot, exists := m.bots[botID]
	if !exists {
		m.logger.Error("Bot not found", zap.String("botID", botID))
		return nil, fmt.Errorf("bot with ID '%s' not found", botID)
	}
	m.logger.Debug("Bot retrieved successfully", zap.String("botID", botID))
	return managedBot.Bot, nil
}

// ListBots returns a slice of all bots managed by the manager.
func (m *Manager) ListBots() []Bot {
	m.mu.RLock()
	defer m.mu.RUnlock()
	m.logger.Debug("Listing all bots")

	bots := make([]Bot, 0, len(m.bots))
	for _, managedBot := range m.bots {
		bots = append(bots, managedBot.Bot)
	}
	m.logger.Debug("ListBots completed", zap.Int("count", len(bots)))
	return bots
}

// StartBot begins the trading activity of a specific bot.
func (m *Manager) StartBot(botID string) error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	m.logger.Info("Attempting to start bot", zap.String("botID", botID))

	managedBot, exists := m.bots[botID]
	if !exists {
		m.logger.Error("Bot not found for starting", zap.String("botID", botID))
		return fmt.Errorf("bot with ID '%s' not found", botID)
	}

	if err := managedBot.Start(); err != nil {
		m.logger.Error("Failed to start bot", zap.String("botID", botID), zap.Error(err))
		return err
	}
	m.logger.Info("Bot started successfully", zap.String("botID", botID))
	return nil
}

// StopBot ceases the trading activity of a specific bot.
func (m *Manager) StopBot(botID string) error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	m.logger.Info("Attempting to stop bot", zap.String("botID", botID))

	managedBot, exists := m.bots[botID]
	if !exists {
		m.logger.Error("Bot not found for stopping", zap.String("botID", botID))
		return fmt.Errorf("bot with ID '%s' not found", botID)
	}

	err := managedBot.Stop()
	if err != nil {
		m.logger.Error("Failed to stop bot", zap.String("botID", botID), zap.Error(err))
		return err
	}
	m.logger.Info("Bot stopped successfully", zap.String("botID", botID))
	return nil
}

// RemoveBot stops a bot and removes it from the manager.
func (m *Manager) RemoveBot(botID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.logger.Info("Attempting to remove bot", zap.String("botID", botID))

	managedBot, exists := m.bots[botID]
	if !exists {
		m.logger.Error("Bot not found for removal", zap.String("botID", botID))
		return fmt.Errorf("bot with ID '%s' not found", botID)
	}

	m.logger.Debug("Stopping bot before removal", zap.String("botID", botID))
	if err := managedBot.Stop(); err != nil {
		m.logger.Error("Error stopping bot during removal", zap.String("botID", botID), zap.Error(err))
		return fmt.Errorf("error stopping bot '%s': %w", botID, err)
	}

	m.logger.Debug("Closing bot resources before removal", zap.String("botID", botID))
	if err := managedBot.Close(); err != nil {
		m.logger.Error(fmt.Sprintf("error closing resources for bot '%s': %v", botID, err))
	}

	delete(m.bots, botID)
	m.logger.Info("Bot removed successfully", zap.String("botID", botID))
	return nil
}

// GetBotStatus retrieves the current status of a specific bot.
func (m *Manager) GetBotStatus(botID string) (strategy.Status, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	m.logger.Debug("Getting bot status", zap.String("botID", botID))

	managedBot, exists := m.bots[botID]
	if !exists {
		m.logger.Error("Bot not found for status check", zap.String("botID", botID))
		return strategy.Error, fmt.Errorf("bot with ID '%s' not found", botID)
	}

	return managedBot.GetStatus()
}

// UpdateBotGatewayOpts applies a new set of gateway configuration to a specific bot.
func (m *Manager) UpdateBotGatewayOpts(botID string, opts gateway.Options) error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	m.logger.Info("Attempting to update bot gateway options", zap.String("botID", botID))

	managedBot, exists := m.bots[botID]
	if !exists {
		m.logger.Error("Bot not found for gateway options update", zap.String("botID", botID))
		return fmt.Errorf("bot with ID '%s' not found", botID)
	}

	if err := managedBot.UpdateBotGatewayOpts(opts); err != nil {
		m.logger.Error("Failed to update bot gateway options", zap.String("botID", botID), zap.Error(err))
		return err
	}
	m.logger.Info("Bot gateway options updated successfully", zap.String("botID", botID))
	return nil
}

// UpdateBotParams applies a new configuration to a specific bot.
func (m *Manager) UpdateBotParams(botID string, opts strategy.Params) error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	m.logger.Info("Attempting to update bot params", zap.String("botID", botID))

	managedBot, exists := m.bots[botID]
	if !exists {
		m.logger.Error("Bot not found for params update", zap.String("botID", botID))
		return fmt.Errorf("bot with ID '%s' not found", botID)
	}

	if err := managedBot.UpdateParams(opts); err != nil {
		m.logger.Error("Failed to update bot params", zap.String("botID", botID), zap.Error(err))
		return err
	}
	m.logger.Info("Bot params updated successfully", zap.String("botID", botID))
	return nil
}

// StartAllBots starts all the bots managed by the manager.
func (m *Manager) StartAllBots() []error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	m.logger.Info("Attempting to start all bots")

	var errs []error
	for botID, managedBot := range m.bots {
		if err := managedBot.Start(); err != nil {
			m.logger.Error("Failed to start bot", zap.String("botID", botID), zap.Error(err))
			errs = append(errs, fmt.Errorf("failed to start bot '%s': %w", botID, err))
		}
	}
	if len(errs) > 0 {
		m.logger.Error("Encountered errors while starting all bots", zap.Int("errorCount", len(errs)))
	} else {
		m.logger.Info("All bots started successfully")
	}
	return errs
}

// StopAllBots stops all the bots managed by the manager.
func (m *Manager) StopAllBots() []error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	m.logger.Info("Attempting to stop all bots")

	var errs []error
	for botID, managedBot := range m.bots {
		if err := managedBot.Stop(); err != nil {
			m.logger.Error("Failed to stop bot", zap.String("botID", botID), zap.Error(err))
			errs = append(errs, fmt.Errorf("failed to stop bot '%s': %w", botID, err))
		}
	}

	if len(errs) > 0 {
		m.logger.Error("Encountered errors while stopping all bots", zap.Int("errorCount", len(errs)))
	} else {
		m.logger.Info("All bots stopped successfully")
	}
	return errs
}
