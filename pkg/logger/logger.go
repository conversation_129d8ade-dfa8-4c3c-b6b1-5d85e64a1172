package logger

import (
	"log"
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// New creates the application's base zap.Logger, which writes to the console.
func New(level string) *zap.Logger {
	logLevel, err := zapcore.ParseLevel(level)
	if err != nil {
		log.Printf("invalid log level '%s', defaulting to 'info'", level)
		logLevel = zapcore.InfoLevel
	}

	consoleCore := newConsoleCore(logLevel)

	logger := zap.New(consoleCore, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	zap.ReplaceGlobals(logger)

	return logger
}

// WithCore is a new helper function that adds a new backend to an existing logger.
// It takes a logger and a new core, and returns a new logger instance that writes
// to both the original and the new core.
func WithCore(l *zap.Logger, newCore zapcore.Core) *zap.Logger {
	existingCore := l.Core()

	// The combined core filters "context" for the console (existingCore)
	// but passes all fields to the OTel backend (newCore).
	combinedCore := newSelectiveFieldCore(existingCore, newCore, "context")

	return l.WithOptions(zap.WrapCore(func(c zapcore.Core) zapcore.Core {
		return combinedCore
	}))
}

// newConsoleCore create a Core for console output.
func newConsoleCore(level zapcore.Level) zapcore.Core {
	encoderConfig := zap.NewDevelopmentEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	return zapcore.NewCore(
		zapcore.NewConsoleEncoder(encoderConfig),
		zapcore.Lock(os.Stdout),
		level,
	)
}

// selectiveFieldCore is a custom zapcore.Core that wraps two cores.
// It filters out a specific field before passing log entries to the primary core (console),
// while sending all fields to the secondary core (OpenTelemetry).
type selectiveFieldCore struct {
	primaryCore   zapcore.Core
	secondaryCore zapcore.Core
	fieldToFilter string
}

// newSelectiveFieldCore creates a new core that filters a field for one destination.
func newSelectiveFieldCore(primary, secondary zapcore.Core, fieldToFilter string) zapcore.Core {
	return &selectiveFieldCore{
		primaryCore:   primary,
		secondaryCore: secondary,
		fieldToFilter: fieldToFilter,
	}
}

func (c *selectiveFieldCore) Enabled(lvl zapcore.Level) bool {
	return c.primaryCore.Enabled(lvl) || c.secondaryCore.Enabled(lvl)
}

func (c *selectiveFieldCore) With(fields []zapcore.Field) zapcore.Core {
	return &selectiveFieldCore{
		primaryCore:   c.primaryCore.With(fields),
		secondaryCore: c.secondaryCore.With(fields),
		fieldToFilter: c.fieldToFilter,
	}
}

func (c *selectiveFieldCore) Check(ent zapcore.Entry, ce *zapcore.CheckedEntry) *zapcore.CheckedEntry {
	if c.Enabled(ent.Level) {
		return ce.AddCore(ent, c)
	}
	return ce
}

func (c *selectiveFieldCore) Write(ent zapcore.Entry, fields []zapcore.Field) error {
	// Filter out the specified field for the primary core.
	primaryFields := make([]zapcore.Field, 0, len(fields))
	for i := range fields {
		if fields[i].Key != c.fieldToFilter {
			primaryFields = append(primaryFields, fields[i])
		}
	}
	_ = c.primaryCore.Write(ent, primaryFields) // we don't care about error from console
	return c.secondaryCore.Write(ent, fields)
}

func (c *selectiveFieldCore) Sync() error {
	_ = c.primaryCore.Sync() // we don't care about error from console
	return c.secondaryCore.Sync()
}
