package util

import (
	"strconv"
	"time"
)

// ParseAndValidateTimestamp parses a timestamp string and validates it, returning
// the corresponding time.Time value and any encountered error. The timestampStr
// should be in a floating-point format representing seconds since the Unix epoch.
// The function first parses the timestampStr into a floating-point number,
// then extracts the integer part as seconds and the fractional part as nanoseconds.
// It constructs a time.Time value using the seconds and nanoseconds parts
// and returns it along with any parsing errors.
//
// Parameters:
//   - timestampStr: A string containing a timestamp in floating-point format.
//
// Returns:
//   - time.Time: The parsed and validated timestamp as a time.Time value.
//   - error: An error, if any, encountered during parsing and validation.
//
// Example:
//
//	parsedTime, err := parseAndValidateTimestamp("1631320735.500000000")
//	if err != nil {
//	    fmt.Println("Error:", err)
//	} else {
//	    fmt.Println("Parsed Timestamp:", parsedTime)
//	}
func ParseAndValidateTimestamp(timestampStr string) (time.Time, error) {
	timestampUnix, err := strconv.ParseFloat(timestampStr, 64)
	if err != nil {
		return time.Time{}, err
	}

	secPart := int64(timestampUnix)
	nsecPart := int64((timestampUnix - float64(secPart)) * 1e9)

	parsedTime := time.Unix(secPart, nsecPart)
	return parsedTime, nil
}

func ConvertStringToTime(input string) (time.Time, error) {
	// Define the layout corresponding to the provided timestamp format
	layout := "2006-01-02T15:04:05.999999Z"

	// Parse the input string using the defined layout
	t, err := time.Parse(layout, input)
	if err != nil {
		return time.Time{}, err
	}

	return t, nil
}

func ConvertUnixStringToTime(unixString string) (time.Time, error) {
	// Parse the Unix timestamp string to an int64
	unixTimestamp, err := strconv.ParseInt(unixString, 10, 64)
	if err != nil {
		return time.Time{}, err
	}

	// Convert the Unix timestamp to time.Time
	t := time.Unix(unixTimestamp, 0)

	return t, nil
}
