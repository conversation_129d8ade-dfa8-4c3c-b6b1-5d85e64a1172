package util

import (
	"reflect"
	"time"

	"github.com/mitchellh/mapstructure"
)

// MapToStruct converts a map to a struct using mapstructure.
func MapToStruct(input map[string]interface{}, output interface{}) error {
	config := &mapstructure.DecoderConfig{
		Result:           output,
		WeaklyTypedInput: true,
		DecodeHook:       mapstructure.ComposeDecodeHookFunc(timeStringToTimeHookFunc),
	}

	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return err
	}

	if err = decoder.Decode(input); err != nil {
		return err
	}

	return nil
}

func timeStringToTimeHookFunc(from reflect.Kind, to reflect.Kind, v interface{}) (interface{}, error) {
	if from == reflect.String && to == reflect.TypeOf(time.Time{}).Kind() {
		str := v.(string)
		parsedTime, err := time.Parse(time.RFC3339, str)
		if err != nil {
			return nil, err
		}
		return parsedTime, nil
	}
	return v, nil
}
