package util

import (
	"database/sql"

	"github.com/herenow/atomic-bm/internal/pkg/codes"
	"github.com/herenow/atomic-bm/internal/pkg/errors"
)

var ErrNoRowsAffected = errors.NotFound(codes.Parameters, "no rows affected")

// EnsureRowsAffected checks the number of affected rows and returns an error if no rows were affected.
func EnsureRowsAffected(result sql.Result) error {
	affected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrap(err, "error getting rows affected")
	}

	if affected == 0 {
		return ErrNoRowsAffected
	}

	return nil
}
