package otellogs

import (
	"context"
	"crypto/tls"
	"fmt"
	"strings"
	"time"

	"go.opentelemetry.io/contrib/bridges/otelzap"
	"go.opentelemetry.io/otel/exporters/otlp/otlplog/otlploggrpc"
	"go.opentelemetry.io/otel/exporters/otlp/otlplog/otlploghttp"
	logglobal "go.opentelemetry.io/otel/log/global"
	"go.opentelemetry.io/otel/sdk/log"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.34.0"
	"go.uber.org/zap/zapcore"
	"google.golang.org/grpc/credentials"
)

// ShutdownFunc defines a function that can be called to gracefully shutdown telemetry.
type ShutdownFunc func(context.Context) error

// Config holds the configuration for creating an OpenTelemetry-aware zap.Core.
type Config struct {
	// ServiceName is the name of the service that will be reported.
	ServiceName string
	// ServiceVersion is the version of the service.
	ServiceVersion string
	// OtelEndpoint is the URL of the OTLP collector (e.g., "localhost:4317" for gRPC).
	OtelEndpoint string
	// BearerToken is the token used for authentication with the collector.
	BearerToken string
	// Insecure for using insecure tls
	Insecure bool
}

// NewCore creates a zapcore.Core that is configured to send logs to an
// OpenTelemetry endpoint. It also returns a shutdown function to ensure
// buffered logs are flushed before the application exits.
// This function is now the single responsibility of this package.
func NewCore(ctx context.Context, cfg Config) (zapcore.Core, ShutdownFunc, error) {
	exporter, err := newExporter(ctx, cfg)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create OTLP exporter: %w", err)
	}

	res, err := resource.Merge(
		resource.Default(),
		resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(cfg.ServiceName),
			semconv.ServiceVersion(cfg.ServiceVersion),
		),
	)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create resource: %w", err)
	}

	processor := log.NewBatchProcessor(exporter)
	loggerProvider := log.NewLoggerProvider(
		log.WithResource(res),
		log.WithProcessor(processor),
	)

	// Set this provider as the global logger provider. This is important
	// so the otelzap bridge can find it.
	logglobal.SetLoggerProvider(loggerProvider)

	core := otelzap.NewCore("otellogs")
	shutdown := func(ctx context.Context) error {
		return loggerProvider.Shutdown(ctx)
	}

	return core, shutdown, nil
}

// newExporter creates an OTLP exporter based on the endpoint scheme.
func newExporter(ctx context.Context, cfg Config) (log.Exporter, error) {
	// We can support both gRPC and HTTP based on the endpoint for more flexibility.
	// If the endpoint is something like "http://..." or "https://...", we use HTTP.
	// Otherwise, we default to gRPC.
	if strings.HasPrefix(cfg.OtelEndpoint, "http") {
		opts := []otlploghttp.Option{
			otlploghttp.WithEndpoint(cfg.OtelEndpoint),
			otlploghttp.WithHeaders(map[string]string{
				"Authorization": "Bearer " + cfg.BearerToken,
			}),
			otlploghttp.WithTimeout(10 * time.Second),
		}

		if cfg.Insecure {
			opts = append(opts, otlploghttp.WithInsecure())
		} else {
			// Assumes a standard TLS setup. You can customize the tls.Config as needed.
			opts = append(opts, otlploghttp.WithTLSClientConfig(&tls.Config{}))
		}

		return otlploghttp.New(ctx, opts...)
	}

	opts := []otlploggrpc.Option{
		otlploggrpc.WithEndpoint(cfg.OtelEndpoint),
		otlploggrpc.WithHeaders(map[string]string{
			"Authorization": "Bearer " + cfg.BearerToken,
		}),
		otlploggrpc.WithTimeout(10 * time.Second),
	}

	if cfg.Insecure {
		opts = append(opts, otlploggrpc.WithInsecure())
	} else {
		// Assumes a standard TLS setup. You can customize the tls.Config as needed.
		opts = append(opts, otlploggrpc.WithTLSCredentials(credentials.NewClientTLSFromCert(nil, "")))
	}

	return otlploggrpc.New(ctx, opts...)
}
