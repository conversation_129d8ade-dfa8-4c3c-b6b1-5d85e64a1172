package otelmetrics

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetricgrpc"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.34.0"
	"google.golang.org/grpc/credentials"
)

// Config holds the configuration for the metrics provider.
type Config struct {
	ServiceName string
	Endpoint    string
	BearerToken string
	Insecure    bool
}

// NewProvider creates a new OpenTelemetry MeterProvider and sets it as the global provider.
// It configures a periodic reader to export metrics to an OTLP endpoint via gRPC.
func NewProvider(ctx context.Context, cfg Config) (*metric.MeterProvider, error) {
	res, err := resource.New(ctx,
		resource.WithAttributes(
			semconv.ServiceNameKey.String(cfg.ServiceName),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create resource: %w", err)
	}

	// Configure the OTLP gRPC exporter.
	exporterOpts := []otlpmetricgrpc.Option{
		otlpmetricgrpc.WithEndpoint(cfg.Endpoint),
		otlpmetricgrpc.WithHeaders(map[string]string{
			"Authorization": "Bearer " + cfg.BearerToken,
		}),
	}

	if cfg.Insecure {
		exporterOpts = append(exporterOpts, otlpmetricgrpc.WithInsecure())
	} else {
		// Assumes a standard TLS setup. You can customize the tls.Config as needed.
		exporterOpts = append(exporterOpts, otlpmetricgrpc.WithTLSCredentials(credentials.NewClientTLSFromCert(nil, "")))
	}

	metricExporter, err := otlpmetricgrpc.New(ctx, exporterOpts...)
	if err != nil {
		return nil, fmt.Errorf("failed to create otlpgrpc metric exporter: %w", err)
	}

	// The periodic reader is recommended for production environments.
	// It collects and exports metrics at a regular interval.
	reader := metric.NewPeriodicReader(metricExporter,
		metric.WithInterval(30*time.Second), // Adjust the interval as needed.
	)

	provider := metric.NewMeterProvider(
		metric.WithResource(res),
		metric.WithReader(reader),
	)

	// Set the global MeterProvider, which is the standard practice.
	otel.SetMeterProvider(provider)

	return provider, nil
}
