// Package notify provides a set of tools for sending notifications using Firebase Cloud Messaging (FCM).
// It offers functionalities to send both individual and batch notifications, and manage topic subscriptions.
//
// The package hinges on a Client struct, which encapsulates the Firebase application and messaging client.
// To use this package, initialize a Client instance with Firebase configuration using the NewPushNotifyClient function.
// This configuration requires Firebase project details like ProjectId, PrivateKeyId, and ClientEmail among others.
//
// The Client struct provides methods to send notifications (SendNotification), send multiple notifications
// simultaneously (SendMultiNotifications), and manage topic subscriptions with SubscribeTopic and UnsubscribeTopic.
//
// Usage Example:
//
//	cfg := FirebaseConfig{...} // Set your Firebase configuration here.
//	client, err := NewPushNotifyClient(context.Background(), &cfg)
//	if err != nil {
//	    log.Fatalf("Failed to create a notify client: %v", err)
//	}
//	// Use client to send notifications or manage topics.
//
// Note: This package requires Firebase SDK and additional dependencies like "github.com/pkg/errors" for error handling.
package notify

import (
	"context"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/pkg/errors"
	"github.com/segmentio/encoding/json"
	"google.golang.org/api/option"
)

// FirebaseConfig holds the necessary Firebase project configuration details.
type FirebaseConfig struct {
	Type                    string `mapstructure:"type" json:"type"`
	ProjectId               string `mapstructure:"project_id" json:"project_id"`
	PrivateKeyId            string `mapstructure:"private_key_id" json:"private_key_id"`
	PrivateKey              string `mapstructure:"private_key" json:"private_key"`
	ClientEmail             string `mapstructure:"client_email" json:"client_email"`
	ClientId                string `mapstructure:"client_id" json:"client_id"`
	AuthUri                 string `mapstructure:"auth_uri" json:"auth_uri"`
	TokenUri                string `mapstructure:"token_uri" json:"token_uri"`
	AuthProviderX509CertUrl string `mapstructure:"auth_provider_x509_cert_url" json:"auth_provider_x509_cert_url"`
	ClientX509CertUrl       string `mapstructure:"client_x509_cert_url" json:"client_x509_cert_url"`
	UniverseDomain          string `mapstructure:"universe_domain" json:"universe_domain"`
}

// Client represents the Firebase client, encapsulating the Firebase app and messaging client.
type Client struct {
	app    *firebase.App
	client *messaging.Client
}

// NewPushNotifyClient initializes and returns a new Client with the provided FirebaseConfig.
// It validates the necessary configuration and sets up the Firebase app and messaging client.
func NewPushNotifyClient(ctx context.Context, cfg *FirebaseConfig) (*Client, error) {
	if cfg.PrivateKey == "" || cfg.ProjectId == "" || cfg.ClientId == "" {
		return nil, errors.New("missing firebase config variables")
	}

	cfgByte, err := json.Marshal(cfg)
	if err != nil {
		return nil, err
	}

	app, err := firebase.NewApp(ctx, nil, option.WithCredentialsJSON(cfgByte))
	if err != nil {
		return nil, err
	}

	client, err := app.Messaging(ctx)
	if err != nil {
		return nil, err
	}

	return &Client{app: app, client: client}, nil
}

// SendNotification sends a single notification message.
func (c *Client) SendNotification(ctx context.Context, msg *messaging.Message) (string, error) {
	return c.client.Send(ctx, msg)
}

// SendMultiNotifications sends multiple notification messages in a batch.
func (c *Client) SendMultiNotifications(ctx context.Context, msg *messaging.MulticastMessage) (*messaging.BatchResponse, error) {
	return c.client.SendEachForMulticast(ctx, msg)
}

// SubscribeTopic subscribes a list of tokens to a topic.
func (c *Client) SubscribeTopic(ctx context.Context, tokens []string, topic string) (*messaging.TopicManagementResponse, error) {
	return c.client.SubscribeToTopic(ctx, tokens, topic)
}

// UnsubscribeTopic unsubscribes a list of tokens from a topic.
func (c *Client) UnsubscribeTopic(ctx context.Context, tokens []string, topic string) (*messaging.TopicManagementResponse, error) {
	return c.client.UnsubscribeFromTopic(ctx, tokens, topic)
}
