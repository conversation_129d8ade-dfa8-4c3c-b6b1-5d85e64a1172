package ordermanager

import (
	"context"
	"sync/atomic"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"go.uber.org/zap"
)

const DefaultCancelDeadline = 30 * time.Second

func (m *manager) CancelOrder(ctx context.Context, internalID string, cancelReason string) (*ManagedOrder, error) {
	if !m.init {
		return nil, ErrManagerNotInitialized
	}

	managedOrder, ok := m.getOrder(internalID)
	if !ok {
		return nil, ErrOrderNotFound
	}

	if err := m.updateViewAndStore(managedOrder, func(order OrderView) (OrderView, error) {
		m.logger.Debug("Initiating order cancellation",
			zap.String("internal_id", order.InternalID),
			zap.String("exchange_id", order.OrderID))

		if order.IsCanceling() && !order.IsExpiredCancel() {
			return order, ErrOrderIsCancelling
		}

		cancelDeadline := time.Now().Add(DefaultCancelDeadline)
		order.SetCanceling(cancelDeadline)
		order.SetCancelReason(cancelReason)

		gwOrder := gateway.Order{
			ID:            order.OrderID,
			ClientOrderID: order.ClientOrderID,
			Market: gateway.Market{
				Symbol: order.Symbol,
				Meta:   order.MarketMeta,
			},
		}

		go m.cancelOrderAsync(ctx, managedOrder, gwOrder)

		return order, nil
	}); err != nil {
		return nil, err
	}

	return managedOrder, nil
}

func (m *manager) cancelOrderAsync(ctx context.Context, managedOrder *ManagedOrder, gwOrder gateway.Order) {
	select {
	case <-ctx.Done():
		m.handleCancellationFailure(managedOrder, gateway.OrderUnknown, ctx.Err())
		managedOrder.cancelChan <- ctx.Err()
		return
	default:
	}

	err := m.accGtw.CancelOrder(gwOrder)
	if err != nil {
		var newState gateway.OrderState
		if gateway.CheckAlreadyCancelledErr(err) {
			newState = gateway.OrderCancelled
		} else {
			newState = gateway.OrderUnknown
		}

		m.logger.Debug("Cancel failed from exchange",
			zap.String("order_id", gwOrder.ID),
			zap.String("new_state", string(newState)),
			zap.Error(err))

		m.handleCancellationFailure(managedOrder, newState, err)
		managedOrder.cancelChan <- err
		return
	}

	if err := m.updateViewAndStore(managedOrder, func(order OrderView) (OrderView, error) {
		m.logger.Debug("Order cancelled from exchange",
			zap.String("order_id", order.InternalID),
			zap.String("exchange_order_id", order.OrderID))

		order.SetCanceled()

		return order, nil
	}); err != nil {
		order := managedOrder.View()
		m.logger.Error("CRITICAL: failed to update cancelled order state",
			zap.String("internal_id", order.InternalID),
			zap.Error(err))
		managedOrder.cancelChan <- err
		return
	}

	atomic.AddUint64(&m.ordersCancelled, 1)

	managedOrder.cancelChan <- nil
}

func (m *manager) handleCancellationFailure(managedOrder *ManagedOrder, newState gateway.OrderState, originalErr error) {
	if err := m.updateViewAndStore(managedOrder, func(order OrderView) (OrderView, error) {
		order.SetState(newState)

		if newState == gateway.OrderUnknown {
			order.SetLastCancelErr(originalErr)
		}

		return order, nil
	}); err != nil {
		order := managedOrder.View()
		m.logger.Error("CRITICAL: failed to update failed cancel order state",
			zap.String("internal_id", order.InternalID),
			zap.String("new_state", string(newState)),
			zap.Error(err),
			zap.NamedError("original_err", err))
	}
}
