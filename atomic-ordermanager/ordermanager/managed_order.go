package ordermanager

import (
	"sync"
	"time"

	"github.com/herenow/atomic-tools/pkg/logger"
	"github.com/herenow/ordermanager/types"
	"go.uber.org/zap"
)

type ManagedOrder struct {
	view        OrderView
	mu          sync.RWMutex
	confirmChan chan error
	cancelChan  chan error
}

func NewManagedOrder(view types.OrderView) *ManagedOrder {
	mo := &ManagedOrder{
		view:        view,
		confirmChan: make(chan error, 1),
		cancelChan:  make(chan error, 1),
	}

	return mo
}

type UpdateViewFunc func(currentView types.OrderView) (newView types.OrderView, returnErr error)

func (mo *ManagedOrder) UpdateView(exec UpdateViewFunc) error {
	mo.mu.Lock()
	defer mo.mu.Unlock()

	newView, err := exec(mo.view)
	if err != nil {
		return err
	}

	// Update internal state
	newView.LastUpdateAt = time.Now()
	newView.Version++
	mo.view = newView
	return nil
}

// View returns a copy of the current order state
func (mo *ManagedOrder) View() types.OrderView {
	mo.mu.RLock()
	defer mo.mu.RUnlock()
	return mo.view
}

// Version returns the current version of the order
func (mo *ManagedOrder) Version() int {
	mo.mu.RLock()
	defer mo.mu.RUnlock()
	return mo.view.Version
}

// ==================== SYNCHRONIZATION METHODS ====================
// For backward compatibility with existing lock-based code

func (mo *ManagedOrder) Lock() {
	mo.mu.Lock()
}

func (mo *ManagedOrder) Unlock() {
	mo.mu.Unlock()
}

func (mo *ManagedOrder) RLock() {
	mo.mu.RLock()
}

func (mo *ManagedOrder) RUnlock() {
	mo.mu.RUnlock()
}

// Confirmed returns a read-only channel that will receive an error when the order
// is confirmed by the exchange. A nil error indicates successful confirmation,
// while a non-nil error indicates the order was rejected or failed.
// The channel is closed after sending the result.
func (mo *ManagedOrder) Confirmed() <-chan error {
	return mo.confirmChan
}

// Cancelled returns a read-only channel that will receive an error when the order
// cancellation is processed by the exchange. A nil error indicates successful cancellation,
// while a non-nil error indicates the cancellation failed.
// The channel is closed after sending the result.
func (mo *ManagedOrder) Cancelled() <-chan error {
	return mo.cancelChan
}

func (mo *ManagedOrder) Close() {
	close(mo.confirmChan)
	close(mo.cancelChan)
}

func loggerManagedOrder(lgr *logger.Logger, mo *ManagedOrder) *logger.Logger {
	view := mo.View()
	fields := []zap.Field{
		zap.String("internal_id", view.InternalID),
		zap.String("order_id", view.OrderID),
		zap.String("symbol", view.Symbol),
		zap.String("state", string(view.State)),
		zap.String("side", string(view.Side)),
		zap.Float64("price", view.Price),
		zap.Float64("amount", view.Amount),
		zap.Float64("filled_base", view.FilledBase),
		zap.Float64("filled_quote", view.FilledQuote),
		zap.Time("sent_at", view.SentAt),
		zap.Time("confirmed_at", view.ConfirmedAt),
	}

	if view.LastOpenErr != "" {
		fields = append(fields, zap.String("last_open_err", view.LastOpenErr))
	}

	if view.LastCancelErr != "" {
		fields = append(fields, zap.String("last_cancel_err", view.LastCancelErr))
	}

	return lgr.With(fields...)
}
