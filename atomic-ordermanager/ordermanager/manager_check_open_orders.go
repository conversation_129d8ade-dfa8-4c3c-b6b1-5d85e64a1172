package ordermanager

import (
	"context"
	"fmt"
	"maps"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"go.uber.org/zap"
)

func (m *manager) CheckOpenOrders(ctx context.Context, market gateway.Market) error {
	if !m.init {
		return ErrManagerNotInitialized
	}

	m.logger.Info("Checking open orders on exchange",
		zap.String("symbol", market.Symbol),
		zap.String("exchange", m.exchangeKey))

	// 1. Fetch current open orders from exchange
	exchangeOrders, err := m.accGtw.OpenOrders(market)
	if err != nil {
		return fmt.Errorf("failed to fetch open orders from exchange: %w", err)
	}

	m.logger.Debug("Fetched open orders from exchange",
		zap.Int("count", len(exchangeOrders)))

	// 2. Create map of remaining exchange orders to process
	remainingOrders := make(map[string]gateway.Order)
	for _, order := range exchangeOrders {
		remainingOrders[order.ID] = order
	}

	m.mu.Lock()
	openOrders := make(map[internalOrderID]*ManagedOrder, len(m.openOrders))
	pendingOrders := make(map[internalOrderID]*ManagedOrder, len(m.pendingOrders))
	unknownOrders := make(map[internalOrderID]*ManagedOrder, len(m.unknownOrders))

	maps.Copy(openOrders, m.openOrders)
	maps.Copy(pendingOrders, m.pendingOrders)
	maps.Copy(unknownOrders, m.unknownOrders)
	m.mu.Unlock()

	// 3. Exclude orders that match our open orders (everything is OK)
	var missingOpenOrders []*ManagedOrder
	for _, managedOrder := range openOrders {
		order := managedOrder.View()
		if order.OrderID != "" {
			if _, exists := remainingOrders[order.OrderID]; exists {
				delete(remainingOrders, order.OrderID)
				m.logger.Debug("Matched open order",
					zap.String("internal_id", order.InternalID),
					zap.String("exchange_id", order.OrderID))
			} else {
				// Order is missing from exchange response
				missingOpenOrders = append(missingOpenOrders, managedOrder)
			}
		}
	}

	// 4. Check if pending orders became available
	for _, pendingOrder := range pendingOrders {
		order := pendingOrder.View()
		for exchangeID, exchangeOrder := range remainingOrders {
			if exchangeID == order.OrderID {
				m.logger.Debug("Matched pending order with exchange order",
					zap.String("internal_id", order.InternalID),
					zap.String("exchange_id", exchangeID))

				// Update unknown order to open state
				order.SetState(gateway.OrderOpen)
				if err := m.storeUpdatedView(pendingOrder, order); err != nil {
					m.logger.Error("Failed to update unknown order to open",
						zap.String("internal_id", order.InternalID),
						zap.Error(err))
				}

				delete(remainingOrders, exchangeID)

				// Register a order update
				go m.registerOrderUpdate(exchangeOrder, pendingOrder)

				break
			}
		}
	}

	// 5. Check remaining orders against unknown orders with tolerance
	for exchangeID, exchangeOrder := range remainingOrders {
		for _, unknownOrder := range unknownOrders {
			order := unknownOrder.View()
			if m.ordersMatch(exchangeOrder, order, market) {
				m.logger.Debug("Matched unknown order with exchange order",
					zap.String("internal_id", order.InternalID),
					zap.String("exchange_id", exchangeID))

				// Update unknown order to open state
				order.OrderID = exchangeID
				order.SoftMatch = true
				order.SetState(gateway.OrderOpen)
				if err := m.storeUpdatedView(unknownOrder, order); err != nil {
					m.logger.Error("Failed to update unknown order to open",
						zap.String("internal_id", order.InternalID),
						zap.Error(err))
				}

				delete(remainingOrders, exchangeID)
				break
			}
		}
	}

	// 6. Check remaining orders against repository for unexpected open orders
	for exchangeID, exchangeOrder := range remainingOrders {
		managedOrder, ok := m.getOrderByExchangeID(exchangeID)
		if !ok {
			// Not our order
			continue
		}

		order := managedOrder.View()

		// This is a very bad state - order should not be open anymore
		if gateway.OrderStateTerminated(order.State) {
			m.logger.Warn("Order became unexpectedly open after terminal state",
				zap.String("internal_id", order.InternalID),
				zap.String("exchange_id", exchangeID),
				zap.String("previous_state", string(order.State)))

			order.SetUnexpectedlyOpen()
			if err := m.storeUpdatedView(managedOrder, order); err != nil {
				m.logger.Error("Failed to update unexpectedly open order",
					zap.String("internal_id", order.InternalID),
					zap.Error(err))
			}

			delete(remainingOrders, exchangeID)

			// Register a order update
			go m.registerOrderUpdate(exchangeOrder, managedOrder)
		}
	}

	// 7. Mark remaining unknown orders as presumed cancelled
	for _, unknownOrder := range unknownOrders {
		order := unknownOrder.View()

		// Only presume cancelled if order has been unknown for some time
		if time.Since(order.LastUpdateAt) > 5*time.Second {
			m.logger.Info("Presuming unknown order as cancelled",
				zap.String("internal_id", order.InternalID),
				zap.Duration("unknown_duration", time.Since(order.LastUpdateAt)))

			order.SetPresumedCanceled()
			if err := m.storeUpdatedView(unknownOrder, order); err != nil {
				m.logger.Error("Failed to update presumed cancelled order",
					zap.String("internal_id", order.InternalID),
					zap.Error(err))
			}
		}
	}

	return nil
}

// ordersMatch checks if exchange order matches managed order within tolerance
func (m *manager) ordersMatch(exchangeOrder gateway.Order, order OrderView, market gateway.Market) bool {
	// Order must be at most 30 seconds old
	if time.Since(order.SentAt) > 30*time.Second {
		return false
	}

	// Check symbol match
	if exchangeOrder.Market.Symbol != order.Symbol {
		return false
	}

	// Check side match
	if exchangeOrder.Side != order.Side {
		return false
	}

	// Check price match with tolerance (±1 price tick)
	priceMatch := abs(exchangeOrder.Price-order.Price) <= market.PriceTick

	// Check amount match with tolerance (±1 amount tick)
	amountMatch := abs(exchangeOrder.Amount-order.Amount) <= market.AmountTick

	return priceMatch && amountMatch
}

// abs returns absolute value of float64
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}
