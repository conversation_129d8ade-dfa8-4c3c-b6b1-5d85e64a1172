package ordermanager

import (
	"context"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/ordermanager/types"
)

// Re-export types for backward compatibility
type OrderView = types.OrderView
type FillMeta = types.FillMeta
type Fill = types.Fill

type OrderManager interface {
	// Lifecycle Management
	Init(ctx context.Context) error
	ProcessTick(tick gateway.Tick)

	// Imperative Order Commands
	SendOrder(ctx context.Context, params SendOrderParams) (*ManagedOrder, error)
	CancelOrder(ctx context.Context, internalID string, cancelReason string) (*ManagedOrder, error)
	CheckOpenOrders(ctx context.Context, market gateway.Market) error

	// State observability
	GetOpenOrders() []*ManagedOrder
	GetPendingOrders() []*ManagedOrder
	GetUnknownOrders() []*ManagedOrder

	// Metrics & Health
	Metrics() Metrics

	// Callbacks
	SetFillCallback(callback func(Fill) (meta FillMeta))

	// Configuration
	SetBotID(botID string)
	SetAccountID(accountID string)
	SetSource(source string)
}

type AccountGateway interface {
	OpenOrders(gateway.Market) (orders []gateway.Order, err error)
	SendOrder(gateway.Order) (orderID string, err error)
	CancelOrder(gateway.Order) (err error)
	NewClientOrderID() string
}

type Exchange interface {
	ID() string
}
