package syncer

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"github.com/herenow/atomic-tools/pkg/logger"
	"github.com/herenow/ordermanager/repository"
	"go.uber.org/zap"
)

type ClickhouseSyncer struct {
	conn   driver.Conn
	logger *logger.Logger
}

// NewClickhouseSyncer creates a new syncer for ClickHouse.
func NewClickhouseSyncer(conn driver.Conn, logger *logger.Logger) *ClickhouseSyncer {
	return &ClickhouseSyncer{
		conn:   conn,
		logger: logger,
	}
}

// Flush syncs unsynced orders and fills to ClickHouse
func (s *ClickhouseSyncer) Flush(ctx context.Context, repo repository.Repository) error {
	s.logger.Info("Starting ClickHouse sync")

	// Sync orders
	if err := s.syncOrders(ctx, repo); err != nil {
		s.logger.Error("Failed to sync orders", zap.Error(err))
		return fmt.Errorf("failed to sync orders: %w", err)
	}

	// Sync fills
	if err := s.syncFills(ctx, repo); err != nil {
		s.logger.Error("Failed to sync fills", zap.Error(err))
		return fmt.Errorf("failed to sync fills: %w", err)
	}

	s.logger.Info("ClickHouse sync completed successfully")
	return nil
}

func (s *ClickhouseSyncer) syncOrders(ctx context.Context, repo repository.Repository) error {
	// Get unsynced orders from the repository
	orders, err := repo.GetUnsyncedOrders(1000)
	if err != nil {
		return fmt.Errorf("failed to get unsynced orders: %w", err)
	}

	if len(orders) == 0 {
		s.logger.Debug("No unsynced orders to sync")
		return nil
	}

	s.logger.Info("Syncing orders to ClickHouse", zap.Int("count", len(orders)))

	// Prepare batch insert statement
	batch, err := s.conn.PrepareBatch(ctx, `
		INSERT INTO orders (
			bot_id, account_id, source, exchange_id, symbol, internal_id, tag, order_id, client_order_id,
			side, price, amount, post_only, state, previous_states, canceling, soft_match,
			filled_base, filled_quote, filled_fee, fee_asset, first_fill_at, last_fill_at,
			sent_at, confirmed_at, canceled_at, cancel_requested_at, cancel_deadline, last_update_at,
			version, presumed_canceled_count, unexpectedly_open_count,
			last_open_err, last_cancel_err, cancel_reason
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to prepare batch: %w", err)
	}
	defer batch.Abort()

	// Add rows to batch
	for _, order := range orders {
		previousStates := make([]string, len(order.PreviousStates))
		for i, state := range order.PreviousStates {
			previousStates[i] = string(state)
		}
		err := batch.Append(
			order.BotID, order.AccountID, order.Source, order.ExchangeID, order.Symbol, order.InternalID, order.Tag, order.OrderID, order.ClientOrderID,
			string(order.Side), order.Price, order.Amount, order.PostOnly, string(order.State), previousStates, order.Canceling, order.SoftMatch,
			order.FilledBase, order.FilledQuote, order.FilledFee, order.FeeAsset, order.FirstFillAt, order.LastFillAt,
			order.SentAt, order.ConfirmedAt, order.CanceledAt, order.CancelRequestedAt, order.CancelDeadline, order.LastUpdateAt,
			order.Version, order.PresumedCanceledCount, order.UnexpectedlyOpenCount,
			order.LastOpenErr, order.LastCancelErr, order.CancelReason,
		)
		if err != nil {
			return fmt.Errorf("failed to append order to batch: %w", err)
		}
	}

	// Execute batch insert
	if err := batch.Send(); err != nil {
		return fmt.Errorf("failed to send batch: %w", err)
	}

	// Mark orders as synced
	if err := repo.DeleteUnsyncedOrders(orders); err != nil {
		return fmt.Errorf("failed to mark orders as synced: %w", err)
	}

	return nil
}

func (s *ClickhouseSyncer) syncFills(ctx context.Context, repo repository.Repository) error {
	// Get unsynced fills from the repository
	fills, err := repo.GetUnsyncedFills(1000)
	if err != nil {
		return fmt.Errorf("failed to get unsynced fills: %w", err)
	}

	if len(fills) == 0 {
		s.logger.Debug("No unsynced fills to sync")
		return nil
	}

	s.logger.Info("Syncing fills to ClickHouse", zap.Int("count", len(fills)))

	// Prepare batch insert statement
	batch, err := s.conn.PrepareBatch(ctx, `
		INSERT INTO fills (
			bot_id, account_id, source, exchange_id, symbol, internal_id, tag, internal_order_id, trade_id, order_id,
			side, amount, price, fee, fee_asset, ts, meta
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to prepare batch: %w", err)
	}
	defer batch.Abort()

	// Add rows to batch
	for _, fill := range fills {
		var meta []byte
		if fill.Meta != nil {
			meta, err = json.Marshal(fill.Meta)
			if err != nil {
				return fmt.Errorf("failed to marshal fill meta: %w", err)
			}
		}

		err := batch.Append(
			fill.BotID, fill.AccountID, fill.Source, fill.ExchangeID, fill.Symbol, fill.InternalID, fill.Tag, fill.InternalOrderID, fill.TradeID, fill.OrderID,
			string(fill.Side), fill.Amount, fill.Price, fill.Fee, fill.FeeAsset, fill.Timestamp, string(meta),
		)
		if err != nil {
			return fmt.Errorf("failed to append fill to batch: %w", err)
		}
	}

	// Execute batch insert
	if err := batch.Send(); err != nil {
		return fmt.Errorf("failed to send batch: %w", err)
	}

	// Mark fills as synced
	if err := repo.DeleteUnsyncedFills(fills); err != nil {
		return fmt.Errorf("failed to mark fills as synced: %w", err)
	}

	return nil
}

// CreateOrdersTableSchema returns the ClickHouse DDL for creating the orders table
func CreateOrdersTableSchema() string {
	return `
CREATE TABLE IF NOT EXISTS orders (
	bot_id LowCardinality(String),
	account_id LowCardinality(String),
	source LowCardinality(String),
	exchange_id LowCardinality(String),
	symbol LowCardinality(String),
	internal_id String,
	tag LowCardinality(String),
	order_id String,
	client_order_id String,
	side LowCardinality(String),
	price Float64,
	amount Float64,
	post_only Bool,
	state LowCardinality(String),
	previous_states Array(LowCardinality(String)),
	canceling Bool,
	soft_match Bool,
	filled_base Float64,
	filled_quote Float64,
	filled_fee Float64,
	fee_asset LowCardinality(String),
	first_fill_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	last_fill_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	sent_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	confirmed_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	canceled_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	cancel_requested_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	cancel_deadline DateTime64(6) CODEC(Delta, ZSTD(1)),
	last_update_at DateTime64(6) CODEC(Delta, ZSTD(1)),
	version Int32,
	presumed_canceled_count Int32,
	unexpectedly_open_count Int32,
	last_open_err String,
	last_cancel_err String,
	cancel_reason LowCardinality(String)
) ENGINE = ReplacingMergeTree(last_update_at)
PRIMARY KEY (exchange_id, symbol, internal_id)
ORDER BY (exchange_id, symbol, internal_id)
PARTITION BY toYYYYMMDD(sent_at)`
}

// CreateFillsTableSchema returns the ClickHouse DDL for creating the fills table
func CreateFillsTableSchema() string {
	return `
CREATE TABLE IF NOT EXISTS fills (
	bot_id LowCardinality(String),
	account_id LowCardinality(String),
	source LowCardinality(String),
	exchange_id LowCardinality(String),
	symbol LowCardinality(String),
	internal_id String,
	tag LowCardinality(String),
	internal_order_id String,
	trade_id String,
	order_id String,
	side LowCardinality(String),
	amount Float64,
	price Float64,
	fee Float64,
	fee_asset LowCardinality(String),
	ts DateTime64(6) CODEC(Delta, ZSTD(1)),
	meta JSON
) ENGINE = MergeTree()
PRIMARY KEY (exchange_id, symbol, internal_id)
ORDER BY (exchange_id, symbol, internal_id)
PARTITION BY toYYYYMM(ts)`
}
