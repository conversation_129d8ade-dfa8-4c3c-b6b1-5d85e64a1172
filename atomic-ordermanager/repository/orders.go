package repository

import (
	"github.com/cockroachdb/pebble"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/ordermanager/types"
)

func (r *repository) StoreOrder(order types.OrderView, previousState gateway.OrderState) error {
	if order.InternalID == "" {
		return ErrInternalIDRequired
	} else if order.ExchangeID == "" {
		return ErrExchangeIDRequired
	}

	batch := r.db.NewBatch()
	defer batch.Close()

	// Save the order view
	orderKey := OrderKey(order.InternalID)
	data, err := r.encodeData(order)
	if err != nil {
		return err
	}
	if err := batch.Set(orderKey, data, nil); err != nil {
		return err
	}

	// Store new state index
	newState := order.State
	newStateKey := StateKey(string(newState), orderKey)
	if err := batch.Set(newStateKey, []byte{}, nil); err != nil {
		return err
	}

	// Delete the old state index
	if previousState != "" {
		previousStateKey := StateKey(string(previousState), orderKey)
		if err := batch.Delete(previousStateKey, nil); err != nil {
			return err
		}
	}

	// Store the exchange order ID mapping
	if order.OrderID != "" {
		exchangeOrderKey := ExchangeOrderKey(order.ExchangeID, order.OrderID)
		if err := batch.Set(exchangeOrderKey, []byte(order.InternalID), nil); err != nil {
			return err
		}
	}

	// Mark order to be resynced
	unsyncedKey := UnsyncedKey(orderKey)
	if err := batch.Set(unsyncedKey, []byte{}, nil); err != nil {
		return err
	}

	return batch.Commit(r.syncOption)
}

func (r *repository) GetOrder(id string) (types.OrderView, error) {
	return getItem[types.OrderView](r, OrderKey(id))
}

func (r *repository) GetOrderByOrderID(exchange, orderID string) (types.OrderView, error) {
	key := ExchangeOrderKey(exchange, orderID)
	data, closer, err := r.db.Get(key)
	if err != nil {
		return types.OrderView{}, err
	}
	defer closer.Close()
	internalID := string(data)
	return r.GetOrder(internalID)
}

func (r *repository) GetOrdersByState(state gateway.OrderState) ([]string, error) {
	orderPrefix := OrderKey("")
	prefix := StateKey(string(state), orderPrefix)
	iter, err := r.db.NewIter(&pebble.IterOptions{
		LowerBound: prefix,
		UpperBound: r.endPrefix(prefix),
	})
	if err != nil {
		return nil, err
	}
	defer iter.Close()

	var internalIDs []string
	for iter.First(); iter.Valid(); iter.Next() {
		internalIDs = append(internalIDs, string(iter.Key()[len(prefix):]))
	}
	return internalIDs, nil
}

func (r *repository) GetUnsyncedOrders(limit int) ([]OrderView, error) {
	views, err := getUnsyncedItems(r, UnsyncedKey(OrderKey("")), r.GetOrder, limit)
	if err != nil {
		return nil, err
	}
	result := make([]types.OrderView, len(views))
	for i, view := range views {
		result[i] = view
	}
	return result, nil
}

func (r *repository) DeleteUnsyncedOrders(orders []types.OrderView) error {
	return deleteItemsAsSynced(r, orders, OrderKey, func(order OrderView) string {
		return order.InternalID
	})
}
