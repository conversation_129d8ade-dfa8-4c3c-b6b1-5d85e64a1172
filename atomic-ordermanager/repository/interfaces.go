package repository

import (
	"errors"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/ordermanager/iterator"
	"github.com/herenow/ordermanager/types"
)

type OrderView = types.OrderView
type Fill = types.Fill

type Repository interface {
	// Order operations
	StoreOrder(order OrderView, previousState gateway.OrderState) error
	GetOrder(id string) (OrderView, error)
	GetOrderByOrderID(exchange, orderID string) (OrderView, error)
	GetOrdersByState(state gateway.OrderState) ([]string, error)
	GetUnsyncedOrders(limit int) ([]OrderView, error)
	DeleteUnsyncedOrders(views []OrderView) error

	// Fill operations
	StoreFill(fill Fill) error
	GetFill(internalID string) (Fill, error)
	CheckFillByExchange(exchange, orderID, tradeID string) (bool, error)
	GetUnsyncedFills(limit int) ([]Fill, error)
	DeleteUnsyncedFills(fills []Fill) error

	// Iterator operations
	FillIterator() iterator.Builder[Fill]
	OrderIterator() iterator.Builder[OrderView]

	// Lifecycle
	Close() error
}

var ErrInternalIDRequired = errors.New("internal ID required for storage")
var ErrExchangeIDRequired = errors.New("exchange ID required for storage")
var ErrOrderIDRequired = errors.New("order ID required for storage")
var ErrTradeIDRequired = errors.New("trade ID required for storage")
