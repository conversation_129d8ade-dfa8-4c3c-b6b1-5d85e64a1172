package metrics

import (
	"sort"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/ordermanager/types"
)

// fillEntry represents a single fill with its expiration time
type fillEntry struct {
	fill      types.Fill
	expiresAt time.Time
}

// marketTimeRangeKey is a unique key for market + time range combination
type marketTimeRangeKey struct {
	market    string
	timeRange time.Duration
}

// timeTracker implements TimeTracker interface
type timeTracker struct {
	mu sync.RWMutex

	// Configuration
	timeRanges []time.Duration

	// Data storage: map[marketTimeRangeKey][]fillEntry
	// Each key maps to a slice of fills sorted by expiration time
	fills map[marketTimeRangeKey][]fillEntry

	// Cached metrics to avoid recalculation
	metricsCache map[marketTimeRangeKey]FillMetrics

	// Last cleanup time
	lastCleanup time.Time
}

// NewTimeTracker creates a new time tracker
func NewTimeTracker(timeRanges []time.Duration) TimeTracker {
	return &timeTracker{
		timeRanges:   timeRanges,
		fills:        make(map[marketTimeRangeKey][]fillEntry),
		metricsCache: make(map[marketTimeRangeKey]FillMetrics),
		lastCleanup:  time.Now(),
	}
}

// AddFill adds a fill to all relevant time ranges
func (t *timeTracker) AddFill(fill types.Fill) error {
	t.mu.Lock()
	defer t.mu.Unlock()

	now := time.Now()

	// Add to each time range
	for _, timeRange := range t.timeRanges {
		key := marketTimeRangeKey{
			market:    fill.Symbol,
			timeRange: timeRange,
		}

		// Calculate expiration time
		expiresAt := fill.Timestamp.Add(timeRange)

		// Skip if fill is already expired
		if expiresAt.Before(now) {
			continue
		}

		// Create fill entry
		entry := fillEntry{
			fill:      fill,
			expiresAt: expiresAt,
		}

		// Add to sorted slice (insert sort to maintain order by expiration time)
		t.fills[key] = t.insertSorted(t.fills[key], entry)

		// Invalidate cache for this key
		delete(t.metricsCache, key)
	}

	return nil
}

// GetMetrics returns current metrics for a market and time range
func (t *timeTracker) GetMetrics(market string, timeRange time.Duration) (FillMetrics, error) {
	t.mu.RLock()
	defer t.mu.RUnlock()

	key := marketTimeRangeKey{
		market:    market,
		timeRange: timeRange,
	}

	// Check cache first
	if cached, ok := t.metricsCache[key]; ok {
		return cached, nil
	}

	// Calculate metrics
	metrics := t.calculateMetrics(key)

	// Cache the result
	t.metricsCache[key] = metrics

	return metrics, nil
}

// Cleanup removes expired fills
func (t *timeTracker) Cleanup() error {
	t.mu.Lock()
	defer t.mu.Unlock()

	now := time.Now()

	// Clean up each time range
	for key, entries := range t.fills {
		cleaned := t.removeExpiredEntries(entries, now)

		// Update if anything was removed
		if len(cleaned) != len(entries) {
			t.fills[key] = cleaned
			// Invalidate cache
			delete(t.metricsCache, key)
		}
	}

	t.lastCleanup = now
	return nil
}

// GetTrackedMarkets returns all markets being tracked
func (t *timeTracker) GetTrackedMarkets() []string {
	t.mu.RLock()
	defer t.mu.RUnlock()

	markets := make(map[string]bool)
	for key := range t.fills {
		markets[key.market] = true
	}

	result := make([]string, 0, len(markets))
	for market := range markets {
		result = append(result, market)
	}

	sort.Strings(result)
	return result
}

// GetTimeRanges returns configured time ranges
func (t *timeTracker) GetTimeRanges() []time.Duration {
	return t.timeRanges
}

// insertSorted inserts a fill entry in the correct position to maintain sorting by expiration time
func (t *timeTracker) insertSorted(entries []fillEntry, entry fillEntry) []fillEntry {
	// Find insertion point
	idx := sort.Search(len(entries), func(i int) bool {
		return entries[i].expiresAt.After(entry.expiresAt)
	})

	// Insert at idx
	entries = append(entries, fillEntry{}) // Extend slice
	copy(entries[idx+1:], entries[idx:])   // Shift elements
	entries[idx] = entry                   // Insert new element

	return entries
}

// removeExpiredEntries removes entries that have expired
func (t *timeTracker) removeExpiredEntries(entries []fillEntry, now time.Time) []fillEntry {
	// Since entries are sorted by expiration time, we can use binary search
	// to find the first non-expired entry
	idx := sort.Search(len(entries), func(i int) bool {
		return entries[i].expiresAt.After(now)
	})

	// Return slice starting from first non-expired entry
	return entries[idx:]
}

// calculateMetrics calculates metrics for a specific market and time range
func (t *timeTracker) calculateMetrics(key marketTimeRangeKey) FillMetrics {
	entries := t.fills[key]

	metrics := FillMetrics{
		Market:    key.market,
		TimeRange: key.timeRange,
		UpdatedAt: time.Now(),
		Buy:       FillMetricsSide{},
		Sell:      FillMetricsSide{},
	}

	// Aggregate fills
	for _, entry := range entries {
		fill := entry.fill

		// Determine side based on order tags or other logic
		// For now, we'll use a simple heuristic - this should be improved
		// based on your specific needs
		isBuy := t.isBuyFill(fill)

		if isBuy {
			metrics.Buy.Volume += fill.Amount
			metrics.Buy.QuoteVolume += fill.Amount * fill.Price
			metrics.Buy.Count++
		} else {
			metrics.Sell.Volume += fill.Amount
			metrics.Sell.QuoteVolume += fill.Amount * fill.Price
			metrics.Sell.Count++
		}
	}

	// Calculate average prices
	metrics.Buy.AvgPrice = metrics.Buy.CalculateAvgPrice()
	metrics.Sell.AvgPrice = metrics.Sell.CalculateAvgPrice()

	return metrics
}

// isBuyFill determines if a fill is a buy or sell
func (t *timeTracker) isBuyFill(fill types.Fill) bool {
	return fill.Side == gateway.Bid
}
