package metrics

import (
	"context"
	"time"

	"github.com/herenow/ordermanager/repository"
	"github.com/herenow/ordermanager/types"
)

// MetricsAdapter defines the interface for metrics collection
type MetricsAdapter interface {
	// Initialize the metrics system with historical data
	Init(ctx context.Context, repo repository.Repository) error

	// OnFill is called when a new fill is processed
	OnFill(fill types.Fill) error

	// Cleanup removes expired data (called periodically)
	Cleanup() error

	// Close releases resources
	Close() error
}

// FillMetricsProvider provides fill-based metrics
type FillMetricsProvider interface {
	// GetFillMetrics returns metrics for a specific market and time range
	GetFillMetrics(market string, timeRange time.Duration) (FillMetrics, error)

	// GetAvailableMarkets returns all markets being tracked
	GetAvailableMarkets() []string

	// GetAvailableTimeRanges returns all time ranges being tracked
	GetAvailableTimeRanges() []time.Duration
}

// FillMetrics represents aggregated fill metrics for a time period
type FillMetrics struct {
	Market    string          `json:"market"`
	TimeRange time.Duration   `json:"time_range"`
	UpdatedAt time.Time       `json:"updated_at"`
	Buy       FillMetricsSide `json:"buy"`
	Sell      FillMetricsSide `json:"sell"`
}

// FillMetricsSide represents metrics for one side (buy or sell)
type FillMetricsSide struct {
	Volume      float64 `json:"volume"`       // Total base volume
	QuoteVolume float64 `json:"quote_volume"` // Total quote volume (instead of storing avg price)
	Count       int     `json:"count"`        // Number of fills
	AvgPrice    float64 `json:"avg_price"`    // Calculated: QuoteVolume / Volume
}

// CalculateAvgPrice calculates the average price from volume and quote volume
func (s FillMetricsSide) CalculateAvgPrice() float64 {
	if s.Volume == 0 {
		return 0
	}
	return s.QuoteVolume / s.Volume
}

// FillMetrics methods for aggregation
func (fm FillMetrics) TotalVolume() float64 {
	return fm.Buy.Volume + fm.Sell.Volume
}

func (fm FillMetrics) TotalQuoteVolume() float64 {
	return fm.Buy.QuoteVolume + fm.Sell.QuoteVolume
}

func (fm FillMetrics) NetVolume() float64 {
	return fm.Buy.Volume - fm.Sell.Volume
}

func (fm FillMetrics) NetQuoteVolume() float64 {
	return fm.Buy.QuoteVolume - fm.Sell.QuoteVolume
}

func (fm FillMetrics) TotalCount() int {
	return fm.Buy.Count + fm.Sell.Count
}

// Registry manages multiple metrics adapters
type Registry interface {
	// RegisterAdapter registers a new metrics adapter
	RegisterAdapter(name string, adapter MetricsAdapter) error

	// GetAdapter returns a specific adapter
	GetAdapter(name string) (MetricsAdapter, bool)

	// OnFill notifies all registered adapters about a new fill
	OnFill(fill types.Fill) error

	// Cleanup triggers cleanup on all adapters
	Cleanup() error

	// Close closes all adapters
	Close() error

	// InitializeAll initializes all registered adapters
	InitializeAll(ctx context.Context, repo repository.Repository) error

	// GetMetricsProviders returns all adapters that implement FillMetricsProvider
	GetMetricsProviders() map[string]FillMetricsProvider

	// GetStatus returns the current status of the registry
	GetStatus() map[string]interface{}
}

// TimeTracker manages time-based data with expiration
type TimeTracker interface {
	// AddFill adds a fill to the tracker
	AddFill(fill types.Fill) error

	// GetMetrics returns current metrics for a time range
	GetMetrics(market string, timeRange time.Duration) (FillMetrics, error)

	// Cleanup removes expired fills
	Cleanup() error

	// GetTrackedMarkets returns markets being tracked
	GetTrackedMarkets() []string

	// GetTimeRanges returns configured time ranges
	GetTimeRanges() []time.Duration
}
