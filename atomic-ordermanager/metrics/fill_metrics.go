package metrics

import (
	"context"
	"fmt"
	"time"

	"github.com/herenow/atomic-tools/pkg/logger"
	"github.com/herenow/ordermanager/repository"
	"github.com/herenow/ordermanager/types"
	"go.uber.org/zap"
)

// fillMetrics implements MetricsAdapter and FillMetricsProvider
type fillMetrics struct {
	logger      *logger.Logger
	timeTracker TimeTracker
	timeRanges  []time.Duration

	// Configuration
	hydrateFromDuration time.Duration // How far back to hydrate from
}

// FillMetricsConfig configures the fill metrics system
type FillMetricsConfig struct {
	TimeRanges          []time.Duration
	HydrateFromDuration time.Duration
	Logger              *logger.Logger
}

// DefaultFillMetricsConfig returns default configuration
func DefaultFillMetricsConfig(logger *logger.Logger) FillMetricsConfig {
	return FillMetricsConfig{
		TimeRanges: []time.Duration{
			1 * time.Hour,
			24 * time.Hour,
			7 * 24 * time.Hour, // 1 week
		},
		HydrateFromDuration: 7 * 24 * time.Hour, // Hydrate from 1 week back
		Logger:              logger,
	}
}

// NewFillMetrics creates a new fill metrics system
func NewFillMetrics(config FillMetricsConfig) *fillMetrics {
	if config.Logger == nil {
		panic("logger is required for FillMetrics")
	}

	if len(config.TimeRanges) == 0 {
		defaultConfig := DefaultFillMetricsConfig(config.Logger)
		config.TimeRanges = defaultConfig.TimeRanges
	}

	if config.HydrateFromDuration == 0 {
		defaultConfig := DefaultFillMetricsConfig(config.Logger)
		config.HydrateFromDuration = defaultConfig.HydrateFromDuration
	}

	return &fillMetrics{
		logger:              config.Logger,
		timeTracker:         NewTimeTracker(config.TimeRanges),
		timeRanges:          config.TimeRanges,
		hydrateFromDuration: config.HydrateFromDuration,
	}
}

// Init initializes the metrics system with historical data
func (fm *fillMetrics) Init(ctx context.Context, repo repository.Repository) error {
	startTime := time.Now()

	fm.logger.Info("Initializing fill metrics system",
		zap.Duration("hydrate_from", fm.hydrateFromDuration),
		zap.Int("time_ranges", len(fm.timeRanges)))

	// Recreate time tracker with repository
	fm.timeTracker = NewTimeTracker(fm.timeRanges)

	// Calculate cutoff time for hydration
	cutoffTime := time.Now().Add(-fm.hydrateFromDuration)

	// Use FillIterator to hydrate historical data
	fillIterator := repo.FillIterator().
		Forward().
		BatchSize(1000).
		Build()

	defer fillIterator.Close()

	hydratedCount := 0
	skippedCount := 0

	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("hydration cancelled: %w", ctx.Err())
		default:
		}

		fill, hasNext := fillIterator.Next()
		if !hasNext {
			break
		}

		// Skip fills that are too old
		if fill.Timestamp.Before(cutoffTime) {
			skippedCount++
			continue
		}

		// Add to time tracker
		if err := fm.timeTracker.AddFill(fill); err != nil {
			fm.logger.Error("failed to add fill to time tracker during hydration",
				zap.String("fill_id", fill.InternalID),
				zap.Error(err))
			continue
		}

		hydratedCount++

		// Log progress periodically
		if hydratedCount%10000 == 0 {
			fm.logger.Info("Hydration progress",
				zap.Int("hydrated", hydratedCount),
				zap.Int("skipped", skippedCount))
		}
	}

	hydrationDuration := time.Since(startTime)

	fm.logger.Info("Fill metrics hydration completed",
		zap.Int("hydrated_fills", hydratedCount),
		zap.Int("skipped_fills", skippedCount),
		zap.Duration("hydration_duration", hydrationDuration),
		zap.Strings("tracked_markets", fm.timeTracker.GetTrackedMarkets()))

	return nil
}

// OnFill is called when a new fill is processed
func (fm *fillMetrics) OnFill(fill types.Fill) error {
	fm.logger.Debug("Processing new fill",
		zap.String("fill_id", fill.InternalID),
		zap.String("symbol", fill.Symbol),
		zap.Float64("amount", fill.Amount),
		zap.Float64("price", fill.Price))

	return fm.timeTracker.AddFill(fill)
}

// Cleanup removes expired data
func (fm *fillMetrics) Cleanup() error {
	fm.logger.Debug("Running fill metrics cleanup")

	if err := fm.timeTracker.Cleanup(); err != nil {
		return fmt.Errorf("failed to cleanup time tracker: %w", err)
	}

	return nil
}

// Close releases resources
func (fm *fillMetrics) Close() error {
	fm.logger.Info("Closing fill metrics system")
	return nil
}

// GetFillMetrics returns metrics for a specific market and time range
func (fm *fillMetrics) GetFillMetrics(market string, timeRange time.Duration) (FillMetrics, error) {
	// Check if time range is supported
	supported := false
	for _, tr := range fm.timeRanges {
		if tr == timeRange {
			supported = true
			break
		}
	}

	if !supported {
		return FillMetrics{}, fmt.Errorf("time range %v not supported", timeRange)
	}

	return fm.timeTracker.GetMetrics(market, timeRange)
}

// GetAvailableMarkets returns all markets being tracked
func (fm *fillMetrics) GetAvailableMarkets() []string {
	return fm.timeTracker.GetTrackedMarkets()
}

// GetAvailableTimeRanges returns all time ranges being tracked
func (fm *fillMetrics) GetAvailableTimeRanges() []time.Duration {
	return fm.timeRanges
}

// GetAllMetrics returns metrics for all markets and time ranges
func (fm *fillMetrics) GetAllMetrics() (map[string]map[time.Duration]FillMetrics, error) {
	markets := fm.GetAvailableMarkets()
	timeRanges := fm.GetAvailableTimeRanges()

	result := make(map[string]map[time.Duration]FillMetrics)

	for _, market := range markets {
		result[market] = make(map[time.Duration]FillMetrics)

		for _, timeRange := range timeRanges {
			metrics, err := fm.GetFillMetrics(market, timeRange)
			if err != nil {
				fm.logger.Error("failed to get metrics",
					zap.String("market", market),
					zap.Duration("time_range", timeRange),
					zap.Error(err))
				continue
			}

			result[market][timeRange] = metrics
		}
	}

	return result, nil
}

// GetMetricsSummary returns a summary of all metrics
func (fm *fillMetrics) GetMetricsSummary() map[string]interface{} {
	markets := fm.GetAvailableMarkets()
	timeRanges := fm.GetAvailableTimeRanges()

	summary := map[string]interface{}{
		"tracked_markets":   len(markets),
		"time_ranges":       len(timeRanges),
		"markets":           markets,
		"time_ranges_hours": make([]float64, len(timeRanges)),
	}

	for i, tr := range timeRanges {
		summary["time_ranges_hours"].([]float64)[i] = tr.Hours()
	}

	return summary
}
