# OrderManager Metrics System

This package provides a comprehensive metrics system for tracking trade execution metrics across multiple markets and time ranges. It's designed to work with the OrderManager and addresses the limitations of the reference `executions_manager.go` implementation.

## Key Improvements Over Reference Implementation

### 1. **Multi-Market Support**
- Tracks metrics for multiple markets simultaneously
- No longer limited to a single market like the reference implementation

### 2. **No Goroutines for Expiration**
- Uses efficient periodic cleanup instead of spawning goroutines for each fill
- Prevents massive context switching on multiple cores
- Maintains sorted data structures for efficient expiration

### 3. **Accurate Price Calculation**
- Stores total quote volume instead of average prices
- Eliminates price calculation errors that accumulate over time
- Calculates average price as `QuoteVolume / Volume` when needed

### 4. **Clean Architecture**
- Separates metrics collection from storage responsibilities
- Uses callback-based integration with OrderManager
- Provides pluggable metrics adapters via Registry pattern

### 5. **Efficient Data Management**
- Uses FillIterator for historical data hydration
- Implements time-based expiration without goroutines
- Maintains sorted data structures for efficient operations

## Architecture

```
metrics/
├── interfaces.go      # Core interfaces and types
├── time_tracker.go    # Time-based metrics tracking with expiration
├── fill_metrics.go    # Fill metrics implementation
├── registry.go        # Registry for managing multiple metrics adapters
├── metrics.go         # High-level API and convenience functions
├── integration.go     # Integration with OrderManager
├── example.go         # Usage examples and tests
└── README.md         # This documentation
```

## Usage

### Basic Setup

```go
import (
    "context"
    "time"
    "github.com/herenow/ordermanager/metrics"
    "github.com/herenow/ordermanager/ordermanager"
)

// Create metrics manager with default configuration
manager, err := metrics.SetupDefaultMetrics(ctx, repo)
if err != nil {
    panic(err)
}
defer manager.Close()

// Process fills (this would be called from OrderManager)
fill := &ordermanager.Fill{
    Symbol:    "BTCUSDT",
    Amount:    1.0,
    Price:     50000.0,
    Timestamp: time.Now(),
    Tag:       "buy",
}
manager.OnFill(fill)

// Get metrics
metrics, err := manager.GetFillMetrics("BTCUSDT", 1*time.Hour)
if err != nil {
    panic(err)
}

fmt.Printf("BTC 1h: Buy Volume: %.2f, Sell Volume: %.2f\n", 
    metrics.Buy.Volume, metrics.Sell.Volume)
```

### Integration with OrderManager

#### Option 1: Extended OrderManager
```go
// Create OrderManager with built-in metrics
omm, err := metrics.NewOrderManagerWithMetrics(exchange, accGtw, logger, repo)
if err != nil {
    panic(err)
}
defer omm.Close()

ctx := context.Background()
if err := omm.Init(ctx); err != nil {
    panic(err)
}

// Use normally - metrics are automatically collected
for tick := range tickChannel {
    omm.ProcessTick(tick)
}

// Access metrics
metricsManager := omm.GetMetrics()
allMetrics, _ := metricsManager.GetAllMetrics()
```

#### Option 2: Callback-based Integration
```go
// Create base OrderManager
baseOrderManager := ordermanager.New(exchange, accGtw, logger, repo)

// Create metrics manager
metricsManager, err := metrics.SetupDefaultMetrics(ctx, repo)
if err != nil {
    panic(err)
}

// Integrate with callbacks
orderManagerWithMetrics := metrics.IntegrateWithCallbacks(baseOrderManager, metricsManager)

// Use the wrapped manager
for tick := range tickChannel {
    orderManagerWithMetrics.ProcessTick(tick)
}
```

### Custom Configuration

```go
// Define custom time ranges
customTimeRanges := []time.Duration{
    5 * time.Minute,
    15 * time.Minute,
    1 * time.Hour,
    4 * time.Hour,
    24 * time.Hour,
}

// Create custom metrics configuration
config := metrics.FillMetricsConfig{
    TimeRanges:          customTimeRanges,
    HydrateFromDuration: 24 * time.Hour,
    Logger:              logger.New("custom_metrics"),
}

// Create metrics system
fillMetrics := metrics.NewFillMetrics(config)

registry := metrics.NewRegistry(metrics.DefaultRegistryConfig())
registry.RegisterAdapter("custom_fill_metrics", fillMetrics)

manager := metrics.NewManager(metrics.ManagerConfig{
    Registry: registry,
})
```

## API Reference

### Core Types

#### `FillMetrics`
```go
type FillMetrics struct {
    Market     string            `json:"market"`
    TimeRange  time.Duration     `json:"time_range"`
    UpdatedAt  time.Time         `json:"updated_at"`
    Buy        FillMetricsSide   `json:"buy"`
    Sell       FillMetricsSide   `json:"sell"`
}
```

#### `FillMetricsSide`
```go
type FillMetricsSide struct {
    Volume      float64 `json:"volume"`       // Total base volume
    QuoteVolume float64 `json:"quote_volume"` // Total quote volume
    Count       int     `json:"count"`        // Number of fills
    AvgPrice    float64 `json:"avg_price"`    // Calculated: QuoteVolume / Volume
}
```

### Key Methods

#### `Manager.GetFillMetrics(market, timeRange)`
Returns fill metrics for a specific market and time range.

#### `Manager.GetAllMetrics()`
Returns metrics for all tracked markets and time ranges.

#### `Manager.OnFill(fill)`
Processes a new fill and updates metrics.

#### `Manager.GetStatus()`
Returns current status of the metrics system.

## Time Ranges

The system supports arbitrary time ranges. Common configurations:

- **Standard**: 1h, 24h, 1week
- **High-frequency**: 1m, 5m, 15m, 1h
- **Long-term**: 1h, 6h, 24h, 1week, 1month

## Data Expiration

The system automatically expires fills when they fall outside their time ranges:

- **No Goroutines**: Uses periodic cleanup instead of goroutines
- **Efficient**: Maintains sorted data structures for O(log n) operations
- **Configurable**: Cleanup interval can be customized

## Performance Considerations

1. **Memory Usage**: Tracks fills in memory for fast access
2. **Cleanup**: Automatic cleanup removes expired data
3. **Batch Processing**: Supports batch operations for efficiency
4. **Caching**: Caches calculated metrics to avoid recalculation

## Error Handling

The system is designed to be resilient:

- Fill processing errors don't stop the OrderManager
- Metrics failures are logged but don't propagate
- Graceful degradation when metrics are unavailable

## Testing

Run the basic functionality test:

```go
if err := metrics.TestBasicFunctionality(); err != nil {
    panic(err)
}
```

## Examples

See `example.go` for comprehensive usage examples including:

- Basic metrics collection
- Integration patterns
- Custom time ranges
- API usage patterns
- Testing utilities

## Future Enhancements

1. **Persistence**: Optional metrics persistence to disk
2. **Aggregation**: Higher-level aggregation across markets
3. **Alerting**: Metrics-based alerting system
4. **Streaming**: Real-time metrics streaming
5. **Visualization**: Built-in metrics visualization tools