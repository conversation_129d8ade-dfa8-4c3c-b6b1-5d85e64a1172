package metrics

import (
	"context"
	"time"

	"github.com/herenow/atomic-tools/pkg/logger"
	"github.com/herenow/ordermanager/repository"
	"github.com/herenow/ordermanager/types"
)

// Package-level convenience functions and types

// Manager provides a high-level interface to the metrics system
type Manager struct {
	registry Registry
	logger   *logger.Logger
}

// ManagerConfig configures the metrics manager
type ManagerConfig struct {
	Registry Registry
	Logger   *logger.Logger
}

// NewManager creates a new metrics manager
func NewManager(config ManagerConfig) *Manager {
	if config.Logger == nil {
		panic("logger is required for Manager")
	}

	if config.Registry == nil {
		config.Registry = NewRegistry(DefaultRegistryConfig(config.Logger))
	}

	return &Manager{
		registry: config.Registry,
		logger:   config.Logger,
	}
}

// Setup sets up the metrics system with default fill metrics
func (m *Manager) Setup(ctx context.Context, repo repository.Repository) error {
	// Create and register default fill metrics
	fillMetrics := NewFillMetrics(DefaultFillMetricsConfig(m.logger))

	if err := m.registry.RegisterAdapter("fill_metrics", fillMetrics); err != nil {
		return err
	}

	// Initialize all adapters
	return m.registry.InitializeAll(ctx, repo)
}

// OnFill processes a new fill
func (m *Manager) OnFill(fill types.Fill) error {
	return m.registry.OnFill(fill)
}

// GetFillMetrics returns fill metrics for a specific market and time range
func (m *Manager) GetFillMetrics(market string, timeRange time.Duration) (FillMetrics, error) {
	providers := m.registry.GetMetricsProviders()

	// Try to get from the first available provider
	for _, provider := range providers {
		if metrics, err := provider.GetFillMetrics(market, timeRange); err == nil {
			return metrics, nil
		}
	}

	// Return empty metrics if no provider available
	return FillMetrics{
		Market:    market,
		TimeRange: timeRange,
		UpdatedAt: time.Now(),
	}, nil
}

// GetAllMetrics returns all available metrics
func (m *Manager) GetAllMetrics() (map[string]map[time.Duration]FillMetrics, error) {
	providers := m.registry.GetMetricsProviders()

	for _, provider := range providers {
		if fillProvider, ok := provider.(*fillMetrics); ok {
			return fillProvider.GetAllMetrics()
		}
	}

	return make(map[string]map[time.Duration]FillMetrics), nil
}

// GetStatus returns the current status of the metrics system
func (m *Manager) GetStatus() map[string]interface{} {
	status := m.registry.GetStatus()

	// Add additional status information
	providers := m.registry.GetMetricsProviders()

	providerStatus := make(map[string]interface{})
	for name, provider := range providers {
		if fillProvider, ok := provider.(*fillMetrics); ok {
			providerStatus[name] = fillProvider.GetMetricsSummary()
		}
	}

	status["providers"] = providerStatus
	return status
}

// Cleanup runs cleanup on all adapters
func (m *Manager) Cleanup() error {
	return m.registry.Cleanup()
}

// Close closes the metrics system
func (m *Manager) Close() error {
	return m.registry.Close()
}

// Convenience functions for common operations

// SetupDefaultMetrics sets up the metrics system with default configuration
func SetupDefaultMetrics(ctx context.Context, repo repository.Repository) (*Manager, error) {
	manager := NewManager(ManagerConfig{})

	if err := manager.Setup(ctx, repo); err != nil {
		return nil, err
	}

	return manager, nil
}

// StandardTimeRanges returns commonly used time ranges
func StandardTimeRanges() []time.Duration {
	return []time.Duration{
		1 * time.Hour,
		24 * time.Hour,
		7 * 24 * time.Hour,  // 1 week
		30 * 24 * time.Hour, // 1 month (approximately)
	}
}

// QuickTimeRanges returns shorter time ranges for testing
func QuickTimeRanges() []time.Duration {
	return []time.Duration{
		1 * time.Minute,
		5 * time.Minute,
		15 * time.Minute,
		1 * time.Hour,
	}
}
