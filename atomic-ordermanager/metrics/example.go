package metrics

import (
	"fmt"
	"time"
)

// Example shows how to use the metrics system
func Example() {
	// This is a demonstration of how to use the metrics system
	// In practice, you would integrate this with your actual OrderManager

	fmt.Println("Metrics System Example")
	fmt.Println("======================")
	fmt.Println("1. Inject your logger into ManagerConfig")
	fmt.Println("2. Create metrics manager with NewManager(config)")
	fmt.Println("3. Set up metrics with custom time ranges")
	fmt.Println("4. Integrate with OrderManager using IntegrateWithCallbacks")
	fmt.Println("5. Access metrics via manager.GetFillMetrics(market, timeRange)")
	fmt.Println("")
	fmt.Println("See cmd/simulator/main.go for real usage example.")
}

// ExampleCustomTimeRanges shows how to set up custom time ranges
func ExampleCustomTimeRanges() {
	// Example of custom time ranges that you might use
	customTimeRanges := []time.Duration{
		15 * time.Minute,
		1 * time.Hour,
		4 * time.Hour,
		24 * time.Hour,
	}

	fmt.Printf("Example: Creating metrics system with %d custom time ranges\n", len(customTimeRanges))
	fmt.Println("Time ranges:")
	for _, tr := range customTimeRanges {
		fmt.Printf("  - %v\n", tr)
	}

	// In real usage:
	// 1. Create your logger
	// 2. Create FillMetricsConfig with custom time ranges and logger
	// 3. Use IntegrateWithCallbacks to create OrderManager with metrics

	fmt.Println("\nSee cmd/simulator/main.go for complete implementation.")
}

// ExampleIntegration shows the integration pattern
func ExampleIntegration() {
	fmt.Println("Integration Pattern:")
	fmt.Println("===================")
	fmt.Println("// 1. Create your logger")
	fmt.Println("// logger := yourLogger")
	fmt.Println("")
	fmt.Println("// 2. Create metrics config")
	fmt.Println("// config := metrics.FillMetricsConfig{")
	fmt.Println("//     TimeRanges: []time.Duration{1*time.Hour, 24*time.Hour},")
	fmt.Println("//     Logger: logger,")
	fmt.Println("// }")
	fmt.Println("")
	fmt.Println("// 3. Create and integrate with OrderManager")
	fmt.Println("// om := metrics.IntegrateWithCallbacks(exchange, gateway, logger, repo, metricsManager)")
	fmt.Println("")
	fmt.Println("// 4. Use normally - fills are automatically tracked")
	fmt.Println("// om.SendOrder(...)")
	fmt.Println("")
	fmt.Println("// 5. Query metrics")
	fmt.Println("// metrics, _ := metricsManager.GetFillMetrics(\"BTC/USDT\", time.Hour)")
}

// ExampleTimeRanges returns example time ranges
func ExampleTimeRanges() []time.Duration {
	return []time.Duration{
		1 * time.Hour,
		24 * time.Hour,
		7 * 24 * time.Hour, // 1 week
	}
}
