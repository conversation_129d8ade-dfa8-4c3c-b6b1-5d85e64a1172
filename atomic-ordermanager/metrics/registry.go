package metrics

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/herenow/atomic-tools/pkg/logger"
	"github.com/herenow/ordermanager/repository"
	"github.com/herenow/ordermanager/types"
	"go.uber.org/zap"
)

// registry implements Registry interface
type registry struct {
	mu       sync.RWMutex
	adapters map[string]MetricsAdapter
	logger   *logger.Logger

	// Cleanup configuration
	cleanupInterval time.Duration
	lastCleanup     time.Time
}

// RegistryConfig configures the metrics registry
type RegistryConfig struct {
	CleanupInterval time.Duration
	Logger          *logger.Logger
}

// DefaultRegistryConfig returns default configuration
func DefaultRegistryConfig(logger *logger.Logger) RegistryConfig {
	return RegistryConfig{
		CleanupInterval: 5 * time.Minute, // Cleanup every 5 minutes
		Logger:          logger,
	}
}

// NewRegistry creates a new metrics registry
func NewRegistry(config RegistryConfig) Registry {
	if config.Logger == nil {
		panic("logger is required for Registry")
	}

	if config.CleanupInterval == 0 {
		defaultConfig := DefaultRegistryConfig(config.Logger)
		config.CleanupInterval = defaultConfig.CleanupInterval
	}

	return &registry{
		adapters:        make(map[string]MetricsAdapter),
		logger:          config.Logger,
		cleanupInterval: config.CleanupInterval,
		lastCleanup:     time.Now(),
	}
}

// RegisterAdapter registers a new metrics adapter
func (r *registry) RegisterAdapter(name string, adapter MetricsAdapter) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.adapters[name]; exists {
		return fmt.Errorf("adapter %s already registered", name)
	}

	r.adapters[name] = adapter
	r.logger.Info("Registered metrics adapter", zap.String("name", name))

	return nil
}

// GetAdapter returns a specific adapter
func (r *registry) GetAdapter(name string) (MetricsAdapter, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	adapter, exists := r.adapters[name]
	return adapter, exists
}

// OnFill notifies all registered adapters about a new fill
func (r *registry) OnFill(fill types.Fill) error {
	r.mu.RLock()
	adapters := make([]MetricsAdapter, 0, len(r.adapters))
	for _, adapter := range r.adapters {
		adapters = append(adapters, adapter)
	}
	r.mu.RUnlock()

	// Notify all adapters
	var lastError error
	for _, adapter := range adapters {
		if err := adapter.OnFill(fill); err != nil {
			r.logger.Error("adapter failed to process fill",
				zap.String("fill_id", fill.InternalID),
				zap.Error(err))
			lastError = err
		}
	}

	// Auto-cleanup if needed
	if r.shouldRunCleanup() {
		r.runCleanup()
	}

	return lastError
}

// Cleanup triggers cleanup on all adapters
func (r *registry) Cleanup() error {
	r.mu.RLock()
	adapters := make([]MetricsAdapter, 0, len(r.adapters))
	for _, adapter := range r.adapters {
		adapters = append(adapters, adapter)
	}
	r.mu.RUnlock()

	var lastError error
	for _, adapter := range adapters {
		if err := adapter.Cleanup(); err != nil {
			r.logger.Error("adapter cleanup failed", zap.Error(err))
			lastError = err
		}
	}

	r.lastCleanup = time.Now()
	return lastError
}

// Close closes all adapters
func (r *registry) Close() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	var lastError error
	for name, adapter := range r.adapters {
		if err := adapter.Close(); err != nil {
			r.logger.Error("adapter close failed",
				zap.String("name", name),
				zap.Error(err))
			lastError = err
		}
	}

	r.adapters = make(map[string]MetricsAdapter)
	r.logger.Info("Closed all metrics adapters")

	return lastError
}

// shouldRunCleanup checks if cleanup should be run
func (r *registry) shouldRunCleanup() bool {
	return time.Since(r.lastCleanup) >= r.cleanupInterval
}

// runCleanup runs cleanup asynchronously
func (r *registry) runCleanup() {
	// Run cleanup in background to avoid blocking fill processing
	go func() {
		if err := r.Cleanup(); err != nil {
			r.logger.Error("background cleanup failed", zap.Error(err))
		}
	}()
}

// GetRegisteredAdapters returns names of all registered adapters
func (r *registry) GetRegisteredAdapters() []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	names := make([]string, 0, len(r.adapters))
	for name := range r.adapters {
		names = append(names, name)
	}

	return names
}

// InitializeAll initializes all registered adapters
func (r *registry) InitializeAll(ctx context.Context, repo repository.Repository) error {
	r.mu.RLock()
	adapters := make(map[string]MetricsAdapter)
	for name, adapter := range r.adapters {
		adapters[name] = adapter
	}
	r.mu.RUnlock()

	for name, adapter := range adapters {
		r.logger.Info("Initializing adapter", zap.String("name", name))

		if err := adapter.Init(ctx, repo); err != nil {
			r.logger.Error("adapter initialization failed",
				zap.String("name", name),
				zap.Error(err))
			return fmt.Errorf("failed to initialize adapter %s: %w", name, err)
		}

		r.logger.Info("Adapter initialized successfully", zap.String("name", name))
	}

	return nil
}

// GetMetricsProviders returns all adapters that implement FillMetricsProvider
func (r *registry) GetMetricsProviders() map[string]FillMetricsProvider {
	r.mu.RLock()
	defer r.mu.RUnlock()

	providers := make(map[string]FillMetricsProvider)
	for name, adapter := range r.adapters {
		if provider, ok := adapter.(FillMetricsProvider); ok {
			providers[name] = provider
		}
	}

	return providers
}

// GetStatus returns the current status of the registry
func (r *registry) GetStatus() map[string]interface{} {
	r.mu.RLock()
	defer r.mu.RUnlock()

	return map[string]interface{}{
		"registered_adapters": len(r.adapters),
		"adapter_names":       r.GetRegisteredAdapters(),
		"cleanup_interval":    r.cleanupInterval,
		"last_cleanup":        r.lastCleanup,
		"next_cleanup":        r.lastCleanup.Add(r.cleanupInterval),
	}
}
