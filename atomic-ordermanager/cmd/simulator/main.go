package main

import (
	"context"
	"fmt"
	"net/url"
	"os"
	"os/signal"
	"sort"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/buger/goterm"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/integration/binance"
	"github.com/herenow/ordermanager/cmd/simulator/mocks"
	"github.com/herenow/ordermanager/cmd/simulator/pkg/logger"
	"github.com/herenow/ordermanager/metrics"
	ordermanager "github.com/herenow/ordermanager/ordermanager"
	"github.com/herenow/ordermanager/repository"
	"github.com/herenow/ordermanager/syncer"
	"go.uber.org/zap"

	_ "github.com/ClickHouse/clickhouse-go/v2"
)

var logOnly bool

// fillDisplay wraps a fill with display metadata
type fillDisplay struct {
	fill  repository.Fill
	isNew bool
}

// fillsCache maintains recent fills for display with cursor-based navigation
type fillsCache struct {
	fills      []*fillDisplay
	maxSize    int
	lastSeenID string // Track the most recent fill we've seen
}

// newFillsCache creates a new fills cache with persistent iterator
func newFillsCache(repo repository.Repository, maxSize int) *fillsCache {
	fc := &fillsCache{
		fills:   make([]*fillDisplay, 0, maxSize),
		maxSize: maxSize,
		// Don't create iterator here - we'll use fresh ones for each update
	}
	fc.populateInitialCache(repo)
	return fc
}

// populateInitialCache loads the first batch of fills for display
func (fc *fillsCache) populateInitialCache(repo repository.Repository) {
	// Get initial batch of recent fills using fluent API
	tempIter := repo.FillIterator().Reverse().BatchSize(fc.maxSize).Build()
	defer tempIter.Close()

	// Load initial fills (up to maxSize)
	for len(fc.fills) < fc.maxSize && tempIter.HasNext() {
		fill, ok := tempIter.Next()
		if !ok {
			break
		}

		// Add to cache (initial fills are not marked as new)
		fc.fills = append(fc.fills, &fillDisplay{
			fill:  fill,
			isNew: false,
		})

		// Track the newest fill ID we've seen
		if fc.lastSeenID == "" {
			fc.lastSeenID = fill.InternalID
		}
	}
}

// update refreshes the entire cache with the most recent fills
func (fc *fillsCache) update(repo repository.Repository) {
	// Store the current newest fill ID to detect new fills
	previousNewestID := fc.lastSeenID

	// Get a fresh snapshot of recent fills using fluent API
	tempIter := repo.FillIterator().Reverse().BatchSize(fc.maxSize).Build()
	defer tempIter.Close()

	var newFills []*fillDisplay

	// Collect the most recent fills up to maxSize
	for tempIter.HasNext() && len(newFills) < fc.maxSize {
		fill, ok := tempIter.Next()
		if !ok {
			break
		}

		// Mark as new if this fill is newer than our previous newest
		isNew := previousNewestID != "" && fill.InternalID != previousNewestID && len(newFills) == 0

		newFills = append(newFills, &fillDisplay{
			fill:  fill,
			isNew: isNew,
		})

		// Stop marking as new after we've seen our previous newest
		if fill.InternalID == previousNewestID {
			isNew = false
		}
	}

	// Replace the entire cache with fresh data
	fc.fills = newFills

	// Update lastSeenID to the newest fill
	if len(fc.fills) > 0 {
		fc.lastSeenID = fc.fills[0].fill.InternalID
	}
}

// getFills returns the current cached fill displays
func (fc *fillsCache) getFills() []*fillDisplay {
	return fc.fills
}

// close releases resources
func (fc *fillsCache) close() {
	// No persistent iterator to close with new fluent API approach
}

// parseTimeRanges parses a comma-separated string of time ranges into []time.Duration
func parseTimeRanges(timeRangesStr string) ([]time.Duration, error) {
	if timeRangesStr == "" {
		return metrics.StandardTimeRanges()[:3], nil // Default: 1h, 24h, 1week
	}

	parts := strings.Split(timeRangesStr, ",")
	timeRanges := make([]time.Duration, 0, len(parts))

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		duration, err := time.ParseDuration(part)
		if err != nil {
			return nil, fmt.Errorf("invalid time range '%s': %w", part, err)
		}
		timeRanges = append(timeRanges, duration)
	}

	if len(timeRanges) == 0 {
		return metrics.StandardTimeRanges()[:3], nil // Default: 1h, 24h, 1week
	}

	return timeRanges, nil
}

// formatTimeRange formats a time.Duration for display
func formatTimeRange(d time.Duration) string {
	switch {
	case d < time.Hour:
		return fmt.Sprintf("%.0fm", d.Minutes())
	case d < 24*time.Hour:
		return fmt.Sprintf("%.0fh", d.Hours())
	case d < 7*24*time.Hour:
		return fmt.Sprintf("%.0fd", d.Hours()/24)
	case d == 7*24*time.Hour:
		return "1w"
	default:
		return fmt.Sprintf("%.1fd", d.Hours()/24)
	}
}

// parseClickHouseDSN parses a ClickHouse DSN URL into clickhouse.Options
func parseClickHouseDSN(dsn string) (*clickhouse.Options, error) {
	u, err := url.Parse(dsn)
	if err != nil {
		return nil, fmt.Errorf("invalid DSN format: %w", err)
	}

	port := 9000
	if u.Port() != "" {
		port, err = strconv.Atoi(u.Port())
		if err != nil {
			return nil, fmt.Errorf("invalid port: %w", err)
		}
	}

	username := "default"
	password := ""
	if u.User != nil {
		username = u.User.Username()
		password, _ = u.User.Password()
	}

	database := "default"
	if u.Path != "" && u.Path != "/" {
		database = u.Path[1:] // Remove leading slash
	}

	return &clickhouse.Options{
		Addr: []string{fmt.Sprintf("%s:%d", u.Hostname(), port)},
		Auth: clickhouse.Auth{
			Database: database,
			Username: username,
			Password: password,
		},
	}, nil
}

func main() {
	cfg := loadConfig()
	logOnly = cfg.logOnly

	// Set up custom log buffer that writes to file
	logBuffer, err := logger.NewLogBuffer("simulator.log")
	if err != nil {
		panic(err)
	}
	logBuffer.SetLogOnlyMode(logOnly)
	defer logBuffer.Close()

	_logger, err := logger.New(logBuffer, cfg.logLevel)
	if err != nil {
		panic(err)
	}

	_logger.Info("--- Starting OrderManager Simulator ---")

	// --- 1. Initialize Dependencies ---
	if cfg.clean {
		os.RemoveAll(cfg.dbPath) // Clean up from previous runs
	}
	repo, err := repository.NewRepository(cfg.dbPath, repository.DefaultConfig())
	defer repo.Close()
	if err != nil {
		_logger.Fatal("Failed to create repository", zap.Error(err))
	}

	// Parse metrics time ranges
	timeRanges, err := parseTimeRanges(cfg.metricsTimeRanges)
	if err != nil {
		_logger.Fatal("Failed to parse metrics time ranges", zap.Error(err))
	}
	_logger.Info("Metrics time ranges configured", zap.String("ranges", cfg.metricsTimeRanges))

	ctx, cancel := context.WithCancel(context.Background())

	// Initialize the Mock Gateway
	mockGateway := mocks.NewMockGateway(cfg.mockDelay)

	// --- 2. Create the ordermanager with metrics ---
	market := gateway.Market{
		Exchange: binance.Exchange,
		Symbol:   cfg.market,
	}

	// Create metrics manager with custom time ranges
	metricsManager := metrics.NewManager(metrics.ManagerConfig{
		Logger: _logger,
	})

	// Create custom fill metrics with configured time ranges
	metricsConfig := metrics.FillMetricsConfig{
		TimeRanges:          timeRanges,
		HydrateFromDuration: timeRanges[len(timeRanges)-1], // Use longest time range for hydration
		Logger:              _logger,
	}

	fillMetrics := metrics.NewFillMetrics(metricsConfig)

	// Register and initialize the metrics system manually
	registry := metrics.NewRegistry(metrics.DefaultRegistryConfig(_logger))
	if err := registry.RegisterAdapter("fill_metrics", fillMetrics); err != nil {
		_logger.Fatal("Failed to register fill metrics adapter", zap.Error(err))
	}

	if err := registry.InitializeAll(ctx, repo); err != nil {
		_logger.Fatal("Failed to initialize metrics system", zap.Error(err))
	}

	// Update metrics manager with configured registry
	metricsManager = metrics.NewManager(metrics.ManagerConfig{
		Registry: registry,
		Logger:   _logger,
	})

	// Create OrderManager with metrics integration
	momWithMetrics := metrics.IntegrateWithCallbacks(binance.Exchange, mockGateway, _logger, repo, metricsManager)

	// --- 3. Start the Manager ---
	if err := momWithMetrics.Init(context.Background()); err != nil {
		_logger.Fatal("Failed to start manager", zap.Error(err))
	}

	if cfg.cancelAllOrdersOnStartup {
		_logger.Info("Cancelling all open orders on startup...")
		openOrders := momWithMetrics.GetOpenOrders()
		cancelErrs := make([]error, 0, len(openOrders))
		for _, order := range openOrders {
			if _, err := momWithMetrics.CancelOrder(context.Background(), order.View().InternalID, "startup_cleanup"); err != nil {
				cancelErrs = append(cancelErrs, err)
			}
		}
		if len(cancelErrs) > 0 {
			_logger.Error("Some orders could not be cancelled on startup", zap.Int("failed_count", len(cancelErrs)), zap.Errors("errors", cancelErrs))
		}
	} else {
		_logger.Info("Skipping cancellation of all open orders on startup.")
	}

	// --- 4. Initialize ClickHouse Syncer (Optional) ---
	var clickhouseSyncer *syncer.ClickhouseSyncer
	if cfg.clickhouseDSN != "" {

		_logger.Info("Attempting to connect to ClickHouse", zap.String("dsn", cfg.clickhouseDSN))

		// Parse DSN and open connection using native driver
		opts, err := parseClickHouseDSN(cfg.clickhouseDSN)
		if err != nil {
			_logger.Error("Failed to parse ClickHouse DSN - syncing disabled", zap.Error(err))
			return
		}

		conn, err := clickhouse.Open(opts)
		if err != nil {
			_logger.Error("Failed to open ClickHouse connection - syncing disabled", zap.Error(err))
			return
		}
		defer conn.Close()

		// Test the connection
		if err := conn.Ping(context.Background()); err != nil {
			_logger.Error("Failed to ping ClickHouse - syncing disabled", zap.Error(err))
			return
		}

		// Create tables if they don't exist
		if err := conn.Exec(context.Background(), syncer.CreateOrdersTableSchema()); err != nil {
			_logger.Error("Failed to create orders table - syncing disabled", zap.Error(err))
			return
		}
		if err := conn.Exec(context.Background(), syncer.CreateFillsTableSchema()); err != nil {
			_logger.Error("Failed to create fills table - syncing disabled", zap.Error(err))
			return
		}

		clickhouseSyncer = syncer.NewClickhouseSyncer(conn, _logger)
		_logger.Info("ClickHouse syncer initialized successfully")
	}

	// --- 5. Initialize fills cache ---
	fillsCache := newFillsCache(repo, 20) // Keep last 20 fills
	defer fillsCache.close()

	// --- 6. Start the Simulator ---
	sim := newSimulator(ctx, momWithMetrics, market, mockGateway, _logger, cfg.fillRate, cfg.fillProbability)
	sim.start()

	// --- 7. Handle Graceful Shutdown ---
	sigChan := make(chan os.Signal, 1)
	finishChan := make(chan struct{}, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	tickerInterval := 500 * time.Millisecond
	ticker := time.NewTicker(tickerInterval)
	defer ticker.Stop()

	// Syncer ticker - sync every 5 seconds
	var syncTicker *time.Ticker
	if clickhouseSyncer != nil {
		syncTicker = time.NewTicker(5 * time.Second)
		defer syncTicker.Stop()
	}

	_logger.Info("Press Ctrl+C to stop the simulation", zap.Duration("duration", cfg.duration))

	go func() {
		<-time.After(cfg.duration)
		finishChan <- struct{}{}
	}()

	// Block until a signal is received or the duration expires
	for {
		select {
		case <-sigChan:
			_logger.Info("Shutdown signal received.")
			goto end
		case <-finishChan:
			_logger.Info("Simulation duration has elapsed", zap.Duration("duration", cfg.duration))
			goto end
		case <-ticker.C:
			if !logOnly {
				fillsCache.update(repo) // Update fills cache before rendering
				renderOrders(momWithMetrics, market, fillsCache, metricsManager, timeRanges)
			}
		case <-func() <-chan time.Time {
			if syncTicker != nil {
				return syncTicker.C
			}
			return make(chan time.Time) // Never triggers if no syncer
		}():
			if clickhouseSyncer != nil {
				if err := clickhouseSyncer.Flush(ctx, repo); err != nil {
					_logger.Error("Failed to sync to ClickHouse", zap.Error(err))
				}
			}
		}
	}

end:
	// --- 8. Stop the Simulator ---
	cancel() // Signal the simulator to stop
	sim.stop()
	if !logOnly {
		fillsCache.update(repo) // Final update before shutdown
		renderOrders(momWithMetrics, market, fillsCache, metricsManager, timeRanges)
	}

	// Cleanup metrics
	metricsManager.Close()
}

func renderOrders(mom ordermanager.OrderManager, market gateway.Market, fillsCache *fillsCache, metricsManager *metrics.Manager, timeRanges []time.Duration) {
	if logOnly {
		return
	}
	goterm.Clear()
	goterm.MoveCursor(1, 1)
	goterm.Println("--- Order Manager ---")
	goterm.Println("Market:", market.Symbol)

	// Get metrics and order counts
	orderMetrics := mom.Metrics()
	unknownOrders := mom.GetUnknownOrders()
	unknownCount := len(unknownOrders)

	// Display order statistics
	goterm.Printf("Orders: %d open, %d pending", orderMetrics.OpenOrdersCount, orderMetrics.PendingOrdersCount)
	if unknownCount > 0 {
		goterm.Printf(", %d unknown", unknownCount)
	}
	goterm.Printf(" | Sent: %d, Cancelled: %d\n", orderMetrics.OrdersSentTotal, orderMetrics.OrdersCancelledTotal)
	goterm.Println("--------------------")

	// Display fill metrics table
	if metricsManager != nil && len(timeRanges) > 0 {
		renderFillMetrics(market.Symbol, metricsManager, timeRanges)
	}

	activeOrdersTable := goterm.NewTable(0, 10, 5, ' ', 0)
	fmt.Fprintln(activeOrdersTable, "Exchange ID\tOrder ID\tSide\tPrice\tAmount\tFilled\tStatus")

	pendingOrdersTable := goterm.NewTable(0, 10, 5, ' ', 0)
	fmt.Fprintln(pendingOrdersTable, "Exchange ID\tOrder ID\tSide\tPrice\tAmount\tFilled\tStatus")

	activeOrders := mom.GetOpenOrders()
	pendingOrders := mom.GetPendingOrders()

	// Sort by internal id
	sort.Slice(activeOrders, func(i, j int) bool {
		return activeOrders[i].View().InternalID > activeOrders[j].View().InternalID
	})
	sort.Slice(pendingOrders, func(i, j int) bool {
		return pendingOrders[i].View().InternalID > pendingOrders[j].View().InternalID
	})

	// Limit active orders to 20, show "..." if more exist
	displayCount := len(activeOrders)
	if displayCount > 20 {
		displayCount = 20
	}

	for i := 0; i < displayCount; i++ {
		order := activeOrders[i]
		view := order.View()
		exchangeID := view.OrderID
		if len(exchangeID) > 8 {
			exchangeID = exchangeID[len(exchangeID)-8:]
		}
		orderID := view.InternalID
		if len(orderID) > 8 {
			orderID = orderID[len(orderID)-8:]
		}
		fmt.Fprintf(activeOrdersTable, "%s\t%s\t%s\t%.2f\t%.2f\t%.2f\t%s\n",
			exchangeID,
			orderID,
			view.Side,
			view.Price,
			view.Amount,
			view.FilledBase,
			view.State,
		)
	}

	// Add "..." if there are more than 20 active orders
	if len(activeOrders) > 20 {
		fmt.Fprintf(activeOrdersTable, "...\t...\t...\t...\t...\t...\t...\n")
	}

	for _, order := range pendingOrders {
		view := order.View()
		exchangeID := view.OrderID
		if len(exchangeID) > 8 {
			exchangeID = exchangeID[len(exchangeID)-8:]
		}
		orderID := view.InternalID
		if len(orderID) > 8 {
			orderID = orderID[len(orderID)-8:]
		}
		fmt.Fprintf(pendingOrdersTable, "%s\t%s\t%s\t%.2f\t%.2f\t%.2f\t%s\n",
			exchangeID,
			orderID,
			view.Side,
			view.Price,
			view.Amount,
			view.FilledBase,
			view.State,
		)
	}

	// Display recent fills
	fillsTable := goterm.NewTable(0, 10, 5, ' ', 0)
	fmt.Fprintln(fillsTable, "Time\tOrder ID\tAmount\tPrice\tFee\tTag\tStatus")

	// Get recent fills from cache (most recent activity)
	fillDisplays := fillsCache.getFills()
	fillDisplayCount := len(fillDisplays)
	if fillDisplayCount > 10 {
		fillDisplayCount = 10 // Show maximum 10 fills
	}

	for i := 0; i < fillDisplayCount; i++ {
		fillDisplay := fillDisplays[i]
		fill := fillDisplay.fill
		orderID := fill.OrderID
		if len(orderID) > 8 {
			orderID = orderID[len(orderID)-8:]
		}
		tag := fill.Tag
		if tag == "" {
			tag = "-"
		}
		status := ""
		if fillDisplay.isNew {
			status = "NEW"
		}
		fmt.Fprintf(fillsTable, "%s\t%s\t%.4f\t%.2f\t%.4f\t%s\t%s\n",
			fill.Timestamp.Format("15:04:05"),
			orderID,
			fill.Amount,
			fill.Price,
			fill.Fee,
			tag,
			status,
		)
	}

	goterm.Println("--- Active Orders ---")
	goterm.Println(activeOrdersTable)
	goterm.Println("--- Pending Orders ---")
	goterm.Println(pendingOrdersTable)

	// Display unknown orders table if any exist
	if unknownCount > 0 {
		unknownOrdersTable := goterm.NewTable(0, 10, 5, ' ', 0)
		fmt.Fprintln(unknownOrdersTable, "Exchange ID\tOrder ID\tSide\tPrice\tAmount\tFilled\tStatus\tPrevious states\tCancel requested ago")

		// Sort by internal ID (newest first since UUID v7 is time-ordered)
		sort.Slice(unknownOrders, func(i, j int) bool {
			return unknownOrders[i].View().InternalID > unknownOrders[j].View().InternalID
		})

		// Show maximum 5 unknown orders
		displayCount := unknownCount
		if displayCount > 5 {
			displayCount = 5
		}

		for i := 0; i < displayCount; i++ {
			order := unknownOrders[i]
			view := order.View()
			exchangeID := view.OrderID
			if len(exchangeID) > 8 {
				exchangeID = exchangeID[len(exchangeID)-8:]
			}
			orderID := view.InternalID
			if len(orderID) > 8 {
				orderID = orderID[len(orderID)-8:]
			}
			fmt.Fprintf(unknownOrdersTable, "%s\t%s\t%s\t%.2f\t%.2f\t%.2f\t%s\t%v\t%v\n",
				exchangeID,
				orderID,
				view.Side,
				view.Price,
				view.Amount,
				view.FilledBase,
				view.State,
				view.PreviousStates,
				time.Since(view.CancelRequestedAt),
			)
		}

		// Add "..." if there are more than 5 unknown orders
		if unknownCount > 5 {
			fmt.Fprintf(unknownOrdersTable, "...\t...\t...\t...\t...\t...\t...\n")
		}

		goterm.Println("--- Unknown Orders ---")
		goterm.Println(unknownOrdersTable)
	}

	goterm.Println("--- Recent Fills ---")
	goterm.Println(fillsTable)
	goterm.Flush()
}

// renderFillMetrics displays a table of fill metrics for different time ranges
func renderFillMetrics(marketSymbol string, metricsManager *metrics.Manager, timeRanges []time.Duration) {
	goterm.Println("--- Fill Metrics ---")

	// Create metrics table
	metricsTable := goterm.NewTable(0, 10, 5, ' ', 0)

	// Table header
	header := "Time Range\tBuy Vol\tBuy Avg\tSell Vol\tSell Avg\tNet Vol\tNet Quote\tTotal Trades"
	fmt.Fprintln(metricsTable, header)

	// Fetch and display metrics for each time range
	for _, timeRange := range timeRanges {
		fillMetrics, err := metricsManager.GetFillMetrics(marketSymbol, timeRange)
		if err != nil {
			// If error, show empty row
			fmt.Fprintf(metricsTable, "%s\t-\t-\t-\t-\t-\t-\t-\n", formatTimeRange(timeRange))
			continue
		}

		// Calculate values for display
		buyVol := fillMetrics.Buy.Volume
		buyAvg := fillMetrics.Buy.CalculateAvgPrice()
		sellVol := fillMetrics.Sell.Volume
		sellAvg := fillMetrics.Sell.CalculateAvgPrice()
		netVol := fillMetrics.NetVolume()
		netQuote := fillMetrics.NetQuoteVolume()
		totalTrades := fillMetrics.TotalCount()

		// Format values for display
		buyVolStr := formatVolume(buyVol)
		buyAvgStr := formatPrice(buyAvg)
		sellVolStr := formatVolume(sellVol)
		sellAvgStr := formatPrice(sellAvg)
		netVolStr := formatVolumeWithSign(netVol)
		netQuoteStr := formatVolumeWithSign(netQuote)
		totalTradesStr := fmt.Sprintf("%d", totalTrades)

		fmt.Fprintf(metricsTable, "%s\t%s\t%s\t%s\t%s\t%s\t%s\t%s\n",
			formatTimeRange(timeRange),
			buyVolStr,
			buyAvgStr,
			sellVolStr,
			sellAvgStr,
			netVolStr,
			netQuoteStr,
			totalTradesStr,
		)
	}

	goterm.Println(metricsTable)
}

// formatVolume formats volume for display
func formatVolume(vol float64) string {
	if vol == 0 {
		return "-"
	}
	if vol < 0.01 {
		return fmt.Sprintf("%.4f", vol)
	}
	if vol < 1 {
		return fmt.Sprintf("%.3f", vol)
	}
	if vol < 100 {
		return fmt.Sprintf("%.2f", vol)
	}
	return fmt.Sprintf("%.1f", vol)
}

// formatVolumeWithSign formats volume with + or - sign
func formatVolumeWithSign(vol float64) string {
	if vol == 0 {
		return "-"
	}
	formatted := formatVolume(vol)
	if formatted == "-" {
		return "-"
	}
	if vol > 0 {
		return "+" + formatted
	}
	return formatted // Already has negative sign
}

// formatPrice formats price for display
func formatPrice(price float64) string {
	if price == 0 {
		return "-"
	}
	if price < 0.01 {
		return fmt.Sprintf("%.6f", price)
	}
	if price < 1 {
		return fmt.Sprintf("%.4f", price)
	}
	if price < 100 {
		return fmt.Sprintf("%.2f", price)
	}
	if price < 10000 {
		return fmt.Sprintf("%.1f", price)
	}
	return fmt.Sprintf("%.0f", price)
}
