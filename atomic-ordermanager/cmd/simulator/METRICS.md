# OrderManager Simulator - Metrics Display

The simulator now includes real-time fill metrics tracking and display capabilities.

## Metrics Time Ranges Configuration

You can configure which time ranges to track and display using the `--metrics-time-ranges` parameter:

```bash
# Default configuration (1h, 12h, 24h)
./simulator

# Custom time ranges
./simulator --metrics-time-ranges="5m,1h,6h,24h"

# Short-term focus
./simulator --metrics-time-ranges="30m,1h,12h,24h"

# Long-term analysis with 1 week
./simulator --metrics-time-ranges="1h,6h,24h,168h"
```

## Supported Time Range Formats

Uses Go's standard `time.ParseDuration` format:

### Common Examples
- `1m` = 1 minute
- `5m` = 5 minutes
- `30m` = 30 minutes
- `1h` = 1 hour
- `12h` = 12 hours
- `24h` = 24 hours (1 day)
- `168h` = 168 hours (1 week)

### Advanced Formats
- `15m` = 15 minutes
- `2h` = 2 hours
- `90m` = 90 minutes (1.5 hours)
- `2h30m` = 2 hours and 30 minutes
- `720h` = 720 hours (30 days)

## Metrics Table Display

The simulator displays a comprehensive metrics table showing:

| Column | Description |
|--------|-------------|
| Time Range | The configured time period (e.g., 1h, 24h) |
| Buy Vol | Total volume of buy trades |
| Buy Avg | Volume-weighted average price of buy trades |
| Sell Vol | Total volume of sell trades |
| Sell Avg | Volume-weighted average price of sell trades |
| Net Vol | Net volume (Buy Vol - Sell Vol) |
| Net Quote | Net quote volume (positive = net buying, negative = net selling) |
| Total Trades | Total number of trades in the time period |

## Example Output

```
--- Fill Metrics ---
Time Range  Buy Vol   Buy Avg   Sell Vol  Sell Avg  Net Vol   Net Quote  Total Trades
1h         5.250     50125.50  3.100     50200.25  +2.150    +107631.8  8
12h        15.750    50050.75  12.250    50150.50  +3.500    +174826.2  28
24h        32.100    49975.25  29.800    50075.75  +2.300    +114587.5  62
```

## Features

### Real-time Updates
- Metrics are updated in real-time as fills are processed
- Historical data is automatically loaded when the simulator starts
- Data expires automatically when it falls outside the time range

### Accurate Calculations
- Uses volume-weighted average prices instead of simple averages
- Stores total quote volumes to prevent accumulation errors
- Calculates average prices as QuoteVolume / Volume when displayed

### Multi-Market Support
- Can track multiple markets simultaneously
- Each market maintains separate metrics

### Memory Efficient
- Automatic cleanup of expired data
- No goroutines used for expiration (uses periodic cleanup)
- Efficient sorted data structures for time-based operations

## Usage Examples

```bash
# Quick testing with short time ranges
./simulator --metrics-time-ranges="1m,5m,15m" --duration=10m

# Production monitoring with longer ranges
./simulator --metrics-time-ranges="1h,6h,24h,168h" --duration=0

# High-frequency trading focus
./simulator --metrics-time-ranges="30s,1m,5m,15m,1h"

# Custom duration testing
./simulator --metrics-time-ranges="90m,6h,18h" --duration=2h
```

## Integration with ClickHouse

When using ClickHouse syncing, the metrics will work alongside the persistent storage:

```bash
./simulator \
  --clickhouse-dsn="clickhouse://user:pass@localhost:9000/trading" \
  --metrics-time-ranges="1h,12h,24h,168h" \
  --duration=0
```

## Notes

- Metrics are calculated in-memory for fast access
- Historical data is loaded from the repository on startup
- The longest configured time range determines how far back historical data is loaded
- All metrics are market-specific and will show the configured market symbol