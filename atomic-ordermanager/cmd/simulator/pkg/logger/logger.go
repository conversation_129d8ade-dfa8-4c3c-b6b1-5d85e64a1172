package logger

import (
	"os"
	"sync"

	"github.com/herenow/atomic-tools/pkg/logger"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// LogBuffer is a custom log writer that writes logs directly to a file.
type LogBuffer struct {
	mu      sync.Mutex
	logFile *os.File
	logOnly bool
}

// NewLogBuffer creates a new LogBuffer that writes to a log file.
func NewLogBuffer(logFilePath string) (*LogBuffer, error) {
	logFile, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, err
	}
	return &LogBuffer{logFile: logFile}, nil
}

// Close closes the log file.
func (lb *LogBuffer) Close() error {
	lb.mu.Lock()
	defer lb.mu.Unlock()
	if lb.logFile != nil {
		return lb.logFile.Close()
	}
	return nil
}

// SetLogOnlyMode configures the LogBuffer to immediately print to stdout in log-only mode.
func (lb *LogBuffer) SetLogOnlyMode(logOnly bool) {
	lb.mu.Lock()
	defer lb.mu.Unlock()
	lb.logOnly = logOnly
}

// Write writes logs to the file and optionally to stdout in logOnly mode.
func (lb *LogBuffer) Write(p []byte) (n int, err error) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	// Always write to log file
	if lb.logFile != nil {
		if _, err := lb.logFile.Write(p); err != nil {
			return 0, err
		}
	}

	// If in logOnly mode, also write to stdout
	if lb.logOnly {
		os.Stdout.Write(p)
	}

	return len(p), nil
}

// String returns an empty string since logs are now written directly to file.
func (lb *LogBuffer) String() string {
	return ""
}

// Clear is a no-op since logs are written directly to file.
func (lb *LogBuffer) Clear() {
}

// New creates a new logger that writes to the provided LogBuffer.
func New(logBuffer *LogBuffer, levelStr string) (*logger.Logger, error) {
	level, err := zapcore.ParseLevel(levelStr)
	if err != nil {
		return nil, err
	}

	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "ts"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder

	core := zapcore.NewCore(
		zapcore.NewConsoleEncoder(encoderConfig),
		zapcore.AddSync(logBuffer),
		level,
	)

	zapLogger := zap.New(core)
	return &logger.Logger{Logger: zapLogger}, nil
}
