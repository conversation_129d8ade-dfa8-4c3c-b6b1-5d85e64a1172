package main

import (
	"flag"
	"time"
)

// config holds the application's configuration values.
type config struct {
	dbPath                   string
	market                   string
	duration                 time.Duration
	mockDelay                time.Duration
	fillRate                 time.Duration
	fillProbability          float64
	logOnly                  bool
	logLevel                 string
	clean                    bool
	cancelAllOrdersOnStartup bool
	clickhouseDSN            string
	metricsTimeRanges        string
}

// loadConfig loads the configuration from command-line flags.
func loadConfig() config {
	var cfg config
	flag.StringVar(&cfg.dbPath, "db-path", "./data/ordermanager", "Path to the database directory")
	flag.StringVar(&cfg.market, "market", "BTC/USDT", "Market symbol to trade on")
	flag.DurationVar(&cfg.duration, "duration", 30*time.Second, "Duration for the simulation to run (e.g., 1m, 5m, 1h). 0 means run forever.")
	flag.DurationVar(&cfg.mockDelay, "mock-delay", 100*time.Millisecond, "Simulated network delay for mock gateway operations (e.g., 100ms, 1s).")
	flag.DurationVar(&cfg.fillRate, "fill-rate", 200*time.Millisecond, "Rate at which fills are generated (e.g., 50ms, 1s).")
	flag.Float64Var(&cfg.fillProbability, "fill-probability", 0.3, "Probability (0.0-1.0) that an eligible order will receive a fill on each tick.")
	flag.BoolVar(&cfg.logOnly, "log-only", false, "If true, only prints logs and disables goterm redraw.")
	flag.StringVar(&cfg.logLevel, "log-level", "info", "Logging level (e.g., debug, info, warn, error).")
	flag.BoolVar(&cfg.clean, "clean", false, "If true, cleans the database directory before starting the simulation.")
	flag.BoolVar(&cfg.cancelAllOrdersOnStartup, "cancel-all-orders-on-startup", false, "If true, cancels all open orders on startup.")
	flag.StringVar(&cfg.clickhouseDSN, "clickhouse-dsn", "", "ClickHouse DSN for database connection. If set, enables ClickHouse syncing every 5 seconds.")
	flag.StringVar(&cfg.metricsTimeRanges, "metrics-time-ranges", "1h,12h,24h", "Comma-separated list of time ranges for metrics display (e.g., 1h,12h,24h,168h)")
	flag.Parse()
	return cfg
}
