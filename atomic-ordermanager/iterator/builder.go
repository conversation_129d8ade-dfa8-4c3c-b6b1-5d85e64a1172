package iterator

// Builder provides a fluent API for creating iterators
type Builder[T any] interface {
	Forward() Builder[T]
	Reverse() Builder[T]
	BatchSize(size int) Builder[T]
	StartAfter(id string) Builder[T]
	Build() *Iterator[T]
}

// builder provides a fluent API for creating iterators
type builder[T any] struct {
	fetcherFactory func(startAfterID string) Fetcher[T]
	direction      Direction
	batchSize      int
	startAfterID   string
}

// NewBuilder creates a new iterator builder
func NewBuilder[T any](fetcherFactory func(startAfterID string) Fetcher[T]) Builder[T] {
	return &builder[T]{
		fetcherFactory: fetcherFactory,
		direction:      DirectionReverse, // Default to newest first
		batchSize:      100,              // Default batch size
	}
}

// NewRepositoryBuilder creates a builder that works directly with repository functions
func NewRepositoryBuilder[T any](
	getItemsFunc func(lastID string, limit int, forward bool) ([]T, error),
	idExtractor func(T) string,
) Builder[T] {
	return &builder[T]{
		fetcherFactory: func(startAfterID string) Fetcher[T] {
			return &repositoryFetcher[T]{
				getItems:     getItemsFunc,
				idExtractor:  idExtractor,
				startAfterID: startAfterID,
			}
		},
		direction: DirectionReverse, // Default to newest first
		batchSize: 100,              // Default batch size
	}
}

// repositoryFetcher implements Fetcher using repository functions directly
type repositoryFetcher[T any] struct {
	getItems     func(lastID string, limit int, forward bool) ([]T, error)
	idExtractor  func(T) string
	startAfterID string
}

// FetchFirst implements Fetcher[*T]
func (f *repositoryFetcher[T]) FetchFirst(direction Direction, limit int) ([]T, error) {
	forward := direction == DirectionForward
	return f.getItems(f.startAfterID, limit, forward)
}

// FetchNext implements Fetcher[*T]
func (f *repositoryFetcher[T]) FetchNext(lastID string, direction Direction, limit int) ([]T, error) {
	forward := direction == DirectionForward
	return f.getItems(lastID, limit, forward)
}

// GetID implements Fetcher[*T]
func (f *repositoryFetcher[T]) GetID(item T) string {
	return f.idExtractor(item)
}

// Forward sets the iteration direction to oldest→newest
func (b *builder[T]) Forward() Builder[T] {
	b.direction = DirectionForward
	return b
}

// Reverse sets the iteration direction to newest→oldest (default)
func (b *builder[T]) Reverse() Builder[T] {
	b.direction = DirectionReverse
	return b
}

// BatchSize sets the number of items to fetch per batch
func (b *builder[T]) BatchSize(size int) Builder[T] {
	if size <= 0 {
		size = 100 // Default to 100 if invalid
	}
	b.batchSize = size
	return b
}

// StartAfter sets the cursor to start iteration after the given ID
func (b *builder[T]) StartAfter(id string) Builder[T] {
	b.startAfterID = id
	return b
}

// Build creates the iterator with the configured options
func (b *builder[T]) Build() *Iterator[T] {
	fetcher := b.fetcherFactory(b.startAfterID)
	return New(fetcher, b.direction, b.batchSize)
}
