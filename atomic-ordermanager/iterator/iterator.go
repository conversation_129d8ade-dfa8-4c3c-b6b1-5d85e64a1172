package iterator

import (
	"time"

	"github.com/google/uuid"
)

// Direction specifies the iteration direction
type Direction int

const (
	DirectionForward Direction = iota // Oldest to newest
	DirectionReverse                  // Newest to oldest
)

// Interface represents a generic iterator interface
type Interface[T any] interface {
	// Next advances the iterator and returns the next item
	Next() (T, bool)
	// HasNext returns true if there are more items
	HasNext() bool
	// Close releases any resources held by the iterator
	Close() error
}

// <PERSON><PERSON><PERSON> defines the interface for fetching items in batches
type Fetcher[T any] interface {
	// FetchFirst gets the first batch of items based on direction
	FetchFirst(direction Direction, limit int) ([]T, error)
	// FetchNext gets the next batch of items after the given ID
	FetchNext(lastID string, direction Direction, limit int) ([]T, error)
	// GetID extracts the ID from an item for pagination
	GetID(item T) string
}

// Iterator provides iteration over any type
type Iterator[T any] struct {
	fetcher   Fetcher[T]
	direction Direction
	batchSize int

	// Current batch state
	currentBatch []T
	batchIndex   int
	lastID       string
	exhausted    bool
	closed       bool
}

// New creates a new iterator
func New[T any](fetcher Fetcher[T], direction Direction, batchSize int) *Iterator[T] {
	if batchSize <= 0 {
		batchSize = 100 // Default batch size
	}

	return &Iterator[T]{
		fetcher:   fetcher,
		direction: direction,
		batchSize: batchSize,
	}
}

// Next implements Interface[T]
func (it *Iterator[T]) Next() (T, bool) {
	var zero T

	if it.closed {
		return zero, false
	}

	// Check if we need to load a new batch
	if it.batchIndex >= len(it.currentBatch) {
		if !it.loadNextBatch() {
			return zero, false
		}
	}

	// Check if the next item would violate the cursor cap (only for UUID v7 items)
	if it.batchIndex < len(it.currentBatch) {
		item := it.currentBatch[it.batchIndex]
		itemID := it.fetcher.GetID(item)
		
		// Calculate current millisecond boundary
		currentMs := time.Now().Truncate(time.Millisecond).UnixMilli()
		
		// If this item has a timestamp beyond current millisecond boundary, don't return it yet
		if isItemBeyondTsMs(currentMs, itemID) {
			return zero, false
		}
	}

	item := it.currentBatch[it.batchIndex]
	it.batchIndex++
	it.lastID = it.fetcher.GetID(item)

	return item, true
}

// HasNext implements Interface[T]
func (it *Iterator[T]) HasNext() bool {
	if it.closed || it.exhausted {
		return false
	}

	// If we have items in current batch, we have next
	if it.batchIndex < len(it.currentBatch) {
		return true
	}

	// Try to load next batch to see if there are more items
	return it.loadNextBatch()
}

// Close implements Interface[T]
func (it *Iterator[T]) Close() error {
	it.closed = true
	it.currentBatch = nil
	return nil
}

// loadNextBatch loads the next batch of items based on direction
func (it *Iterator[T]) loadNextBatch() bool {
	if it.exhausted {
		return false
	}

	var items []T
	var err error

	if it.lastID == "" {
		// First batch - get from beginning or end based on direction
		items, err = it.fetcher.FetchFirst(it.direction, it.batchSize)
	} else {
		// Subsequent batches - get next batch after lastID
		items, err = it.fetcher.FetchNext(it.lastID, it.direction, it.batchSize)
	}

	if err != nil {
		it.exhausted = true
		return false
	}

	if len(items) == 0 {
		it.exhausted = true
		return false
	}

	it.currentBatch = items
	it.batchIndex = 0

	// If we got fewer items than requested, we've reached the end
	if len(items) < it.batchSize {
		it.exhausted = true
	}

	return true
}

// isItemBeyondTsMs checks if an item's timestamp is beyond the given millisecond boundary
// This prevents reading items that are too recent and might cause UUID v7 sequencing issues
func isItemBeyondTsMs(currentMs int64, id string) bool {
	// Try to parse as UUID v7 to extract timestamp
	if parsedUUID, err := uuid.Parse(id); err == nil {
		// Check if this is a UUID v7 (version 7)
		if parsedUUID.Version() == 7 {
			// Extract timestamp from UUID v7 (returns milliseconds since Unix epoch)
			itemMs := extractUUIDv7TimeMs(parsedUUID)
			
			// Check if the item timestamp is beyond current millisecond boundary
			return itemMs > currentMs
		}
	}
	
	// For non-UUID v7 IDs, no restriction
	return false
}

// extractUUIDv7TimeMs extracts the timestamp from a UUID v7 as milliseconds since Unix epoch
func extractUUIDv7TimeMs(u uuid.UUID) int64 {
	// UUID v7 format: timestamp (48 bits) + version (4 bits) + random (12 bits) + variant (2 bits) + random (62 bits)
	// Extract the first 48 bits as milliseconds since Unix epoch
	bytes := u[:]
	
	// Combine first 6 bytes to get 48-bit timestamp
	timestampMs := uint64(bytes[0])<<40 |
		uint64(bytes[1])<<32 |
		uint64(bytes[2])<<24 |
		uint64(bytes[3])<<16 |
		uint64(bytes[4])<<8 |
		uint64(bytes[5])
	
	// Return as int64 milliseconds
	return int64(timestampMs)
}