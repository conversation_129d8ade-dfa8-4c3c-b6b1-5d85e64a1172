package types

import (
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

// FillMeta represents metadata associated with a fill
type FillMeta map[string]any

type Fill struct {
	BotID     string `json:"bot_id"`
	AccountID string `json:"account_id"`
	Source    string `json:"source"`

	ExchangeID string `json:"exchange_id"`
	Symbol     string `json:"symbol"`

	InternalID      string `json:"internal_id"`
	Tag             string `json:"tag"`
	InternalOrderID string `json:"internal_order_id"`
	TradeID         string `json:"trade_id"` // Trade in on the exchange
	OrderID         string `json:"order_id"` // Order ID on the exchange

	Side     gateway.Side `json:"side"`
	Amount   float64      `json:"amount"`
	Price    float64      `json:"price"`
	Fee      float64      `json:"fee"`
	FeeAsset string       `json:"fee_asset"`

	Timestamp time.Time `json:"timestamp"`
	Meta      FillMeta  `json:"meta,omitempty"` // Additional metadata for the fill provided by a callback
}
