package types

import (
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type OrderView struct {
	BotID     string `json:"bot_id"`
	AccountID string `json:"account_id"`
	Source    string `json:"source"`

	ExchangeID string `json:"exchange_id"`
	Symbol     string `json:"symbol"`

	InternalID    string `json:"internal_id"`
	Tag           string `json:"tag"`
	OrderID       string `json:"order_id"`
	ClientOrderID string `json:"client_order_id"`

	Side     gateway.Side `json:"side"`
	Price    float64      `json:"price"`
	Amount   float64      `json:"amount"`
	PostOnly bool         `json:"post_only"`

	State          gateway.OrderState   `json:"state"`
	PreviousStates []gateway.OrderState `json:"previous_states"`
	Canceling      bool                 `json:"canceling"`
	SoftMatch      bool                 `json:"soft_match"`

	FilledBase  float64   `json:"filled_base"`
	FilledQuote float64   `json:"filled_quote"`
	FilledFee   float64   `json:"filled_fee"`
	FeeAsset    string    `json:"fee_asset"`
	FirstFillAt time.Time `json:"first_fill_at"`
	LastFillAt  time.Time `json:"last_fill_at"`

	SentAt            time.Time `json:"sent_at"`
	ConfirmedAt       time.Time `json:"confirmed_at"`
	CanceledAt        time.Time `json:"canceled_at"`
	CancelRequestedAt time.Time `json:"cancel_requested_at"`
	CancelDeadline    time.Time `json:"cancel_deadline"`
	LastUpdateAt      time.Time `json:"last_update_at"`

	Version               int `json:"version"`
	PresumedCanceledCount int `json:"presumed_canceled_count"`
	UnexpectedlyOpenCount int `json:"unexpectedly_open_count"`

	LastOpenErr   string `json:"last_open_err,omitempty"`
	LastCancelErr string `json:"last_cancel_err,omitempty"`
	CancelReason  string `json:"cancel_reason,omitempty"`

	MarketMeta any `json:"market_meta,omitempty"` // Additional metadata for the market, if needed
}

func NewOrderView(botID, accountID string, market gateway.Market, tag string, side gateway.Side, state gateway.OrderState, price, amount float64, postOnly bool, source string) OrderView {
	return OrderView{
		BotID:          botID,
		AccountID:      accountID,
		ExchangeID:     market.Exchange.ID(),
		Symbol:         market.Symbol,
		MarketMeta:     market.Meta, // Meta information about the market, might be needed when canceling orders
		Tag:            tag,
		Side:           side,
		State:          state,
		Price:          price,
		Amount:         amount,
		PostOnly:       postOnly,
		PreviousStates: make([]gateway.OrderState, 0),
		Version:        0, // Start from zero, will increment on first update
		Source:         source,
	}
}

// ==================== SET METHODS ====================

func (ov *OrderView) SetState(newState gateway.OrderState) *OrderView {
	if newState != ov.State {
		ov.PreviousStates = append(ov.PreviousStates, ov.State)
		ov.State = newState
	}
	return ov
}

func (ov *OrderView) SetSent() *OrderView {
	ov.SentAt = time.Now()
	return ov
}

func (ov *OrderView) SetInternalID(id string) *OrderView {
	ov.InternalID = id
	return ov
}

func (ov *OrderView) SetConfirmation(exchangeOrderID string) *OrderView {
	ov.OrderID = exchangeOrderID
	ov.ConfirmedAt = time.Now()
	return ov
}

func (ov *OrderView) SetCanceling(deadline time.Time) *OrderView {
	ov.Canceling = true
	ov.CancelRequestedAt = time.Now()
	ov.CancelDeadline = deadline
	return ov
}

func (ov *OrderView) SetCanceled() *OrderView {
	ov.SetState(gateway.OrderCancelled)
	ov.Canceling = false
	ov.CanceledAt = time.Now()
	return ov
}

func (ov *OrderView) SetExchangeOrderID(exchangeOrderID string) *OrderView {
	ov.OrderID = exchangeOrderID
	return ov
}

func (ov *OrderView) SetPresumedCanceled() *OrderView {
	ov.PresumedCanceledCount++
	ov.SetState(gateway.OrderCancelled)
	return ov
}

func (ov *OrderView) SetUnexpectedlyOpen() *OrderView {
	ov.UnexpectedlyOpenCount++
	if ov.FilledBase > 0 {
		ov.SetState(gateway.OrderPartiallyFilled)
	} else {
		ov.SetState(gateway.OrderOpen)
	}
	return ov
}

func (ov *OrderView) SetClientOrderID(clientOrderID string) *OrderView {
	ov.ClientOrderID = clientOrderID
	return ov
}

func (ov *OrderView) SetLastOpenErr(err error) *OrderView {
	if err != nil {
		ov.LastOpenErr = err.Error()
	} else {
		ov.LastOpenErr = ""
	}
	return ov
}

func (ov *OrderView) SetLastCancelErr(err error) *OrderView {
	if err != nil {
		ov.LastCancelErr = err.Error()
	} else {
		ov.LastCancelErr = ""
	}
	return ov
}

func (ov *OrderView) SetCancelReason(reason string) *OrderView {
	ov.CancelReason = reason
	return ov
}

// ==================== QUERY METHODS ====================

func (ov *OrderView) IsFullyFilled() bool {
	return ov.FilledBase >= ov.Amount
}

func (ov *OrderView) IsPartiallyFilled() bool {
	return ov.FilledBase > 0 && ov.FilledBase < ov.Amount
}

func (ov *OrderView) RemainingAmount() float64 {
	return ov.Amount - ov.FilledBase
}

func (ov *OrderView) IsTerminated() bool {
	return gateway.OrderStateTerminated(ov.State)
}

func (ov *OrderView) IsCanceling() bool {
	return ov.Canceling && !ov.IsTerminated()
}

func (ov *OrderView) IsExpiredCancel() bool {
	return time.Now().After(ov.CancelDeadline)
}

func (ov *OrderView) Age() time.Duration {
	return time.Since(ov.SentAt)
}

// ==================== CONFLICT RESOLUTION ====================

func (ov OrderView) MergeWith(older OrderView) OrderView {
	merged := ov

	merged.FilledBase = max(ov.FilledBase, older.FilledBase)
	merged.FilledQuote = max(ov.FilledQuote, older.FilledQuote)
	merged.FilledFee = max(ov.FilledFee, older.FilledFee)

	if !older.FirstFillAt.IsZero() && (ov.FirstFillAt.IsZero() || older.FirstFillAt.Before(ov.FirstFillAt)) {
		merged.FirstFillAt = older.FirstFillAt
	}

	if older.LastFillAt.After(ov.LastFillAt) {
		merged.LastFillAt = older.LastFillAt
	}

	if !older.SentAt.IsZero() && (ov.SentAt.IsZero() || older.SentAt.Before(ov.SentAt)) {
		merged.SentAt = older.SentAt
	}

	if !older.ConfirmedAt.IsZero() && (ov.ConfirmedAt.IsZero() || older.ConfirmedAt.Before(ov.ConfirmedAt)) {
		merged.ConfirmedAt = older.ConfirmedAt
	}

	if older.Canceling && !ov.Canceling {
		merged.Canceling = true
		merged.CancelRequestedAt = older.CancelRequestedAt
		merged.CancelDeadline = older.CancelDeadline
	} else if older.Canceling && ov.Canceling {
		if older.CancelRequestedAt.Before(ov.CancelRequestedAt) {
			merged.CancelRequestedAt = older.CancelRequestedAt
		}
		if older.CancelDeadline.After(ov.CancelDeadline) {
			merged.CancelDeadline = older.CancelDeadline
		}
	}

	merged.PresumedCanceledCount = max(ov.PresumedCanceledCount, older.PresumedCanceledCount)
	merged.UnexpectedlyOpenCount = max(ov.UnexpectedlyOpenCount, older.UnexpectedlyOpenCount)

	if older.LastUpdateAt.After(ov.LastUpdateAt) {
		merged.LastUpdateAt = older.LastUpdateAt
	}

	merged.PreviousStates = mergePreviousStates(ov.PreviousStates, older.PreviousStates)

	if older.OrderID != "" && ov.OrderID == "" {
		merged.OrderID = older.OrderID
	}
	if older.ClientOrderID != "" && ov.ClientOrderID == "" {
		merged.ClientOrderID = older.ClientOrderID
	}
	if older.FeeAsset != "" && ov.FeeAsset == "" {
		merged.FeeAsset = older.FeeAsset
	}

	if older.LastOpenErr != "" && ov.LastOpenErr == "" {
		merged.LastOpenErr = older.LastOpenErr
	}

	if older.LastCancelErr != "" && ov.LastCancelErr == "" {
		merged.LastCancelErr = older.LastCancelErr
	}

	if older.CancelReason != "" && ov.CancelReason == "" {
		merged.CancelReason = older.CancelReason
	}

	return merged
}

func mergePreviousStates(current, older []gateway.OrderState) []gateway.OrderState {
	stateSet := make(map[gateway.OrderState]bool)
	var merged []gateway.OrderState

	for _, state := range current {
		if !stateSet[state] {
			merged = append(merged, state)
			stateSet[state] = true
		}
	}

	for _, state := range older {
		if !stateSet[state] {
			merged = append(merged, state)
			stateSet[state] = true
		}
	}

	return merged
}
