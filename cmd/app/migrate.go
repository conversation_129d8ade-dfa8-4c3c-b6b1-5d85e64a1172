package main

import (
	"errors"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"

	"github.com/golang-migrate/migrate/v4"
	_ "github.com/golang-migrate/migrate/v4/database/postgres"
	"github.com/golang-migrate/migrate/v4/source/iofs"
	atomicbm "github.com/herenow/atomic-bm"
	"github.com/herenow/atomic-bm/internal/app"
	"github.com/spf13/cobra"
)

// migrationLogger satisfies to migrate.Logger interface.
type migrationLogger struct{}

// Printf is like log.Printf but for migrations.
func (l *migrationLogger) Printf(format string, v ...interface{}) {
	log.Printf(format, v...)
}

// Verbose should return true if you want verbose logging.
func (l *migrationLogger) Verbose() bool {
	return false
}

// AddCommands adds the migration subcommands to the root command.
func AddCommands(rootCmd *cobra.Command) {
	createCmd := &cobra.Command{
		Use:   "create [name]",
		Short: "Create new up and down migration files",
		Long:  `Creates a new set of versioned migration files (up and down) in the migrations directory.`,
		Args:  cobra.ExactArgs(1), // We require exactly one argument for the name
		Run:   runCreateMigration,
	}

	migrateCmd := &cobra.Command{
		Use:   "migrate",
		Short: "Run database migrations using .sql files",
		Long:  `Manages database schema migrations.`,
	}

	upCmd := &cobra.Command{
		Use:   "up",
		Short: "Apply all available 'up' migrations. Up all by default or a specific number of steps.",
		Args:  cobra.MaximumNArgs(1),
		Run:   runMigrateUp,
	}

	downCmd := &cobra.Command{
		Use:   "down [steps]",
		Short: "Revert 'down' migrations. Reverts all by default or a specific number of steps.",
		Args:  cobra.MaximumNArgs(1),
		Run:   runMigrateDown,
	}

	gotoCmd := &cobra.Command{
		Use:   "goto [version]",
		Short: "Migrate to a specific version",
		Args:  cobra.ExactArgs(1),
		Run:   runMigrateGoto,
	}

	versionCmd := &cobra.Command{
		Use:   "version",
		Short: "Print the current migration version",
		Run:   runMigrateVersion,
	}

	migrateCmd.AddCommand(createCmd, upCmd, downCmd, gotoCmd, versionCmd)
	rootCmd.AddCommand(migrateCmd)
}

func runCreateMigration(cmd *cobra.Command, args []string) {
	// 1. Define the migrations directory
	migrationsDir := "internal/db/migrations"

	// 2. Find the latest version number
	latestVersion := 0
	// Regex to extract the version number from filenames like '000001_...sql'
	re := regexp.MustCompile(`^(\d+)_.*\.sql$`)

	files, err := os.ReadDir(migrationsDir)
	if err != nil {
		log.Fatalf("Failed to read migrations directory: %v", err)
	}

	for _, file := range files {
		matches := re.FindStringSubmatch(file.Name())
		if len(matches) > 1 {
			version, err := strconv.Atoi(matches[1])
			if err != nil {
				// This should not happen if regex matches, but good to be safe
				continue
			}
			if version > latestVersion {
				latestVersion = version
			}
		}
	}

	// 3. Increment to get the new version
	newVersion := latestVersion + 1

	// 4. Sanitize the user-provided name
	// Replaces spaces and hyphens with underscores and converts to lowercase
	name := strings.ToLower(args[0])
	name = strings.ReplaceAll(name, " ", "_")
	name = strings.ReplaceAll(name, "-", "_")

	// 5. Generate the new filenames
	// We use %04d to pad the version with leading zeros, e.g., 000001, 000002, etc.
	baseFilename := fmt.Sprintf("%06d_%s", newVersion, name)
	upFilename := fmt.Sprintf("%s.up.sql", baseFilename)
	downFilename := fmt.Sprintf("%s.down.sql", baseFilename)

	upFilePath := filepath.Join(migrationsDir, upFilename)
	downFilePath := filepath.Join(migrationsDir, downFilename)

	// 6. Create the files with placeholder content
	upContent := []byte("-- +migrate Up\n-- SQL in this section is executed when the migration is applied.\n")
	downContent := []byte("-- +migrate Down\n-- SQL in this section is executed when the migration is rolled back.\n")

	if err = os.WriteFile(upFilePath, upContent, 0644); err != nil {
		log.Fatalf("Failed to create up migration file: %v", err)
	}
	log.Printf("Created migration file: %s", upFilePath)

	if err = os.WriteFile(downFilePath, downContent, 0644); err != nil {
		log.Fatalf("Failed to create down migration file: %v", err)
	}
	log.Printf("Created migration file: %s", downFilePath)
}

// getMigrateInstance creates a new migrate instance based on the application configuration.
func getMigrateInstance() (*migrate.Migrate, error) {
	// Load application configuration
	cfg, err := app.ReadConfig("migrate")
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// Use the embedded filesystem.
	sourceInstance, err := iofs.New(atomicbm.MigrationFS, "internal/db/migrations")
	if err != nil {
		return nil, fmt.Errorf("failed to create iofs instance: %w", err)
	}

	// Create a new migration logger
	logger := &migrationLogger{}

	m, err := migrate.NewWithSourceInstance("iofs", sourceInstance, cfg.Database.DSN)
	if err != nil {
		return nil, fmt.Errorf("failed to create migrate instance: %w", err)
	}

	m.Log = logger

	return m, nil
}

func runMigrateUp(cmd *cobra.Command, args []string) {
	m, err := getMigrateInstance()
	if err != nil {
		log.Fatalf("Migration setup failed: %v", err)
	}

	if len(args) > 0 {
		steps, err := strconv.Atoi(args[0])
		if err != nil {
			log.Fatalf("Invalid number of steps: %v", err)
		}
		log.Printf("Applying %d migration(s)...", steps)
		if err = m.Steps(steps); err != nil {
			log.Fatalf("Failed to apply migrations: %v", err)
		}
	} else {
		log.Println("Applying all 'up' migrations...")
		if err = m.Up(); err != nil {
			log.Fatalf("Failed to aplly all migrations: %v", err)
		}
	}

	log.Println("Migrations applied successfully.")
	runMigrateVersion(cmd, args)
}

func runMigrateDown(cmd *cobra.Command, args []string) {
	m, err := getMigrateInstance()
	if err != nil {
		log.Fatalf("Migration setup failed: %v", err)
	}

	if len(args) > 0 {
		steps, err := strconv.Atoi(args[0])
		if err != nil {
			log.Fatalf("Invalid number of steps: %v", err)
		}
		log.Printf("Reverting %d migration(s)...", steps)
		if err = m.Steps(-steps); err != nil {
			log.Fatalf("Failed to revert migrations: %v", err)
		}
	} else {
		log.Println("Reverting all 'down' migrations...")
		if err = m.Down(); err != nil {
			log.Fatalf("Failed to revert all migrations: %v", err)
		}
	}

	log.Println("Migrations reverted successfully.")
	runMigrateVersion(cmd, args)
}

func runMigrateGoto(cmd *cobra.Command, args []string) {
	m, err := getMigrateInstance()
	if err != nil {
		log.Fatalf("Migration setup failed: %v", err)
	}

	version, err := strconv.Atoi(args[0])
	if err != nil {
		log.Fatalf("Invalid version argument: %v", err)
	}

	log.Printf("Migrating to version %d...", version)
	if err = m.Migrate(uint(version)); err != nil {
		if errors.Is(err, migrate.ErrNoChange) {
			log.Printf("Already at version %d.", version)
			return
		}
		log.Fatalf("Failed to migrate to version %d: %v", version, err)
	}

	log.Printf("Migration to version %d successful.", version)
	runMigrateVersion(cmd, args)
}

func runMigrateVersion(cmd *cobra.Command, args []string) {
	m, err := getMigrateInstance()
	if err != nil {
		log.Fatalf("Migration setup failed: %v", err)
	}

	version, dirty, err := m.Version()
	if err != nil {
		if errors.Is(err, migrate.ErrNilVersion) {
			log.Println("No migrations have been applied yet.")
			return
		}
		log.Fatalf("Failed to get migration version: %v", err)
	}

	log.Printf("Current migration version: %d, Dirty: %v", version, dirty)
}
