package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/herenow/atomic-bm/internal/app"
	"github.com/herenow/atomic-bm/internal/auth/casbin"
	"github.com/herenow/atomic-bm/internal/controller/account"
	"github.com/herenow/atomic-bm/internal/controller/bot"
	"github.com/herenow/atomic-bm/internal/controller/exchange"
	"github.com/herenow/atomic-bm/internal/controller/notifications"
	"github.com/herenow/atomic-bm/internal/controller/params"
	"github.com/herenow/atomic-bm/internal/controller/regions"
	"github.com/herenow/atomic-bm/internal/controller/trollbox"
	"github.com/herenow/atomic-bm/internal/controller/user"
	"github.com/herenow/atomic-bm/internal/db/repo/userrepo"
	"github.com/herenow/atomic-bm/internal/pkg/cronjob"
	"github.com/herenow/atomic-bm/internal/ws"

	"github.com/herenow/atomic-bm/atomic-br/botregionalservice"
	"github.com/herenow/atomic-bm/cmd/app/dependencies"
	"github.com/herenow/atomic-bm/pkg/logger"
	"github.com/herenow/atomic-protocols/gen/atomic/api/botregion/v1"
	"github.com/herenow/atomic-protocols/rpc"

	"github.com/google/uuid"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/reflection"
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "botmanager",
		Short: "A bot manager application with integrated migration tools.",
	}

	// Add all commands to the root command
	addCommands(rootCmd)

	if err := rootCmd.Execute(); err != nil {
		log.Fatalf("Command execution failed: %s", err.Error())
	}
}

func addCommands(rootCmd *cobra.Command) {
	// --- Server Command ---
	serverCmd := &cobra.Command{
		Use:   app.ServerCMD,
		Short: "Run the BotManager API server",
		RunE:  runServer,
	}

	// --- Bot Regional Local Command ---
	botRegionalLocalCmd := &cobra.Command{
		Use:   app.LocalBotRegionalServerCMD,
		Short: "Run a mocked Bot Regional Server locally",
		RunE:  runBotRegionalLocal,
	}

	// --- Create User Command ---
	createUserCmd := &cobra.Command{
		Use:   app.CreateUserCMD,
		Short: "Create a new user",
		RunE:  runCreateUser,
	}
	// Add flags to the createUser command
	createUserCmd.Flags().String("name", "", "User name")
	createUserCmd.Flags().String("password", "", "User password")
	createUserCmd.Flags().String("role", "user", "User Role (admin, manager, user)")
	createUserCmd.Flags().String("email", "", "User Email")

	// Add all commands to the root
	rootCmd.AddCommand(serverCmd, botRegionalLocalCmd, createUserCmd)

	// Add migration commands from the migrate package
	AddCommands(rootCmd)
}

func runCreateUser(cmd *cobra.Command, args []string) error {
	username, _ := cmd.Flags().GetString("name")
	if username == "" {
		return errors.New("missing username")
	}
	password, _ := cmd.Flags().GetString("password")
	if password == "" {
		return errors.New("missing password")
	}
	userEmail, _ := cmd.Flags().GetString("email")
	if userEmail == "" {
		return errors.New("missing user email")
	}
	userRole, _ := cmd.Flags().GetString("role")
	switch userRole {
	case "admin":
		userRole = casbin.RoleAdmin
	case "manager":
		userRole = casbin.RoleManager
	default:
		userRole = casbin.RoleUser
	}

	cfg, err := app.ReadConfig(app.CreateUserCMD)
	if err != nil {
		return fmt.Errorf("failed to read config for create-user: %w", err)
	}

	bm, err := app.New(cfg, logger.New("debug"))
	if err != nil {
		return err
	}
	defer bm.Stop()

	bm.InitPolicies()
	ctx := bm.Context()

	u := userrepo.New(bm.DB())
	userUUID := uuid.New().String()
	passHashed, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	if err = u.Create(ctx, &userrepo.User{
		ID:       userUUID,
		Name:     username,
		Role:     userRole,
		Email:    userEmail,
		Password: string(passHashed),
	}); err != nil {
		return err
	}

	if err = bm.Policy().AddRoleForUser(userUUID, userRole); err != nil {
		bm.Logger().Fatal("failed to create role for user")
	}

	bm.Logger().Info("User Created Successfully")
	return nil
}

func runBotRegionalLocal(cmd *cobra.Command, args []string) error {
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	l := logger.New("info")

	g, gCtx := errgroup.WithContext(ctx)
	port := 50051
	ls, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%v", port))
	if err != nil {
		l.Error("grpc tcp listener", zap.Error(err))
		return err
	}

	s := rpc.NewServer(nil, nil, nil)
	srv, err := botregionalservice.NewBotRegionalService(ctx)
	if err != nil {
		l.Error("grpc server listener", zap.Error(err))
	}

	botregion.RegisterBotRegionServiceServer(s, srv)
	reflection.Register(s)

	g.Go(func() error {
		l.Info(fmt.Sprintf("Starting Bot Regional GRPC server at port: %v", port))
		return s.Serve(ls)
	})

	g.Go(func() error {
		<-gCtx.Done()
		l.Info("Shutting down Bot Regional GRPC server")
		s.GracefulStop()
		return nil
	})

	l.Info("Bot Regional GRPC server running. Waiting for signal to exit.")
	if err = g.Wait(); err != nil {
		l.Error("Application exited with error", zap.Error(err))
		return err
	}

	l.Info("Application exited gracefully")
	return nil
}

func runServer(cmd *cobra.Command, args []string) error {
	cfg, err := app.ReadConfig(app.ServerCMD)
	if err != nil {
		return fmt.Errorf("failed to read config for server: %w", err)
	}

	// Create a new application instance with the config
	bm, err := app.New(cfg, logger.New(cfg.Log.Level))
	if err != nil {
		return err
	}
	defer bm.Stop()

	// Initialize server-specific components that don't depend on the router
	bm.InitSession()
	bm.InitPolicies()
	bm.InitRouter()
	bm.InitPushNotifyClient()

	// Use the application's context for the errgroup.
	// This context is cancelled when bm.Stop() is called.
	g, gCtx := errgroup.WithContext(bm.Context())

	port := 50051
	ls, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%v", port))
	if err != nil {
		bm.Logger().Error("grpc tcp listener", zap.Error(err))
		return err
	}

	s := rpc.NewServer(nil, nil, nil)
	gsrv, err := botregionalservice.NewBotRegionalService(gCtx)
	if err != nil {
		bm.Logger().Error("grpc server listener", zap.Error(err))
	}

	botregion.RegisterBotRegionServiceServer(s, gsrv)
	reflection.Register(s)

	g.Go(func() error {
		bm.Logger().Info(fmt.Sprintf("Starting Bot Regional GRPC server at port: %v", port))
		return s.Serve(ls)
	})

	g.Go(func() error {
		<-gCtx.Done()
		bm.Logger().Info("Shutting down Bot Regional GRPC server")
		s.GracefulStop()
		return nil
	})

	// Goroutine to listen for OS signals and trigger the app's shutdown.
	go func() {
		sigCtx, stopSignalListener := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
		defer stopSignalListener()
		<-sigCtx.Done()
		bm.Logger().Info("Received OS interrupt signal, shutting down...")
		bm.Stop() // This will cancel gCtx and trigger graceful shutdowns
	}()

	// Initialize WebSocket Client
	wsClient := ws.Init(gCtx, bm.Policy(), bm.WSRouter(), bm.Session(), userrepo.New(bm.DB()), bm.Logger())
	bm.RegisterWebsocketClient(wsClient)

	// Create the DI container
	container, err := dependencies.NewContainer(bm.DB(), bm.Config(), wsClient, bm.Logger(), bm.Cache())
	if err != nil {
		return err
	}

	// Goroutine to start the bot service worker.
	g.Go(func() error {
		bm.Logger().Info("Starting bot service worker")
		container.BotService.Start(gCtx)
		return nil
	})

	// Declare all routes
	container.Start(bm,
		account.Register,
		bot.Register,
		exchange.Register,
		notifications.Register,
		params.Register,
		regions.Register,
		trollbox.Register,
		user.Register,
	)

	// HTTP Server
	srv := &http.Server{
		Addr:         bm.Config().Listen.Addr,
		ReadTimeout:  5 * time.Second,
		WriteTimeout: 0 * time.Second,
		IdleTimeout:  60 * time.Second,
		Handler:      bm.HTTPHandler(),
	}

	operatorSvc := container.OperatorService
	// Goroutine to start the operator's reconciliation loop.
	g.Go(func() error {
		bm.Logger().Info("Starting operator service")
		operatorSvc.Start(gCtx)
		return nil
	})

	// Goroutine to gracefully stop the operator on application shutdown.
	g.Go(func() error {
		<-gCtx.Done()
		bm.Logger().Info("Shutting down operator service")
		operatorSvc.Stop()
		return nil
	})

	// CronJob Service
	cron, err := cronjob.New(bm.Config().Redis.Addr, bm.Logger())
	if err != nil {
		return err
	}
	bm.RegisterCronJobService(cron)

	g.Go(func() error {
		return cron.Run()
	})
	g.Go(func() error {
		<-gCtx.Done()
		bm.Logger().Info("Shutting down cronjob service")
		return cron.Close()
	})

	var regionIDs []string
	for _, rs := range bm.Config().BotRegionalServers {
		regionIDs = append(regionIDs, rs.RegionID)
	}

	g.Go(func() error {
		bm.Logger().Info("Starting HTTP server", zap.String("addr", srv.Addr))
		if err = srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			return err
		}
		return nil
	})
	g.Go(func() error {
		<-gCtx.Done()
		bm.Logger().Info("Shutting down HTTP server")
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		return srv.Shutdown(shutdownCtx)
	})

	bm.Logger().Info("Application running. Waiting for signal to exit.")
	if err = g.Wait(); err != nil && !errors.Is(err, context.Canceled) {
		bm.Logger().Error("Application exited with error", zap.Error(err))
		return err
	}

	bm.Logger().Info("Application exited gracefully")
	return nil
}
