package dependencies

import (
	"github.com/herenow/atomic-bm/atomic-br/botregionalclient"
	"github.com/herenow/atomic-bm/internal/app"
	"github.com/herenow/atomic-bm/internal/db/repo/accountrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/botrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/exchangerepo"
	"github.com/herenow/atomic-bm/internal/db/repo/paramrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/regionsrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/sessionrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/trollboxrepo"
	"github.com/herenow/atomic-bm/internal/db/repo/userrepo"
	"github.com/herenow/atomic-bm/internal/pkg/cache"
	"github.com/herenow/atomic-bm/internal/services/accountservice"
	"github.com/herenow/atomic-bm/internal/services/botsservice"
	"github.com/herenow/atomic-bm/internal/services/exchangeservice"
	"github.com/herenow/atomic-bm/internal/services/operatorservice"
	"github.com/herenow/atomic-bm/internal/services/paramsservice"
	"github.com/herenow/atomic-bm/internal/services/trollboxservice"
	"github.com/herenow/atomic-bm/internal/ws"
	"github.com/uptrace/bun"
	"go.uber.org/zap"
)

type Module func(app *app.App, dp *Container)

// Container holds all the application's dependencies.
// It is used to manage the lifecycle and wiring of repositories and services.
type Container struct {
	modules []Module

	// Repositories
	AccountRepo  accountrepo.IAccountsRepo
	UserRepo     userrepo.IUserRepository
	TrollboxRepo trollboxrepo.ITrollBoxRepository
	BotRepo      botrepo.IBots
	RegionRepo   regionsrepo.IRegionRepo
	ExchangeRepo exchangerepo.IExchange
	SessionRepo  sessionrepo.ISessionRepository
	ParamsRepo   paramrepo.IParams

	// gRPC Clients
	BotRegionalClient botregionalclient.IBotRegionalClient

	// Services
	AccountService  accountservice.IAccountService
	BotService      botsservice.IBotsService
	ExchangeService exchangeservice.IExchangeService
	ParamsService   paramsservice.IParamsService
	OperatorService operatorservice.IOperator

	// Cache
	Cache cache.ICache
}

// NewContainer creates and wires up all the application's dependencies.
func NewContainer(db *bun.DB, config *app.Config, wsClient *ws.WebsocketClient, logger *zap.Logger, cache cache.ICache) (*Container, error) {
	// =========================================================================
	// Build Repositories
	// =========================================================================

	accRepo := accountrepo.New(db)
	userRepo := userrepo.New(db)
	tbRepo := trollboxrepo.New(db)
	botRepo := botrepo.New(db)
	regionRepo := regionsrepo.New(db)
	exchRepo := exchangerepo.New(db)
	sessionRepo := sessionrepo.New(db)
	paramsRepo := paramrepo.New(db)

	// =========================================================================
	// Build gRPC Clients
	// =========================================================================

	brSvrs := make(map[string]string, len(config.BotRegionalServers))
	var regionIDs []string
	for _, server := range config.BotRegionalServers {
		brSvrs[server.RegionID] = server.BotRegionalAddr
		regionIDs = append(regionIDs, server.RegionID)
	}

	botRegionalCli, err := botregionalclient.New(brSvrs)
	if err != nil {
		return nil, err
	}

	// =========================================================================
	// Build Services
	// =========================================================================

	// WS Channels without instance
	trollboxservice.New(wsClient, tbRepo, logger)

	operatorSvc := operatorservice.New(operatorservice.Config{
		Logger:         logger,
		BotsRepo:       botRepo,
		Regions:        regionIDs,
		BotRegionalCli: botRegionalCli,
		WsClient:       wsClient,
	})
	paramSvc := paramsservice.New(botRepo, paramsRepo, accRepo, operatorSvc, config.ParamSecretKey, config.ParamSecretKeyVersion)
	operatorSvc.SetParamsService(paramSvc)

	botSvc := botsservice.New(accRepo, botRepo, operatorSvc, cache, logger, botRegionalCli, regionIDs)
	accSvc := accountservice.New(accRepo, exchRepo, regionRepo)
	exSvc := exchangeservice.New(exchRepo)

	// =========================================================================
	// Construct and Return Container
	// =========================================================================

	container := &Container{
		// Repositories
		AccountRepo:  accRepo,
		UserRepo:     userRepo,
		TrollboxRepo: tbRepo,
		BotRepo:      botRepo,
		RegionRepo:   regionRepo,
		ExchangeRepo: exchRepo,
		SessionRepo:  sessionRepo,
		ParamsRepo:   paramsRepo,

		// gRPC Clients
		BotRegionalClient: botRegionalCli,

		// Services
		AccountService:  accSvc,
		BotService:      botSvc,
		ExchangeService: exSvc,
		ParamsService:   paramSvc,
		OperatorService: operatorSvc,
	}

	return container, nil
}

func (c *Container) Start(app *app.App, modules ...Module) {
	for _, module := range modules {
		module(app, c)
	}
}
