# ====================================================================================
# PROJECT CONFIGURATION
# ====================================================================================

# Default version for local development.
# It is overridden by command-line (e.g., make ... VERSION=v1.2.3)
# or by automatic detection from the latest git tag for deployment targets.
VERSION ?= dev

# Automatically determines the release version from the latest git tag.
# This value is used by default for all deployment-related targets.
LATEST_GIT_TAG := $(shell git describe --tags --abbrev=0 2>/dev/null)

# Determines the final version to be used in builds and deployments.
# - If VERSION is passed via command line (e.g., make ... VERSION=v1.2.4), it uses that.
# - Otherwise, it falls back to the LATEST_GIT_TAG.
ifeq ($(VERSION),dev)
    VERSION_TO_USE := $(LATEST_GIT_TAG)
else
    VERSION_TO_USE := $(VERSION)
endif


# Application and Docker settings
APP_NAME := botmanager
DOCKER_REGISTRY ?= sjc.vultrcr.com/atomcr
DOCKER_IMAGE_PROD := $(DOCKER_REGISTRY)/$(APP_NAME)
DOCKER_IMAGE_DEV := $(APP_NAME):$(VERSION)
DOCKER_NETWORK := atomic-bm_default

# Go build parameters
# Use VERSION_TO_USE for release builds, but fall back to VERSION for local/dev builds.
BUILD_VERSION := $(or $(VERSION_TO_USE),$(VERSION))
BINARY_PATH := ./bin/$(APP_NAME)
CMD_PATH := ./cmd/app
LDFLAGS := -ldflags="-w -s -X main.Version=$(BUILD_VERSION)"

# Variables for local development
DOCKER_COMPOSE_FILE := docker-compose.yml
DATABASE_URL := postgres://postgres:postgres@localhost:5432/botmanager?sslmode=disable

# ====================================================================================
# HELP
# ====================================================================================

.PHONY: help
help: ## 💬 Show this help message
	@echo "Usage: make <target>"
	@echo ""
	@echo "Local Development:"
	@awk -F ':.*## ' '/^local-/ { printf "  \033[36m%-25s\033[0m %s\n", substr($$1, 7), $$2 }' $(MAKEFILE_LIST)
	@echo ""
	@echo "Build & Release:"
	@awk -F ':.*## ' '/^build-/ { printf "  \033[36m%-25s\033[0m %s\n", substr($$1, 7), $$2 }' $(MAKEFILE_LIST)
	@echo ""
	@echo "Deployment:"
	@awk -F ':.*## ' '/^deploy-/ { printf "  \033[36m%-25s\033[0m %s\n", substr($$1, 8), $$2 }' $(MAKEFILE_LIST)


# ====================================================================================
# LOCAL DEVELOPMENT WORKFLOW (DOCKER COMPOSE)
# ====================================================================================

.PHONY: local-start
local-start: local-db-migrate ## 🚀 Start all services (db, redis, app) in detached mode and follow logs
	@echo "🚀 Starting all services..."
	docker compose -f $(DOCKER_COMPOSE_FILE) up -d
	@$(MAKE) local-seed-users
	@echo "✅ All services are up and running."
	@$(MAKE) local-logs

.PHONY: local-bm
local-bm:
	@echo "🚀 Starting botmanager."
	docker compose -f $(DOCKER_COMPOSE_FILE) up botmanager

.PHONY: local-br
local-br:
	@echo "🚀 Starting bot regional service."
	docker compose -f $(DOCKER_COMPOSE_FILE) up -d botregional

.PHONY: local-stop
local-stop: ## 🛑 Stop all running services
	@echo "🛑 Stopping all services..."
	docker compose -f $(DOCKER_COMPOSE_FILE) stop

.PHONY: local-down
local-down: ## 🧹 Stop and remove all containers, networks, and volumes
	@echo "🧹 Tearing down the local environment..."
	docker compose -f $(DOCKER_COMPOSE_FILE) down -v --remove-orphans
	@echo "✅ Environment cleaned."

.PHONY: local-restart
local-restart: local-stop local-start ## 🔄 Restart all services

.PHONY: local-logs
local-logs: ## 📜 Follow logs for the main application container
	@echo "📜 Tailing logs for $(APP_NAME)..."
	docker compose -f $(DOCKER_COMPOSE_FILE) logs -f $(APP_NAME)

.PHONY: local-build-dev
local-build-dev: ## 🐳 Build the development Docker image for the app
	@echo "🐳 Building development image: $(DOCKER_IMAGE_DEV)..."
	docker compose -f $(DOCKER_COMPOSE_FILE) build $(APP_NAME)

# --- Database Migrations ---
.PHONY: local-db-setup
local-db-setup: ## 💾 Start only the database service and run migrations
	@echo "🚀 Starting database and redis services..."
	docker compose -f $(DOCKER_COMPOSE_FILE) up -d psql redis
	@echo "✅ Services started."
	@$(MAKE) local-db-migrate

.PHONY: local-db-migrate
local-db-migrate: ## 📈 Run database migrations up
	@echo "📈 Applying database migrations..."
	go run ./cmd/app migrate up

.PHONY: local-db-rollback
local-db-rollback: ## 📉 Roll back the last database migration
	@echo "📉 Rolling back database migration..."
	go run ./cmd/app migrate down

.PHONY: local-db-create-migration
local-db-create-migration: ## 📝 Create a new migration file (e.g., make local-db-create-migration name=add_new_table)
	@if [ -z "$(name)" ]; then echo "❌ 'name' is required. Usage: make local-db-create-migration name=your_migration_name"; exit 1; fi
	@echo "📝 Creating new migration: $(name)..."
	docker compose -f $(DOCKER_COMPOSE_FILE) run --rm $(APP_NAME) botmanager migrate create $(name)
	sudo chown -R $(shell id -u):$(shell id -g) internal/db/migrations
	@echo "✅ Migration file created. Don't forget to edit it."

.PHONY: local-db-reset
local-db-reset: local-db-rollback local-db-migrate ## 🔄 Reset the database by rolling back and re-applying migrations

# --- Seeding & Tools ---
.PHONY: local-seed-users
local-seed-users: ## 🌱 Create default users (admin and gabriel)
	@echo "🌱 Seeding database with initial users..."
	docker compose -f $(DOCKER_COMPOSE_FILE) run --rm $(APP_NAME) botmanager create-user --name=admin --password=admin123 --email=<EMAIL> --role=admin
	docker compose -f $(DOCKER_COMPOSE_FILE) run --rm $(APP_NAME) botmanager create-user --name=gabriel --password=123 --email=<EMAIL>

.PHONY: local-docs-preview
local-docs-preview: ## 👁️ Preview API documentation locally
	@echo "👁️ Starting documentation preview server..."
	redocly preview-docs ./docs/swagger.json

# ====================================================================================
# BUILD
# ====================================================================================

.PHONY: build-tidy
build-tidy: ## ✨ Run go mod tidy
	@echo "✨ Tidying go modules..."
	go mod tidy

.PHONY: build-pnpm-install
build-pnpm-install: ## 📦 Install pnpm dependencies for semantic-release
	@echo "📦 Installing pnpm dependencies..."
	pnpm install

.PHONY: build-binary
build-binary: build-tidy ## 🔨 Build the Go binary locally (not in Docker)
	@echo "🔨 Building binary for version $(BUILD_VERSION)..."
	@GOPRIVATE=github.com/herenow GO111MODULE=on CGO_ENABLED=0 go build $(LDFLAGS) -tags netgo -o $(BINARY_PATH) $(CMD_PATH)
	@echo "✅ Binary created at $(BINARY_PATH)"

.PHONY: build-docs-gen
build-docs-gen: ## 📄 Generate Swagger/OpenAPI documentation
	@echo "📄 Generating API documentation..."
	swag init -g ./internal/app/routes.go --requiredByDefault -ot go,json

# ====================================================================================
# RELEASE & DEPLOYMENT (NOMAD)
# ====================================================================================

.PHONY: deploy-login
deploy-login: ## 🔐 Log in to Vultr container registry (requires VULTR_USERNAME, VULTR_PASSWORD)
	@echo "🔐 Logging into Docker registry: $(DOCKER_REGISTRY)..."
	@if [ -z "$(VULTR_USERNAME)" ] || [ -z "$(VULTR_PASSWORD)" ]; then echo "❌ VULTR_USERNAME and VULTR_PASSWORD must be set."; exit 1; fi
	@docker login $(DOCKER_REGISTRY) --username $(VULTR_USERNAME) --password $(VULTR_PASSWORD)
	@echo "✅ Login successful."

.PHONY: deploy-build-release
deploy-build-release: build-pnpm-install ## 🎉 Create a new release using semantic-release (tags, changelog, GitHub release)
	@echo "🚀 Starting release process..."
	@if [ -z "$(GITHUB_TOKEN)" ]; then echo "❌ GITHUB_TOKEN environment variable is not set."; exit 1; fi
	pnpm semantic-release --no-ci -b master

.PHONY: deploy-docker-build
deploy-docker-build: ## 🐳 Build the final production Docker image (version is auto-detected from git tag)
	@if [ -z "$(VERSION_TO_USE)" ]; then echo "❌ Could not determine release version from git tag. Have you created a release yet?"; exit 1; fi
	@echo "🐳 Building production Docker image: $(DOCKER_IMAGE_PROD):$(VERSION_TO_USE)..."
	docker build --build-arg APP_VERSION=$(VERSION_TO_USE) -t $(DOCKER_IMAGE_PROD):$(VERSION_TO_USE) .
	docker tag $(DOCKER_IMAGE_PROD):$(VERSION_TO_USE) $(DOCKER_IMAGE_PROD):latest
	@echo "✅ Production image created."

.PHONY: deploy-docker-push
deploy-docker-push: ## ⬆️ Push the production Docker image to the registry.
	@if [ -z "$(VERSION_TO_USE)" ]; then echo "❌ Could not determine release version from git tag. Have you created a release yet?"; exit 1; fi
	@echo "⬆️ Pushing Docker image: $(DOCKER_IMAGE_PROD):$(VERSION_TO_USE)..."
	docker push $(DOCKER_IMAGE_PROD):$(VERSION_TO_USE)
	docker push $(DOCKER_IMAGE_PROD):latest
	@echo "✅ Image pushed to registry."

.PHONY: deploy-to-nomad
deploy-to-nomad: #deploy-docker-build deploy-docker-push ## 🚢 Build, push, and deploy the application to Nomad.
	@if [ -z "$(VERSION_TO_USE)" ]; then echo "❌ Could not determine release version from git tag. Have you created a release yet?"; exit 1; fi
	@echo "🚢 Deploying version $(VERSION_TO_USE) to Nomad..."
	@echo "📝 Updating Nomad job file with version $(VERSION_TO_USE)..."
	@sed -i.bak 's|image = "sjc.vultrcr.com/atomcr/botmanager.*"|image = "$(DOCKER_IMAGE_PROD):$(VERSION_TO_USE)"|g' deploy/bm.nomad.hcl && rm deploy/bm.nomad.hcl.bak
	@sed -i.bak 's|image = "sjc.vultrcr.com/atomcr/botmanager.*"|image = "$(DOCKER_IMAGE_PROD):$(VERSION_TO_USE)"|g' deploy/migrate.nomad.hcl && rm deploy/migrate.nomad.hcl.bak
	@echo "✅ Nomad job file is ready. Run the following command to deploy:"
	nomad job run deploy/bm.nomad.hcl

.PHONY: deploy-release
deploy-release: deploy-build-release ## 🚀🚀 Create a new release and deploy it to Nomad automatically
	@echo "🚀 Deploying the new release..."
	$(eval NEW_VERSION := $(shell git describe --tags --abbrev=0))
	@if [ -z "$(NEW_VERSION)" ]; then echo "❌ semantic-release finished, but no new git tag was found."; exit 1; fi
	@echo "--> New version detected: $(NEW_VERSION)"
	@$(MAKE) deploy-to-nomad VERSION=$(NEW_VERSION)

