# Atomic Protocols

Here, you'll find a collection of Protobuf (Protocol Buffers) schemas that define the data models for our services. We use Buf to streamline the management, generation, and linting of these schemas.

## About

* **What is Protobuf?** Protobuf is a language-neutral, platform-neutral, extensible mechanism for serializing structured data. It's designed to be smaller, faster, and simpler than XML.
* **Why Buf?** Buf is a modern toolkit for working with Protocol Buffers. It provides a powerful linter, a breaking change detector, and a schema registry to make Protobuf development easier and more reliable.

## Getting Started

1. **Prerequisites:**
    * **Buf CLI:** Make sure you have the Buf CLI installed. Refer to the [Buf installation guide](https://docs.buf.build/installation) for instructions.
    * **Go:** Ensure you have Go installed and set up correctly. Check the [Go installation guide](https://go.dev/doc/install) for details.

2. **Install protoc Plugins:**
   ```bash
    go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.34.2
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.4.0
   ```

3. **Use Makefile**
   * To generate the protobuf files.
   ```bash
    make gen-proto
   ```
