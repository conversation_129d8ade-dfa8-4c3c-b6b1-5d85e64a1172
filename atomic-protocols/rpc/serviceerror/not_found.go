package serviceerror

import (
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type (
	// NotFound represents not found error.
	NotFound struct {
		Message string
		st      *status.Status
	}
)

// NewNotFound returns new NotFound error.
func NewNotFound(message string) error {
	return &NotFound{
		Message: message,
	}
}

// Error returns string message.
func (e *NotFound) Error() string {
	return e.Message
}

func (e *NotFound) Status() *status.Status {
	if e.st != nil {
		return e.st
	}

	st := status.New(codes.NotFound, e.Message)
	return st
}

func newNotFound(st *status.Status) error {
	return &NotFound{
		Message: st.Message(),
		st:      st,
	}
}
