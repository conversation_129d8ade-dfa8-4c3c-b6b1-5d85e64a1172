package serviceerror

import (
	"context"
	"errors"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// ToStatus converts service error to gRPC Status.
// If an error is not a service error, it returns status with code Unknown.
func ToStatus(err error) *status.Status {
	if err == nil {
		return status.New(codes.OK, "")
	}

	if svcerr, ok := err.(ServiceError); ok {
		return svcerr.Status()
	}
	// err does not implement ServiceError directly, but check if it wraps it.
	// This path does more allocation so prefer to return a ServiceError directly if possible.
	var svcerr ServiceError
	if errors.As(err, &svcerr) {
		s := svcerr.Status().Proto()
		s.Message = err.Error() // don't lose the wrapped message
		return status.FromProto(s)
	}

	// Special case for context.DeadlineExceeded and context.Canceled because they can happen in unpredictable places.
	if errors.Is(err, context.DeadlineExceeded) {
		return status.New(codes.DeadlineExceeded, err.Error())
	}
	if errors.Is(err, context.Canceled) {
		return status.New(codes.Canceled, err.Error())
	}

	// Internal logic of status.Convert is:
	//   - if err is already Status or gRPC Status, then just return it (this should never happen though).
	//   - otherwise returns codes.Unknown with message from err.Error() (this might happen if some generic go error reach to this point).
	return status.Convert(err)
}

// FromStatus converts gRPC Status to service error.
func FromStatus(st *status.Status) error {
	if st == nil || st.Code() == codes.OK {
		return nil
	}

	errDetails := extractErrorDetails(st)

	// If there was an error during details extraction, for example, unknown message type,
	// which can happen when new error details are added and getting read by old clients,
	// then errDetails will be of type `error` with corresponding error inside.
	// This error is ignored and `serviceerror` is built using `st.Code()` only.
	switch st.Code() {
	case codes.DeadlineExceeded:
		return newDeadlineExceeded(st)
	case codes.Canceled:
		return newCanceled(st)
	case codes.Unavailable:
		return newUnavailable(st)
	case codes.Unimplemented:
		return newUnimplemented(st)
	case codes.Unknown:
		// Unwrap error message from unknown error.
		return errors.New(st.Message())
	case codes.NotFound:
		return newNotFound(st)
	case codes.InvalidArgument:
		switch errDetails.(type) {
		default:
			return newInvalidArgument(st)
		}
	case codes.AlreadyExists:
		return newAlreadyExists(st)
	case codes.FailedPrecondition:
		return newFailedPrecondition(st)
	case codes.PermissionDenied:
		//switch errDetails := errDetails.(type) {
		//case *errordetails.PermissionDeniedFailure:
		//	return newPermissionDenied(st, errDetails)
		//default:
		//	return newPermissionDenied(st, nil)
		//}
	case codes.Unauthenticated:
		// fall through to st.Err()
	}

	// `st.Code()` has unknown value (should never happen).
	// Use standard gRPC error representation "rpc error: code = %s desc = %s".
	return st.Err()
}

func extractErrorDetails(st *status.Status) interface{} {
	details := st.Details()
	if len(details) > 0 {
		return details[0]
	}

	return nil
}
