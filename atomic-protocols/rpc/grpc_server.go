package rpc

import (
	"context"
	"crypto/tls"
	"runtime"

	"github.com/herenow/atomic-protocols/rpc/serviceerror"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

func NewServer(tlsConfig *tls.Config,
	unaryInterceptors []grpc.UnaryServerInterceptor,
	streamInterceptors []grpc.StreamServerInterceptor,
	serverOpts ...grpc.ServerOption) *grpc.Server {
	var grpcSecureOpt credentials.TransportCredentials
	if tlsConfig == nil {
		grpcSecureOpt = insecure.NewCredentials()
	} else {
		grpcSecureOpt = credentials.NewTLS(tlsConfig)
	}

	defaultServerOpts := []grpc.ServerOption{
		// Creds sets credentials for server connections.
		grpc.Creds(grpcSecureOpt),
		// The NumStreamWorkers sets the number of goroutines for processing incoming streams in a gRPC server.
		grpc.NumStreamWorkers(uint32(runtime.NumCPU())),
		// SharedWriteBuffer allows reusing per-connection transport write buffer.
		grpc.SharedWriteBuffer(true),
		// MaxConcurrentStreams will apply a limit on the number of concurrent streams to each ServerTransport.
		grpc.MaxConcurrentStreams(defaultMaxConcurrentStreams),
		// ConnectionTimeout is the connection time out for HTTP/2 handshaking.
		grpc.ConnectionTimeout(defaultConnectionTimeout),
		// MaxRecvMsgSize set the max message size in bytes the server can receive.
		grpc.MaxRecvMsgSize(defaultMaxReceiveMessageSize),
		// MaxSendMsgSize set the max message size in bytes the server can send.
		grpc.MaxSendMsgSize(defaultMaxSendMessageSize),
		// WriteBufferSize determines how much data can be batched before doing a writing on the wire
		grpc.WriteBufferSize(defaultWriteBufSize),
		// ReadBufferSize lets you set the size of read buffer, this determines how much
		// data can be read at most for one read syscall
		grpc.ReadBufferSize(defaultReadBufSize),
		// KeepaliveParams sets keepalive and max-age parameters
		grpc.KeepaliveParams(kasp),
		// KeepaliveEnforcementPolicy sets keepalive enforcement policy
		grpc.KeepaliveEnforcementPolicy(kaep),
		// ChainUnaryInterceptor set up unary middlewares where the first interceptor will be the outermost,
		// while the last interceptor will be the innermost wrapper around the real call.
		grpc.ChainUnaryInterceptor(
			append(
				unaryInterceptors,
				serverErrorInterceptor,
			)...,
		),
		// ChainStreamInterceptor set up stream middlewares where the first interceptor will be the outermost,
		// while the last interceptor will be the innermost wrapper around the real call.
		grpc.ChainStreamInterceptor(
			append(
				streamInterceptors,
			)...,
		),
	}

	// Override default options with user defined
	for _, opt := range serverOpts {
		defaultServerOpts = append(defaultServerOpts, opt)
	}

	return grpc.NewServer(defaultServerOpts...)
}

func serverErrorInterceptor(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (any, error) {
	res, err := handler(ctx, req)
	if err != nil {
		err = serviceerror.FromStatus(status.Convert(err))
		return nil, err
	}
	return res, nil
}
