package interceptor

import (
	"context"
	"io"

	"github.com/herenow/atomic-protocols/rpc/serviceerror"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type (
	ClientStreamErrorInterceptor struct {
		grpc.ClientStream
	}
)

var _ grpc.ClientStream = (*ClientStreamErrorInterceptor)(nil)

func NewClientStreamErrorInterceptor(
	clientStream grpc.ClientStream,
) *ClientStreamErrorInterceptor {
	return &ClientStreamErrorInterceptor{
		ClientStream: clientStream,
	}
}

func (c *ClientStreamErrorInterceptor) CloseSend() error {
	return errorConvert(c.ClientStream.CloseSend())
}

func (c *ClientStreamErrorInterceptor) SendMsg(m interface{}) error {
	return errorConvert(c.ClientStream.SendMsg(m))
}

func (c *ClientStreamErrorInterceptor) RecvMsg(m interface{}) error {
	return errorConvert(c.ClientStream.RecvMsg(m))
}

func StreamClientErrorInterceptor(
	ctx context.Context,
	desc *grpc.StreamDesc,
	cc *grpc.ClientConn,
	method string,
	streamer grpc.Streamer,
	opts ...grpc.CallOption,
) (grpc.ClientStream, error) {
	clientStream, err := streamer(ctx, desc, cc, method, opts...)
	if err != nil {
		return nil, errorConvert(err)
	}
	return NewClientStreamErrorInterceptor(clientStream), nil
}

func errorConvert(err error) error {
	switch err {
	case nil:
		return nil
	case io.EOF:
		return io.EOF
	default:
		return FromStatus(status.Convert(err))
	}
}

// FromStatus converts gRPC Status to service error.
func FromStatus(st *status.Status) error {
	if st == nil {
		return nil
	}

	switch st.Code() {
	case codes.OK:
		return nil
	case codes.DeadlineExceeded:
		return serviceerror.NewDeadlineExceeded(st.Message())
	case codes.Canceled:
		return serviceerror.NewCanceled(st.Message())
	case codes.InvalidArgument:
		return serviceerror.NewInvalidArgument(st.Message())
	case codes.FailedPrecondition:
		return serviceerror.NewFailedPrecondition(st.Message())
	case codes.Unavailable:
		return serviceerror.NewUnavailable(st.Message())
	case codes.Internal:
		return serviceerror.NewInternal(st.Message())
	case codes.Unknown:
		return serviceerror.NewInternal(st.Message())
	default:
		return serviceerror.NewInternal(st.Message())
	}
}
