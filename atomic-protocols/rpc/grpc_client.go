package rpc

import (
	"context"
	"crypto/tls"

	"github.com/herenow/atomic-protocols/rpc/interceptor"
	"github.com/herenow/atomic-protocols/rpc/serviceerror"
	"google.golang.org/grpc"
	"google.golang.org/grpc/backoff"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

// NewClient creates a client connection to the given target with default options.
func NewClient(hostName string,
	tlsConfig *tls.Config,
	streamInterceptor []grpc.StreamClientInterceptor,
	unaryInterceptors []grpc.UnaryClientInterceptor,
	clientOpts ...grpc.DialOption) (*grpc.ClientConn, error) {
	var creds credentials.TransportCredentials
	if tlsConfig == nil {
		creds = insecure.NewCredentials()
	} else {
		creds = credentials.NewTLS(tlsConfig)
	}

	// gRPC maintains connection pool inside grpc.ClientConn.
	// This connection pool has auto reconnect feature.
	var cp = grpc.ConnectParams{
		Backoff:           backoff.DefaultConfig,
		MinConnectTimeout: defaultConnectionTimeout,
	}
	cp.Backoff.MaxDelay = MaxBackoffDelay

	dialOptions := []grpc.DialOption{
		grpc.WithDefaultCallOptions(
			grpc.MaxCallRecvMsgSize(defaultMaxReceiveMessageSize),
			grpc.MaxCallSendMsgSize(defaultMaxSendMessageSize),
		),
		grpc.WithChainUnaryInterceptor(
			append(
				unaryInterceptors,
				clientErrorInterceptor,
			)...,
		),
		grpc.WithChainStreamInterceptor(
			append(
				streamInterceptor,
				interceptor.StreamClientErrorInterceptor,
			)...,
		),
		grpc.WithDefaultServiceConfig(defaultServiceConfig),
		grpc.WithDisableServiceConfig(),
		grpc.WithConnectParams(cp),
		grpc.WithKeepaliveParams(kacp),
		grpc.WithTransportCredentials(creds),
	}

	// override client opts
	dialOptions = append(dialOptions, clientOpts...)

	return grpc.NewClient(hostName, dialOptions...)
}

func clientErrorInterceptor(
	ctx context.Context,
	method string,
	req, reply interface{},
	cc *grpc.ClientConn,
	invoker grpc.UnaryInvoker,
	opts ...grpc.CallOption,
) error {
	return serviceerror.FromStatus(status.Convert(invoker(ctx, method, req, reply, cc, opts...)))
}
