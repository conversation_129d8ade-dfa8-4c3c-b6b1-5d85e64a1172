package rpc

import (
	"math"
	"time"

	"google.golang.org/grpc/keepalive"
)

const (

	// MaxBackoffDelay is a maximum interval between reconnected attempts.
	MaxBackoffDelay = 10 * time.Second
)

const (
	// defaultServiceConfig is a default gRPC connection service config that enables DNS round-robin between IPs.
	// To use DNS resolver, a "dns:///" prefix should be applied to the hostPort.
	// https://github.com/grpc/grpc/blob/master/doc/naming.md
	defaultServiceConfig = `{"loadBalancingConfig": [{"round_robin":{}}]}`

	// defaultMaxReceiveMessageSize is the default maximum size of received messages.
	defaultMaxReceiveMessageSize = 128 * 1024 * 1024 // 128MB

	// defaultMaxSendMessageSize is the default maximum size of sent messages.
	defaultMaxSendMessageSize = math.MaxInt32 // 2GB

	// defaultMaxConcurrentStreams is the default maximum number of concurrent streams.
	defaultMaxConcurrentStreams = math.MaxUint32 // 4,294,967,295 streams

	// defaultConnectionTimeout is the default connection timeout duration.
	defaultConnectionTimeout = 20 * time.Second // 20 seconds

	// defaultWriteBufSize is the default size of the write buffer.
	defaultWriteBufSize = 32 * 1024 // 32KB

	// defaultReadBufSize is the default size of the read buffer.
	defaultReadBufSize = 32 * 1024 // 32KB
)

var kasp = keepalive.ServerParameters{
	MaxConnectionIdle:     15 * time.Second, // If a client is idle for 15 seconds, send a GOAWAY
	MaxConnectionAgeGrace: 5 * time.Second,  // Allow 5 seconds for pending RPCs to complete before forcibly closing connections
	Time:                  5 * time.Second,  // Ping the client if it is idle for 5 seconds to ensure the connection is still active
	Timeout:               1 * time.Second,  // Wait 1 second for the ping ack before assuming the connection is dead
}

var kaep = keepalive.EnforcementPolicy{
	MinTime:             5 * time.Second, // If a client pings more than once every 5 seconds, terminate the connection
	PermitWithoutStream: true,            // Allow pings even when there are no active streams
}

var kacp = keepalive.ClientParameters{
	Time:                10 * time.Second, // Send pings every 10 seconds if there is no activity
	Timeout:             1 * time.Second,  // Wait 1 second for ping ack before considering the connection is dead
	PermitWithoutStream: true,             // Send pings even without active streams
}
