// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: atomic/api/enums/v1/bot.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// BotType is the bot type (e.g., "market maker", "arbitrage", "trend follower").
type BotType int32

const (
	BotType_BOT_TYPE_UNSPECIFIED  BotType = 0
	BotType_BOT_TYPE_MARKET_MAKER BotType = 1
)

// Enum value maps for BotType.
var (
	BotType_name = map[int32]string{
		0: "BOT_TYPE_UNSPECIFIED",
		1: "BOT_TYPE_MARKET_MAKER",
	}
	BotType_value = map[string]int32{
		"BOT_TYPE_UNSPECIFIED":  0,
		"BOT_TYPE_MARKET_MAKER": 1,
	}
)

func (x BotType) Enum() *BotType {
	p := new(BotType)
	*p = x
	return p
}

func (x BotType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BotType) Descriptor() protoreflect.EnumDescriptor {
	return file_atomic_api_enums_v1_bot_proto_enumTypes[0].Descriptor()
}

func (BotType) Type() protoreflect.EnumType {
	return &file_atomic_api_enums_v1_bot_proto_enumTypes[0]
}

func (x BotType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BotType.Descriptor instead.
func (BotType) EnumDescriptor() ([]byte, []int) {
	return file_atomic_api_enums_v1_bot_proto_rawDescGZIP(), []int{0}
}

// BotStatus is the bot current status (e.g., "starting", "running"...).
type BotStatus int32

const (
	BotStatus_BOT_STATUS_UNSPECIFIED BotStatus = 0
	BotStatus_BOT_STATUS_STARTING    BotStatus = 1
	BotStatus_BOT_STATUS_RUNNING     BotStatus = 2
	BotStatus_BOT_STATUS_STOPPING    BotStatus = 3
	BotStatus_BOT_STATUS_STOPPED     BotStatus = 4
	BotStatus_BOT_STATUS_ERROR       BotStatus = 5
)

// Enum value maps for BotStatus.
var (
	BotStatus_name = map[int32]string{
		0: "BOT_STATUS_UNSPECIFIED",
		1: "BOT_STATUS_STARTING",
		2: "BOT_STATUS_RUNNING",
		3: "BOT_STATUS_STOPPING",
		4: "BOT_STATUS_STOPPED",
		5: "BOT_STATUS_ERROR",
	}
	BotStatus_value = map[string]int32{
		"BOT_STATUS_UNSPECIFIED": 0,
		"BOT_STATUS_STARTING":    1,
		"BOT_STATUS_RUNNING":     2,
		"BOT_STATUS_STOPPING":    3,
		"BOT_STATUS_STOPPED":     4,
		"BOT_STATUS_ERROR":       5,
	}
)

func (x BotStatus) Enum() *BotStatus {
	p := new(BotStatus)
	*p = x
	return p
}

func (x BotStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BotStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_atomic_api_enums_v1_bot_proto_enumTypes[1].Descriptor()
}

func (BotStatus) Type() protoreflect.EnumType {
	return &file_atomic_api_enums_v1_bot_proto_enumTypes[1]
}

func (x BotStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BotStatus.Descriptor instead.
func (BotStatus) EnumDescriptor() ([]byte, []int) {
	return file_atomic_api_enums_v1_bot_proto_rawDescGZIP(), []int{1}
}

var File_atomic_api_enums_v1_bot_proto protoreflect.FileDescriptor

var file_atomic_api_enums_v1_bot_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x13, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2a, 0x3e, 0x0a, 0x07, 0x42, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x14, 0x42, 0x4f, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x42, 0x4f, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x5f, 0x4d, 0x41, 0x4b,
	0x45, 0x52, 0x10, 0x01, 0x2a, 0x9f, 0x01, 0x0a, 0x09, 0x42, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x42, 0x4f, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17,
	0x0a, 0x13, 0x42, 0x4f, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x41,
	0x52, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x4f, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12,
	0x17, 0x0a, 0x13, 0x42, 0x4f, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x54,
	0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x4f, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x14, 0x0a, 0x10, 0x42, 0x4f, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x05, 0x42, 0x43, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x68, 0x65, 0x72, 0x65, 0x6e, 0x6f, 0x77, 0x2f, 0x61, 0x74, 0x6f,
	0x6d, 0x69, 0x63, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x73, 0x2f, 0x67, 0x65,
	0x6e, 0x2f, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_atomic_api_enums_v1_bot_proto_rawDescOnce sync.Once
	file_atomic_api_enums_v1_bot_proto_rawDescData = file_atomic_api_enums_v1_bot_proto_rawDesc
)

func file_atomic_api_enums_v1_bot_proto_rawDescGZIP() []byte {
	file_atomic_api_enums_v1_bot_proto_rawDescOnce.Do(func() {
		file_atomic_api_enums_v1_bot_proto_rawDescData = protoimpl.X.CompressGZIP(file_atomic_api_enums_v1_bot_proto_rawDescData)
	})
	return file_atomic_api_enums_v1_bot_proto_rawDescData
}

var file_atomic_api_enums_v1_bot_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_atomic_api_enums_v1_bot_proto_goTypes = []any{
	(BotType)(0),   // 0: atomic.api.enums.v1.BotType
	(BotStatus)(0), // 1: atomic.api.enums.v1.BotStatus
}
var file_atomic_api_enums_v1_bot_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_atomic_api_enums_v1_bot_proto_init() }
func file_atomic_api_enums_v1_bot_proto_init() {
	if File_atomic_api_enums_v1_bot_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_atomic_api_enums_v1_bot_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_atomic_api_enums_v1_bot_proto_goTypes,
		DependencyIndexes: file_atomic_api_enums_v1_bot_proto_depIdxs,
		EnumInfos:         file_atomic_api_enums_v1_bot_proto_enumTypes,
	}.Build()
	File_atomic_api_enums_v1_bot_proto = out.File
	file_atomic_api_enums_v1_bot_proto_rawDesc = nil
	file_atomic_api_enums_v1_bot_proto_goTypes = nil
	file_atomic_api_enums_v1_bot_proto_depIdxs = nil
}
