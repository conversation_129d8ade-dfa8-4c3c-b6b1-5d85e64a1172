// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: atomic/api/enums/v1/order.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// OrderType defines the type of order being placed or executed.
type OrderType int32

const (
	OrderType_ORDER_TYPE_UNSPECIFIED OrderType = 0
	OrderType_ORDER_TYPE_LIMIT       OrderType = 1
	OrderType_ORDER_TYPE_MARKET      OrderType = 2
	OrderType_ORDER_TYPE_UNKNOWN     OrderType = 3
)

// Enum value maps for OrderType.
var (
	OrderType_name = map[int32]string{
		0: "ORDER_TYPE_UNSPECIFIED",
		1: "ORDER_TYPE_LIMIT",
		2: "ORDER_TYPE_MARKET",
		3: "ORDER_TYPE_UNKNOWN",
	}
	OrderType_value = map[string]int32{
		"ORDER_TYPE_UNSPECIFIED": 0,
		"ORDER_TYPE_LIMIT":       1,
		"ORDER_TYPE_MARKET":      2,
		"ORDER_TYPE_UNKNOWN":     3,
	}
)

func (x OrderType) Enum() *OrderType {
	p := new(OrderType)
	*p = x
	return p
}

func (x OrderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderType) Descriptor() protoreflect.EnumDescriptor {
	return file_atomic_api_enums_v1_order_proto_enumTypes[0].Descriptor()
}

func (OrderType) Type() protoreflect.EnumType {
	return &file_atomic_api_enums_v1_order_proto_enumTypes[0]
}

func (x OrderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderType.Descriptor instead.
func (OrderType) EnumDescriptor() ([]byte, []int) {
	return file_atomic_api_enums_v1_order_proto_rawDescGZIP(), []int{0}
}

// OrderSide indicates whether the order is to buy or sell an asset.
type OrderSide int32

const (
	OrderSide_ORDER_SIDE_UNSPECIFIED OrderSide = 0
	OrderSide_ORDER_SIDE_BUY         OrderSide = 1
	OrderSide_ORDER_SIDE_SELL        OrderSide = 2
	OrderSide_ORDER_SIDE_UNKNOWN     OrderSide = 3
)

// Enum value maps for OrderSide.
var (
	OrderSide_name = map[int32]string{
		0: "ORDER_SIDE_UNSPECIFIED",
		1: "ORDER_SIDE_BUY",
		2: "ORDER_SIDE_SELL",
		3: "ORDER_SIDE_UNKNOWN",
	}
	OrderSide_value = map[string]int32{
		"ORDER_SIDE_UNSPECIFIED": 0,
		"ORDER_SIDE_BUY":         1,
		"ORDER_SIDE_SELL":        2,
		"ORDER_SIDE_UNKNOWN":     3,
	}
)

func (x OrderSide) Enum() *OrderSide {
	p := new(OrderSide)
	*p = x
	return p
}

func (x OrderSide) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderSide) Descriptor() protoreflect.EnumDescriptor {
	return file_atomic_api_enums_v1_order_proto_enumTypes[1].Descriptor()
}

func (OrderSide) Type() protoreflect.EnumType {
	return &file_atomic_api_enums_v1_order_proto_enumTypes[1]
}

func (x OrderSide) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderSide.Descriptor instead.
func (OrderSide) EnumDescriptor() ([]byte, []int) {
	return file_atomic_api_enums_v1_order_proto_rawDescGZIP(), []int{1}
}

// OrderState is the current state of an order in its lifecycle.
type OrderState int32

const (
	OrderState_ORDER_STATE_UNSPECIFIED OrderState = 0
	OrderState_ORDER_STATE_SENT        OrderState = 1
	OrderState_ORDER_STATE_OPEN        OrderState = 2
	OrderState_ORDER_STATE_PARTIALLY   OrderState = 3
	OrderState_ORDER_STATE_FILLED      OrderState = 4
	OrderState_ORDER_STATE_CLOSED      OrderState = 5
	OrderState_ORDER_STATE_CANCELED    OrderState = 6
	OrderState_ORDER_STATE_UNKNOWN     OrderState = 7
)

// Enum value maps for OrderState.
var (
	OrderState_name = map[int32]string{
		0: "ORDER_STATE_UNSPECIFIED",
		1: "ORDER_STATE_SENT",
		2: "ORDER_STATE_OPEN",
		3: "ORDER_STATE_PARTIALLY",
		4: "ORDER_STATE_FILLED",
		5: "ORDER_STATE_CLOSED",
		6: "ORDER_STATE_CANCELED",
		7: "ORDER_STATE_UNKNOWN",
	}
	OrderState_value = map[string]int32{
		"ORDER_STATE_UNSPECIFIED": 0,
		"ORDER_STATE_SENT":        1,
		"ORDER_STATE_OPEN":        2,
		"ORDER_STATE_PARTIALLY":   3,
		"ORDER_STATE_FILLED":      4,
		"ORDER_STATE_CLOSED":      5,
		"ORDER_STATE_CANCELED":    6,
		"ORDER_STATE_UNKNOWN":     7,
	}
)

func (x OrderState) Enum() *OrderState {
	p := new(OrderState)
	*p = x
	return p
}

func (x OrderState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderState) Descriptor() protoreflect.EnumDescriptor {
	return file_atomic_api_enums_v1_order_proto_enumTypes[2].Descriptor()
}

func (OrderState) Type() protoreflect.EnumType {
	return &file_atomic_api_enums_v1_order_proto_enumTypes[2]
}

func (x OrderState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderState.Descriptor instead.
func (OrderState) EnumDescriptor() ([]byte, []int) {
	return file_atomic_api_enums_v1_order_proto_rawDescGZIP(), []int{2}
}

var File_atomic_api_enums_v1_order_proto protoreflect.FileDescriptor

var file_atomic_api_enums_v1_order_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x13, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2a, 0x6c, 0x0a, 0x09, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x14, 0x0a, 0x10, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49,
	0x4d, 0x49, 0x54, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x03, 0x2a, 0x68, 0x0a, 0x09, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x69, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x49, 0x44, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a,
	0x0e, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x49, 0x44, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x10,
	0x01, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x49, 0x44, 0x45, 0x5f,
	0x53, 0x45, 0x4c, 0x4c, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x53, 0x49, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x03, 0x2a, 0xd3,
	0x01, 0x0a, 0x0a, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a,
	0x17, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x01,
	0x12, 0x14, 0x0a, 0x10, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x4f, 0x50, 0x45, 0x4e, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x4c, 0x59, 0x10,
	0x03, 0x12, 0x16, 0x0a, 0x12, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x5f, 0x46, 0x49, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10,
	0x05, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x12, 0x17, 0x0a, 0x13, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x07, 0x42, 0x43, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x68, 0x65, 0x72, 0x65, 0x6e, 0x6f, 0x77, 0x2f, 0x61, 0x74, 0x6f, 0x6d, 0x69,
	0x63, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x73, 0x2f, 0x67, 0x65, 0x6e, 0x2f,
	0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_atomic_api_enums_v1_order_proto_rawDescOnce sync.Once
	file_atomic_api_enums_v1_order_proto_rawDescData = file_atomic_api_enums_v1_order_proto_rawDesc
)

func file_atomic_api_enums_v1_order_proto_rawDescGZIP() []byte {
	file_atomic_api_enums_v1_order_proto_rawDescOnce.Do(func() {
		file_atomic_api_enums_v1_order_proto_rawDescData = protoimpl.X.CompressGZIP(file_atomic_api_enums_v1_order_proto_rawDescData)
	})
	return file_atomic_api_enums_v1_order_proto_rawDescData
}

var file_atomic_api_enums_v1_order_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_atomic_api_enums_v1_order_proto_goTypes = []any{
	(OrderType)(0),  // 0: atomic.api.enums.v1.OrderType
	(OrderSide)(0),  // 1: atomic.api.enums.v1.OrderSide
	(OrderState)(0), // 2: atomic.api.enums.v1.OrderState
}
var file_atomic_api_enums_v1_order_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_atomic_api_enums_v1_order_proto_init() }
func file_atomic_api_enums_v1_order_proto_init() {
	if File_atomic_api_enums_v1_order_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_atomic_api_enums_v1_order_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_atomic_api_enums_v1_order_proto_goTypes,
		DependencyIndexes: file_atomic_api_enums_v1_order_proto_depIdxs,
		EnumInfos:         file_atomic_api_enums_v1_order_proto_enumTypes,
	}.Build()
	File_atomic_api_enums_v1_order_proto = out.File
	file_atomic_api_enums_v1_order_proto_rawDesc = nil
	file_atomic_api_enums_v1_order_proto_goTypes = nil
	file_atomic_api_enums_v1_order_proto_depIdxs = nil
}
