// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: atomic/api/proto/v1/types.proto

package proto

import (
	v1 "github.com/herenow/atomic-protocols/gen/atomic/api/enums/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Bot is basic bot information
type Bot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string           `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ExchangeId     string           `protobuf:"bytes,2,opt,name=exchange_id,json=exchangeId,proto3" json:"exchange_id,omitempty"`
	AccountId      string           `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	Symbol         string           `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Status         v1.BotStatus     `protobuf:"varint,5,opt,name=status,proto3,enum=atomic.api.enums.v1.BotStatus" json:"status,omitempty"`
	StartOnCreate  bool             `protobuf:"varint,6,opt,name=start_on_create,json=startOnCreate,proto3" json:"start_on_create,omitempty"`
	LastError      string           `protobuf:"bytes,7,opt,name=last_error,json=lastError,proto3" json:"last_error,omitempty"`
	Type           v1.BotType       `protobuf:"varint,8,opt,name=type,proto3,enum=atomic.api.enums.v1.BotType" json:"type,omitempty"`
	GatewayOptions *structpb.Struct `protobuf:"bytes,9,opt,name=gateway_options,json=gatewayOptions,proto3" json:"gateway_options,omitempty"`
	Params         *structpb.Struct `protobuf:"bytes,10,opt,name=params,proto3" json:"params,omitempty"`
}

func (x *Bot) Reset() {
	*x = Bot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_proto_v1_types_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bot) ProtoMessage() {}

func (x *Bot) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_proto_v1_types_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bot.ProtoReflect.Descriptor instead.
func (*Bot) Descriptor() ([]byte, []int) {
	return file_atomic_api_proto_v1_types_proto_rawDescGZIP(), []int{0}
}

func (x *Bot) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Bot) GetExchangeId() string {
	if x != nil {
		return x.ExchangeId
	}
	return ""
}

func (x *Bot) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Bot) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Bot) GetStatus() v1.BotStatus {
	if x != nil {
		return x.Status
	}
	return v1.BotStatus(0)
}

func (x *Bot) GetStartOnCreate() bool {
	if x != nil {
		return x.StartOnCreate
	}
	return false
}

func (x *Bot) GetLastError() string {
	if x != nil {
		return x.LastError
	}
	return ""
}

func (x *Bot) GetType() v1.BotType {
	if x != nil {
		return x.Type
	}
	return v1.BotType(0)
}

func (x *Bot) GetGatewayOptions() *structpb.Struct {
	if x != nil {
		return x.GatewayOptions
	}
	return nil
}

func (x *Bot) GetParams() *structpb.Struct {
	if x != nil {
		return x.Params
	}
	return nil
}

// State represents a comprehensive snapshot of a bot's current trading state,
// encompassing its open orders, order book, inventory, market depth, and timestamp.
type State struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BotId  string                 `protobuf:"bytes,1,opt,name=bot_id,json=botId,proto3" json:"bot_id,omitempty"`
	Data   *structpb.Struct       `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Params *structpb.Struct       `protobuf:"bytes,3,opt,name=params,proto3" json:"params,omitempty"`
	Time   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=time,proto3" json:"time,omitempty"`
}

func (x *State) Reset() {
	*x = State{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_proto_v1_types_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*State) ProtoMessage() {}

func (x *State) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_proto_v1_types_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use State.ProtoReflect.Descriptor instead.
func (*State) Descriptor() ([]byte, []int) {
	return file_atomic_api_proto_v1_types_proto_rawDescGZIP(), []int{1}
}

func (x *State) GetBotId() string {
	if x != nil {
		return x.BotId
	}
	return ""
}

func (x *State) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *State) GetParams() *structpb.Struct {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *State) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

var File_atomic_api_proto_v1_types_proto protoreflect.FileDescriptor

var file_atomic_api_proto_v1_types_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x13, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x1d, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x03, 0x0a, 0x03, 0x42, 0x6f, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4f, 0x6e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x0f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2f, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xac, 0x01, 0x0a, 0x05, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2f, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x43, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x68, 0x65, 0x72, 0x65, 0x6e, 0x6f, 0x77, 0x2f, 0x61,
	0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x73, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_atomic_api_proto_v1_types_proto_rawDescOnce sync.Once
	file_atomic_api_proto_v1_types_proto_rawDescData = file_atomic_api_proto_v1_types_proto_rawDesc
)

func file_atomic_api_proto_v1_types_proto_rawDescGZIP() []byte {
	file_atomic_api_proto_v1_types_proto_rawDescOnce.Do(func() {
		file_atomic_api_proto_v1_types_proto_rawDescData = protoimpl.X.CompressGZIP(file_atomic_api_proto_v1_types_proto_rawDescData)
	})
	return file_atomic_api_proto_v1_types_proto_rawDescData
}

var file_atomic_api_proto_v1_types_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_atomic_api_proto_v1_types_proto_goTypes = []any{
	(*Bot)(nil),                   // 0: atomic.api.proto.v1.Bot
	(*State)(nil),                 // 1: atomic.api.proto.v1.State
	(v1.BotStatus)(0),             // 2: atomic.api.enums.v1.BotStatus
	(v1.BotType)(0),               // 3: atomic.api.enums.v1.BotType
	(*structpb.Struct)(nil),       // 4: google.protobuf.Struct
	(*timestamppb.Timestamp)(nil), // 5: google.protobuf.Timestamp
}
var file_atomic_api_proto_v1_types_proto_depIdxs = []int32{
	2, // 0: atomic.api.proto.v1.Bot.status:type_name -> atomic.api.enums.v1.BotStatus
	3, // 1: atomic.api.proto.v1.Bot.type:type_name -> atomic.api.enums.v1.BotType
	4, // 2: atomic.api.proto.v1.Bot.gateway_options:type_name -> google.protobuf.Struct
	4, // 3: atomic.api.proto.v1.Bot.params:type_name -> google.protobuf.Struct
	4, // 4: atomic.api.proto.v1.State.data:type_name -> google.protobuf.Struct
	4, // 5: atomic.api.proto.v1.State.params:type_name -> google.protobuf.Struct
	5, // 6: atomic.api.proto.v1.State.time:type_name -> google.protobuf.Timestamp
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_atomic_api_proto_v1_types_proto_init() }
func file_atomic_api_proto_v1_types_proto_init() {
	if File_atomic_api_proto_v1_types_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_atomic_api_proto_v1_types_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Bot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_proto_v1_types_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*State); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_atomic_api_proto_v1_types_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_atomic_api_proto_v1_types_proto_goTypes,
		DependencyIndexes: file_atomic_api_proto_v1_types_proto_depIdxs,
		MessageInfos:      file_atomic_api_proto_v1_types_proto_msgTypes,
	}.Build()
	File_atomic_api_proto_v1_types_proto = out.File
	file_atomic_api_proto_v1_types_proto_rawDesc = nil
	file_atomic_api_proto_v1_types_proto_goTypes = nil
	file_atomic_api_proto_v1_types_proto_depIdxs = nil
}
