// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: atomic/api/botregion/v1/request_response.proto

package botregion

import (
	v1 "github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// NewBotRequest create a new bot without starting
type NewBotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bot *v1.Bot `protobuf:"bytes,1,opt,name=bot,proto3" json:"bot,omitempty"`
}

func (x *NewBotRequest) Reset() {
	*x = NewBotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewBotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewBotRequest) ProtoMessage() {}

func (x *NewBotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewBotRequest.ProtoReflect.Descriptor instead.
func (*NewBotRequest) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{0}
}

func (x *NewBotRequest) GetBot() *v1.Bot {
	if x != nil {
		return x.Bot
	}
	return nil
}

// StartBotRequest starting a bot strategy
type StartBotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BotId string `protobuf:"bytes,1,opt,name=bot_id,json=botId,proto3" json:"bot_id,omitempty"`
}

func (x *StartBotRequest) Reset() {
	*x = StartBotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartBotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartBotRequest) ProtoMessage() {}

func (x *StartBotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartBotRequest.ProtoReflect.Descriptor instead.
func (*StartBotRequest) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{1}
}

func (x *StartBotRequest) GetBotId() string {
	if x != nil {
		return x.BotId
	}
	return ""
}

// GetBotRequest fetches a bot by id
type GetBotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BotId string `protobuf:"bytes,1,opt,name=bot_id,json=botId,proto3" json:"bot_id,omitempty"`
}

func (x *GetBotRequest) Reset() {
	*x = GetBotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBotRequest) ProtoMessage() {}

func (x *GetBotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBotRequest.ProtoReflect.Descriptor instead.
func (*GetBotRequest) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{2}
}

func (x *GetBotRequest) GetBotId() string {
	if x != nil {
		return x.BotId
	}
	return ""
}

// GetBotResponse fetches a bot by id
type GetBotResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bot *v1.Bot `protobuf:"bytes,1,opt,name=bot,proto3" json:"bot,omitempty"`
}

func (x *GetBotResponse) Reset() {
	*x = GetBotResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBotResponse) ProtoMessage() {}

func (x *GetBotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBotResponse.ProtoReflect.Descriptor instead.
func (*GetBotResponse) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{3}
}

func (x *GetBotResponse) GetBot() *v1.Bot {
	if x != nil {
		return x.Bot
	}
	return nil
}

// StopBotRequest stops a bot by its id
type StopBotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BotId string `protobuf:"bytes,1,opt,name=bot_id,json=botId,proto3" json:"bot_id,omitempty"`
}

func (x *StopBotRequest) Reset() {
	*x = StopBotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopBotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopBotRequest) ProtoMessage() {}

func (x *StopBotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopBotRequest.ProtoReflect.Descriptor instead.
func (*StopBotRequest) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{4}
}

func (x *StopBotRequest) GetBotId() string {
	if x != nil {
		return x.BotId
	}
	return ""
}

// ListBotsResponse list all bots that are running
type ListBotsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bots []*v1.Bot `protobuf:"bytes,1,rep,name=bots,proto3" json:"bots,omitempty"`
}

func (x *ListBotsResponse) Reset() {
	*x = ListBotsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBotsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBotsResponse) ProtoMessage() {}

func (x *ListBotsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBotsResponse.ProtoReflect.Descriptor instead.
func (*ListBotsResponse) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{5}
}

func (x *ListBotsResponse) GetBots() []*v1.Bot {
	if x != nil {
		return x.Bots
	}
	return nil
}

// UpdateBotParamsRequest contains a list of updates for multiple bots.
type UpdateBotParamsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BotUpdates []*BotParamsUpdate `protobuf:"bytes,1,rep,name=bot_updates,json=botUpdates,proto3" json:"bot_updates,omitempty"`
}

func (x *UpdateBotParamsRequest) Reset() {
	*x = UpdateBotParamsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBotParamsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBotParamsRequest) ProtoMessage() {}

func (x *UpdateBotParamsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBotParamsRequest.ProtoReflect.Descriptor instead.
func (*UpdateBotParamsRequest) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateBotParamsRequest) GetBotUpdates() []*BotParamsUpdate {
	if x != nil {
		return x.BotUpdates
	}
	return nil
}

// BotParamsUpdate contains the set of parameters for a single bot.
type BotParamsUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BotId  string           `protobuf:"bytes,1,opt,name=bot_id,json=botId,proto3" json:"bot_id,omitempty"`
	Params *structpb.Struct `protobuf:"bytes,2,opt,name=params,proto3" json:"params,omitempty"`
}

func (x *BotParamsUpdate) Reset() {
	*x = BotParamsUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BotParamsUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BotParamsUpdate) ProtoMessage() {}

func (x *BotParamsUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BotParamsUpdate.ProtoReflect.Descriptor instead.
func (*BotParamsUpdate) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{7}
}

func (x *BotParamsUpdate) GetBotId() string {
	if x != nil {
		return x.BotId
	}
	return ""
}

func (x *BotParamsUpdate) GetParams() *structpb.Struct {
	if x != nil {
		return x.Params
	}
	return nil
}

// UpdateGatewayOptionsRequest contains a list of updates for gtw in multiple bots.
type UpdateGatewayOptionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GtwUpdates []*UpdateGatewayOptions `protobuf:"bytes,1,rep,name=gtw_updates,json=gtwUpdates,proto3" json:"gtw_updates,omitempty"`
}

func (x *UpdateGatewayOptionsRequest) Reset() {
	*x = UpdateGatewayOptionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGatewayOptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGatewayOptionsRequest) ProtoMessage() {}

func (x *UpdateGatewayOptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGatewayOptionsRequest.ProtoReflect.Descriptor instead.
func (*UpdateGatewayOptionsRequest) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateGatewayOptionsRequest) GetGtwUpdates() []*UpdateGatewayOptions {
	if x != nil {
		return x.GtwUpdates
	}
	return nil
}

// UpdateGatewayOptions contains the set of parameters for a single gtw update.
type UpdateGatewayOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BotId      string           `protobuf:"bytes,1,opt,name=bot_id,json=botId,proto3" json:"bot_id,omitempty"`
	GtwOptions *structpb.Struct `protobuf:"bytes,2,opt,name=gtw_options,json=gtwOptions,proto3" json:"gtw_options,omitempty"`
}

func (x *UpdateGatewayOptions) Reset() {
	*x = UpdateGatewayOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGatewayOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGatewayOptions) ProtoMessage() {}

func (x *UpdateGatewayOptions) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGatewayOptions.ProtoReflect.Descriptor instead.
func (*UpdateGatewayOptions) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateGatewayOptions) GetBotId() string {
	if x != nil {
		return x.BotId
	}
	return ""
}

func (x *UpdateGatewayOptions) GetGtwOptions() *structpb.Struct {
	if x != nil {
		return x.GtwOptions
	}
	return nil
}

// GetBotStateRequest fetches the current bot state by its id
type GetBotStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BotId string `protobuf:"bytes,1,opt,name=bot_id,json=botId,proto3" json:"bot_id,omitempty"`
}

func (x *GetBotStateRequest) Reset() {
	*x = GetBotStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBotStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBotStateRequest) ProtoMessage() {}

func (x *GetBotStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBotStateRequest.ProtoReflect.Descriptor instead.
func (*GetBotStateRequest) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{10}
}

func (x *GetBotStateRequest) GetBotId() string {
	if x != nil {
		return x.BotId
	}
	return ""
}

// GetBotStateResponse get the current bot state
type GetBotStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State *v1.State `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *GetBotStateResponse) Reset() {
	*x = GetBotStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBotStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBotStateResponse) ProtoMessage() {}

func (x *GetBotStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBotStateResponse.ProtoReflect.Descriptor instead.
func (*GetBotStateResponse) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{11}
}

func (x *GetBotStateResponse) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

// ListBotsStateResponse fetch all bots state
type ListBotsStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BotsState []*v1.State `protobuf:"bytes,1,rep,name=bots_state,json=botsState,proto3" json:"bots_state,omitempty"`
}

func (x *ListBotsStateResponse) Reset() {
	*x = ListBotsStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBotsStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBotsStateResponse) ProtoMessage() {}

func (x *ListBotsStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_atomic_api_botregion_v1_request_response_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBotsStateResponse.ProtoReflect.Descriptor instead.
func (*ListBotsStateResponse) Descriptor() ([]byte, []int) {
	return file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP(), []int{12}
}

func (x *ListBotsStateResponse) GetBotsState() []*v1.State {
	if x != nil {
		return x.BotsState
	}
	return nil
}

var File_atomic_api_botregion_v1_request_response_proto protoreflect.FileDescriptor

var file_atomic_api_botregion_v1_request_response_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x6f, 0x74,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x17, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x6f, 0x74,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3b, 0x0a, 0x0d, 0x4e, 0x65, 0x77, 0x42,
	0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x03, 0x62, 0x6f, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x74,
	0x52, 0x03, 0x62, 0x6f, 0x74, 0x22, 0x28, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x6f,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x6f, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x22,
	0x26, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x15, 0x0a, 0x06, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x42, 0x6f,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x03, 0x62, 0x6f, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x74,
	0x52, 0x03, 0x62, 0x6f, 0x74, 0x22, 0x27, 0x0a, 0x0e, 0x53, 0x74, 0x6f, 0x70, 0x42, 0x6f, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x6f, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x22, 0x40,
	0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x62, 0x6f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x74, 0x52, 0x04, 0x62, 0x6f, 0x74, 0x73,
	0x22, 0x63, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x0b, 0x62, 0x6f,
	0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x6f, 0x74,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x62, 0x6f, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x73, 0x22, 0x59, 0x0a, 0x0f, 0x42, 0x6f, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x6f, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12,
	0x2f, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x22, 0x6d, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x4e, 0x0a, 0x0b, 0x67, 0x74, 0x77, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x0a, 0x67, 0x74, 0x77, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x22,
	0x67, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x6f, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x38,
	0x0a, 0x0b, 0x67, 0x74, 0x77, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0a, 0x67, 0x74,
	0x77, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x2b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x42,
	0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15,
	0x0a, 0x06, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x62, 0x6f, 0x74, 0x49, 0x64, 0x22, 0x47, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x74,
	0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x52,
	0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x73, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x74,
	0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x62, 0x6f, 0x74, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x42, 0x4b, 0x5a, 0x49, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x68, 0x65, 0x72, 0x65, 0x6e, 0x6f, 0x77, 0x2f, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2d,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x73, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x61, 0x74,
	0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_atomic_api_botregion_v1_request_response_proto_rawDescOnce sync.Once
	file_atomic_api_botregion_v1_request_response_proto_rawDescData = file_atomic_api_botregion_v1_request_response_proto_rawDesc
)

func file_atomic_api_botregion_v1_request_response_proto_rawDescGZIP() []byte {
	file_atomic_api_botregion_v1_request_response_proto_rawDescOnce.Do(func() {
		file_atomic_api_botregion_v1_request_response_proto_rawDescData = protoimpl.X.CompressGZIP(file_atomic_api_botregion_v1_request_response_proto_rawDescData)
	})
	return file_atomic_api_botregion_v1_request_response_proto_rawDescData
}

var file_atomic_api_botregion_v1_request_response_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_atomic_api_botregion_v1_request_response_proto_goTypes = []any{
	(*NewBotRequest)(nil),               // 0: atomic.api.botregion.v1.NewBotRequest
	(*StartBotRequest)(nil),             // 1: atomic.api.botregion.v1.StartBotRequest
	(*GetBotRequest)(nil),               // 2: atomic.api.botregion.v1.GetBotRequest
	(*GetBotResponse)(nil),              // 3: atomic.api.botregion.v1.GetBotResponse
	(*StopBotRequest)(nil),              // 4: atomic.api.botregion.v1.StopBotRequest
	(*ListBotsResponse)(nil),            // 5: atomic.api.botregion.v1.ListBotsResponse
	(*UpdateBotParamsRequest)(nil),      // 6: atomic.api.botregion.v1.UpdateBotParamsRequest
	(*BotParamsUpdate)(nil),             // 7: atomic.api.botregion.v1.BotParamsUpdate
	(*UpdateGatewayOptionsRequest)(nil), // 8: atomic.api.botregion.v1.UpdateGatewayOptionsRequest
	(*UpdateGatewayOptions)(nil),        // 9: atomic.api.botregion.v1.UpdateGatewayOptions
	(*GetBotStateRequest)(nil),          // 10: atomic.api.botregion.v1.GetBotStateRequest
	(*GetBotStateResponse)(nil),         // 11: atomic.api.botregion.v1.GetBotStateResponse
	(*ListBotsStateResponse)(nil),       // 12: atomic.api.botregion.v1.ListBotsStateResponse
	(*v1.Bot)(nil),                      // 13: atomic.api.proto.v1.Bot
	(*structpb.Struct)(nil),             // 14: google.protobuf.Struct
	(*v1.State)(nil),                    // 15: atomic.api.proto.v1.State
}
var file_atomic_api_botregion_v1_request_response_proto_depIdxs = []int32{
	13, // 0: atomic.api.botregion.v1.NewBotRequest.bot:type_name -> atomic.api.proto.v1.Bot
	13, // 1: atomic.api.botregion.v1.GetBotResponse.bot:type_name -> atomic.api.proto.v1.Bot
	13, // 2: atomic.api.botregion.v1.ListBotsResponse.bots:type_name -> atomic.api.proto.v1.Bot
	7,  // 3: atomic.api.botregion.v1.UpdateBotParamsRequest.bot_updates:type_name -> atomic.api.botregion.v1.BotParamsUpdate
	14, // 4: atomic.api.botregion.v1.BotParamsUpdate.params:type_name -> google.protobuf.Struct
	9,  // 5: atomic.api.botregion.v1.UpdateGatewayOptionsRequest.gtw_updates:type_name -> atomic.api.botregion.v1.UpdateGatewayOptions
	14, // 6: atomic.api.botregion.v1.UpdateGatewayOptions.gtw_options:type_name -> google.protobuf.Struct
	15, // 7: atomic.api.botregion.v1.GetBotStateResponse.state:type_name -> atomic.api.proto.v1.State
	15, // 8: atomic.api.botregion.v1.ListBotsStateResponse.bots_state:type_name -> atomic.api.proto.v1.State
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_atomic_api_botregion_v1_request_response_proto_init() }
func file_atomic_api_botregion_v1_request_response_proto_init() {
	if File_atomic_api_botregion_v1_request_response_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*NewBotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*StartBotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*GetBotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*GetBotResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*StopBotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ListBotsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateBotParamsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*BotParamsUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateGatewayOptionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*UpdateGatewayOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*GetBotStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*GetBotStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_atomic_api_botregion_v1_request_response_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*ListBotsStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_atomic_api_botregion_v1_request_response_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_atomic_api_botregion_v1_request_response_proto_goTypes,
		DependencyIndexes: file_atomic_api_botregion_v1_request_response_proto_depIdxs,
		MessageInfos:      file_atomic_api_botregion_v1_request_response_proto_msgTypes,
	}.Build()
	File_atomic_api_botregion_v1_request_response_proto = out.File
	file_atomic_api_botregion_v1_request_response_proto_rawDesc = nil
	file_atomic_api_botregion_v1_request_response_proto_goTypes = nil
	file_atomic_api_botregion_v1_request_response_proto_depIdxs = nil
}
