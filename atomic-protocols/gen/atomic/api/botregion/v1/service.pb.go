// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        (unknown)
// source: atomic/api/botregion/v1/service.proto

package botregion

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_atomic_api_botregion_v1_service_proto protoreflect.FileDescriptor

var file_atomic_api_botregion_v1_service_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x6f, 0x74,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x61,
	0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xa1, 0x07,
	0x0a, 0x10, 0x42, 0x6f, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x48, 0x0a, 0x06, 0x4e, 0x65, 0x77, 0x42, 0x6f, 0x74, 0x12, 0x26, 0x2e, 0x61,
	0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77, 0x42, 0x6f, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4c, 0x0a, 0x08,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x6f, 0x74, 0x12, 0x28, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69,
	0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x42, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4a, 0x0a, 0x07, 0x53, 0x74,
	0x6f, 0x70, 0x42, 0x6f, 0x74, 0x12, 0x27, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x6f, 0x70, 0x42, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3e, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41,
	0x6c, 0x6c, 0x42, 0x6f, 0x74, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3d, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x70, 0x41, 0x6c,
	0x6c, 0x42, 0x6f, 0x74, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x59, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x74, 0x12,
	0x26, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x6f, 0x74,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4d, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x74, 0x73, 0x12, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x29, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5a, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x2f, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x6f, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x61, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4f, 0x70, 0x74, 0x73,
	0x12, 0x34, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x6f,
	0x74, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x68,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x2e,
	0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x6f, 0x74, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x74, 0x6f,
	0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x6f, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x2e, 0x2e, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x6f, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x4b, 0x5a, 0x49, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x68, 0x65, 0x72, 0x65, 0x6e, 0x6f, 0x77, 0x2f, 0x61, 0x74, 0x6f, 0x6d, 0x69, 0x63, 0x2d, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x73, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x61, 0x74, 0x6f,
	0x6d, 0x69, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x6f, 0x74, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_atomic_api_botregion_v1_service_proto_goTypes = []any{
	(*NewBotRequest)(nil),               // 0: atomic.api.botregion.v1.NewBotRequest
	(*StartBotRequest)(nil),             // 1: atomic.api.botregion.v1.StartBotRequest
	(*StopBotRequest)(nil),              // 2: atomic.api.botregion.v1.StopBotRequest
	(*emptypb.Empty)(nil),               // 3: google.protobuf.Empty
	(*GetBotRequest)(nil),               // 4: atomic.api.botregion.v1.GetBotRequest
	(*UpdateBotParamsRequest)(nil),      // 5: atomic.api.botregion.v1.UpdateBotParamsRequest
	(*UpdateGatewayOptionsRequest)(nil), // 6: atomic.api.botregion.v1.UpdateGatewayOptionsRequest
	(*GetBotStateRequest)(nil),          // 7: atomic.api.botregion.v1.GetBotStateRequest
	(*GetBotResponse)(nil),              // 8: atomic.api.botregion.v1.GetBotResponse
	(*ListBotsResponse)(nil),            // 9: atomic.api.botregion.v1.ListBotsResponse
	(*GetBotStateResponse)(nil),         // 10: atomic.api.botregion.v1.GetBotStateResponse
	(*ListBotsStateResponse)(nil),       // 11: atomic.api.botregion.v1.ListBotsStateResponse
}
var file_atomic_api_botregion_v1_service_proto_depIdxs = []int32{
	0,  // 0: atomic.api.botregion.v1.BotRegionService.NewBot:input_type -> atomic.api.botregion.v1.NewBotRequest
	1,  // 1: atomic.api.botregion.v1.BotRegionService.StartBot:input_type -> atomic.api.botregion.v1.StartBotRequest
	2,  // 2: atomic.api.botregion.v1.BotRegionService.StopBot:input_type -> atomic.api.botregion.v1.StopBotRequest
	3,  // 3: atomic.api.botregion.v1.BotRegionService.StartAllBots:input_type -> google.protobuf.Empty
	3,  // 4: atomic.api.botregion.v1.BotRegionService.StopAllBots:input_type -> google.protobuf.Empty
	4,  // 5: atomic.api.botregion.v1.BotRegionService.GetBot:input_type -> atomic.api.botregion.v1.GetBotRequest
	3,  // 6: atomic.api.botregion.v1.BotRegionService.ListBots:input_type -> google.protobuf.Empty
	5,  // 7: atomic.api.botregion.v1.BotRegionService.UpdateBotParams:input_type -> atomic.api.botregion.v1.UpdateBotParamsRequest
	6,  // 8: atomic.api.botregion.v1.BotRegionService.UpdateGatewayOpts:input_type -> atomic.api.botregion.v1.UpdateGatewayOptionsRequest
	7,  // 9: atomic.api.botregion.v1.BotRegionService.GetBotState:input_type -> atomic.api.botregion.v1.GetBotStateRequest
	3,  // 10: atomic.api.botregion.v1.BotRegionService.ListBotsState:input_type -> google.protobuf.Empty
	3,  // 11: atomic.api.botregion.v1.BotRegionService.NewBot:output_type -> google.protobuf.Empty
	3,  // 12: atomic.api.botregion.v1.BotRegionService.StartBot:output_type -> google.protobuf.Empty
	3,  // 13: atomic.api.botregion.v1.BotRegionService.StopBot:output_type -> google.protobuf.Empty
	3,  // 14: atomic.api.botregion.v1.BotRegionService.StartAllBots:output_type -> google.protobuf.Empty
	3,  // 15: atomic.api.botregion.v1.BotRegionService.StopAllBots:output_type -> google.protobuf.Empty
	8,  // 16: atomic.api.botregion.v1.BotRegionService.GetBot:output_type -> atomic.api.botregion.v1.GetBotResponse
	9,  // 17: atomic.api.botregion.v1.BotRegionService.ListBots:output_type -> atomic.api.botregion.v1.ListBotsResponse
	3,  // 18: atomic.api.botregion.v1.BotRegionService.UpdateBotParams:output_type -> google.protobuf.Empty
	3,  // 19: atomic.api.botregion.v1.BotRegionService.UpdateGatewayOpts:output_type -> google.protobuf.Empty
	10, // 20: atomic.api.botregion.v1.BotRegionService.GetBotState:output_type -> atomic.api.botregion.v1.GetBotStateResponse
	11, // 21: atomic.api.botregion.v1.BotRegionService.ListBotsState:output_type -> atomic.api.botregion.v1.ListBotsStateResponse
	11, // [11:22] is the sub-list for method output_type
	0,  // [0:11] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_atomic_api_botregion_v1_service_proto_init() }
func file_atomic_api_botregion_v1_service_proto_init() {
	if File_atomic_api_botregion_v1_service_proto != nil {
		return
	}
	file_atomic_api_botregion_v1_request_response_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_atomic_api_botregion_v1_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_atomic_api_botregion_v1_service_proto_goTypes,
		DependencyIndexes: file_atomic_api_botregion_v1_service_proto_depIdxs,
	}.Build()
	File_atomic_api_botregion_v1_service_proto = out.File
	file_atomic_api_botregion_v1_service_proto_rawDesc = nil
	file_atomic_api_botregion_v1_service_proto_goTypes = nil
	file_atomic_api_botregion_v1_service_proto_depIdxs = nil
}
