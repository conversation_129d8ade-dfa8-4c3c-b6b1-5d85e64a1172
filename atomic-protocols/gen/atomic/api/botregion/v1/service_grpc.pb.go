// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             (unknown)
// source: atomic/api/botregion/v1/service.proto

package botregion

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	BotRegionService_NewBot_FullMethodName            = "/atomic.api.botregion.v1.BotRegionService/NewBot"
	BotRegionService_StartBot_FullMethodName          = "/atomic.api.botregion.v1.BotRegionService/StartBot"
	BotRegionService_StopBot_FullMethodName           = "/atomic.api.botregion.v1.BotRegionService/StopBot"
	BotRegionService_StartAllBots_FullMethodName      = "/atomic.api.botregion.v1.BotRegionService/StartAllBots"
	BotRegionService_StopAllBots_FullMethodName       = "/atomic.api.botregion.v1.BotRegionService/StopAllBots"
	BotRegionService_GetBot_FullMethodName            = "/atomic.api.botregion.v1.BotRegionService/GetBot"
	BotRegionService_ListBots_FullMethodName          = "/atomic.api.botregion.v1.BotRegionService/ListBots"
	BotRegionService_UpdateBotParams_FullMethodName   = "/atomic.api.botregion.v1.BotRegionService/UpdateBotParams"
	BotRegionService_UpdateGatewayOpts_FullMethodName = "/atomic.api.botregion.v1.BotRegionService/UpdateGatewayOpts"
	BotRegionService_GetBotState_FullMethodName       = "/atomic.api.botregion.v1.BotRegionService/GetBotState"
	BotRegionService_ListBotsState_FullMethodName     = "/atomic.api.botregion.v1.BotRegionService/ListBotsState"
)

// BotRegionServiceClient is the client API for BotRegionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// BotRegionService
type BotRegionServiceClient interface {
	// NewBot create a new bot but does not start it
	NewBot(ctx context.Context, in *NewBotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// StartBot start a given bot with the given parameters
	StartBot(ctx context.Context, in *StartBotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// StopBot stop a given bot
	StopBot(ctx context.Context, in *StopBotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// StartAll start all bots that this region manages
	StartAllBots(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// StopAll stop all bots that this region manages
	StopAllBots(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetBot get an bot by its id
	GetBot(ctx context.Context, in *GetBotRequest, opts ...grpc.CallOption) (*GetBotResponse, error)
	// ListBots lists known bots in the region
	ListBots(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListBotsResponse, error)
	// UpdateBotParams updates a list of parameters for one or many bots
	UpdateBotParams(ctx context.Context, in *UpdateBotParamsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// UpdateGatewayOpts updates a list of gateway options for one or many bots
	UpdateGatewayOpts(ctx context.Context, in *UpdateGatewayOptionsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetBotState returns the current state of a given bot
	GetBotState(ctx context.Context, in *GetBotStateRequest, opts ...grpc.CallOption) (*GetBotStateResponse, error)
	// ListBotsState returns a list of bots with their actual state
	ListBotsState(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListBotsStateResponse, error)
}

type botRegionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBotRegionServiceClient(cc grpc.ClientConnInterface) BotRegionServiceClient {
	return &botRegionServiceClient{cc}
}

func (c *botRegionServiceClient) NewBot(ctx context.Context, in *NewBotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, BotRegionService_NewBot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *botRegionServiceClient) StartBot(ctx context.Context, in *StartBotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, BotRegionService_StartBot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *botRegionServiceClient) StopBot(ctx context.Context, in *StopBotRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, BotRegionService_StopBot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *botRegionServiceClient) StartAllBots(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, BotRegionService_StartAllBots_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *botRegionServiceClient) StopAllBots(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, BotRegionService_StopAllBots_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *botRegionServiceClient) GetBot(ctx context.Context, in *GetBotRequest, opts ...grpc.CallOption) (*GetBotResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBotResponse)
	err := c.cc.Invoke(ctx, BotRegionService_GetBot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *botRegionServiceClient) ListBots(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListBotsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBotsResponse)
	err := c.cc.Invoke(ctx, BotRegionService_ListBots_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *botRegionServiceClient) UpdateBotParams(ctx context.Context, in *UpdateBotParamsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, BotRegionService_UpdateBotParams_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *botRegionServiceClient) UpdateGatewayOpts(ctx context.Context, in *UpdateGatewayOptionsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, BotRegionService_UpdateGatewayOpts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *botRegionServiceClient) GetBotState(ctx context.Context, in *GetBotStateRequest, opts ...grpc.CallOption) (*GetBotStateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBotStateResponse)
	err := c.cc.Invoke(ctx, BotRegionService_GetBotState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *botRegionServiceClient) ListBotsState(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListBotsStateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBotsStateResponse)
	err := c.cc.Invoke(ctx, BotRegionService_ListBotsState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BotRegionServiceServer is the server API for BotRegionService service.
// All implementations must embed UnimplementedBotRegionServiceServer
// for forward compatibility
//
// BotRegionService
type BotRegionServiceServer interface {
	// NewBot create a new bot but does not start it
	NewBot(context.Context, *NewBotRequest) (*emptypb.Empty, error)
	// StartBot start a given bot with the given parameters
	StartBot(context.Context, *StartBotRequest) (*emptypb.Empty, error)
	// StopBot stop a given bot
	StopBot(context.Context, *StopBotRequest) (*emptypb.Empty, error)
	// StartAll start all bots that this region manages
	StartAllBots(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// StopAll stop all bots that this region manages
	StopAllBots(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// GetBot get an bot by its id
	GetBot(context.Context, *GetBotRequest) (*GetBotResponse, error)
	// ListBots lists known bots in the region
	ListBots(context.Context, *emptypb.Empty) (*ListBotsResponse, error)
	// UpdateBotParams updates a list of parameters for one or many bots
	UpdateBotParams(context.Context, *UpdateBotParamsRequest) (*emptypb.Empty, error)
	// UpdateGatewayOpts updates a list of gateway options for one or many bots
	UpdateGatewayOpts(context.Context, *UpdateGatewayOptionsRequest) (*emptypb.Empty, error)
	// GetBotState returns the current state of a given bot
	GetBotState(context.Context, *GetBotStateRequest) (*GetBotStateResponse, error)
	// ListBotsState returns a list of bots with their actual state
	ListBotsState(context.Context, *emptypb.Empty) (*ListBotsStateResponse, error)
	mustEmbedUnimplementedBotRegionServiceServer()
}

// UnimplementedBotRegionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBotRegionServiceServer struct {
}

func (UnimplementedBotRegionServiceServer) NewBot(context.Context, *NewBotRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewBot not implemented")
}
func (UnimplementedBotRegionServiceServer) StartBot(context.Context, *StartBotRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartBot not implemented")
}
func (UnimplementedBotRegionServiceServer) StopBot(context.Context, *StopBotRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopBot not implemented")
}
func (UnimplementedBotRegionServiceServer) StartAllBots(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartAllBots not implemented")
}
func (UnimplementedBotRegionServiceServer) StopAllBots(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopAllBots not implemented")
}
func (UnimplementedBotRegionServiceServer) GetBot(context.Context, *GetBotRequest) (*GetBotResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBot not implemented")
}
func (UnimplementedBotRegionServiceServer) ListBots(context.Context, *emptypb.Empty) (*ListBotsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBots not implemented")
}
func (UnimplementedBotRegionServiceServer) UpdateBotParams(context.Context, *UpdateBotParamsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBotParams not implemented")
}
func (UnimplementedBotRegionServiceServer) UpdateGatewayOpts(context.Context, *UpdateGatewayOptionsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGatewayOpts not implemented")
}
func (UnimplementedBotRegionServiceServer) GetBotState(context.Context, *GetBotStateRequest) (*GetBotStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBotState not implemented")
}
func (UnimplementedBotRegionServiceServer) ListBotsState(context.Context, *emptypb.Empty) (*ListBotsStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBotsState not implemented")
}
func (UnimplementedBotRegionServiceServer) mustEmbedUnimplementedBotRegionServiceServer() {}

// UnsafeBotRegionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BotRegionServiceServer will
// result in compilation errors.
type UnsafeBotRegionServiceServer interface {
	mustEmbedUnimplementedBotRegionServiceServer()
}

func RegisterBotRegionServiceServer(s grpc.ServiceRegistrar, srv BotRegionServiceServer) {
	s.RegisterService(&BotRegionService_ServiceDesc, srv)
}

func _BotRegionService_NewBot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewBotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BotRegionServiceServer).NewBot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BotRegionService_NewBot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BotRegionServiceServer).NewBot(ctx, req.(*NewBotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BotRegionService_StartBot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartBotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BotRegionServiceServer).StartBot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BotRegionService_StartBot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BotRegionServiceServer).StartBot(ctx, req.(*StartBotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BotRegionService_StopBot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopBotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BotRegionServiceServer).StopBot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BotRegionService_StopBot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BotRegionServiceServer).StopBot(ctx, req.(*StopBotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BotRegionService_StartAllBots_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BotRegionServiceServer).StartAllBots(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BotRegionService_StartAllBots_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BotRegionServiceServer).StartAllBots(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _BotRegionService_StopAllBots_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BotRegionServiceServer).StopAllBots(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BotRegionService_StopAllBots_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BotRegionServiceServer).StopAllBots(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _BotRegionService_GetBot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BotRegionServiceServer).GetBot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BotRegionService_GetBot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BotRegionServiceServer).GetBot(ctx, req.(*GetBotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BotRegionService_ListBots_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BotRegionServiceServer).ListBots(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BotRegionService_ListBots_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BotRegionServiceServer).ListBots(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _BotRegionService_UpdateBotParams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBotParamsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BotRegionServiceServer).UpdateBotParams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BotRegionService_UpdateBotParams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BotRegionServiceServer).UpdateBotParams(ctx, req.(*UpdateBotParamsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BotRegionService_UpdateGatewayOpts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGatewayOptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BotRegionServiceServer).UpdateGatewayOpts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BotRegionService_UpdateGatewayOpts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BotRegionServiceServer).UpdateGatewayOpts(ctx, req.(*UpdateGatewayOptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BotRegionService_GetBotState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBotStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BotRegionServiceServer).GetBotState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BotRegionService_GetBotState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BotRegionServiceServer).GetBotState(ctx, req.(*GetBotStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BotRegionService_ListBotsState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BotRegionServiceServer).ListBotsState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BotRegionService_ListBotsState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BotRegionServiceServer).ListBotsState(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// BotRegionService_ServiceDesc is the grpc.ServiceDesc for BotRegionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BotRegionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "atomic.api.botregion.v1.BotRegionService",
	HandlerType: (*BotRegionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NewBot",
			Handler:    _BotRegionService_NewBot_Handler,
		},
		{
			MethodName: "StartBot",
			Handler:    _BotRegionService_StartBot_Handler,
		},
		{
			MethodName: "StopBot",
			Handler:    _BotRegionService_StopBot_Handler,
		},
		{
			MethodName: "StartAllBots",
			Handler:    _BotRegionService_StartAllBots_Handler,
		},
		{
			MethodName: "StopAllBots",
			Handler:    _BotRegionService_StopAllBots_Handler,
		},
		{
			MethodName: "GetBot",
			Handler:    _BotRegionService_GetBot_Handler,
		},
		{
			MethodName: "ListBots",
			Handler:    _BotRegionService_ListBots_Handler,
		},
		{
			MethodName: "UpdateBotParams",
			Handler:    _BotRegionService_UpdateBotParams_Handler,
		},
		{
			MethodName: "UpdateGatewayOpts",
			Handler:    _BotRegionService_UpdateGatewayOpts_Handler,
		},
		{
			MethodName: "GetBotState",
			Handler:    _BotRegionService_GetBotState_Handler,
		},
		{
			MethodName: "ListBotsState",
			Handler:    _BotRegionService_ListBotsState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "atomic/api/botregion/v1/service.proto",
}
