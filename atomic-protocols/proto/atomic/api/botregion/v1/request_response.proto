syntax = "proto3";

package atomic.api.botregion.v1;

import "google/protobuf/struct.proto";
import "atomic/api/proto/v1/types.proto";

option go_package = "github.com/herenow/atomic-protocols/gen/atomic/api/botregion/v1;botregion";


// NewBotRequest create a new bot without starting
message NewBotRequest {
    proto.v1.Bot bot = 1;
}

// StartBotRequest starting a bot strategy
message StartBotRequest {
    string bot_id = 1;
}

// GetBotRequest fetches a bot by id
message GetBotRequest {
    string bot_id = 1;
}

// GetBotResponse fetches a bot by id
message GetBotResponse {
    proto.v1.Bot bot = 1;
}

// StopBotRequest stops a bot by its id
message StopBotRequest {
    string bot_id = 1;
}

// ListBotsResponse list all bots that are running
message ListBotsResponse {
    repeated proto.v1.Bot bots = 1;
}

// UpdateBotParamsRequest contains a list of updates for multiple bots.
message UpdateBotParamsRequest {
    repeated BotParamsUpdate bot_updates = 1;
}

// BotParamsUpdate contains the set of parameters for a single bot.
message BotParamsUpdate {
    string bot_id = 1;
    google.protobuf.Struct params = 2;
}

// UpdateGatewayOptionsRequest contains a list of updates for gtw in multiple bots.
message UpdateGatewayOptionsRequest {
    repeated UpdateGatewayOptions gtw_updates = 1;
}

// UpdateGatewayOptions contains the set of parameters for a single gtw update.
message UpdateGatewayOptions {
    string bot_id = 1;
    google.protobuf.Struct gtw_options = 2;
}

// GetBotStateRequest fetches the current bot state by its id
message GetBotStateRequest {
    string bot_id = 1;
}

// GetBotStateResponse get the current bot state
message GetBotStateResponse {
    proto.v1.State state = 1;
}

// ListBotsStateResponse fetch all bots state
message ListBotsStateResponse {
    repeated proto.v1.State bots_state = 1;
}
