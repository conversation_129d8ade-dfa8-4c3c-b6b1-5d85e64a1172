syntax = "proto3";

import "google/protobuf/empty.proto";

package atomic.api.botregion.v1;

import "atomic/api/botregion/v1/request_response.proto";

option go_package = "github.com/herenow/atomic-protocols/gen/atomic/api/botregion/v1;botregion";

// BotRegionService
service BotRegionService {
  // NewBot create a new bot but does not start it
  rpc NewBot(NewBotRequest) returns (google.protobuf.Empty);

  // StartBot start a given bot with the given parameters
  rpc StartBot(StartBotRequest) returns (google.protobuf.Empty);

  // StopBot stop a given bot
  rpc StopBot(StopBotRequest) returns (google.protobuf.Empty);

  // StartAll start all bots that this region manages
  rpc StartAllBots(google.protobuf.Empty) returns (google.protobuf.Empty);

  // StopAll stop all bots that this region manages
  rpc StopAllBots(google.protobuf.Empty) returns (google.protobuf.Empty);

  // GetBot get an bot by its id
  rpc GetBot(GetBotRequest) returns (GetBotResponse);

  // ListBots lists known bots in the region
  rpc ListBots(google.protobuf.Empty) returns (ListBotsResponse);

  // UpdateBotParams updates a list of parameters for one or many bots
  rpc UpdateBotParams(UpdateBotParamsRequest) returns (google.protobuf.Empty);

  // UpdateGatewayOpts updates a list of gateway options for one or many bots
  rpc UpdateGatewayOpts(UpdateGatewayOptionsRequest) returns (google.protobuf.Empty);

  // GetBotState returns the current state of a given bot
  rpc GetBotState(GetBotStateRequest) returns (GetBotStateResponse);

  // ListBotsState returns a list of bots with their actual state
  rpc ListBotsState(google.protobuf.Empty) returns (ListBotsStateResponse);
}
