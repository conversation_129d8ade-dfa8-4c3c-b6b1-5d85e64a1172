syntax = "proto3";

package atomic.api.enums.v1;

option go_package = "github.com/herenow/atomic-protocols/gen/atomic/api/enums/v1;enums";

// OrderType defines the type of order being placed or executed.
enum OrderType {
    ORDER_TYPE_UNSPECIFIED = 0;
    ORDER_TYPE_LIMIT = 1;
    ORDER_TYPE_MARKET = 2;
    ORDER_TYPE_UNKNOWN = 3;
}

// OrderSide indicates whether the order is to buy or sell an asset.
enum OrderSide {
    ORDER_SIDE_UNSPECIFIED = 0;
    ORDER_SIDE_BUY = 1;
    ORDER_SIDE_SELL = 2;
    ORDER_SIDE_UNKNOWN = 3;
}

// OrderState is the current state of an order in its lifecycle.
enum OrderState {
    ORDER_STATE_UNSPECIFIED = 0;
    ORDER_STATE_SENT = 1;
    ORDER_STATE_OPEN = 2;
    ORDER_STATE_PARTIALLY = 3;
    ORDER_STATE_FILLED = 4;
    ORDER_STATE_CLOSED = 5;
    ORDER_STATE_CANCELED = 6;
    ORDER_STATE_UNKNOWN = 7;
}
