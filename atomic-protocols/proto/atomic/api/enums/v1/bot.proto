syntax = "proto3";

package atomic.api.enums.v1;

option go_package = "github.com/herenow/atomic-protocols/gen/atomic/api/enums/v1;enums";

// BotType is the bot type (e.g., "market maker", "arbitrage", "trend follower").
enum BotType {
    BOT_TYPE_UNSPECIFIED = 0;
    BOT_TYPE_MARKET_MAKER = 1;
}

// BotStatus is the bot current status (e.g., "starting", "running"...).
enum BotStatus {
    BOT_STATUS_UNSPECIFIED = 0;
    BOT_STATUS_STARTING = 1;
    BOT_STATUS_RUNNING = 2;
    BOT_STATUS_STOPPING = 3;
    BOT_STATUS_STOPPED = 4;
    BOT_STATUS_ERROR = 5;
}