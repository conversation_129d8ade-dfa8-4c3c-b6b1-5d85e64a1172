syntax = "proto3";

package atomic.api.proto.v1;

import "atomic/api/enums/v1/bot.proto";
import "atomic/api/enums/v1/order.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/herenow/atomic-protocols/gen/atomic/api/proto/v1;proto";

// Bot is basic bot information
message Bot {
    string id = 1;
    string exchange_id = 2;
    string account_id = 3;
    string symbol = 4;
    enums.v1.BotStatus status = 5;
    bool start_on_create = 6;
    string last_error = 7;
    enums.v1.BotType type = 8;
    google.protobuf.Struct gateway_options = 9;
    google.protobuf.Struct params = 10;
}

// State represents a comprehensive snapshot of a bot's current trading state,
// encompassing its open orders, order book, inventory, market depth, and timestamp.
message State {
    string bot_id = 1;
    google.protobuf.Struct data = 2;
    google.protobuf.Struct params = 3;
    google.protobuf.Timestamp time = 4;
}
