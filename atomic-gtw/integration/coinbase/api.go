package coinbase

import (
	"crypto/rand"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"io/ioutil"
	"math"
	"math/big"
	"net/http"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
	"gopkg.in/square/go-jose.v2"
	"gopkg.in/square/go-jose.v2/jwt"
)

const (
	apiBaseProd         = "https://api.exchange.coinbase.com"
	apiBaseSandbox      = "https://api-public.sandbox.exchange.coinbase.com"
	apiBaseProdAdvanced = "https://api.coinbase.com"
	apiBaseProdHost     = "api.coinbase.com"
	apiTimestamp        = "/api/v2/time"
	apiPlaceOrder       = "/api/v3/brokerage/orders"
	apiCancelOrder      = "/api/v3/brokerage/orders/batch_cancel"
	apiMarket           = "/api/v3/brokerage/market/products"
	apiDepthBook        = "/api/v3/brokerage/market/product_book?product_id=%s"
	apiOpenOrders       = "/api/v3/brokerage/orders/historical/batch"
	apiAccountBalance   = "/api/v3/brokerage/accounts"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
	baseURL    string
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

type TimeStamp struct {
	Data struct {
		Iso   time.Time `json:"iso"`
		Epoch int       `json:"epoch"`
	} `json:"data"`
}

func (a *API) Timestamp() (ts int64, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiTimestamp, nil, false)
	if err != nil {
		return ts, err
	}

	var TimeStampData TimeStamp
	err = a.makeHttpRequest(req, &TimeStampData)
	ts = int64(TimeStampData.Data.Epoch)
	return ts, err
}

type APIMarketProduct struct {
	ProductID       string  `json:"product_id"`
	QuoteIncrement  float64 `json:"quote_increment,string"`
	BaseIncrement   float64 `json:"base_increment,string"`
	BaseCurrencyID  string  `json:"base_currency_id"`
	QuoteCurrencyID string  `json:"quote_currency_id"`
	Status          string  `json:"status"`
}

type APIiMarket struct {
	Products []APIMarketProduct `json:"products"`
}

func (a *API) Markets() (APIiMarket, error) {
	req, err := a.newHttpRequest(http.MethodGet, apiMarket, nil, false)
	if err != nil {
		return APIiMarket{}, err
	}

	var Market APIiMarket
	err = a.makeHttpRequest(req, &Market)
	if err != nil {
		return Market, err
	}

	return Market, nil
}

type APIAccountBalance struct {
	Accounts []struct {
		Currency  string  `json:"currency"`
		Balance   float64 `json:"balance,string"`
		Available float64 `json:"available,string"`
	} `json:"accounts"`
}

func (a *API) AccountBalance() (res APIAccountBalance, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiAccountBalance, nil, true)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APIOrderData struct {
	OrderID    string  `json:"order_id"`
	ProductID  string  `json:"product_id"`
	Side       string  `json:"side"`
	Status     string  `json:"status"`
	Size       float64 `json:"size,string"`
	Price      float64 `json:"price,string"`
	FilledSize float64 `json:"filled_size,string"`
	TotalFees  float64 `json:"total_fees,string"`
}

type APIOpenOrders struct {
	Orders []APIOrderData `json:"orders"`
}

func (a *API) OpenOrders() (orders APIOpenOrders, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiOpenOrders, nil, true)
	if err != nil {
		return orders, err
	}
	q := req.URL.Query()
	req.URL.RawQuery = q.Encode()

	err = a.makeHttpRequest(req, &orders)

	return orders, err
}

type APIPlaceOrder struct {
	OrderID string `json:"order_id"`
}

func (a *API) PlaceOrder(order io.Reader) (orderID string, err error) {
	var apiOrder APIPlaceOrder
	req, err := a.newHttpRequest(http.MethodPost, apiPlaceOrder, order, true)
	if err != nil {
		return "", err
	}

	err = a.makeHttpRequest(req, &apiOrder)
	return apiOrder.OrderID, err
}

func (a *API) CancelOrder(order io.Reader) (err error) {
	var apiOrder APIPlaceOrder
	req, err := a.newHttpRequest(http.MethodPost, apiCancelOrder, order, true)
	if err != nil {
		return err
	}

	if err = a.makeHttpRequest(req, &apiOrder); err != nil {
		return err
	}

	return nil
}

type DepthValues struct {
	Price  float64 `json:"price,string"`
	Amount float64 `json:"size,string"`
}

type APIDepthBook struct {
	PriceBook struct {
		Asks []DepthValues `json:"asks"`
		Bids []DepthValues `json:"bids"`
	} `json:"pricebook"`
}

func (a *API) DepthBook(symbol string, params gateway.GetDepthParams) (depth APIDepthBook, err error) {
	depthURL := fmt.Sprintf(apiDepthBook, symbol)
	req, err := a.newHttpRequest(http.MethodGet, depthURL, nil, false)
	if err != nil {
		return depth, err
	}

	err = a.makeHttpRequest(req, &depth)
	if err != nil {
		return depth, err
	}

	return depth, nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	if responseObject != nil {
		err = json.Unmarshal(body, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return nil
}

func (a *API) newHttpRequest(method string, path string, data io.Reader, signed bool) (*http.Request, error) {
	url := a.baseURL + path
	req, err := http.NewRequest(method, url, data)
	if err != nil {
		return nil, err
	}

	if signed {
		uri := fmt.Sprintf("%s %s%s", method, apiBaseProdHost, path)
		jwtToken, err := buildAPIJWT(a.options.ApiSecret, a.options.ApiKey, uri)
		if err != nil {
			return nil, err
		}
		req.Header.Set("Authorization", "Bearer "+jwtToken)
	}

	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	return req, nil
}

type WSKeyClaims struct {
	*jwt.Claims
}

type nonceSource struct{}

func (n nonceSource) Nonce() (string, error) {
	r, err := rand.Int(rand.Reader, big.NewInt(math.MaxInt64))
	if err != nil {
		return "", err
	}

	return r.String(), nil
}

type APIKeyClaims struct {
	*jwt.Claims
	URI string `json:"uri"`
}

func buildAPIJWT(keySecret, keyName, uri string) (string, error) {
	// Replaces literal "\n" with `newline` in the keySecret
	keySecret = strings.ReplaceAll(keySecret, "\\n", "\n")

	block, _ := pem.Decode([]byte(keySecret))
	if block == nil {
		return "", fmt.Errorf("jwt: Could not decode private key")
	}

	key, err := x509.ParseECPrivateKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("jwt: %w", err)
	}

	sig, err := jose.NewSigner(
		jose.SigningKey{Algorithm: jose.ES256, Key: key},
		(&jose.SignerOptions{NonceSource: nonceSource{}}).WithType("JWT").WithHeader("kid", keyName),
	)
	if err != nil {
		return "", fmt.Errorf("jwt: %w", err)
	}

	cl := &APIKeyClaims{
		Claims: &jwt.Claims{
			Subject:   keyName,
			Issuer:    "coinbase-cloud",
			NotBefore: jwt.NewNumericDate(time.Now()),
			Expiry:    jwt.NewNumericDate(time.Now().Add(2 * time.Minute)),
		},
		URI: uri,
	}
	jwtString, err := jwt.Signed(sig).Claims(cl).CompactSerialize()
	if err != nil {
		return "", fmt.Errorf("jwt: %w", err)
	}
	return jwtString, nil
}
