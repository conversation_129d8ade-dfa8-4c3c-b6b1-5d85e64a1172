package coinbase

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	baseURL string
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewMarketDataGateway(baseURL string, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		baseURL: baseURL,
		options: options,
		tickCh:  tickCh,
	}
}

type WsRequest struct {
	Type       string   `json:"type"`
	ProductIds []string `json:"product_ids"`
	Channel    string   `json:"channel"`
	JWT        string   `json:"jwt"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	err := ws.Connect(g.baseURL)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	symbols := make([]string, 0)
	for _, market := range markets {
		symbols = append(symbols, market.Symbol)
	}

	subRequest := WsRequest{
		Type:       "subscribe",
		ProductIds: symbols,
		Channel:    "level2",
	}

	data, err := json.Marshal(subRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}

	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write level2 sub msg to ws: %s", err)
	}

	subRequest = WsRequest{
		Type:       "subscribe",
		ProductIds: symbols,
		Channel:    "market_trades",
	}

	data, err = json.Marshal(subRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}

	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write market_trades sub msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ch)

	return nil
}

type WsResponse struct {
	Channel string          `json:"channel"`
	Events  json.RawMessage `json:"events"`
}

func (g *MarketDataGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		var msg WsResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		switch msg.Channel {
		case "error":
			log.Printf("WS [%s] msg w/ error msg: %s", g.baseURL, string(data))
			continue
		case "market_trades":
			if err := g.processTradeUpdates(msg.Events); err != nil {
				log.Printf("%s error processing matches \"%s\": %s", g.baseURL, data, err)
			}
		case "l2_data":
			if err := g.processLevel2UpdateMsg(msg.Events); err != nil {
				log.Printf("%s error processing l2update \"%s\": %s", g.baseURL, data, err)
			}
		default:
			log.Printf("Coinbase unprocessable message type [%s], data [%s]", msg.Channel, string(data))
		}
	}
}

type WsL2Update struct {
	Side   string  `json:"side"`
	Price  float64 `json:"price_level,string"`
	Amount float64 `json:"new_quantity,string"`
}

type WsL2Data struct {
	ProductID string       `json:"product_id"`
	Updates   []WsL2Update `json:"updates"`
}

func (g *MarketDataGateway) processLevel2UpdateMsg(data json.RawMessage) error {
	var l2update []WsL2Data
	err := json.Unmarshal(data, &l2update)
	if err != nil {
		return fmt.Errorf("unmarshal WsL2Data err: %s", err)
	}

	var bids []gateway.PriceArray
	var asks []gateway.PriceArray
	for _, update := range l2update {
		for _, levels := range update.Updates {
			if levels.Side == "bid" {
				bids = append(bids, gateway.PriceArray{Price: levels.Price, Amount: levels.Amount})
			} else {
				asks = append(asks, gateway.PriceArray{Price: levels.Price, Amount: levels.Amount})
			}
		}
		g.processDepthUpdate(update.ProductID, bids, asks, false)
	}

	return nil
}

func (g *MarketDataGateway) processDepthUpdate(symbol string, bids []gateway.PriceArray, asks []gateway.PriceArray, snapshot bool) {
	eventLog := make([]gateway.Event, 0, len(bids)+len(asks)+1)
	appendEvents := func(symbol string, side gateway.Side, prices []gateway.PriceArray) {
		for _, order := range prices {
			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  order.Price,
					Amount: order.Amount,
				},
			}

			eventLog = append(eventLog, event)
		}
	}

	if snapshot {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: symbol,
			},
		})
	}

	appendEvents(symbol, gateway.Ask, asks)
	appendEvents(symbol, gateway.Bid, bids)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

type WsTradeUpdate struct {
	Type   string        `json:"type"`
	Trades []WsMatchData `json:"trades"`
}

type WsMatchData struct {
	TradeID   string    `json:"trade_id"`
	Side      string    `json:"side"`
	Size      float64   `json:"size,string"`
	Price     float64   `json:"price,string"`
	ProductID string    `json:"product_id"`
	Time      time.Time `json:"time"`
}

func (g *MarketDataGateway) processTradeUpdates(data json.RawMessage) error {
	var events []WsTradeUpdate
	err := json.Unmarshal(data, &events)
	if err != nil {
		return fmt.Errorf("unmarhsal []WsTradeUpdate [%s] err [%s]", string(data), err)
	}

	eventLog := make([]gateway.Event, 0, 5)
	for _, update := range events {
		var side gateway.Side
		for _, trade := range update.Trades {
			if trade.Side == "BUY" {
				side = gateway.Bid
			} else {
				side = gateway.Ask
			}

			event := gateway.Event{
				Type: gateway.TradeEvent,
				Data: gateway.Trade{
					Symbol:    trade.ProductID,
					Timestamp: trade.Time,
					ID:        trade.TradeID,
					Direction: side,
					Price:     trade.Price,
					Amount:    trade.Size,
				},
			}

			eventLog = append(eventLog, event)
		}
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}
