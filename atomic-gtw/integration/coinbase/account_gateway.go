package coinbase

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api     *API
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		api:     api,
		options: options,
		tickCh:  tickCh,
	}
}

func (g *AccountGateway) Connect() error {
	return nil
}

type APIOrderLimitGTC struct {
	BaseSize   string `json:"base_size"`
	LimitPrice string `json:"limit_price"`
	PostOnly   bool   `json:"post_only"`
}

type APIOrderConfig struct {
	LimitLimitGTC APIOrderLimitGTC `json:"limit_limit_gtc"`
}

type APIOrderReq struct {
	ClientOrderID      string         `json:"client_order_id"`
	ProductID          string         `json:"product_id"`
	Side               string         `json:"side"`
	OrderConfiguration APIOrderConfig `json:"order_configuration"`
}

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	data := APIOrderReq{
		ClientOrderID: uuid.New().String(),
		ProductID:     order.Market.Symbol,
		OrderConfiguration: APIOrderConfig{
			LimitLimitGTC: APIOrderLimitGTC{
				BaseSize:   utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
				LimitPrice: utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
				PostOnly:   order.PostOnly,
			},
		},
	}

	if order.Side == gateway.Bid {
		data.Side = "BUY"
	} else if order.Side == gateway.Ask {
		data.Side = "SELL"
	}

	orderData, err := json.Marshal(data)
	if err != nil {
		return orderId, err
	}
	orderDataBuffer := bytes.NewBuffer(orderData)
	orderID, err := g.api.PlaceOrder(orderDataBuffer)
	if err != nil {
		return orderId, err
	}

	return orderID, err
}

type APICancelOrderReq struct {
	OrderIDS []string `json:"order_ids"`
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	data := APICancelOrderReq{
		OrderIDS: []string{order.ID},
	}

	cancelOrderData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	cancelOrderDataBuffer := bytes.NewBuffer(cancelOrderData)
	return g.api.CancelOrder(cancelOrderDataBuffer)
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrders, err := g.api.OpenOrders()
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders.Orders))
	for _, order := range openOrders.Orders {
		orders = append(orders, mapAPIOrderToCommon(order))
	}

	return
}

func mapAPIOrderToCommon(o APIOrderData) gateway.Order {
	return gateway.Order{
		Market: gateway.Market{
			Exchange: Exchange,
			Symbol:   o.ProductID,
		},
		ID:               o.OrderID,
		Side:             mapAPIOrderTypeToCommonSide(o.Side),
		State:            mapAPIOrderStateToCommon(o.Status),
		Amount:           o.Size,
		Price:            o.Price,
		FilledAmount:     o.FilledSize,
		FilledMoneyValue: o.Size,
		AvgPrice:         o.Price,
		Fee:              o.TotalFees,
	}
}

func mapAPIOrderTypeToCommonSide(orderType string) gateway.Side {
	if orderType == "BUY" {
		return gateway.Bid
	}
	if orderType == "SELL" {
		return gateway.Ask
	}
	log.Printf("Coinbase invalid order side \"%s\"", orderType)
	return ""
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	accountBalance, err := g.api.AccountBalance()
	if err != nil {
		return nil, err
	}

	accBalances := make([]gateway.Balance, len(accountBalance.Accounts))
	for _, balance := range accountBalance.Accounts {
		accBalances = append(accBalances, gateway.Balance{
			Asset:     balance.Currency,
			Available: balance.Available,
			Total:     balance.Balance,
		})
	}

	return accBalances, err
}

func mapAPIOrderStateToCommon(st string) gateway.OrderState {
	switch st {
	case "OPEN":
		return gateway.OrderOpen
	case "FILLED":
		return gateway.OrderFullyFilled
	case "CANCELLED":
		return gateway.OrderCancelled
	case "EXPIRED":
		return gateway.OrderCancelled
	}
	return gateway.OrderUnknown
}

func (g *AccountGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()

	feedUrl := wsFeedURL(g.options)
	err := ws.Connect(feedUrl)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	symbols := make([]string, 0)
	for _, market := range markets {
		symbols = append(symbols, market.Symbol)
	}

	jtwToken, err := buildAPIJWT(g.options.ApiSecret, g.options.ApiKey, "")
	if err != nil {
		return err
	}

	accRequest := WsRequest{
		Type:       "subscribe",
		ProductIds: symbols,
		Channel:    "user",
		JWT:        jtwToken,
	}

	data, err := json.Marshal(accRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal account request, err: %s", err)
	}

	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write account msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ch)

	return nil
}

func (g *AccountGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		var msg WsResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarshal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		err = g.processMessageData(msg.Events)
		if err != nil {
			log.Printf("Failed to process message data [%s] err [%s]", string(msg.Events), err)
		}
	}
}

type WsOrderData struct {
	OrderID            string    `json:"order_id"`
	ClientOrderID      string    `json:"client_order_id"`
	CumulativeQuantity float64   `json:"cumulative_quantity,string"`
	LeavesQuantity     float64   `json:"leaves_quantity,string"`
	AvgPrice           float64   `json:"avg_price,string"`
	LimitPrice         float64   `json:"limit_price,string"`
	StopPrice          string    `json:"stop_price"`
	TotalFees          string    `json:"total_fees"`
	Status             string    `json:"status"`
	ProductID          string    `json:"product_id"`
	CreationTime       time.Time `json:"creation_time"`
	OrderSide          string    `json:"order_side"`
	OrderType          string    `json:"order_type"`
}

type WsUserResponse struct {
	Type   string        `json:"type"`
	Orders []WsOrderData `json:"orders"`
}

func (g *AccountGateway) processMessageData(data json.RawMessage) error {

	var msgs []WsUserResponse
	err := json.Unmarshal(data, &msgs)
	if err != nil {
		return fmt.Errorf("unmarhsal WsResponse [%s] err [%s]", string(data), err)
	}

	for _, msg := range msgs {
		for _, order := range msg.Orders {
			switch order.Status {
			case "FILLED":
				g.processFill(msg)
			case "CANCELLED":
				g.processDone(msg)
			}
		}
	}

	return nil
}

func (g *AccountGateway) processFill(order WsUserResponse) {
	var orderID string
	var side gateway.Side
	for _, o := range order.Orders {
		orderID = o.OrderID

		if o.OrderSide == "BUY" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		g.tickCh <- gateway.TickWithEvents([]gateway.Event{
			gateway.NewFillEvent(gateway.Fill{
				Timestamp: o.CreationTime,
				Symbol:    o.ProductID,
				OrderID:   orderID,
				Side:      side,
				Amount:    o.CumulativeQuantity,
				Price:     o.AvgPrice,
			}),
		}...)
	}
}

type WsUserOrderDone struct {
	ProductID string `json:"product_id"`
	OrderID   string `json:"order_id"`
	Reason    string `json:"reason"`
}

func (g *AccountGateway) processDone(order WsUserResponse) {
	for _, o := range order.Orders {
		g.tickCh <- gateway.TickWithEvents([]gateway.Event{
			gateway.NewOrderUpdateEvent(gateway.Order{
				ID:    o.OrderID,
				State: mapAPIOrderStateToCommon(o.Status),
			}),
		}...)
	}
}
