package coinbase

import (
	"fmt"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Coinbase",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(apiBaseProdAdvanced, options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(wsFeedURL(g.options), g.options, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return fmt.Errorf("Failed to subscribe to market data, err %s", err)
	}

	if g.options.ApiKey != "" {
		err = g.accountGateway.SubscribeMarkets(markets)
		if err != nil {
			return fmt.Errorf("Failed to subscribe to account updates, err %s", err)
		}
	}

	return nil
}

func (g *Gateway) Close() error {
	return gateway.NotImplementedErr
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.Markets()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, len(symbols.Products))
	for i, symbol := range symbols.Products {
		commonMarkets[i] = symbolToCommonMarket(symbol)
	}

	return commonMarkets, nil
}

func symbolToCommonMarket(market APIMarketProduct) gateway.Market {
	priceTick := market.QuoteIncrement
	amountTick := market.BaseIncrement

	return gateway.Market{
		Exchange: Exchange,
		Symbol:   market.ProductID,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(market.BaseCurrencyID),
			Quote: strings.ToUpper(market.QuoteCurrencyID),
		},
		Closed:                 market.Status != "online",
		TakerFee:               0.001,
		MakerFee:               0.001,
		PriceTick:              priceTick,
		AmountTick:             amountTick,
		MinimumOrderMoneyValue: 0,
		MarketType:             gateway.SpotMarket,
	}
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func parsePriceLevelsToDepth(levels []DepthValues) []gateway.PriceLevel {
	commonLevels := make([]gateway.PriceLevel, len(levels))
	for i, level := range levels {
		commonLevels[i] = gateway.PriceLevel{
			Price:  level.Price,
			Amount: level.Amount,
		}
	}

	return commonLevels
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := g.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks := parsePriceLevelsToDepth(depth.PriceBook.Asks)
	bids := parsePriceLevelsToDepth(depth.PriceBook.Bids)
	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}

func wsFeedURL(options gateway.Options) string {
	return "wss://advanced-trade-ws.coinbase.com"
}
