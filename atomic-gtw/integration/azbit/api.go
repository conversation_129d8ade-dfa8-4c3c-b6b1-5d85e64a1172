package azbit

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseProd       = "https://data.azbit.com"
	apiSymbols        = "/api/currencies/pairs"
	apiOrderBook      = "/api/orderbook"
	apiOpenOrders     = "/api/user/orders?status=active"
	apiGetDeals       = "/api/user/deals"
	apiPlaceOrder     = "/api/orders"
	apiCancelOrder    = "/api/orders"
	apiAccountBalance = "/api/wallets/balances"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
	baseURL    string
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

type AssetDetails struct {
	Code           string      `json:"code"`
	DigitsPrice    float64     `json:"digitsPrice"`
	DigitsAmount   float64     `json:"digitsAmount"`
	MinQuoteAmount interface{} `json:"minQuoteAmount"`
}

func (a *API) Symbols() (symbols []AssetDetails, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiSymbols, nil, false)
	if err != nil {
		return symbols, err
	}

	var listSymbols []AssetDetails
	_, err = a.makeHttpRequest(req, &listSymbols)
	if err != nil {
		return nil, err
	}

	return listSymbols, nil
}

type OrderBook struct {
	IsBid        bool    `json:"isBid"`
	Price        float64 `json:"price"`
	Amount       float64 `json:"amount"`
	CurrencyTo   float64 `json:"currencyTo"`
	QuoteAmount  float64 `json:"quoteAmount"`
	CurrencyFrom float64 `json:"currencyFrom"`
}

func (a *API) Market(symbol string) (books []OrderBook, err error) {
	url := fmt.Sprintf("%s?currencyPairCode=%s", apiOrderBook, symbol)
	req, err := a.newHttpRequest(http.MethodGet, url, nil, false)
	if err != nil {
		return books, err
	}

	_, err = a.makeHttpRequest(req, &books)
	if err != nil {
		return nil, err
	}

	return books, nil
}

type ApiBalance struct {
	Balances        []Balance `json:"balances"`
	BalancesBlocked []Balance `json:"balancesBlockedInOrder"`
}
type Balance struct {
	Amount         float64 `json:"amount"`
	AmountBtc      float64 `json:"amountBtc"`
	AmountUsdt     float64 `json:"amountUsdt"`
	CurrencyCode   string  `json:"currencyCode"`
	CurrencyName   string  `json:"currencyName"`
	Digits         float64 `json:"digits"`
	CurrencyIsFiat bool    `json:"currencyIsFiat"`
}

func (a *API) AccountBalance() (res ApiBalance, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiAccountBalance, nil, true)
	if err != nil {
		return res, err
	}

	_, err = a.makeHttpRequest(req, &res)
	if err != nil {
		return res, err
	}

	return res, err
}

type Order struct {
	Id               string  `json:"id"`
	IsBid            bool    `json:"isBid"`
	Price            float64 `json:"price"`
	InitialAmount    float64 `json:"initialAmount"`
	Amount           float64 `json:"amount"`
	CurrencyTo       float64 `json:"currencyTo"`
	QuoteAmount      float64 `json:"quoteAmount"`
	CurrencyFrom     float64 `json:"currencyFrom"`
	Date             string  `json:"date"`
	UserId           string  `json:"userId"`
	IsCanceled       bool    `json:"isCanceled"`
	Status           string  `json:"status"`
	CurrencyPairCode string  `json:"currencyPairCode"`
}

func (a *API) OpenOrders() (orders []Order, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiOpenOrders, nil, true)
	if err != nil {
		return orders, err
	}
	var listOpenOrder []Order

	_, err = a.makeHttpRequest(req, &listOpenOrder)
	if err != nil {
		return orders, err
	}

	return listOpenOrder, nil
}

type Fill struct {
	Id               string  `json:"id"`
	DealDateUtc      string  `json:"dealDateUtc"`
	CurrencyPairCode string  `json:"currencyPairCode"`
	Volume           float64 `json:"volume"`
	Price            float64 `json:"price"`
	IsBuy            bool    `json:"isBuy"`
	IsUserBuyer      bool    `json:"isUserBuyer"`
	OrderId          string  `json:"orderId"`
}

func (a *API) GetFills(startDate time.Time) (fills []Fill, err error) {
	sinceDate := startDate.UTC().Format("2006-01-02T15:04:05")
	url := fmt.Sprintf("%s?sinceDate=%s", apiGetDeals, sinceDate)
	req, err := a.newHttpRequest(http.MethodGet, url, nil, true)
	if err != nil {
		return fills, err
	}

	_, err = a.makeHttpRequest(req, &fills)
	if err != nil {
		return fills, err
	}

	return fills, nil
}

type OrderRequest struct {
	IsBid            bool    `json:"isBid"`
	CurrencyPairCode string  `json:"currencyPairCode"`
	Amount           float64 `json:"amount"`
	Price            float64 `json:"price"`
}

func (a *API) PlaceOrder(order io.Reader) (orderID string, err error) {
	req, err := a.newHttpRequest(http.MethodPost, apiPlaceOrder, order, true)
	if err != nil {
		return orderID, err
	}
	body, err := a.makeHttpRequest(req, nil)
	if err != nil {
		return "", fmt.Errorf("error place order: %s", err)
	}
	if len(body) != 36 {
		return "", fmt.Errorf("balance not enough")
	}
	orderID = string(body)
	return orderID, nil

}

func (a *API) CancelOrder(orderID string) (err error) {
	url := fmt.Sprintf("%s/%s", apiCancelOrder, orderID)
	req, err := a.newHttpRequest(http.MethodDelete, url, nil, true)
	if err != nil {
		return err
	}

	_, err = a.makeHttpRequest(req, nil)
	if err != nil {
		err = fmt.Errorf("error cancel order: %s", err)
		return err
	}

	return nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) (body []byte, err error) {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return nil, err
	}

	body, err = ioutil.ReadAll(res.Body)
	if err != nil {
		return body, err
	}

	if responseObject != nil {
		err = json.Unmarshal(body, responseObject)
		if err != nil {
			return body, fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return body, nil
}

func (a *API) getSignature(signatureText string) string {
	h := hmac.New(sha256.New, []byte(a.options.ApiSecret))
	h.Write([]byte(signatureText))
	signature := hex.EncodeToString(h.Sum(nil))
	return signature
}

func (a *API) newHttpRequest(method string, path string, data io.Reader, signed bool) (*http.Request, error) {
	urlRequest := a.baseURL + path
	req, err := http.NewRequest(method, urlRequest, data)
	if err != nil {
		return nil, err
	}

	if signed {
		fullUrl := a.baseURL + path
		signatureText := a.options.ApiKey
		signatureText += fullUrl
		var reqBody []byte
		if req.Body != nil {
			reqBody, err = ioutil.ReadAll(req.Body)
			if err != nil {
				return nil, fmt.Errorf("failed to read req body for sign data, err: %s", err)
			}

			// Put back the body into the request
			req.Body = ioutil.NopCloser(bytes.NewReader(reqBody))
		}

		signatureText += string(reqBody)
		signature := a.getSignature(signatureText)
		req.Body = ioutil.NopCloser(bytes.NewBuffer(reqBody))
		req.Header.Set("API-PublicKey", a.options.ApiKey)
		req.Header.Set("API-Signature", signature)
	}
	req.Header.Set("Content-Type", "application/json")

	return req, nil
}
