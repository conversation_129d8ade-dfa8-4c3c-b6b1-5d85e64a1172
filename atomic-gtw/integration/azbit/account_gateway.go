package azbit

import (
	"bytes"
	"encoding/json"
	"log"
	"regexp"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

type AccountGateway struct {
	base.AccountGateway
	api       *API
	options   gateway.Options
	tickCh    chan gateway.Tick
	baseUrlWs string
	startDate time.Time
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick, baseUrlWs string) *AccountGateway {
	return &AccountGateway{
		api:       api,
		options:   options,
		tickCh:    tickCh,
		baseUrlWs: baseUrlWs,
		startDate: time.Now(),
	}
}

func (g *AccountGateway) Connect() error {
	go g.trackFillsUpdates()

	return nil
}

func (g *AccountGateway) trackFillsUpdates() {
	pollInterval := 5 * time.Second
	log.Printf("Azbit polling for account executions every %v", pollInterval)

	for {
		fills, err := g.api.GetFills(g.startDate)
		if err != nil {
			log.Printf("Azbit account gateway failed to fetch fills, err: %s", err)
			time.Sleep(pollInterval)
			continue
		}

		if len(fills) == 0 {
			time.Sleep(pollInterval)
			continue
		}

		events := make([]gateway.Event, 0)

		var lastDealTime time.Time
		for _, fill := range fills {
			dealDateTime, err := time.Parse("2006-01-02T15:04:05.9999999", fill.DealDateUtc)
			if err != nil {
				log.Printf("Azbit account gateway failed to parse deal date, err: %s", err)
				continue
			}

			if dealDateTime.After(lastDealTime) {
				lastDealTime = dealDateTime
			}

			if dealDateTime.After(g.startDate) {
				fillEvent := gateway.Fill{
					ID:        fill.Id,
					Timestamp: dealDateTime,
					OrderID:   fill.OrderId,
					Symbol:    fill.CurrencyPairCode,
					Amount:    fill.Volume,
					Price:     fill.Price,
				}

				events = append(events, gateway.Event{
					Type: gateway.FillEvent,
					Data: fillEvent,
				})
			}
		}

		if lastDealTime.After(g.startDate) {
			g.startDate = lastDealTime
		}

		if len(events) > 0 {
			g.tickCh <- gateway.Tick{
				ReceivedTimestamp: time.Now(),
				EventLog:          events,
			}
		}

		time.Sleep(pollInterval)
	}
}

var insuficientBalanceMatch = regexp.MustCompile(`balance not enough`)

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	var data OrderRequest
	data.Amount = order.Amount
	data.Price = order.Price
	data.CurrencyPairCode = order.Market.Symbol
	if order.Side == gateway.Bid {
		data.IsBid = true
	} else if order.Side == gateway.Ask {
		data.IsBid = false
	}

	dataBytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	orderDataBuffer := bytes.NewBuffer(dataBytes)
	orderID, err := g.api.PlaceOrder(orderDataBuffer)
	if err != nil {
		switch {
		case insuficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr

		default:
			return orderId, err
		}
	}
	return orderID, nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID)
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	accountBalance, err := g.api.AccountBalance()
	if err != nil {
		return balances, err
	}

	lockedBalances := make(map[string]float64)
	for _, balance := range accountBalance.BalancesBlocked {
		lockedBalances[balance.CurrencyCode] += balance.Amount
	}

	balances = make([]gateway.Balance, 0)
	for _, balance := range accountBalance.Balances {
		lockedAmount := lockedBalances[balance.CurrencyCode]

		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(balance.CurrencyCode),
			Available: balance.Amount,
			Total:     balance.Amount + lockedAmount,
		})
	}

	return
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrders, err := g.api.OpenOrders()
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders))
	for _, order := range openOrders {
		orders = append(orders, mapAPIOrderToCommon(order, order.Id, market))
	}

	return
}

func mapAPIOrderToCommon(o Order, id string, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market: market,
		ID:     id,
		Side:   mapAPIOrderTypeToCommonSide(o.IsBid),
		State:  mapAPIOrderStateToCommon(o.Status),
		Amount: o.Amount,
		Price:  o.Price,
	}
}

func mapAPIOrderTypeToCommonSide(orderType bool) gateway.Side {
	if orderType {
		return gateway.Bid
	} else {
		return gateway.Ask
	}
}
