package azbit

import (
	"fmt"
	"math"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Azbit",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	tickCh            chan gateway.Tick
	api               *API
	marketDataGateway *MarketDataGateway
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(apiBaseProd, options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh, "wsURL")
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("account gtw connect: %s", err)
		}
	}

	g.marketDataGateway = NewMarketDataGateway(g.options, g.api, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return gateway.NotImplementedErr
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.Symbols()
	if err != nil {
		return nil, fmt.Errorf("failed to get markets, err %s", err)
	}

	commonMarkets := make([]gateway.Market, 0, len(symbols))
	for _, symbol := range symbols {
		market := symbolToCommonMarket(symbol)
		commonMarkets = append(commonMarkets, market)

	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func symbolToCommonMarket(symbol AssetDetails) gateway.Market {
	priceTick := 1 / math.Pow10(int(symbol.DigitsPrice))
	amountTick := 1 / math.Pow10(int(symbol.DigitsAmount))

	baseQuote := strings.Split(symbol.Code, "_")
	base := baseQuote[0]
	quote := baseQuote[1]
	return gateway.Market{
		Exchange: Exchange,
		Symbol:   symbol.Code,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(base),
			Quote: strings.ToUpper(quote),
		},
		TakerFee:   0.001,
		MakerFee:   0.001,
		PriceTick:  priceTick,
		AmountTick: amountTick,
		MarketType: gateway.SpotMarket,
	}
}

func mapAPIOrderStateToCommon(st string) gateway.OrderState {
	switch st {
	case "Created":
		return gateway.OrderOpen
	case "Expired":
		return gateway.OrderCancelled
	case "Canceled":
		return gateway.OrderCancelled
	case "Closed":
		return gateway.OrderClosed
	}
	return gateway.OrderUnknown
}
