package onetrading

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api     *API
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		api:     api,
		options: options,
		tickCh:  tickCh,
	}
}

type WsTradingChannelSub struct {
	Type     string `json:"type"`
	Channels []struct {
		Name string `json:"name"`
	} `json:"channels"`
}

type WsAuthenticateSub struct {
	Type     string `json:"type"`
	APIToken string `json:"api_token"`
}

func (g *AccountGateway) Connect() error {
	ws := utils.NewWsClient()
	err := ws.Connect("wss://streams.fast.onetrading.com?x-version=3")
	if err != nil {
		return fmt.Errorf("ws connect: %w", err)
	}

	wsAuthMessage := WsAuthenticateSub{
		Type:     "AUTHENTICATE",
		APIToken: g.options.Token,
	}

	wsAuthToBytes, err := json.Marshal(wsAuthMessage)
	if err != nil {
		return fmt.Errorf("ws auth json marshal: %w", err)
	}

	if err := ws.WriteMessage(wsAuthToBytes); err != nil {
		return fmt.Errorf("ws auth write: %w", err)
	}

	authCh := make(chan []byte, 100)
	ws.SubscribeMessages(authCh)
	defer ws.RemoveSubscriber(authCh)

	isAuthenticated, err := g.wsAuthMessageHandler(authCh)
	if err != nil {
		return fmt.Errorf("ws auth message handler: %w", err)
	}
	if !isAuthenticated {
		return fmt.Errorf("ws auth failed")
	}

	wsTradingChannelMessage := WsTradingChannelSub{
		Type: "SUBSCRIBE",
		Channels: []struct {
			Name string `json:"name"`
		}{
			{
				Name: "TRADING",
			},
		},
	}

	wsTradingChannelToBytes, err := json.Marshal(wsTradingChannelMessage)
	if err != nil {
		return fmt.Errorf("ws trading json marshal: %w", err)
	}

	if err := ws.WriteMessage(wsTradingChannelToBytes); err != nil {
		return fmt.Errorf("ws trading write: %w", err)
	}

	if err := startWsPinger(ws, "AccountGateway"); err != nil {
		return fmt.Errorf("failed to start websocket pinger, err: %s", err)
	}

	tradingCh := make(chan []byte, 100)
	ws.SubscribeMessages(tradingCh)
	go func(ch chan []byte) {
		for msg := range tradingCh {
			var tradingUpdate TradingOrderFill
			err = json.Unmarshal(msg, &tradingUpdate)
			if err != nil {
				log.Printf("failed to unmarshal OneTrading user stream order update error %s", err)
				continue
			}

			if tradingUpdate.Type == "FILL" {
				g.processFillEvent(tradingUpdate)
			}
		}
	}(tradingCh)

	return nil
}

type WsAuthResponse struct {
	Error       string `json:"error"`
	AccountID   string `json:"account_id"`
	ChannelName string `json:"channel_name"`
	Type        string `json:"type"`
}

func (g *AccountGateway) wsAuthMessageHandler(ch chan []byte) (bool, error) {
	msg := <-ch
	var authResponse WsAuthResponse
	err := json.Unmarshal(msg, &authResponse)
	if err != nil {
		return false, err
	}
	if authResponse.Type == "AUTHENTICATED" {
		return true, nil
	} else {
		return false, fmt.Errorf("ws auth type not authenticated, instead: %s, error: %s", authResponse.Type, authResponse.Error)
	}
}

type TradingOrderFill struct {
	Status            string  `json:"status"`
	OrderBookSequence int     `json:"order_book_sequence"`
	Side              string  `json:"side"`
	MatchedPrice      float64 `json:"matched_price,string"`
	MatchedAmount     float64 `json:"matched_amount,string"`
	MatchedAs         string  `json:"matched_as"`
	InstrumentCode    string  `json:"instrument_code"`
	OrderID           string  `json:"order_id"`
	TradeID           string  `json:"trade_id"`
	Remaining         float64 `json:"remaining,string"`
	ChannelName       string  `json:"channel_name"`
	Type              string  `json:"type"`
	Time              int64   `json:"time"`
}

func (g *AccountGateway) processFillEvent(fill TradingOrderFill) {
	eventLog := make([]gateway.Event, 0, 1)
	var side gateway.Side
	if fill.Side == "BUY" {
		side = gateway.Bid
	} else {
		side = gateway.Ask
	}
	orderID := fill.OrderID
	event := gateway.Event{
		Type: gateway.FillEvent,
		Data: gateway.Fill{
			ID:        fill.TradeID,
			OrderID:   orderID,
			Timestamp: gateway.ParseTimestamp(fill.Time),
			Side:      side,
			Symbol:    fill.InstrumentCode,
			Price:     fill.MatchedPrice,
			Amount:    fill.MatchedAmount,
		},
	}
	eventLog = append(eventLog, event)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	accountBalance, err := g.api.AccountBalance()
	if err != nil {
		return balances, err
	}

	balances = make([]gateway.Balance, 0, len(accountBalance.Balances))
	for _, balance := range accountBalance.Balances {
		balances = append(balances, gateway.Balance{
			Asset:     balance.CurrencyCode,
			Available: balance.Available,
			Total:     balance.Available + balance.Locked,
		})
	}

	return
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrders, err := g.api.OpenOrders(market.Symbol)
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders.OrderHistory))
	for _, order := range openOrders.OrderHistory {
		orders = append(orders, mapAPIOrderToCommon(order.Order, market))
	}

	return
}

func mapAPIOrderToCommon(o Order, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market: market,
		ID:     o.OrderID,
		Side:   mapAPIOrderTypeToCommonSide(o.Side),
		State:  mapOrderStateToCommon(o.Status),
		Amount: o.Amount,
		Price:  o.Price,
	}
}

func mapOrderStateToCommon(status string) gateway.OrderState {
	switch status {
	case "OPEN":
		return gateway.OrderOpen
	case "FILLED_CLOSED", "FILLED_REJECTED", "REJECTED":
		return gateway.OrderCancelled
	case "FILLED":
		return gateway.OrderFullyFilled
	case "FAILED":
		return gateway.OrderClosed
	default:
		return gateway.OrderUnknown
	}
}

func mapAPIOrderTypeToCommonSide(orderSide string) gateway.Side {
	if orderSide == "BUY" {
		return gateway.Bid
	} else if orderSide == "SELL" {
		return gateway.Ask
	} else {
		log.Printf("OneTrading invalid order side \"%s\"", orderSide)
		return ""
	}
}

var insuficientBalanceMatch = regexp.MustCompile(`INSUFFICIENT_FUNDS`)
var rateLimitMatch = regexp.MustCompile(`RATELIMIT`)

func (g *AccountGateway) SendOrder(order gateway.Order) (string, error) {
	var side string
	if order.Side == gateway.Bid {
		side = "BUY"
	} else {
		side = "SELL"
	}

	newOrder := NewOrderBody{
		InstrumentCode: order.Market.Symbol,
		Side:           side,
		OrderType:      "LIMIT",
		Amount:         utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		Price:          utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
		TimeInForce:    "GOOD_TILL_CANCELLED",
		IsPostOnly:     order.PostOnly,
	}
	orderID, err := g.api.NewOrder(newOrder)
	if err != nil {
		switch {
		case insuficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		case rateLimitMatch.MatchString(err.Error()):
			return "", gateway.RateLimitErr
		default:
			return "", err
		}
	}

	return orderID, nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID)
}
