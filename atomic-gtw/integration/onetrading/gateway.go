package onetrading

import (
	"fmt"
	"math"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "OneTrading",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(apiBaseProd, options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	if g.options.Token != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to order entry gateway, err %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return err
	}

	return nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	exchangeInfo, err := g.api.Symbols()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0)
	for _, symbol := range exchangeInfo {
		market := symbolToCommonMarket(symbol)
		commonMarkets = append(commonMarkets, market)
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func symbolToCommonMarket(symbol AssetDetails) gateway.Market {
	priceTick := 1 / math.Pow10(int(symbol.MarketPrecision))
	amountTick := 1 / math.Pow10(int(symbol.AmountPrecision))
	exchangeSymbol := symbol.Base.Code + "_" + symbol.Quote.Code

	return gateway.Market{
		Exchange: Exchange,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(symbol.Base.Code),
			Quote: strings.ToUpper(symbol.Quote.Code),
		},
		Symbol:     exchangeSymbol,
		TakerFee:   0.002,
		MakerFee:   0.002,
		PriceTick:  priceTick,
		AmountTick: amountTick,
	}
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	var bids []gateway.PriceLevel
	var asks []gateway.PriceLevel
	depth, err := g.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	for _, bid := range depth.Bids {
		bids = append(bids, gateway.PriceLevel{
			Price:  bid.Price,
			Amount: bid.Amount,
		})
	}
	for _, ask := range depth.Asks {
		asks = append(asks, gateway.PriceLevel{
			Price:  ask.Price,
			Amount: ask.Amount,
		})
	}

	return gateway.DepthBook{
		Bids: bids,
		Asks: asks,
	}, nil
}
