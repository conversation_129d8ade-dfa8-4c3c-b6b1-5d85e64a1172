package onetrading

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/gorilla/websocket"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	tickCh chan gateway.Tick
}

func NewMarketDataGateway(tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		tickCh: tickCh,
	}
}

type NewWsMessage struct {
	Type     string      `json:"type"`
	Channels []WsChannel `json:"channels"`
}

type WsChannel struct {
	Name            string   `json:"name"`
	InstrumentCodes []string `json:"instrument_codes"`
	Depth           int      `json:"depth"`
}

func (mg *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	log.Printf("%s subscribing to %d markets...", Exchange.Name, len(markets))
	ws := utils.NewWsClient()
	err := ws.Connect("wss://streams.fast.onetrading.com?x-version=3")
	if err != nil {
		return fmt.Errorf("connect to market data err: %s", err)
	}

	instrumentCodes := make([]string, 0, len(markets))
	for _, market := range markets {
		instrumentCodes = append(instrumentCodes, market.Symbol)
	}

	sendWsSubRequest := func(channelName string, instrumentCodes []string, depth int) error {
		wsMessage := NewWsMessage{
			Type: "SUBSCRIBE",
			Channels: []WsChannel{
				{
					Name:            channelName,
					InstrumentCodes: instrumentCodes,
					Depth:           depth,
				},
			},
		}
		msgData, err := json.Marshal(wsMessage)
		if err != nil {
			return fmt.Errorf("marshal ws message err: %s", err)
		}

		if err := ws.WriteMessage(msgData); err != nil {
			return fmt.Errorf("write ws message err: %s", err)
		}

		return nil
	}

	if err := sendWsSubRequest("PRICE_TICKS", instrumentCodes, 0); err != nil {
		return fmt.Errorf("%s: %x", "PRICE TICKS SUB", err)
	}

	if err := sendWsSubRequest("ORDER_BOOK", instrumentCodes, 0); err != nil {
		return fmt.Errorf("%s: %x", "ORDER BOOK SUB", err)
	}

	if err := startWsPinger(ws, "MarketDataGateway"); err != nil {
		return fmt.Errorf("failed to start websocket pinger, err: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go mg.messageHandler(ch)

	return nil
}

type HeartbeatStatus struct {
	Subscription string `json:"subscription"`
	ChannelName  string `json:"channel_name"`
	Type         string `json:"type"`
	Time         int64  `json:"time"`
}

func startWsPinger(ws *utils.WsClient, origin string) error {
	interval := 10 * time.Second
	ticker := time.NewTicker(interval)

	go func() {
		for {
			<-ticker.C
			if err := ws.WriteRawMessage(websocket.PingMessage, []byte(`{"type":"HEARTBEAT"}`)); err != nil {
				panic(fmt.Errorf("%s ws [%s] pinger write err: %s", Exchange.Name, origin, err))
			}
		}
	}()

	return nil
}

// This is only used when connecting to the garcon endpoint at:
// wss://garcon.fast.onetrading.com
func startWsHeartbeat(ws *utils.WsClient, origin string) error {
	timeout := 20 * time.Second
	heartbeatTicker := time.NewTicker(timeout)
	ch := make(chan []byte)
	ws.SubscribeMessages(ch)

	go func() {
		for {
			select {
			case <-heartbeatTicker.C:
				panic(fmt.Errorf("%s ws [%s] heartbeat timeout after %s without receiving a heartbeat", Exchange.Name, origin, timeout))
			case _ = <-ch:
				// Any received msg counts as a heartbeat
				heartbeatTicker.Reset(timeout)
			}
		}
	}()

	return nil
}

type WsBaseResponse struct {
	Type        string `json:"type"`
	ChannelName string `json:"channel_name"`
}

type WsDepthResponse struct {
	InstrumentCode string               `json:"instrument_code"`
	Changes        [][]string           `json:"changes"`
	Bids           []gateway.PriceArray `json:"bids"`
	Asks           []gateway.PriceArray `json:"asks"`
	Time           int64                `json:"time"`
}

type WsTickHistoryResponse struct {
	InstrumentCode string           `json:"instrument_code"`
	History        []WsTickResponse `json:"history"`
}

type WsTickResponse struct {
	Sequence       int64   `json:"sequence"`
	BestAsk        string  `json:"best_ask"`
	BestBid        string  `json:"best_bid"`
	InstrumentCode string  `json:"instrument_code"`
	Price          float64 `json:"price,string"`
	Amount         float64 `json:"amount,string"`
	TakerSide      string  `json:"taker_side"`
	Time           int64   `json:"time"`
	Volume         string  `json:"volume"`
}

func (mg *MarketDataGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		var msg WsBaseResponse
		if err := json.Unmarshal(data, &msg); err != nil {
			log.Printf("%s market data unmarshal err: %s", Exchange.Name, err)
			continue
		}

		switch msg.ChannelName {
		case "PRICE_TICKS":
			if msg.Type == "PRICE_TICK" {
				var tick WsTickResponse
				if err := json.Unmarshal(data, &tick); err != nil {
					log.Printf("%s tick data unmarshal err: %s", Exchange.Name, err)
					continue
				}

				mg.processPriceTicks(tick.InstrumentCode, []WsTickResponse{tick})
			} else if msg.Type == "PRICE_TICK_HISTORY" {
				var tickHistory WsTickHistoryResponse
				if err := json.Unmarshal(data, &tickHistory); err != nil {
					log.Printf("%s tick history data unmarshal err: %s", Exchange.Name, err)
					continue
				}

				mg.processPriceTicks(tickHistory.InstrumentCode, tickHistory.History)
			}
		case "ORDER_BOOK":
			var depth WsDepthResponse
			if err := json.Unmarshal(data, &depth); err != nil {
				log.Printf("%s depth data unmarshal err: %s", Exchange.Name, err)
				continue
			}

			if msg.Type == "ORDER_BOOK_SNAPSHOT" {
				mg.processDepthEvent(depth.InstrumentCode, depth.Bids, depth.Asks, true)
			} else if msg.Type == "ORDER_BOOK_UPDATE" {
				bids := make([]gateway.PriceArray, 0)
				asks := make([]gateway.PriceArray, 0)
				for _, change := range depth.Changes {
					if len(change) >= 3 {
						price := change[1]
						amount := change[2]
						priceToFloat, err := strconv.ParseFloat(price, 64)
						if err != nil {
							log.Printf("%s market data unmarshal err: %s", Exchange.Name, err)
							continue
						}

						amountToFloat, err := strconv.ParseFloat(amount, 64)
						if err != nil {
							log.Printf("%s market data unmarshal err: %s", Exchange.Name, err)
							continue
						}

						if change[0] == "BUY" {
							bids = append(bids, gateway.PriceArray{Price: priceToFloat, Amount: amountToFloat})
						} else {
							asks = append(asks, gateway.PriceArray{Price: priceToFloat, Amount: amountToFloat})
						}
					}
				}

				mg.processDepthEvent(depth.InstrumentCode, bids, asks, false)
			}
		}
	}
}

func (mg *MarketDataGateway) processDepthEvent(symbol string, bids []gateway.PriceArray, asks []gateway.PriceArray, snapshot bool) {
	eventLog := make([]gateway.Event, 0, len(bids)+len(asks)+1)
	appendEvents := func(symbol string, side gateway.Side, prices []gateway.PriceArray) {
		for _, order := range prices {
			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  order.Price,
					Amount: order.Amount,
				},
			}
			eventLog = append(eventLog, event)
		}
	}

	if snapshot {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: symbol,
			},
		})
	}

	appendEvents(symbol, gateway.Ask, asks)
	appendEvents(symbol, gateway.Bid, bids)

	mg.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

func (mg *MarketDataGateway) processPriceTicks(symbol string, ticks []WsTickResponse) {
	eventLog := make([]gateway.Event, 0, len(ticks))
	for _, tick := range ticks {
		id := ""
		// Leave trade id empty if not present, because the "sequence" is not
		// documented on their docs as a valid field, although it is present.
		// So if we leave it empty, we will indicate that the trade id should
		// not be used, and be generated instead.
		if tick.Sequence > 0 {
			id = strconv.FormatInt(tick.Sequence, 10)
		}

		eventLog = append(eventLog, gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				ID:        id,
				Symbol:    symbol,
				Direction: gateway.StringToSide(tick.TakerSide),
				Price:     tick.Price,
				Amount:    tick.Amount,
				Timestamp: gateway.ParseTimestamp(tick.Time),
			},
		})
	}

	mg.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}
