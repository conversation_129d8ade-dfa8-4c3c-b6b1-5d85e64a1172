package onetrading

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseProd       = "https://api.onetrading.com/fast/v1"
	apiSymbols        = "/instruments"
	apiAccountBalance = "/account/balances"
	apiOrderAction    = "/account/orders"
	apiDepthBook      = "/order-book/%s?depth=%d"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
	baseURL    string
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

type APIResponse struct {
	Code string          `json:"code"`
	Msg  string          `json:"msg"`
	Data json.RawMessage `json:"data"`
}

type BaseQuoteData struct {
	Code      string `json:"code"`
	Precision int    `json:"precision"`
}

type AssetDetails struct {
	State           string        `json:"state"`
	Base            BaseQuoteData `json:"base"`
	Quote           BaseQuoteData `json:"quote"`
	AmountPrecision int           `json:"amount_precision"`
	MarketPrecision int           `json:"market_precision"`
	MinSize         string        `json:"min_size"`
}

func (a *API) Symbols() (symbols []AssetDetails, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiSymbols, nil, nil, false)
	if err != nil {
		return symbols, err
	}

	err = a.makeHttpRequest(req, &symbols)
	if err != nil {
		return nil, err
	}

	return symbols, nil
}

type Balance struct {
	AccountID    string  `json:"account_id"`
	CurrencyCode string  `json:"currency_code"`
	Available    float64 `json:"available,string"`
	Locked       float64 `json:"locked,string"`
}

type AccountBalance struct {
	AccountID string    `json:"account_id"`
	Balances  []Balance `json:"balances"`
}

func (a *API) AccountBalance() (balances AccountBalance, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiAccountBalance, nil, nil, true)
	if err != nil {
		return balances, err
	}

	err = a.makeHttpRequest(req, &balances)
	if err != nil {
		return balances, err
	}

	return balances, nil
}

type Order struct {
	OrderID        string    `json:"order_id"`
	InstrumentCode string    `json:"instrument_code"`
	Time           time.Time `json:"time"`
	Side           string    `json:"side"`
	Price          float64   `json:"price,string"`
	Amount         float64   `json:"amount,string"`
	FilledAmount   string    `json:"filled_amount"`
	Type           string    `json:"type"`
	Status         string    `json:"status"`
}

type OrderHistory struct {
	Order Order `json:"order"`
}

type OpenOrders struct {
	OrderHistory []OrderHistory `json:"order_history"`
	MaxPageSize  int            `json:"max_page_size"`
}

func (a *API) OpenOrders(symbol string) (orders OpenOrders, err error) {
	params := url.Values{}
	params.Add("instrument_code", symbol)

	req, err := a.newHttpRequest(http.MethodGet, apiOrderAction, nil, &params, true)
	if err != nil {
		return orders, err
	}

	err = a.makeHttpRequest(req, &orders)
	if err != nil {
		return orders, err
	}

	return orders, nil
}

type NewOrderRes struct {
	OrderID string `json:"order_id"`
}

type NewOrderBody struct {
	InstrumentCode string `json:"instrument_code"`
	Side           string `json:"side"`
	OrderType      string `json:"type"`
	Amount         string `json:"amount"`
	Price          string `json:"price"`
	TimeInForce    string `json:"time_in_force"`
	IsPostOnly     bool   `json:"is_post_only"`
}

func (a *API) NewOrder(order NewOrderBody) (string, error) {
	jsonData, err := json.Marshal(order)
	if err != nil {
		return "", err
	}

	req, err := a.newHttpRequest(http.MethodPost, apiOrderAction, bytes.NewReader(jsonData), nil, true)
	if err != nil {
		return "", err
	}

	var orderRes NewOrderRes
	err = a.makeHttpRequest(req, &orderRes)
	if err != nil {
		return "", err
	}

	return orderRes.OrderID, nil
}

func (a *API) CancelOrder(orderID string) (err error) {
	data := struct {
		OrderID string `json:"order_id"`
	}{
		OrderID: orderID,
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	req, err := a.newHttpRequest(http.MethodDelete, apiOrderAction, bytes.NewReader(jsonData), nil, true)
	if err != nil {
		return err
	}

	var response []string
	err = a.makeHttpRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

type Depth struct {
	Price  float64 `json:"price,string"`
	Amount float64 `json:"amount,string"`
}

type APIDepthBook struct {
	Bids []Depth `json:"bids"`
	Asks []Depth `json:"asks"`
}

func (a *API) DepthBook(symbol string, params gateway.GetDepthParams) (depth APIDepthBook, err error) {
	req, err := a.newHttpRequest(http.MethodGet, fmt.Sprintf(apiDepthBook, symbol, params.Limit), nil, nil, false)
	if err != nil {
		return depth, err
	}

	err = a.makeHttpRequest(req, &depth)
	if err != nil {
		return depth, err
	}

	return depth, nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}

	// Check response status code valid range
	if res.StatusCode < 200 || res.StatusCode >= 300 {
		return fmt.Errorf("invalid status code: %d, body: %s", res.StatusCode, string(body))
	}

	err = json.Unmarshal(body, responseObject)
	if err != nil {
		return fmt.Errorf("unmarshal err [%s] body:\n%s", err, string(body))
	}

	return nil
}

func (a *API) newHttpRequest(method string, path string, data io.Reader, params *url.Values, signed bool) (*http.Request, error) {
	urlRequest := a.baseURL + path
	req, err := http.NewRequest(method, urlRequest, data)
	if err != nil {
		return nil, err
	}

	if params != nil {
		req.URL.RawQuery = params.Encode()
	}

	req.Header.Set("Content-Type", "application/json")
	if signed {
		bearerToken := "Bearer " + a.options.Token
		req.Header.Add("Authorization", bearerToken)
	}

	return req, nil
}
