package bit

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api        *API
	options    gateway.Options
	tickCh     chan gateway.Tick
	baseUrlWs  string
	symbolsMap map[string]SymbolDetails
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick, baseUrlWs string, symbolsMap map[string]SymbolDetails) *AccountGateway {
	return &AccountGateway{
		api:        api,
		options:    options,
		tickCh:     tickCh,
		baseUrlWs:  baseUrlWs,
		symbolsMap: symbolsMap,
	}
}

func (g *AccountGateway) Connect() error {
	ws := utils.NewWsClient()

	err := ws.Connect(g.baseUrlWs)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}
	token, err := g.api.GetWebsocketToken()
	if err != nil {
		return fmt.Errorf("failed to get ws token, err: %s", err)
	}
	authRequest := WsAuthRequest{
		Type:     "subscribe",
		Channels: []string{"order"},
		Interval: "raw",
		Token:    token,
	}

	dataAuthRequest, err := json.Marshal(authRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}
	if err = ws.WriteMessage(dataAuthRequest); err != nil {
		return fmt.Errorf("failed write account msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	authErr := make(chan error)
	go func() {
		msg := <-ch

		var err error
		var res WsResponse
		if unmarshalErr := json.Unmarshal(msg, &res); err != nil {
			err = fmt.Errorf("failed to unmarshal account response, err: %s", unmarshalErr)
		}

		if res.Channel == "error" {
			err = fmt.Errorf("failed to subscribe to account, err: %s", res.Module)
		}

		authErr <- err
	}()

	select {
	case err := <-authErr:
		if err != nil {
			closeErr := ws.Close()
			if closeErr != nil {
				log.Printf("Failed to close ws connection after auth err: %s, authErr: %s", closeErr, err)
			}

			return fmt.Errorf("auth err: %s", err)
		}
	case <-time.After(5 * time.Second):
		err := ws.Close()
		if err != nil {
			log.Printf("Failed to close ws connection after timeout: %s", err)
		}

		return fmt.Errorf("Timed out waiting for auth response")
	}
	pongCh := make(chan bool)

	go g.messageHandler(ch, pongCh)
	go websocketPinger(ws, pongCh)

	return nil
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	accountBalance, err := g.api.AccountBalance()
	if err != nil {
		return balances, err
	}

	balances = make([]gateway.Balance, 0, len(accountBalance))
	for _, balance := range accountBalance {
		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(balance.Currency),
			Available: balance.AvailableBalance,
			Total:     balance.MarginBalance,
		})
	}

	return
}

type RequestOpenOrder struct {
	Pair string `json:"pair"`
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	params := map[string]interface{}{
		"pair": g.symbolsMap[market.Symbol].Pair,
	}
	openOrders, err := g.api.OpenOrders(params)
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders))
	for _, order := range openOrders {
		orders = append(orders, mapAPIOrderToCommon(order, market))
	}

	return
}

func mapAPIOrderToCommon(o OpenOrders, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market: market,
		ID:     o.OrderId,
		Side:   mapAPIOrderTypeToCommonSide(o.Side),
		State:  mapAPIOrderStateToCommon(o.Status),
		Amount: o.Qty,
		Price:  o.Price,
	}
}

func mapAPIOrderTypeToCommonSide(orderType string) gateway.Side {
	if orderType == "buy" {
		return gateway.Bid
	} else if orderType == "sell" {
		return gateway.Ask
	} else {
		log.Printf("bit invalid order side \"%s\"", orderType)
		return ""
	}
}

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	var side string
	if order.Side == gateway.Bid {
		side = "buy"
	} else {
		side = "sell"
	}
	symbol := g.symbolsMap[order.Market.Symbol].Pair

	params := map[string]interface{}{
		"pair":              symbol,
		"side":              side,
		"time_in_force":     "gtc",
		"qty":               utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		"price":             utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
		"mmp":               false,
		"self_trading_mode": 0,
	}

	if order.PostOnly {
		params["post_only"] = true
	}

	orderID, err := g.api.PlaceOrder(params)
	if err != nil {
		return orderId, err
	}

	return orderID, err
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	symbol := g.symbolsMap[order.Market.Symbol].Pair
	return g.api.CancelOrder(order.ID, symbol)
}

type WsAuthRequest struct {
	Type     string   `json:"type"`
	Channels []string `json:"channels"`
	Interval string   `json:"interval"`
	Token    string   `json:"token"`
}

func (g *AccountGateway) SubscribeMarkets(markets []gateway.Market) error {
	return nil
}

func (g *AccountGateway) messageHandler(ch chan []byte, pongCh chan bool) {
	for data := range ch {
		var msg WsResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		if string(data) == "pong" {
			pongCh <- true
			continue
		}

		switch msg.Channel {
		case "error":
			log.Printf("WS [%s] msg w/ error msg: %s", g.baseUrlWs, string(data))
			continue
		case "order":
			if err := g.processTradeUpdates(msg.Data); err != nil {
				log.Printf("%s error processing matches \"%s\": %s", g.baseUrlWs, data, err)
			}
		default:
			log.Printf("Bit unprocessable message type [%s], data [%s]", msg.Data, string(data))
		}
	}
}

type WsUserOrder struct {
	OrderId      string  `json:"order_id"`
	CreatedAt    int64   `json:"created_at"`
	UpdatedAt    int64   `json:"updated_at"`
	UserId       string  `json:"user_id"`
	Pair         string  `json:"pair"`
	OrderType    string  `json:"order_type"`
	Side         string  `json:"side"`
	Price        float64 `json:"price,string"`
	Qty          float64 `json:"qty,string"`
	TimeInForce  string  `json:"time_in_force"`
	AvgPrice     float64 `json:"avg_price,string"`
	FilledQty    float64 `json:"filled_qty,string"`
	Status       string  `json:"status"`
	Fee          string  `json:"fee"`
	TakerFeeRate string  `json:"taker_fee_rate"`
	MakerFeeRate string  `json:"maker_fee_rate"`
	Label        string  `json:"label"`
	Mmp          bool    `json:"mmp"`
}

func (g *AccountGateway) processTradeUpdates(data []byte) error {
	var matches []WsUserOrder
	err := json.Unmarshal(data, &matches)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}

	eventLog := make([]gateway.Event, 0, 1)

	for _, order := range matches {
		status := mapWsOrderStatus(order.Status)

		var side gateway.Side
		if order.Side == "buy" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		event := gateway.Event{
			Type: gateway.OrderUpdateEvent,
			Data: gateway.Order{
				ID:           order.OrderId,
				Side:         side,
				Price:        order.Price,
				Amount:       order.Qty,
				FilledAmount: order.FilledQty,
				State:        status,
				AvgPrice:     order.AvgPrice,
			},
		}

		eventLog = append(eventLog, event)

	}
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

func mapWsOrderStatus(status string) gateway.OrderState {
	switch status {
	case "pending":
		return gateway.OrderOpen
	case "open":
		return gateway.OrderOpen
	case "filled":
		return gateway.OrderFullyFilled
	case "cancelled":
		return gateway.OrderCancelled
	}

	return gateway.OrderUnknown
}
