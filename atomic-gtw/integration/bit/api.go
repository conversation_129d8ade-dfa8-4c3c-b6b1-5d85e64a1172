package bit

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseProd       = "https://api.bit.com"
	apiSymbols        = "/spot/v1/instruments"
	apiAccountBalance = "/um/v1/accounts"
	apiOpenOrders     = "/spot/v1/open_orders"
	apiPlaceOrder     = "/spot/v1/orders"
	apiCancelOrder    = "/spot/v1/cancel_orders"
	apiWebsocketToken = "/spot/v1/ws/auth"
	apiDepthBook      = "/linear/v1/orderbooks?instrument_id=%s&level=%d"
)

type API struct {
	options       gateway.Options
	httpClient    *utils.HttpClient
	baseURL       string
	lastNonceUsed int64
	lock          sync.Mutex
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

type APIResponse struct {
	Code int             `json:"code"`
	Msg  string          `json:"message"`
	Data json.RawMessage `json:"data"`
}

type SymbolDetails struct {
	Pair             string  `json:"pair"`
	BaseCurrency     string  `json:"base_currency"`
	QuoteCurrency    string  `json:"quote_currency"`
	DisplayName      string  `json:"display_name"`
	BaseDisplayName  string  `json:"base_display_name"`
	QuoteDisplayName string  `json:"quote_display_name"`
	PriceStep        float64 `json:"price_step,string"`
	QtyStep          float64 `json:"qty_step,string"`
	QtyMin           string  `json:"qty_min"`
	QuoteQtyStep     float64 `json:"quote_qty_step,string"`
	QuoteQtyMin      string  `json:"quote_qty_min"`
	TakerFeeRate     string  `json:"taker_fee_rate"`
	MakerFeeRate     string  `json:"maker_fee_rate"`
	Status           int     `json:"status"`
	DisplayStatus    int     `json:"display_status"`
}

func (a *API) Symbols() (symbols []SymbolDetails, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiSymbols, nil, false)
	if err != nil {
		return symbols, err
	}

	err = a.makeHttpRequest(req, &symbols)
	if err != nil {
		return nil, err
	}

	return symbols, nil
}

type ApiAccountBalance struct {
	Details []AccountBalance `json:"details"`
}

type AccountBalance struct {
	Currency               string  `json:"currency"`
	CcyDisplayName         string  `json:"ccy_display_name"`
	Equity                 string  `json:"equity"`
	Liability              string  `json:"liability"`
	IndexPrice             string  `json:"index_price"`
	CashBalance            string  `json:"cash_balance"`
	MarginBalance          float64 `json:"margin_balance,string"`
	AvailableBalance       float64 `json:"available_balance,string"`
	InitialMargin          string  `json:"initial_margin"`
	SpotMargin             string  `json:"spot_margin"`
	MaintenanceMargin      string  `json:"maintenance_margin"`
	PotentialLiability     string  `json:"potential_liability"`
	Interest               string  `json:"interest"`
	InterestRate           string  `json:"interest_rate"`
	Pnl                    string  `json:"pnl"`
	TotalDelta             string  `json:"total_delta"`
	SessionRpl             string  `json:"session_rpl"`
	SessionUpl             string  `json:"session_upl"`
	OptionAvailableBalance string  `json:"option_available_balance"`
}

func (a *API) AccountBalance() (resp []AccountBalance, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiAccountBalance, nil, true)
	if err != nil {
		return nil, err
	}
	var apiBalance ApiAccountBalance
	if err = a.makeHttpRequest(req, &apiBalance); err != nil {
		return nil, err
	}
	resp = apiBalance.Details

	return resp, nil
}

type OpenOrders struct {
	OrderId        string  `json:"order_id"`
	CreatedAt      int64   `json:"created_at"`
	UpdatedAt      int64   `json:"updated_at"`
	UserId         string  `json:"user_id"`
	Pair           string  `json:"pair"`
	OrderType      string  `json:"order_type"`
	Side           string  `json:"side"`
	Price          float64 `json:"price,string"`
	Qty            float64 `json:"qty,string"`
	QuoteQty       string  `json:"quote_qty"`
	TimeInForce    string  `json:"time_in_force"`
	AvgPrice       string  `json:"avg_price"`
	FilledQty      string  `json:"filled_qty"`
	Status         string  `json:"status"`
	Fee            string  `json:"fee"`
	TakerFeeRate   string  `json:"taker_fee_rate"`
	MakerFeeRate   string  `json:"maker_fee_rate"`
	CancelReason   string  `json:"cancel_reason"`
	Label          string  `json:"label"`
	Source         string  `json:"source"`
	PostOnly       bool    `json:"post_only"`
	RejectPostOnly bool    `json:"reject_post_only"`
	Mmp            bool    `json:"mmp"`
	IsLiquidation  bool    `json:"is_liquidation"`
	IsUm           bool    `json:"is_um"`
}

func (a *API) OpenOrders(params map[string]interface{}) (resp []OpenOrders, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiOpenOrders, params, true)
	if err != nil {
		return nil, err
	}
	var apiOpen []OpenOrders
	if err = a.makeHttpRequest(req, &apiOpen); err != nil {
		return nil, err
	}

	return resp, nil
}

type ApiToken struct {
	Token string `json:"token"`
}

func (a *API) GetWebsocketToken() (token string, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiWebsocketToken, nil, true)
	if err != nil {
		return "", err

	}
	var apiToken ApiToken
	if err = a.makeHttpRequest(req, &apiToken); err != nil {
		return "", err
	}
	token = apiToken.Token

	return token, nil
}

type ApiPlaceOrder struct {
	OrderId        string `json:"order_id"`
	CreatedAt      int64  `json:"created_at"`
	UpdatedAt      int64  `json:"updated_at"`
	UserId         string `json:"user_id"`
	Pair           string `json:"pair"`
	OrderType      string `json:"order_type"`
	Side           string `json:"side"`
	Price          string `json:"price"`
	Qty            string `json:"qty"`
	QuoteQty       string `json:"quote_qty"`
	TimeInForce    string `json:"time_in_force"`
	AvgPrice       string `json:"avg_price"`
	FilledQty      string `json:"filled_qty"`
	Status         string `json:"status"`
	TakerFeeRate   string `json:"taker_fee_rate"`
	MakerFeeRate   string `json:"maker_fee_rate"`
	CancelReason   string `json:"cancel_reason"`
	Label          string `json:"label"`
	Source         string `json:"source"`
	PostOnly       bool   `json:"post_only"`
	RejectPostOnly bool   `json:"reject_post_only"`
	Mmp            bool   `json:"mmp"`
	IsLiquidation  bool   `json:"is_liquidation"`
	IsUm           bool   `json:"is_um"`
}

func (a *API) PlaceOrder(order map[string]interface{}) (orderID string, err error) {
	req, err := a.newHttpRequest(http.MethodPost, apiPlaceOrder, order, true)
	if err != nil {
		return "", err
	}

	var resp ApiPlaceOrder
	if err = a.makeHttpRequest(req, &resp); err != nil {
		return "", err
	}

	orderID = resp.OrderId

	return orderID, nil
}

type ApiCancelOrder struct {
	NumCancelled int   `json:"num_cancelled"`
	OrderIds     []int `json:"order_ids"`
}

func (a *API) CancelOrder(orderID string, symbol string) (err error) {
	cancelOrdersMap := map[string]interface{}{
		"order_id": orderID,
	}
	req, err := a.newHttpRequest(http.MethodPost, apiCancelOrder, cancelOrdersMap, true)
	if err != nil {
		return err
	}
	var apiCancel ApiCancelOrder
	if err = a.makeHttpRequest(req, &apiCancel); err != nil {
		return err
	}

	return nil
}

type DepthBook struct {
	Asks [][]string `json:"asks"`
	Bids [][]string `json:"bids"`
}

func (a *API) DepthBook(symbol string, params gateway.GetDepthParams) (depthBook DepthBook, err error) {
	if params.Limit == 0 {
		params.Limit = 50
	}
	req, err := a.newHttpRequest(http.MethodGet, fmt.Sprintf(apiDepthBook, symbol, params.Limit), nil, false)
	if err != nil {
		return depthBook, err
	}

	err = a.makeHttpRequest(req, &depthBook)
	if err != nil {
		return depthBook, err
	}

	return depthBook, nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	var apiRes APIResponse
	err = json.Unmarshal(body, &apiRes)
	if err != nil {
		return fmt.Errorf("unmarshal err [%s] body:\n%s", err, string(body))
	}

	if apiRes.Msg != "" {
		return fmt.Errorf("api error: %s", apiRes.Msg)
	}

	if responseObject != nil {
		err = json.Unmarshal(apiRes.Data, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return nil
}

func (a *API) getSignature(apiPath string, paramMap map[string]interface{}) string {
	strToSign := apiPath + "&" + encodeObject(paramMap)
	sig := hmac.New(sha256.New, []byte(a.options.ApiSecret))
	sig.Write([]byte(strToSign))
	return hex.EncodeToString(sig.Sum(nil))
}

func encodeList(itemList []interface{}) string {
	listVal := make([]string, len(itemList))
	for i, item := range itemList {
		objVal := encodeObject(item)
		listVal[i] = objVal
	}
	output := "[" + strings.Join(listVal, "&") + "]"
	return output
}

func encodeObject(obj interface{}) string {
	switch val := obj.(type) {
	case string:
		return val
	case int:
		return strconv.Itoa(val)
	case int64:
		return strconv.FormatInt(val, 10)
	case bool:
		return strconv.FormatBool(val)
	case map[string]interface{}:
		sortedKeys := make([]string, 0, len(val))
		for k := range val {
			sortedKeys = append(sortedKeys, k)
		}
		sort.Strings(sortedKeys)
		retList := make([]string, len(val))
		for i, key := range sortedKeys {
			value := val[key]
			switch value.(type) {
			case []interface{}:
				listVal := encodeList(value.([]interface{}))
				retList[i] = key + "=" + listVal
			case map[string]interface{}:
				dictVal := encodeObject(value)
				retList[i] = key + "=" + dictVal
			case bool:
				boolVal := strconv.FormatBool(value.(bool))
				retList[i] = key + "=" + boolVal
			default:
				generalVal := fmt.Sprintf("%v", value)
				retList[i] = key + "=" + generalVal
			}
		}
		output := strings.Join(retList, "&")
		return output
	default:
		panic("Unsupported type")
	}
}

func (a *API) newHttpRequest(method string, path string, paramsMap map[string]interface{}, signed bool) (*http.Request, error) {
	urlRequest := a.baseURL + path
	req, err := http.NewRequest(method, urlRequest, nil)
	if err != nil {
		return nil, err
	}

	if signed {
		timestamp := time.Now().UnixMilli()
		if err != nil {
			return nil, err
		}
		if method == http.MethodGet {
			paramsMap = map[string]interface{}{
				"timestamp": timestamp,
			}
		} else {
			paramsMap["timestamp"] = timestamp
		}
		signature := a.getSignature(path, paramsMap)
		req.Header.Set("X-Bit-Access-Key", a.options.ApiKey)
		if method == http.MethodGet {
			params := req.URL.Query()
			params.Set("signature", signature)
			params.Set("timestamp", strconv.FormatInt(timestamp, 10))
			req.URL.RawQuery = params.Encode()

		} else {
			req.Header.Set("Content-Type", "application/json")
			paramsMap["signature"] = signature
			paramsMap["timestamp"] = timestamp
			newBody, err := json.Marshal(paramsMap)
			if err != nil {
				return nil, err
			}
			req.Body = io.NopCloser(bytes.NewReader(newBody))
		}
	}

	return req, nil
}
