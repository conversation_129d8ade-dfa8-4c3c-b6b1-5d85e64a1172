package bit

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	baseURL string
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewMarketDataGateway(baseURL string, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		baseURL: baseURL,
		options: options,
		tickCh:  tickCh,
	}
}

type WsRequest struct {
	Type     string   `json:"type"`
	Pairs    []string `json:"pairs"`
	Channels []string `json:"channels"`
	Interval string   `json:"interval"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()

	err := ws.Connect(g.baseURL)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}
	var pairs []string
	for _, market := range markets {
		pairs = append(pairs, market.Symbol)
	}

	bookRequest := WsRequest{
		Type: "subscribe",
		Channels: []string{
			"depth",
			"trade",
		},
		Interval: "raw",
		Pairs:    pairs,
	}
	data, err := json.Marshal(bookRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}

	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write sub msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	pongCh := make(chan bool)
	go g.messageHandler(ch, pongCh)
	go websocketPinger(ws, pongCh)

	return nil
}

type WsResponse struct {
	Type      string          `json:"type"`
	Channel   string          `json:"channel"`
	Timestamp int64           `json:"timestamp"`
	Module    string          `json:"module"`
	Data      json.RawMessage `json:"data"`
}

func (g *MarketDataGateway) messageHandler(ch chan []byte, pongCh chan bool) {
	for data := range ch {
		var msg WsResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		if msg.Type == "pong" {
			pongCh <- true
			continue
		}

		switch {
		case msg.Channel == "trade":
			if err := g.processTradeUpdates(msg.Data); err != nil {
				log.Printf("%s error processing matches \"%s\": %s", g.baseURL, data, err)
			}

		case msg.Channel == "depth":
			err := g.processBookMsg(msg.Data)
			if err != nil {
				log.Printf("%s error processing book \"%s\": %s", g.baseURL, data, err)
			}
		default:
			log.Printf("bit unprocessable message type [%s], data [%s]", msg.Data, string(data))
		}
	}
}

type WsResponseBook struct {
	Type      string               `json:"type"`
	Pair      string               `json:"pair"`
	Sequence  int                  `json:"sequence"`
	Timestamp int64                `json:"timestamp"`
	Asks      []gateway.PriceArray `json:"asks"`
	Bids      []gateway.PriceArray `json:"bids"`
	Changes   [][3]string          `json:"changes"`
}

func (g *MarketDataGateway) processBookMsg(data []byte) error {
	var wsResponse WsResponseBook
	err := json.Unmarshal(data, &wsResponse)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}

	var snapshot bool
	var bids, asks []gateway.PriceArray
	if wsResponse.Type == "snapshot" {
		snapshot = true
		bids = wsResponse.Bids
		asks = wsResponse.Asks
	} else if wsResponse.Type == "update" {
		bids = extractFromChanges("buy", wsResponse.Changes)
		asks = extractFromChanges("sell", wsResponse.Changes)
	} else {
		return fmt.Errorf("unknown message type %s", wsResponse.Type)
	}

	g.processDepthUpdate(wsResponse.Pair, bids, asks, snapshot)

	return nil
}

func extractFromChanges(key string, changes [][3]string) []gateway.PriceArray {
	var pxs []gateway.PriceArray

	for _, change := range changes {
		if change[0] == key {
			price, err := strconv.ParseFloat(change[1], 64)
			if err != nil {
				log.Printf("%s extractBidsFromChanges[%s] err parseFloat price [%s]: %s", Exchange, key, change[0], err)
				continue
			}
			amount, err := strconv.ParseFloat(change[2], 64)
			if err != nil {
				log.Printf("%s extractBidsFromChanges[%s] err parseFloat amount [%s]: %s", Exchange, key, change[1], err)
				continue
			}

			pxs = append(pxs, gateway.PriceArray{
				Price:  price,
				Amount: amount,
			})
		}
	}
	return pxs
}

func (g *MarketDataGateway) processDepthUpdate(symbol string, bids []gateway.PriceArray, asks []gateway.PriceArray, snapshot bool) {
	eventLog := make([]gateway.Event, 0, len(bids)+len(asks)+1)
	appendEvents := func(symbol string, side gateway.Side, prices []gateway.PriceArray) {
		for _, order := range prices {
			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  order.Price,
					Amount: order.Amount,
				},
			}

			eventLog = append(eventLog, event)
		}
	}

	if snapshot {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: symbol,
			},
		})
	}

	appendEvents(symbol, gateway.Ask, asks)
	appendEvents(symbol, gateway.Bid, bids)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

type WsResponseTrade struct {
	Action string `json:"action"`
	Arg    struct {
		InstType string `json:"instType"`
		Channel  string `json:"channel"`
		InstId   string `json:"instId"`
	} `json:"arg"`
	Data []json.RawMessage `json:"data"`
}
type WsTradee struct {
	TradeId   string  `json:"trade_id"`
	Pair      string  `json:"pair"`
	Price     float64 `json:"price,string"`
	Qty       float64 `json:"qty,string"`
	Side      string  `json:"side"`
	CreatedAt int64   `json:"created_at"`
}

func (g *MarketDataGateway) processTradeUpdates(data []byte) error {
	var matches []WsTradee
	err := json.Unmarshal(data, &matches)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}

	eventLog := make([]gateway.Event, 0, 1)

	for _, match := range matches {
		var side gateway.Side
		if match.Side == "buy" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		event := gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				Symbol:    match.Pair,
				Direction: side,
				Price:     match.Price,
				Amount:    match.Qty,
			},
		}

		eventLog = append(eventLog, event)

	}
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

func websocketPinger(ws *utils.WsClient, pongCh chan bool) {
	for {
		select {
		case <-pongCh:
			// Reset ping timer
		case <-time.After(30 * time.Second):
			err := ws.WriteMessage([]byte("{\"type\":\"ping\", \"params\":{}}"))
			if err != nil {
				panic(fmt.Errorf("%s ws failed to send ping, err: %s", Exchange.Name, err))
			}

			// Check for pong response
			select {
			case <-pongCh:
			case <-time.After(3 * time.Second):
				panic(fmt.Errorf("%s pong not received after 3 seconds...", Exchange.Name))
			}
		}
	}
}
