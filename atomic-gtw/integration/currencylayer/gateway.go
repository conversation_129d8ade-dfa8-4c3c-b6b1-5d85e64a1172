package currencylayer

import (
	"log"
	"math"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "CurrencyLayer",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *MarketDataGateway
	api               *API
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	if options.FxSource == "" {
		options.FxSource = "USD" // Use USD as the base currency for the markets by default
	}

	if options.RefreshIntervalMs == 0 {
		options.RefreshIntervalMs = 10 * 1000 // 10 seconds
	} else if options.RefreshIntervalMs < 1000 {
		log.Printf("CurrencyLayer minimum RefreshIntervalMs is 1000ms, cannot use %dms, defaulting to 1000ms...", options.RefreshIntervalMs)
		options.RefreshIntervalMs = 1000
	}

	api := NewAPI(options.ApiKey)

	gtw := &Gateway{
		api:     api,
		options: options,
		tickCh:  make(chan gateway.Tick, 10),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.marketDataGateway = NewMarketDataGateway(g.Markets(), g.api, g.options, g.tickCh)
	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	if err := g.marketDataGateway.SubscribeMarkets(); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	quotes, err := g.api.GetQuotes(g.options.FxSource)
	if err != nil {
		log.Printf("CurrencyLayer failed to load markets while fetching quotes, err: %s", err)
		return nil, err
	}

	commonMarkets := make([]gateway.Market, len(quotes))
	for i, quote := range quotes {
		commonMarkets[i] = gateway.Market{
			Exchange: Exchange,
			Pair: gateway.Pair{
				Base:  quote.Base,
				Quote: quote.Quote,
			},
			Symbol:     quote.Symbol,
			PriceTick:  1 / math.Pow10(quote.Precision),
			AmountTick: 0.01, // USD price tick. TODO: What if we use another source currency
			MarketType: gateway.SpotMarket,
		}
	}

	return commonMarkets, nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
