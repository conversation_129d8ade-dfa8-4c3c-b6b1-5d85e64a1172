package bitypreco

import (
	"log"

	"github.com/herenow/atomic-gtw/gateway"
)

// OrderStatus is the possible status of an order from BityPreco api
type OrderStatus string

const (
	OPEN      OrderStatus = "EMPTY"
	PARTIALLY OrderStatus = "PARTIAL"
	CANCELLED OrderStatus = "ORDER_CANCELED"
	FILLED    OrderStatus = "FILLED"
)

func (b OrderStatus) String() string {
	switch b {
	case OPEN:
		return "EMPTY"
	case PARTIALLY:
		return "PARTIAL"
	case CANCELLED:
		return "ORDER_CANCELED"
	case FILLED:
		return "FILLED"
	default:
		return "UNKNOWN"
	}
}

func mapAPIOrderToCommon(o APIOpenOrders, market gateway.Market) gateway.Order {
	var avg float64
	if o.ExecAmount > 0 {
		avg = o.Cost / o.ExecAmount
	}

	return gateway.Order{
		ID:               o.ID,
		Market:           market,
		State:            mapAPIOrderStateToCommon(o.Status),
		Side:             mapAPIOrderTypeToCommonSide(o.Type),
		Price:            o.Price,
		Amount:           o.Amount,
		FilledAmount:     o.Exec<PERSON>mount,
		AvgPrice:         avg,
		FilledMoneyValue: o.Cost,
		Fee:              o.Fee,
	}
}

func mapAPIOrderTypeToCommonSide(orderType string) gateway.Side {
	if orderType == "BUY" {
		return gateway.Bid
	} else if orderType == "SELL" {
		return gateway.Ask
	} else {
		log.Printf("BityPreco invalid order side \"%s\"", orderType)
		return ""
	}
}

func mapAPIOrderStateToCommon(st OrderStatus) gateway.OrderState {
	switch st {
	case OPEN:
		return gateway.OrderOpen
	case PARTIALLY:
		return gateway.OrderPartiallyFilled
	case FILLED:
		return gateway.OrderFullyFilled
	case CANCELLED:
		return gateway.OrderCancelled
	default:
		return gateway.OrderUnknown
	}
}
