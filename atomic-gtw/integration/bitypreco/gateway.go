package bitypreco

import (
	"fmt"
	"math"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "BityPreco",
}

const (
	wsURL = "wss://bp-channels.gigalixirapp.com/%s/socket/websocket?vsn=2.0.0"
)

type Gateway struct {
	base.Gateway
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	options           gateway.Options
	api               *API
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(apiBase, options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	g.marketDataGateway = NewMarketDataGateway(wsURL, g.options, g.tickCh, g.api)

	return nil
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.Markets()
	if err != nil {
		return nil, fmt.Errorf("failed to get markets: %s", err)
	}

	mkts := make([]gateway.Market, 0, len(res))
	for symbol, res := range res {
		if !res.OrderbookDisable {
			mkts = append(mkts, gateway.Market{
				Exchange:               Exchange,
				Symbol:                 symbol,
				Pair:                   gateway.Pair{Base: res.Base, Quote: res.Quote},
				PriceTick:              1 / math.Pow10(res.QuotePrecision),
				AmountTick:             1 / math.Pow10(res.BasePrecision),
				MinimumOrderSize:       res.MinBaseAmount,
				MinimumOrderMoneyValue: res.MinQuoteAmount,
				MakerFee:               0.002,
				TakerFee:               0.002,
				MarketType:             gateway.SpotMarket,
			})
		}
	}

	return mkts, nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	if err := g.marketDataGateway.SubscribeMarkets(markets); err != nil {
		return fmt.Errorf("failed to subscribe to market data: %s", err)
	}
	return nil
}

func parsePriceLevelsToDepth(levels []APIPrice) []gateway.PriceLevel {
	var priceLevels []gateway.PriceLevel
	for _, level := range levels {
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  level.Price,
			Amount: level.Amount,
		})
	}
	return priceLevels
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	var asks []gateway.PriceLevel
	var bids []gateway.PriceLevel
	depth, err := g.api.Depth(market.Symbol)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks = parsePriceLevelsToDepth(depth.Asks)
	bids = parsePriceLevelsToDepth(depth.Bids)
	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
