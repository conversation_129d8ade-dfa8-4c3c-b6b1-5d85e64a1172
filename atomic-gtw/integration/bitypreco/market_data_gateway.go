package bitypreco

import (
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strings"
	"time"

	"github.com/buger/jsonparser"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	snapshot = true
)

type MarketDataGateway struct {
	baseURL   string
	heartbeat int
	pongCh    chan bool
	tickCh    chan gateway.Tick
	options   gateway.Options
	api       *API
}

func NewMarketDataGateway(baseURL string, options gateway.Options, tickCh chan gateway.Tick, api *API) *MarketDataGateway {
	return &MarketDataGateway{
		baseURL: baseURL,
		options: options,
		tickCh:  tickCh,
		pongCh:  make(chan bool),
		api:     api,
	}
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	if err := ws.Connect(fmt.Sprintf(g.baseURL, "orderbook")); err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	for _, market := range markets {
		if err := ws.WriteMessage([]byte(fmt.Sprintf(`["5","5","orderbook:%s-%s","phx_join",{}]`, market.Pair.Base, market.Pair.Quote))); err != nil {
			return fmt.Errorf("failed write sub msg to ws: %s", err)
		}
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ch)
	go g.websocketPinger(ws)
	go g.tradesTracking(markets)

	return nil
}

func (g *MarketDataGateway) tradesTracking(markets []gateway.Market) {
	refreshInterval := 5 * time.Second
	if g.options.RefreshIntervalMs > 0 {
		refreshInterval = time.Duration(g.options.RefreshIntervalMs) * time.Millisecond
	}

	log.Printf("%s polling for trades every %v", Exchange, refreshInterval)

	for _, market := range markets {
		go g.trackTradesForMarket(market, refreshInterval)
	}
}

func (g *MarketDataGateway) trackTradesForMarket(market gateway.Market, refreshInterval time.Duration) {
	var tradesFromTime time.Time

	for {
		trades, err := g.api.Trades(market.Symbol)
		if err != nil {
			log.Printf("%s failed to get trades for %s: %s", Exchange, market.Symbol, err)
			time.Sleep(refreshInterval)
			continue
		}

		newTrades := make([]gateway.Trade, 0, len(trades))
		newestTradeTime := tradesFromTime
		for _, trade := range trades {
			tradeTime, err := trade.Time()
			if err != nil {
				log.Printf("%s failed to parse trade time [%s]: %s", Exchange, trade.Timestamp, err)
				continue
			}

			if tradeTime.After(tradesFromTime) {
				newTrades = append(newTrades, gateway.Trade{
					ID:        tradeToID(trade),
					Symbol:    market.Symbol,
					Timestamp: tradeTime,
					Direction: apiSideToGtw(trade.Type),
					Price:     trade.Price,
					Amount:    trade.Amount,
				})
			}

			if tradeTime.After(newestTradeTime) {
				newestTradeTime = tradeTime
			}
		}

		if len(newTrades) > 0 {
			tradesFromTime = newestTradeTime

			events := make([]gateway.Event, len(newTrades))
			for i, trade := range newTrades {
				events[i] = gateway.NewTradeEvent(trade)
			}

			g.tickCh <- gateway.TickWithEvents(events...)
		}

		time.Sleep(refreshInterval)
	}
}

func tradeToID(trade APITrade) string {
	sid := fmt.Sprintf("%s-%s-%s-%f-%f", trade.Market, trade.Timestamp, trade.Type, trade.Price, trade.Amount)
	hash := sha1.New()
	hash.Write([]byte(sid))
	bs := hash.Sum(nil)
	return hex.EncodeToString(bs)
}

func apiSideToGtw(side string) gateway.Side {
	if side == "BUY" {
		return gateway.Bid
	} else if side == "SELL" {
		return gateway.Ask
	}

	return ""
}

func (g *MarketDataGateway) websocketPinger(ws *utils.WsClient) {
	for {
		select {
		case <-g.pongCh: // Reset ping timer
		case <-time.After(2 * time.Second):
			err := ws.WriteMessage([]byte(`[null,null,"phoenix","heartbeat",{}]`))
			if err != nil {
				panic(fmt.Errorf("%s ws failed to send ping, err: %s", Exchange.Name, err))
			}

			// Check for pong response
			select {
			case <-g.pongCh:
			case <-time.After(3 * time.Second):
				panic(fmt.Errorf("%s pong not received after 3 seconds", Exchange.Name))
			}
		}
	}
}

type WsResponse struct {
	JoinReference    interface{}
	MessageReference interface{}
	TopicName        string
	EventName        string
	Data             json.RawMessage
}

// UnmarshalJSON implements json.Unmarshaler for WsResponse struct
func (w *WsResponse) UnmarshalJSON(b []byte) error {
	var res []json.RawMessage
	if err := json.Unmarshal(b, &res); err != nil {
		return fmt.Errorf("failed to unmarshal response in to WsResponse: %v", err)
	}

	// We are working with Phoenix Channels thus, it's expected the response to be an array with 5 items
	// more about at https://hexdocs.pm/phoenix/writing_a_channels_client.html#message-format
	if len(res) != 5 {
		return fmt.Errorf("failed to unmarshal response in to WsResponse: unexpected message, expected the response length to be 5 but is [%v]", res)
	}

	w.JoinReference = res[0]
	w.MessageReference = res[1]
	w.TopicName = strings.Replace(string(res[2]), "\"", "", 2)
	w.EventName = strings.Replace(string(res[3]), "\"", "", 2)
	w.Data = res[4]

	return nil
}

func (g *MarketDataGateway) messageHandler(ch chan []byte) {
	var (
		msg         WsResponse
		orderbookRE = regexp.MustCompile(`orderbook:(.*)`)
	)

	for data := range ch {
		if err := json.Unmarshal(data, &msg); err != nil {
			log.Printf("failed to unmarhsal message [%s] err [%s]", string(data), err)
			continue
		}

		switch {
		case orderbookRE.MatchString(msg.TopicName) && msg.EventName == "snapshot":
			symParts := strings.Split(msg.TopicName, ":")
			if len(symParts) < 2 {
				log.Printf("%s failed to parse symbol from topic name [%s] on orderbook match", Exchange, msg.TopicName)
				continue
			}
			symbol := symParts[1]
			if err := g.processSnapshotMsg(symbol, msg.Data); err != nil {
				log.Printf(`%s error processing snapshot "%s": %s`, g.baseURL, data, err)
			}
		case msg.TopicName == "phoenix" && msg.EventName == "phx_reply":
			g.pongCh <- true
		default:
			log.Printf("%s unprocessable message type [%s], data [%s]", Exchange, msg.Data, string(data))
		}
	}
}

type PriceArray struct {
	Amount float64 `json:"amount"`
	Price  float64 `json:"price"`
}

type WsSnapShot struct {
	Asks []PriceArray `json:"asks"`
	Bids []PriceArray `json:"bids"`
}

// UnmarshalJSON implements json.Unmarshaler for WsSnapShot struct
func (w *WsSnapShot) UnmarshalJSON(b []byte) error {
	if err := jsonparser.ObjectEach(b, func(key []byte, value []byte, dataType jsonparser.ValueType, offset int) error {
		switch string(key) {
		case "asks":
			if err := json.Unmarshal(value, &w.Asks); err != nil {
				return fmt.Errorf("failed to unmarshal asks: %s", err)
			}
		case "bids":
			if err := json.Unmarshal(value, &w.Bids); err != nil {
				return fmt.Errorf("failed to unmarshal bids: %s", err)
			}
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (g *MarketDataGateway) processSnapshotMsg(sym string, data []byte) error {
	var wsResponse WsSnapShot
	if err := json.Unmarshal(data, &wsResponse); err != nil {
		return fmt.Errorf("failed to unmarshal WsSnapShot: %s", err)
	}
	g.processDepthUpdate(sym, snapshot, wsResponse.Bids, wsResponse.Asks)
	return nil
}

func (g *MarketDataGateway) processDepthUpdate(sym string, snapshot bool, bids, asks []PriceArray) {
	eventLog := make([]gateway.Event, 0, len(bids)+len(asks)+1)

	appendEvents := func(sym string, side gateway.Side, prices []PriceArray) {
		for _, order := range prices {
			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: sym,
					Side:   side,
					Price:  order.Price,
					Amount: order.Amount,
				},
			}
			eventLog = append(eventLog, event)
		}
	}

	if snapshot {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: sym,
			},
		})
	}

	appendEvents(sym, gateway.Ask, asks)
	appendEvents(sym, gateway.Bid, bids)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}
