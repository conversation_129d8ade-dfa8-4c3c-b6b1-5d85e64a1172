package bitypreco

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
	"github.com/stretchr/testify/assert"
)

var (
	// response from their api
	balancesResponse           = `{"success":true,"ABFY":1,"ABFY_locked":-1,"ADA":2,"ADA_locked":-2,"ATLAS":3,"ATLAS_locked":-3,"AXS":4,"AXS_locked":-4,"BNB":5,"BNB_locked":-5,"BTC":6,"BTC_locked":-6,"BUSD":7,"BUSD_locked":-7,"CAKE":8,"CAKE_locked":-8,"CRZO":9,"CRZO_locked":-9,"ELLO":10,"ELLO_locked":-10,"ETH":11,"ETH_locked":-11,"GMT":12,"GMT_locked":-12,"LUNA":13,"LUNA_locked":-13,"LUNC":14,"LUNC_locked":-14,"PAXG":15,"PAXG_locked":-15,"POLIS":16,"POLIS_locked":-16,"SLP":17,"SLP_locked":-17,"SOL":18,"SOL_locked":-18,"UNI":19,"UNI_locked":-19,"USDC":20,"USDC_locked":-20,"USDT":21,"USDT_locked":-21,"BRL":22,"BRL_locked":-22,"timestamp":"2023-07-0618:50:45"}`
	errorResponse              = `{"success":false,"message_cod":"FORBIDEN_ACCESS","API":"FRONTEND","post":null}`
	depthResponse              = `{"success":true,"bids":[{"amount":0.10437047,"price":148501,"id":"1015929126"},{"amount":0.0007014,"price":148500,"id":"1015917009"},{"amount":0.05245,"price":148494,"id":"1015931481"},{"amount":0.0501,"price":148494,"id":"1015931505"},{"amount":0.13208,"price":148491,"id":"1015931394"},{"amount":0.01536,"price":148491,"id":"1015931433"},{"amount":0.06575,"price":148427,"id":"1015930995"},{"amount":0.14735,"price":148427,"id":"1015931537"},{"amount":0.2869,"price":148401,"id":"1015930886"},{"amount":0.02268,"price":148395,"id":"1015931296"},{"amount":0.08586,"price":148378.7,"id":"1015931018"},{"amount":0.01961999,"price":148362,"id":"1015931395"},{"amount":0.03609997,"price":148339.83,"id":"1015931540"},{"amount":0.03456318,"price":148339.69,"id":"1015931145"},{"amount":0.43103681,"price":148339.69,"id":"1015931198"},{"amount":0.03439997,"price":148339.69,"id":"1015931523"},{"amount":0.21390002,"price":148336.83,"id":"1015931382"},{"amount":0.03576,"price":148332,"id":"1015931397"},{"amount":0.0239,"price":148329.3,"id":"1015931186"},{"amount":0.04468,"price":148322,"id":"1015931398"},{"amount":0.14993,"price":148322,"id":"1015931509"},{"amount":6.756e-5,"price":148159,"id":"1015845600"},{"amount":7.61e-6,"price":148097.0001,"id":"1015834860"},{"amount":0.01029,"price":148057,"id":"1015931323"},{"amount":0.1397,"price":148057,"id":"1015931525"},{"amount":0.24,"price":148029.79,"id":"1015931315"},{"amount":0.00743324,"price":148000,"id":"1015858698"},{"amount":0.04293,"price":147941,"id":"1015931531"},{"amount":0.04999,"price":147891,"id":"1015931324"},{"amount":0.04073,"price":147876,"id":"1015931535"},{"amount":0.05227,"price":147875,"id":"1015931055"},{"amount":0.00206998,"price":147875,"id":"1015931144"},{"amount":0.00046,"price":147875,"id":"1015931174"},{"amount":0.0051,"price":147831,"id":"1015930859"},{"amount":1.018e-5,"price":147800,"id":"1015823439"},{"amount":0.05378,"price":147644,"id":"1015930880"},{"amount":0.0132509,"price":147559,"id":"1015913198"},{"amount":0.00067796,"price":147500,"id":"1015816755"},{"amount":0.01694915,"price":147500,"id":"1015826766"},{"amount":0.01695202,"price":147475,"id":"1015827117"}],"asks":[{"amount":0.01188,"price":148764,"id":"1015931538"},{"amount":0.48812,"price":148775,"id":"1015930932"},{"amount":0.13208,"price":148775,"id":"1015931547"},{"amount":0.1179,"price":148777,"id":"1015931483"},{"amount":0.1,"price":148891.5,"id":"1015931495"},{"amount":0.0015,"price":148922,"id":"1015931191"},{"amount":0.09839534,"price":148957.68,"id":"1015931544"},{"amount":0.40160466,"price":148958.71,"id":"1015931279"},{"amount":0.0109,"price":148962.72,"id":"1015931542"},{"amount":0.23909999,"price":148972.59,"id":"1015931541"},{"amount":0.00668065,"price":149000,"id":"1015504775"},{"amount":0.11758252,"price":149000,"id":"1015891544"},{"amount":0.01459,"price":149039,"id":"1015931511"},{"amount":0.00539,"price":149109,"id":"1015931068"},{"amount":0.08611,"price":149109,"id":"1015931176"},{"amount":0.05585,"price":149109,"id":"1015931249"},{"amount":0.14749,"price":149180,"id":"1015931322"},{"amount":0.0025,"price":149180,"id":"1015931524"},{"amount":0.08803,"price":149189,"id":"1015931508"},{"amount":0.0239,"price":149239.2,"id":"1015931506"},{"amount":0.1,"price":149310,"id":"1015931493"},{"amount":0.03253,"price":149376,"id":"1015931149"},{"amount":0.00758,"price":149376,"id":"1015931213"},{"amount":0.03420999,"price":149376,"id":"1015931262"},{"amount":0.02904,"price":149376,"id":"1015931536"},{"amount":0.03744,"price":149394,"id":"1015931087"},{"amount":0.00999,"price":149761,"id":"1015930761"},{"amount":0.07969999,"price":149761,"id":"1015931053"},{"amount":0.0103,"price":149761,"id":"1015931532"},{"amount":0.00271493,"price":150000,"id":"1014551538"},{"amount":0.00066666,"price":150000,"id":"1014842341"},{"amount":0.00279006,"price":150000,"id":"1015525304"},{"amount":0.00033671,"price":150000,"id":"1015602546"},{"amount":1.01354096,"price":150000,"id":"1015682505"},{"amount":0.00067805,"price":150022,"id":"1015802123"},{"amount":0.00339433,"price":150022,"id":"1015802768"},{"amount":0.00933333,"price":150200,"id":"1015490991"},{"amount":0.0703,"price":150222,"id":"1015931008"},{"amount":0.02969,"price":150227,"id":"1015931007"},{"amount":7.504e-5,"price": 150231,"id":"1015187195"}],"timestamp":"2023-07-07 19:46:44"}`
	openOrdersResponse         = `[{"id":"1017119872","market":"USDT-BRL","type":"BUY","status":"EMPTY","amount":1,"price":1,"exec_amount":0,"cost":0,"fee":0,"percent_fee":"0.2","limited":"1","programmed":"0","canceled":"0","time_stamp":"2023-07-0819:34:56","quote_id":null,"tag":null,"obs":null}]`
	placeBuyOrderResponse      = `{"success":true,"order_id":"1018180685","amount":1,"exec_amount":0,"type":"BUY","market":"USDT-BRL","price":1,"timestamp":"2023-07-0918:21:24","message_cod":"ORDER_CREATED"}`
	placeSellOrderResponse     = `{"success":true,"order_id":"1018180685","amount":1,"exec_amount":0,"type":"SELL","market":"USDT-BRL","price":1,"timestamp":"2023-07-0918:21:24","message_cod":"ORDER_CREATED"}`
	cancelOrderResponse        = `{"success":true,"message_cod":"ORDER_CANCELED"}`
	cancelInvalidOrderResponse = `{"success": false,"message_cod":"INVALID_ORDER_ID"}`
	orderStatusResponse        = `{"success":true,"order":{"id":"1020705308","market":"USDT-BRL","type":"BUY","status":"FILLED","amount":20,"price":4.8889,"exec_amount":20,"cost":97.778,"fee":0,"limited":"1","canceled":"0","time_stamp":"2023-07-1119:16:47","concluded":"2023-07-1119:17:27"}}`

	// expected parsed response
	depthAPIResponse          = APIDepth{Bids: []APIPrice{{Price: 148501, Amount: 0.10437047}, {Price: 148500, Amount: 0.0007014}, {Price: 148494, Amount: 0.05245}, {Price: 148494, Amount: 0.0501}, {Price: 148491, Amount: 0.13208}, {Price: 148491, Amount: 0.01536}, {Price: 148427, Amount: 0.06575}, {Price: 148427, Amount: 0.14735}, {Price: 148401, Amount: 0.2869}, {Price: 148395, Amount: 0.02268}, {Price: 148378.7, Amount: 0.08586}, {Price: 148362, Amount: 0.01961999}, {Price: 148339.83, Amount: 0.03609997}, {Price: 148339.69, Amount: 0.03456318}, {Price: 148339.69, Amount: 0.43103681}, {Price: 148339.69, Amount: 0.03439997}, {Price: 148336.83, Amount: 0.21390002}, {Price: 148332, Amount: 0.03576}, {Price: 148329.3, Amount: 0.0239}, {Price: 148322, Amount: 0.04468}, {Price: 148322, Amount: 0.14993}, {Price: 148159, Amount: 6.756e-05}, {Price: 148097.0001, Amount: 7.61e-06}, {Price: 148057, Amount: 0.01029}, {Price: 148057, Amount: 0.1397}, {Price: 148029.79, Amount: 0.24}, {Price: 148000, Amount: 0.00743324}, {Price: 147941, Amount: 0.04293}, {Price: 147891, Amount: 0.04999}, {Price: 147876, Amount: 0.04073}, {Price: 147875, Amount: 0.05227}, {Price: 147875, Amount: 0.00206998}, {Price: 147875, Amount: 0.00046}, {Price: 147831, Amount: 0.0051}, {Price: 147800, Amount: 1.018e-05}, {Price: 147644, Amount: 0.05378}, {Price: 147559, Amount: 0.0132509}, {Price: 147500, Amount: 0.00067796}, {Price: 147500, Amount: 0.01694915}, {Price: 147475, Amount: 0.01695202}}, Asks: []APIPrice{{Price: 148764, Amount: 0.01188}, {Price: 148775, Amount: 0.48812}, {Price: 148775, Amount: 0.13208}, {Price: 148777, Amount: 0.1179}, {Price: 148891.5, Amount: 0.1}, {Price: 148922, Amount: 0.0015}, {Price: 148957.68, Amount: 0.09839534}, {Price: 148958.71, Amount: 0.40160466}, {Price: 148962.72, Amount: 0.0109}, {Price: 148972.59, Amount: 0.23909999}, {Price: 149000, Amount: 0.00668065}, {Price: 149000, Amount: 0.11758252}, {Price: 149039, Amount: 0.01459}, {Price: 149109, Amount: 0.00539}, {Price: 149109, Amount: 0.08611}, {Price: 149109, Amount: 0.05585}, {Price: 149180, Amount: 0.14749}, {Price: 149180, Amount: 0.0025}, {Price: 149189, Amount: 0.08803}, {Price: 149239.2, Amount: 0.0239}, {Price: 149310, Amount: 0.1}, {Price: 149376, Amount: 0.03253}, {Price: 149376, Amount: 0.00758}, {Price: 149376, Amount: 0.03420999}, {Price: 149376, Amount: 0.02904}, {Price: 149394, Amount: 0.03744}, {Price: 149761, Amount: 0.00999}, {Price: 149761, Amount: 0.07969999}, {Price: 149761, Amount: 0.0103}, {Price: 150000, Amount: 0.00271493}, {Price: 150000, Amount: 0.00066666}, {Price: 150000, Amount: 0.00279006}, {Price: 150000, Amount: 0.00033671}, {Price: 150000, Amount: 1.01354096}, {Price: 150022, Amount: 0.00067805}, {Price: 150022, Amount: 0.00339433}, {Price: 150200, Amount: 0.00933333}, {Price: 150222, Amount: 0.0703}, {Price: 150227, Amount: 0.02969}, {Price: 150231, Amount: 7.504e-05}}, Timestamp: "2023-07-07 19:46:44"}
	balancesAPIResponse       = []APIBalance{{Asset: "ABFY", Available: 1, Freeze: -1}, {Asset: "ADA", Available: 2, Freeze: -2}, {Asset: "ATLAS", Available: 3, Freeze: -3}, {Asset: "AXS", Available: 4, Freeze: -4}, {Asset: "BNB", Available: 5, Freeze: -5}, {Asset: "BTC", Available: 6, Freeze: -6}, {Asset: "BUSD", Available: 7, Freeze: -7}, {Asset: "CAKE", Available: 8, Freeze: -8}, {Asset: "CRZO", Available: 9, Freeze: -9}, {Asset: "ELLO", Available: 10, Freeze: -10}, {Asset: "ETH", Available: 11, Freeze: -11}, {Asset: "GMT", Available: 12, Freeze: -12}, {Asset: "LUNA", Available: 13, Freeze: -13}, {Asset: "LUNC", Available: 14, Freeze: -14}, {Asset: "PAXG", Available: 15, Freeze: -15}, {Asset: "POLIS", Available: 16, Freeze: -16}, {Asset: "SLP", Available: 17, Freeze: -17}, {Asset: "SOL", Available: 18, Freeze: -18}, {Asset: "UNI", Available: 19, Freeze: -19}, {Asset: "USDC", Available: 20, Freeze: -20}, {Asset: "USDT", Available: 21, Freeze: -21}, {Asset: "BRL", Available: 22, Freeze: -22}}
	openOrdersAPIResponse     = []APIOpenOrders{{ID: "1017119872", Market: "USDT-BRL", Type: "BUY", Status: "EMPTY", Amount: 1, Price: 1, ExecAmount: 0, Cost: 0, Limited: "1", Fee: 0, PercentFee: "0.2", Canceled: "0"}}
	placeBuyOrderAPIResponse  = APIPlaceOrder{Success: true, OrderId: "1018180685", Amount: 1, ExecAmount: 0, Type: "BUY", Market: "USDT-BRL", Price: 1, Timestamp: "2023-07-0918:21:24", MessageCod: "ORDER_CREATED"}
	placeSellOrderAPIResponse = APIPlaceOrder{Success: true, OrderId: "1018180685", Amount: 1, ExecAmount: 0, Type: "SELL", Market: "USDT-BRL", Price: 1, Timestamp: "2023-07-0918:21:24", MessageCod: "ORDER_CREATED"}
	orderStatusAPIResponse    = OrderDetail{OrderID: "1020705308", Market: "USDT-BRL", Type: "BUY", Status: "FILLED", Limited: "1", Canceled: "0", TimeStamp: "2023-07-1119:16:47", Concluded: "2023-07-1119:17:27", Amount: 20, Price: 4.8889, ExecAmount: 20, Cost: 97.778, Fee: 0}
)

func newApi(serverURL string) *API {
	return &API{
		options: gateway.Options{},
		client:  &utils.HttpClient{},
		baseURL: serverURL,
	}
}

func TestNewAPI(t *testing.T) {
	api := NewAPI(apiBase, gateway.Options{})
	assert.Equal(t, api.baseURL, apiBase)
}

func Test_balancesToCommon(t *testing.T) {

	tests := []struct {
		purpose          string
		expectedBalances []APIBalance
		expectedError    error
	}{
		{
			purpose:          "Success",
			expectedBalances: balancesAPIResponse,
			expectedError:    nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			balances, err := balancesToCommon([]byte(balancesResponse))
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedBalances, balances)
		})
	}
}

func TestBalances(t *testing.T) {
	tests := []struct {
		purpose          string
		mockHttpRes      utils.MockResponse
		expectedError    error
		expectedBalances []APIBalance
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHttpRes: utils.MockResponse{
				Endpoint:   apiBalance,
				StatusCode: http.StatusOK,
				Response:   errorResponse,
			},
			expectedBalances: []APIBalance{},
			expectedError:    fmt.Errorf("failed to request api [/v1/trading/balance], error: msg: FORBIDEN_ACCESS"),
		},
		{
			purpose: "Success",
			mockHttpRes: utils.MockResponse{
				Endpoint:   apiBalance,
				StatusCode: http.StatusOK,
				Response:   balancesResponse,
			},
			expectedBalances: balancesAPIResponse,
			expectedError:    nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHttpRes.Endpoint, tt.mockHttpRes.StatusCode, tt.mockHttpRes.Response)
			defer mockServer.Close()

			balances, err := newApi(mockServer.URL).Balance()
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedBalances, balances)
		})
	}
}

func TestDepth(t *testing.T) {
	tests := []struct {
		purpose       string
		mockHttpRes   utils.MockResponse
		market        string
		expectedError error
		expectedDepth APIDepth
	}{
		{
			purpose: "Unhappy path missing authentication",
			market:  "btc-asd",
			mockHttpRes: utils.MockResponse{
				Endpoint:   apiDepth,
				StatusCode: http.StatusNotFound,
				Response:   "status not found",
			},
			expectedDepth: APIDepth{},
			expectedError: fmt.Errorf("failed to request api [/btc-asd/orderbook], error: failed to make http request: 404 Not Found"),
		},
		{
			purpose: "Success",
			market:  "btc-brl",
			mockHttpRes: utils.MockResponse{
				Endpoint:   fmt.Sprintf(apiDepth, "btc-brl"),
				StatusCode: http.StatusOK,
				Response:   depthResponse,
			},
			expectedDepth: depthAPIResponse,
			expectedError: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHttpRes.Endpoint, tt.mockHttpRes.StatusCode, tt.mockHttpRes.Response)
			defer mockServer.Close()

			depth, err := newApi(mockServer.URL).Depth(tt.market)
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedDepth, depth)
		})
	}
}

func TestOpenOrders(t *testing.T) {
	tests := []struct {
		purpose            string
		mockHttpRes        utils.MockResponse
		market             string
		expectedOpenOrders []APIOpenOrders
		expectedError      error
	}{
		{
			purpose: "Unhappy path missing authentication",
			mockHttpRes: utils.MockResponse{
				Endpoint:   apiOpenOrders,
				StatusCode: http.StatusOK,
				Response:   errorResponse,
			},
			expectedOpenOrders: []APIOpenOrders{},
			expectedError:      fmt.Errorf("failed to request api [/v1/trading/open_orders], error: msg: FORBIDEN_ACCESS"),
		},
		{
			purpose: "Success",
			market:  "USDT-BRL",
			mockHttpRes: utils.MockResponse{
				Endpoint:   apiOpenOrders,
				StatusCode: http.StatusOK,
				Response:   openOrdersResponse,
			},
			expectedOpenOrders: openOrdersAPIResponse,
			expectedError:      nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHttpRes.Endpoint, tt.mockHttpRes.StatusCode, tt.mockHttpRes.Response)
			defer mockServer.Close()

			orders, err := newApi(mockServer.URL).OpenOrders(tt.market)
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedOpenOrders, orders)
		})
	}
}

func TestPlaceOrder(t *testing.T) {
	tests := []struct {
		purpose            string
		side               string
		params             map[string]string
		mockHttpRes        utils.MockResponse
		expectedPlaceOrder APIPlaceOrder
		expectedError      error
	}{
		{
			purpose: "Unhappy path missing authentication",
			side:    "buy",
			mockHttpRes: utils.MockResponse{
				Endpoint:   "buy",
				StatusCode: http.StatusOK,
				Response:   errorResponse,
			},
			expectedPlaceOrder: APIPlaceOrder{},
			expectedError:      fmt.Errorf("failed to request api [/v1/trading/buy], error: msg: FORBIDEN_ACCESS"),
		},
		{
			purpose: "Success buy order",
			side:    "buy",
			params:  map[string]string{"limit": "true", "price": "1", "amount": "1", "market": "USDT-BRL"},
			mockHttpRes: utils.MockResponse{
				Endpoint:   "buy",
				StatusCode: http.StatusOK,
				Response:   placeBuyOrderResponse,
			},
			expectedPlaceOrder: placeBuyOrderAPIResponse,
			expectedError:      nil,
		},
		{
			purpose: "Success sell order",
			side:    "sell",
			params:  map[string]string{"limit": "true", "price": "1", "amount": "1", "market": "USDT-BRL"},
			mockHttpRes: utils.MockResponse{
				Endpoint:   "sell",
				StatusCode: http.StatusOK,
				Response:   placeSellOrderResponse,
			},
			expectedPlaceOrder: placeSellOrderAPIResponse,
			expectedError:      nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(fmt.Sprintf(apiPlaceOrder, tt.mockHttpRes.Endpoint), tt.mockHttpRes.StatusCode, tt.mockHttpRes.Response)
			defer mockServer.Close()

			order, err := newApi(mockServer.URL).PlaceOrder(tt.side, tt.params)
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedPlaceOrder, order)
		})
	}
}

func TestCancelOrder(t *testing.T) {
	tests := []struct {
		purpose       string
		orderID       string
		mockHttpRes   utils.MockResponse
		expectedError error
	}{
		{
			purpose: "Unhappy path missing authentication",
			mockHttpRes: utils.MockResponse{
				Endpoint:   apiCancelOrder,
				StatusCode: http.StatusOK,
				Response:   errorResponse,
			},
			expectedError: fmt.Errorf("failed to request api [/v1/trading/order_cancel], error: msg: FORBIDEN_ACCESS"),
		},
		{
			purpose: "Unhappy path invalid order id",
			mockHttpRes: utils.MockResponse{
				Endpoint:   apiCancelOrder,
				StatusCode: http.StatusOK,
				Response:   cancelInvalidOrderResponse,
			},
			expectedError: fmt.Errorf("failed to request api [/v1/trading/order_cancel], error: msg: INVALID_ORDER_ID"),
		},
		{
			purpose: "Success",
			mockHttpRes: utils.MockResponse{
				Endpoint:   apiCancelOrder,
				StatusCode: http.StatusOK,
				Response:   cancelOrderResponse,
			},
			expectedError: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHttpRes.Endpoint, tt.mockHttpRes.StatusCode, tt.mockHttpRes.Response)
			defer mockServer.Close()

			err := newApi(mockServer.URL).CancelOrder(tt.orderID)
			assert.Equal(t, tt.expectedError, err)
		})
	}
}

func TestOrderStatus(t *testing.T) {
	tests := []struct {
		purpose            string
		orderID            string
		mockHttpRes        utils.MockResponse
		expectedPlaceOrder OrderDetail
		expectedError      error
	}{
		{
			purpose: "Unhappy path missing authentication",
			orderID: "1020705308",
			mockHttpRes: utils.MockResponse{
				Endpoint:   apiOrderStatus,
				StatusCode: http.StatusOK,
				Response:   errorResponse,
			},
			expectedPlaceOrder: OrderDetail{},
			expectedError:      fmt.Errorf("failed to request api [/v1/trading/order_status], error: msg: FORBIDEN_ACCESS"),
		},
		{
			purpose: "Success",
			orderID: "1020705308",
			mockHttpRes: utils.MockResponse{
				Endpoint:   apiOrderStatus,
				StatusCode: http.StatusOK,
				Response:   orderStatusResponse,
			},
			expectedPlaceOrder: orderStatusAPIResponse,
			expectedError:      nil,
		},
		{
			purpose: "Success",
			orderID: "1020705308",
			mockHttpRes: utils.MockResponse{
				Endpoint:   apiOrderStatus,
				StatusCode: http.StatusOK,
				Response:   orderStatusResponse,
			},
			expectedPlaceOrder: orderStatusAPIResponse,
			expectedError:      nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHttpRes.Endpoint, tt.mockHttpRes.StatusCode, tt.mockHttpRes.Response)
			defer mockServer.Close()

			order, err := newApi(mockServer.URL).OrderStatus(tt.orderID)
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedPlaceOrder, order)
		})
	}
}
