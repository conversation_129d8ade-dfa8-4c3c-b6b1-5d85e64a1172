// <EMAIL>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PublicMiniTickersV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublicMiniTickersV3Api struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Items         []*PublicMiniTickerV3Api `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicMiniTickersV3Api) Reset() {
	*x = PublicMiniTickersV3Api{}
	mi := &file_PublicMiniTickersV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicMiniTickersV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicMiniTickersV3Api) ProtoMessage() {}

func (x *PublicMiniTickersV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMiniTickersV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicMiniTickersV3Api.ProtoReflect.Descriptor instead.
func (*PublicMiniTickersV3Api) Descriptor() ([]byte, []int) {
	return file_PublicMiniTickersV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PublicMiniTickersV3Api) GetItems() []*PublicMiniTickerV3Api {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_PublicMiniTickersV3Api_proto protoreflect.FileDescriptor

const file_PublicMiniTickersV3Api_proto_rawDesc = "" +
	"\n" +
	"\x1cPublicMiniTickersV3Api.proto\x1a\x1bPublicMiniTickerV3Api.proto\"F\n" +
	"\x16PublicMiniTickersV3Api\x12,\n" +
	"\x05items\x18\x01 \x03(\v2\x16.PublicMiniTickerV3ApiR\x05itemsBr\n" +
	"\x1ccom.mxc.push.common.protobufB\x1bPublicMiniTickersV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PublicMiniTickersV3Api_proto_rawDescOnce sync.Once
	file_PublicMiniTickersV3Api_proto_rawDescData []byte
)

func file_PublicMiniTickersV3Api_proto_rawDescGZIP() []byte {
	file_PublicMiniTickersV3Api_proto_rawDescOnce.Do(func() {
		file_PublicMiniTickersV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PublicMiniTickersV3Api_proto_rawDesc), len(file_PublicMiniTickersV3Api_proto_rawDesc)))
	})
	return file_PublicMiniTickersV3Api_proto_rawDescData
}

var file_PublicMiniTickersV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_PublicMiniTickersV3Api_proto_goTypes = []any{
	(*PublicMiniTickersV3Api)(nil), // 0: PublicMiniTickersV3Api
	(*PublicMiniTickerV3Api)(nil),  // 1: PublicMiniTickerV3Api
}
var file_PublicMiniTickersV3Api_proto_depIdxs = []int32{
	1, // 0: PublicMiniTickersV3Api.items:type_name -> PublicMiniTickerV3Api
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_PublicMiniTickersV3Api_proto_init() }
func file_PublicMiniTickersV3Api_proto_init() {
	if File_PublicMiniTickersV3Api_proto != nil {
		return
	}
	file_PublicMiniTickerV3Api_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PublicMiniTickersV3Api_proto_rawDesc), len(file_PublicMiniTickersV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PublicMiniTickersV3Api_proto_goTypes,
		DependencyIndexes: file_PublicMiniTickersV3Api_proto_depIdxs,
		MessageInfos:      file_PublicMiniTickersV3Api_proto_msgTypes,
	}.Build()
	File_PublicMiniTickersV3Api_proto = out.File
	file_PublicMiniTickersV3Api_proto_goTypes = nil
	file_PublicMiniTickersV3Api_proto_depIdxs = nil
}
