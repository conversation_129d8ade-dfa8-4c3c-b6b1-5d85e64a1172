// <EMAIL>@<symbol>@<interval>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PublicSpotKlineV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublicSpotKlineV3Api struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// K线周期(Min1,Min5,Min15,Min30,Min60,Hour4,Hour8,Day1,Week1,Month1)
	Interval string `protobuf:"bytes,1,opt,name=interval,proto3" json:"interval,omitempty"`
	// 窗口开始时间戳(秒时间戳)
	WindowStart int64 `protobuf:"varint,2,opt,name=windowStart,proto3" json:"windowStart,omitempty"`
	// 开盘价
	OpeningPrice string `protobuf:"bytes,3,opt,name=openingPrice,proto3" json:"openingPrice,omitempty"`
	// 收盘价
	ClosingPrice string `protobuf:"bytes,4,opt,name=closingPrice,proto3" json:"closingPrice,omitempty"`
	// 最高价
	HighestPrice string `protobuf:"bytes,5,opt,name=highestPrice,proto3" json:"highestPrice,omitempty"`
	// 最低价
	LowestPrice string `protobuf:"bytes,6,opt,name=lowestPrice,proto3" json:"lowestPrice,omitempty"`
	// 成交量
	Volume string `protobuf:"bytes,7,opt,name=volume,proto3" json:"volume,omitempty"`
	// 成交额
	Amount string `protobuf:"bytes,8,opt,name=amount,proto3" json:"amount,omitempty"`
	// 窗口结束时间戳(秒时间戳)
	WindowEnd     int64 `protobuf:"varint,9,opt,name=windowEnd,proto3" json:"windowEnd,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicSpotKlineV3Api) Reset() {
	*x = PublicSpotKlineV3Api{}
	mi := &file_PublicSpotKlineV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicSpotKlineV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicSpotKlineV3Api) ProtoMessage() {}

func (x *PublicSpotKlineV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PublicSpotKlineV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicSpotKlineV3Api.ProtoReflect.Descriptor instead.
func (*PublicSpotKlineV3Api) Descriptor() ([]byte, []int) {
	return file_PublicSpotKlineV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PublicSpotKlineV3Api) GetInterval() string {
	if x != nil {
		return x.Interval
	}
	return ""
}

func (x *PublicSpotKlineV3Api) GetWindowStart() int64 {
	if x != nil {
		return x.WindowStart
	}
	return 0
}

func (x *PublicSpotKlineV3Api) GetOpeningPrice() string {
	if x != nil {
		return x.OpeningPrice
	}
	return ""
}

func (x *PublicSpotKlineV3Api) GetClosingPrice() string {
	if x != nil {
		return x.ClosingPrice
	}
	return ""
}

func (x *PublicSpotKlineV3Api) GetHighestPrice() string {
	if x != nil {
		return x.HighestPrice
	}
	return ""
}

func (x *PublicSpotKlineV3Api) GetLowestPrice() string {
	if x != nil {
		return x.LowestPrice
	}
	return ""
}

func (x *PublicSpotKlineV3Api) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

func (x *PublicSpotKlineV3Api) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *PublicSpotKlineV3Api) GetWindowEnd() int64 {
	if x != nil {
		return x.WindowEnd
	}
	return 0
}

var File_PublicSpotKlineV3Api_proto protoreflect.FileDescriptor

const file_PublicSpotKlineV3Api_proto_rawDesc = "" +
	"\n" +
	"\x1aPublicSpotKlineV3Api.proto\"\xb0\x02\n" +
	"\x14PublicSpotKlineV3Api\x12\x1a\n" +
	"\binterval\x18\x01 \x01(\tR\binterval\x12 \n" +
	"\vwindowStart\x18\x02 \x01(\x03R\vwindowStart\x12\"\n" +
	"\fopeningPrice\x18\x03 \x01(\tR\fopeningPrice\x12\"\n" +
	"\fclosingPrice\x18\x04 \x01(\tR\fclosingPrice\x12\"\n" +
	"\fhighestPrice\x18\x05 \x01(\tR\fhighestPrice\x12 \n" +
	"\vlowestPrice\x18\x06 \x01(\tR\vlowestPrice\x12\x16\n" +
	"\x06volume\x18\a \x01(\tR\x06volume\x12\x16\n" +
	"\x06amount\x18\b \x01(\tR\x06amount\x12\x1c\n" +
	"\twindowEnd\x18\t \x01(\x03R\twindowEndBp\n" +
	"\x1ccom.mxc.push.common.protobufB\x19PublicSpotKlineV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PublicSpotKlineV3Api_proto_rawDescOnce sync.Once
	file_PublicSpotKlineV3Api_proto_rawDescData []byte
)

func file_PublicSpotKlineV3Api_proto_rawDescGZIP() []byte {
	file_PublicSpotKlineV3Api_proto_rawDescOnce.Do(func() {
		file_PublicSpotKlineV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PublicSpotKlineV3Api_proto_rawDesc), len(file_PublicSpotKlineV3Api_proto_rawDesc)))
	})
	return file_PublicSpotKlineV3Api_proto_rawDescData
}

var file_PublicSpotKlineV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_PublicSpotKlineV3Api_proto_goTypes = []any{
	(*PublicSpotKlineV3Api)(nil), // 0: PublicSpotKlineV3Api
}
var file_PublicSpotKlineV3Api_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_PublicSpotKlineV3Api_proto_init() }
func file_PublicSpotKlineV3Api_proto_init() {
	if File_PublicSpotKlineV3Api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PublicSpotKlineV3Api_proto_rawDesc), len(file_PublicSpotKlineV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PublicSpotKlineV3Api_proto_goTypes,
		DependencyIndexes: file_PublicSpotKlineV3Api_proto_depIdxs,
		MessageInfos:      file_PublicSpotKlineV3Api_proto_msgTypes,
	}.Build()
	File_PublicSpotKlineV3Api_proto = out.File
	file_PublicSpotKlineV3Api_proto_goTypes = nil
	file_PublicSpotKlineV3Api_proto_depIdxs = nil
}
