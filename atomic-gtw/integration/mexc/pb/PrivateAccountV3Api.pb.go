// <EMAIL>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PrivateAccountV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PrivateAccountV3Api struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	VcoinName           string                 `protobuf:"bytes,1,opt,name=vcoinName,proto3" json:"vcoinName,omitempty"`
	CoinId              string                 `protobuf:"bytes,2,opt,name=coinId,proto3" json:"coinId,omitempty"`
	BalanceAmount       string                 `protobuf:"bytes,3,opt,name=balanceAmount,proto3" json:"balanceAmount,omitempty"`
	BalanceAmountChange string                 `protobuf:"bytes,4,opt,name=balanceAmountChange,proto3" json:"balanceAmountChange,omitempty"`
	FrozenAmount        string                 `protobuf:"bytes,5,opt,name=frozenAmount,proto3" json:"frozenAmount,omitempty"`
	FrozenAmountChange  string                 `protobuf:"bytes,6,opt,name=frozenAmountChange,proto3" json:"frozenAmountChange,omitempty"`
	Type                string                 `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	Time                int64                  `protobuf:"varint,8,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PrivateAccountV3Api) Reset() {
	*x = PrivateAccountV3Api{}
	mi := &file_PrivateAccountV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrivateAccountV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivateAccountV3Api) ProtoMessage() {}

func (x *PrivateAccountV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PrivateAccountV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivateAccountV3Api.ProtoReflect.Descriptor instead.
func (*PrivateAccountV3Api) Descriptor() ([]byte, []int) {
	return file_PrivateAccountV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PrivateAccountV3Api) GetVcoinName() string {
	if x != nil {
		return x.VcoinName
	}
	return ""
}

func (x *PrivateAccountV3Api) GetCoinId() string {
	if x != nil {
		return x.CoinId
	}
	return ""
}

func (x *PrivateAccountV3Api) GetBalanceAmount() string {
	if x != nil {
		return x.BalanceAmount
	}
	return ""
}

func (x *PrivateAccountV3Api) GetBalanceAmountChange() string {
	if x != nil {
		return x.BalanceAmountChange
	}
	return ""
}

func (x *PrivateAccountV3Api) GetFrozenAmount() string {
	if x != nil {
		return x.FrozenAmount
	}
	return ""
}

func (x *PrivateAccountV3Api) GetFrozenAmountChange() string {
	if x != nil {
		return x.FrozenAmountChange
	}
	return ""
}

func (x *PrivateAccountV3Api) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PrivateAccountV3Api) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

var File_PrivateAccountV3Api_proto protoreflect.FileDescriptor

const file_PrivateAccountV3Api_proto_rawDesc = "" +
	"\n" +
	"\x19PrivateAccountV3Api.proto\"\x9f\x02\n" +
	"\x13PrivateAccountV3Api\x12\x1c\n" +
	"\tvcoinName\x18\x01 \x01(\tR\tvcoinName\x12\x16\n" +
	"\x06coinId\x18\x02 \x01(\tR\x06coinId\x12$\n" +
	"\rbalanceAmount\x18\x03 \x01(\tR\rbalanceAmount\x120\n" +
	"\x13balanceAmountChange\x18\x04 \x01(\tR\x13balanceAmountChange\x12\"\n" +
	"\ffrozenAmount\x18\x05 \x01(\tR\ffrozenAmount\x12.\n" +
	"\x12frozenAmountChange\x18\x06 \x01(\tR\x12frozenAmountChange\x12\x12\n" +
	"\x04type\x18\a \x01(\tR\x04type\x12\x12\n" +
	"\x04time\x18\b \x01(\x03R\x04timeBo\n" +
	"\x1ccom.mxc.push.common.protobufB\x18PrivateAccountV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PrivateAccountV3Api_proto_rawDescOnce sync.Once
	file_PrivateAccountV3Api_proto_rawDescData []byte
)

func file_PrivateAccountV3Api_proto_rawDescGZIP() []byte {
	file_PrivateAccountV3Api_proto_rawDescOnce.Do(func() {
		file_PrivateAccountV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PrivateAccountV3Api_proto_rawDesc), len(file_PrivateAccountV3Api_proto_rawDesc)))
	})
	return file_PrivateAccountV3Api_proto_rawDescData
}

var file_PrivateAccountV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_PrivateAccountV3Api_proto_goTypes = []any{
	(*PrivateAccountV3Api)(nil), // 0: PrivateAccountV3Api
}
var file_PrivateAccountV3Api_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_PrivateAccountV3Api_proto_init() }
func file_PrivateAccountV3Api_proto_init() {
	if File_PrivateAccountV3Api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PrivateAccountV3Api_proto_rawDesc), len(file_PrivateAccountV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PrivateAccountV3Api_proto_goTypes,
		DependencyIndexes: file_PrivateAccountV3Api_proto_depIdxs,
		MessageInfos:      file_PrivateAccountV3Api_proto_msgTypes,
	}.Build()
	File_PrivateAccountV3Api_proto = out.File
	file_PrivateAccountV3Api_proto_goTypes = nil
	file_PrivateAccountV3Api_proto_depIdxs = nil
}
