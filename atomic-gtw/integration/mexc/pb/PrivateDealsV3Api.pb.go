// <EMAIL>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PrivateDealsV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PrivateDealsV3Api struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Price         string                 `protobuf:"bytes,1,opt,name=price,proto3" json:"price,omitempty"`
	Quantity      string                 `protobuf:"bytes,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Amount        string                 `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	TradeType     int32                  `protobuf:"varint,4,opt,name=tradeType,proto3" json:"tradeType,omitempty"`
	IsMaker       bool                   `protobuf:"varint,5,opt,name=isMaker,proto3" json:"isMaker,omitempty"`
	IsSelfTrade   bool                   `protobuf:"varint,6,opt,name=isSelfTrade,proto3" json:"isSelfTrade,omitempty"`
	TradeId       string                 `protobuf:"bytes,7,opt,name=tradeId,proto3" json:"tradeId,omitempty"`
	ClientOrderId string                 `protobuf:"bytes,8,opt,name=clientOrderId,proto3" json:"clientOrderId,omitempty"`
	OrderId       string                 `protobuf:"bytes,9,opt,name=orderId,proto3" json:"orderId,omitempty"`
	FeeAmount     string                 `protobuf:"bytes,10,opt,name=feeAmount,proto3" json:"feeAmount,omitempty"`
	FeeCurrency   string                 `protobuf:"bytes,11,opt,name=feeCurrency,proto3" json:"feeCurrency,omitempty"`
	Time          int64                  `protobuf:"varint,12,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrivateDealsV3Api) Reset() {
	*x = PrivateDealsV3Api{}
	mi := &file_PrivateDealsV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrivateDealsV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivateDealsV3Api) ProtoMessage() {}

func (x *PrivateDealsV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PrivateDealsV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivateDealsV3Api.ProtoReflect.Descriptor instead.
func (*PrivateDealsV3Api) Descriptor() ([]byte, []int) {
	return file_PrivateDealsV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PrivateDealsV3Api) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *PrivateDealsV3Api) GetQuantity() string {
	if x != nil {
		return x.Quantity
	}
	return ""
}

func (x *PrivateDealsV3Api) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *PrivateDealsV3Api) GetTradeType() int32 {
	if x != nil {
		return x.TradeType
	}
	return 0
}

func (x *PrivateDealsV3Api) GetIsMaker() bool {
	if x != nil {
		return x.IsMaker
	}
	return false
}

func (x *PrivateDealsV3Api) GetIsSelfTrade() bool {
	if x != nil {
		return x.IsSelfTrade
	}
	return false
}

func (x *PrivateDealsV3Api) GetTradeId() string {
	if x != nil {
		return x.TradeId
	}
	return ""
}

func (x *PrivateDealsV3Api) GetClientOrderId() string {
	if x != nil {
		return x.ClientOrderId
	}
	return ""
}

func (x *PrivateDealsV3Api) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *PrivateDealsV3Api) GetFeeAmount() string {
	if x != nil {
		return x.FeeAmount
	}
	return ""
}

func (x *PrivateDealsV3Api) GetFeeCurrency() string {
	if x != nil {
		return x.FeeCurrency
	}
	return ""
}

func (x *PrivateDealsV3Api) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

var File_PrivateDealsV3Api_proto protoreflect.FileDescriptor

const file_PrivateDealsV3Api_proto_rawDesc = "" +
	"\n" +
	"\x17PrivateDealsV3Api.proto\"\xe5\x02\n" +
	"\x11PrivateDealsV3Api\x12\x14\n" +
	"\x05price\x18\x01 \x01(\tR\x05price\x12\x1a\n" +
	"\bquantity\x18\x02 \x01(\tR\bquantity\x12\x16\n" +
	"\x06amount\x18\x03 \x01(\tR\x06amount\x12\x1c\n" +
	"\ttradeType\x18\x04 \x01(\x05R\ttradeType\x12\x18\n" +
	"\aisMaker\x18\x05 \x01(\bR\aisMaker\x12 \n" +
	"\visSelfTrade\x18\x06 \x01(\bR\visSelfTrade\x12\x18\n" +
	"\atradeId\x18\a \x01(\tR\atradeId\x12$\n" +
	"\rclientOrderId\x18\b \x01(\tR\rclientOrderId\x12\x18\n" +
	"\aorderId\x18\t \x01(\tR\aorderId\x12\x1c\n" +
	"\tfeeAmount\x18\n" +
	" \x01(\tR\tfeeAmount\x12 \n" +
	"\vfeeCurrency\x18\v \x01(\tR\vfeeCurrency\x12\x12\n" +
	"\x04time\x18\f \x01(\x03R\x04timeBm\n" +
	"\x1ccom.mxc.push.common.protobufB\x16PrivateDealsV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PrivateDealsV3Api_proto_rawDescOnce sync.Once
	file_PrivateDealsV3Api_proto_rawDescData []byte
)

func file_PrivateDealsV3Api_proto_rawDescGZIP() []byte {
	file_PrivateDealsV3Api_proto_rawDescOnce.Do(func() {
		file_PrivateDealsV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PrivateDealsV3Api_proto_rawDesc), len(file_PrivateDealsV3Api_proto_rawDesc)))
	})
	return file_PrivateDealsV3Api_proto_rawDescData
}

var file_PrivateDealsV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_PrivateDealsV3Api_proto_goTypes = []any{
	(*PrivateDealsV3Api)(nil), // 0: PrivateDealsV3Api
}
var file_PrivateDealsV3Api_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_PrivateDealsV3Api_proto_init() }
func file_PrivateDealsV3Api_proto_init() {
	if File_PrivateDealsV3Api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PrivateDealsV3Api_proto_rawDesc), len(file_PrivateDealsV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PrivateDealsV3Api_proto_goTypes,
		DependencyIndexes: file_PrivateDealsV3Api_proto_depIdxs,
		MessageInfos:      file_PrivateDealsV3Api_proto_msgTypes,
	}.Build()
	File_PrivateDealsV3Api_proto = out.File
	file_PrivateDealsV3Api_proto_goTypes = nil
	file_PrivateDealsV3Api_proto_depIdxs = nil
}
