// <EMAIL>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PublicBookTickerV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublicBookTickerV3Api struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BidPrice      string                 `protobuf:"bytes,1,opt,name=bidPrice,proto3" json:"bidPrice,omitempty"`
	BidQuantity   string                 `protobuf:"bytes,2,opt,name=bidQuantity,proto3" json:"bidQuantity,omitempty"`
	AskPrice      string                 `protobuf:"bytes,3,opt,name=askPrice,proto3" json:"askPrice,omitempty"`
	AskQuantity   string                 `protobuf:"bytes,4,opt,name=askQuantity,proto3" json:"askQuantity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicBookTickerV3Api) Reset() {
	*x = PublicBookTickerV3Api{}
	mi := &file_PublicBookTickerV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicBookTickerV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicBookTickerV3Api) ProtoMessage() {}

func (x *PublicBookTickerV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PublicBookTickerV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicBookTickerV3Api.ProtoReflect.Descriptor instead.
func (*PublicBookTickerV3Api) Descriptor() ([]byte, []int) {
	return file_PublicBookTickerV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PublicBookTickerV3Api) GetBidPrice() string {
	if x != nil {
		return x.BidPrice
	}
	return ""
}

func (x *PublicBookTickerV3Api) GetBidQuantity() string {
	if x != nil {
		return x.BidQuantity
	}
	return ""
}

func (x *PublicBookTickerV3Api) GetAskPrice() string {
	if x != nil {
		return x.AskPrice
	}
	return ""
}

func (x *PublicBookTickerV3Api) GetAskQuantity() string {
	if x != nil {
		return x.AskQuantity
	}
	return ""
}

var File_PublicBookTickerV3Api_proto protoreflect.FileDescriptor

const file_PublicBookTickerV3Api_proto_rawDesc = "" +
	"\n" +
	"\x1bPublicBookTickerV3Api.proto\"\x93\x01\n" +
	"\x15PublicBookTickerV3Api\x12\x1a\n" +
	"\bbidPrice\x18\x01 \x01(\tR\bbidPrice\x12 \n" +
	"\vbidQuantity\x18\x02 \x01(\tR\vbidQuantity\x12\x1a\n" +
	"\baskPrice\x18\x03 \x01(\tR\baskPrice\x12 \n" +
	"\vaskQuantity\x18\x04 \x01(\tR\vaskQuantityBq\n" +
	"\x1ccom.mxc.push.common.protobufB\x1aPublicBookTickerV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PublicBookTickerV3Api_proto_rawDescOnce sync.Once
	file_PublicBookTickerV3Api_proto_rawDescData []byte
)

func file_PublicBookTickerV3Api_proto_rawDescGZIP() []byte {
	file_PublicBookTickerV3Api_proto_rawDescOnce.Do(func() {
		file_PublicBookTickerV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PublicBookTickerV3Api_proto_rawDesc), len(file_PublicBookTickerV3Api_proto_rawDesc)))
	})
	return file_PublicBookTickerV3Api_proto_rawDescData
}

var file_PublicBookTickerV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_PublicBookTickerV3Api_proto_goTypes = []any{
	(*PublicBookTickerV3Api)(nil), // 0: PublicBookTickerV3Api
}
var file_PublicBookTickerV3Api_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_PublicBookTickerV3Api_proto_init() }
func file_PublicBookTickerV3Api_proto_init() {
	if File_PublicBookTickerV3Api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PublicBookTickerV3Api_proto_rawDesc), len(file_PublicBookTickerV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PublicBookTickerV3Api_proto_goTypes,
		DependencyIndexes: file_PublicBookTickerV3Api_proto_depIdxs,
		MessageInfos:      file_PublicBookTickerV3Api_proto_msgTypes,
	}.Build()
	File_PublicBookTickerV3Api_proto = out.File
	file_PublicBookTickerV3Api_proto_goTypes = nil
	file_PublicBookTickerV3Api_proto_depIdxs = nil
}
