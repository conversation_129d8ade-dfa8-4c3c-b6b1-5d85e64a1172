// <EMAIL>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PrivateOrdersV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PrivateOrdersV3Api struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ClientId           string                 `protobuf:"bytes,2,opt,name=clientId,proto3" json:"clientId,omitempty"`
	Price              string                 `protobuf:"bytes,3,opt,name=price,proto3" json:"price,omitempty"`
	Quantity           string                 `protobuf:"bytes,4,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Amount             string                 `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	AvgPrice           string                 `protobuf:"bytes,6,opt,name=avgPrice,proto3" json:"avgPrice,omitempty"`
	OrderType          int32                  `protobuf:"varint,7,opt,name=orderType,proto3" json:"orderType,omitempty"`
	TradeType          int32                  `protobuf:"varint,8,opt,name=tradeType,proto3" json:"tradeType,omitempty"`
	IsMaker            bool                   `protobuf:"varint,9,opt,name=isMaker,proto3" json:"isMaker,omitempty"`
	RemainAmount       string                 `protobuf:"bytes,10,opt,name=remainAmount,proto3" json:"remainAmount,omitempty"`
	RemainQuantity     string                 `protobuf:"bytes,11,opt,name=remainQuantity,proto3" json:"remainQuantity,omitempty"`
	LastDealQuantity   *string                `protobuf:"bytes,12,opt,name=lastDealQuantity,proto3,oneof" json:"lastDealQuantity,omitempty"`
	CumulativeQuantity string                 `protobuf:"bytes,13,opt,name=cumulativeQuantity,proto3" json:"cumulativeQuantity,omitempty"`
	CumulativeAmount   string                 `protobuf:"bytes,14,opt,name=cumulativeAmount,proto3" json:"cumulativeAmount,omitempty"`
	Status             int32                  `protobuf:"varint,15,opt,name=status,proto3" json:"status,omitempty"`
	CreateTime         int64                  `protobuf:"varint,16,opt,name=createTime,proto3" json:"createTime,omitempty"`
	Market             *string                `protobuf:"bytes,17,opt,name=market,proto3,oneof" json:"market,omitempty"`
	TriggerType        *int32                 `protobuf:"varint,18,opt,name=triggerType,proto3,oneof" json:"triggerType,omitempty"`
	TriggerPrice       *string                `protobuf:"bytes,19,opt,name=triggerPrice,proto3,oneof" json:"triggerPrice,omitempty"`
	State              *int32                 `protobuf:"varint,20,opt,name=state,proto3,oneof" json:"state,omitempty"`
	OcoId              *string                `protobuf:"bytes,21,opt,name=ocoId,proto3,oneof" json:"ocoId,omitempty"`
	RouteFactor        *string                `protobuf:"bytes,22,opt,name=routeFactor,proto3,oneof" json:"routeFactor,omitempty"`
	SymbolId           *string                `protobuf:"bytes,23,opt,name=symbolId,proto3,oneof" json:"symbolId,omitempty"`
	MarketId           *string                `protobuf:"bytes,24,opt,name=marketId,proto3,oneof" json:"marketId,omitempty"`
	MarketCurrencyId   *string                `protobuf:"bytes,25,opt,name=marketCurrencyId,proto3,oneof" json:"marketCurrencyId,omitempty"`
	CurrencyId         *string                `protobuf:"bytes,26,opt,name=currencyId,proto3,oneof" json:"currencyId,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PrivateOrdersV3Api) Reset() {
	*x = PrivateOrdersV3Api{}
	mi := &file_PrivateOrdersV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrivateOrdersV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivateOrdersV3Api) ProtoMessage() {}

func (x *PrivateOrdersV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PrivateOrdersV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivateOrdersV3Api.ProtoReflect.Descriptor instead.
func (*PrivateOrdersV3Api) Descriptor() ([]byte, []int) {
	return file_PrivateOrdersV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PrivateOrdersV3Api) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetQuantity() string {
	if x != nil {
		return x.Quantity
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetAvgPrice() string {
	if x != nil {
		return x.AvgPrice
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetOrderType() int32 {
	if x != nil {
		return x.OrderType
	}
	return 0
}

func (x *PrivateOrdersV3Api) GetTradeType() int32 {
	if x != nil {
		return x.TradeType
	}
	return 0
}

func (x *PrivateOrdersV3Api) GetIsMaker() bool {
	if x != nil {
		return x.IsMaker
	}
	return false
}

func (x *PrivateOrdersV3Api) GetRemainAmount() string {
	if x != nil {
		return x.RemainAmount
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetRemainQuantity() string {
	if x != nil {
		return x.RemainQuantity
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetLastDealQuantity() string {
	if x != nil && x.LastDealQuantity != nil {
		return *x.LastDealQuantity
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetCumulativeQuantity() string {
	if x != nil {
		return x.CumulativeQuantity
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetCumulativeAmount() string {
	if x != nil {
		return x.CumulativeAmount
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PrivateOrdersV3Api) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *PrivateOrdersV3Api) GetMarket() string {
	if x != nil && x.Market != nil {
		return *x.Market
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetTriggerType() int32 {
	if x != nil && x.TriggerType != nil {
		return *x.TriggerType
	}
	return 0
}

func (x *PrivateOrdersV3Api) GetTriggerPrice() string {
	if x != nil && x.TriggerPrice != nil {
		return *x.TriggerPrice
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetState() int32 {
	if x != nil && x.State != nil {
		return *x.State
	}
	return 0
}

func (x *PrivateOrdersV3Api) GetOcoId() string {
	if x != nil && x.OcoId != nil {
		return *x.OcoId
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetRouteFactor() string {
	if x != nil && x.RouteFactor != nil {
		return *x.RouteFactor
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetSymbolId() string {
	if x != nil && x.SymbolId != nil {
		return *x.SymbolId
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetMarketId() string {
	if x != nil && x.MarketId != nil {
		return *x.MarketId
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetMarketCurrencyId() string {
	if x != nil && x.MarketCurrencyId != nil {
		return *x.MarketCurrencyId
	}
	return ""
}

func (x *PrivateOrdersV3Api) GetCurrencyId() string {
	if x != nil && x.CurrencyId != nil {
		return *x.CurrencyId
	}
	return ""
}

var File_PrivateOrdersV3Api_proto protoreflect.FileDescriptor

const file_PrivateOrdersV3Api_proto_rawDesc = "" +
	"\n" +
	"\x18PrivateOrdersV3Api.proto\"\x92\b\n" +
	"\x12PrivateOrdersV3Api\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\bclientId\x18\x02 \x01(\tR\bclientId\x12\x14\n" +
	"\x05price\x18\x03 \x01(\tR\x05price\x12\x1a\n" +
	"\bquantity\x18\x04 \x01(\tR\bquantity\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\tR\x06amount\x12\x1a\n" +
	"\bavgPrice\x18\x06 \x01(\tR\bavgPrice\x12\x1c\n" +
	"\torderType\x18\a \x01(\x05R\torderType\x12\x1c\n" +
	"\ttradeType\x18\b \x01(\x05R\ttradeType\x12\x18\n" +
	"\aisMaker\x18\t \x01(\bR\aisMaker\x12\"\n" +
	"\fremainAmount\x18\n" +
	" \x01(\tR\fremainAmount\x12&\n" +
	"\x0eremainQuantity\x18\v \x01(\tR\x0eremainQuantity\x12/\n" +
	"\x10lastDealQuantity\x18\f \x01(\tH\x00R\x10lastDealQuantity\x88\x01\x01\x12.\n" +
	"\x12cumulativeQuantity\x18\r \x01(\tR\x12cumulativeQuantity\x12*\n" +
	"\x10cumulativeAmount\x18\x0e \x01(\tR\x10cumulativeAmount\x12\x16\n" +
	"\x06status\x18\x0f \x01(\x05R\x06status\x12\x1e\n" +
	"\n" +
	"createTime\x18\x10 \x01(\x03R\n" +
	"createTime\x12\x1b\n" +
	"\x06market\x18\x11 \x01(\tH\x01R\x06market\x88\x01\x01\x12%\n" +
	"\vtriggerType\x18\x12 \x01(\x05H\x02R\vtriggerType\x88\x01\x01\x12'\n" +
	"\ftriggerPrice\x18\x13 \x01(\tH\x03R\ftriggerPrice\x88\x01\x01\x12\x19\n" +
	"\x05state\x18\x14 \x01(\x05H\x04R\x05state\x88\x01\x01\x12\x19\n" +
	"\x05ocoId\x18\x15 \x01(\tH\x05R\x05ocoId\x88\x01\x01\x12%\n" +
	"\vrouteFactor\x18\x16 \x01(\tH\x06R\vrouteFactor\x88\x01\x01\x12\x1f\n" +
	"\bsymbolId\x18\x17 \x01(\tH\aR\bsymbolId\x88\x01\x01\x12\x1f\n" +
	"\bmarketId\x18\x18 \x01(\tH\bR\bmarketId\x88\x01\x01\x12/\n" +
	"\x10marketCurrencyId\x18\x19 \x01(\tH\tR\x10marketCurrencyId\x88\x01\x01\x12#\n" +
	"\n" +
	"currencyId\x18\x1a \x01(\tH\n" +
	"R\n" +
	"currencyId\x88\x01\x01B\x13\n" +
	"\x11_lastDealQuantityB\t\n" +
	"\a_marketB\x0e\n" +
	"\f_triggerTypeB\x0f\n" +
	"\r_triggerPriceB\b\n" +
	"\x06_stateB\b\n" +
	"\x06_ocoIdB\x0e\n" +
	"\f_routeFactorB\v\n" +
	"\t_symbolIdB\v\n" +
	"\t_marketIdB\x13\n" +
	"\x11_marketCurrencyIdB\r\n" +
	"\v_currencyIdBn\n" +
	"\x1ccom.mxc.push.common.protobufB\x17PrivateOrdersV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PrivateOrdersV3Api_proto_rawDescOnce sync.Once
	file_PrivateOrdersV3Api_proto_rawDescData []byte
)

func file_PrivateOrdersV3Api_proto_rawDescGZIP() []byte {
	file_PrivateOrdersV3Api_proto_rawDescOnce.Do(func() {
		file_PrivateOrdersV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PrivateOrdersV3Api_proto_rawDesc), len(file_PrivateOrdersV3Api_proto_rawDesc)))
	})
	return file_PrivateOrdersV3Api_proto_rawDescData
}

var file_PrivateOrdersV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_PrivateOrdersV3Api_proto_goTypes = []any{
	(*PrivateOrdersV3Api)(nil), // 0: PrivateOrdersV3Api
}
var file_PrivateOrdersV3Api_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_PrivateOrdersV3Api_proto_init() }
func file_PrivateOrdersV3Api_proto_init() {
	if File_PrivateOrdersV3Api_proto != nil {
		return
	}
	file_PrivateOrdersV3Api_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PrivateOrdersV3Api_proto_rawDesc), len(file_PrivateOrdersV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PrivateOrdersV3Api_proto_goTypes,
		DependencyIndexes: file_PrivateOrdersV3Api_proto_depIdxs,
		MessageInfos:      file_PrivateOrdersV3Api_proto_msgTypes,
	}.Build()
	File_PrivateOrdersV3Api_proto = out.File
	file_PrivateOrdersV3Api_proto_goTypes = nil
	file_PrivateOrdersV3Api_proto_depIdxs = nil
}
