// <EMAIL>@aggreType

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PublicAggreDepthsV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublicAggreDepthsV3Api struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Asks          []*PublicAggreDepthV3ApiItem `protobuf:"bytes,1,rep,name=asks,proto3" json:"asks,omitempty"`
	Bids          []*PublicAggreDepthV3ApiItem `protobuf:"bytes,2,rep,name=bids,proto3" json:"bids,omitempty"`
	EventType     string                       `protobuf:"bytes,3,opt,name=eventType,proto3" json:"eventType,omitempty"`
	FromVersion   string                       `protobuf:"bytes,4,opt,name=fromVersion,proto3" json:"fromVersion,omitempty"`
	ToVersion     string                       `protobuf:"bytes,5,opt,name=toVersion,proto3" json:"toVersion,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicAggreDepthsV3Api) Reset() {
	*x = PublicAggreDepthsV3Api{}
	mi := &file_PublicAggreDepthsV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicAggreDepthsV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicAggreDepthsV3Api) ProtoMessage() {}

func (x *PublicAggreDepthsV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PublicAggreDepthsV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicAggreDepthsV3Api.ProtoReflect.Descriptor instead.
func (*PublicAggreDepthsV3Api) Descriptor() ([]byte, []int) {
	return file_PublicAggreDepthsV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PublicAggreDepthsV3Api) GetAsks() []*PublicAggreDepthV3ApiItem {
	if x != nil {
		return x.Asks
	}
	return nil
}

func (x *PublicAggreDepthsV3Api) GetBids() []*PublicAggreDepthV3ApiItem {
	if x != nil {
		return x.Bids
	}
	return nil
}

func (x *PublicAggreDepthsV3Api) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *PublicAggreDepthsV3Api) GetFromVersion() string {
	if x != nil {
		return x.FromVersion
	}
	return ""
}

func (x *PublicAggreDepthsV3Api) GetToVersion() string {
	if x != nil {
		return x.ToVersion
	}
	return ""
}

type PublicAggreDepthV3ApiItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Price         string                 `protobuf:"bytes,1,opt,name=price,proto3" json:"price,omitempty"`
	Quantity      string                 `protobuf:"bytes,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicAggreDepthV3ApiItem) Reset() {
	*x = PublicAggreDepthV3ApiItem{}
	mi := &file_PublicAggreDepthsV3Api_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicAggreDepthV3ApiItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicAggreDepthV3ApiItem) ProtoMessage() {}

func (x *PublicAggreDepthV3ApiItem) ProtoReflect() protoreflect.Message {
	mi := &file_PublicAggreDepthsV3Api_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicAggreDepthV3ApiItem.ProtoReflect.Descriptor instead.
func (*PublicAggreDepthV3ApiItem) Descriptor() ([]byte, []int) {
	return file_PublicAggreDepthsV3Api_proto_rawDescGZIP(), []int{1}
}

func (x *PublicAggreDepthV3ApiItem) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *PublicAggreDepthV3ApiItem) GetQuantity() string {
	if x != nil {
		return x.Quantity
	}
	return ""
}

var File_PublicAggreDepthsV3Api_proto protoreflect.FileDescriptor

const file_PublicAggreDepthsV3Api_proto_rawDesc = "" +
	"\n" +
	"\x1cPublicAggreDepthsV3Api.proto\"\xd6\x01\n" +
	"\x16PublicAggreDepthsV3Api\x12.\n" +
	"\x04asks\x18\x01 \x03(\v2\x1a.PublicAggreDepthV3ApiItemR\x04asks\x12.\n" +
	"\x04bids\x18\x02 \x03(\v2\x1a.PublicAggreDepthV3ApiItemR\x04bids\x12\x1c\n" +
	"\teventType\x18\x03 \x01(\tR\teventType\x12 \n" +
	"\vfromVersion\x18\x04 \x01(\tR\vfromVersion\x12\x1c\n" +
	"\ttoVersion\x18\x05 \x01(\tR\ttoVersion\"M\n" +
	"\x19PublicAggreDepthV3ApiItem\x12\x14\n" +
	"\x05price\x18\x01 \x01(\tR\x05price\x12\x1a\n" +
	"\bquantity\x18\x02 \x01(\tR\bquantityBr\n" +
	"\x1ccom.mxc.push.common.protobufB\x1bPublicAggreDepthsV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PublicAggreDepthsV3Api_proto_rawDescOnce sync.Once
	file_PublicAggreDepthsV3Api_proto_rawDescData []byte
)

func file_PublicAggreDepthsV3Api_proto_rawDescGZIP() []byte {
	file_PublicAggreDepthsV3Api_proto_rawDescOnce.Do(func() {
		file_PublicAggreDepthsV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PublicAggreDepthsV3Api_proto_rawDesc), len(file_PublicAggreDepthsV3Api_proto_rawDesc)))
	})
	return file_PublicAggreDepthsV3Api_proto_rawDescData
}

var file_PublicAggreDepthsV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_PublicAggreDepthsV3Api_proto_goTypes = []any{
	(*PublicAggreDepthsV3Api)(nil),    // 0: PublicAggreDepthsV3Api
	(*PublicAggreDepthV3ApiItem)(nil), // 1: PublicAggreDepthV3ApiItem
}
var file_PublicAggreDepthsV3Api_proto_depIdxs = []int32{
	1, // 0: PublicAggreDepthsV3Api.asks:type_name -> PublicAggreDepthV3ApiItem
	1, // 1: PublicAggreDepthsV3Api.bids:type_name -> PublicAggreDepthV3ApiItem
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_PublicAggreDepthsV3Api_proto_init() }
func file_PublicAggreDepthsV3Api_proto_init() {
	if File_PublicAggreDepthsV3Api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PublicAggreDepthsV3Api_proto_rawDesc), len(file_PublicAggreDepthsV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PublicAggreDepthsV3Api_proto_goTypes,
		DependencyIndexes: file_PublicAggreDepthsV3Api_proto_depIdxs,
		MessageInfos:      file_PublicAggreDepthsV3Api_proto_msgTypes,
	}.Build()
	File_PublicAggreDepthsV3Api_proto = out.File
	file_PublicAggreDepthsV3Api_proto_goTypes = nil
	file_PublicAggreDepthsV3Api_proto_depIdxs = nil
}
