// <EMAIL>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PublicIncreaseDepthsBatchV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublicIncreaseDepthsBatchV3Api struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Items         []*PublicIncreaseDepthsV3Api `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	EventType     string                       `protobuf:"bytes,2,opt,name=eventType,proto3" json:"eventType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicIncreaseDepthsBatchV3Api) Reset() {
	*x = PublicIncreaseDepthsBatchV3Api{}
	mi := &file_PublicIncreaseDepthsBatchV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicIncreaseDepthsBatchV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicIncreaseDepthsBatchV3Api) ProtoMessage() {}

func (x *PublicIncreaseDepthsBatchV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PublicIncreaseDepthsBatchV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicIncreaseDepthsBatchV3Api.ProtoReflect.Descriptor instead.
func (*PublicIncreaseDepthsBatchV3Api) Descriptor() ([]byte, []int) {
	return file_PublicIncreaseDepthsBatchV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PublicIncreaseDepthsBatchV3Api) GetItems() []*PublicIncreaseDepthsV3Api {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *PublicIncreaseDepthsBatchV3Api) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

var File_PublicIncreaseDepthsBatchV3Api_proto protoreflect.FileDescriptor

const file_PublicIncreaseDepthsBatchV3Api_proto_rawDesc = "" +
	"\n" +
	"$PublicIncreaseDepthsBatchV3Api.proto\x1a\x1fPublicIncreaseDepthsV3Api.proto\"p\n" +
	"\x1ePublicIncreaseDepthsBatchV3Api\x120\n" +
	"\x05items\x18\x01 \x03(\v2\x1a.PublicIncreaseDepthsV3ApiR\x05items\x12\x1c\n" +
	"\teventType\x18\x02 \x01(\tR\teventTypeBz\n" +
	"\x1ccom.mxc.push.common.protobufB#PublicIncreaseDepthsBatchV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PublicIncreaseDepthsBatchV3Api_proto_rawDescOnce sync.Once
	file_PublicIncreaseDepthsBatchV3Api_proto_rawDescData []byte
)

func file_PublicIncreaseDepthsBatchV3Api_proto_rawDescGZIP() []byte {
	file_PublicIncreaseDepthsBatchV3Api_proto_rawDescOnce.Do(func() {
		file_PublicIncreaseDepthsBatchV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PublicIncreaseDepthsBatchV3Api_proto_rawDesc), len(file_PublicIncreaseDepthsBatchV3Api_proto_rawDesc)))
	})
	return file_PublicIncreaseDepthsBatchV3Api_proto_rawDescData
}

var file_PublicIncreaseDepthsBatchV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_PublicIncreaseDepthsBatchV3Api_proto_goTypes = []any{
	(*PublicIncreaseDepthsBatchV3Api)(nil), // 0: PublicIncreaseDepthsBatchV3Api
	(*PublicIncreaseDepthsV3Api)(nil),      // 1: PublicIncreaseDepthsV3Api
}
var file_PublicIncreaseDepthsBatchV3Api_proto_depIdxs = []int32{
	1, // 0: PublicIncreaseDepthsBatchV3Api.items:type_name -> PublicIncreaseDepthsV3Api
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_PublicIncreaseDepthsBatchV3Api_proto_init() }
func file_PublicIncreaseDepthsBatchV3Api_proto_init() {
	if File_PublicIncreaseDepthsBatchV3Api_proto != nil {
		return
	}
	file_PublicIncreaseDepthsV3Api_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PublicIncreaseDepthsBatchV3Api_proto_rawDesc), len(file_PublicIncreaseDepthsBatchV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PublicIncreaseDepthsBatchV3Api_proto_goTypes,
		DependencyIndexes: file_PublicIncreaseDepthsBatchV3Api_proto_depIdxs,
		MessageInfos:      file_PublicIncreaseDepthsBatchV3Api_proto_msgTypes,
	}.Build()
	File_PublicIncreaseDepthsBatchV3Api_proto = out.File
	file_PublicIncreaseDepthsBatchV3Api_proto_goTypes = nil
	file_PublicIncreaseDepthsBatchV3Api_proto_depIdxs = nil
}
