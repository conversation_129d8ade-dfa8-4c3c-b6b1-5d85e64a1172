// <EMAIL>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PublicAggreBookTickerV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublicAggreBookTickerV3Api struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BidPrice      string                 `protobuf:"bytes,1,opt,name=bidPrice,proto3" json:"bidPrice,omitempty"`
	BidQuantity   string                 `protobuf:"bytes,2,opt,name=bidQuantity,proto3" json:"bidQuantity,omitempty"`
	AskPrice      string                 `protobuf:"bytes,3,opt,name=askPrice,proto3" json:"askPrice,omitempty"`
	AskQuantity   string                 `protobuf:"bytes,4,opt,name=askQuantity,proto3" json:"askQuantity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicAggreBookTickerV3Api) Reset() {
	*x = PublicAggreBookTickerV3Api{}
	mi := &file_PublicAggreBookTickerV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicAggreBookTickerV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicAggreBookTickerV3Api) ProtoMessage() {}

func (x *PublicAggreBookTickerV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PublicAggreBookTickerV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicAggreBookTickerV3Api.ProtoReflect.Descriptor instead.
func (*PublicAggreBookTickerV3Api) Descriptor() ([]byte, []int) {
	return file_PublicAggreBookTickerV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PublicAggreBookTickerV3Api) GetBidPrice() string {
	if x != nil {
		return x.BidPrice
	}
	return ""
}

func (x *PublicAggreBookTickerV3Api) GetBidQuantity() string {
	if x != nil {
		return x.BidQuantity
	}
	return ""
}

func (x *PublicAggreBookTickerV3Api) GetAskPrice() string {
	if x != nil {
		return x.AskPrice
	}
	return ""
}

func (x *PublicAggreBookTickerV3Api) GetAskQuantity() string {
	if x != nil {
		return x.AskQuantity
	}
	return ""
}

var File_PublicAggreBookTickerV3Api_proto protoreflect.FileDescriptor

const file_PublicAggreBookTickerV3Api_proto_rawDesc = "" +
	"\n" +
	" PublicAggreBookTickerV3Api.proto\"\x98\x01\n" +
	"\x1aPublicAggreBookTickerV3Api\x12\x1a\n" +
	"\bbidPrice\x18\x01 \x01(\tR\bbidPrice\x12 \n" +
	"\vbidQuantity\x18\x02 \x01(\tR\vbidQuantity\x12\x1a\n" +
	"\baskPrice\x18\x03 \x01(\tR\baskPrice\x12 \n" +
	"\vaskQuantity\x18\x04 \x01(\tR\vaskQuantityBv\n" +
	"\x1ccom.mxc.push.common.protobufB\x1fPublicAggreBookTickerV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PublicAggreBookTickerV3Api_proto_rawDescOnce sync.Once
	file_PublicAggreBookTickerV3Api_proto_rawDescData []byte
)

func file_PublicAggreBookTickerV3Api_proto_rawDescGZIP() []byte {
	file_PublicAggreBookTickerV3Api_proto_rawDescOnce.Do(func() {
		file_PublicAggreBookTickerV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PublicAggreBookTickerV3Api_proto_rawDesc), len(file_PublicAggreBookTickerV3Api_proto_rawDesc)))
	})
	return file_PublicAggreBookTickerV3Api_proto_rawDescData
}

var file_PublicAggreBookTickerV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_PublicAggreBookTickerV3Api_proto_goTypes = []any{
	(*PublicAggreBookTickerV3Api)(nil), // 0: PublicAggreBookTickerV3Api
}
var file_PublicAggreBookTickerV3Api_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_PublicAggreBookTickerV3Api_proto_init() }
func file_PublicAggreBookTickerV3Api_proto_init() {
	if File_PublicAggreBookTickerV3Api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PublicAggreBookTickerV3Api_proto_rawDesc), len(file_PublicAggreBookTickerV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PublicAggreBookTickerV3Api_proto_goTypes,
		DependencyIndexes: file_PublicAggreBookTickerV3Api_proto_depIdxs,
		MessageInfos:      file_PublicAggreBookTickerV3Api_proto_msgTypes,
	}.Build()
	File_PublicAggreBookTickerV3Api_proto = out.File
	file_PublicAggreBookTickerV3Api_proto_goTypes = nil
	file_PublicAggreBookTickerV3Api_proto_depIdxs = nil
}
