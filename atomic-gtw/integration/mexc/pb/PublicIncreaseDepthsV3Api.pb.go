// <EMAIL>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PublicIncreaseDepthsV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublicIncreaseDepthsV3Api struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	Asks          []*PublicIncreaseDepthV3ApiItem `protobuf:"bytes,1,rep,name=asks,proto3" json:"asks,omitempty"`
	Bids          []*PublicIncreaseDepthV3ApiItem `protobuf:"bytes,2,rep,name=bids,proto3" json:"bids,omitempty"`
	EventType     string                          `protobuf:"bytes,3,opt,name=eventType,proto3" json:"eventType,omitempty"`
	Version       string                          `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicIncreaseDepthsV3Api) Reset() {
	*x = PublicIncreaseDepthsV3Api{}
	mi := &file_PublicIncreaseDepthsV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicIncreaseDepthsV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicIncreaseDepthsV3Api) ProtoMessage() {}

func (x *PublicIncreaseDepthsV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PublicIncreaseDepthsV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicIncreaseDepthsV3Api.ProtoReflect.Descriptor instead.
func (*PublicIncreaseDepthsV3Api) Descriptor() ([]byte, []int) {
	return file_PublicIncreaseDepthsV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PublicIncreaseDepthsV3Api) GetAsks() []*PublicIncreaseDepthV3ApiItem {
	if x != nil {
		return x.Asks
	}
	return nil
}

func (x *PublicIncreaseDepthsV3Api) GetBids() []*PublicIncreaseDepthV3ApiItem {
	if x != nil {
		return x.Bids
	}
	return nil
}

func (x *PublicIncreaseDepthsV3Api) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *PublicIncreaseDepthsV3Api) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type PublicIncreaseDepthV3ApiItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Price         string                 `protobuf:"bytes,1,opt,name=price,proto3" json:"price,omitempty"`
	Quantity      string                 `protobuf:"bytes,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicIncreaseDepthV3ApiItem) Reset() {
	*x = PublicIncreaseDepthV3ApiItem{}
	mi := &file_PublicIncreaseDepthsV3Api_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicIncreaseDepthV3ApiItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicIncreaseDepthV3ApiItem) ProtoMessage() {}

func (x *PublicIncreaseDepthV3ApiItem) ProtoReflect() protoreflect.Message {
	mi := &file_PublicIncreaseDepthsV3Api_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicIncreaseDepthV3ApiItem.ProtoReflect.Descriptor instead.
func (*PublicIncreaseDepthV3ApiItem) Descriptor() ([]byte, []int) {
	return file_PublicIncreaseDepthsV3Api_proto_rawDescGZIP(), []int{1}
}

func (x *PublicIncreaseDepthV3ApiItem) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *PublicIncreaseDepthV3ApiItem) GetQuantity() string {
	if x != nil {
		return x.Quantity
	}
	return ""
}

var File_PublicIncreaseDepthsV3Api_proto protoreflect.FileDescriptor

const file_PublicIncreaseDepthsV3Api_proto_rawDesc = "" +
	"\n" +
	"\x1fPublicIncreaseDepthsV3Api.proto\"\xb9\x01\n" +
	"\x19PublicIncreaseDepthsV3Api\x121\n" +
	"\x04asks\x18\x01 \x03(\v2\x1d.PublicIncreaseDepthV3ApiItemR\x04asks\x121\n" +
	"\x04bids\x18\x02 \x03(\v2\x1d.PublicIncreaseDepthV3ApiItemR\x04bids\x12\x1c\n" +
	"\teventType\x18\x03 \x01(\tR\teventType\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\"P\n" +
	"\x1cPublicIncreaseDepthV3ApiItem\x12\x14\n" +
	"\x05price\x18\x01 \x01(\tR\x05price\x12\x1a\n" +
	"\bquantity\x18\x02 \x01(\tR\bquantityBu\n" +
	"\x1ccom.mxc.push.common.protobufB\x1ePublicIncreaseDepthsV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PublicIncreaseDepthsV3Api_proto_rawDescOnce sync.Once
	file_PublicIncreaseDepthsV3Api_proto_rawDescData []byte
)

func file_PublicIncreaseDepthsV3Api_proto_rawDescGZIP() []byte {
	file_PublicIncreaseDepthsV3Api_proto_rawDescOnce.Do(func() {
		file_PublicIncreaseDepthsV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PublicIncreaseDepthsV3Api_proto_rawDesc), len(file_PublicIncreaseDepthsV3Api_proto_rawDesc)))
	})
	return file_PublicIncreaseDepthsV3Api_proto_rawDescData
}

var file_PublicIncreaseDepthsV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_PublicIncreaseDepthsV3Api_proto_goTypes = []any{
	(*PublicIncreaseDepthsV3Api)(nil),    // 0: PublicIncreaseDepthsV3Api
	(*PublicIncreaseDepthV3ApiItem)(nil), // 1: PublicIncreaseDepthV3ApiItem
}
var file_PublicIncreaseDepthsV3Api_proto_depIdxs = []int32{
	1, // 0: PublicIncreaseDepthsV3Api.asks:type_name -> PublicIncreaseDepthV3ApiItem
	1, // 1: PublicIncreaseDepthsV3Api.bids:type_name -> PublicIncreaseDepthV3ApiItem
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_PublicIncreaseDepthsV3Api_proto_init() }
func file_PublicIncreaseDepthsV3Api_proto_init() {
	if File_PublicIncreaseDepthsV3Api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PublicIncreaseDepthsV3Api_proto_rawDesc), len(file_PublicIncreaseDepthsV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PublicIncreaseDepthsV3Api_proto_goTypes,
		DependencyIndexes: file_PublicIncreaseDepthsV3Api_proto_depIdxs,
		MessageInfos:      file_PublicIncreaseDepthsV3Api_proto_msgTypes,
	}.Build()
	File_PublicIncreaseDepthsV3Api_proto = out.File
	file_PublicIncreaseDepthsV3Api_proto_goTypes = nil
	file_PublicIncreaseDepthsV3Api_proto_depIdxs = nil
}
