// <EMAIL>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PublicMiniTickerV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublicMiniTickerV3Api struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 交易对名
	Symbol string `protobuf:"bytes,1,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 最新价格
	Price string `protobuf:"bytes,2,opt,name=price,proto3" json:"price,omitempty"`
	// utc+8时区涨跌幅
	Rate string `protobuf:"bytes,3,opt,name=rate,proto3" json:"rate,omitempty"`
	// 时区涨跌幅
	ZonedRate string `protobuf:"bytes,4,opt,name=zonedRate,proto3" json:"zonedRate,omitempty"`
	// 滚动最高价
	High string `protobuf:"bytes,5,opt,name=high,proto3" json:"high,omitempty"`
	// 滚动最低价
	Low string `protobuf:"bytes,6,opt,name=low,proto3" json:"low,omitempty"`
	// 滚动成交额
	Volume string `protobuf:"bytes,7,opt,name=volume,proto3" json:"volume,omitempty"`
	// 滚动成交量
	Quantity string `protobuf:"bytes,8,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// utc+8时区上期收盘价模式涨跌幅
	LastCloseRate string `protobuf:"bytes,9,opt,name=lastCloseRate,proto3" json:"lastCloseRate,omitempty"`
	// 上期收盘价模式时区涨跌幅
	LastCloseZonedRate string `protobuf:"bytes,10,opt,name=lastCloseZonedRate,proto3" json:"lastCloseZonedRate,omitempty"`
	// 上期收盘价模式滚动最高价
	LastCloseHigh string `protobuf:"bytes,11,opt,name=lastCloseHigh,proto3" json:"lastCloseHigh,omitempty"`
	// 上期收盘价模式滚动最低价
	LastCloseLow  string `protobuf:"bytes,12,opt,name=lastCloseLow,proto3" json:"lastCloseLow,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicMiniTickerV3Api) Reset() {
	*x = PublicMiniTickerV3Api{}
	mi := &file_PublicMiniTickerV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicMiniTickerV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicMiniTickerV3Api) ProtoMessage() {}

func (x *PublicMiniTickerV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PublicMiniTickerV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicMiniTickerV3Api.ProtoReflect.Descriptor instead.
func (*PublicMiniTickerV3Api) Descriptor() ([]byte, []int) {
	return file_PublicMiniTickerV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PublicMiniTickerV3Api) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *PublicMiniTickerV3Api) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *PublicMiniTickerV3Api) GetRate() string {
	if x != nil {
		return x.Rate
	}
	return ""
}

func (x *PublicMiniTickerV3Api) GetZonedRate() string {
	if x != nil {
		return x.ZonedRate
	}
	return ""
}

func (x *PublicMiniTickerV3Api) GetHigh() string {
	if x != nil {
		return x.High
	}
	return ""
}

func (x *PublicMiniTickerV3Api) GetLow() string {
	if x != nil {
		return x.Low
	}
	return ""
}

func (x *PublicMiniTickerV3Api) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

func (x *PublicMiniTickerV3Api) GetQuantity() string {
	if x != nil {
		return x.Quantity
	}
	return ""
}

func (x *PublicMiniTickerV3Api) GetLastCloseRate() string {
	if x != nil {
		return x.LastCloseRate
	}
	return ""
}

func (x *PublicMiniTickerV3Api) GetLastCloseZonedRate() string {
	if x != nil {
		return x.LastCloseZonedRate
	}
	return ""
}

func (x *PublicMiniTickerV3Api) GetLastCloseHigh() string {
	if x != nil {
		return x.LastCloseHigh
	}
	return ""
}

func (x *PublicMiniTickerV3Api) GetLastCloseLow() string {
	if x != nil {
		return x.LastCloseLow
	}
	return ""
}

var File_PublicMiniTickerV3Api_proto protoreflect.FileDescriptor

const file_PublicMiniTickerV3Api_proto_rawDesc = "" +
	"\n" +
	"\x1bPublicMiniTickerV3Api.proto\"\xf1\x02\n" +
	"\x15PublicMiniTickerV3Api\x12\x16\n" +
	"\x06symbol\x18\x01 \x01(\tR\x06symbol\x12\x14\n" +
	"\x05price\x18\x02 \x01(\tR\x05price\x12\x12\n" +
	"\x04rate\x18\x03 \x01(\tR\x04rate\x12\x1c\n" +
	"\tzonedRate\x18\x04 \x01(\tR\tzonedRate\x12\x12\n" +
	"\x04high\x18\x05 \x01(\tR\x04high\x12\x10\n" +
	"\x03low\x18\x06 \x01(\tR\x03low\x12\x16\n" +
	"\x06volume\x18\a \x01(\tR\x06volume\x12\x1a\n" +
	"\bquantity\x18\b \x01(\tR\bquantity\x12$\n" +
	"\rlastCloseRate\x18\t \x01(\tR\rlastCloseRate\x12.\n" +
	"\x12lastCloseZonedRate\x18\n" +
	" \x01(\tR\x12lastCloseZonedRate\x12$\n" +
	"\rlastCloseHigh\x18\v \x01(\tR\rlastCloseHigh\x12\"\n" +
	"\flastCloseLow\x18\f \x01(\tR\flastCloseLowBq\n" +
	"\x1ccom.mxc.push.common.protobufB\x1aPublicMiniTickerV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PublicMiniTickerV3Api_proto_rawDescOnce sync.Once
	file_PublicMiniTickerV3Api_proto_rawDescData []byte
)

func file_PublicMiniTickerV3Api_proto_rawDescGZIP() []byte {
	file_PublicMiniTickerV3Api_proto_rawDescOnce.Do(func() {
		file_PublicMiniTickerV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PublicMiniTickerV3Api_proto_rawDesc), len(file_PublicMiniTickerV3Api_proto_rawDesc)))
	})
	return file_PublicMiniTickerV3Api_proto_rawDescData
}

var file_PublicMiniTickerV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_PublicMiniTickerV3Api_proto_goTypes = []any{
	(*PublicMiniTickerV3Api)(nil), // 0: PublicMiniTickerV3Api
}
var file_PublicMiniTickerV3Api_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_PublicMiniTickerV3Api_proto_init() }
func file_PublicMiniTickerV3Api_proto_init() {
	if File_PublicMiniTickerV3Api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PublicMiniTickerV3Api_proto_rawDesc), len(file_PublicMiniTickerV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PublicMiniTickerV3Api_proto_goTypes,
		DependencyIndexes: file_PublicMiniTickerV3Api_proto_depIdxs,
		MessageInfos:      file_PublicMiniTickerV3Api_proto_msgTypes,
	}.Build()
	File_PublicMiniTickerV3Api_proto = out.File
	file_PublicMiniTickerV3Api_proto_goTypes = nil
	file_PublicMiniTickerV3Api_proto_depIdxs = nil
}
