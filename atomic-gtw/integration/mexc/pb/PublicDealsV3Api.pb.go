// <EMAIL>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.3
// source: PublicDealsV3Api.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublicDealsV3Api struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Deals         []*PublicDealsV3ApiItem `protobuf:"bytes,1,rep,name=deals,proto3" json:"deals,omitempty"`
	EventType     string                  `protobuf:"bytes,2,opt,name=eventType,proto3" json:"eventType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicDealsV3Api) Reset() {
	*x = PublicDealsV3Api{}
	mi := &file_PublicDealsV3Api_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicDealsV3Api) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicDealsV3Api) ProtoMessage() {}

func (x *PublicDealsV3Api) ProtoReflect() protoreflect.Message {
	mi := &file_PublicDealsV3Api_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicDealsV3Api.ProtoReflect.Descriptor instead.
func (*PublicDealsV3Api) Descriptor() ([]byte, []int) {
	return file_PublicDealsV3Api_proto_rawDescGZIP(), []int{0}
}

func (x *PublicDealsV3Api) GetDeals() []*PublicDealsV3ApiItem {
	if x != nil {
		return x.Deals
	}
	return nil
}

func (x *PublicDealsV3Api) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

type PublicDealsV3ApiItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Price         string                 `protobuf:"bytes,1,opt,name=price,proto3" json:"price,omitempty"`
	Quantity      string                 `protobuf:"bytes,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	TradeType     int32                  `protobuf:"varint,3,opt,name=tradeType,proto3" json:"tradeType,omitempty"`
	Time          int64                  `protobuf:"varint,4,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicDealsV3ApiItem) Reset() {
	*x = PublicDealsV3ApiItem{}
	mi := &file_PublicDealsV3Api_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicDealsV3ApiItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicDealsV3ApiItem) ProtoMessage() {}

func (x *PublicDealsV3ApiItem) ProtoReflect() protoreflect.Message {
	mi := &file_PublicDealsV3Api_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicDealsV3ApiItem.ProtoReflect.Descriptor instead.
func (*PublicDealsV3ApiItem) Descriptor() ([]byte, []int) {
	return file_PublicDealsV3Api_proto_rawDescGZIP(), []int{1}
}

func (x *PublicDealsV3ApiItem) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *PublicDealsV3ApiItem) GetQuantity() string {
	if x != nil {
		return x.Quantity
	}
	return ""
}

func (x *PublicDealsV3ApiItem) GetTradeType() int32 {
	if x != nil {
		return x.TradeType
	}
	return 0
}

func (x *PublicDealsV3ApiItem) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

var File_PublicDealsV3Api_proto protoreflect.FileDescriptor

const file_PublicDealsV3Api_proto_rawDesc = "" +
	"\n" +
	"\x16PublicDealsV3Api.proto\"]\n" +
	"\x10PublicDealsV3Api\x12+\n" +
	"\x05deals\x18\x01 \x03(\v2\x15.PublicDealsV3ApiItemR\x05deals\x12\x1c\n" +
	"\teventType\x18\x02 \x01(\tR\teventType\"z\n" +
	"\x14PublicDealsV3ApiItem\x12\x14\n" +
	"\x05price\x18\x01 \x01(\tR\x05price\x12\x1a\n" +
	"\bquantity\x18\x02 \x01(\tR\bquantity\x12\x1c\n" +
	"\ttradeType\x18\x03 \x01(\x05R\ttradeType\x12\x12\n" +
	"\x04time\x18\x04 \x01(\x03R\x04timeBl\n" +
	"\x1ccom.mxc.push.common.protobufB\x15PublicDealsV3ApiProtoH\x01P\x01Z1github.com/herenow/atomic-gtw/integration/mexc/pbb\x06proto3"

var (
	file_PublicDealsV3Api_proto_rawDescOnce sync.Once
	file_PublicDealsV3Api_proto_rawDescData []byte
)

func file_PublicDealsV3Api_proto_rawDescGZIP() []byte {
	file_PublicDealsV3Api_proto_rawDescOnce.Do(func() {
		file_PublicDealsV3Api_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_PublicDealsV3Api_proto_rawDesc), len(file_PublicDealsV3Api_proto_rawDesc)))
	})
	return file_PublicDealsV3Api_proto_rawDescData
}

var file_PublicDealsV3Api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_PublicDealsV3Api_proto_goTypes = []any{
	(*PublicDealsV3Api)(nil),     // 0: PublicDealsV3Api
	(*PublicDealsV3ApiItem)(nil), // 1: PublicDealsV3ApiItem
}
var file_PublicDealsV3Api_proto_depIdxs = []int32{
	1, // 0: PublicDealsV3Api.deals:type_name -> PublicDealsV3ApiItem
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_PublicDealsV3Api_proto_init() }
func file_PublicDealsV3Api_proto_init() {
	if File_PublicDealsV3Api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_PublicDealsV3Api_proto_rawDesc), len(file_PublicDealsV3Api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_PublicDealsV3Api_proto_goTypes,
		DependencyIndexes: file_PublicDealsV3Api_proto_depIdxs,
		MessageInfos:      file_PublicDealsV3Api_proto_msgTypes,
	}.Build()
	File_PublicDealsV3Api_proto = out.File
	file_PublicDealsV3Api_proto_goTypes = nil
	file_PublicDealsV3Api_proto_depIdxs = nil
}
