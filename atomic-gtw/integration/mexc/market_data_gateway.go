package mexc

import (
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"strconv"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/integration/mexc/pb"
	"github.com/herenow/atomic-gtw/utils"
	"google.golang.org/protobuf/proto"
)

const (
	wsEndpoint = "wss://wbs-api.mexc.com/ws"
)

type WsRequest struct {
	Method string   `json:"method"`
	Params []string `json:"params"`
}

type WsResponse struct {
	ID   int    `json:"id"`
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type depthQueue struct {
	ReceivedSnapshot bool
	Queue            []*pb.PublicAggreDepthsV3Api
	Lock             *sync.Mutex
}

type MarketDataGateway struct {
	options      gateway.Options
	api          *API
	tickCh       chan gateway.Tick
	lastVersions map[string]int64
	depthQueues  map[string]*depthQueue
	mutex        *sync.RWMutex
}

func NewMarketDataGateway(options gateway.Options, api *API, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options:      options,
		api:          api,
		tickCh:       tickCh,
		lastVersions: make(map[string]int64),
		depthQueues:  make(map[string]*depthQueue),
		mutex:        &sync.RWMutex{},
	}
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	// Limit to 15 markets per websocket connection, since MEXC has a limit of 30 channels per connection
	// and we are subscribing to 2 channels per market (depth and trades)
	batchesOf := 15
	batches := make([][]gateway.Market, ((len(markets)-1)/batchesOf)+1)

	log.Printf("MEXC subscribing to %d markets, will need %d websocket connections, maximum of %d markets on each websocket.", len(markets), len(batches), batchesOf)

	for index, market := range markets {
		group := index / batchesOf

		if batches[group] == nil {
			batches[group] = make([]gateway.Market, 0)
		}

		batches[group] = append(batches[group], market)
	}

	for _, batch := range batches {
		err := g.subscribeMarketData(batch)
		if err != nil {
			return err
		}
	}

	return nil
}

func (g *MarketDataGateway) subscribeMarketData(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	ws.SetDeflate(utils.WsNoDeflate)
	ws.SetTag("MEXC-MarketData")
	ws.SetProxies(g.options.Proxies)

	if err := ws.Connect(wsEndpoint); err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	for _, market := range markets {
		g.depthQueues[market.Symbol] = &depthQueue{
			ReceivedSnapshot: false,
			Queue:            make([]*pb.PublicAggreDepthsV3Api, 0),
			Lock:             &sync.Mutex{},
		}
	}

	msgCh := make(chan []byte, 100)
	ws.SubscribeMessages(msgCh)
	go g.messageHandler(msgCh)

	go g.startHeartbeat(ws)

	var depthParams, tradeParams []string
	for _, market := range markets {
		depthParams = append(depthParams, fmt.Sprintf("<EMAIL>@10ms@%s", market.Symbol))
		tradeParams = append(tradeParams, fmt.Sprintf("<EMAIL>@10ms@%s", market.Symbol))
	}

	if err := g.sendSubscription(ws, depthParams); err != nil {
		return fmt.Errorf("failed to subscribe to depth updates: %s", err)
	}

	if err := g.sendSubscription(ws, tradeParams); err != nil {
		return fmt.Errorf("failed to subscribe to trade updates: %s", err)
	}

	time.Sleep(1 * time.Second)

	var wg sync.WaitGroup
	errors := make(chan error, len(markets))
	sem := make(chan struct{}, 5) // Limit concurrent snapshot fetches to 5

	for _, market := range markets {
		wg.Add(1)
		go func(mkt gateway.Market) {
			defer wg.Done()

			sem <- struct{}{}
			defer func() { <-sem }()

			if err := g.fetchDepthSnapshot(mkt); err != nil {
				log.Printf("Failed to fetch initial depth snapshot for %s: %s", mkt.Symbol, err)
				errors <- fmt.Errorf("fetch snapshot for %s: %s", mkt.Symbol, err)
			}
		}(market)
	}

	wg.Wait()
	close(errors)

	var errs []error
	for err := range errors {
		errs = append(errs, err)
	}

	if len(errs) > 0 {
		return fmt.Errorf("failed to fetch snapshots for %d markets", len(errs))
	}

	return nil
}

func (g *MarketDataGateway) fetchDepthSnapshot(market gateway.Market) error {
	params := &url.Values{}
	params.Set("symbol", market.Symbol)
	params.Set("limit", strconv.Itoa(1000))
	depth, err := g.api.Depth(params)
	if err != nil {
		return fmt.Errorf("fetch depth snapshot: %s", err)
	}

	queue, ok := g.depthQueues[market.Symbol]
	if !ok {
		return fmt.Errorf("depth queue not found for symbol: %s", market.Symbol)
	}

	events := []gateway.Event{
		gateway.NewSnapshotSequenceEvent(gateway.SnapshotSequence{
			Symbol: market.Symbol,
		}),
	}

	for _, bid := range depth.Bids {
		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Bid,
				Price:  bid.Price,
				Amount: bid.Amount,
			},
		})
	}

	for _, ask := range depth.Asks {
		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Ask,
				Price:  ask.Price,
				Amount: ask.Amount,
			},
		})
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}

	g.mutex.Lock()
	g.lastVersions[market.Symbol] = depth.LastUpdateID
	g.mutex.Unlock()

	queue.Lock.Lock()
	queuedUpdates := queue.Queue
	queue.Queue = nil
	queue.ReceivedSnapshot = true
	queue.Lock.Unlock()

	processedCount := 0
	for _, queuedDepth := range queuedUpdates {
		toVersion, err := strconv.ParseInt(queuedDepth.ToVersion, 10, 64)
		if err != nil {
			return fmt.Errorf("parse toVersion '%s' for %s: %v", queuedDepth.FromVersion, market.Symbol, err)
		}

		// Skip outdated messages
		if toVersion <= depth.LastUpdateID {
			continue
		}

		if err := g.processDepthUpdate(market.Symbol, queuedDepth); err != nil {
			log.Printf("MEXC: Failed to process queued depth update for %s: %s", market.Symbol, err)
		} else {
			processedCount++
		}
	}

	if processedCount > 0 {
		log.Printf("MEXC: Processed %d queued depth updates for %s after snapshot", processedCount, market.Symbol)
	}

	return nil
}

func (g *MarketDataGateway) sendSubscription(ws *utils.WsClient, channels []string) error {
	request := WsRequest{
		Method: "SUBSCRIPTION",
		Params: channels,
	}

	data, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("marshal subscription request: %s", err)
	}

	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("write subscription message: %s", err)
	}

	return nil
}

func (g *MarketDataGateway) startHeartbeat(ws *utils.WsClient) {
	ticker := time.NewTicker(15 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		pingMsg := WsRequest{
			Method: "PING",
		}

		data, err := json.Marshal(pingMsg)
		if err != nil {
			log.Printf("Failed to marshal ping message: %s", err)
			continue
		}

		err = ws.WriteMessage(data)
		if err != nil {
			err = fmt.Errorf("write ping message: %s", err)
			panic(err)
		}
	}
}

type jsonMessage struct {
	ID   int    `json:"id"`
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

func (g *MarketDataGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		if len(data) == 0 {
			continue
		}

		if data[0] == '{' {
			var msg jsonMessage
			if err := json.Unmarshal(data, &msg); err != nil {
				log.Printf("MEXC: Failed to unmarshal JSON message: %s", err)
			}

			log.Printf("MEXC: Receive regular unprocessable json message: %s", string(data))
		} else if err := g.processProtobufData(data); err != nil {
			log.Printf("MEXC: Failed to process message: %s", err)
		}
	}
}

func (g *MarketDataGateway) processProtobufData(data []byte) error {
	wrapper := &pb.PushDataV3ApiWrapper{}
	if err := proto.Unmarshal(data, wrapper); err != nil {
		return fmt.Errorf("unmarshal protobuf data: %s", err)
	}

	return g.processProtobufMessage(wrapper)
}

func (g *MarketDataGateway) processProtobufMessage(wrapper *pb.PushDataV3ApiWrapper) error {
	symbol := *wrapper.Symbol

	switch wrapper.Body.(type) {
	case *pb.PushDataV3ApiWrapper_PublicAggreDepths:
		return g.processDepthsMessage(symbol, wrapper.GetPublicAggreDepths())
	case *pb.PushDataV3ApiWrapper_PublicAggreDeals:
		return g.processDealsMessage(symbol, wrapper.GetPublicAggreDeals())
	default:
		log.Printf("MEXC: Unrecognized message type: %T", wrapper.Body)
	}

	return fmt.Errorf("unrecognized protobuf message type")
}

func (g *MarketDataGateway) processDepthsMessage(symbol string, depths *pb.PublicAggreDepthsV3Api) error {
	if depths == nil {
		return nil
	}

	queue, ok := g.depthQueues[symbol]
	if ok {
		queue.Lock.Lock()

		if !queue.ReceivedSnapshot {
			queue.Queue = append(queue.Queue, depths)
			queue.Lock.Unlock()
			return nil
		}

		queue.Lock.Unlock()
	}

	return g.processDepthUpdate(symbol, depths)
}

func (g *MarketDataGateway) processDepthUpdate(symbol string, depths *pb.PublicAggreDepthsV3Api) error {
	toVersionStr := depths.ToVersion

	toVersion, err := strconv.ParseInt(toVersionStr, 10, 64)
	if err != nil {
		return fmt.Errorf("MEXC: Failed to parse toVersion '%s' for %s: %v",
			toVersionStr, symbol, err)
	}

	g.mutex.RLock()
	lastVersion, ok := g.lastVersions[symbol]
	g.mutex.RUnlock()

	// Skip outdated messages
	if ok && toVersion < lastVersion {
		log.Printf("MEXC: Warning ignoring outdated depth update for %s: update toVersion %d < last version %d",
			symbol, toVersion, lastVersion)
		return nil
	}

	g.mutex.Lock()
	g.lastVersions[symbol] = toVersion
	g.mutex.Unlock()

	events := []gateway.Event{}

	for _, ask := range depths.Asks {
		price, err := strconv.ParseFloat(ask.Price, 64)
		if err != nil {
			return fmt.Errorf("parse ask price: %s", err)
		}

		amount, err := strconv.ParseFloat(ask.Quantity, 64)
		if err != nil {
			return fmt.Errorf("parse ask quantity: %s", err)
		}

		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Ask,
				Price:  price,
				Amount: amount,
			},
		})
	}

	for _, bid := range depths.Bids {
		price, err := strconv.ParseFloat(bid.Price, 64)
		if err != nil {
			return fmt.Errorf("parse bid price: %s", err)
		}

		amount, err := strconv.ParseFloat(bid.Quantity, 64)
		if err != nil {
			return fmt.Errorf("parse bid quantity: %s", err)
		}

		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Bid,
				Price:  price,
				Amount: amount,
			},
		})
	}

	if len(events) > 0 {
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          events,
		}
	}

	return nil
}

func (g *MarketDataGateway) processDealsMessage(symbol string, deals *pb.PublicAggreDealsV3Api) error {
	if deals == nil {
		return nil
	}

	events := []gateway.Event{}

	for _, deal := range deals.Deals {
		price, err := strconv.ParseFloat(deal.Price, 64)
		if err != nil {
			return fmt.Errorf("parse trade price: %s", err)
		}

		amount, err := strconv.ParseFloat(deal.Quantity, 64)
		if err != nil {
			return fmt.Errorf("parse trade quantity: %s", err)
		}

		var side gateway.Side
		if deal.TradeType == 1 {
			side = gateway.Bid
		} else if deal.TradeType == 2 {
			side = gateway.Ask
		}

		// Generate a unique trade ID from timestamp and values
		tradeData := gateway.Trade{
			Symbol:    symbol,
			Direction: side,
			Price:     price,
			Amount:    amount,
			Timestamp: gateway.ParseTimestamp(deal.Time),
		}

		events = append(events, gateway.Event{
			Type: gateway.TradeEvent,
			Data: tradeData,
		})
	}

	if len(events) > 0 {
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          events,
		}
	}

	return nil
}
