package mexc

import (
	"errors"
	"fmt"
	"math"
	"net/url"
	"strconv"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "MEXC",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.options, g.api, g.tickCh)
	if err := g.accountGateway.Connect(); err != nil {
		return errors.New(fmt.Sprintf("Failed to connect to order entry gateway, err %s", err))
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.options, g.api, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return err
	}

	if g.options.ApiKey != "" {
		err := g.accountGateway.SubscribeMarkets(markets)
		if err != nil {
			return err
		}
	}

	return nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	exchangeInfo, err := g.api.ExchangeInfo()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0)
	for _, symbol := range exchangeInfo.Symbols {
		commonMarkets = append(commonMarkets, symbolToCommonMarket(symbol))
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func symbolToCommonMarket(symbol APISymbol) gateway.Market {
	priceTick := 1 / math.Pow10(int(symbol.QuotePrecision))
	amountTick := 1 / math.Pow10(int(symbol.BaseAssetPrecision))

	return gateway.Market{
		Exchange: Exchange,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(symbol.BaseAsset),
			Quote: strings.ToUpper(symbol.QuoteAsset),
		},
		Symbol:     symbol.Symbol,
		TakerFee:   0.002,
		MakerFee:   0.002,
		PriceTick:  priceTick,
		AmountTick: amountTick,
	}
}

func parsePriceLevelsToDepth(levels []gateway.PriceArray) []gateway.PriceLevel {
	var priceLevels []gateway.PriceLevel
	for _, level := range levels {
		priceLevels = append(priceLevels, gateway.PriceLevel(level))
	}
	return priceLevels
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	if params.Limit == 0 {
		params.Limit = 5000
	}
	reqParams := &url.Values{}
	reqParams.Set("symbol", market.Symbol)
	reqParams.Set("limit", strconv.Itoa(params.Limit))
	depth, err := gtw.api.Depth(reqParams)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	asks := parsePriceLevelsToDepth(depth.Asks)
	bids := parsePriceLevelsToDepth(depth.Bids)
	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
