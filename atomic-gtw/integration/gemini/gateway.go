package gemini

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Gemini",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.marketDataGateway = NewMarketDataGateway(g.tickCh)
	if err := g.marketDataGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.GetSymbols()
	if err != nil {
		err = fmt.Errorf("failed to fetch symbols, api returned err: %s", err)
		return nil, err
	}
	log.Printf("Gemini fetching [%d] symbol information...", len(symbols))

	commonMarkets := make([]gateway.Market, len(symbols))
	wg := &sync.WaitGroup{}
	wg.Add(len(commonMarkets))
	for i, symbol := range symbols {
		go func(i int, symbol string) {
			defer wg.Done()
			details, err := g.api.GetSymbolDetails(symbol)

			if err != nil {
				log.Printf("Gemini failed to fetch symbol [%s] details, api returned err: %s", symbol, err)
				return
			}

			commonMarkets[i] = g.parseToCommonMarket(details)
		}(i, symbol)
	}
	wg.Wait()

	return commonMarkets, nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) parseToCommonMarket(market *ApiSymbolDetail) gateway.Market {
	pair := gateway.Pair{
		Base:  strings.ToUpper(market.BaseCurrency),
		Quote: strings.ToUpper(market.QuoteCurrency),
	}
	priceTick, _ := market.QuoteIncrement.Float64()
	tickSize, _ := market.TickSize.Float64()

	return gateway.Market{
		Exchange:               Exchange,
		Pair:                   pair,
		Symbol:                 market.Symbol,
		TakerFee:               0.001,
		MakerFee:               0.001,
		PriceTick:              priceTick,
		AmountTick:             tickSize,
		MinimumOrderSize:       market.MinOrderSize,
		MinimumOrderMoneyValue: 0,    // TODO: We don't get this info from the API. No limit?
		PaysFeeInStock:         true, // TODO: Check, I'm not sure about the value here
		MarketType:             gateway.SpotMarket,
	}
}

func parsePriceLevelsToDepth(levels []ApiPrice) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel

	for _, level := range levels {
		price, err := strconv.ParseFloat(level.Price, 64)
		if err != nil {
			return nil, err
		}
		amount, err := strconv.ParseFloat(level.Amount, 64)
		if err != nil {
			return nil, err
		}
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		})
	}

	return priceLevels, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.GetDepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks, err := parsePriceLevelsToDepth(depth.Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	bids, err := parsePriceLevelsToDepth(depth.Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
