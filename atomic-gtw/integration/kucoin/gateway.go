package kucoin

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "KuCoin",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	var err error

	g.accountGateway = NewAccountGateway(g.api, g.tickCh)
	if g.options.ApiKey != "" {
		err = g.accountGateway.Connect()
		if err != nil {
			return err
		}
	}

	g.marketDataGateway = NewMarketDataGateway(g.tickCh, g.api, g.options)
	err = g.marketDataGateway.Connect()
	if err != nil {
		return err
	}

	return nil
}

func (gtw *Gateway) AccountGateway() gateway.AccountGateway {
	return gtw.accountGateway
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	markets, err := g.api.GetMarkets()
	if err != nil {
		return nil, fmt.Errorf("failed to get markets, err %s", err)
	}

	commonMarkets := make([]gateway.Market, 0, len(markets))
	for _, market := range markets {
		commonMarkets = append(commonMarkets, g.parseToCommonMarket(&market))
	}

	return commonMarkets, nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) parseToCommonMarket(market *ApiMarket) gateway.Market {
	pair := gateway.Pair{
		Base:  strings.ToUpper(market.BaseCurrency),
		Quote: strings.ToUpper(market.QuoteCurrency),
	}
	priceTick, _ := strconv.ParseFloat(market.PriceIncrement, 64)
	amountTick, _ := strconv.ParseFloat(market.BaseIncrement, 64)
	minimumOrderSize, _ := strconv.ParseFloat(market.BaseMinSize, 64)
	minimumOrderMoneyValue, _ := strconv.ParseFloat(market.QuoteMinSize, 64)

	return gateway.Market{
		Exchange:               Exchange,
		Pair:                   pair,
		Symbol:                 market.Symbol,
		TakerFee:               0.001,
		MakerFee:               0.001,
		PriceTick:              priceTick,
		AmountTick:             amountTick,
		MinimumOrderSize:       minimumOrderSize,
		MinimumOrderMoneyValue: minimumOrderMoneyValue,
		PaysFeeInStock:         true, // TODO: Check, I'm not sure about the value here
		MarketType:             gateway.SpotMarket,
	}
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	var bids []gateway.PriceLevel
	var asks []gateway.PriceLevel

	if params.Limit == 0 {
		params.Limit = 1000
	}
	depth, err := g.api.GetOrderBook(market.Symbol)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	for _, bid := range depth.Bids {
		bids = append(bids, gateway.PriceLevel(bid))
	}
	for _, ask := range depth.Asks {
		asks = append(asks, gateway.PriceLevel(ask))
	}

	return gateway.DepthBook{
		Bids: bids,
		Asks: asks,
	}, nil
}
