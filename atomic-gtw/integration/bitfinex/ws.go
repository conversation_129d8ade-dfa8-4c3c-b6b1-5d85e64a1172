package bitfinex

import (
	"context"
	"crypto/hmac"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"strconv"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

const (
	wsURL              = "wss://api.bitfinex.com/ws/2"
	wsHeartbeatTimeout = 30 * time.Second
	wsPingInterval     = 10 * time.Second // Frequency to ping the server
	wsSubscriptionCap  = 30               // Bitfinex limits to 30 channels per connection
	debugLogging       = false            // Set to true to enable detailed debug logs
)

// WSClient represents a WebSocket client for Bitfinex
type WSClient struct {
	apiKey      string
	apiSecret   string
	conn        *websocket.Conn
	mu          sync.Mutex
	isConnected bool
	heartbeatCh chan struct{}
	closeCh     chan struct{}

	// Subscriptions
	chanIdToSub         map[int64]*Subscription
	bookHandler         func(*BookUpdate)
	tradeHandler        func(*TradeUpdate)
	bookSnapshotHandler func(*BookSnapshot)
	executionHandler    func(*TradeExecution)
	orderCallbacks      map[int64]chan OrderResponse
	cancelCallbacks     map[int64]chan OrderCancelResponse
}

// Subscription represents an active subscription
type Subscription struct {
	Channel     string
	Symbol      string
	ChanId      int64
	Precision   string
	Frequency   string
	Length      int
	Unsubscribe func() error
}

// OrderResponse represents a response to an order request
type OrderResponse struct {
	ID     int64
	CID    int64
	Status string
	Text   string
}

// OrderCancelResponse represents a response to an order cancel request
type OrderCancelResponse struct {
	ID     int64
	Status string
	Text   string
}

// BookUpdate represents an order book update from the WebSocket
type BookUpdate struct {
	Symbol string
	Price  float64
	Count  int64
	Amount float64
	Side   string
}

// BookSnapshot represents an order book snapshot from the WebSocket
type BookSnapshot struct {
	Symbol string
	Levels []BookUpdate
}

// TradeUpdate represents a trade update from the WebSocket
type TradeUpdate struct {
	ID        int64
	MTS       int64
	Amount    float64
	Price     float64
	Symbol    string
	Direction string
}

// TradeExecution represents a trade execution notification
type TradeExecution struct {
	ID          int64   // Trade ID
	Pair        string  // Symbol (e.g., tETHUST)
	MTS         int64   // Timestamp in milliseconds
	OrderID     int64   // Order ID
	ExecAmount  float64 // Executed amount (negative for sells)
	ExecPrice   float64 // Executed price
	OrderType   string  // Order type (e.g., MARKET)
	OrderPrice  float64 // Order price
	Maker       int     // Maker flag (-1 if true)
	Fee         float64 // Fee amount
	FeeCurrency string  // Fee currency
	CID         int64   // Client ID
}

// NewWSClient creates a new WebSocket client
func NewWSClient(apiKey, apiSecret string) *WSClient {
	return &WSClient{
		apiKey:          apiKey,
		apiSecret:       apiSecret,
		isConnected:     false,
		chanIdToSub:     make(map[int64]*Subscription),
		orderCallbacks:  make(map[int64]chan OrderResponse),
		cancelCallbacks: make(map[int64]chan OrderCancelResponse),
		heartbeatCh:     make(chan struct{}, 1),
		closeCh:         make(chan struct{}),
	}
}

// debugLog logs debug information when debug logging is enabled
func debugLog(format string, args ...interface{}) {
	if debugLogging {
		log.Printf("[BFX-DEBUG] "+format, args...)
	}
}

// Connect establishes a WebSocket connection to Bitfinex
func (c *WSClient) Connect() error {
	c.mu.Lock()

	// Early return if already connected
	if c.isConnected {
		c.mu.Unlock()
		return nil
	}

	dialer := websocket.Dialer{
		Proxy:            websocket.DefaultDialer.Proxy,
		HandshakeTimeout: 45 * time.Second,
		// Add read/write buffer sizes
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
	}

	conn, resp, err := dialer.Dial(wsURL, nil)
	if err != nil {
		if resp != nil {
			log.Printf("Bitfinex WebSocket connection failed with status: %d", resp.StatusCode)
		}
		c.mu.Unlock()
		return fmt.Errorf("WebSocket dial failed: %w", err)
	}

	// Set read/write deadlines to prevent hung connections
	conn.SetReadDeadline(time.Now().Add(wsHeartbeatTimeout))
	conn.SetWriteDeadline(time.Now().Add(wsHeartbeatTimeout))

	// Set up ping/pong handlers for connection health
	conn.SetPingHandler(func(pingData string) error {
		debugLog("Received ping, sending pong")
		// Reply with pong when we receive a ping
		err := conn.WriteControl(websocket.PongMessage, []byte(pingData), time.Now().Add(5*time.Second))
		if err != nil {
			log.Printf("Bitfinex WebSocket failed to send pong: %s", err)
		}
		return nil
	})

	conn.SetPongHandler(func(pongData string) error {
		debugLog("Received pong")
		// Reset read deadline when we get a pong back
		conn.SetReadDeadline(time.Now().Add(wsHeartbeatTimeout))
		return nil
	})

	c.conn = conn
	c.isConnected = true
	c.mu.Unlock() // Release the lock before starting goroutines and calling authenticate

	// Start message processor
	go c.messageProcessor()

	// Start heartbeat checker
	go c.heartbeatChecker()

	// Authenticate if we have credentials
	if c.apiKey != "" && c.apiSecret != "" {
		if err := c.authenticate(); err != nil {
			c.mu.Lock()
			c.isConnected = false
			if c.conn != nil {
				c.conn.Close()
			}
			c.mu.Unlock()
			return fmt.Errorf("authentication failed: %w", err)
		}
		log.Printf("Bitfinex WebSocket authenticated successfully")
	}

	return nil
}

// authenticate sends an authentication message to Bitfinex
func (c *WSClient) authenticate() error {
	nonce := fmt.Sprintf("%d", time.Now().UnixNano()/1000000)
	payload := "AUTH" + nonce

	sig := hmac.New(sha512.New384, []byte(c.apiSecret))
	sig.Write([]byte(payload))
	signature := hex.EncodeToString(sig.Sum(nil))

	authMsg := map[string]interface{}{
		"apiKey":      c.apiKey,
		"authSig":     signature,
		"authNonce":   nonce,
		"authPayload": payload,
		"event":       "auth",
	}

	debugLog("Sending authentication message")
	return c.sendMessage(authMsg)
}

// sendMessage sends a message to the WebSocket
func (c *WSClient) sendMessage(message interface{}) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.isConnected {
		return fmt.Errorf("websocket not connected")
	}

	// Set write deadline before sending
	c.conn.SetWriteDeadline(time.Now().Add(wsHeartbeatTimeout))

	// Log the message (be careful not to log sensitive auth data)
	debugLog("Sending message: %v", sanitizeLogMessage(message))

	if err := c.conn.WriteJSON(message); err != nil {
		log.Printf("Bitfinex write error: %s", err)
		return err
	}

	return nil
}

// sanitizeLogMessage prevents logging sensitive auth data
func sanitizeLogMessage(msg interface{}) interface{} {
	// For authentication messages, hide sensitive fields
	if m, ok := msg.(map[string]interface{}); ok {
		if event, ok := m["event"].(string); ok && event == "auth" {
			result := make(map[string]interface{})
			for k, v := range m {
				if k == "apiKey" || k == "authSig" || k == "authPayload" {
					result[k] = "[REDACTED]"
				} else {
					result[k] = v
				}
			}
			return result
		}
	}
	return msg
}

// messageProcessor handles incoming WebSocket messages
func (c *WSClient) messageProcessor() {
	defer func() {
		c.mu.Lock()
		c.isConnected = false
		if c.conn != nil {
			c.conn.Close()
		}
		c.mu.Unlock()
		log.Printf("Bitfinex WebSocket message processor exited")
	}()

	for {
		select {
		case <-c.closeCh:
			return
		default:
			_, message, err := c.conn.ReadMessage()
			if err != nil {
				log.Printf("Bitfinex websocket read error: %s", err)
				c.reconnect()
				return
			}

			debugLog("Received: %s", string(message))

			// Reset read deadline after successful read
			c.conn.SetReadDeadline(time.Now().Add(wsHeartbeatTimeout))

			// Reset heartbeat timer
			select {
			case c.heartbeatCh <- struct{}{}:
			default:
			}

			// Process the message
			if err := c.processMessage(message); err != nil {
				log.Printf("Bitfinex message processing error: %s", err)
			}
		}
	}
}

// processMessage processes an incoming WebSocket message
func (c *WSClient) processMessage(message []byte) error {
	// Check if it's an array (data update) or object (event)
	if message[0] == '[' {
		return c.processDataMessage(message)
	} else {
		return c.processEventMessage(message)
	}
}

// processDataMessage processes a data message from the WebSocket
func (c *WSClient) processDataMessage(message []byte) error {
	var data []interface{}
	if err := json.Unmarshal(message, &data); err != nil {
		return fmt.Errorf("failed to unmarshal data message: %s", err)
	}

	// First element is the channel ID
	if len(data) < 2 {
		return nil // Heartbeat or malformed message
	}

	// Check if this is a notification message (format: [0,"n",[...]])
	if len(data) >= 3 && data[0].(float64) == 0 && data[1].(string) == "n" {
		debugLog("Processing notification data message")
		return c.processNotificationDataMessage(data)
	}

	chanID, ok := data[0].(float64)
	if !ok {
		return fmt.Errorf("channel ID is not a number")
	}

	chanIDInt := int64(chanID)

	// Channel ID 0 is used for auth channel messages including trade executions
	if chanIDInt == 0 {
		// This is an auth channel message, check if it's a trade execution
		if len(data) >= 3 {
			// Check if it's a trade update ("tu") message
			msgType, isString := data[1].(string)
			if isString && (msgType == "tu" || msgType == "te") {
				// Process trade execution specifically for "tu" messages
				// which contain complete information including fees
				if msgType == "tu" {
					return c.processTradeExecution(data)
				}
				// For "te" messages, we can return without error but don't process
				// since we'll get the full info in the "tu" message
				return nil
			}
		}
	}

	// Look up the subscription
	c.mu.Lock()
	sub, exists := c.chanIdToSub[chanIDInt]
	c.mu.Unlock()

	if !exists {
		return nil // Unknown subscription
	}

	// Process based on channel type
	switch sub.Channel {
	case "book":
		return c.processBookMessage(data, sub)
	case "trades":
		return c.processTradeMessage(data, sub)
	}

	return nil
}

func (c *WSClient) processTradeExecution(data []interface{}) error {
	if len(data) < 3 {
		return fmt.Errorf("invalid trade execution message format")
	}

	// Trade data is the third element
	tradeData, ok := data[2].([]interface{})
	if !ok || len(tradeData) < 11 { // Need at least 11 elements for fee info
		return fmt.Errorf("invalid trade data format")
	}

	// Create trade execution with all available fields
	execution := &TradeExecution{
		ID:          parseIntOrZero(tradeData[0]),
		Pair:        toString(tradeData[1]),
		MTS:         parseIntOrZero(tradeData[2]),
		OrderID:     parseIntOrZero(tradeData[3]),
		ExecAmount:  parseFloat(tradeData[4]),
		ExecPrice:   parseFloat(tradeData[5]),
		OrderType:   toString(tradeData[6]),
		OrderPrice:  parseFloat(tradeData[7]),
		Maker:       int(parseIntOrZero(tradeData[8])),
		Fee:         parseFloat(tradeData[9]),
		FeeCurrency: toString(tradeData[10]),
	}

	// Add CID if available (index 11)
	if len(tradeData) > 11 {
		execution.CID = parseIntOrZero(tradeData[11])
	}

	debugLog("Trade execution - ID: %d, OrderID: %d, Amount: %f, Price: %f, Fee: %f %s",
		execution.ID, execution.OrderID, execution.ExecAmount, execution.ExecPrice,
		execution.Fee, execution.FeeCurrency)

	// Get handler safely
	var handler func(*TradeExecution)
	c.mu.Lock()
	handler = c.executionHandler
	c.mu.Unlock()

	// Call handler outside of lock
	if handler != nil {
		handler(execution)
	}

	return nil
}

// Helper functions for safe type conversion
func parseIntOrZero(v interface{}) int64 {
	switch i := v.(type) {
	case float64:
		return int64(i)
	case int64:
		return i
	case string:
		val, err := strconv.ParseInt(i, 10, 64)
		if err == nil {
			return val
		}
	}
	return 0
}

func toString(v interface{}) string {
	if str, ok := v.(string); ok {
		return str
	}
	return ""
}

// processBookMessage processes a book channel message
func (c *WSClient) processBookMessage(data []interface{}, sub *Subscription) error {
	// Check if it's a snapshot (array of arrays)
	if len(data) < 2 {
		return nil // Invalid message
	}

	// If data[1] is an array of arrays, it's a snapshot
	if bookData, ok := data[1].([]interface{}); ok {
		// Check if this is a snapshot (first entry in bookData is an array)
		if len(bookData) > 0 {
			// Determine if it's a snapshot by checking if the first element is an array
			firstElement, ok := bookData[0].([]interface{})
			if ok && len(firstElement) >= 3 {
				// This is a snapshot - an array of order entries
				return c.processBookSnapshot(bookData, sub)
			} else if ok {
				// It's possibly a malformed snapshot
				debugLog("Received possibly malformed book snapshot")
			}
		}

		// Not a snapshot (or empty array), treat as a single update
		// For a single update, data[1] is the order entry itself
		if len(bookData) >= 3 {
			price := parseFloat(bookData[0])
			count, _ := parseInt(bookData[1])
			amount := parseFloat(bookData[2])

			var side string
			if amount > 0 {
				side = "bid"
			} else {
				side = "ask"
			}

			bookUpdate := &BookUpdate{
				Symbol: sub.Symbol,
				Price:  price,
				Count:  count,
				Amount: amount,
				Side:   side,
			}

			// Call handler outside of lock
			if c.bookHandler != nil {
				c.bookHandler(bookUpdate)
			}
		}
	}

	return nil
}

// processBookSnapshot processes a book snapshot message
func (c *WSClient) processBookSnapshot(snapshot []interface{}, sub *Subscription) error {
	bookSnapshot := &BookSnapshot{
		Symbol: sub.Symbol,
		Levels: make([]BookUpdate, 0, len(snapshot)),
	}

	for _, levelData := range snapshot {
		level, ok := levelData.([]interface{})
		if !ok || len(level) < 3 {
			continue
		}

		price := parseFloat(level[0])
		count, _ := parseInt(level[1])
		amount := parseFloat(level[2])

		var side string
		if amount > 0 {
			side = "bid"
		} else {
			side = "ask"
		}

		bookSnapshot.Levels = append(bookSnapshot.Levels, BookUpdate{
			Symbol: sub.Symbol,
			Price:  price,
			Count:  count,
			Amount: amount,
			Side:   side,
		})
	}

	// Call handler outside of lock
	if c.bookSnapshotHandler != nil {
		c.bookSnapshotHandler(bookSnapshot)
	}

	return nil
}

// processTradeMessage processes a trade channel message
func (c *WSClient) processTradeMessage(data []interface{}, sub *Subscription) error {
	// Check if we have enough data
	if len(data) < 2 {
		return nil
	}

	// Case 1: Trade snapshot - second element is an array of arrays
	if snapshot, ok := data[1].([]interface{}); ok {
		// This is a snapshot of trades
		for _, tradeEntry := range snapshot {
			tradeData, ok := tradeEntry.([]interface{})
			if !ok || len(tradeData) < 4 {
				continue // Skip invalid entries
			}

			id, _ := parseInt(tradeData[0])
			mts, _ := parseInt(tradeData[1])
			amount := parseFloat(tradeData[2])
			price := parseFloat(tradeData[3])

			tradeUpdate := &TradeUpdate{
				ID:     id,
				MTS:    mts,
				Amount: math.Abs(amount),
				Price:  price,
				Symbol: sub.Symbol,
			}

			// Determine direction based on amount sign
			if amount > 0 {
				tradeUpdate.Direction = "buy"
			} else {
				tradeUpdate.Direction = "sell"
			}

			// Call handler for each trade in the snapshot
			if c.tradeHandler != nil {
				c.tradeHandler(tradeUpdate)
			}
		}
		return nil
	}

	// Case 2: Trade update - second element is a string event type ("te" or "tu")
	eventType, isEventType := data[1].(string)
	if isEventType {
		// Process both "te" (trade executed) and "tu" (trade update) messages
		// Both contain the same information, but "tu" is sent after "te" with confirmed data
		if eventType != "te" && eventType != "tu" {
			return nil // Not a trade event we're interested in
		}

		if len(data) < 3 {
			return nil // No trade data
		}

		// Process the trade data
		tradeData, ok := data[2].([]interface{})
		if !ok || len(tradeData) < 4 {
			return nil // Invalid trade data
		}

		id, _ := parseInt(tradeData[0])
		mts, _ := parseInt(tradeData[1])
		amount := parseFloat(tradeData[2])
		price := parseFloat(tradeData[3])

		tradeUpdate := &TradeUpdate{
			ID:     id,
			MTS:    mts,
			Amount: math.Abs(amount),
			Price:  price,
			Symbol: sub.Symbol,
		}

		// Determine direction based on amount sign
		if amount > 0 {
			tradeUpdate.Direction = "buy"
		} else {
			tradeUpdate.Direction = "sell"
		}

		// Call handler outside of lock
		if c.tradeHandler != nil {
			c.tradeHandler(tradeUpdate)
		}
	}

	return nil
}

// processEventMessage processes an event message from the WebSocket
func (c *WSClient) processEventMessage(message []byte) error {
	var event map[string]interface{}
	if err := json.Unmarshal(message, &event); err != nil {
		return fmt.Errorf("failed to unmarshal event message: %s", err)
	}

	eventType, ok := event["event"].(string)
	if !ok {
		return nil // Malformed event
	}

	debugLog("Processing event type: %s", eventType)

	switch eventType {
	case "subscribed":
		return c.handleSubscribedEvent(event)
	case "auth":
		return c.handleAuthEvent(event)
	case "notification":
		return c.handleNotificationEvent(event)
	case "info", "ping", "pong":
		// Informational events, no action needed
		return nil
	default:
		log.Printf("Unhandled Bitfinex event type: %s", eventType)
	}

	return nil
}

// handleSubscribedEvent handles a subscribed event
func (c *WSClient) handleSubscribedEvent(event map[string]interface{}) error {
	chanID, ok := event["chanId"].(float64)
	if !ok {
		return fmt.Errorf("missing channel ID in subscribed event")
	}

	channel, ok := event["channel"].(string)
	if !ok {
		return fmt.Errorf("missing channel in subscribed event")
	}

	symbol, _ := event["symbol"].(string)
	precision, _ := event["prec"].(string)
	frequency, _ := event["freq"].(string)
	length, _ := parseInt(event["len"])

	sub := &Subscription{
		Channel:   channel,
		Symbol:    symbol,
		ChanId:    int64(chanID),
		Precision: precision,
		Frequency: frequency,
		Length:    int(length),
		Unsubscribe: func() error {
			return c.unsubscribeChannel(int64(chanID))
		},
	}

	c.mu.Lock()
	c.chanIdToSub[int64(chanID)] = sub
	c.mu.Unlock()

	log.Printf("Subscription confirmed: %s/%s on channel ID %d", channel, symbol, int64(chanID))
	return nil
}

// handleAuthEvent handles an auth event
func (c *WSClient) handleAuthEvent(event map[string]interface{}) error {
	status, ok := event["status"].(string)
	if !ok {
		return fmt.Errorf("missing status in auth event")
	}

	if status != "OK" {
		errorMsg, _ := event["msg"].(string)
		return fmt.Errorf("authentication failed: %s", errorMsg)
	}

	return nil
}

// handleNotificationEvent handles a notification event
func (c *WSClient) handleNotificationEvent(event map[string]interface{}) error {
	notificationType, ok := event["type"].(string)
	if !ok {
		return fmt.Errorf("missing type in notification event")
	}

	debugLog("Processing notification type: %s", notificationType)

	switch notificationType {
	case "on-req", "ou-req", "oc-req":
		return c.handleOrderNotification(event)
	}

	return nil
}

// processNotificationDataMessage processes array-format notification messages
func (c *WSClient) processNotificationDataMessage(data []interface{}) error {
	// Expected format: [0,"n",[timestamp,"on-req",null,null,[order_data],null,"ERROR","error_text"]]
	notifyData, ok := data[2].([]interface{})
	if !ok || len(notifyData) < 6 {
		return fmt.Errorf("malformed notification data message")
	}

	// Extract notification type (index 1)
	notificationType, ok := notifyData[1].(string)
	if !ok {
		return fmt.Errorf("missing notification type")
	}

	debugLog("Processing array notification type: %s", notificationType)

	// Extract status (index 6)
	status, _ := notifyData[6].(string)

	// Extract error message (index 7)
	text, _ := notifyData[7].(string)

	// Extract order data (index 4)
	orderData, _ := notifyData[4].([]interface{})

	// Debug log the array contents
	if len(orderData) > 0 {
		debugLog("Order data array contains %d elements", len(orderData))
		for i, item := range orderData {
			debugLog("  orderData[%d] = %v (type: %T)", i, item, item)
		}
	}

	// Process based on notification type
	switch notificationType {
	case "on-req": // Order new request
		if len(orderData) >= 3 {
			// Order ID is typically at index 0, CID is at index 2
			var id int64 = 0
			if len(orderData) > 0 {
				id, _ = parseInt(orderData[0])
				debugLog("Extracted order ID: %d from array notification", id)
			}

			cid, _ := parseInt(orderData[2])
			debugLog("Extracted CID: %d from array notification", cid)

			var callback chan OrderResponse
			var exists bool
			c.mu.Lock()
			callback, exists = c.orderCallbacks[cid]
			c.mu.Unlock()

			if exists && callback != nil {
				response := OrderResponse{
					ID:     id, // Use actual ID from response
					CID:    cid,
					Status: status,
					Text:   text,
				}

				debugLog("Sending order response to callback: %+v", response)

				select {
				case callback <- response:
					debugLog("Successfully sent response to order callback channel")
				default:
					log.Printf("Bitfinex: Failed to send to order callback channel for CID %d - channel full or closed", cid)
				}
			} else {
				log.Printf("No callback found for CID: %d", cid)
			}
		}

	case "oc-req": // Order cancel request
		if len(orderData) >= 1 {
			// Order ID is typically at index 0
			id, _ := parseInt(orderData[0])
			debugLog("Extracted cancel order ID: %d from array notification", id)

			var callback chan OrderCancelResponse
			var exists bool
			c.mu.Lock()
			callback, exists = c.cancelCallbacks[id]
			c.mu.Unlock()

			if exists && callback != nil {
				response := OrderCancelResponse{
					ID:     id,
					Status: status,
					Text:   text,
				}

				debugLog("Sending cancel response to callback: %+v", response)

				select {
				case callback <- response:
					debugLog("Successfully sent response to cancel callback channel")
				default:
					log.Printf("Bitfinex: Failed to send to cancel callback channel for ID %d - channel full or closed", id)
				}
			} else {
				log.Printf("No callback found for cancel order ID: %d", id)
			}
		}
	}

	return nil
}

// handleOrderNotification handles an order notification
func (c *WSClient) handleOrderNotification(event map[string]interface{}) error {
	status, _ := event["status"].(string)
	text, _ := event["text"].(string)
	notificationType, _ := event["type"].(string)

	debugLog("Processing order notification type: %s, status: %s", notificationType, status)

	// Extract data based on notification type
	data, ok := event["notify_info"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("missing notify_info in order notification")
	}

	switch notificationType {
	case "on-req": // Order new request
		id := parseFloat(data["id"])
		cid := parseFloat(data["cid"])

		debugLog("Order notification - ID: %d, CID: %d", int64(id), int64(cid))

		var callback chan OrderResponse
		var exists bool
		c.mu.Lock()
		callback, exists = c.orderCallbacks[int64(cid)]
		c.mu.Unlock()

		if exists && callback != nil {
			response := OrderResponse{
				ID:     int64(id),
				CID:    int64(cid),
				Status: status,
				Text:   text,
			}

			debugLog("Sending order response to callback: %+v", response)

			select {
			case callback <- response:
				debugLog("Successfully sent response to order callback channel")
			default:
				log.Printf("Bitfinex: Failed to send to order callback channel for CID %d - channel full or closed", int64(cid))
			}
		} else {
			log.Printf("No callback found for CID: %d", int64(cid))
		}

	case "oc-req": // Order cancel request
		id := parseFloat(data["id"])
		debugLog("Cancel notification - Order ID: %d", int64(id))

		var callback chan OrderCancelResponse
		var exists bool
		c.mu.Lock()
		callback, exists = c.cancelCallbacks[int64(id)]
		c.mu.Unlock()

		if exists && callback != nil {
			response := OrderCancelResponse{
				ID:     int64(id),
				Status: status,
				Text:   text,
			}

			debugLog("Sending cancel response to callback: %+v", response)

			select {
			case callback <- response:
				debugLog("Successfully sent response to cancel callback channel")
			default:
				log.Printf("Bitfinex: Failed to send to cancel callback channel for ID %d - channel full or closed", int64(id))
			}
		} else {
			log.Printf("No callback found for cancel order ID: %d", int64(id))
		}
	}

	return nil
}

// handleTradeExecution handles a trade execution notification
func (c *WSClient) handleTradeExecution(event map[string]interface{}) error {
	data, ok := event["notify_info"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("missing notify_info in trade execution")
	}

	id := parseFloat(data["id"])
	pair := data["pair"].(string)
	mts := parseFloat(data["mts"])
	orderID := parseFloat(data["order_id"])
	execAmount := parseFloat(data["exec_amount"])
	execPrice := parseFloat(data["exec_price"])

	execution := &TradeExecution{
		ID:         int64(id),
		Pair:       pair,
		MTS:        int64(mts),
		OrderID:    int64(orderID),
		ExecAmount: execAmount,
		ExecPrice:  execPrice,
	}

	debugLog("Trade execution - ID: %d, OrderID: %d, Amount: %f, Price: %f",
		execution.ID, execution.OrderID, execution.ExecAmount, execution.ExecPrice)

	// Get handler safely
	var handler func(*TradeExecution)
	c.mu.Lock()
	handler = c.executionHandler
	c.mu.Unlock()

	// Call handler outside of lock
	if handler != nil {
		handler(execution)
	}

	return nil
}

// heartbeatChecker ensures the connection is alive
func (c *WSClient) heartbeatChecker() {
	heartbeatTicker := time.NewTicker(wsHeartbeatTimeout)
	pingTicker := time.NewTicker(wsPingInterval) // Fixed ping interval to keep connection alive
	defer heartbeatTicker.Stop()
	defer pingTicker.Stop()

	debugLog("Heartbeat checker started")

	for {
		select {
		case <-c.closeCh:
			debugLog("Heartbeat checker exiting due to close signal")
			return
		case <-c.heartbeatCh:
			heartbeatTicker.Reset(wsHeartbeatTimeout)
			debugLog("Heartbeat received, reset timer")
		case <-pingTicker.C:
			// Proactively send pings to keep the connection alive
			debugLog("Sending ping")
			if err := c.sendPing(); err != nil {
				log.Printf("Bitfinex WebSocket ping failed: %s", err)
				// Don't immediately reconnect on ping failure, let the heartbeat checker do that
			}
		case <-heartbeatTicker.C:
			// No heartbeat received in the timeout period
			log.Printf("Bitfinex WebSocket heartbeat timeout, reconnecting...")
			c.reconnect()
			return
		}
	}
}

// sendPing sends a ping message to the server
func (c *WSClient) sendPing() error {
	pingMsg := map[string]interface{}{
		"event": "ping",
	}
	return c.sendMessage(pingMsg)
}

// reconnect attempts to reconnect the WebSocket
func (c *WSClient) reconnect() {
	panic("Bitfinex reconnect not supported")
}

// Close closes the WebSocket connection
func (c *WSClient) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.isConnected || c.conn == nil {
		return nil
	}

	log.Printf("Closing Bitfinex WebSocket connection")

	// Signal the goroutines to exit
	select {
	case <-c.closeCh:
		// Already closed
	default:
		close(c.closeCh)
	}

	c.isConnected = false

	// Send a proper close frame
	closeMsg := websocket.FormatCloseMessage(websocket.CloseNormalClosure, "client initiated close")
	deadline := time.Now().Add(2 * time.Second)
	c.conn.SetWriteDeadline(deadline)
	c.conn.WriteMessage(websocket.CloseMessage, closeMsg)

	// Now close the underlying connection
	return c.conn.Close()
}

// RegisterExecutionHandler registers a handler for trade executions
func (c *WSClient) RegisterExecutionHandler(handler func(*TradeExecution)) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.executionHandler = handler
}

// RegisterBookHandler registers a handler for book updates
func (c *WSClient) RegisterBookHandler(handler func(*BookUpdate)) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.bookHandler = handler
}

// RegisterBookSnapshotHandler registers a handler for book snapshots
func (c *WSClient) RegisterBookSnapshotHandler(handler func(*BookSnapshot)) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.bookSnapshotHandler = handler
}

// RegisterTradeHandler registers a handler for trade updates
func (c *WSClient) RegisterTradeHandler(handler func(*TradeUpdate)) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.tradeHandler = handler
}

// SubscribeBook subscribes to the order book for a symbol
func (c *WSClient) SubscribeBook(symbol, precision, frequency string, length int) error {
	if !c.isConnected {
		return fmt.Errorf("websocket not connected")
	}

	// Default values
	if precision == "" {
		precision = "P0"
	}
	if frequency == "" {
		frequency = "F0"
	}
	if length <= 0 {
		length = 25
	}

	msg := map[string]interface{}{
		"event":   "subscribe",
		"channel": "book",
		"symbol":  symbol,
		"prec":    precision,
		"freq":    frequency,
		"len":     length,
	}

	return c.sendMessage(msg)
}

// SubscribeTrades subscribes to trades for a symbol
func (c *WSClient) SubscribeTrades(symbol string) error {
	if !c.isConnected {
		return fmt.Errorf("websocket not connected")
	}

	msg := map[string]interface{}{
		"event":   "subscribe",
		"channel": "trades",
		"symbol":  symbol,
	}

	return c.sendMessage(msg)
}

// unsubscribeChannel unsubscribes from a channel
func (c *WSClient) unsubscribeChannel(chanID int64) error {
	msg := map[string]interface{}{
		"event":  "unsubscribe",
		"chanId": chanID,
	}

	return c.sendMessage(msg)
}

// SubmitOrder submits a new order
func (c *WSClient) SubmitOrder(ctx context.Context, order map[string]interface{}) (OrderResponse, error) {
	// Check connection status without holding the lock
	var isConn bool
	c.mu.Lock()
	isConn = c.isConnected
	c.mu.Unlock()

	if !isConn {
		return OrderResponse{}, fmt.Errorf("websocket not connected")
	}

	cid, ok := order["cid"].(int64)
	if !ok {
		return OrderResponse{}, fmt.Errorf("missing client ID (cid) in order")
	}

	debugLog("Submitting order with CID: %d", cid)
	respCh := make(chan OrderResponse, 1)

	// Register callback
	c.mu.Lock()
	c.orderCallbacks[cid] = respCh
	c.mu.Unlock()

	// Format order message according to Bitfinex API
	msg := []interface{}{
		0,
		"on",
		nil,
		order,
	}

	if err := c.sendMessage(msg); err != nil {
		c.mu.Lock()
		delete(c.orderCallbacks, cid)
		c.mu.Unlock()
		return OrderResponse{}, fmt.Errorf("failed to send order: %w", err)
	}

	// Wait for response with timeout from context
	select {
	case resp := <-respCh:
		c.mu.Lock()
		delete(c.orderCallbacks, cid)
		c.mu.Unlock()
		debugLog("Received order response: %+v", resp)
		return resp, nil
	case <-ctx.Done():
		c.mu.Lock()
		delete(c.orderCallbacks, cid)
		c.mu.Unlock()
		return OrderResponse{}, fmt.Errorf("order submission timed out: %w", ctx.Err())
	}
}

// CancelOrder cancels an existing order
func (c *WSClient) CancelOrder(ctx context.Context, orderID int64) (OrderCancelResponse, error) {
	// Check connection status without holding the lock
	var isConn bool
	c.mu.Lock()
	isConn = c.isConnected
	c.mu.Unlock()

	if !isConn {
		return OrderCancelResponse{}, fmt.Errorf("websocket not connected")
	}

	debugLog("Cancelling order with ID: %d", orderID)
	respCh := make(chan OrderCancelResponse, 1)

	// Register callback
	c.mu.Lock()
	c.cancelCallbacks[orderID] = respCh
	c.mu.Unlock()

	// Format cancel message according to Bitfinex API
	msg := []interface{}{
		0,
		"oc",
		nil,
		map[string]interface{}{
			"id": orderID,
		},
	}

	if err := c.sendMessage(msg); err != nil {
		c.mu.Lock()
		delete(c.cancelCallbacks, orderID)
		c.mu.Unlock()
		return OrderCancelResponse{}, fmt.Errorf("failed to send cancel request: %w", err)
	}

	// Wait for response with timeout from context
	select {
	case resp := <-respCh:
		c.mu.Lock()
		delete(c.cancelCallbacks, orderID)
		c.mu.Unlock()
		debugLog("Received cancel response: %+v", resp)
		return resp, nil
	case <-ctx.Done():
		c.mu.Lock()
		delete(c.cancelCallbacks, orderID)
		c.mu.Unlock()
		return OrderCancelResponse{}, fmt.Errorf("order cancellation timed out: %w", ctx.Err())
	}
}

// IsConnected returns whether the WebSocket is currently connected
func (c *WSClient) IsConnected() bool {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.isConnected
}

// parseInt parses an interface{} to int64
func parseInt(v interface{}) (int64, bool) {
	switch i := v.(type) {
	case float64:
		return int64(i), true
	case int64:
		return i, true
	case string:
		var val int64
		if _, err := fmt.Sscanf(i, "%d", &val); err == nil {
			return val, true
		}
	}
	return 0, false
}

// parseFloat parses an interface{} to float64
func parseFloat(data interface{}) float64 {
	switch v := data.(type) {
	case string:
		f, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return 0
		}
		return f
	case int64:
		return float64(v)
	case float64:
		return v
	}

	return 0
}
