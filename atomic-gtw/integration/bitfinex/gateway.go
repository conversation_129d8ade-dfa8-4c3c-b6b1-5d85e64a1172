package bitfinex

import (
	"fmt"
	"log"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Bitfinex",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	api               *API
	marketDataGateway *MarketDataGateway
	accountGateway    *AccountGateway
	tickCh            chan gateway.Tick
	wsClient          *WSClient
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		api:     NewAPI(options),
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	wsClient := NewWSClient(g.options.ApiKey, g.options.ApiSecret)

	if err := wsClient.Connect(); err != nil {
		return fmt.Errorf("failed to connect to Bitfinex WebSocket: %s", err)
	}

	g.wsClient = wsClient

	symbolsToMarket := make(map[string]gateway.Market)
	for _, market := range g.Markets() {
		symbolsToMarket[market.Symbol] = market
	}

	g.marketDataGateway = NewMarketDataGateway(g.options, g.wsClient, g.tickCh)

	g.accountGateway = NewAccountGateway(g.options, g.wsClient, g.tickCh, g.api)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return err
		}
	}

	// Register WebSocket callbacks
	g.wsClient.RegisterBookHandler(func(update *BookUpdate) {
		market, ok := symbolsToMarket[update.Symbol]
		if !ok {
			log.Printf("Bfx book update failed to locate symbol %s on symbolsToMarket", update.Symbol)
			return
		}

		var amount float64
		if update.Count == 0 {
			amount = 0
		} else {
			amount = math.Abs(update.Amount)
		}

		var side gateway.Side
		if update.Side == "bid" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		events := make([]gateway.Event, 1)
		events[0] = gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   side,
				Price:  update.Price,
				Amount: amount,
			},
		}

		g.tickCh <- gateway.Tick{
			EventLog: events,
		}
	})

	g.wsClient.RegisterBookSnapshotHandler(func(snapshot *BookSnapshot) {
		market, ok := symbolsToMarket[snapshot.Symbol]
		if !ok {
			log.Printf("Bfx snapshot failed to locate symbol %s on symbolsToMarket", snapshot.Symbol)
			return
		}

		events := make([]gateway.Event, 0, len(snapshot.Levels)+1)
		events = append(events, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: market.Symbol,
			},
		})

		for _, update := range snapshot.Levels {
			var amount float64
			if update.Count == 0 {
				amount = 0
			} else {
				amount = math.Abs(update.Amount)
			}

			var side gateway.Side
			if update.Side == "bid" {
				side = gateway.Bid
			} else {
				side = gateway.Ask
			}

			events = append(events, gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: market.Symbol,
					Side:   side,
					Price:  update.Price,
					Amount: amount,
				},
			})
		}

		g.tickCh <- gateway.Tick{
			EventLog: events,
		}
	})

	g.wsClient.RegisterTradeHandler(func(tradeUpdate *TradeUpdate) {
		market, ok := symbolsToMarket[tradeUpdate.Symbol]
		if !ok {
			log.Printf("Bfx trade update failed to locate symbol %s on symbolsToMarket", tradeUpdate.Symbol)
			return
		}

		var side gateway.Side
		if tradeUpdate.Direction == "buy" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		events := make([]gateway.Event, 1)
		events[0] = gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				ID:        strconv.FormatInt(tradeUpdate.ID, 10),
				Timestamp: time.Unix(0, tradeUpdate.MTS*int64(time.Millisecond)),
				Symbol:    market.Symbol,
				Direction: side,
				Price:     tradeUpdate.Price,
				Amount:    tradeUpdate.Amount,
			},
		}

		g.tickCh <- gateway.Tick{
			EventLog: events,
		}
	})

	g.wsClient.RegisterExecutionHandler(func(execution *TradeExecution) {
		g.accountGateway.processTradeExecution(execution)
	})

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	if g.wsClient != nil {
		return g.wsClient.Close()
	}
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	// Use REST API to fetch market information
	tickers, err := g.api.Tickers()
	if err != nil {
		return nil, fmt.Errorf("failed to load tickers: %s", err)
	}

	// Get pairs and market details from REST API
	pairs, err := g.api.GetPairs()
	if err != nil {
		return nil, fmt.Errorf("failed to load pairs: %s", err)
	}

	tickersMap := make(map[string]Ticker)
	for _, ticker := range tickers {
		tickersMap[ticker.Symbol] = ticker
	}

	commonMarkets := make([]gateway.Market, 0)
	for _, pair := range pairs {
		tradingSymbol := pairSymbolToTradingSymbol(pair.Pair)
		ticker, ok := tickersMap[tradingSymbol]
		if ok {
			amountPrecision := floatNumberOfDecimalPlaces(ticker.Volume)
			pricePrecision := floatNumberOfDecimalPlaces(ticker.LastPrice)
			commonMarkets = append(commonMarkets, gateway.Market{
				Exchange:         Exchange,
				Symbol:           tradingSymbol,
				Pair:             tradingSymbolToPair(tradingSymbol),
				MakerFee:         0.001,
				TakerFee:         0.002,
				PriceTick:        1 / math.Pow10(pricePrecision),
				AmountTick:       1 / math.Pow10(amountPrecision),
				MinimumOrderSize: pair.MinimumOrderSize,
				MarketType:       gateway.SpotMarket,
			})
		}
	}

	return commonMarkets, nil
}

// Tries to convert a float to a string and count the number of decimal places
// This is not a precise func.
func floatNumberOfDecimalPlaces(num float64) int {
	val := strconv.FormatFloat(num, 'f', -1, 64)
	parts := strings.Split(val, ".")

	if len(parts) < 2 {
		return 0
	}

	return len(parts[1])
}

// Bitfinex api returns us its pair symbols as: btcusd, ethbtc, ethusd
// But when receiving market data, it will return tBTCUSD
// The first letter means the update is for the trading order book
// and the next 6 letters are the actual symbol.
func pairSymbolToTradingSymbol(symbol string) string {
	return "t" + strings.ToUpper(symbol)
}

// Bitfinex trading instruments symbols look like: tBTCUSD, tETHBTC, tETHUSD...
// It will always have 1 character to identity the market and 3 letters
// to determine the base currency and 3 letter for the quote currency.
func tradingSymbolToPair(symbol string) gateway.Pair {
	var base string
	var quote string

	parts := strings.Split(symbol, ":")
	if len(parts) >= 2 {
		base = parts[0][1:]
		quote = parts[1]
	} else {
		base = symbol[1:4]
		quote = symbol[4:7]
	}

	return gateway.Pair{
		Base:  assetTranslateToCommon(base),
		Quote: assetTranslateToCommon(quote),
	}
}

func assetTranslateToCommon(asset string) string {
	asset = strings.ToUpper(asset)
	switch asset {
	case "UST":
		return "USDT"
	}
	return asset
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	// Set a default limit if not specified
	if params.Limit == 0 {
		params.Limit = 100
	}

	// Make sure we don't exceed Bitfinex's maximum book size
	if params.Limit > 100 {
		params.Limit = 100
	}

	// Call the API to fetch the depth data
	depth, err := g.api.Depth(market.Symbol, params.Limit)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	// Convert to the gateway.DepthBook format
	depthBook := gateway.DepthBook{
		Asks: gateway.PriceArrayToPriceLevels(depth.Asks),
		Bids: gateway.PriceArrayToPriceLevels(depth.Bids),
	}

	return depthBook, nil
}
