package bitfinex

import (
	"log"
	"sync"

	"github.com/herenow/atomic-gtw/gateway"
)

type MarketDataGateway struct {
	options  gateway.Options
	tickCh   chan gateway.Tick
	wsClient *WSClient
	mutex    sync.Mutex
}

func NewMarketDataGateway(options gateway.Options, wsClient *WSClient, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options:  options,
		tickCh:   tickCh,
		wsClient: wsClient,
		mutex:    sync.Mutex{},
	}
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	// Split markets into batches to respect Bitfinex's channel limit per connection
	batchesOf := wsSubscriptionCap - 1 // Reserve 1 slot for potential auth channel
	batches := make([][]gateway.Market, ((len(markets)-1)/batchesOf)+1)

	log.Printf("Bitfinex: subscribing to %d markets, will need %d batch(es) with maximum of %d topics each",
		len(markets), len(batches), batchesOf)

	for index, market := range markets {
		group := index / batchesOf
		batches[group] = append(batches[group], market)
	}

	// Process each batch
	for _, marketBatch := range batches {
		if err := g.subscribeBatch(marketBatch); err != nil {
			log.Printf("Bitfinex: error subscribing to market batch: %s", err)
		}
	}

	return nil
}

func (g *MarketDataGateway) subscribeBatch(markets []gateway.Market) error {
	// Subscribe to book and trades for each market
	for _, market := range markets {
		// Subscribe to order book
		if err := g.wsClient.SubscribeBook(market.Symbol, "P0", "F0", 25); err != nil {
			log.Printf("Bitfinex: error subscribing to order book for %s: %s", market.Symbol, err)
			continue
		}

		// Subscribe to trades
		if err := g.wsClient.SubscribeTrades(market.Symbol); err != nil {
			log.Printf("Bitfinex: error subscribing to trades for %s: %s", market.Symbol, err)
			continue
		}
	}

	return nil
}

// Tick returns the tick channel
func (g *MarketDataGateway) Tick() chan gateway.Tick {
	return g.tickCh
}
