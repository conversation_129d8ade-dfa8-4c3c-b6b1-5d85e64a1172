package bitfinex

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha512"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBase          = "https://api-pub.bitfinex.com"
	apiV1Base        = "https://api.bitfinex.com/v1"
	apiAuthBase      = "https://api.bitfinex.com"
	apiSymbolDetails = "/symbols_details"
	apiTickers       = "/v2/tickers"
	apiBook          = "/v2/book"
	apiBalances      = "/v1/balances"
	apiOrders        = "/v1/orders"
)

const (
	public  = false
	private = true
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
}

// NewAPI creates a new Bitfinex API client
func NewAPI(options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options:    options,
		httpClient: client,
	}
}

// Ticker represents a ticker response from Bitfinex
type Ticker struct {
	Symbol             string
	Bid                float64
	BidSize            float64
	Ask                float64
	AskSize            float64
	DailyChange        float64
	DailyChangePercent float64
	LastPrice          float64
	Volume             float64
	DailyHigh          float64
	DailyLow           float64
}

// PairDetails represents the details for a trading pair
type PairDetails struct {
	Pair             string  `json:"pair"`
	PricePrecision   int     `json:"price_precision"`
	InitialMargin    string  `json:"initial_margin"`
	MinimumMargin    string  `json:"minimum_margin"`
	MaximumOrderSize string  `json:"maximum_order_size"`
	MinimumOrderSize float64 `json:"minimum_order_size,string"`
	Expiration       string  `json:"expiration"`
	Margin           bool    `json:"margin"`
}

// WalletBalance represents a wallet balance in Bitfinex
type WalletBalance struct {
	Type      string  `json:"type"`
	Currency  string  `json:"currency"`
	Amount    float64 `json:"amount,string"`
	Available float64 `json:"available,string"`
}

// BitfinexOrder represents an order on Bitfinex
type BitfinexOrder struct {
	ID              int64   `json:"id"`
	Symbol          string  `json:"symbol"`
	Exchange        string  `json:"exchange"`
	Price           float64 `json:"price,string"`
	AvgPrice        float64 `json:"avg_execution_price,string"`
	Side            string  `json:"side"`
	Type            string  `json:"type"`
	Timestamp       string  `json:"timestamp"`
	IsLive          bool    `json:"is_live"`
	IsCancelled     bool    `json:"is_cancelled"`
	IsHidden        bool    `json:"is_hidden"`
	WasForced       bool    `json:"was_forced"`
	OriginalAmount  float64 `json:"original_amount,string"`
	RemainingAmount float64 `json:"remaining_amount,string"`
	ExecutedAmount  float64 `json:"executed_amount,string"`
}

// BitfinexDepth represents the order book response from Bitfinex
type BitfinexDepth struct {
	Asks []gateway.PriceArray
	Bids []gateway.PriceArray
}

// APIDepthLevel represents a price level in the order book
type APIDepthLevel []interface{}

func (t *Ticker) UnmarshalJSON(b []byte) error {
	var data [11]interface{}
	if err := json.Unmarshal(b, &data); err != nil {
		return err
	}

	t.Symbol = data[0].(string)
	t.Bid = parseFloat(data[1])
	t.BidSize = parseFloat(data[2])
	t.Ask = parseFloat(data[3])
	t.AskSize = parseFloat(data[4])
	t.DailyChange = parseFloat(data[5])
	t.DailyChangePercent = parseFloat(data[6])
	t.LastPrice = parseFloat(data[7])
	t.Volume = parseFloat(data[8])
	t.DailyHigh = parseFloat(data[9])
	t.DailyLow = parseFloat(data[10])

	return nil
}

// apiError represents an error from the API
type apiError struct {
	Exchange string
	Endpoint string
	Err      error
}

// Error implements the error interface
func (e *apiError) Error() string {
	return fmt.Sprintf("[%s] failed to request api [%s] error: %s", e.Exchange, e.Endpoint, e.Err.Error())
}

// newAPIError creates a new API error
func (a *API) newAPIError(endpoint string, err error) error {
	return &apiError{
		Exchange: Exchange.String(),
		Endpoint: endpoint,
		Err:      err,
	}
}

// Tickers returns all trading tickers
func (a *API) Tickers() ([]Ticker, error) {
	req, err := a.newHTTPRequest(http.MethodGet, apiBase+apiTickers, nil, public)
	if err != nil {
		return []Ticker{}, a.newAPIError(apiTickers, err)
	}

	q := req.URL.Query()
	q.Set("symbols", "ALL")
	req.URL.RawQuery = q.Encode()

	var tickers []Ticker
	err = a.makeHTTPRequest(req, &tickers)
	return tickers, err
}

// GetPairs returns all available trading pairs with their details
func (a *API) GetPairs() ([]PairDetails, error) {
	req, err := a.newHTTPRequest(http.MethodGet, apiV1Base+apiSymbolDetails, nil, public)
	if err != nil {
		return nil, a.newAPIError(apiSymbolDetails, err)
	}

	var pairs []PairDetails
	err = a.makeHTTPRequest(req, &pairs)
	return pairs, err
}

// Balances returns all account balances
func (a *API) Balances() ([]WalletBalance, error) {
	if a.options.ApiKey == "" || a.options.ApiSecret == "" {
		return nil, fmt.Errorf("API key and secret required for authenticated endpoints")
	}

	nonce := fmt.Sprintf("%d", time.Now().UnixNano()/1000000)
	payload := fmt.Sprintf("{\"request\":\"%s\",\"nonce\":\"%s\"}", apiBalances, nonce)

	req, err := a.newHTTPRequestWithAuth(http.MethodPost, apiAuthBase+apiBalances, nil, payload)
	if err != nil {
		return nil, a.newAPIError(apiBalances, err)
	}

	var walletBalances []WalletBalance
	if err := a.makeHTTPRequest(req, &walletBalances); err != nil {
		return nil, err
	}

	return walletBalances, nil
}

// OpenOrders returns all open orders for a market
func (a *API) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	if a.options.ApiKey == "" || a.options.ApiSecret == "" {
		return nil, fmt.Errorf("API key and secret required for authenticated endpoints")
	}

	nonce := fmt.Sprintf("%d", time.Now().UnixNano()/1000000)
	payload := fmt.Sprintf("{\"request\":\"%s\",\"nonce\":\"%s\"}", apiOrders, nonce)

	req, err := a.newHTTPRequestWithAuth(http.MethodPost, apiAuthBase+apiOrders, nil, payload)
	if err != nil {
		return nil, a.newAPIError(apiOrders, err)
	}

	var bfxOrders []BitfinexOrder
	if err := a.makeHTTPRequest(req, &bfxOrders); err != nil {
		// Check if it's the no orders case
		if strings.Contains(err.Error(), "no orders") {
			return []gateway.Order{}, nil
		}
		return nil, err
	}

	// Convert to gateway orders
	orders := make([]gateway.Order, 0, len(bfxOrders))
	for _, order := range bfxOrders {
		// Filter by market if provided
		if market.Symbol != "" {
			// Convert symbol format (potentially)
			bfxSymbol := "t" + strings.ToUpper(order.Symbol)
			if bfxSymbol != market.Symbol {
				continue
			}
		}

		var state gateway.OrderState
		if order.IsCancelled {
			state = gateway.OrderCancelled
		} else if order.RemainingAmount == 0 {
			state = gateway.OrderFullyFilled
		} else if order.ExecutedAmount > 0 {
			state = gateway.OrderPartiallyFilled
		} else {
			state = gateway.OrderOpen
		}

		var side gateway.Side
		if order.Side == "buy" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		orders = append(orders, gateway.Order{
			ID:       strconv.FormatInt(order.ID, 10),
			Market:   market,
			State:    state,
			Side:     side,
			Price:    order.Price,
			Amount:   order.OriginalAmount,
			AvgPrice: order.AvgPrice,
		})
	}

	return orders, nil
}

// Depth fetches the order book for a symbol with the given precision and limit
func (a *API) Depth(symbol string, limit int) (BitfinexDepth, error) {
	// Default precision is P0 (aggregated book with precision of 5 significant digits)
	precision := "P0"

	path := fmt.Sprintf("%s/%s/%s", apiBook, symbol, precision)
	req, err := a.newHTTPRequest(http.MethodGet, apiBase+path, nil, public)
	if err != nil {
		return BitfinexDepth{}, a.newAPIError(apiBook, err)
	}

	// Set limit parameter
	q := req.URL.Query()
	q.Set("len", strconv.Itoa(limit))
	req.URL.RawQuery = q.Encode()

	var depthLevels []APIDepthLevel
	err = a.makeHTTPRequest(req, &depthLevels)
	if err != nil {
		return BitfinexDepth{}, err
	}

	// Process the response into asks and bids
	depth := BitfinexDepth{
		Asks: make([]gateway.PriceArray, 0),
		Bids: make([]gateway.PriceArray, 0),
	}

	for _, level := range depthLevels {
		if len(level) < 3 {
			continue
		}

		price := parseFloat(level[0])
		count := parseFloat(level[1]) // Number of orders at this price level
		amount := parseFloat(level[2])

		// Skip empty price levels
		if count <= 0 {
			continue
		}

		// Bitfinex represents bids with positive amounts and asks with negative amounts
		priceArray := gateway.PriceArray{
			Price:  price,
			Amount: math.Abs(amount),
		}

		if amount > 0 {
			// Bid
			depth.Bids = append(depth.Bids, priceArray)
		} else if amount < 0 {
			// Ask
			depth.Asks = append(depth.Asks, priceArray)
		}
	}

	return depth, nil
}

// newHTTPRequest creates a new HTTP request
func (a *API) newHTTPRequest(method, url string, data io.Reader, isPrivate bool) (*http.Request, error) {
	req, err := http.NewRequest(method, url, data)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")

	return req, nil
}

// newHTTPRequestWithAuth creates a new HTTP request with authentication
func (a *API) newHTTPRequestWithAuth(method, url string, data io.Reader, payload string) (*http.Request, error) {
	req, err := http.NewRequest(method, url, data)
	if err != nil {
		return nil, err
	}

	encodedPayload := base64Encode([]byte(payload))
	signature := a.signPayload(encodedPayload)

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-BFX-APIKEY", a.options.ApiKey)
	req.Header.Set("X-BFX-PAYLOAD", encodedPayload)
	req.Header.Set("X-BFX-SIGNATURE", signature)

	return req, nil
}

// makeHTTPRequest makes an HTTP request and processes the response
func (a *API) makeHTTPRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}

	// Check for error responses
	if res.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP error %d: %s", res.StatusCode, string(body))
	}

	// Check for API errors
	if bytes.Contains(body, []byte("error")) {
		var apiErr struct {
			Error string `json:"error"`
		}
		if err := json.Unmarshal(body, &apiErr); err == nil && apiErr.Error != "" {
			return fmt.Errorf("API error: %s", apiErr.Error)
		}
	}

	if responseObject != nil {
		if err = json.Unmarshal(body, responseObject); err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", body, err)
		}
	}

	return nil
}

// signPayload signs a payload using HMAC-SHA384
func (a *API) signPayload(payload string) string {
	h := hmac.New(sha512.New384, []byte(a.options.ApiSecret))
	h.Write([]byte(payload))
	return hex.EncodeToString(h.Sum(nil))
}

// base64Encode encodes data to base64
func base64Encode(data []byte) string {
	return base64.StdEncoding.EncodeToString(data)
}
