package bitfinex

import (
	"context"
	"fmt"
	"log"
	"math"
	"math/rand"
	"regexp"
	"strconv"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

const BFX_MAX_CID_VALUE = ((1 << 45) / 2) - 1 // uInt45

var insufficientBalanceMatch = regexp.MustCompile(`not enough exchange balance`)

type AccountGateway struct {
	base.AccountGateway
	wsClient          *WSClient
	api               *API
	tickCh            chan gateway.Tick
	options           gateway.Options
	orders            map[int64]gateway.Order
	ordersLock        *sync.Mutex
	ordersCb          map[int64]chan gateway.Order
	ordersCbLock      *sync.Mutex
	orderIDToCID      map[int64]int64
	orderCancelCb     map[int64]chan error
	orderCancelCbLock *sync.Mutex
}

func NewAccountGateway(options gateway.Options, wsClient *WSClient, tickCh chan gateway.Tick, api *API) *AccountGateway {
	return &AccountGateway{
		options:           options,
		tickCh:            tickCh,
		orders:            make(map[int64]gateway.Order),
		ordersLock:        &sync.Mutex{},
		ordersCb:          make(map[int64]chan gateway.Order),
		ordersCbLock:      &sync.Mutex{},
		orderIDToCID:      make(map[int64]int64),
		orderCancelCb:     make(map[int64]chan error),
		orderCancelCbLock: &sync.Mutex{},
		wsClient:          wsClient,
		api:               api,
	}
}

func (g *AccountGateway) Connect() error {
	// We register callbacks for order responses
	g.wsClient.RegisterExecutionHandler(func(execution *TradeExecution) {
		g.processTradeExecution(execution)
	})

	return nil
}

func (g *AccountGateway) processOrderRequest(id, cid int64, status, err string) {
	g.ordersLock.Lock()
	g.ordersCbLock.Lock()
	defer g.ordersLock.Unlock()
	defer g.ordersCbLock.Unlock()

	cb, ok := g.ordersCb[cid]
	if !ok {
		log.Printf("Bfx failed to find orderCb cid [%d] on orders map on processOrderOpenError", cid)
		return
	}

	order, ok := g.orders[cid]
	if !ok {
		log.Printf("Bfx failed to find order cid [%d] on orders map on processOrderOpenError", cid)
		return
	}

	if status != "SUCCESS" {
		order.Error = fmt.Errorf("%s", err)
		delete(g.orders, cid)
	} else {
		order.ID = strconv.FormatInt(id, 10)
		order.State = gateway.OrderOpen
		g.orders[cid] = order
		g.orderIDToCID[id] = cid
	}

	cb <- order
}

func (g *AccountGateway) processTradeExecution(exec *TradeExecution) {
	g.ordersLock.Lock()
	defer g.ordersLock.Unlock()

	// Try to find the client ID (CID)
	var cid int64
	var ok bool

	// First try to use the CID from the execution if available
	if exec.CID != 0 {
		cid = exec.CID
		ok = true
	} else {
		// Fall back to looking up by order ID in our mapping
		cid, ok = g.orderIDToCID[exec.OrderID]
	}

	if !ok {
		log.Printf("Bfx failed to find CID for order ID [%d]", exec.OrderID)
		return
	}

	// Determine side based on the execution amount sign
	var side gateway.Side
	if exec.ExecAmount < 0 {
		side = gateway.Ask
	} else {
		side = gateway.Bid
	}

	// Create a Fill event with complete information including fees
	g.tickCh <- gateway.TickWithEvents(gateway.NewFillEvent(gateway.Fill{
		ID:            strconv.FormatInt(exec.ID, 10),
		OrderID:       strconv.FormatInt(exec.OrderID, 10),
		ClientOrderID: strconv.FormatInt(cid, 10),
		Timestamp:     time.Unix(0, exec.MTS*int64(time.Millisecond)),
		Symbol:        exec.Pair,
		Side:          side,
		Amount:        math.Abs(exec.ExecAmount),
		Price:         exec.ExecPrice,
		Fee:           exec.Fee,
		FeeAsset:      assetTranslateToCommon(exec.FeeCurrency),
	}))
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	// We need to implement a REST client for balances
	res, err := g.api.Balances()
	if err != nil {
		return nil, err
	}

	// Convert to gateway balances
	balances = make([]gateway.Balance, 0, len(res))
	for _, wallet := range res {
		// We're only interested in exchange wallets
		if wallet.Type == "exchange" {
			balances = append(balances, gateway.Balance{
				Asset:     assetTranslateToCommon(wallet.Currency),
				Available: wallet.Available,
				Total:     wallet.Amount,
			})
		}
	}

	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	return g.api.OpenOrders(market)
}

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	var amount = order.Amount
	if order.Side == gateway.Ask {
		amount = amount * -1
	}

	cid := rand.Int63n(BFX_MAX_CID_VALUE)
	cb := make(chan gateway.Order, 1)

	g.ordersLock.Lock()
	g.ordersCbLock.Lock()

	order.State = gateway.OrderSent
	g.orders[cid] = order
	g.ordersCb[cid] = cb

	g.ordersLock.Unlock()
	g.ordersCbLock.Unlock()

	orderData := map[string]interface{}{
		"cid":       cid,
		"symbol":    order.Market.Symbol,
		"type":      "EXCHANGE LIMIT",
		"price":     utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
		"amount":    utils.FloatToStringWithTick(amount, order.Market.AmountTick),
		"post_only": order.PostOnly,
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	resp, err := g.wsClient.SubmitOrder(ctx, orderData)
	if err != nil {
		// Clean up order data on error
		g.ordersLock.Lock()
		g.ordersCbLock.Lock()
		delete(g.orders, cid)
		delete(g.ordersCb, cid)
		g.ordersLock.Unlock()
		g.ordersCbLock.Unlock()
		return "", err
	}

	// Process order response
	g.processOrderRequest(resp.ID, resp.CID, resp.Status, resp.Text)

	select {
	case order := <-cb:
		if order.Error != nil {
			if insufficientBalanceMatch.MatchString(order.Error.Error()) {
				return order.ID, gateway.InsufficientBalanceErr
			} else {
				return order.ID, order.Error
			}
		} else {
			return order.ID, nil
		}
	case <-time.After(10 * time.Second):
		return "", fmt.Errorf("timed out after waiting or 10 seconds for order to open")
	}
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	orderId, err := strconv.ParseInt(order.ID, 10, 64)
	if err != nil {
		return fmt.Errorf("failed to parse int: %s", err)
	}

	cb := make(chan error, 1)
	g.orderCancelCbLock.Lock()
	g.orderCancelCb[orderId] = cb
	g.orderCancelCbLock.Unlock()

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	resp, err := g.wsClient.CancelOrder(ctx, orderId)
	if err != nil {
		return err
	}

	if resp.Status != "SUCCESS" {
		return fmt.Errorf("order cancel failed: %s", resp.Text)
	}

	return nil
}

// Map Bitfinex order status to common gateway order state
func mapStatusToCommon(status string) gateway.OrderState {
	switch status {
	case "ACTIVE":
		return gateway.OrderOpen
	case "EXECUTED":
		return gateway.OrderFullyFilled
	case "PARTIALLY FILLED":
		return gateway.OrderPartiallyFilled
	case "CANCELED":
		return gateway.OrderCancelled
	}
	return gateway.OrderUnknown
}
