package hyperliquid

import (
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
	"github.com/stretchr/testify/assert"
)

const (
	addr  = "******************************************"
	pvKey = ""
)

var api = NewAPI(MainnetURL, gateway.Options{ApiKey: pvKey})

func TestAccountInfo(t *testing.T) {
	states, err := api.Positions(addr)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Printf("Result is: %#v\n", states)
}

func TestOpenOrder(t *testing.T) {
	//cid := GetRandomCloid()
	ethAsset := 1
	ethDecimals := 4

	gtw := NewAccountGateway(gateway.Options{
		UserID: addr,
		ApiKey: pvKey,
	}, api, nil)

	//res, err := api.PlaceOrder(OrderRequest{
	//	AssetID:         ethAsset,
	//	AssetSzDecimals: ethDecimals,
	//	IsBuy:           true,
	//	LimitPx:         3000,
	//	Sz:              0.0038,
	//	OrderType:       OrderType{Limit: &LimitOrderType{Tif: "Gtc"}},
	//	ReduceOnly:      false,
	//	Cloid:           &cid,
	//})
	//if err != nil {
	//	t.Fatal(err)
	//	return
	//}
	//assert.NotNil(t, res)

	meta := SymbolMetadata{
		AssetID: ethAsset,
	}

	res, err := gtw.SendOrder(gateway.Order{
		Market: gateway.Market{Meta: meta, AmountTick: utils.PrecisionToTick(ethDecimals)},
		Side:   gateway.Bid,
		Price:  3000,
		Amount: 0.0038,
	})
	if err != nil {
		t.Fatal(err)
		return
	}
	assert.NotNil(t, res)

	time.Sleep(5)

	oid, _ := strconv.Atoi(res)

	res1, err := api.CancelOrderByOid(ethAsset, oid)
	if err != nil {
		t.Fatal(err)
		return
	}
	assert.NotNil(t, res1)
}
