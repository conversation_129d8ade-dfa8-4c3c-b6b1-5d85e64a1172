package hyperliquid

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	WsMainnet = "wss://api.hyperliquid.xyz/ws"
	WsTestnet = "wss://api.hyperliquid-testnet.xyz/ws"
)

type MarketDataGateway struct {
	options       gateway.Options
	ws            *utils.WsClient
	ch            chan []byte
	pongCh        chan struct{}
	tickCh        chan gateway.Tick
	coinsToSymbol map[string]string
}

func NewMarketDataGateway(options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	ws := utils.NewWsClient()
	ws.SetProxies(options.Proxies)
	return &MarketDataGateway{
		options:       options,
		ch:            make(chan []byte),
		pongCh:        make(chan struct{}),
		tickCh:        tickCh,
		ws:            ws,
		coinsToSymbol: make(map[string]string),
	}
}

type Subscription struct {
	Type     string  `json:"type"`
	User     *string `json:"user,omitempty"`
	Coin     *string `json:"coin,omitempty"`
	Interval *string `json:"interval,omitempty"`
	// NSigFigs is the fraction of orders to be grouped when fetching orderbook
	// 5 = 0.1 (group orders by 0.1 units) Fewer Orders Grouped
	// 4 = 1
	// 3 = 10
	// 2 = 100 (group orders by 100 units) More Orders Grouped
	NSigFigs int32 `json:"nSigFigs,omitempty"`
}

type WSRequest struct {
	Method       string       `json:"method"`
	Subscription Subscription `json:"subscription"`
}

type WSResponse struct {
	Channel string          `json:"channel"`
	Data    json.RawMessage `json:"data"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	if err := g.ws.Connect(WsMainnet); err != nil {
		return fmt.Errorf("ws connect err: %w", err)
	}
	g.ws.SubscribeMessages(g.ch)

	for _, mkt := range markets {
		meta, err := gateway.DecodeMeta[SymbolMetadata](mkt.Meta)
		if err != nil {
			return fmt.Errorf("decode market metadata for market %s err: %w", mkt.Symbol, err)
		}

		// Register coin to symbol mapping
		g.coinsToSymbol[meta.Coin] = mkt.Symbol

		if err := g.Subscribe(Subscription{
			Type:     "l2Book",
			Coin:     &meta.Coin,
			NSigFigs: 5,
		}); err != nil {
			return fmt.Errorf("ws subscribe markets err: %w", err)
		}

		if err := g.Subscribe(Subscription{
			Type: "trades",
			Coin: &meta.Coin,
		}); err != nil {
			return fmt.Errorf("ws subscribe trades err: %w", err)
		}
	}

	if g.options.UserID != "" {
		if err := g.Subscribe(Subscription{
			Type: "userFills",
			User: &g.options.UserID,
		}); err != nil {
			return fmt.Errorf("ws subscribe order updates err: %w", err)
		}
	}

	go g.websocketPinger()
	go g.messageHandler()

	return nil
}

func (g *MarketDataGateway) messageHandler() {
	for msg := range g.ch {
		var resp WSResponse
		if err := json.Unmarshal(msg, &resp); err != nil {
			g.handleError(fmt.Errorf("unmarshaling ws message: %w\n", err))
			continue
		}

		switch resp.Channel {
		case "subscriptionResponse":
			// Handle subscription confirmation
		case "l2Book":
			var ob WSBook
			if err := json.Unmarshal(resp.Data, &ob); err != nil {
				g.handleError(fmt.Errorf("unmarshaling orderbook updates: %w\n", err))
				continue
			}
			g.handleOrderbookUpdates(ob)
		case "trades":
			var trades []WSTrade
			if err := json.Unmarshal(resp.Data, &trades); err != nil {
				g.handleError(fmt.Errorf("unmarshaling trades updates: %w\n", err))
			}
			g.handleTradesUpdate(trades)
		case "userFills":
			var fills WSFills
			if err := json.Unmarshal(resp.Data, &fills); err != nil {
				g.handleError(fmt.Errorf("unmarshaling fills updates: %w\n", err))
			}
			g.handleFills(fills)
		case "pong":
			select {
			case g.pongCh <- struct{}{}:
			default:
			}
		}
	}
}

type ObPriceLevel struct {
	Px float64 `json:"px,string"`
	Sz float64 `json:"sz,string"`
	N  int32   `json:"n"` // number of orders
}

type WSBook struct {
	Coin   string           `json:"coin"`
	Levels [][]ObPriceLevel `json:"levels"` // index 0 for Bid and 1 for Ask
	Time   int64            `json:"time"`
}

func (g *MarketDataGateway) handleOrderbookUpdates(ob WSBook) {
	symbol, ok := g.coinsToSymbol[ob.Coin]
	if !ok {
		log.Printf("%s unknown coin %s in orderbook update", Exchange, ob.Coin)
		return
	}

	if len(ob.Levels) != 2 {
		return
	}
	b := ob.Levels[0]
	a := ob.Levels[1]

	evLogs := make([]gateway.Event, 0, len(b)+len(a)+1)

	// It's not documented, but hyperliquid sends a snapshot of the top 20 prices
	// in each message, they don't send deletes.
	evLogs = append(evLogs, gateway.NewSnapshotSequenceEvent(gateway.SnapshotSequence{
		Symbol: symbol,
	}))

	for _, price := range b {
		evLogs = append(evLogs, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Bid,
				Price:  price.Px,
				Amount: price.Sz,
			},
		})
	}

	for _, price := range a {
		evLogs = append(evLogs, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Ask,
				Price:  price.Px,
				Amount: price.Sz,
			},
		})
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          evLogs,
	}
}

type WSTrade struct {
	Tid  int64   `json:"tid"`
	Coin string  `json:"coin"`
	Side string  `json:"side"`
	Px   float64 `json:"px,string"`
	Sz   float64 `json:"sz,string"`
	Time int64   `json:"time"`
}

func (g *MarketDataGateway) handleTradesUpdate(trades []WSTrade) {
	if len(trades) == 0 {
		return
	}

	evLogs := make([]gateway.Event, len(trades))
	for i, t := range trades {
		var side gateway.Side
		if t.Side == "A" {
			side = gateway.Ask
		} else {
			side = gateway.Bid
		}

		symbol, ok := g.coinsToSymbol[t.Coin]
		if !ok {
			log.Printf("%s unknown coin %s in trade [%s %f@%f] update", Exchange, t.Coin, side, t.Px, t.Sz)
			continue
		}

		evLogs[i] = gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				Timestamp: gateway.ParseTimestamp(t.Time),
				Symbol:    symbol,
				ID:        strconv.FormatInt(t.Tid, 10),
				Direction: side,
				Price:     t.Px,
				Amount:    t.Sz,
			},
		}
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          evLogs,
	}
}

type Fill struct {
	Coin          string  `json:"coin"`
	Px            float64 `json:"px,string"`
	Sz            float64 `json:"sz,string"`
	Fee           float64 `json:"fee,string"`
	Side          string  `json:"side"`
	StartPosition string  `json:"startPosition"`
	Dir           string  `json:"dir"`
	ClosedPnl     string  `json:"closedPnl"`
	Hash          string  `json:"hash"`
	Oid           int64   `json:"oid"`
	Crossed       bool    `json:"crossed"`
	Tid           int64   `json:"tid"`
	FeeToken      string  `json:"feeToken"`
	Time          int64   `json:"time"`
}

type WSFills struct {
	Fills []Fill `json:"fills"`
}

func (g *MarketDataGateway) handleFills(req WSFills) {
	if len(req.Fills) == 0 {
		return // skip empty fills
	}

	evLogs := make([]gateway.Event, len(req.Fills))
	for i, f := range req.Fills {
		var side gateway.Side

		if f.Side == "A" {
			side = gateway.Ask
		} else {
			side = gateway.Bid
		}

		evLogs[i] = gateway.Event{
			Type: gateway.FillEvent,
			Data: gateway.Fill{
				Timestamp: gateway.ParseTimestamp(f.Time),
				Symbol:    f.Coin,
				ID:        strconv.FormatInt(f.Tid, 10),
				OrderID:   strconv.FormatInt(f.Oid, 10),
				Side:      side,
				Price:     f.Px,
				Amount:    f.Sz,
				Taker:     f.Crossed, // whether order crossed the spread (was taker)
				Fee:       f.Fee,
				FeeAsset:  f.FeeToken,
			},
		}
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          evLogs,
	}
}

// Subscribe creates a subscription for a given channel.
func (g *MarketDataGateway) Subscribe(sub Subscription) error {
	req, err := json.Marshal(WSRequest{Method: "subscribe", Subscription: sub})
	if err != nil {
		return fmt.Errorf("marshal subscription request: %w", err)
	}
	return g.ws.WriteMessage(req)
}

func (g *MarketDataGateway) websocketPinger() {
	pongTimeout := 5 * time.Second
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case _ = <-ticker.C:
			if err := g.ws.WriteMessage([]byte(`{"method":"ping"}`)); err != nil {
				err = fmt.Errorf("ws send ping error: %w", err)
				panic(err)
			}

			select {
			case <-g.pongCh:
			case <-time.After(pongTimeout):
				err := fmt.Errorf("pong not received after %s", pongTimeout)
				panic(err)
			}
		}
	}
}

func (g *MarketDataGateway) handleError(err error) {
	log.Printf("[%s] error: %v\n", Exchange, err)
}
