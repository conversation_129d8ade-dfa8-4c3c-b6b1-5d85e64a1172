package hyperliquid

import (
	"bytes"
	"crypto/rand"
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/common/math"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/signer/core/apitypes"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
	"github.com/vmihailenco/msgpack/v5"
)

const (
	MainnetURL = "https://api.hyperliquid.xyz"
	TestnetURL = "https://api.hyperliquid-testnet.xyz"

	ETHChainID int64 = 1337

	VerifyingContract = "******************************************"
)

// GetRandomCloid generates a random cloid (a 16-byte hexadecimal string).
//
// This function creates a slice of 16 bytes, fills it with cryptographically
// secure random data from the system's random number generator, and then encodes
// the data as a hexadecimal string.
func GetRandomCloid() string {
	buf := make([]byte, 16)
	// then we can call rand.Read.
	if _, err := rand.Read(buf); err != nil {
		log.Fatalf("[%s] error while generating random string: %s", Exchange, err)
	}
	return hexutil.Encode(buf)
}

type API struct {
	baseURL string
	options gateway.Options
	client  *utils.HttpClient

	isMainNet bool
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:   baseURL,
		options:   options,
		isMainNet: baseURL == MainnetURL,
		client:    client,
	}
}

func Post[T any](api *API, path string, payload any) (*T, error) {
	apiURL := fmt.Sprintf("%s%s", api.baseURL, path)

	bodyReq, err := json.Marshal(payload)

	req, err := http.NewRequest(http.MethodPost, apiURL, bytes.NewReader(bodyReq))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := api.client.SendRequest(req)
	if err != nil {
		return nil, err
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	resp.Body.Close()

	var result T
	if err = json.Unmarshal(body, &result); err != nil {
		var typeErr *json.UnmarshalTypeError
		if errors.As(err, &typeErr) {
			return nil, fmt.Errorf(
				"cannot unmarshal field '%s' of value '%s' into type '%s' in response",
				typeErr.Field, typeErr.Value, typeErr.Type)
		}
		return nil, fmt.Errorf("failed to unmarshal response into type %T, body: %s", result, body)
	}

	return &result, nil
}

type Symbols struct {
	Universe []struct {
		Name         string `json:"name"`
		SzDecimals   int    `json:"szDecimals"`
		MaxLeverage  int    `json:"maxLeverage"`
		OnlyIsolated bool   `json:"onlyIsolated"`
	} `json:"universe"`
}

func (a *API) GetPerpSymbols() (*Symbols, error) {
	request := GetInfoRequest{
		Type: "meta",
	}

	res, err := Post[Symbols](a, "/info", request)
	if err != nil {
		return nil, fmt.Errorf("[%s] get positions, err: %w", Exchange, err)
	}

	return res, nil
}

type SpotToken struct {
	Name        string      `json:"name"`
	SzDecimals  int         `json:"szDecimals"`
	WeiDecimals int         `json:"weiDecimals"`
	Index       int         `json:"index"`
	TokenID     string      `json:"tokenId"`
	IsCanonical bool        `json:"isCanonical"`
	EvmContract interface{} `json:"evmContract"` // Use interface{} to handle potential null values
	FullName    interface{} `json:"fullName"`    // Use interface{} to handle potential null values
}

type SpotUniverse struct {
	Name        string `json:"name"`
	Tokens      []int  `json:"tokens"`
	Index       int    `json:"index"`
	IsCanonical bool   `json:"isCanonical"`
}

type SpotMeta struct {
	Tokens   []SpotToken    `json:"tokens"`
	Universe []SpotUniverse `json:"universe"`
}

func (a *API) GetSpotSymbols() (*SpotMeta, error) {
	request := GetInfoRequest{
		Type: "spotMeta",
	}

	res, err := Post[SpotMeta](a, "/info", request)
	if err != nil {
		return nil, fmt.Errorf("[%s] get positions, err: %w", Exchange, err)
	}

	return res, nil
}

type CumFunding struct {
	AllTime     float64 `json:"allTime,string"`
	SinceOpen   float64 `json:"sinceOpen,string"`
	SinceChange float64 `json:"sinceChange,string"`
}

type Leverage struct {
	Type  string `json:"type"`
	Value int32  `json:"value"`
}

type Position struct {
	Coin           string     `json:"coin"`
	Szi            float64    `json:"szi,string"`
	EntryPx        float64    `json:"entryPx,string"`
	PositionValue  float64    `json:"positionValue,string"`
	UnrealizedPnl  float64    `json:"unrealizedPnl,string"`
	ReturnOnEquity float64    `json:"returnOnEquity,string"`
	LiquidationPx  float64    `json:"liquidationPx,string"`
	MarginUsed     float64    `json:"marginUsed,string"`
	MaxLeverage    int32      `json:"maxLeverage"`
	Leverage       Leverage   `json:"leverage"`
	CumFunding     CumFunding `json:"cumFunding"`
}

type AssetPosition struct {
	Position Position `json:"position"`
	Type     string   `json:"type"`
}

type MarginSummary struct {
	AccountValue    float64 `json:"accountValue,string"`
	TotalMarginUsed float64 `json:"totalMarginUsed,string"`
	TotalNtlPos     float64 `json:"totalNtlPos,string"`
	TotalRawUsd     float64 `json:"totalRawUsd,string"`
}

type GetUserStateRequest struct {
	User  string `json:"user"`
	Typez string `json:"type"`
}

type UserStateResponse struct {
	MarginSummary      MarginSummary   `json:"marginSummary"`
	CrossMarginSummary MarginSummary   `json:"crossMarginSummary"`
	Withdrawable       string          `json:"withdrawable"`
	AssetPositions     []AssetPosition `json:"assetPositions"`
	Time               int64           `json:"time"`
}

func (a *API) Positions(address string) (*UserStateResponse, error) {
	req := GetUserStateRequest{
		User:  address,
		Typez: "clearinghouseState",
	}

	res, err := Post[UserStateResponse](a, "/info", req)
	if err != nil {
		return nil, fmt.Errorf("[%s] get positions, err: %w", Exchange, err)
	}

	return res, nil
}

type GetInfoRequest struct {
	User     *string `json:"user,omitempty"`
	Type     string  `json:"type"`
	OID      *string `json:"oid,omitempty"`
	Coin     *string `json:"coin,omitempty"`
	NSigFigs *int32  `json:"nSigFigs,omitempty"`
}

type OpenOrder struct {
	Oid        int64   `json:"oid"`
	Coin       string  `json:"coin"`
	Side       string  `json:"side"`
	LimitPx    float64 `json:"limitPx,string"`
	Sz         float64 `json:"sz,string"`
	OrigSz     float64 `json:"origSz,string"`
	ReduceOnly bool    `json:"reduceOnly"`
	Timestamp  int64   `json:"timestamp"`
}

func (a *API) GetOrders(address string) ([]OpenOrder, error) {
	req := GetInfoRequest{
		User: &address,
		Type: "openOrders",
	}

	res, err := Post[[]OpenOrder](a, "/info", req)
	if err != nil {
		return nil, fmt.Errorf("[%s] get open orders, err: %w", Exchange, err)
	}

	return *res, nil
}

type OrderResponse struct {
	Order struct {
		Order struct {
			Children         []any  `json:"children"`
			Cloid            string `json:"cloid"`
			Coin             string `json:"coin"`
			IsPositionTpsl   bool   `json:"isPositionTpsl"`
			IsTrigger        bool   `json:"isTrigger"`
			LimitPx          string `json:"limitPx"`
			Oid              int64  `json:"oid"`
			OrderType        string `json:"orderType"`
			OrigSz           string `json:"origSz"`
			ReduceOnly       bool   `json:"reduceOnly"`
			Side             string `json:"side"`
			Sz               string `json:"sz"`
			Tif              string `json:"tif"`
			Timestamp        int64  `json:"timestamp"`
			TriggerCondition string `json:"triggerCondition"`
			TriggerPx        string `json:"triggerPx"`
		} `json:"order"`
		Status          string `json:"status"`
		StatusTimestamp int64  `json:"statusTimestamp"`
	} `json:"order"`
	Status string `json:"status"`
}

func (a *API) FindOrder(cloid string) (*OrderResponse, error) {
	request := GetInfoRequest{
		User: &a.options.UserID,
		Type: "orderStatus",
		OID:  &cloid,
	}

	res, err := Post[OrderResponse](a, "/info", request)
	if err != nil {
		return nil, fmt.Errorf("[%s] get order by id, err: %w", Exchange, err)
	}

	return res, nil
}

type Liquidation struct {
	User      string `json:"liquidatedUser"`
	MarkPrice string `json:"markPx"`
	Method    string `json:"method"`
}

type OrderFill struct {
	Cloid         string       `json:"cloid"`
	ClosedPnl     string       `json:"closedPnl"`
	Coin          string       `json:"coin"`
	Crossed       bool         `json:"crossed"`
	Dir           string       `json:"dir"`
	Fee           string       `json:"fee"`
	FeeToken      string       `json:"feeToken"`
	Hash          string       `json:"hash"`
	Oid           int          `json:"oid"`
	Px            string       `json:"px"`
	Side          string       `json:"side"`
	StartPosition string       `json:"startPosition"`
	Sz            string       `json:"sz"`
	Tid           int64        `json:"tid"`
	Time          int64        `json:"time"`
	Liquidation   *Liquidation `json:"liquidation"`
}

func (a *API) GetFills(address string) ([]OrderFill, error) {
	request := GetInfoRequest{
		User: &address,
		Type: "userFills",
	}

	res, err := Post[[]OrderFill](a, "/info", request)
	if err != nil {
		return nil, fmt.Errorf("[%s] get order by id, err: %w", Exchange, err)
	}

	return *res, nil
}

type DepthLevel struct {
	Px float64 `json:"px,string"`
	Sz float64 `json:"sz,string"`
	N  int32   `json:"n"` // number of orders
}

type DepthBook struct {
	Coin   string         `json:"coin"`
	Levels [][]DepthLevel `json:"levels"` // index 0 for Bid and 1 for Ask
	Time   int64          `json:"time"`
}

func (a *API) DepthBook(coin string, nSigFigs int32) (*DepthBook, error) {
	request := GetInfoRequest{
		Type:     "l2Book",
		Coin:     &coin,
		NSigFigs: &nSigFigs,
	}

	res, err := Post[DepthBook](a, "/info", request)
	if err != nil {
		return nil, fmt.Errorf("[%s] get order by id, err: %w", Exchange, err)
	}

	return res, nil
}

type RestingStatus struct {
	OrderID int    `json:"oid"`
	Cloid   string `json:"cloid"`
}

type FilledStatus struct {
	OrderID int    `json:"oid"`
	AvgPx   string `json:"avgPx"`
	TotalSz string `json:"totalSz"`
}

type StatusResponse struct {
	Resting *RestingStatus `json:"resting"`
	Filled  *FilledStatus  `json:"filled"`
	Error   *string        `json:"error"`
}

type DataResponse struct {
	Statuses []StatusResponse `json:"statuses"`
}

type InnerResponse struct {
	Type string       `json:"type"`
	Data DataResponse `json:"data"`
}

type PlaceOrderResponse struct {
	Status   string         `json:"status"`
	Response *InnerResponse `json:"response"`
}

type OrderRequest struct {
	AssetID         int       `json:"coin"`
	AssetSzDecimals int       `json:"sz_decimals"`
	IsBuy           bool      `json:"is_buy"`
	Sz              float64   `json:"sz"`
	LimitPx         float64   `json:"limit_px"`
	OrderType       OrderType `json:"order_type"`
	ReduceOnly      bool      `json:"reduce_only"`
	Cloid           *string   `json:"cloid,omitempty"`
}

func (a *API) PlaceOrder(req OrderRequest) (string, error) {
	action := PlaceOrderAction{
		Type:     "order",
		Orders:   []OrderWire{orderReqToWire(req)},
		Grouping: "na",
	}

	ts := getNonce()

	rsvSign, err := a.signL1Action(action, ts)
	if err != nil {
		return "", fmt.Errorf("[%s] sign place order request, err: %w", Exchange, err)
	}

	payload := ExchangeRequest{
		Action:    action,
		Nonce:     ts,
		Signature: *rsvSign,
	}

	result, err := Post[PlaceOrderResponse](a, "/exchange", payload)
	if err != nil {
		return "", fmt.Errorf("[%s] place order, err: %w", Exchange, err)
	}

	if len(result.Response.Data.Statuses) == 0 {
		return "", fmt.Errorf("[%s] no status responses received, result: %v", Exchange, result)
	}

	res := result.Response.Data.Statuses[0]

	switch {
	case res.Resting != nil:
		if res.Resting == nil {
			return "", fmt.Errorf("[%s] missing resting status for open order, response: %v", Exchange, res)
		}
		if req.Cloid != nil {
			return res.Resting.Cloid, nil
		}
		return strconv.Itoa(res.Resting.OrderID), nil
	case res.Filled != nil:
		if res.Filled == nil {
			return "", fmt.Errorf("[%s] missing resting status for filled order, response: %v", Exchange, res)
		}
		return strconv.Itoa(res.Filled.OrderID), nil
	case res.Error != nil:
		if res.Error == nil {
			return "", fmt.Errorf("[%s] place order", Exchange)
		}
		return "", fmt.Errorf("[%s] place order, err: %s", Exchange, *res.Error)

	default:
		return "", fmt.Errorf("[%s] invalid place order status: %s", Exchange, result.Status)
	}
}

func (a *API) ReplaceOrder(id string, req OrderRequest) error {
	action := PlaceOrderAction{
		Type:     "modify",
		Oid:      &id,
		Orders:   []OrderWire{orderReqToWire(req)},
		Grouping: "na",
	}

	ts := getNonce()

	rsvSign, err := a.signL1Action(action, ts)
	if err != nil {
		return fmt.Errorf("[%s] sign replace order request, err: %w", Exchange, err)
	}

	payload := ExchangeRequest{
		Action:    action,
		Nonce:     ts,
		Signature: *rsvSign,
	}

	if _, err = Post[any](a, "/exchange", payload); err != nil {
		return fmt.Errorf("[%s] replace order, err: %w", Exchange, err)
	}

	return nil
}

type CancelDataResponse struct {
	Statuses []string `json:"statuses"`
}

type InnerCancelResponse struct {
	Data CancelDataResponse `json:"data"`
}

type CancelOrderResponse struct {
	Status   string              `json:"status"`
	Response InnerCancelResponse `json:"response"`
}

func (r CancelOrderResponse) IsCancelled() bool {
	if r.Status != "ok" {
		return false
	}
	for _, status := range r.Response.Data.Statuses {
		if status == "success" {
			return true
		}
		return false
	}
	return false
}

type CancelCloidWire struct {
	Asset int    `msgpack:"asset" json:"asset"`
	Cloid string `msgpack:"cloid" json:"cloid"`
}

type CancelCloidOrderAction struct {
	Type    string            `msgpack:"type" json:"type"`
	Cancels []CancelCloidWire `msgpack:"cancels" json:"cancels"`
}

func (a *API) CancelOrderByCloid(assetID int, cloid string) (*CancelOrderResponse, error) {
	timestamp := getNonce()
	action := CancelCloidOrderAction{
		Type: "cancelByCloid",
		Cancels: []CancelCloidWire{
			{
				Asset: assetID,
				Cloid: cloid,
			},
		},
	}

	rsvSign, err := a.signL1Action(action, timestamp)
	if err != nil {
		return nil, fmt.Errorf("[%s] sign cancel order request, err: %w", Exchange, err)
	}

	payload := ExchangeRequest{
		Action:    action,
		Nonce:     timestamp,
		Signature: *rsvSign,
	}

	res, err := Post[CancelOrderResponse](a, "/exchange", payload)
	if err != nil {
		return nil, fmt.Errorf("[%s] cancel order request, err: %w", Exchange, err)
	}

	return res, nil
}

type CancelOidWire struct {
	Asset int `msgpack:"a" json:"a"`
	Oid   int `msgpack:"o" json:"o"`
}

type CancelOidOrderAction struct {
	Type    string          `msgpack:"type" json:"type"`
	Cancels []CancelOidWire `msgpack:"cancels" json:"cancels"`
}

func (a *API) CancelOrderByOid(assetID, oid int) (*CancelOrderResponse, error) {
	action := CancelOidOrderAction{
		Type:    "cancel",
		Cancels: []CancelOidWire{{Asset: assetID, Oid: oid}},
	}

	ts := getNonce()

	rsvSign, err := a.signL1Action(action, ts)
	if err != nil {
		return nil, fmt.Errorf("[%s] sign cancel order by oid request, err: %w", Exchange, err)
	}

	payload := ExchangeRequest{
		Action:    action,
		Nonce:     ts,
		Signature: *rsvSign,
	}

	res, err := Post[CancelOrderResponse](a, "/exchange", payload)
	if err != nil {
		return nil, fmt.Errorf("[%s] cancel order by oid request, err: %w", Exchange, err)
	}

	return res, nil
}

type RSVSignature struct {
	R string `json:"r"`
	S string `json:"s"`
	V byte   `json:"v"`
}

func toTypedSig(r [32]byte, s [32]byte, v byte) *RSVSignature {
	return &RSVSignature{
		R: hexutil.Encode(r[:]),
		S: hexutil.Encode(s[:]),
		V: v,
	}
}

// ExchangeRequest is used for auth operations like create or cancel order
type ExchangeRequest struct {
	Action       any          `json:"action"`
	Nonce        int64        `json:"nonce"`
	Signature    RSVSignature `json:"signature"`
	VaultAddress *string      `json:"vaultAddress"`
}

type sigRequest struct {
	PrimaryType string
	DType       []apitypes.Type
	DTypeMsg    map[string]interface{}
	IsMainNet   bool
}

// signL1Action generates an RSV signature for the given L1 action.
// It marshals the action data, appends a nonce, and creates a typed data
// message for signing. The message is signed using the wallet private key
// and returns the resulting RSV signature.
func (a *API) signL1Action(action any, nonce int64) (*RSVSignature, error) {
	data, err := msgpack.Marshal(action)
	if err != nil {
		panic(fmt.Sprintf("failed to marshal the data %s", err))
	}

	nonceBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(nonceBytes, uint64(nonce))
	data = append(data, nonceBytes...)
	data = append(data, []byte("\x00")...)

	msg := apitypes.TypedDataMessage{
		"source":       getNetSource(a.isMainNet),
		"connectionId": crypto.Keccak256Hash(data).Bytes(),
	}

	req := sigRequest{
		PrimaryType: "Agent",
		DType: []apitypes.Type{
			{
				Name: "source",
				Type: "string",
			},
			{
				Name: "connectionId",
				Type: "bytes32",
			},
		},
		DTypeMsg:  msg,
		IsMainNet: a.isMainNet,
	}

	contractType := apitypes.Types{
		req.PrimaryType: req.DType,
		"EIP712Domain": {
			{Name: "name", Type: "string"},
			{Name: "version", Type: "string"},
			{Name: "chainId", Type: "uint256"},
			{Name: "verifyingContract", Type: "address"},
		},
	}

	domain := apitypes.TypedDataDomain{
		Name:              "Exchange",
		Version:           "1",
		ChainId:           math.NewHexOrDecimal256(ETHChainID),
		VerifyingContract: VerifyingContract,
	}

	typedData := apitypes.TypedData{
		Types:       contractType,
		PrimaryType: req.PrimaryType,
		Domain:      domain,
		Message:     req.DTypeMsg,
	}

	key, err := crypto.HexToECDSA(a.options.ApiKey)
	if err != nil {
		return nil, fmt.Errorf("parse apikey to ecdsa, err: %w", err)
	}

	byts, _, err := apitypes.TypedDataAndHash(typedData)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate hash action sign: %w", err)
	}

	sig, err := crypto.Sign(byts, key)
	if err != nil {
		return nil, fmt.Errorf("failed to sign action: %w", err)
	}

	var r, s [32]byte
	v := sig[64] + 27
	copy(r[:], sig[:32])
	copy(s[:], sig[32:64])

	return toTypedSig(r, s, v), nil
}

func getNetSource(isMain bool) string {
	if isMain {
		return "a"
	} else {
		return "b"
	}
}

func getNonce() int64 {
	return time.Now().UnixMilli()
}
