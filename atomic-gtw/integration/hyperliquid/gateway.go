package hyperliquid

import (
	"fmt"
	"log"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

var Exchange = gateway.Exchange{
	Name: "Hyperliquid",
}

type Gateway struct {
	base.Gateway
	options        gateway.Options
	mktDataGtw     *MarketDataGateway
	accountGateway *AccountGateway
	tickCh         chan gateway.Tick
	api            *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(MainnetURL, options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.options, g.api, g.tickCh)
	g.mktDataGtw = NewMarketDataGateway(g.options, g.tickCh)
	return nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.mktDataGtw.SubscribeMarkets(markets)
}

type SymbolMetadata struct {
	AssetID int    `json:"asset_id"`
	Coin    string `json:"coin"`
}

const SpotStartIndex = 10000

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	perpMarkets, err := g.getPerpMarkets()
	if err != nil {
		return nil, fmt.Errorf("failed to get perp markets: %w", err)
	}

	spotMarkets, err := g.getSpotMarkets()
	if err != nil {
		return nil, fmt.Errorf("failed to get spot markets: %w", err)
	}

	return append(perpMarkets, spotMarkets...), nil
}

func (g *Gateway) getPerpMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.GetPerpSymbols()
	if err != nil {
		return nil, err
	}

	var commonMarkets []gateway.Market
	for i, u := range symbols.Universe {
		meta := SymbolMetadata{
			AssetID: i,
			Coin:    u.Name,
		}
		commonMarkets = append(commonMarkets, gateway.Market{
			Exchange: Exchange,
			Symbol:   u.Name,
			Pair: gateway.Pair{
				Base:  u.Name,
				Quote: "USDC",
			},
			MakerFee:               0.0001,
			TakerFee:               0.00035,
			PriceTick:              utils.PrecisionToTick(6), // max accepted for hypeliquid
			AmountTick:             utils.PrecisionToTick(u.SzDecimals),
			MinimumOrderMoneyValue: 10,
			MarketType:             gateway.FuturesMarket,
			FuturesMarginAsset:     "USDC",
			Meta:                   meta,
		})
	}

	return commonMarkets, nil
}

func (g *Gateway) getSpotMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.GetSpotSymbols()
	if err != nil {
		return nil, fmt.Errorf("failed to get spot symbols: %w", err)
	}

	tokenMap := make(map[int]SpotToken)
	for _, token := range symbols.Tokens {
		tokenMap[token.Index] = token
	}

	var commonMarkets []gateway.Market
	for _, u := range symbols.Universe {
		if len(u.Tokens) != 2 {
			log.Printf("Skipping universe %s with %d tokens, expected 2", u.Name, len(u.Tokens))
			continue
		}

		baseToken, ok1 := tokenMap[u.Tokens[0]]
		if !ok1 {
			log.Printf("Base token0 %d not found in token map for universe %s", u.Tokens[0], u.Name)
			continue
		}
		quoteToken, ok2 := tokenMap[u.Tokens[1]]
		if !ok2 {
			log.Printf("Base token1 %d not found in token map for universe %s", u.Tokens[1], u.Name)
			continue
		}

		meta := SymbolMetadata{
			AssetID: SpotStartIndex + u.Index,
			Coin:    u.Name,
		}

		market := gateway.Market{
			Exchange: Exchange,
			Symbol:   fmt.Sprintf("%s/%s", baseToken.Name, quoteToken.Name),
			Pair: gateway.Pair{
				Base:  baseToken.Name,
				Quote: quoteToken.Name,
			},
			MakerFee:   0.0004,
			TakerFee:   0.0007,
			PriceTick:  utils.PrecisionToTick(quoteToken.SzDecimals),
			AmountTick: utils.PrecisionToTick(baseToken.SzDecimals),
			Meta:       meta,
		}
		commonMarkets = append(commonMarkets, market)
	}

	return commonMarkets, nil
}

func (g *Gateway) GetDepthBook(mkt gateway.Market, _ gateway.GetDepthParams) (gateway.DepthBook, error) {
	ob, err := g.api.DepthBook(mkt.Symbol, 5)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	if len(ob.Levels) != 2 {
		return gateway.DepthBook{}, err
	}
	b := ob.Levels[0]
	a := ob.Levels[1]

	bids := make([]gateway.PriceLevel, len(a))
	for i, price := range b {
		bids[i] = gateway.PriceLevel{
			Price:  price.Px,
			Amount: price.Sz,
		}
	}

	asks := make([]gateway.PriceLevel, len(a))
	for i, price := range a {
		asks[i] = gateway.PriceLevel{
			Price:  price.Px,
			Amount: price.Sz,
		}
	}

	return gateway.DepthBook{Asks: asks, Bids: bids}.Sort(), nil
}
