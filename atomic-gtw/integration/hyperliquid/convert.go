package hyperliquid

import (
	"math"
	"math/big"
	"strconv"
)

type OrderType struct {
	Limit *LimitOrderType `msgpack:"limit,omitempty" json:"limit,omitempty"`
}

type LimitOrderType struct {
	//  "Alo" | "Ioc" | "Gtc"
	Tif string `msgpack:"tif" json:"tif"`
}

type OrderTypeWire struct {
	Limit *LimitOrderType `msgpack:"limit,omitempty" json:"limit,omitempty"`
}

type PlaceOrderAction struct {
	Type     string      `msgpack:"type" json:"type"`
	Oid      *string     `msgpack:"oid,omitempty" json:"oid,omitempty"`
	Orders   []OrderWire `msgpack:"orders" json:"orders"`
	Grouping string      `msgpack:"grouping" json:"grouping"`
}

type OrderWire struct {
	Asset      int           `msgpack:"a" json:"a"`
	IsBuy      bool          `msgpack:"b" json:"b"`
	LimitPx    string        `msgpack:"p" json:"p"`
	SizePx     string        `msgpack:"s" json:"s"`
	ReduceOnly bool          `msgpack:"r" json:"r"`
	OrderType  OrderTypeWire `msgpack:"t" json:"t"`
	Cloid      *string       `msgpack:"c,omitempty" json:"c,omitempty"`
}

func orderReqToWire(req OrderRequest) OrderWire {
	return OrderWire{
		Asset:      req.AssetID,
		IsBuy:      req.IsBuy,
		LimitPx:    floatToWire(req.LimitPx, nil),
		SizePx:     floatToWire(req.Sz, &req.AssetSzDecimals),
		ReduceOnly: req.ReduceOnly,
		OrderType:  orderTypeToWire(req.OrderType),
		Cloid:      req.Cloid,
	}
}

func orderTypeToWire(orderType OrderType) OrderTypeWire {
	if orderType.Limit != nil {
		return OrderTypeWire{
			Limit: &LimitOrderType{
				Tif: orderType.Limit.Tif,
			},
		}
	}
	return OrderTypeWire{}
}

// floatToWire format the float with custom decimal places, default is 6
// hyperliquid only allows at most 6 digits.
func floatToWire(x float64, szDecimals *int) string {
	// Converts the float64 value to a big.Float for precision.
	bigf := big.NewFloat(x)

	// If the number of decimal places is provided, uses that value.
	var maxDecSz uint
	if szDecimals != nil {
		maxDecSz = uint(*szDecimals)
	} else {
		// If not provided, calculates the number of decimal places based on the
		// integer part of the float64 value.  The maximum number of decimal places
		// is 6, but it will be reduced if the integer part is large.
		intPart, _ := bigf.Int64()

		intSize := len(strconv.FormatInt(intPart, 10))
		if intSize >= 6 {
			maxDecSz = 0
		} else {
			maxDecSz = uint(6 - intSize)
		}
	}

	x, _ = bigf.Float64()
	mult := math.Pow(10.0, float64(maxDecSz))

	// Rounds the float64 value to the determined number of decimal places.
	rounded := math.Floor(x*mult) / mult

	// Converts the rounded value to a string and returns it.
	return strconv.FormatFloat(rounded, 'f', -1, 64)
}
