package hyperliquid

import (
	"fmt"
	"strconv"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	options gateway.Options
	api     *API
	tickCh  chan gateway.Tick
}

func NewAccountGateway(options gateway.Options, api *API, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		api:     api,
		options: options,
		tickCh:  tickCh,
	}
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	res, err := g.api.Positions(g.options.UserID)
	if err != nil {
		return nil, err
	}

	balances := make([]gateway.Balance, len(res.AssetPositions))
	for i, pos := range res.AssetPositions {
		balances[i] = gateway.Balance{
			Asset:     pos.Position.Coin,
			Total:     pos.Position.Szi,
			Available: pos.Position.Szi,
		}
	}

	balances = append(balances, gateway.Balance{
		Asset:     "USDC",
		Total:     res.MarginSummary.AccountValue,
		Available: res.MarginSummary.AccountValue - res.MarginSummary.TotalMarginUsed,
	})

	return balances, err
}

func (g *AccountGateway) OpenOrders(_ gateway.Market) ([]gateway.Order, error) {
	res, err := g.api.GetOrders(g.options.UserID)
	if err != nil {
		return nil, err
	}

	orders := make([]gateway.Order, len(res))
	for i, o := range res {
		var side gateway.Side
		if o.Side == "A" {
			side = gateway.Ask
		} else {
			side = gateway.Bid
		}

		var (
			filled = o.OrigSz - o.Sz
			cost   = o.LimitPx * o.OrigSz
			avg    float64
		)

		if filled > 0 {
			avg = cost / filled
		}

		var state gateway.OrderState
		switch {
		case filled == o.OrigSz:
			state = gateway.OrderOpen
		case filled < o.OrigSz:
			state = gateway.OrderPartiallyFilled
		case filled == 0:
			state = gateway.OrderFullyFilled
		}

		orders[i] = gateway.Order{
			ID:               strconv.FormatInt(o.Oid, 10),
			Type:             gateway.LimitOrder,
			Side:             side,
			State:            state,
			Price:            o.LimitPx,
			Amount:           o.OrigSz,
			FilledAmount:     filled,
			RemainingAmount:  o.Sz,
			FilledMoneyValue: avg * filled,
		}
	}

	return orders, nil
}

func (g *AccountGateway) SendOrder(o gateway.Order) (string, error) {
	var side bool
	switch o.Side {
	case gateway.Bid:
		side = true
	case gateway.Ask:
		side = false
	}

	if o.Market.AmountTick == 0 {
		return "", fmt.Errorf("[%s] missing amount tick", Exchange)
	}

	meta, err := gateway.DecodeMeta[SymbolMetadata](o.Market.Meta)
	if err != nil {
		return "", fmt.Errorf("decode market metadata err: %w", err)
	}

	req := OrderRequest{
		AssetID:         meta.AssetID,
		AssetSzDecimals: utils.TickPrecision(o.Market.AmountTick),
		IsBuy:           side,
		Sz:              utils.FloorToTick(o.Amount, o.Market.AmountTick),
		LimitPx:         utils.FloorToTick(o.Price, o.Market.PriceTick),
		OrderType:       OrderType{Limit: &LimitOrderType{Tif: "Gtc"}},
	}

	if o.ClientOrderID != "" {
		req.Cloid = &o.ClientOrderID
	}

	order, err := g.api.PlaceOrder(req)
	if err != nil {
		return "", err
	}

	return order, nil
}

func (g *AccountGateway) ReplaceOrder(o gateway.Order) (gateway.Order, error) {
	var side bool
	switch o.Side {
	case gateway.Bid:
		side = true
	case gateway.Ask:
		side = false
	}

	meta, err := gateway.DecodeMeta[SymbolMetadata](o.Market.Meta)
	if err != nil {
		return gateway.Order{}, fmt.Errorf("decode market metadata err: %w", err)
	}

	if err := g.api.ReplaceOrder(o.ID, OrderRequest{
		AssetID:         meta.AssetID,
		AssetSzDecimals: utils.TickPrecision(o.Market.AmountTick),
		IsBuy:           side,
		Sz:              utils.FloorToTick(o.Amount, o.Market.AmountTick),
		LimitPx:         utils.FloorToTick(o.Price, o.Market.PriceTick),
		OrderType:       OrderType{Limit: &LimitOrderType{Tif: "Gtc"}},
		Cloid:           &o.ClientOrderID,
	}); err != nil {
		return gateway.Order{}, err
	}

	return o, nil
}

func (g *AccountGateway) CancelOrder(o gateway.Order) error {
	meta, err := gateway.DecodeMeta[SymbolMetadata](o.Market.Meta)
	if err != nil {
		return fmt.Errorf("decode market metadata err: %w", err)
	}
	assetID := meta.AssetID

	if o.ClientOrderID != "" {
		if _, err := g.api.CancelOrderByCloid(assetID, o.ClientOrderID); err != nil {
			return err
		}
		return nil
	}

	oid, err := strconv.Atoi(o.ID)
	if err != nil {
		return err
	}

	if _, err = g.api.CancelOrderByOid(assetID, oid); err != nil {
		return err
	}

	return nil
}

func (g *AccountGateway) Positions() ([]gateway.Position, error) {
	positions, err := g.api.Positions(g.options.UserID)
	if err != nil {
		return nil, err
	}

	gtwPoss := make([]gateway.Position, len(positions.AssetPositions))
	for i, p := range positions.AssetPositions {
		gtwPoss[i] = gateway.Position{
			Symbol:        p.Position.Coin,
			Amount:        p.Position.Szi,
			Value:         p.Position.PositionValue,
			ExecutedPNL:   p.Position.ReturnOnEquity,
			UnrealizedPNL: p.Position.UnrealizedPnl,
		}
	}

	return gtwPoss, gateway.NotImplementedErr
}
