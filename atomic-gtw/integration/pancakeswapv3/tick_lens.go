package pancakeswapv3

import (
	"context"
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum/ethclient"
	"log"
	"math"
	"math/big"
	"sort"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/herenow/atomic-gtw/gateway"
)

var (
	ErrInvalidTickSpacing = errors.New("invalid tick spacing")
	ErrZeroNet            = errors.New("tick net delta must be zero")
	ErrSorted             = errors.New("ticks must be sorted")
)

// TickLensAddresses maps chain IDs to official TickLens contract addresses
var TickLensAddresses = map[gateway.ChainID]common.Address{
	gateway.ChainEthereum: common.HexToAddress("******************************************"),
	gateway.ChainArbitrum: common.HexToAddress("******************************************"),
}

// TickLens ABI for getPopulatedTicksInWord function
const tickLensABI = `[{"inputs":[{"internalType":"address","name":"pool","type":"address"},{"internalType":"int16","name":"tickBitmapIndex","type":"int16"}],"name":"getPopulatedTicksInWord","outputs":[{"components":[{"internalType":"int24","name":"tick","type":"int24"},{"internalType":"int128","name":"liquidityNet","type":"int128"},{"internalType":"uint128","name":"liquidityGross","type":"uint128"}],"internalType":"struct ITickLens.PopulatedTick[]","name":"populatedTicks","type":"tuple[]"}],"stateMutability":"view","type":"function"}]`

// Multicall3 ABI remains the same
const multicall3ABI = `[{"inputs":[{"components":[{"internalType":"address","name":"target","type":"address"},{"internalType":"bool","name":"allowFailure","type":"bool"},{"internalType":"bytes","name":"callData","type":"bytes"}],"internalType":"struct Multicall3.Call3[]","name":"calls","type":"tuple[]"}],"name":"aggregate3","outputs":[{"components":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"bytes","name":"returnData","type":"bytes"}],"internalType":"struct Multicall3.Result[]","name":"returnData","type":"tuple[]"}],"stateMutability":"payable","type":"function"}]`

type Multicall3Call3 struct {
	Target       common.Address
	AllowFailure bool
	CallData     []byte
}

type Multicall3Result struct {
	Success    bool
	ReturnData []byte
}

type Tick struct {
	Index          int
	LiquidityGross *big.Int
	LiquidityNet   *big.Int
}

// TickLens is a utility for fetching and providing tick data from V3 pools
type TickLens struct {
	client           *ethclient.Client
	multicallAddress common.Address
	multicallABI     abi.ABI
	poolABI          *abi.ABI
	tickLensAddress  common.Address
	tickLensABI      abi.ABI
	ticks            []Tick
	tickSpacing      int
}

// NewTickLens creates a new TickLens instance
func NewTickLens(chainID gateway.ChainID, tickSpacing int, client *ethclient.Client) (*TickLens, error) {
	multicallAddress, exists := MulticallAddresses[chainID]
	if !exists {
		return nil, fmt.Errorf("multicall3 contract not available on chain %s", chainID)
	}

	parsedMulticallABI, err := abi.JSON(strings.NewReader(multicall3ABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse Multicall3 ABI: %w", err)
	}

	parsedPoolABI, err := PancakeswapV3PoolMetaData.GetAbi()
	if err != nil {
		return nil, fmt.Errorf("failed to parse pool ABI: %w", err)
	}

	lens := &TickLens{
		client:           client,
		multicallAddress: multicallAddress,
		multicallABI:     parsedMulticallABI,
		poolABI:          parsedPoolABI,
		tickLensAddress:  common.Address{},
		tickLensABI:      abi.ABI{},
		ticks:            []Tick{},
		tickSpacing:      tickSpacing,
	}

	if tickLensAddr, exists := TickLensAddresses[chainID]; exists {
		parsedTickLensABI, err := abi.JSON(strings.NewReader(tickLensABI))
		if err != nil {
			return nil, fmt.Errorf("failed to parse TickLens ABI: %w", err)
		}

		lens.tickLensAddress = tickLensAddr
		lens.tickLensABI = parsedTickLensABI
	}

	return lens, nil
}

// LoadPoolTicks loads tick data for a specific pool.
// It first attempts a fast, limited fetch around the current price. If no ticks
// are found (common in pools with sparse liquidity), it falls back to a full
// scan to ensure correctness.
func (t *TickLens) LoadPoolTicks(poolAddress common.Address, currentTick int) error {
	const wordsToFetch = 5 // Number of words to fetch on each side of the current tick

	currentWord := int16((currentTick / t.tickSpacing) >> 8)

	// Determine the range of words to fetch for the initial, fast query
	startWord := currentWord - wordsToFetch
	endWord := currentWord + wordsToFetch

	// Clamp to valid word range
	minWord := int16((MinTick / t.tickSpacing) >> 8)
	maxWord := int16((MaxTick / t.tickSpacing) >> 8)

	if startWord < minWord {
		startWord = minWord
	}
	if endWord > maxWord {
		endWord = maxWord
	}

	ticks, err := t.fetchTicksInWordRange(poolAddress, startWord, endWord)
	if err != nil {
		return fmt.Errorf("failed to get ticks in word range: %w", err)
	}

	// Fallback for sparse pools: if the initial limited fetch yields no ticks,
	// try fetching all ticks. This is slower but necessary for correctness on pools
	// with very distant liquidity.
	if len(ticks) == 0 {
		ticks, err = t.fetchTicksInWordRange(poolAddress, minWord, maxWord)
		if err != nil {
			return fmt.Errorf("failed to get all ticks on fallback: %w", err)
		}
	}

	// Basic validation of the fetched ticks
	for _, tick := range ticks {
		if tick.Index%t.tickSpacing != 0 {
			return ErrInvalidTickSpacing
		}
	}

	sort.Slice(ticks, func(i, j int) bool {
		return ticks[i].Index < ticks[j].Index
	})

	// This check is valid after sorting
	for i := 0; i < len(ticks)-1; i++ {
		if ticks[i].Index > ticks[i+1].Index {
			return ErrSorted
		}
	}

	t.ticks = ticks
	return nil
}

// fetchTicksInWordRange fetches ticks within a given range of bitmap words.
func (t *TickLens) fetchTicksInWordRange(poolAddress common.Address, startWord, endWord int16) ([]Tick, error) {
	allTicks := make([]Tick, 0)
	calls := make([]Multicall3Call3, 0)
	wordPositions := make([]int16, 0)

	for word := startWord; word <= endWord; word++ {
		callData, err := t.tickLensABI.Pack("getPopulatedTicksInWord", poolAddress, word)
		if err != nil {
			return nil, fmt.Errorf("failed to pack getPopulatedTicksInWord call: %w", err)
		}

		calls = append(calls, Multicall3Call3{
			Target:       t.tickLensAddress,
			AllowFailure: true,
			CallData:     callData,
		})
		wordPositions = append(wordPositions, word)

		// Execute in batches to avoid overwhelming the RPC node
		if len(calls) >= 50 || word == endWord {
			batchTicks, err := t.executeTickLensBatch(calls, wordPositions)
			if err != nil {
				return nil, err
			}
			allTicks = append(allTicks, batchTicks...)
			calls = make([]Multicall3Call3, 0)
			wordPositions = make([]int16, 0)
		}
	}

	return allTicks, nil
}

// GetTick returns tick data for a specific tick index
func (t *TickLens) GetTick(index int) Tick {
	tick := t.ticks[binarySearch(t.ticks, index)]
	if tick.Index != index {
		panic("index is not contained in ticks")
	}
	return tick
}

func (t *TickLens) NextInitializedTickWithinOneWord(tick int, lte bool, tickSpacing int) (int, bool) {
	compressed := math.Floor(float64(tick) / float64(tickSpacing))
	if lte {
		wordPos := int(compressed) >> 8
		minimum := (wordPos << 8) * tickSpacing
		if isBelowSmallest(t.ticks, tick) {
			return minimum, false
		}
		index := nextInitializedTick(t.ticks, tick, lte).Index
		nextTickValue := math.Max(float64(minimum), float64(index))
		return int(nextTickValue), int(nextTickValue) == index
	} else {
		wordPos := int(compressed+1) >> 8
		maximum := ((wordPos+1)<<8)*tickSpacing - 1
		if isAtOrAboveLargest(t.ticks, tick) {
			return maximum, false
		}
		index := nextInitializedTick(t.ticks, tick, lte).Index
		nextTickValue := math.Min(float64(maximum), float64(index))
		return int(nextTickValue), int(nextTickValue) == index
	}
}

func nextInitializedTick(ticks []Tick, tick int, lte bool) Tick {
	if lte {
		if isBelowSmallest(ticks, tick) {
			panic("below smallest")
		}
		if isAtOrAboveLargest(ticks, tick) {
			return ticks[len(ticks)-1]
		}
		index := binarySearch(ticks, tick)
		return ticks[index]
	} else {
		if isAtOrAboveLargest(ticks, tick) {
			panic("at or above largest")
		}

		// The original logic `binarySearch(ticks, tick) + 1` was fragile and
		// could cause an index out of bounds panic.
		// This new logic directly finds the index of the first tick with an index
		// greater than the input tick using sort.Search, which is more robust.
		i := sort.Search(len(ticks), func(j int) bool { return ticks[j].Index > tick })

		// The `isAtOrAboveLargest` guard ensures `i` will be a valid index.
		// If no tick is larger (which the guard prevents), sort.Search would return len(ticks).
		if i >= len(ticks) {
			// This path should not be reachable due to the guard.
			// Panicking here indicates a logic error elsewhere or a race condition.
			panic("logic error: failed to find next initialized tick")
		}
		return ticks[i]
	}
}

func isAtOrAboveLargest(ticks []Tick, tick int) bool {
	if len(ticks) == 0 {
		return false // Or handle as an error/special case
	}
	return tick >= ticks[len(ticks)-1].Index
}

func binarySearch(ticks []Tick, tick int) int {
	if len(ticks) == 0 || isBelowSmallest(ticks, tick) {
		// Handle cases where the tick is out of the range of fetched ticks.
		// Depending on desired behavior, you could panic, return an error, or a special value.
		// For now, let's assume valid input.
		log.Printf("Warning: tick %d is outside the range of fetched ticks", tick)
		// Returning 0 or len(ticks) might be options, but can lead to subtle bugs.
		// Let's proceed, but this highlights a potential issue with partial tick data.
	}

	// Standard binary search logic
	i := sort.Search(len(ticks), func(i int) bool { return ticks[i].Index >= tick })

	if i < len(ticks) && ticks[i].Index == tick {
		return i // Exact match
	}
	if i > 0 {
		return i - 1 // The element just before the insertion point
	}
	return 0 // Should not happen if isBelowSmallest is handled properly
}

func isBelowSmallest(ticks []Tick, tick int) bool {
	if len(ticks) == 0 {
		return true // No ticks, so any tick is "below"
	}
	return tick < ticks[0].Index
}

// executeTickLensBatch executes a batch of TickLens calls
func (t *TickLens) executeTickLensBatch(calls []Multicall3Call3, wordPositions []int16) ([]Tick, error) {
	callData, err := t.multicallABI.Pack("aggregate3", calls)
	if err != nil {
		return nil, fmt.Errorf("failed to pack aggregate3 call: %w", err)
	}

	result, err := t.client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &t.multicallAddress,
		Data: callData,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to execute multicall: %w", err)
	}

	var results []Multicall3Result
	if err = t.multicallABI.UnpackIntoInterface(&results, "aggregate3", result); err != nil {
		return nil, fmt.Errorf("failed to unpack multicall results: %w", err)
	}

	allTicks := make([]Tick, 0)
	for i, res := range results {
		if !res.Success || len(res.ReturnData) == 0 {
			continue
		}

		var populatedTicks []struct {
			Tick           *big.Int `json:"tick"`
			LiquidityNet   *big.Int `json:"liquidityNet"`
			LiquidityGross *big.Int `json:"liquidityGross"`
		}

		// The actual name in the ABI is "populatedTicks"
		err = t.tickLensABI.UnpackIntoInterface(&populatedTicks, "getPopulatedTicksInWord", res.ReturnData)
		if err != nil {
			fmt.Printf("Warning: failed to unpack tick data for word %d: %v\n", wordPositions[i], err)
			continue
		}

		for _, pt := range populatedTicks {
			allTicks = append(allTicks, Tick{
				Index:          int(pt.Tick.Int64()),
				LiquidityGross: pt.LiquidityGross,
				LiquidityNet:   pt.LiquidityNet,
			})
		}
	}

	return allTicks, nil
}
