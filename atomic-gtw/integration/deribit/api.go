package deribit

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"sync"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseSandbox = "https://test.deribit.com/api/v2"
	apiBaseProd    = "https://www.deribit.com/api/v2"
	apiPlaceOrder  = "/private/%s"
	apiCancelOrder = "/private/cancel"
	apiSymbols     = "/public/get_instruments"
	apiOpenOrders  = "/private/get_open_orders_by_instrument"
	apiAuth        = "/public/auth"
	apiSubAccounts = "/private/get_subaccounts"
	apiDepthBook   = "/public/get_order_book?depth=%d&instrument_name=%s"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
	token      string
	jwtMutex   sync.Mutex
	baseURL    string
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

type APIResponse struct {
	Jsonrpc string          `json:"jsonrpc"`
	Result  json.RawMessage `json:"result"`
	Error   APIError        `json:"error"`
	UsIn    int64           `json:"usIn"`
	UsOut   int64           `json:"usOut"`
	UsDiff  int             `json:"usDiff"`
	Testnet bool            `json:"testnet"`
}

type APIError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    string `json:"data"`
}

type APISymbol struct {
	TickSize                 float64 `json:"tick_size"`
	TakerCommission          float64 `json:"taker_commission"`
	Rfq                      bool    `json:"rfq"`
	QuoteCurrency            string  `json:"quote_currency"`
	PriceIndex               string  `json:"price_index"`
	MinTradeAmount           float64 `json:"min_trade_amount"`
	MakerCommission          float64 `json:"maker_commission"`
	Kind                     string  `json:"kind"`
	IsActive                 bool    `json:"is_active"`
	InstrumentName           string  `json:"instrument_name"`
	InstrumentId             int     `json:"instrument_id"`
	ExpirationTimestamp      int64   `json:"expiration_timestamp"`
	CreationTimestamp        int64   `json:"creation_timestamp"`
	CounterCurrency          string  `json:"counter_currency"`
	ContractSize             float64 `json:"contract_size"`
	BlockTradeTickSize       float64 `json:"block_trade_tick_size"`
	BlockTradeMinTradeAmount int     `json:"block_trade_min_trade_amount"`
	BlockTradeCommission     float64 `json:"block_trade_commission"`
	BaseCurrency             string  `json:"base_currency"`
}

func (a *API) Symbols() (symbols []APISymbol, err error) {
	symbolsParams := []string{"BTC", "ETH", "SOL", "USDC"}

	for _, symbol := range symbolsParams {
		apiSymbols := apiSymbols + "?kind=spot&currency=" + symbol
		req, err := a.newHttpRequest(http.MethodGet, apiSymbols, nil, false)
		if err != nil {
			return symbols, err
		}

		var symbolDataList []APISymbol
		err = a.makeHttpRequest(req, &symbolDataList)
		if err != nil {
			return nil, err
		}

		for _, symbol := range symbolDataList {
			symbols = append(symbols, symbol)
		}
	}

	return symbols, err
}

type APIRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      int64       `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params"`
}

type APIAuthParams struct {
	GrantType    string `json:"grant_type"`
	ClientId     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
}

type APIToken struct {
	TokenType    string `json:"token_type"`
	Sid          string `json:"sid"`
	Scope        string `json:"scope"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
	AccessToken  string `json:"access_token"`
}

func (a *API) AuthWithClientCredentials() (res APIToken, err error) {
	data := APIRequest{
		Method: "public/auth",
		Params: APIAuthParams{
			GrantType:    "client_credentials",
			ClientId:     a.options.ApiKey,
			ClientSecret: a.options.ApiSecret,
		},
	}

	dataBytes, err := json.Marshal(data)
	if err != nil {
		return res, err
	}

	dataIo := bytes.NewReader(dataBytes)
	req, err := a.newHttpRequest(http.MethodPost, apiAuth, dataIo, true)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	if err != nil {
		return res, err
	}

	return res, err
}

func (a *API) SetJwtToken(token string) {
	a.jwtMutex.Lock()
	defer a.jwtMutex.Unlock()

	a.token = token
}

func (a *API) GetJwtToken() string {
	a.jwtMutex.Lock()
	defer a.jwtMutex.Unlock()

	return a.token
}

type APIPortfolio struct {
	Currency                 string  `json:"currency"`
	AvailableFunds           float64 `json:"available_funds"`
	AvailableWithdrawalFunds float64 `json:"available_withdrawal_funds"`
	Balance                  float64 `json:"balance"`
	Equity                   float64 `json:"equity"`
	InitialMargin            float64 `json:"initial_margin"`
	MaintenanceMargin        float64 `json:"maintenance_margin"`
	MarginBalance            float64 `json:"margin_balance"`
}

type APISubAccount struct {
	Email        string         `json:"email"`
	ID           int64          `json:"id"`
	IsPassword   bool           `json:"is_password"`
	LoginEnabled bool           `json:"login_enabled"`
	Portfolio    []APIPortfolio `json:"portfolio"`
	SystemName   string         `json:"system_name"`
	Type         string         `json:"type"`
	Username     string         `json:"username"`
}

type APISubAccountParams struct {
	WithPortfolio bool `json:"with_portfolio"`
}

func (a *API) GetSubAccounts() (accs []APISubAccount, err error) {
	data := APIRequest{
		JSONRPC: "2.0",
		ID:      123,
		Method:  "private/get_subaccounts",
		Params: APISubAccountParams{
			WithPortfolio: true,
		},
	}

	bodyData, err := json.Marshal(data)
	if err != nil {
		return accs, err
	}

	req, err := a.newHttpRequest(http.MethodGet, apiSubAccounts, bytes.NewBuffer(bodyData), true)
	if err != nil {
		return accs, err
	}

	err = a.makeHttpRequest(req, &accs)
	return accs, err
}

type APIOrder struct {
	TimeInForce         string  `json:"time_in_force"`
	ReduceOnly          bool    `json:"reduce_only"`
	ProfitLoss          float64 `json:"profit_loss"`
	Price               float64 `json:"price"`
	PostOnly            bool    `json:"post_only"`
	OrderType           string  `json:"order_type"`
	OrderState          string  `json:"order_state"`
	OrderId             string  `json:"order_id"`
	MaxShow             float64 `json:"max_show"`
	LastUpdateTimestamp int64   `json:"last_update_timestamp"`
	Label               string  `json:"label"`
	IsLiquidation       bool    `json:"is_liquidation"`
	InstrumentName      string  `json:"instrument_name"`
	FilledAmount        float64 `json:"filled_amount"`
	Direction           string  `json:"direction"`
	CreationTimestamp   int64   `json:"creation_timestamp"`
	Commission          float64 `json:"commission"`
	AveragePrice        float64 `json:"average_price"`
	Api                 bool    `json:"api"`
	Amount              float64 `json:"amount"`
}

func (a *API) OpenOrders(symbol string) (orders []APIOrder, err error) {
	url := fmt.Sprintf(apiOpenOrders+"?instrument_name=%s", symbol)
	req, err := a.newHttpRequest(http.MethodGet, url, nil, true)
	if err != nil {
		return orders, err
	}

	err = a.makeHttpRequest(req, &orders)
	if err != nil {
		return orders, err
	}

	return orders, nil
}

type OrderRequest struct {
	Method string      `json:"method"`
	Params OrderParams `json:"params"`
}

type OrderParams struct {
	InstrumentName string `json:"instrument_name"`
	Price          string `json:"price"`
	Amount         string `json:"amount"`
	Type           string `json:"type"`
	PostOnly       bool   `json:"post_only"`
}

type APIPlaceOrderRes struct {
	Order APIOrder `json:"order"`
}

func (a *API) PlaceOrder(order io.Reader, side string) (string, error) {
	url := fmt.Sprintf(apiPlaceOrder, side)
	req, err := a.newHttpRequest(http.MethodPost, url, order, true)
	if err != nil {
		return "", err
	}

	var res APIPlaceOrderRes
	err = a.makeHttpRequest(req, &res)
	if err != nil {
		return "", err
	}

	return res.Order.OrderId, err
}

func (a *API) CancelOrder(orderID string) (err error) {
	url := fmt.Sprintf(apiCancelOrder+"?order_id=%s", orderID)
	req, err := a.newHttpRequest(http.MethodGet, url, nil, true)
	if err != nil {
		return err
	}

	var res APIOrder
	err = a.makeHttpRequest(req, &res)
	if err != nil {
		return err
	}

	if res.OrderId != orderID {
		return fmt.Errorf("failed to cancel order, expected to cancel order id: %s, instead canceled order id: %s", orderID, res.OrderId)
	}

	return nil
}

type APIDepthBook struct {
	Bids []gateway.PriceArray `json:"bids"`
	Asks []gateway.PriceArray `json:"asks"`
}

func (a *API) DepthBook(symbol string, params gateway.GetDepthParams) (book APIDepthBook, err error) {
	if params.Limit == 0 {
		params.Limit = 10000
	}
	url := fmt.Sprintf(apiDepthBook, params.Limit, symbol)
	req, err := a.newHttpRequest(http.MethodGet, url, nil, false)
	if err != nil {
		return book, err
	}

	err = a.makeHttpRequest(req, &book)
	if err != nil {
		return book, err
	}

	return book, err
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	if res.StatusCode != http.StatusOK && res.StatusCode != http.StatusCreated {
		err = fmt.Errorf("failed to make http request, status code: %d, body: %s", res.StatusCode, string(body))
		return err
	}

	var apiRes APIResponse
	err = json.Unmarshal(body, &apiRes)
	if err != nil {
		return fmt.Errorf("unmarshal err [%s] body:\n%s", err, string(body))
	}

	if apiRes.Error.Code != 0 {
		err = fmt.Errorf("api error code [%d] error message [%s], body: %s", apiRes.Error.Code, apiRes.Error.Message, string(body))
		return err
	}

	if responseObject != nil {
		err = json.Unmarshal(apiRes.Result, responseObject)
		if err != nil {
			return err
		}
	}

	return nil
}

func (a *API) newHttpRequest(method string, path string, data io.Reader, signed bool) (*http.Request, error) {
	url := a.baseURL + path
	req, err := http.NewRequest(method, url, data)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	if signed {
		req.Header.Set("Authorization", "Bearer "+a.GetJwtToken())
	}
	return req, nil
}
