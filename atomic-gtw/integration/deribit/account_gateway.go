package deribit

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api     *API
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		api:     api,
		options: options,
		tickCh:  tickCh,
	}
}

func (g *AccountGateway) Connect() error {
	token, err := g.api.AuthWithClientCredentials()
	if err != nil {
		return fmt.Errorf("api get auth err: %s", err)
	}

	g.api.SetJwtToken(token.AccessToken)

	err = g.jwtTokenRefresher(token.ExpiresIn)
	if err != nil {
		log.Println("Error updating jwt token: ", err)
		return err
	}

	return nil
}

func (g *AccountGateway) jwtTokenRefresher(expiresIn int64) error {
	go func() {
		for {
			log.Printf("%s updating jwt token in %d seconds...", Exchange, expiresIn)

			time.Sleep(time.Duration(expiresIn-60) * time.Second) // Update 60 seconds before expiration

			log.Printf("%s updating jwt token...", Exchange)

			token, err := g.api.AuthWithClientCredentials()
			if err != nil {
				panic(fmt.Errorf("failed update jwt token with refresh token, err: %s", err))
			}

			g.api.SetJwtToken(token.AccessToken)
			expiresIn = token.ExpiresIn
		}
	}()

	return nil
}

var insuficientBalanceMatch = regexp.MustCompile(`not_enough_funds`)
var tooManyRequestsMatch = regexp.MustCompile(`too_many_requests`)

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	var side string
	if order.Side == gateway.Bid {
		side = "buy"
	} else {
		side = "sell"
	}

	params := OrderParams{
		InstrumentName: order.Market.Symbol,
		Amount:         utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		Price:          utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
		Type:           "limit",
	}

	if order.PostOnly {
		params.PostOnly = true
	}

	data := OrderRequest{
		Method: "private/" + side,
		Params: params,
	}

	orderData, err := json.Marshal(data)
	if err != nil {
		return orderId, err
	}
	orderDataBuffer := bytes.NewBuffer(orderData)
	orderID, err := g.api.PlaceOrder(orderDataBuffer, side)
	if err != nil {
		switch {
		case insuficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		case tooManyRequestsMatch.MatchString(err.Error()):
			return "", gateway.RateLimitErr
		default:
			return "", err
		}
	}

	return orderID, err
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID)
}

type WsPortfolio struct {
	AvailableFunds           float64 `json:"available_funds"`
	AvailableWithdrawalFunds float64 `json:"available_withdrawal_funds"`
	Balance                  float64 `json:"balance"`
	Currency                 string  `json:"currency"`
	MarginBalance            float64 `json:"margin_balance"`
	SpotReserve              float64 `json:"spot_reserve"`
}

var userPortfolioRegex = regexp.MustCompile(`user.portfolio.*`)

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	ws := utils.NewWsClient()
	ws.SetHeaders(http.Header{
		"Authorization": []string{fmt.Sprintf("Bearer %s", g.api.GetJwtToken())},
	})
	feedUrl := wsFeedURL(g.options)

	err = ws.Connect(feedUrl)
	if err != nil {
		return balances, fmt.Errorf("ws connect err: %s", err)
	}
	defer ws.Close()

	channels := make([]string, 0)
	currencies := []string{"BTC", "ETH", "USDC", "USDT", "XRP", "MATIC", "SOL"}
	for _, currency := range currencies {
		channels = append(channels, fmt.Sprintf("user.portfolio.%s", currency))
	}

	params := make(map[string]interface{})
	params["channels"] = channels

	subRequest := WsRequest{
		Method: "private/subscribe",
		Params: params,
	}

	data, err := json.Marshal(subRequest)
	if err != nil {
		return balances, fmt.Errorf("failed to marshal sub request, err: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	defer ws.RemoveSubscriber(ch)

	if err := ws.WriteMessage(data); err != nil {
		return balances, fmt.Errorf("failed write sub msg to ws: %s", err)
	}

	balances = make([]gateway.Balance, 0)
	quit := make(chan struct{})
	errCh := make(chan error)
	go func() {
		for {
			select {
			case msg := <-ch:
				var resp WsResponse
				if err := json.Unmarshal(msg, &resp); err != nil {
					log.Printf("%s failed to unmarshal ws %s msg, err: %s", Exchange, string(msg), err)
				}

				if userPortfolioRegex.MatchString(resp.Params.Channel) {
					var portfolio WsPortfolio
					if err := json.Unmarshal(resp.Params.Data, &portfolio); err != nil {
						errCh <- fmt.Errorf("failed to unmarshal portfolio data %s, err: %s", string(resp.Params.Data), err)
						continue
					}

					balances = append(balances, gateway.Balance{
						Asset:     strings.ToUpper(portfolio.Currency),
						Available: portfolio.AvailableFunds,
						Total:     portfolio.MarginBalance,
					})

					if len(balances) >= len(currencies) {
						errCh <- nil
					}
				}
			case <-quit:
				return
			}
		}
	}()
	defer close(quit)

	select {
	case <-time.After(5 * time.Second):
		return balances, fmt.Errorf("timeout waiting for ws responses, received %d balances, expected %d", len(balances), len(currencies))
	case err := <-errCh:
		if err != nil {
			return balances, fmt.Errorf("error waiting for ws responses: %s", err)
		}
	}

	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrders, err := g.api.OpenOrders(market.Symbol)
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders))
	for _, order := range openOrders {
		o := mapAPIOrderToCommon(order)
		o.Market = market
		orders = append(orders, o)
	}

	return orders, nil
}

func (g *AccountGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	ws.SetHeaders(http.Header{
		"Authorization": []string{fmt.Sprintf("Bearer %s", g.api.GetJwtToken())},
	})
	feedUrl := wsFeedURL(g.options)

	err := ws.Connect(feedUrl)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	err = wsRequestHeartbeat(ws)
	if err != nil {
		return fmt.Errorf("failed to request heartbeat, err: %s", err)
	}

	channels := make([]string, 0)
	for _, market := range markets {
		channels = append(channels, fmt.Sprintf("user.trades.%s.raw", market.Symbol))
	}

	params := make(map[string]interface{})
	params["channels"] = channels

	subRequest := WsRequest{
		Method: "private/subscribe",
		Params: params,
	}

	data, err := json.Marshal(subRequest)

	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}

	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write sub msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ws, ch)

	return nil
}

var userTradeRegex = regexp.MustCompile(`user.trades\.(.*)\.raw`)

func (g *AccountGateway) messageHandler(ws *utils.WsClient, ch chan []byte) {
	for data := range ch {
		var msg WsResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarshal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		switch {
		case wsDefaultMessageHandler(ws, msg, data):
		case userTradeRegex.MatchString(msg.Params.Channel):
			if err := g.processFill(msg.Params.Data); err != nil {
				log.Printf("Deribit account ws error processing fill \"%s\": %s", data, err)
			}
		default:
			log.Printf("%s accGtw unprocessable message data [%s]", Exchange, string(data))
		}
	}
}

func wsRequestHeartbeat(ws *utils.WsClient) error {
	params := make(map[string]interface{})
	params["interval"] = 10 // seconds
	requestId := time.Now().UnixNano()

	heartbeatRequest := WsRequest{
		Method: "public/set_heartbeat",
		Params: params,
		ID:     requestId,
	}

	data, err := json.Marshal(heartbeatRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal heartbeat request, err: %s", err)
	}

	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write heartbeat msg to ws: %s", err)
	}

	resCh := make(chan []byte)
	authRes := make(chan error)
	quit := make(chan bool)
	ws.SubscribeMessages(resCh)
	defer ws.RemoveSubscriber(resCh)
	go func() {
		for {
			select {
			case msg := <-resCh:
				var resp WsResponse
				if err := json.Unmarshal(msg, &resp); err != nil {
					log.Printf("%s failed to unmarshal ws %s msg, err: %s", Exchange, string(msg), err)
					continue
				}

				if resp.ID == requestId {
					var result string
					if err := json.Unmarshal(resp.Result, &result); err != nil {
						authRes <- fmt.Errorf("failed to unmarshal heartbeat result %s, err: %s", string(resp.Result), err)
					} else if result != "ok" {
						authRes <- fmt.Errorf("failed to set heartbeat, result: %s", resp.Result)
					} else {
						authRes <- nil
					}
				}
			case <-quit:
				close(quit)
				return
			}
		}
	}()

	select {
	case err := <-authRes:
		return err
	case <-time.After(5 * time.Second):
		quit <- true
		return fmt.Errorf("timeout waiting for heartbeat response")
	}
}

var testRequestMsgID = time.Now().UnixNano()

func wsDefaultMessageHandler(ws *utils.WsClient, msg WsResponse, msgData []byte) bool {
	switch {
	case msg.ID == testRequestMsgID:
		// ignore test request response
		return true
	case msg.Method == "heartbeat":
		err := ws.WriteMessage([]byte(`{"jsonrpc":"2.0","method":"public/test","id":` + strconv.FormatInt(testRequestMsgID, 10) + `}`))
		if err != nil {
			log.Printf("%s failed to write pong to ws: %s", Exchange, err)
		}
		return true
	}

	return false
}

type WsUserTrade struct {
	TradeSeq       int         `json:"trade_seq"`
	TradeId        string      `json:"trade_id"`
	Timestamp      int64       `json:"timestamp"`
	TickDirection  int         `json:"tick_direction"`
	State          string      `json:"state"`
	SelfTrade      bool        `json:"self_trade"`
	RiskReducing   bool        `json:"risk_reducing"`
	ReduceOnly     bool        `json:"reduce_only"`
	ProfitLoss     float64     `json:"profit_loss"`
	Price          float64     `json:"price"`
	PostOnly       bool        `json:"post_only"`
	OrderType      string      `json:"order_type"`
	OrderId        string      `json:"order_id"`
	Mmp            bool        `json:"mmp"`
	MatchingId     interface{} `json:"matching_id"`
	MarkPrice      float64     `json:"mark_price"`
	Liquidity      string      `json:"liquidity"`
	InstrumentName string      `json:"instrument_name"`
	IndexPrice     float64     `json:"index_price"`
	FeeCurrency    string      `json:"fee_currency"`
	Fee            float64     `json:"fee"`
	Direction      string      `json:"direction"`
	Api            bool        `json:"api"`
	Amount         float64     `json:"amount"`
}

func (g *AccountGateway) processFill(data []byte) error {
	var matches []WsUserTrade
	err := json.Unmarshal(data, &matches)
	if err != nil {
		return fmt.Errorf("unmarhsal WsUserOrderDone [%s] err [%s]", string(data), err)
	}

	events := make([]gateway.Event, len(matches))
	for i, match := range matches {
		var side gateway.Side
		// This is the maker side, if we were the taker we would have bought
		if match.Direction == "buy" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		events[i] = gateway.NewFillEvent(gateway.Fill{
			Timestamp: time.Unix(0, match.Timestamp*int64(time.Millisecond)),
			Symbol:    match.InstrumentName,
			OrderID:   match.OrderId,
			Side:      side,
			Amount:    match.Amount,
			Price:     match.Price,
		})
	}

	g.tickCh <- gateway.TickWithEvents(events...)

	return nil
}
