package deribit

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	baseURL string
	options gateway.Options
	tickCh  chan gateway.Tick
	api     *API
}

func NewMarketDataGateway(baseURL string, options gateway.Options, tickCh chan gateway.Tick, api *API) *MarketDataGateway {
	return &MarketDataGateway{
		baseURL: baseURL,
		options: options,
		tickCh:  tickCh,
		api:     api,
	}
}

type WsRequest struct {
	Method  string                 `json:"method"`
	Params  map[string]interface{} `json:"params"`
	Jsonrpc string                 `json:"jsonrpc"`
	ID      int64                  `json:"id"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()

	err := ws.Connect(g.baseURL)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	err = wsRequestHeartbeat(ws)
	if err != nil {
		return fmt.Errorf("failed to request heartbeat, err: %s", err)
	}

	channels := make([]string, 0)
	for _, market := range markets {
		channels = append(channels, fmt.Sprintf("book.%s.100ms", market.Symbol))
		channels = append(channels, fmt.Sprintf("trades.%s.100ms", market.Symbol))
	}

	params := make(map[string]interface{})
	params["channels"] = channels

	subRequest := WsRequest{
		Method: "public/subscribe",
		Params: params,
	}

	data, err := json.Marshal(subRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}

	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write sub msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ws, ch)

	return nil
}

type WsResponseParams struct {
	Channel string          `json:"channel"`
	Data    json.RawMessage `json:"data"`
}

type WsResponse struct {
	Jsonrpc string           `json:"jsonrpc"`
	ID      int64            `json:"id"`
	Result  json.RawMessage  `json:"result"`
	Method  string           `json:"method"`
	Params  WsResponseParams `json:"params"`
}

var orderbookRegex = regexp.MustCompile(`book\.(.*)\.100ms`)
var tradesRegex = regexp.MustCompile(`trades\.(.*)\.100ms`)

func (g *MarketDataGateway) messageHandler(ws *utils.WsClient, ch chan []byte) {
	for data := range ch {
		var msg WsResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		switch {
		case wsDefaultMessageHandler(ws, msg, data):
		case orderbookRegex.MatchString(msg.Params.Channel):
			if err := g.processDepthUpdate(msg.Params.Data); err != nil {
				log.Printf("%s error processing book update \"%s\": %s", Exchange, data, err)
			}
		case tradesRegex.MatchString(msg.Params.Channel):
			if err := g.processTrade(msg.Params.Data); err != nil {
				log.Printf("%s error processing book update \"%s\": %s", Exchange, data, err)
			}
		default:
			log.Printf("%s mktdGtw unprocessable message data [%s]", Exchange, string(data))
		}
	}
}

type WsBook struct {
	Type           string               `json:"type"`
	Timestamp      int64                `json:"timestamp"`
	PrevChangeId   int64                `json:"prev_change_id"`
	InstrumentName string               `json:"instrument_name"`
	ChangeId       int64                `json:"change_id"`
	Bids           [][3]json.RawMessage `json:"bids"`
	Asks           [][3]json.RawMessage `json:"asks"`
}

type WsTrade struct {
	TradeSeq       int     `json:"trade_seq"`
	TradeId        string  `json:"trade_id"`
	Timestamp      int64   `json:"timestamp"`
	TickDirection  int     `json:"tick_direction"`
	Price          float64 `json:"price"`
	MarkPrice      float64 `json:"mark_price"`
	InstrumentName string  `json:"instrument_name"`
	IndexPrice     float64 `json:"index_price"`
	Direction      string  `json:"direction"`
	Amount         float64 `json:"amount"`
}

func (g *MarketDataGateway) processDepthUpdate(data json.RawMessage) error {
	var bookUpdate WsBook
	err := json.Unmarshal(data, &bookUpdate)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}

	eventLog := make([]gateway.Event, 0, len(bookUpdate.Bids)+len(bookUpdate.Asks)+1)
	appendEvents := func(symbol string, side gateway.Side, dataArray [][3]json.RawMessage) error {
		for _, data := range dataArray {
			var px, amount float64
			err := json.Unmarshal(data[1], &px)
			if err != nil {
				return fmt.Errorf("unmarshal price \"%s\" err: %s", data[1], err)
			}
			err = json.Unmarshal(data[2], &amount)
			if err != nil {
				return fmt.Errorf("unmarshal amount \"%s\" err: %s", data[2], err)
			}

			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  px,
					Amount: amount,
				},
			}

			eventLog = append(eventLog, event)
		}

		return nil
	}

	if bookUpdate.Type == "snapshot" {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: bookUpdate.InstrumentName,
			},
		})
	}

	if err := appendEvents(bookUpdate.InstrumentName, gateway.Ask, bookUpdate.Asks); err != nil {
		return fmt.Errorf("failed append ask events, err: %s", err)
	}
	if err := appendEvents(bookUpdate.InstrumentName, gateway.Bid, bookUpdate.Bids); err != nil {
		return fmt.Errorf("failed append bid events, err: %s", err)
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

func (g *MarketDataGateway) processTrade(data json.RawMessage) error {
	var tradeUpdate []WsTrade
	err := json.Unmarshal(data, &tradeUpdate)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}

	eventLog := make([]gateway.Event, len(tradeUpdate))
	for i, trade := range tradeUpdate {
		var direction gateway.Side
		if trade.Direction == "buy" {
			direction = gateway.Bid
		} else {
			direction = gateway.Ask
		}

		eventLog[i] = gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				ID:        trade.TradeId,
				Timestamp: time.Unix(0, trade.Timestamp*int64(time.Millisecond)),
				Symbol:    trade.InstrumentName,
				Direction: direction,
				Price:     trade.Price,
				Amount:    trade.Amount,
			},
		}
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}
