package deribit

import (
	"fmt"
	"log"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Deribit",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	tickCh            chan gateway.Tick
	api               *API
	marketDataGateway *MarketDataGateway
}

func NewGateway(options gateway.Options) gateway.Gateway {
	var apiBaseURL string
	if options.Staging {
		apiBaseURL = apiBaseSandbox
	} else {
		apiBaseURL = apiBaseProd
	}

	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(apiBaseURL, options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}
	g.marketDataGateway = NewMarketDataGateway(wsFeedURL(g.options), g.options, g.tickCh, g.api)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return fmt.Errorf("Failed to subscribe to market data, err %s", err)
	}

	if g.options.ApiKey != "" {
		err := g.accountGateway.SubscribeMarkets(markets)
		if err != nil {
			return fmt.Errorf("Failed to subscribe to account updates, err %s", err)
		}
	}

	return nil
}

func (g *Gateway) Close() error {
	return gateway.NotImplementedErr
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.Symbols()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(symbols))
	for _, symbol := range symbols {
		commonMarkets = append(commonMarkets, symbolToCommonMarket(symbol))
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func symbolToCommonMarket(symbol APISymbol) gateway.Market {
	return gateway.Market{
		Exchange: Exchange,
		Symbol:   symbol.InstrumentName,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(symbol.BaseCurrency),
			Quote: strings.ToUpper(symbol.QuoteCurrency),
		},
		TakerFee:               0.001,
		MakerFee:               0.001,
		PriceTick:              symbol.TickSize,
		AmountTick:             symbol.ContractSize,
		MinimumOrderSize:       symbol.MinTradeAmount,
		MinimumOrderMoneyValue: 0,
		MarketType:             gateway.FuturesMarket,
	}
}

func mapAPIOrderToCommon(o APIOrder) gateway.Order {
	return gateway.Order{
		ID:       o.OrderId,
		Side:     mapAPIOrderTypeToCommonSide(o.Direction),
		State:    mapAPIOrderStateToCommon(o.OrderState),
		Amount:   o.Amount,
		Price:    o.Price,
		AvgPrice: o.Price,
	}
}

func mapAPIOrderTypeToCommonSide(orderType string) gateway.Side {
	if len(orderType) >= 3 && orderType[0:3] == "buy" {
		return gateway.Bid
	} else if len(orderType) >= 4 && orderType[0:4] == "sell" {
		return gateway.Ask
	} else {
		log.Printf("Deribit invalid orderType \"%s\", unable to extract side from order type", orderType)
		return ""
	}
}

func mapAPIOrderStateToCommon(st string) gateway.OrderState {
	switch st {
	case "open":
		return gateway.OrderOpen
	case "created":
		return gateway.OrderOpen
	case "filled":
		return gateway.OrderFullyFilled
	case "partial-filled":
		return gateway.OrderPartiallyFilled
	case "canceled":
		return gateway.OrderCancelled
	case "partial-canceled":
		return gateway.OrderCancelled
	}
	return gateway.OrderUnknown
}

func wsFeedURL(options gateway.Options) string {
	if options.Staging {
		return "wss://test.deribit.com/ws/api/v2"
	} else {
		return "wss://www.deribit.com/ws/api/v2"
	}
}

func parsePriceLevelsToDepth(levels []gateway.PriceArray) []gateway.PriceLevel {
	var priceLevels []gateway.PriceLevel
	for _, level := range levels {
		priceLevels = append(priceLevels, gateway.PriceLevel(level))
	}
	return priceLevels
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks := parsePriceLevelsToDepth(depth.Asks)
	bids := parsePriceLevelsToDepth(depth.Bids)
	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
