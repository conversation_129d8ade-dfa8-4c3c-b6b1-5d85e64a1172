package bitget

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "BITGET",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	tickCh            chan gateway.Tick
	api               *API
	marketDataGateway *MarketDataGateway
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(apiBaseProd, options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

const wsURL = "wss://ws.bitget.com/spot/v1/stream"

func (g *Gateway) Connect() error {
	symbolsMarkets, err := g.getSymbolsAndMarkets()
	if err != nil {
		return fmt.Errorf("Failed to get markets, err %s", err)
	}
	symbolsMap := make(map[string]SymbolDetails)
	for symbol, market := range symbolsMarkets {
		symbolsMap[market.Symbol] = symbol
	}

	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh, wsURL, symbolsMap)
	if g.options.ApiKey != "" {
		if err = g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	g.marketDataGateway = NewMarketDataGateway(wsURL, g.options, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return fmt.Errorf("Failed to subscribe to market data, err %s", err)
	}

	if g.options.ApiKey != "" {
		err := g.accountGateway.SubscribeMarkets(markets)
		if err != nil {
			return fmt.Errorf("Failed to subscribe to account updates, err %s", err)
		}
	}
	return nil
}

func (g *Gateway) Close() error {
	return gateway.NotImplementedErr
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbolsMarkets, err := g.getSymbolsAndMarkets()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(symbolsMarkets))
	for _, market := range symbolsMarkets {
		commonMarkets = append(commonMarkets, market)
	}

	return commonMarkets, nil
}

func (g *Gateway) getSymbolsAndMarkets() (map[SymbolDetails]gateway.Market, error) {
	symbols, err := g.api.Symbols()
	if err != nil {
		return nil, err
	}

	symbolsMarkets := make(map[SymbolDetails]gateway.Market)
	for _, symbol := range symbols {
		market := symbolToCommonMarket(symbol)
		symbolsMarkets[symbol] = market
	}

	return symbolsMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func symbolToCommonMarket(symbol SymbolDetails) gateway.Market {

	priceTick := 1 / math.Pow10(int(symbol.PriceScale))
	amountTick := 1 / math.Pow10(int(symbol.QuantityScale))
	return gateway.Market{
		Exchange: Exchange,
		Symbol:   symbol.SymbolName,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(symbol.BaseCoin),
			Quote: strings.ToUpper(symbol.QuoteCoin),
		},
		TakerFee:         0.001,
		MakerFee:         0.001,
		PriceTick:        priceTick,
		AmountTick:       amountTick,
		MinimumOrderSize: symbol.MinTradeAmount,
		MarketType:       gateway.SpotMarket,
	}
}

func mapAPIOrderStateToCommon(st string) gateway.OrderState {
	switch st {
	case "new":
		return gateway.OrderOpen
	case "partial_fill":
		return gateway.OrderPartiallyFilled
	case "full_fill":
		return gateway.OrderFullyFilled
	case "cancelled":
		return gateway.OrderCancelled
	}
	return gateway.OrderUnknown
}

func parsePriceLevelsToDepth(levels [][]string) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel

	for _, level := range levels {
		if len(level) < 2 {
			return nil, fmt.Errorf("invalid price level: %v", level)
		}
		price, err := strconv.ParseFloat(level[0], 64)
		if err != nil {
			return nil, err
		}
		amount, err := strconv.ParseFloat(level[1], 64)
		if err != nil {
			return nil, err
		}
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		})
	}

	return priceLevels, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	if params.Limit == 0 {
		params.Limit = 100
	}
	depth, err := gtw.api.DepthBook(market.Symbol, params.Limit)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks, err := parsePriceLevelsToDepth(depth.Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	bids, err := parsePriceLevelsToDepth(depth.Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
