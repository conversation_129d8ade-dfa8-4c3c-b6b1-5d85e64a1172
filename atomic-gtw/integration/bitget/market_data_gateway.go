package bitget

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	baseURL string
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewMarketDataGateway(baseURL string, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		baseURL: baseURL,
		options: options,
		tickCh:  tickCh,
	}
}

type WsRequest struct {
	Op   string   `json:"op"`
	Args []WsArgs `json:"args"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()

	err := ws.Connect(g.baseURL)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}
	var args []WsArgs
	for _, market := range markets {
		args = append(args, WsArgs{
			Channel:  "books",
			InstId:   market.Symbol,
			InstType: "sp",
		},
			WsArgs{
				Channel:  "trade",
				InstId:   market.Symbol,
				InstType: "sp",
			})
	}

	bookRequest := WsRequest{
		Op:   "subscribe",
		Args: args,
	}
	data, err := json.Marshal(bookRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}

	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write sub msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	pongCh := make(chan bool)
	go g.messageHandler(ch, pongCh)
	go websocketPinger(ws, pongCh)

	return nil
}

type WsResponse struct {
	Arg struct {
		Channel  string `json:"channel"`
		InstId   string `json:"instId"`
		InstType string `json:"instType"`
	} `json:"arg"`
	Action string          `json:"action"`
	Data   json.RawMessage `json:"data"`
}

func (g *MarketDataGateway) messageHandler(ch chan []byte, pongCh chan bool) {
	for data := range ch {
		if string(data) == "pong" {
			pongCh <- true
			continue
		}
		var msg WsResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		switch msg.Arg.Channel {
		case "error":
			log.Printf("WS [%s] msg w/ error msg: %s", g.baseURL, string(data))
			continue
		case "trade":
			if err := g.processTradeUpdates(data); err != nil {
				log.Printf("%s error processing matches \"%s\": %s", g.baseURL, data, err)
			}

		case "books":
			if msg.Action == "snapshot" {
				err := g.processSnapshotMsg(data, msg.Arg.InstId)
				if err != nil {
					log.Printf("%s error processing snapshot \"%s\": %s", g.baseURL, data, err)
				}
			} else if msg.Action == "update" {
				err := g.processBookUpdateMsg(data, msg.Arg.InstId)
				if err != nil {
					log.Printf("%s error processing book update \"%s\": %s", g.baseURL, data, err)
				}
			}
		default:
			log.Printf("BitGet unprocessable message type [%s], data [%s]", msg.Data, string(data))
		}
	}
}

type WsResponseUpdate struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
	Action string         `json:"action"`
	Data   []WsBookUpdate `json:"data"`
}

type WsBookUpdate struct {
	Asks     []gateway.PriceArray `json:"asks"`
	Bids     []gateway.PriceArray `json:"bids"`
	Ts       string               `json:"ts"`
	Checksum int                  `json:"checksum"`
}

type WsResponseSnapShot struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
	Action string       `json:"action"`
	Data   []WsSnapShot `json:"data"`
}

type WsSnapShot struct {
	Asks     []gateway.PriceArray `json:"asks"`
	Bids     []gateway.PriceArray `json:"bids"`
	Ts       string               `json:"ts"`
	Checksum int                  `json:"checksum"`
}

func (g *MarketDataGateway) processSnapshotMsg(data []byte, InstId string) error {
	var wsResponse WsResponseSnapShot
	err := json.Unmarshal(data, &wsResponse)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}
	if len(wsResponse.Data) == 0 {
		return fmt.Errorf("no data")
	}
	for _, dt := range wsResponse.Data {
		g.processDepthUpdate(InstId, dt.Bids, dt.Asks, true)
	}

	return nil
}

func (g *MarketDataGateway) processBookUpdateMsg(data []byte, instId string) error {
	var wsResponse WsResponseUpdate
	err := json.Unmarshal(data, &wsResponse)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}
	if len(wsResponse.Data) == 0 {
		return fmt.Errorf("no data")
	}
	for _, dt := range wsResponse.Data {
		g.processDepthUpdate(instId, dt.Bids, dt.Asks, false)
	}

	return nil
}

func (g *MarketDataGateway) processDepthUpdate(symbol string, bids []gateway.PriceArray, asks []gateway.PriceArray, snapshot bool) {
	eventLog := make([]gateway.Event, 0, len(bids)+len(asks)+1)
	appendEvents := func(symbol string, side gateway.Side, prices []gateway.PriceArray) {
		for _, order := range prices {
			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  order.Price,
					Amount: order.Amount,
				},
			}

			eventLog = append(eventLog, event)
		}
	}

	if snapshot {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: symbol,
			},
		})
	}

	appendEvents(symbol, gateway.Ask, asks)
	appendEvents(symbol, gateway.Bid, bids)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

type WsResponseTrade struct {
	Action string `json:"action"`
	Arg    struct {
		InstType string `json:"instType"`
		Channel  string `json:"channel"`
		InstId   string `json:"instId"`
	} `json:"arg"`
	Data []json.RawMessage `json:"data"`
}

func (g *MarketDataGateway) processTradeUpdates(data []byte) error {
	var matches WsResponseTrade
	err := json.Unmarshal(data, &matches)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}
	if matches.Data == nil {
		return nil
	}

	eventLog := make([]gateway.Event, 0, 1)

	for _, match := range matches.Data {
		var trade WsTrade
		err = trade.UnmarshalJSON(match)
		if err != nil {
			return fmt.Errorf("unmarshal err: %s", err)
		}
		var side gateway.Side
		if trade.Side == "buy" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		event := gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				Timestamp: trade.Timestamp,
				Symbol:    matches.Arg.InstId,
				Direction: side,
				Price:     trade.Price,
				Amount:    trade.Amount,
			},
		}

		eventLog = append(eventLog, event)

	}
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

type WsTrade struct {
	Timestamp time.Time
	Price     float64
	Amount    float64
	Side      string
}

func (w *WsTrade) UnmarshalJSON(b []byte) error {
	var data [4]interface{}
	if err := json.Unmarshal(b, &data); err != nil {
		return err
	}

	price, err := priceArrayParseFloat(data[1])
	if err != nil {
		return err
	}
	amount, err := priceArrayParseFloat(data[2])
	if err != nil {
		return err
	}
	timestampStr := data[0].(string)
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return fmt.Errorf("failed to parse timestamp \"%s\" to int: %s", timestampStr, err)
	}
	w.Timestamp = gateway.ParseTimestamp(timestamp)
	w.Price = price
	w.Amount = amount
	w.Side = data[3].(string)

	return nil
}

func priceArrayParseFloat(data interface{}) (float64, error) {
	switch v := data.(type) {
	case string:
		f, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return 0, err
		}
		return f, nil
	case int64:
		return float64(v), nil
	case float64:
		return v, nil
	}

	return 0, errors.New(fmt.Sprintf("Failed to type cast %+v", data))
}

func websocketPinger(ws *utils.WsClient, pongCh chan bool) {
	for {
		select {
		case <-pongCh:
			// Reset ping timer
		case <-time.After(30 * time.Second):
			err := ws.WriteMessage([]byte("ping"))
			if err != nil {
				panic(fmt.Errorf("%s ws failed to send ping, err: %s", Exchange.Name, err))
			}

			// Check for pong response
			select {
			case <-pongCh:
			case <-time.After(3 * time.Second):
				panic(fmt.Errorf("%s pong not received after 3 seconds...", Exchange.Name))
			}
		}
	}
}
