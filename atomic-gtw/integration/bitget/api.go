package bitget

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseProd       = "https://api.bitget.com"
	apiSymbols        = "/api/spot/v1/public/products"
	apiAccountBalance = "/api/spot/v1/account/assets"
	apiOpenOrders     = "/api/spot/v1/trade/open-orders"
	apiPlaceOrder     = "/api/spot/v1/trade/orders"
	apiCancelOrder    = "/api/spot/v1/trade/cancel-order-v2"
	apiGetServerTime  = "/api/spot/v1/public/time"
	apiDepthBook      = "/api/v2/spot/market/orderbook?symbol=%s&type=step0&limit=%d"
)

type API struct {
	options       gateway.Options
	httpClient    *utils.HttpClient
	baseURL       string
	lastNonceUsed int64
	lock          sync.Mutex
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

func (api *API) getTimestamp() string {
	timestamp := time.Now().UnixMilli()
	tp := strconv.Itoa(int(timestamp))
	return tp
}

type APIResponse struct {
	Code string          `json:"code"`
	Msg  string          `json:"msg"`
	Data json.RawMessage `json:"data"`
}

type SymbolDetails struct {
	Symbol              string  `json:"symbol"`
	SymbolName          string  `json:"symbolName"`
	BaseCoin            string  `json:"baseCoin"`
	QuoteCoin           string  `json:"quoteCoin"`
	MinTradeAmount      float64 `json:"minTradeAmount,string"`
	MaxTradeAmount      string  `json:"maxTradeAmount"`
	TakerFeeRate        string  `json:"takerFeeRate"`
	MakerFeeRate        string  `json:"makerFeeRate"`
	PriceScale          float64 `json:"priceScale,string"`
	QuantityScale       float64 `json:"quantityScale,string"`
	QuotePrecision      string  `json:"quotePrecision"`
	Status              string  `json:"status"`
	MinTradeUSDT        string  `json:"minTradeUSDT"`
	BuyLimitPriceRatio  string  `json:"buyLimitPriceRatio"`
	SellLimitPriceRatio string  `json:"sellLimitPriceRatio"`
}

func (a *API) Symbols() (symbols []SymbolDetails, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiSymbols, nil, false)
	if err != nil {
		return symbols, err
	}

	err = a.makeHttpRequest(req, &symbols)
	if err != nil {
		return nil, err
	}

	return symbols, nil
}

func (a *API) GetTimeServer() (timeServer string, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiGetServerTime, nil, false)
	if err != nil {
		return "", err
	}

	err = a.makeHttpRequest(req, &timeServer)
	if err != nil {
		return "", err
	}

	return timeServer, nil
}

type AccountBalance struct {
	CoinId    int     `json:"coinId"`
	CoinName  string  `json:"coinName"`
	Available float64 `json:"available,string"`
	Frozen    float64 `json:"frozen,string"`
	Lock      string  `json:"lock"`
}

func (a *API) AccountBalance() (resp []AccountBalance, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiAccountBalance, bytes.NewReader(nil), true)
	if err != nil {
		return nil, err
	}

	if err = a.makeHttpRequest(req, &resp); err != nil {
		return nil, err
	}

	return resp, nil
}

type OpenOrders struct {
	AccountId        string  `json:"accountId"`
	Symbol           string  `json:"symbol"`
	OrderId          string  `json:"orderId"`
	ClientOrderId    string  `json:"clientOrderId"`
	Price            float64 `json:"price,string"`
	Quantity         float64 `json:"quantity,string"`
	OrderType        string  `json:"orderType"`
	Side             string  `json:"side"`
	Status           string  `json:"status"`
	FillPrice        string  `json:"fillPrice"`
	FillQuantity     string  `json:"fillQuantity"`
	FillTotalAmount  string  `json:"fillTotalAmount"`
	EnterPointSource string  `json:"enterPointSource"`
	CTime            string  `json:"cTime"`
}

func (a *API) OpenOrders(data []byte) (resp []OpenOrders, err error) {
	req, err := a.newHttpRequest(http.MethodPost, apiOpenOrders, bytes.NewReader(data), true)
	if err != nil {
		return nil, err
	}

	var apiOrders []OpenOrders
	if err = a.makeHttpRequest(req, &apiOrders); err != nil {
		return nil, err
	}

	return apiOrders, nil
}

type ApiPlaceOrder struct {
	OrderId       string `json:"orderId"`
	ClientOrderId string `json:"clientOrderId"`
}

func (a *API) PlaceOrder(order map[string]string) (orderID string, err error) {
	data, err := json.Marshal(order)
	if err != nil {
		return "", err
	}
	req, err := a.newHttpRequest(http.MethodPost, apiPlaceOrder, bytes.NewReader(data), true)
	if err != nil {
		return "", err
	}
	var resp ApiPlaceOrder

	if err = a.makeHttpRequest(req, &resp); err != nil {
		return "", err
	}

	orderID = resp.OrderId

	return orderID, nil
}

func (a *API) CancelOrder(orderID string, symbol string) (err error) {
	data := map[string]string{
		"symbol":  symbol,
		"orderId": orderID,
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	req, err := a.newHttpRequest(http.MethodPost, apiCancelOrder, bytes.NewReader(jsonData), true)
	if err != nil {
		return err
	}

	if err = a.makeHttpRequest(req, nil); err != nil {
		return err
	}

	return nil
}

type DepthBook struct {
	Asks [][]string `json:"asks"`
	Bids [][]string `json:"bids"`
}

func (a *API) DepthBook(symbol string, limit int) (depthBook DepthBook, err error) {
	url := fmt.Sprintf(apiDepthBook, symbol, limit)
	req, err := a.newHttpRequest(http.MethodGet, url, nil, false)
	if err != nil {
		return depthBook, err
	}

	err = a.makeHttpRequest(req, &depthBook)
	if err != nil {
		return depthBook, err
	}

	return depthBook, nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	var apiRes APIResponse
	err = json.Unmarshal(body, &apiRes)
	if err != nil {
		return fmt.Errorf("unmarshal err [%s] body:\n%s", err, string(body))
	}

	if apiRes.Msg != "success" {
		return fmt.Errorf("api error: %s", apiRes.Msg)
	}

	if responseObject != nil {
		err = json.Unmarshal(apiRes.Data, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return nil
}

func (a *API) getSignature(requestPath string, timestamp string, method string, body string) string {
	message := timestamp + method + requestPath + body
	key := []byte(a.options.ApiSecret)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(message))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return signature
}

func (a *API) getWebsocketSignature(timestamp string) string {
	method := "GET"
	requestPath := "/user/verify"
	message := timestamp + method + requestPath
	key := []byte(a.options.ApiSecret)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(message))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return signature
}

func (a *API) newHttpRequest(method string, path string, data io.Reader, signed bool) (*http.Request, error) {
	urlRequest := a.baseURL + path
	req, err := http.NewRequest(method, urlRequest, data)
	if err != nil {
		return nil, err
	}

	if signed {
		timestamp := a.getTimestamp()
		body, err := ioutil.ReadAll(req.Body)
		if err != nil {
			return nil, err
		}

		// Put back the body into the request
		req.Body = ioutil.NopCloser(bytes.NewReader(body))

		signature := a.getSignature(path, timestamp, method, string(body))
		req.Header.Set("ACCESS-KEY", a.options.ApiKey)
		req.Header.Set("ACCESS-SIGN", signature)
		req.Header.Set("ACCESS-TIMESTAMP", timestamp)
		req.Header.Set("ACCESS-PASSPHRASE", a.options.ApiPassphrase)
		req.Header.Set("Content-Type", "application/json")
	}

	return req, nil
}
