package bitget

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api        *API
	options    gateway.Options
	tickCh     chan gateway.Tick
	baseUrlWs  string
	symbolsMap map[string]SymbolDetails
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick, baseUrlWs string, symbolsMap map[string]SymbolDetails) *AccountGateway {
	return &AccountGateway{
		api:        api,
		options:    options,
		tickCh:     tickCh,
		baseUrlWs:  baseUrlWs,
		symbolsMap: symbolsMap,
	}
}

func (g *AccountGateway) Connect() error {
	return nil
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	accountBalance, err := g.api.AccountBalance()
	if err != nil {
		return balances, err
	}

	balances = make([]gateway.Balance, 0, len(accountBalance))
	for _, balance := range accountBalance {
		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(balance.CoinName),
			Available: balance.Available,
			Total:     balance.Available + balance.Frozen,
		})
	}

	return
}

type RequestOpenOrder struct {
	Symbol string `json:"symbol"`
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	symbol := g.symbolsMap[market.Symbol].Symbol
	request := RequestOpenOrder{
		Symbol: symbol,
	}
	data, err := json.Marshal(request)
	if err != nil {
		return orders, err
	}
	openOrders, err := g.api.OpenOrders(data)
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders))
	for _, order := range openOrders {
		orders = append(orders, mapAPIOrderToCommon(order, market))
	}

	return
}

func mapAPIOrderToCommon(o OpenOrders, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market: market,
		ID:     o.OrderId,
		Side:   mapAPIOrderTypeToCommonSide(o.Side),
		State:  mapAPIOrderStateToCommon(o.Status),
		Amount: o.Quantity,
		Price:  o.Price,
	}
}

func mapAPIOrderTypeToCommonSide(orderType string) gateway.Side {
	if orderType == "buy" {
		return gateway.Bid
	} else if orderType == "sell" {
		return gateway.Ask
	} else {
		log.Printf("bitget invalid order side \"%s\"", orderType)
		return ""
	}
}

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	var side string
	if order.Side == gateway.Bid {
		side = "buy"
	} else {
		side = "sell"
	}
	symbol := g.symbolsMap[order.Market.Symbol].Symbol

	params := map[string]string{
		"symbol":    symbol,
		"side":      side,
		"orderType": "limit",
		"force":     "normal",
		"quantity":  utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		"price":     utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
	}

	if order.PostOnly {
		params["force"] = "post_only"
	}

	orderID, err := g.api.PlaceOrder(params)
	if err != nil {
		return orderId, err
	}

	return orderID, err
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	symbol := g.symbolsMap[order.Market.Symbol].Symbol
	return g.api.CancelOrder(order.ID, symbol)
}

type WsAuthRequest struct {
	Op   string `json:"op"`
	Args []struct {
		ApiKey     string `json:"apiKey"`
		Passphrase string `json:"passphrase"`
		Timestamp  string `json:"timestamp"`
		Sign       string `json:"sign"`
	} `json:"args"`
}

type WsUserOrderRequest struct {
	Op   string   `json:"op"`
	Args []WsArgs `json:"args"`
}

type WsArgs struct {
	Channel  string `json:"channel"`
	InstType string `json:"instType"`
	InstId   string `json:"instId"`
}

func (g *AccountGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()

	err := ws.Connect(g.baseUrlWs)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	timeStampStr, err := g.api.GetTimeServer()
	if err != nil {
		return fmt.Errorf("failed to get time server, err: %s", err)
	}
	timestampInt, _ := strconv.ParseInt(timeStampStr, 10, 64)
	timestampSec := timestampInt / 1000
	tp := strconv.Itoa(int(timestampSec))
	signature := g.api.getWebsocketSignature(tp)

	authRequest := WsAuthRequest{
		Op: "login",
		Args: []struct {
			ApiKey     string `json:"apiKey"`
			Passphrase string `json:"passphrase"`
			Timestamp  string `json:"timestamp"`
			Sign       string `json:"sign"`
		}{
			{
				ApiKey:     g.options.ApiKey,
				Passphrase: g.options.ApiPassphrase,
				Timestamp:  tp,
				Sign:       signature,
			},
		},
	}
	dataAuthRequest, err := json.Marshal(authRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}
	if err = ws.WriteMessage(dataAuthRequest); err != nil {
		return fmt.Errorf("failed write account msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	authErr := make(chan error)
	go func() {
		msg := <-ch

		var err error
		var res WsAuthResponse
		if unmarshalErr := json.Unmarshal(msg, &res); err != nil {
			err = fmt.Errorf("failed to unmarshal account response, err: %s", unmarshalErr)
		}

		if res.Event == "error" {
			err = fmt.Errorf("failed to subscribe to account, err: %s", res.Msg)
		}

		authErr <- err
	}()

	select {
	case err := <-authErr:
		if err != nil {
			closeErr := ws.Close()
			if closeErr != nil {
				log.Printf("Failed to close ws connection after auth err: %s, authErr: %s", closeErr, err)
			}

			return fmt.Errorf("auth err: %s", err)
		}
	case <-time.After(5 * time.Second):
		err := ws.Close()
		if err != nil {
			log.Printf("Failed to close ws connection after timeout: %s", err)
		}

		return fmt.Errorf("Timed out waiting for auth response")
	}

	var args []WsArgs
	for _, market := range markets {
		symbol := g.symbolsMap[market.Symbol].Symbol

		args = append(args, WsArgs{
			Channel:  "orders",
			InstType: "spbl",
			InstId:   symbol,
		})
	}
	orderRequest := WsUserOrderRequest{
		Op:   "subscribe",
		Args: args,
	}
	dataOrderRequest, err := json.Marshal(orderRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}
	if err := ws.WriteMessage(dataOrderRequest); err != nil {
		return fmt.Errorf("failed write order msg to ws: %s", err)
	}
	pongCh := make(chan bool)

	go g.messageHandler(ch, pongCh)
	go websocketPinger(ws, pongCh)

	return nil
}

type WsAuthResponse struct {
	Event string `json:"event"`
	Msg   string `json:"msg"`
	Arg   struct {
		Channel  string `json:"channel"`
		InstType string `json:"instType"`
		InstId   string `json:"instId"`
		Uid      string `json:"uid"`
	} `json:"arg"`
	Data json.RawMessage `json:"data"`
}

func (g *AccountGateway) messageHandler(ch chan []byte, pongCh chan bool) {
	for data := range ch {
		if string(data) == "pong" {
			pongCh <- true
			continue
		}
		var msg WsAuthResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		if msg.Event == "error" {
			log.Printf("Failed to login to WS [%s] err [%s]", g.baseUrlWs, string(data))
			continue
		}

		switch msg.Arg.Channel {
		case "error":
			log.Printf("WS [%s] msg w/ error msg: %s", g.baseUrlWs, string(data))
			continue
		case "orders":
			if err := g.processTradeUpdates(msg.Data); err != nil {
				log.Printf("%s error processing matches \"%s\": %s", g.baseUrlWs, data, err)
			}
		default:
			log.Printf("BitGet unprocessable message type [%s], data [%s]", msg.Data, string(data))
		}
	}
}

type WsUserOrder struct {
	Side      string  `json:"side"`
	Sz        float64 `json:"sz,string"`
	Px        float64 `json:"px,string"`
	CTime     int64   `json:"cTime"`
	OrdId     string  `json:"ordId"`
	InstId    string  `json:"instId"`
	ClOrdId   string  `json:"clOrdId"`
	AccFillSz float64 `json:"accFillSz,string"`
	AvgPx     float64 `json:"avgPx,string"`
	Force     string  `json:"force"`
	UTime     int64   `json:"uTime"`
	OrdType   string  `json:"ordType"`
	Status    string  `json:"status"`
}

func (g *AccountGateway) processTradeUpdates(data []byte) error {
	if data == nil {
		return nil
	}
	var matches []WsUserOrder
	err := json.Unmarshal(data, &matches)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}

	eventLog := make([]gateway.Event, 0, 1)

	for _, order := range matches {
		status := mapWsOrderStatus(order.Status)

		var side gateway.Side
		if order.Side == "buy" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		event := gateway.Event{
			Type: gateway.OrderUpdateEvent,
			Data: gateway.Order{
				ID:            order.OrdId,
				Side:          side,
				Price:         order.Px,
				Amount:        order.Sz,
				FilledAmount:  order.AccFillSz,
				ClientOrderID: order.ClOrdId,
				State:         status,
				AvgPrice:      order.AvgPx,
			},
		}

		eventLog = append(eventLog, event)

	}
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

func mapWsOrderStatus(status string) gateway.OrderState {
	switch status {
	case "init":
		return gateway.OrderOpen
	case "new":
		return gateway.OrderOpen
	case "partial-fill":
		return gateway.OrderPartiallyFilled
	case "full-fill":
		return gateway.OrderFullyFilled
	case "cancelled":
		return gateway.OrderCancelled
	}

	return gateway.OrderUnknown
}
