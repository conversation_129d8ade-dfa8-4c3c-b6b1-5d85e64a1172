package binancefutures

import (
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/integration/binance"
)

var Exchange = gateway.Exchange{
	Name: "BinanceFutures",
}

const apiFutures = "https://fapi.binance.com"

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *binance.MarketDataGateway
	tickCh            chan gateway.Tick
	api               *binance.API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     binance.NewAPI(options, apiFutures, apiFutures),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	// Init market data
	g.marketDataGateway = binance.NewMarketDataGateway(g.options, g.api, true, g.tickCh)
	if err := g.marketDataGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.FSymbols()
	if err != nil {
		return nil, err
	}

	commonMarkets, err := symbolsToCommonMarket(res)
	if err != nil {
		return nil, err
	}

	return commonMarkets, nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
