package binancefutures

import (
	"fmt"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/integration/binance"
)

func symbolsToCommonMarket(symbols []binance.APISymbol) ([]gateway.Market, error) {
	commonMarkets := make([]gateway.Market, 0, len(symbols))

	for _, symbol := range symbols {
		if symbol.Status == "TRADING" {
			market, err := symbolToCommonMarket(symbol)
			if err != nil {
				return []gateway.Market{}, err
			}

			commonMarkets = append(commonMarkets, market)
		}
	}

	return commonMarkets, nil
}

func symbolToCommonMarket(symbol binance.APISymbol) (gateway.Market, error) {
	var (
		priceTick         float64
		amountTick        float64
		minimumOrderSize  float64
		minimumOrderValue float64
	)

	for _, filter := range symbol.Filters {
		switch filter.FilterType {
		case "PRICE_FILTER":
			priceTick = filter.TickSize
		case "LOT_SIZE":
			minimumOrderSize = filter.MinQty
			amountTick = filter.StepSize
		case "MIN_NOTIONAL":
			minimumOrderValue = filter.MinNotional
		}
	}

	switch {
	case priceTick == 0:
		return gateway.Market{}, fmt.Errorf("unable to find symbol %s priceTick", symbol.Symbol)
	case amountTick == 0:
		return gateway.Market{}, fmt.Errorf("unable to find symbol %s amountTick", symbol.Symbol)
	case minimumOrderSize == 0:
		return gateway.Market{}, fmt.Errorf("unable to find symbol %s minimumOrderSize", symbol.Symbol)
	}

	return gateway.Market{
		Exchange: Exchange,
		Symbol:   symbol.Symbol,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(symbol.BaseAsset),
			Quote: strings.ToUpper(symbol.QuoteAsset),
		},
		TakerFee:               0.0004,
		MakerFee:               0.0002,
		PriceTick:              priceTick,
		AmountTick:             amountTick,
		MinimumOrderSize:       minimumOrderSize,
		MinimumOrderMoneyValue: minimumOrderValue,
		MarketType:             gateway.FuturesMarket,
	}, nil
}
