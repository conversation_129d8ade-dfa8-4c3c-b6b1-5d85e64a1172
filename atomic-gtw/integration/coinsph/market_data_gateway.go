package coinsph

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	tickCh chan gateway.Tick
}

func NewMarketDataGateway(tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		tickCh: tickCh,
	}
}

type WsDepthResponse struct {
	EventTime    int64                `json:"E"`
	EventType    string               `json:"e"`
	Symbol       string               `json:"s"`
	LastUpdateID int64                `json:"lastUpdateId"`
	Bids         []gateway.PriceArray `json:"bids"`
	Asks         []gateway.PriceArray `json:"asks"`
}

func (mg *MarketDataGateway) Connect() error {
	return nil
}

func (mg *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	log.Printf("%s subscribing to %d markets...", Exchange.Name, len(markets))
	ws := utils.NewWsClient()

	for _, market := range markets {
		symbol := strings.ToLower(market.Pair.Base + market.Pair.Quote)
		wsUrl := fmt.Sprintf("wss://wsapi.pro.coins.ph/openapi/quote/ws/v3/%s@depth5", symbol)

		err := ws.Connect(wsUrl)
		if err != nil {
			return err
		}

		ch := make(chan []byte, 100)
		pongCh := make(chan bool)

		ws.SubscribeMessages(ch)
		go mg.messageHandler(ch)
		go websocketPinger(ws, pongCh)
	}

	return nil
}

func (mg *MarketDataGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		var resp WsDepthResponse
		if err := json.Unmarshal(data, &resp); err != nil {
			log.Printf("Coinsph market data unmarshal err: %s", err)
			continue
		}
		if resp.EventType == "depth" {
			mg.processDepthEvent(resp.Symbol, resp.Bids, resp.Asks, false)
		}
	}
}

func (mg *MarketDataGateway) processDepthEvent(symbol string, bids []gateway.PriceArray, asks []gateway.PriceArray, snapshot bool) {
	eventLog := make([]gateway.Event, 0, len(bids)+len(asks)+1)
	appendEvents := func(symbol string, side gateway.Side, prices []gateway.PriceArray) {
		for _, order := range prices {
			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  order.Price,
					Amount: order.Amount,
				},
			}
			eventLog = append(eventLog, event)
		}
	}

	if snapshot {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: symbol,
			},
		})
	}

	appendEvents(symbol, gateway.Ask, asks)
	appendEvents(symbol, gateway.Bid, bids)

	mg.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

func websocketPinger(ws *utils.WsClient, pongCh chan bool) {
	for {
		select {
		case <-pongCh:
			// Reset ping timer
		case <-time.After(5 * time.Minute):
			err := ws.WriteMessage([]byte("ping"))
			if err != nil {
				panic(fmt.Errorf("%s ws failed to send ping, err: %s", Exchange.Name, err))
			}
		}

		// Check for pong response
		select {
		case <-pongCh:
		case <-time.After(5 * time.Minute):
			panic(fmt.Errorf("%s pong not received after 5 minutes...", Exchange.Name))
		}
	}
}
