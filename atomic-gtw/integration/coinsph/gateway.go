package coinsph

import (
	"fmt"

	"strconv"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Coinsph",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	api               *API
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	g.marketDataGateway = NewMarketDataGateway(g.tickCh)
	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	if err := g.marketDataGateway.SubscribeMarkets(markets); err != nil {
		return fmt.Errorf("Failed to subscribe to market data, err %s", err)
	}

	return nil
}

func (g *Gateway) Close() error {
	return gateway.NotImplementedErr
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.GetSymbols()
	if err != nil {
		panic(err)
	}

	commonMarkets := make([]gateway.Market, 0)
	for _, symbol := range symbols {
		market := symbolToCommonMarket(symbol)
		commonMarkets = append(commonMarkets, market)
	}

	return commonMarkets, nil
}

func symbolToCommonMarket(symbol Symbols) gateway.Market {
	var priceTick float64
	var amountTick float64

	for _, filter := range symbol.Filters {
		if filter.FilterType == "PRICE_FILTER" {
			tickSize, err := strconv.ParseFloat(filter.TickSize, 64)

			if err != nil {
				panic(fmt.Errorf("Failed to parse tick size %s", filter.TickSize))
			}
			priceTick = tickSize
		} else if filter.FilterType == "LOT_SIZE" {
			stepSize, err := strconv.ParseFloat(filter.StepSize, 64)

			if err != nil {
				panic(fmt.Errorf("Failed to parse step size %s", filter.StepSize))
			}
			amountTick = stepSize
		}
	}

	return gateway.Market{
		Exchange: Exchange,
		Symbol:   symbol.Symbol,
		Pair: gateway.Pair{
			Base:  symbol.BaseAsset,
			Quote: symbol.QuoteAsset,
		},
		TakerFee:   0.001,
		MakerFee:   0.001,
		PriceTick:  priceTick,
		AmountTick: amountTick,
		MarketType: gateway.SpotMarket,
	}
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func parsePriceLevelsToDepth(levels [][]string) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel

	for _, level := range levels {
		if len(level) < 2 {
			return nil, fmt.Errorf("invalid price level: %v", level)
		}
		price, err := strconv.ParseFloat(level[0], 64)
		if err != nil {
			return nil, err
		}
		amount, err := strconv.ParseFloat(level[1], 64)
		if err != nil {
			return nil, err
		}
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		})
	}

	return priceLevels, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks, err := parsePriceLevelsToDepth(depth.Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	bids, err := parsePriceLevelsToDepth(depth.Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
