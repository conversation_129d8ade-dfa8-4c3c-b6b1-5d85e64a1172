package coinsph

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseProd = "https://api.pro.coins.ph"

	apiExchangeInfo   = "/openapi/v1/exchangeInfo"
	accountInfo       = "/openapi/v1/account"
	openOrders        = "/openapi/v1/openOrders"
	sendOrder         = "/openapi/v1/order"
	cancelOrder       = "/openapi/v1/order"
	apiUserDataStream = "/openapi/v1/userDataStream"
	apiDepthBook      = "/openapi/quote/v1/depth?symbol=%s&limit=%d"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
	baseURL    string
}

func NewAPI(options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options:    options,
		httpClient: client,
		baseURL:    apiBaseProd,
	}
}

type Filters struct {
	FilterType string `json:"filterType"`
	MinPrice   string `json:"minPrice"`
	MaxPrice   string `json:"maxPrice"`
	TickSize   string `json:"tickSize"`
	StepSize   string `json:"stepSize"`
}

type Symbols struct {
	Symbol              string    `json:"symbol"`
	Status              string    `json:"status"`
	BaseAsset           string    `json:"baseAsset"`
	BaseAssetPrecision  int       `json:"baseAssetPrecision"`
	QuoteAsset          string    `json:"quoteAsset"`
	QuoteAssetPrecision int       `json:"quoteAssetPrecision"`
	Filters             []Filters `json:"filters"`
}

type APISymbols struct {
	Symbols []Symbols `json:"symbols"`
}

func (a *API) GetSymbols() ([]Symbols, error) {
	req, err := a.newHttpRequest(http.MethodGet, apiExchangeInfo, nil, false, false)
	if err != nil {
		return nil, err
	}

	var resp APISymbols

	if err = a.makeHttpRequest(req, &resp); err != nil {
		return nil, err
	}

	return resp.Symbols, nil
}

type Balance struct {
	Asset  string  `json:"asset"`
	Free   float64 `json:"free,string"`
	Locked float64 `json:"locked,string"`
}

type AccountInfo struct {
	CanTrade    bool      `json:"canTrade"`
	CanWithdraw bool      `json:"canWithdraw"`
	CanDeposit  bool      `json:"canDeposit"`
	UpdateTime  int64     `json:"updateTime"`
	AccountType string    `json:"accountType"`
	Balances    []Balance `json:"balances"`
}

func (a *API) GetAccountInfo() (*AccountInfo, error) {
	req, err := a.newHttpRequest(http.MethodGet, accountInfo, nil, true, false)
	if err != nil {
		return nil, err
	}

	var resp AccountInfo

	if err = a.makeHttpRequest(req, &resp); err != nil {
		return nil, err
	}

	return &resp, nil
}

type Order struct {
	Symbol              string  `json:"symbol"`
	OrderID             int64   `json:"orderId"`
	ClientOrderID       string  `json:"clientOrderId"`
	Price               float64 `json:"price,string"`
	OrigQty             float64 `json:"origQty,string"`
	ExecutedQty         string  `json:"executedQty"`
	CummulativeQuoteQty string  `json:"cummulativeQuoteQty"`
	Status              string  `json:"status"`
	TimeInForce         string  `json:"timeInForce"`
	Type                string  `json:"type"`
	Side                string  `json:"side"`
	StopPrice           string  `json:"stopPrice"`
	Time                int64   `json:"time"`
	UpdateTime          int64   `json:"updateTime"`
	IsWorking           bool    `json:"isWorking"`
	OrigQuoteOrderQty   string  `json:"origQuoteOrderQty"`
}

func (a *API) GetOpenOrders() ([]Order, error) {
	req, err := a.newHttpRequest(http.MethodGet, openOrders, nil, true, false)
	if err != nil {
		return nil, err
	}

	var resp []Order

	if err = a.makeHttpRequest(req, &resp); err != nil {
		return nil, err
	}

	return resp, nil
}

type Response struct {
	Symbol              string `json:"symbol"`
	OrderID             int64  `json:"orderId"`
	ClientOrderID       string `json:"clientOrderId"`
	TransactTime        int64  `json:"transactTime"`
	Price               string `json:"price"`
	OrigQty             string `json:"origQty"`
	ExecutedQty         string `json:"executedQty"`
	CummulativeQuoteQty string `json:"cummulativeQuoteQty"`
	Status              string `json:"status"`
	TimeInForce         string `json:"timeInForce"`
	Type                string `json:"type"`
	Side                string `json:"side"`
	StopPrice           string `json:"stopPrice"`
	OrigQuoteOrderQty   string `json:"origQuoteOrderQty"`
}

func (a *API) PlaceOrder(order APIOrderReq, side string, currency string, orderType string, timeInForce string) (orderID string, err error) {
	params := url.Values{
		"price":       {order.Price},
		"quantity":    {order.Amount},
		"symbol":      {currency},
		"side":        {side},
		"type":        {orderType},
		"timeInForce": {timeInForce},
	}

	req, err := a.newHttpRequest(http.MethodPost, sendOrder, &params, true, false)
	if err != nil {
		return "", err
	}

	var apiOrder Response
	err = a.makeHttpRequest(req, &apiOrder)
	if err != nil {
		err = fmt.Errorf("failed to place order, err: %s", err)
		return "", err
	}

	return strconv.FormatInt(apiOrder.OrderID, 10), nil
}

func (a *API) CancelOrder(orderID string) (err error) {
	params := url.Values{}
	params.Set("orderId", orderID)
	req, err := a.newHttpRequest(http.MethodDelete, cancelOrder, &params, true, false)
	if err != nil {
		return err
	}

	err = a.makeHttpRequest(req, nil)
	if err != nil {
		err = fmt.Errorf("failed to cancel order, orderID: %s, err: %s", orderID, err)
		return err
	}
	return nil
}

type APIDepthBook struct {
	Asks [][]string `json:"asks"`
	Bids [][]string `json:"bids"`
}

func (a *API) DepthBook(symbol string, params gateway.GetDepthParams) (depth APIDepthBook, err error) {
	if params.Limit == 0 {
		params.Limit = 100
	}
	url := fmt.Sprintf(apiDepthBook, symbol, params.Limit)
	req, err := a.newHttpRequest(http.MethodGet, url, nil, false, false)
	if err != nil {
		return depth, err
	}

	err = a.makeHttpRequest(req, &depth)
	if err != nil {
		return depth, err
	}

	return depth, nil
}

func (a *API) NewStartUserStreamService() (string, error) {
	req, err := a.newHttpRequest(http.MethodPost, apiUserDataStream, nil, false, true)
	if err != nil {
		return "", err
	}

	var res struct {
		ListenKey string `json:"listenKey"`
	}
	if err = a.makeHttpRequest(req, &res); err != nil {
		return "", err
	}

	return res.ListenKey, nil
}

func (a *API) KeepaliveUserStreamService(listenKey string) error {
	params := url.Values{}
	params.Set("listenKey", listenKey)

	req, err := a.newHttpRequest(http.MethodPut, apiUserDataStream, &params, false, true)
	if err != nil {
		return err
	}

	if err = a.makeHttpRequest(req, struct{}{}); err != nil {
		return err
	}

	return nil
}

func (a *API) newHttpRequest(method string, path string, params *url.Values, signed bool, apiKeyOnly bool) (*http.Request, error) {

	if params == nil {
		params = &url.Values{}
	}

	timestamp := time.Now().UnixMilli()
	params.Add("timestamp", strconv.FormatInt(timestamp, 10))
	params.Add("recvWindow", "25000")

	var reqBodyString string

	if params != nil {
		reqBodyString = params.Encode()
	}

	urlRequest := apiBaseProd + path

	req, err := http.NewRequest(method, urlRequest, strings.NewReader(reqBodyString))
	if err != nil {
		return nil, err
	}

	if signed && !apiKeyOnly {
		signature := generateSignature(a.options.ApiSecret, reqBodyString)
		params.Add("signature", signature)
		req.URL.RawQuery = params.Encode()
		req.Header.Set("X-COINS-APIKEY", a.options.ApiKey)
	}

	if apiKeyOnly {
		req.Header.Set("X-COINS-APIKEY", a.options.ApiKey)
	}

	return req, nil
}

func generateSignature(secretKey, data string) string {
	key := []byte(secretKey)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(data))
	signature := hex.EncodeToString(h.Sum(nil))
	return signature
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)

	if err != nil {
		return err
	}

	var response struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}

	err = json.Unmarshal(body, &response)

	if err != nil {
		return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
	}

	if response.Code < 0 {
		return fmt.Errorf("failed to place order, err: %s", response.Msg)
	}

	if responseObject != nil {
		err = json.Unmarshal(body, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return nil
}
