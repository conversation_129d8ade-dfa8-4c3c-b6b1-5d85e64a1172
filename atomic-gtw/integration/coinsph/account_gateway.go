package coinsph

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api     *API
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		api:     api,
		options: options,
		tickCh:  tickCh,
	}
}

type UserStreamGenericUpdate struct {
	EventType string `json:"e"`
	EventTime int64  `json:"E"`
}

type UserStreamOrderUpdate struct {
	EventType                 string  `json:"e"`
	EventTime                 int64   `json:"E"`
	Symbol                    string  `json:"s"`        // Symbol
	ClientOrderID             string  `json:"c"`        // Client order ID
	Side                      string  `json:"S"`        // Side
	Type                      string  `json:"o"`        // Order type
	TimeInForce               string  `json:"f"`        // Time in force
	Quantity                  float64 `json:"q,string"` // Order quantity
	Price                     float64 `json:"p,string"` // Order price
	StopPrice                 float64 `json:"P,string"` // Stop price
	IcebergQuantity           float64 `json:"F,string"` // Iceberg quantity
	OriginalClientOrderID     string  `json:"C"`        // Original client order ID; This is the ID of the order being canceled
	CurrentExecutionType      string  `json:"x"`        // Current execution type
	CurrentOrderStatus        string  `json:"X"`        // Current order status
	OrderRejectReason         string  `json:"r"`        // Order reject reason; will be an error code.
	OrderID                   int64   `json:"i"`        // Order ID
	LastExecutedQuantity      float64 `json:"l,string"` // Last executed quantity
	CumulativeFilledQuantity  float64 `json:"z,string"` // Cumulative filled quantity
	LastExecutedPrice         float64 `json:"L,string"` // Last executed price
	CommissionAmount          float64 `json:"n,string"` // Commission amount
	CommissionAsset           string  `json:"N"`        // Commission asset
	TransactionTime           int64   `json:"T"`        // Transaction time
	TradeID                   int64   `json:"t"`        // Trade ID
	Maker                     bool    `json:"m"`        // Is this trade the maker side?
	CreationTime              int64   `json:"O"`        // Order creation time
	CumulativeFilledQuote     float64 `json:"Z,string"` // Cumulative quote asset transacted quantity
	LastQuoteExecutedQuantity float64 `json:"Y,string"` // Last quote asset transacted quantity (i.e. lastPrice * lastQty)
	QuoteOrderQuantity        float64 `json:"Q,string"`
}

func (g *AccountGateway) Connect() error {
	// Start user data stream, to receive account updates
	listenKey, err := g.api.NewStartUserStreamService()
	if err != nil {
		return err
	}
	endpoint := fmt.Sprintf("%s/%s", "wss://wsapi.pro.coins.ph/openapi/ws", listenKey)
	ws := utils.NewWsClient()
	err = ws.Connect(endpoint)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)

	go func(ch chan []byte) {
		for msg := range ch {
			var genericUpdate UserStreamGenericUpdate
			err = json.Unmarshal(msg, &genericUpdate)
			if err != nil {
				log.Printf("failed to unmarshal Coinsph user stream update message error %s", err)
			}

			if genericUpdate.EventType == "executionReport" {
				var orderUpdate UserStreamOrderUpdate
				err = json.Unmarshal(msg, &orderUpdate)
				if err != nil {
					log.Printf("failed to unmarshal Coinsph user stream order update error %s", err)
				}
				g.processOrderUpdate(orderUpdate)
			}
		}
	}(ch)

	go func() {
		// Keepalive a user data stream to prevent a time-out. User data streams will close after 60 minutes.
		// It's recommended to send a ping about every 30 minutes.
		ticker := time.NewTicker(30 * time.Minute)
		defer ticker.Stop()

		for range ticker.C {
			err := g.api.KeepaliveUserStreamService(listenKey)
			if err != nil {
				log.Printf("failed to send keepalive request to coinsph user data stream, error %s", err)
				log.Printf("retrying keepalive request in 1 minute...")
				time.Sleep(1 * time.Minute)
				err = g.api.KeepaliveUserStreamService(listenKey)
				if err != nil {
					err = fmt.Errorf("failed when RETRYING keepalive request to coinsph user data stream, error %s", err)
					panic(err)
				}
			}
		}

	}()

	return nil
}

func (g *AccountGateway) processOrderUpdate(order UserStreamOrderUpdate) {
	eventLog := make([]gateway.Event, 0, 1)
	status := mapWsOrderStatus(order.CurrentOrderStatus)
	var side gateway.Side
	if order.Side == "BUY" {
		side = gateway.Bid
	} else {
		side = gateway.Ask
	}
	orderID := strconv.FormatInt(order.OrderID, 10)
	event := gateway.Event{
		Type: gateway.OrderUpdateEvent,
		Data: gateway.Order{
			ID:            orderID,
			Side:          side,
			Price:         order.Price,
			Amount:        order.Quantity,
			FilledAmount:  order.CumulativeFilledQuantity,
			ClientOrderID: order.ClientOrderID,
			State:         status,
		},
	}
	eventLog = append(eventLog, event)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

func mapWsOrderStatus(status string) gateway.OrderState {
	switch status {
	case "INIT":
		return gateway.OrderOpen
	case "NEW":
		return gateway.OrderOpen
	case "TRADE":
		return gateway.OrderPartiallyFilled
	case "FILLED":
		return gateway.OrderFullyFilled
	case "CANCELED":
		return gateway.OrderCancelled
	case "EXPIRED":
		return gateway.OrderCancelled
	default:
		return gateway.OrderUnknown
	}
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	accountBalance, err := g.api.GetAccountInfo()
	if err != nil {
		panic(err)
	}

	balances = make([]gateway.Balance, 0, len(accountBalance.Balances))
	for _, balance := range accountBalance.Balances {
		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(balance.Asset),
			Available: balance.Free,
			Total:     balance.Free + balance.Locked,
		})
	}

	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	currentOpenOrders, err := g.api.GetOpenOrders()
	if err != nil {
		return []gateway.Order{}, err
	}

	gtwOrders := make([]gateway.Order, 0, len(currentOpenOrders))
	for _, order := range currentOpenOrders {
		gtwOrders = append(gtwOrders, mapAPIOrderToCommon(order, market))
	}

	return gtwOrders, nil
}

func mapAPIOrderToCommon(o Order, market gateway.Market) gateway.Order {
	filledAmount, err := strconv.ParseFloat(o.ExecutedQty, 64)
	if err != nil {
		log.Printf("Error: %v\n", err)
	}

	return gateway.Order{
		Market:       market,
		ID:           strconv.FormatInt(o.OrderID, 10),
		Side:         mapAPIOrderTypeToCommonSide(o.Side),
		State:        coinsphOrderStatusToCommonStatus(o.Status),
		Amount:       o.OrigQty,
		Price:        o.Price,
		FilledAmount: filledAmount,
	}
}

func coinsphOrderStatusToCommonStatus(status string) gateway.OrderState {
	switch status {
	case "NEW":
		return gateway.OrderOpen
	case "PARTIALLY_FILLED":
		return gateway.OrderPartiallyFilled
	case "FILLED":
		return gateway.OrderFullyFilled
	case "CANCELED":
		return gateway.OrderCancelled
	case "EXPIRED":
		return gateway.OrderCancelled
	default:
		return gateway.OrderUnknown
	}
}

func mapAPIOrderTypeToCommonSide(side string) gateway.Side {
	switch {
	case side == "BUY":
		return gateway.Bid
	case side == "SELL":
		return gateway.Ask
	default:
		log.Printf("Coinsph invalid order side \"%s\"", side)
		return ""
	}
}

type APIOrderReq struct {
	Amount string `json:"amount"`
	Price  string `json:"price"`
}

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	orderReq := APIOrderReq{
		Amount: utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		Price:  utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
	}
	side := ""
	if order.Side == gateway.Bid {
		side = "BUY"
	} else if order.Side == gateway.Ask {
		side = "SELL"
	}

	orderID, err := g.api.PlaceOrder(orderReq, side, order.Market.Symbol, "LIMIT", "GTC")
	if err != nil {
		return orderId, err
	}

	return orderID, err
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID)
}
