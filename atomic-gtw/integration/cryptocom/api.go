package cryptocom

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseProd    = "https://api.crypto.com/exchange/v1"
	apiBaseSandbox = "https://uat-api.3ona.co/exchange/v1"
)

type API struct {
	options       gateway.Options
	httpClient    *utils.HttpClient
	baseURL       string
	lastReqID     int64
	lastNonceUsed int64
	lock          sync.Mutex
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

func (api *API) getNonce() int64 {
	api.lock.Lock()
	defer api.lock.Unlock()

	nonce := time.Now().UnixMilli()
	if nonce <= api.lastNonceUsed {
		nonce = api.lastNonceUsed + 1
	}

	api.lastNonceUsed = nonce
	return api.lastNonceUsed
}

func (api *API) getReqID() int64 {
	api.lock.Lock()
	defer api.lock.Unlock()

	api.lastReqID += 1
	return api.lastReqID
}

type APIResponse struct {
	ID            int64           `json:"id"`
	Method        string          `json:"method"`
	Code          int             `json:"code"`
	Message       string          `json:"message"`
	Result        json.RawMessage `json:"result"`
	Original      string          `json:"original"`
	DetailCode    int             `json:"detail_code"`
	DetailMessage string          `json:"detail_message"`
}

type APISymbol struct {
	Symbol           string  `json:"symbol"`
	InstType         string  `json:"inst_type"`
	QuoteCurrency    string  `json:"quote_ccy"`
	BaseCurrency     string  `json:"base_ccy"`
	QuantityTickSize float64 `json:"qty_tick_size,string"`
	PriceTickSize    float64 `json:"price_tick_size,string"`
	Tradable         bool    `json:"tradable"`
	UnderlyingSymbol string  `json:"underlying_symbol"`
}

type APISymbolResData struct {
	Instruments []APISymbol `json:"data"`
}

func (a *API) Symbols() (symbols []APISymbol, err error) {
	uri := fmt.Sprintf("%s/public/get-instruments", a.baseURL)
	req, err := http.NewRequest(http.MethodGet, uri, nil)
	if err != nil {
		return nil, err
	}

	var symbolResData APISymbolResData
	err = a.makePublicHttpRequest(req, &symbolResData)
	if err != nil {
		return symbols, err
	}

	return symbolResData.Instruments, nil
}

type APIUserBalancePosition struct {
	InstrumentName string  `json:"instrument_name"`
	Quantity       float64 `json:"quantity,string"`
	ReservedQty    float64 `json:"reserved_qty,string"`
}

type APIUserBalanceData struct {
	PositionBalances []APIUserBalancePosition `json:"position_balances"`
}

type APIUserBalanceRes struct {
	Data []APIUserBalanceData `json:"data"`
}

func (a *API) UserBalance() (res APIUserBalanceRes, err error) {
	req, err := a.newMethodRequest(
		"private/user-balance", // Updated method name
		nil,                    // No parameters needed
	)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APIOrdersRes struct {
	OrderList []APIOrder `json:"data"` // Changed from "order_list" to "data"
}

// Updated APIOrder based on v1 guide structure
type APIOrder struct {
	Status             string   `json:"status"`
	Side               string   `json:"side"`
	Price              float64  `json:"limit_price,string"`
	Quantity           float64  `json:"quantity,string"`
	OrderId            string   `json:"order_id"`
	ClientOid          string   `json:"client_oid"`
	CreateTime         int64    `json:"create_time"`
	Type               string   `json:"order_type"`
	InstrumentName     string   `json:"instrument_name"`
	CumulativeQuantity float64  `json:"cumulative_quantity,string"`
	CumulativeValue    float64  `json:"cumulative_value,string"`
	AvgPrice           float64  `json:"avg_price,string"`
	FeeCurrency        string   `json:"fee_instrument_name"`
	TimeInForce        string   `json:"time_in_force"`
	ExecInst           []string `json:"exec_inst"`
}

func (a *API) OpenOrders(symbol string) (orders []APIOrder, err error) {
	params := map[string]interface{}{
		"instrument_name": symbol,
	}
	req, err := a.newMethodRequest(
		"private/get-open-orders",
		params,
	)
	if err != nil {
		return orders, err
	}

	var apiOrderRes APIOrdersRes
	err = a.makeHttpRequest(req, &apiOrderRes)
	if err != nil {
		return orders, err
	}

	return apiOrderRes.OrderList, err
}

type APIUpdateOrderRes struct {
	OrderID   string `json:"order_id"`
	ClientOID string `json:"client_oid,omitempty"` // Often included in v1 responses
}

func (a *API) PlaceOrder(params map[string]interface{}) (orderID string, err error) {
	req, err := a.newMethodRequest(
		"private/create-order",
		params,
	)
	if err != nil {
		return orderID, err
	}

	var res APIUpdateOrderRes
	err = a.makeHttpRequest(req, &res)
	return res.OrderID, err
}

func (a *API) CancelOrder(orderID string) (err error) {
	params := map[string]interface{}{
		"order_id": orderID,
	}
	req, err := a.newMethodRequest(
		"private/cancel-order",
		params,
	)
	if err != nil {
		return err
	}

	var res APIUpdateOrderRes
	err = a.makeHttpRequest(req, &res)
	return err
}

type APIDepthBook struct {
	Asks []gateway.PriceArray `json:"asks"` // Price, Quantity (both strings)
	Bids []gateway.PriceArray `json:"bids"` // Price, Quantity (both strings)
	T    int64                `json:"t"`    // Timestamp may be included
}

func (a *API) DepthBook(symbol string, params gateway.GetDepthParams) (depth APIDepthBook, err error) {
	if params.Limit == 0 || params.Limit > 50 {
		params.Limit = 50
	}

	uri := fmt.Sprintf("%s/public/get-book?instrument_name=%s&depth=%d", a.baseURL, symbol, params.Limit)
	req, err := http.NewRequest(http.MethodGet, uri, nil)
	if err != nil {
		return depth, err
	}

	type APIDepthBookResult struct {
		Data []APIDepthBook `json:"data"` // The actual book data is nested
	}
	var depthRes APIDepthBookResult
	err = a.makePublicHttpRequest(req, &depthRes)
	if err != nil {
		return depth, err
	}

	if len(depthRes.Data) > 0 {
		depth = depthRes.Data[0]
	} else {
		return depth, fmt.Errorf("no depth data returned for %s", symbol)
	}

	return depth, err
}

type APIRequest struct {
	ID     int64                  `json:"id"`
	Method string                 `json:"method"`
	APIKey string                 `json:"api_key"`
	Sig    string                 `json:"sig"`
	Params map[string]interface{} `json:"params"` // Changed to interface{}
	Nonce  int64                  `json:"nonce"`
}

func (a *API) newMethodRequest(method string, params map[string]interface{}) (*http.Request, error) {
	apiRequest := APIRequest{
		ID:     a.getReqID(),
		Method: method,
		APIKey: a.options.ApiKey,
		Nonce:  a.getNonce(),
		Params: params,
	}

	if params == nil {
		apiRequest.Params = make(map[string]interface{})
	}

	apiRequest.Sig = signAPIRequest(apiRequest, a.options.ApiSecret)

	data, err := json.Marshal(apiRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal json, err: %s", err)
	}

	uri := fmt.Sprintf("%s/%s", a.baseURL, method)
	req, err := http.NewRequest(http.MethodPost, uri, bytes.NewReader(data))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")

	return req, nil
}

func valueToString(v interface{}) string {
	if v == nil {
		return ""
	}

	val := reflect.ValueOf(v)
	kind := val.Kind()

	switch kind {
	case reflect.String:
		return val.String()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return strconv.FormatInt(val.Int(), 10)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return strconv.FormatUint(val.Uint(), 10)
	case reflect.Float32, reflect.Float64:
		// Use appropriate precision if needed, ensure it matches API expectation
		// 'f', -1, 64 uses the minimum digits necessary
		return strconv.FormatFloat(val.Float(), 'f', -1, 64)
	case reflect.Bool:
		return strconv.FormatBool(val.Bool())
	case reflect.Slice, reflect.Array:
		var sb strings.Builder
		for i := 0; i < val.Len(); i++ {
			sb.WriteString(valueToString(val.Index(i).Interface()))
		}
		return sb.String()
	default:
		// Fallback for other types (structs, maps, pointers, etc.)
		// Note: fmt.Sprintf("%v") might not be stable for complex types,
		// ensure the API specifies how these should be serialized if they occur.
		return fmt.Sprintf("%v", v)
	}
}

func signAPIRequest(apiRequest APIRequest, secretKey string) string {
	paramsStr := ""

	if apiRequest.Params != nil && len(apiRequest.Params) > 0 {
		sortedKeys := make([]string, 0, len(apiRequest.Params))
		for key := range apiRequest.Params {
			sortedKeys = append(sortedKeys, key)
		}
		sort.Strings(sortedKeys)

		var paramBuilder strings.Builder
		for _, key := range sortedKeys {
			val := apiRequest.Params[key]
			valStr := valueToString(val)
			paramBuilder.WriteString(key)
			paramBuilder.WriteString(valStr)
		}
		paramsStr = paramBuilder.String()
	}

	var payloadBuilder strings.Builder
	payloadBuilder.WriteString(apiRequest.Method)
	payloadBuilder.WriteString(strconv.FormatInt(apiRequest.ID, 10))
	payloadBuilder.WriteString(apiRequest.APIKey)
	payloadBuilder.WriteString(paramsStr)
	payloadBuilder.WriteString(strconv.FormatInt(apiRequest.Nonce, 10))
	payload := payloadBuilder.String()

	signer := hmac.New(sha256.New, []byte(secretKey))
	signer.Write([]byte(payload))
	signature := hex.EncodeToString(signer.Sum(nil))

	return signature
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	if res.StatusCode < 200 || res.StatusCode >= 300 {
		return fmt.Errorf("http error: %s, body: %s", res.Status, string(body))
	}

	var apiRes APIResponse
	err = json.Unmarshal(body, &apiRes)
	if err != nil {
		// If base unmarshal fails, maybe it's just the result directly (e.g., simple success)
		// Or maybe the error structure is different. Log the body for debugging.
		// Try unmarshalling directly into responseObject if it's non-nil
		if responseObject != nil {
			errDirect := json.Unmarshal(body, responseObject)
			if errDirect == nil {
				return nil
			}
		}
		return fmt.Errorf("unmarshal APIResponse err [%s] body:\n%s", err, string(body))
	}

	if apiRes.Code != 0 {
		errMsg := apiRes.Message
		if errMsg == "" {
			errMsg = fmt.Sprintf("API error code %d", apiRes.Code)
		}
		return fmt.Errorf("%s (method: %s, request_id: %d), body: %s", errMsg, apiRes.Method, apiRes.ID, string(body))
	}

	if responseObject != nil {
		if len(apiRes.Result) > 0 && string(apiRes.Result) != "null" {
			err = json.Unmarshal(apiRes.Result, responseObject)
			if err != nil {
				return fmt.Errorf("failed to unmarshal apiRes.Result, body: %s, unmarshal err: %s", string(body), err)
			}
		} else {
			// Handle cases where code is 0 but result is empty/null when expecting data
			// This might be okay for cancel order, but not for get orders etc.
			// Check if responseObject type suggests data is expected. For now, return nil.
			return nil
		}
	}

	return nil
}

func (a *API) makePublicHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	if res.StatusCode < 200 || res.StatusCode >= 300 {
		return fmt.Errorf("http error: %s, body: %s", res.Status, string(body))
	}

	var apiRes APIResponse
	err = json.Unmarshal(body, &apiRes)
	if err != nil {
		return fmt.Errorf("unmarshal APIResponse err [%s] body:\n%s", err, string(body))
	}

	if apiRes.Code != 0 {
		errMsg := apiRes.Message
		if errMsg == "" {
			errMsg = fmt.Sprintf("API error code %d", apiRes.Code)
		}
		return fmt.Errorf("%s (method: %s, request_id: %d), body: %s", errMsg, apiRes.Method, apiRes.ID, string(body))
	}

	if responseObject != nil {
		if len(apiRes.Result) > 0 && string(apiRes.Result) != "null" {
			err = json.Unmarshal(apiRes.Result, responseObject)
			if err != nil {
				return fmt.Errorf("failed to unmarshal apiRes.Result, body: %s, result: %s, unmarshal err: %s", string(body), string(apiRes.Result), err)
			}
		} else {
			return fmt.Errorf("api returned code 0 but no result data, body: %s", string(body))
		}
	}

	return nil
}
