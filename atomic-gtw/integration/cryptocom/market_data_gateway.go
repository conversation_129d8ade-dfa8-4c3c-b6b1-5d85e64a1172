package cryptocom

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	baseURL string
	options gateway.Options
	tickCh  chan gateway.Tick
	api     *API
}

func NewMarketDataGateway(baseURL string, options gateway.Options, tickCh chan gateway.Tick, api *API) *MarketDataGateway {
	return &MarketDataGateway{
		baseURL: baseURL,
		options: options,
		tickCh:  tickCh,
		api:     api,
	}
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)

	err := ws.Connect(g.baseURL)
	if err != nil {
		return fmt.Errorf("market ws connect err: %s", err)
	}

	bookChannels := make([]string, 0, len(markets))
	tradeChannels := make([]string, 0, len(markets))
	for _, market := range markets {
		bookChannels = append(bookChannels, fmt.Sprintf("book.%s.50", market.Symbol))
		tradeChannels = append(tradeChannels, fmt.Sprintf("trade.%s", market.Symbol))
	}

	ch := make(chan []byte, 1000)
	ws.SubscribeMessages(ch)

	// Subscribe to Books with SNAPSHOT_AND_UPDATE
	if len(bookChannels) > 0 {
		bookSubRequest := WsRequest{
			ID:     time.Now().UnixMilli(),
			Method: "subscribe",
			Params: WsRequestParams{
				Channels:             bookChannels,
				BookSubscriptionType: "SNAPSHOT_AND_UPDATE",
				BookUpdateFrequency:  10,
			},
			Nonce: time.Now().UnixMilli(),
		}

		bookData, err := json.Marshal(bookSubRequest)
		if err != nil {
			ws.Close()
			return fmt.Errorf("failed to marshal market book sub request, err: %s", err)
		}

		if err := ws.WriteMessage(bookData); err != nil {
			ws.Close()
			return fmt.Errorf("failed write market book sub msg to ws: %s", err)
		}

		err = waitForWsReqRes(ws, ch, bookSubRequest.ID, 5*time.Second)
		if err != nil {
			ws.Close()
			return fmt.Errorf("failed waiting for book sub confirmation: %w", err)
		}
	}

	if len(tradeChannels) > 0 {
		tradeSubRequest := WsRequest{
			ID:     time.Now().UnixMilli(),
			Method: "subscribe",
			Params: WsRequestParams{
				Channels: tradeChannels,
			},
			Nonce: time.Now().UnixMilli(),
		}

		tradeData, err := json.Marshal(tradeSubRequest)
		if err != nil {
			ws.Close()
			return fmt.Errorf("failed to marshal market trade sub request, err: %s", err)
		}

		if err := ws.WriteMessage(tradeData); err != nil {
			ws.Close()
			return fmt.Errorf("failed write market trade sub msg to ws: %s", err)
		}

		err = waitForWsReqRes(ws, ch, tradeSubRequest.ID, 5*time.Second)
		if err != nil {
			ws.Close()
			return fmt.Errorf("failed waiting for trade sub confirmation: %w", err)
		}
	}

	go g.messageHandler(ws, ch)

	return nil
}

type WsMessage struct {
	ID       int64           `json:"id"`
	Code     int             `json:"code"`
	Method   string          `json:"method"`
	Result   json.RawMessage `json:"result,omitempty"`
	Message  string          `json:"message,omitempty"`
	Original string          `json:"original,omitempty"`
}

type WsSubscribeMessage struct {
	Channel        string          `json:"channel"`
	Subscription   string          `json:"subscription"`
	InstrumentName string          `json:"instrument_name"`
	Depth          int             `json:"depth,omitempty"`
	Data           json.RawMessage `json:"data"`
}

type WsBook struct {
	Bids []gateway.PriceArray `json:"bids"`
	Asks []gateway.PriceArray `json:"asks"`
	T    int64                `json:"t"`
}

type WsTrade struct {
	Price      float64 `json:"p,string"`
	Quantity   float64 `json:"q,string"`
	Side       string  `json:"s"`
	ID         string  `json:"d"`
	Ts         int64   `json:"t"`
	Instrument string  `json:"i"`
}

func (g *MarketDataGateway) messageHandler(ws *utils.WsClient, ch chan []byte) {
	defer ws.Close()

	for data := range ch {
		var msg WsMessage
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("%s Market WS: Failed to unmarshal WsMessage [%s] err [%s]", Exchange, string(data), err)
			continue
		}

		switch msg.Method {
		case "subscribe":
			if msg.Result == nil {
				log.Printf("%s Market WS: Received subscribe message with nil result: %s", Exchange, string(data))
				continue
			}

			var subMsg WsSubscribeMessage
			err := json.Unmarshal(msg.Result, &subMsg)
			if err != nil {
				log.Printf("%s Market WS: Failed to unmarshal WsSubscribeMessage result [%s] err [%s]", Exchange, string(msg.Result), err)
				continue
			}

			switch subMsg.Channel {
			case "book":
				instrumentName := g.extractInstrumentName(subMsg)
				if instrumentName == "" {
					log.Printf("%s Market WS book: Failed to extract instrument name from subscribe message: %s", Exchange, string(data))
					continue
				}
				if err := g.processBookSnapshot(subMsg.Data, instrumentName); err != nil { // Renamed function
					log.Printf("%s Market WS: error processing book snapshot for %s: %s", Exchange, instrumentName, err)
				}

			case "book.update":
				instrumentName := g.extractInstrumentName(subMsg)
				if instrumentName == "" {
					log.Printf("%s Market WS book.update: Failed to extract instrument name from subscribe message: %s", Exchange, string(data))
					continue
				}
				if err := g.processBookUpdate(subMsg.Data, instrumentName); err != nil {
					log.Printf("%s Market WS: error processing book.update for %s: %s", Exchange, instrumentName, err)
				}

			case "trade":
				if err := g.processTrade(subMsg.Data); err != nil {
					log.Printf("%s Market WS: error processing trade update: %s", Exchange, err)
				}
			default:
				log.Printf("%s Market WS: unhandled subscribe channel [%s] for msg: %s", Exchange, subMsg.Channel, string(data))
			}
		default:
			if err := defaultWsMessageProcessor(ws, msg); err != nil {
				log.Printf("%s Market WS: default processor err: [%s] for msg: %s", Exchange, err, string(data))
			}
		}
	}
}

func (g *MarketDataGateway) extractInstrumentName(subMsg WsSubscribeMessage) string {
	instrumentName := subMsg.InstrumentName
	if instrumentName == "" && subMsg.Subscription != "" {
		parts := strings.Split(subMsg.Subscription, ".")
		if len(parts) > 1 {
			instrumentName = parts[1]
		}
	}
	return instrumentName
}

func defaultWsMessageProcessor(ws *utils.WsClient, msg WsMessage) error {
	switch msg.Method {
	case "public/heartbeat":
		heartbeatResponse := map[string]interface{}{
			"id":     msg.ID,
			"method": "public/respond-heartbeat",
			"nonce":  time.Now().UnixMilli(),
		}
		responseBytes, err := json.Marshal(heartbeatResponse)
		if err != nil {
			return err
		}
		if err := ws.WriteMessage(responseBytes); err != nil {
			return fmt.Errorf("write heartbeat response err: %w", err)
		}
		return nil
	default:
		if msg.Code != 0 {
			return fmt.Errorf("received WS error message: code [%d], method [%s], msg: %s", msg.Code, msg.Method, msg.Message)
		}
	}

	return nil
}

func (g *MarketDataGateway) processBookSnapshot(data json.RawMessage, symbol string) error {
	var bookUpdates []WsBook
	err := json.Unmarshal(data, &bookUpdates)
	if err != nil {
		return fmt.Errorf("unmarshal WsBook list err: %s, data: %s", err, string(data))
	}

	for _, bookUpdate := range bookUpdates {
		asks := bookUpdate.Asks
		bids := bookUpdate.Bids

		eventLog := make([]gateway.Event, 0, len(asks)+len(bids)+1)
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: symbol,
			},
		})
		eventLog = append(eventLog, gateway.PriceArrayToDepthEvents(symbol, gateway.Ask, asks)...)
		eventLog = append(eventLog, gateway.PriceArrayToDepthEvents(symbol, gateway.Bid, bids)...)

		if len(eventLog) > 0 {
			g.tickCh <- gateway.Tick{
				ReceivedTimestamp: time.Now(),
				EventLog:          eventLog,
			}
		}
	}

	return nil
}

type WsBookUpdateData struct {
	Update struct {
		Bids []gateway.PriceArray `json:"bids"`
		Asks []gateway.PriceArray `json:"asks"`
	} `json:"update"`
	T  int64 `json:"t"`  // Publish time
	TT int64 `json:"tt"` // Update time
	U  int64 `json:"u"`  // Update sequence
	PU int64 `json:"pu"` // Previous update sequence
}

func (g *MarketDataGateway) processBookUpdate(data json.RawMessage, symbol string) error {
	var bookUpdates []WsBookUpdateData
	err := json.Unmarshal(data, &bookUpdates)
	if err != nil {
		return fmt.Errorf("unmarshal WsBookUpdateData list err: %s, data: %s", err, string(data))
	}

	for _, updateData := range bookUpdates {
		asks := updateData.Update.Asks
		bids := updateData.Update.Bids

		// Crypto exchanges may send empty updates, which we should ignore. This are heartbeats.
		if len(asks) == 0 && len(bids) == 0 {
			continue
		}

		eventLog := make([]gateway.Event, 0, len(asks)+len(bids))
		eventLog = append(eventLog, gateway.PriceArrayToDepthEvents(symbol, gateway.Ask, asks)...)
		eventLog = append(eventLog, gateway.PriceArrayToDepthEvents(symbol, gateway.Bid, bids)...)

		if len(eventLog) > 0 {
			g.tickCh <- gateway.Tick{
				ReceivedTimestamp: time.Now(),
				EventLog:          eventLog,
			}
		}
	}

	return nil
}

func (g *MarketDataGateway) processTrade(data json.RawMessage) error {
	var listTradeUpdate []WsTrade
	err := json.Unmarshal(data, &listTradeUpdate)
	if err != nil {
		return fmt.Errorf("unmarshal WsTrade list err: %s, data: %s", err, string(data))
	}

	eventLog := make([]gateway.Event, 0, len(listTradeUpdate))
	for _, tradeUpdate := range listTradeUpdate {
		event := gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				ID:        tradeUpdate.ID,
				Symbol:    tradeUpdate.Instrument,
				Direction: mapTradeSide(tradeUpdate.Side),
				Price:     tradeUpdate.Price,
				Amount:    tradeUpdate.Quantity,
				Timestamp: gateway.ParseTimestamp(tradeUpdate.Ts),
			},
		}

		eventLog = append(eventLog, event)
	}

	if len(eventLog) > 0 {
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          eventLog,
		}
	}

	return nil
}

func mapTradeSide(side string) gateway.Side {
	switch side {
	case "BUY":
		return gateway.Bid
	case "SELL":
		return gateway.Ask
	default:
		return ""
	}
}
