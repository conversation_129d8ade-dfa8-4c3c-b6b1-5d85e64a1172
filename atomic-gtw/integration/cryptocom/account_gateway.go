package cryptocom

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api     *API
	options gateway.Options
	tickCh  chan gateway.Tick
	baseURL string
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick, baseURL string) *AccountGateway {
	return &AccountGateway{
		api:     api,
		options: options,
		tickCh:  tickCh,
		baseURL: baseURL,
	}
}

func (g *AccountGateway) Connect() error {
	return nil
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	userBalanceRes, err := g.api.UserBalance()
	if err != nil {
		return balances, err
	}

	if len(userBalanceRes.Data) == 0 || len(userBalanceRes.Data[0].PositionBalances) == 0 {
		return balances, nil
	}

	positionBalances := userBalanceRes.Data[0].PositionBalances
	balances = make([]gateway.Balance, 0, len(positionBalances))

	for _, posBalance := range positionBalances {
		total := posBalance.Quantity
		reserved := posBalance.ReservedQty
		available := total - reserved

		balances = append(balances, gateway.Balance{
			Asset:     posBalance.InstrumentName,
			Available: available,
			Total:     total,
		})
	}

	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrdersAPI, err := g.api.OpenOrders(market.Symbol) // API call returns updated APIOrder struct
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrdersAPI))
	for _, apiOrder := range openOrdersAPI {
		commonOrder := mapAPIOrderToCommon(apiOrder)
		commonOrder.Market = market
		orders = append(orders, commonOrder)
	}

	return orders, nil
}

var postOnlyRejectedMatch = regexp.MustCompile(`POST_ONLY_REJECTED`)
var insuficientBalanceMatch = regexp.MustCompile(`INSUFFICIENT_AVAILABLE_BALANCE|EXCEEDS_MAX_AVAILABLE_BALANCE`)
var tooManyRequestsMatch = regexp.MustCompile(`TOO_MANY_REQUESTS`)
var minOrderSizeMatch = regexp.MustCompile(`BELOW_MIN_ORDER_SIZE`)

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	var side string
	if order.Side == gateway.Bid {
		side = "BUY"
	} else {
		side = "SELL"
	}

	params := map[string]interface{}{
		"instrument_name": order.Market.Symbol,
		"side":            side,
		"type":            "LIMIT",
		"quantity":        utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		"price":           utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
	}

	if order.PostOnly {
		params["exec_inst"] = []string{"POST_ONLY"}
	}
	if order.ClientOrderID != "" {
		params["client_oid"] = order.ClientOrderID
	}

	orderID, err := g.api.PlaceOrder(params)
	if err != nil {
		if insuficientBalanceMatch.MatchString(err.Error()) {
			return "", gateway.InsufficientBalanceErr
		} else if tooManyRequestsMatch.MatchString(err.Error()) {
			return "", gateway.RateLimitErr
		} else if minOrderSizeMatch.MatchString(err.Error()) {
			return "", gateway.MinOrderSizeErr
		} else if postOnlyRejectedMatch.MatchString(err.Error()) {
			return "", &gateway.OrderNotOpenedErr{Err: err}
		}
		return "", err
	}

	return orderID, nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	err := g.api.CancelOrder(order.ID)
	if err != nil {
		if tooManyRequestsMatch.MatchString(err.Error()) {
			return gateway.RateLimitErr
		}
	}
	return err
}

type WsAuthRequest struct {
	ID     int64  `json:"id"`
	Method string `json:"method"`
	APIKey string `json:"api_key"`
	Nonce  int64  `json:"nonce"`
	Sig    string `json:"sig"`
}

func newWsAuthRequest(apiKey, apiSecret string) WsAuthRequest {
	nonce := time.Now().UnixMilli()
	authReqID := nonce
	authReq := WsAuthRequest{
		ID:     authReqID,
		APIKey: apiKey,
		Method: "public/auth",
		Nonce:  nonce,
	}

	payload := ""
	payload += authReq.Method
	payload += fmt.Sprintf("%d", authReq.ID)
	payload += authReq.APIKey
	payload += fmt.Sprintf("%d", authReq.Nonce)
	signer := hmac.New(sha256.New, []byte(apiSecret))
	signer.Write([]byte(payload))
	authReq.Sig = hex.EncodeToString(signer.Sum(nil))

	return authReq
}

type WsRequestParams struct {
	Channels             []string `json:"channels"`
	BookSubscriptionType string   `json:"book_subscription_type,omitempty"`
	BookUpdateFrequency  int      `json:"book_update_frequency,omitempty"`
}

type WsRequest struct {
	ID     int64           `json:"id"`
	Method string          `json:"method"`
	Params WsRequestParams `json:"params"`
	Nonce  int64           `json:"nonce,omitempty"`
}

func newSubRequestForMarkets(fmtStr string, markets []gateway.Market) WsRequest {
	reqId := time.Now().UnixMilli() + 1
	subReq := WsRequest{
		Method: "subscribe",
		ID:     reqId,
		Nonce:  time.Now().UnixMilli(),
	}

	channels := make([]string, 0, len(markets))
	for _, market := range markets {
		channels = append(channels, fmt.Sprintf(fmtStr, market.Symbol))
	}
	subReq.Params.Channels = channels

	return subReq
}

func (g *AccountGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()

	err := ws.Connect(g.baseURL)
	if err != nil {
		return fmt.Errorf("account ws connect err: %s", err)
	}

	authReq := newWsAuthRequest(g.options.ApiKey, g.options.ApiSecret)
	authData, err := json.Marshal(authReq)
	if err != nil {
		return fmt.Errorf("failed to marshal auth request, err: %s", err)
	}

	if err := ws.WriteMessage(authData); err != nil {
		ws.Close()
		return fmt.Errorf("failed write auth msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)

	err = waitForWsReqRes(ws, ch, authReq.ID, 10*time.Second)
	if err != nil {
		closeErr := ws.Close()
		if closeErr != nil {
			log.Printf("Failed to close ws connection after auth req err: %s", closeErr)
		}
		return err
	}

	subChannels := []string{"user.trade.%s"}
	for _, channelFmt := range subChannels {
		subReq := newSubRequestForMarkets(channelFmt, markets)
		subData, err := json.Marshal(subReq)
		if err != nil {
			ws.Close()
			return fmt.Errorf("failed to marshal sub request for %s, err: %s", channelFmt, err)
		}

		if err := ws.WriteMessage(subData); err != nil {
			ws.Close()
			return fmt.Errorf("failed write sub msg to ws for %s: %s", channelFmt, err)
		}

		err = waitForWsReqRes(ws, ch, subReq.ID, 5*time.Second)
		if err != nil {
			closeErr := ws.Close()
			if closeErr != nil {
				log.Printf("Failed to close ws connection after sub req err: %s", closeErr)
			}
			return err
		}
	}

	go g.messageHandler(ws, ch)

	return nil
}

func waitForWsReqRes(ws *utils.WsClient, ch chan []byte, reqID int64, timeout time.Duration) error {
	timeoutTicker := time.NewTimer(timeout)
	defer timeoutTicker.Stop()

	for {
		select {
		case msgData, ok := <-ch:
			if !ok {
				return fmt.Errorf("message channel closed unexpectedly")
			}

			var msg WsMessage
			if err := json.Unmarshal(msgData, &msg); err != nil {
				log.Printf("Failed to unmarshal potential response: %s, data: %s", err, string(msgData))
				continue
			}

			if msg.ID == reqID {
				if msg.Code != 0 {
					return fmt.Errorf("req id %d error response code [%d], msg: %s", reqID, msg.Code, string(msgData))
				}
				return nil
			}

			if err := defaultWsMessageProcessor(ws, msg); err != nil {
				log.Printf("Account WS: Error processing non-response message: %s", err)
			}

		case <-timeoutTicker.C:
			return fmt.Errorf("timed out waiting for response to req id %d", reqID)
		}
	}
}

func (g *AccountGateway) messageHandler(ws *utils.WsClient, ch chan []byte) {
	defer ws.Close()

	for data := range ch {
		var msg WsMessage
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("%s Account WS: Failed to unmarshal WsMessage [%s] err [%s]", Exchange, string(data), err)
			continue
		}

		switch msg.Method {
		case "subscribe":
			if msg.Result == nil {
				log.Printf("%s Account WS: Received subscribe message with nil result: %s", Exchange, string(data))
				continue
			}

			var subMsg WsSubscribeMessage
			err := json.Unmarshal(msg.Result, &subMsg)
			if err != nil {
				log.Printf("%s Account WS: Failed to unmarshal WsSubscribeMessage [%s] err [%s]", Exchange, string(data), err)
				continue
			}

			switch {
			case strings.HasPrefix(subMsg.Channel, "user.trade"):
				if err := g.processUserFill(subMsg.Data); err != nil {
					log.Printf("%s Account WS: error processing user.trade update \"%s\": %s", Exchange.Name, data, err)
				}
			default:
				log.Printf("%s Account WS: Received unhandled subscribe channel [%s]", Exchange, subMsg.Channel)
			}
		default:
			if err := defaultWsMessageProcessor(ws, msg); err != nil {
				log.Printf("%s Account WS: default processor err: [%s] for msg: %s", Exchange, err, string(data))
			}
		}
	}
}

type WsUserTrade struct {
	Side           string  `json:"side"`
	InstrumentName string  `json:"instrument_name"`
	Fee            float64 `json:"fees,string"`
	TradeID        string  `json:"trade_id"`
	CreateTime     int64   `json:"create_time"`
	TradedPrice    float64 `json:"traded_price,string"`
	TradedQuantity float64 `json:"traded_quantity,string"`
	FeeCurrency    string  `json:"fee_instrument_name"`
	OrderID        string  `json:"order_id"`
	TakerSide      string  `json:"taker_side"`
	ClientOid      string  `json:"client_oid,omitempty"`
}

func (g *AccountGateway) processUserFill(data json.RawMessage) error {
	var userTrades []WsUserTrade
	err := json.Unmarshal(data, &userTrades)
	if err != nil {
		return fmt.Errorf("unmarshal WsUserTrade list err: %s, data: %s", err, string(data))
	}

	eventLog := make([]gateway.Event, 0, len(userTrades))
	for _, trade := range userTrades {
		event := gateway.Event{
			Type: gateway.FillEvent,
			Data: gateway.Fill{
				ID:            trade.TradeID,
				OrderID:       trade.OrderID,
				ClientOrderID: trade.ClientOid,
				Symbol:        trade.InstrumentName,
				Price:         trade.TradedPrice,
				Amount:        trade.TradedQuantity,
				Fee:           -trade.Fee, // Inverted sign to match common convention, positive fee pays, negative fee receives, but Crypto.com does the opposite
				FeeAsset:      trade.FeeCurrency,
				Side:          mapAPISideToCommon(trade.Side),
				Timestamp:     gateway.ParseTimestamp(trade.CreateTime),
			},
		}

		eventLog = append(eventLog, event)
	}

	if len(eventLog) > 0 {
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(), // When the message was processed
			EventLog:          eventLog,
		}
	}

	return nil
}

func (g *AccountGateway) NewClientOrderID() string {
	return utils.GenerateRandomAlphanumeric(36)
}
