package coinsbit

import (
	"log"
	"regexp"
	"strconv"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api                *API
	markets            []gateway.Market
	options            gateway.Options
	tickCh             chan gateway.Tick
	trackedDealsMutex  sync.Mutex
	trackedDeals       map[int64]struct{}
	trackedOrdersMutex sync.Mutex
	trackedOrders      map[string]struct{}
}

func NewAccountGateway(api *API, options gateway.Options, markets []gateway.Market, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		api:           api,
		markets:       markets,
		options:       options,
		tickCh:        tickCh,
		trackedDeals:  make(map[int64]struct{}),
		trackedOrders: make(map[string]struct{}),
	}
}

func (g *AccountGateway) Connect() error {
	return nil
}

func (g *AccountGateway) updateOrderDeals(orderID string) error {
	deals, err := g.api.AccountTrades(orderID)
	if err != nil {
		return err
	}

	events := make([]gateway.Event, 0)
	g.trackedDealsMutex.Lock()
	for _, deal := range deals {
		if _, ok := g.trackedDeals[deal.ID]; !ok {
			g.trackedDeals[deal.ID] = struct{}{}
			events = append(events, gateway.NewFillEvent(gateway.Fill{
				ID:      strconv.FormatInt(deal.ID, 10),
				OrderID: orderID,
				Price:   deal.Price,
				Amount:  deal.Amount,
			}))
		}
	}
	g.trackedDealsMutex.Unlock()

	if len(events) > 0 {
		g.tickCh <- gateway.TickWithEvents(events...)
	}

	return nil
}

var insuficientBalanceMatch = regexp.MustCompile(`balance not enough`)

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	data := map[string]string{
		"market": order.Market.Symbol,
		"amount": utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		"price":  utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
	}

	if order.Side == gateway.Bid {
		data["side"] = "buy"
	} else if order.Side == gateway.Ask {
		data["side"] = "sell"
	}

	resOrder, err := g.api.PlaceOrder(data)
	if err != nil {
		switch {
		case insuficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		default:
			return "", err

		}
	}

	orderID := resOrder.OrderId.String()

	// Start tracking fills if order not fully filled
	if resOrder.Left > 0 {
		g.trackedOrdersMutex.Lock()
		g.trackedOrders[orderID] = struct{}{}
		g.trackedOrdersMutex.Unlock()

		// Routinely check for fills every 5 seconds
		go func() {
			for {
				time.Sleep(5 * time.Second)

				// Check if order is still tracked, if not, stop tracking
				g.trackedOrdersMutex.Lock()
				if _, ok := g.trackedOrders[orderID]; !ok {
					delete(g.trackedOrders, orderID)
					g.trackedOrdersMutex.Unlock() // Release lock
					return
				}
				g.trackedOrdersMutex.Unlock()

				// Update fills
				err := g.updateOrderDeals(orderID)
				if err != nil {
					log.Printf("%s [sendOrder] [routine] error updating order [%s] deals: %s", Exchange, orderID, err)
				}
			}
		}()
	} else {
		// If order fully filled, update fills
		go func() {
			err := g.updateOrderDeals(orderID)
			if err != nil {
				log.Printf("%s [sendOrder] error updating order [%s] deals: %s", Exchange, orderID, err)
			}
		}()
	}

	return orderID, nil
}

var orderNotFoundMatch = regexp.MustCompile(`not found`)

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	data := map[string]string{
		"market":  order.Market.Symbol,
		"orderId": order.ID,
	}

	// If order being track, execute last track, and stop tracking
	g.trackedOrdersMutex.Lock()
	if _, ok := g.trackedOrders[order.ID]; ok {
		delete(g.trackedOrders, order.ID)
		go func() {
			err := g.updateOrderDeals(order.ID)
			if err != nil {
				log.Printf("%s [cancelOrder] error updating order [%s] deals: %s", Exchange, order.ID, err)
			}
		}()
	}
	g.trackedOrdersMutex.Unlock()

	err := g.api.CancelOrder(data)
	if err != nil {
		switch {
		case orderNotFoundMatch.MatchString(err.Error()):
			return gateway.OrderNotFoundErr
		default:
			return err

		}
	}

	return nil
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	accountBalance, err := g.api.AccountBalance()
	if err != nil {
		return balances, err
	}

	balances = make([]gateway.Balance, 0, len(accountBalance))
	for asset, balance := range accountBalance {
		balances = append(balances, gateway.Balance{
			Asset:     asset,
			Available: balance.Available,
			Total:     balance.Available + balance.Freeze,
		})
	}

	return
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrders, err := g.api.OpenOrders(market.Symbol)
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders))
	for _, order := range openOrders {
		orders = append(orders, mapAPIOrderToCommon(order, order.ID.String(), market))
	}

	return
}

func mapAPIOrderToCommon(o Order, id string, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market:       market,
		ID:           id,
		Side:         mapAPIOrderSideToGtw(o.Side),
		Amount:       o.Amount,
		Price:        o.Price,
		FilledAmount: o.Amount - o.Left,
	}
}

func mapAPIOrderSideToGtw(side string) gateway.Side {
	if side == "buy" {
		return gateway.Bid
	} else if side == "sell" {
		return gateway.Ask
	} else {
		log.Printf("%s invalid order side \"%s\"", Exchange, side)
		return ""
	}
}
