package coinsbit

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha512"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseProd       = "https://api.coinsbit.io"
	apiSymbols        = "/api/v1/public/markets/"
	apiAccountBalance = "/api/v1/account/balances"
	apiAccountTrades  = "/api/v1/account/trades"
	apiOpenOrders     = "/api/v1/orders"
	apiPlaceOrder     = "/api/v1/order/new"
	apiCancelOrder    = "/api/v1/order/cancel"
	apiDepthBook      = "/api/v1/public/depth/result?market=%s&limit=%d"
)

type API struct {
	options       gateway.Options
	httpClient    *utils.HttpClient
	baseURL       string
	lastNonceUsed int64
	lock          sync.Mutex
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

func (api *API) getNonce() int64 {
	api.lock.Lock()
	defer api.lock.Unlock()

	// Their nonce must be in milliseconds
	// They actually expect a millisecond nonce
	// not an incremental nonce
	nonce := time.Now().UnixMilli()
	if nonce <= api.lastNonceUsed {
		nonce = api.lastNonceUsed + 1
	}

	api.lastNonceUsed = nonce
	return api.lastNonceUsed
}

type APIResponse struct {
	Code    int             `json:"code"`
	Success bool            `json:"success"`
	Message string          `json:"message"`
	Result  json.RawMessage `json:"result"`
}

type AssetDetails struct {
	Name      string  `json:"name"`
	Stock     string  `json:"stock"`
	Money     string  `json:"money"`
	MoneyPrec int     `json:"moneyPrec"`
	StockPrec int     `json:"stockPrec"`
	FeePrec   int     `json:"feePrec"`
	MinAmount float64 `json:"minAmount,string"`
}

func (a *API) Symbols() (symbols []AssetDetails, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiSymbols, nil, false)
	if err != nil {
		return symbols, err
	}

	var listSymbols []AssetDetails
	err = a.makeHttpRequest(req, &listSymbols)
	return listSymbols, err
}

type Balance struct {
	Available float64 `json:"available,string"`
	Freeze    float64 `json:"freeze,string"`
}

func (a *API) AccountBalance() (res map[string]Balance, err error) {
	req, err := a.newHttpRequest(http.MethodPost, apiAccountBalance, nil, true)
	if err != nil {
		return res, err
	}

	var apiBalances map[string]Balance
	err = a.makeHttpRequest(req, &apiBalances)
	if err != nil {
		return nil, err
	}

	mapBalances := make(map[string]Balance)
	for k, v := range apiBalances {
		mapBalances[k] = v

	}

	return mapBalances, err
}

type Deal struct {
	ID          int64   `json:"id"`
	DealOrderID int64   `json:"dealOrderId"` // This is the maker orderID
	Price       float64 `json:"price,string"`
	Amount      float64 `json:"amount,string"`
	Deal        float64 `json:"deal,string"`
	Fee         float64 `json:"fee,string"`
	Role        int64   `json:"role"`
	Time        float64 `json:"time"`
}

type AccountTrades struct {
	Limit   int    `json:"limit"`
	Offset  int    `json:"offset"`
	Records []Deal `json:"records"`
}

func (a *API) AccountTrades(orderID string) (deals []Deal, err error) {
	data := map[string]string{
		"orderId": orderID,
	}
	req, err := a.newHttpRequest(http.MethodPost, apiAccountTrades, data, true)
	if err != nil {
		return deals, err
	}

	var res AccountTrades
	err = a.makeHttpRequest(req, &res)
	if err != nil {
		return nil, err
	}

	return res.Records, err
}

type Order struct {
	OrderId   json.Number `json:"orderId"`
	ID        json.Number `json:"id"`
	Left      float64     `json:"left,string"`
	Market    string      `json:"market"`
	Amount    float64     `json:"amount,string"`
	Type      string      `json:"type"`
	Price     float64     `json:"price,string"`
	Timestamp float64     `json:"timestamp"`
	Side      string      `json:"side"`
}

type OpenOrders struct {
	Limit  int     `json:"limit"`
	Offset int     `json:"offset"`
	Total  int     `json:"total"`
	Result []Order `json:"result"`
}

func (a *API) OpenOrders(symbol string) (orders []Order, err error) {
	data := map[string]string{
		"market": symbol,
	}
	req, err := a.newHttpRequest(http.MethodPost, apiOpenOrders, data, true)
	if err != nil {
		return orders, err
	}

	var openOrders OpenOrders
	err = a.makeHttpRequest(req, &openOrders)
	if err != nil {
		return nil, err
	}

	return openOrders.Result, err
}

type SendOrderRequest struct {
	Market string `json:"market"`
	Side   string `json:"side"`
	Amount string `json:"amount"`
	Price  string `json:"price"`
}

func (a *API) PlaceOrder(order map[string]string) (res Order, err error) {
	req, err := a.newHttpRequest(http.MethodPost, apiPlaceOrder, order, true)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	if err != nil {
		return res, err
	}

	return res, nil
}

func (a *API) CancelOrder(order map[string]string) (err error) {
	req, err := a.newHttpRequest(http.MethodPost, apiCancelOrder, order, true)
	if err != nil {
		return err
	}

	var res Order
	err = a.makeHttpRequest(req, &res)
	if err != nil {
		return err
	}

	return nil
}

type APIDepthBook struct {
	Asks [][]string `json:"asks"`
	Bids [][]string `json:"bids"`
}

func (a *API) DepthBook(symbol string, params gateway.GetDepthParams) (depth APIDepthBook, err error) {
	if params.Limit == 0 {
		params.Limit = 1000
	}
	url := fmt.Sprintf(apiDepthBook, symbol, params.Limit)
	req, err := a.newHttpRequest(http.MethodGet, url, nil, false)
	if err != nil {
		return depth, err
	}

	err = a.makeHttpRequest(req, &depth)
	if err != nil {
		return depth, err
	}

	return depth, nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	var apiRes APIResponse
	err = json.Unmarshal(body, &apiRes)
	if err != nil {
		return fmt.Errorf("unmarshal err [%s] body:\n%s", err, string(body))
	}

	if !apiRes.Success {
		return fmt.Errorf("error: %s", apiRes.Message)
	}

	if responseObject != nil {
		err = json.Unmarshal(apiRes.Result, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return nil
}

func (a *API) newHttpRequest(method string, path string, params map[string]string, signed bool) (*http.Request, error) {
	var bodyData io.Reader
	var headers = map[string]string{
		"Content-type": "application/json",
	}

	if signed {
		body := make(map[string]string)

		for k, v := range params {
			body[k] = v
		}
		body["request"] = path
		body["nonce"] = strconv.FormatInt(a.getNonce(), 10)

		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal payload, err: %s", err)
		}

		payload := base64.StdEncoding.EncodeToString(jsonBody)
		mac := hmac.New(sha512.New, []byte(a.options.ApiSecret))
		mac.Write([]byte(payload))
		signature := hex.EncodeToString(mac.Sum(nil))

		headers["X-TXC-APIKEY"] = a.options.ApiKey
		headers["X-TXC-PAYLOAD"] = payload
		headers["X-TXC-SIGNATURE"] = signature
		bodyData = bytes.NewReader(jsonBody)
		headers["Content-Length"] = strconv.Itoa(len(jsonBody))
	}

	urlRequest := a.baseURL + path
	req, err := http.NewRequest(method, urlRequest, bodyData)
	if err != nil {
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	return req, nil
}
