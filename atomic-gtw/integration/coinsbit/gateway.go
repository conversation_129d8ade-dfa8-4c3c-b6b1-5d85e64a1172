package coinsbit

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"strconv"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/integration/viabtc"
	"github.com/herenow/atomic-gtw/utils"
)

var Exchange = gateway.Exchange{
	Name: "Coinsbit",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	tickCh            chan gateway.Tick
	api               *API
	marketDataGateway *viabtc.MarketDataGateway
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(apiBaseProd, options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.Markets(), g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	g.marketDataGateway = viabtc.NewMarketDataGateway(
		Exchange,
		g.options,
		g.tickCh,
		"wss://ws.coinsbit.io/",
		"https://coinsbit.io",
		viabtc.V3Version,
	)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return gateway.NotImplementedErr
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.Symbols()
	if err != nil {
		return nil, err
	}

	return g.loadMarketsFromSymbols(symbols)
}

func (g *Gateway) loadMarketsFromSymbols(symbols []AssetDetails) ([]gateway.Market, error) {
	commonMarkets := make([]gateway.Market, 0, len(symbols))
	for _, symbol := range symbols {
		market := symbolToCommonMarket(symbol)
		commonMarkets = append(commonMarkets, market)
	}

	return commonMarkets, nil
}

// TODO: Their API doesn't list all the avaialble markets
// so we are going to fetch from the website's API instead
func (g *Gateway) loadMarketsFromWeb() error {
	req, err := http.NewRequest(http.MethodGet, "https://coinsbit.io/v2/market-list", nil)
	if err != nil {
		return fmt.Errorf("Failed to new request, err: %w", err)
	}

	client := utils.NewHttpClient()
	client.UseProxies(g.options.Proxies)
	res, err := client.SendRequest(req)
	if err != nil {
		return fmt.Errorf("Failed to send request, err: %w", err)
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("Failed to read body, err: %w", err)
	}

	if res.StatusCode != http.StatusOK {
		return fmt.Errorf("Failed to fetch markets, status code: %d, res body: %s", res.StatusCode, body)
	}

	type ApiRes struct {
		Response struct {
			MarketList struct {
				Result []struct {
					Name      string  `json:"name"`
					Stock     string  `json:"stock"`
					Money     string  `json:"money"`
					MoneyPrec int     `json:"money_prec"`
					StockPrec int     `json:"stock_prec"`
					FeePrec   int     `json:"fee_prec"`
					MinAmount float64 `json:"min_amount,string"`
				} `json:"result"`
			} `json:"marketList"`
		} `json:"response"`
	}
	apiRes := ApiRes{}
	err = json.Unmarshal(body, &apiRes)
	if err != nil {
		return fmt.Errorf("Failed to unmarshal response, err: %w", err)
	}

	// Map to assetDetails
	assetDetails := make([]AssetDetails, 0, len(apiRes.Response.MarketList.Result))
	for _, market := range apiRes.Response.MarketList.Result {
		assetDetails = append(assetDetails, AssetDetails{
			Name:      market.Name,
			Stock:     market.Stock,
			Money:     market.Money,
			MoneyPrec: market.MoneyPrec,
			StockPrec: market.StockPrec,
			FeePrec:   market.FeePrec,
			MinAmount: market.MinAmount,
		})
	}

	g.loadMarketsFromSymbols(assetDetails)

	return nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func symbolToCommonMarket(symbol AssetDetails) gateway.Market {
	priceTick := 1 / math.Pow10(symbol.MoneyPrec)
	amountTick := 1 / math.Pow10(symbol.StockPrec)

	return gateway.Market{
		Exchange: Exchange,
		Symbol:   symbol.Name,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(symbol.Stock),
			Quote: strings.ToUpper(symbol.Money),
		},
		TakerFee:         0.001,
		MakerFee:         0.001,
		PriceTick:        priceTick,
		AmountTick:       amountTick,
		MinimumOrderSize: symbol.MinAmount,
		MarketType:       gateway.SpotMarket,
	}
}

func parsePriceLevelsToDepth(levels [][]string) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel

	for _, level := range levels {
		if len(level) < 2 {
			return nil, fmt.Errorf("invalid price level: %v", level)
		}
		price, err := strconv.ParseFloat(level[0], 64)
		if err != nil {
			return nil, err
		}
		amount, err := strconv.ParseFloat(level[1], 64)
		if err != nil {
			return nil, err
		}
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		})
	}

	return priceLevels, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks, err := parsePriceLevelsToDepth(depth.Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	bids, err := parsePriceLevelsToDepth(depth.Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
