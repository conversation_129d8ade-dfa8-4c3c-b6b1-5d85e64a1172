package uniswapv2

import (
	"context"
	"fmt"
	"math"
	"math/big"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/herenow/atomic-gtw/gateway"
)

var (
	defaultFactories = map[gateway.ChainID]string{
		gateway.ChainEthereum: "******************************************",
		gateway.ChainArbitrum: "******************************************",
	}
)

const factoryABI = `[{
        "constant": true,
        "inputs": [
            {"name": "tokenA", "type": "address"},
            {"name": "tokenB", "type": "address"}
        ],
        "name": "getPair",
        "outputs": [{"name": "pair", "type": "address"}],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    }]`

const pairABI = `[
    {
        "constant": true,
        "inputs": [],
        "name": "getReserves",
        "outputs": [
            {"name": "_reserve0", "type": "uint112"},
            {"name": "_reserve1", "type": "uint112"},
            {"name": "_blockTimestampLast", "type": "uint32"}
        ],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [],
        "name": "token0",
        "outputs": [{"name": "", "type": "address"}],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [],
        "name": "token1",
        "outputs": [{"name": "", "type": "address"}],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    }
]`

const erc20ABI = `[
    {
        "constant": true,
        "inputs": [],
        "name": "decimals",
        "outputs": [{"name": "", "type": "uint8"}],
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [],
        "name": "symbol",
        "outputs": [{"name": "", "type": "string"}],
        "type": "function"
    }
]`

type V2Pool struct {
	Address     common.Address
	Token0      common.Address
	Token1      common.Address
	Reserve0    *big.Int
	Reserve1    *big.Int
	Decimals0   uint
	Decimals1   uint
	LastUpdated time.Time
}

type V2API struct {
	client     *ethclient.Client
	registry   *gateway.TokenRegistry
	chainID    gateway.ChainID
	factory    common.Address
	poolCache  map[string]*V2Pool        // Cache pool data by pool address
	pairCache  map[string]common.Address // Cache pool addresses by pair symbol (e.g., "WETH/USDC")
	factoryABI abi.ABI
	pairABI    abi.ABI
	mu         sync.RWMutex
}

func NewV2API(chainID gateway.ChainID, options gateway.Options) (*V2API, error) {
	if options.ApiKey == "" {
		return nil, fmt.Errorf("infura API key required")
	}

	// Get RPC endpoint based on chain
	var rpcURL string
	switch chainID {
	case gateway.ChainEthereum:
		rpcURL = fmt.Sprintf("https://mainnet.infura.io/v3/%s", options.ApiKey)
	case gateway.ChainArbitrum:
		rpcURL = fmt.Sprintf("https://arbitrum-mainnet.infura.io/v3/%s", options.ApiKey)
	}

	// Connect to RPC
	client, err := ethclient.Dial(rpcURL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to RPC: %w", err)
	}

	// Initialize token registry for this chain
	registry := gateway.NewTokenRegistry(chainID)

	// Get factory address for this chain
	factoryAddr, ok := defaultFactories[chainID]
	if !ok {
		return nil, fmt.Errorf("no factory address for chain %d", chainID)
	}

	// Parse ABIs
	factoryParsedABI, err := abi.JSON(strings.NewReader(factoryABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse factory ABI: %w", err)
	}

	pairParsedABI, err := abi.JSON(strings.NewReader(pairABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse pair ABI: %w", err)
	}

	return &V2API{
		client:     client,
		registry:   registry,
		chainID:    chainID,
		factory:    common.HexToAddress(factoryAddr),
		poolCache:  make(map[string]*V2Pool),
		pairCache:  make(map[string]common.Address),
		factoryABI: factoryParsedABI,
		pairABI:    pairParsedABI,
	}, nil
}

func (p *V2API) GetPoolAddress(pair gateway.Pair, feeTier int64) (string, error) {
	pairSymbol := fmt.Sprintf("%s-%d", pair.String(), feeTier)

	// Check cache first
	p.mu.RLock()
	if addr, ok := p.pairCache[pairSymbol]; ok {
		p.mu.RUnlock()
		return addr.String(), nil
	}
	p.mu.RUnlock()

	// Get token addresses from registry
	token0Config, err := p.registry.GetTokenConfig(pair.Base)
	if err != nil {
		return "", fmt.Errorf("failed to get base token config: %w", err)
	}

	token1Config, err := p.registry.GetTokenConfig(pair.Quote)
	if err != nil {
		return "", fmt.Errorf("failed to get quote token config: %w", err)
	}

	// Get addresses for this chain
	token0Addr, ok := token0Config.Addresses[p.chainID]
	if !ok {
		return "", fmt.Errorf("token %s not available on chain %d", pair.Base, p.chainID)
	}

	token1Addr, ok := token1Config.Addresses[p.chainID]
	if !ok {
		return "", fmt.Errorf("token %s not available on chain %d", pair.Quote, p.chainID)
	}

	// Call factory to get pool address
	data, err := p.factoryABI.Pack("getPair",
		common.HexToAddress(token0Addr),
		common.HexToAddress(token1Addr),
	)
	if err != nil {
		return "", fmt.Errorf("failed to pack getPair call: %w", err)
	}

	result, err := p.client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &p.factory,
		Data: data,
	}, nil)
	if err != nil {
		return "", fmt.Errorf("failed to call getPair: %w", err)
	}

	var poolAddr common.Address
	if err = p.factoryABI.UnpackIntoInterface(&poolAddr, "getPair", result); err != nil {
		return "", fmt.Errorf("failed to unpack pool address: %w", err)
	}

	if poolAddr == common.HexToAddress("******************************************") {
		return "", fmt.Errorf("no pool exists for pair %s", pairSymbol)
	}

	// Cache the result
	p.mu.Lock()
	p.pairCache[pairSymbol] = poolAddr
	p.mu.Unlock()

	return poolAddr.String(), nil
}

func (p *V2API) GetPoolState(poolAddr string) (V2Pool, error) {
	var pool *V2Pool
	var err error

	// Check cache first
	p.mu.RLock()
	pool, ok := p.poolCache[poolAddr]
	p.mu.RUnlock()

	if !ok {
		// Get pool data if not cached
		pool, err = p.fetchPoolData(poolAddr)
		if err != nil {
			return V2Pool{}, err
		}

		// Cache the pool
		p.mu.Lock()
		p.poolCache[poolAddr] = pool
		p.mu.Unlock()
	}

	// Get current reserves
	reserves, timestamp, err := p.getReserves(poolAddr)
	if err != nil {
		return V2Pool{}, err
	}

	return V2Pool{
		Address:     common.HexToAddress(poolAddr),
		Token0:      pool.Token0,
		Token1:      pool.Token1,
		Reserve0:    reserves[0],
		Reserve1:    reserves[1],
		LastUpdated: time.Unix(int64(timestamp), 0),
	}, nil
}

func (p *V2API) CalculateSpotPrice(pool V2Pool, market gateway.Market) (float64, error) {
	if pool.Reserve0.Sign() == 0 || pool.Reserve1.Sign() == 0 {
		return 0, fmt.Errorf("invalid reserves")
	}

	// Get token data for decimals adjustment
	token0Data, err := p.registry.GetTokenByAddress(pool.Token0.String())
	if err != nil {
		return 0, fmt.Errorf("failed to get token0 data: %w", err)
	}

	token1Data, err := p.registry.GetTokenByAddress(pool.Token1.String())
	if err != nil {
		return 0, fmt.Errorf("failed to get token1 data: %w", err)
	}

	// Create big.Float versions of reserves for precise calculation
	reserve0 := new(big.Float).SetInt(pool.Reserve0)
	reserve1 := new(big.Float).SetInt(pool.Reserve1)

	// Adjust for decimals using division
	decimals0 := new(big.Float).SetFloat64(math.Pow10(int(token0Data.Decimals)))
	decimals1 := new(big.Float).SetFloat64(math.Pow10(int(token1Data.Decimals)))

	reserve0 = reserve0.Quo(reserve0, decimals0)
	reserve1 = reserve1.Quo(reserve1, decimals1)

	// Determine if we need to flip the price based on market direction
	var price *big.Float
	if market.Pair.Base == token0Data.Symbol {
		// If token0 is base, then price should be reserve1/reserve0 (quote/base)
		price = new(big.Float).Quo(reserve1, reserve0)
	} else if market.Pair.Base == token1Data.Symbol {
		// If token1 is base, then price should be reserve0/reserve1 (quote/base)
		price = new(big.Float).Quo(reserve0, reserve1)
	} else {
		return 0, fmt.Errorf("market base token not found in pool")
	}

	result, _ := price.Float64()
	return result, nil
}

func (p *V2API) GeneratePriceLevels(pool V2Pool, market gateway.Market) ([]gateway.PriceLevel, error) {
	// Get token configs for decimal adjustments
	token0Data, err := p.registry.GetTokenByAddress(pool.Token0.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get token0 data: %w", err)
	}
	token1Data, err := p.registry.GetTokenByAddress(pool.Token1.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get token1 data: %w", err)
	}

	// Convert reserves to decimals-adjusted floats
	reserve0Float := new(big.Float).Quo(
		new(big.Float).SetInt(pool.Reserve0),
		new(big.Float).SetFloat64(math.Pow10(int(token0Data.Decimals))),
	)
	reserve1Float := new(big.Float).Quo(
		new(big.Float).SetInt(pool.Reserve1),
		new(big.Float).SetFloat64(math.Pow10(int(token1Data.Decimals))),
	)

	baseReserveFloat, _ := reserve0Float.Float64()
	quoteReserveFloat, _ := reserve1Float.Float64()

	if market.Pair.Base != token0Data.Symbol {
		// Swap reserves if market base is token1
		baseReserveFloat, quoteReserveFloat = quoteReserveFloat, baseReserveFloat
	}

	// Parameters for price level generation
	const (
		numLevels      = 10   // Number of price levels each side
		baseStepSize   = 1.0  // Base amount per level
		maxPriceImpact = 0.01 // Maximum 1% price impact
	)

	levels := make([]gateway.PriceLevel, 0, numLevels*2)
	k := baseReserveFloat * quoteReserveFloat

	// Generate ask levels
	for i := 0; i < numLevels; i++ {
		inputAmount := baseStepSize

		// Calculate quote amount received using constant product formula
		newBaseReserve := baseReserveFloat + inputAmount
		newQuoteReserve := k / newBaseReserve
		quoteAmount := quoteReserveFloat - newQuoteReserve

		// Apply 0.3% fee to output amount
		effectiveQuoteAmount := quoteAmount * 0.997

		// Calculate actual execution price including fee and price impact
		askPrice := effectiveQuoteAmount / inputAmount

		// Check price impact
		priceImpact := math.Abs(askPrice-(quoteReserveFloat/baseReserveFloat)) / (quoteReserveFloat / baseReserveFloat)
		if priceImpact > maxPriceImpact {
			break
		}

		levels = append(levels, gateway.PriceLevel{
			Price:  askPrice,
			Amount: inputAmount,
		})

		// Update reserves for next iteration
		baseReserveFloat = newBaseReserve
		quoteReserveFloat = newQuoteReserve
	}

	// Reset reserves for bid calculation
	baseReserveFloat, _ = reserve0Float.Float64()
	quoteReserveFloat, _ = reserve1Float.Float64()
	if market.Pair.Base != token0Data.Symbol {
		baseReserveFloat, quoteReserveFloat = quoteReserveFloat, baseReserveFloat
	}

	// Generate bid levels
	for i := 0; i < numLevels; i++ {
		outputAmount := baseStepSize

		// Calculate required quote amount using constant product formula
		newBaseReserve := baseReserveFloat - outputAmount
		newQuoteReserve := k / newBaseReserve
		quoteAmount := newQuoteReserve - quoteReserveFloat

		// Apply 0.3% fee to input amount (more quote tokens needed)
		effectiveQuoteAmount := quoteAmount / 0.997

		// Calculate actual execution price including fee and price impact
		bidPrice := effectiveQuoteAmount / outputAmount

		// Check price impact
		priceImpact := math.Abs(bidPrice-(quoteReserveFloat/baseReserveFloat)) / (quoteReserveFloat / baseReserveFloat)
		if priceImpact > maxPriceImpact {
			break
		}

		levels = append(levels, gateway.PriceLevel{
			Price:  bidPrice,
			Amount: outputAmount,
		})

		// Update reserves for next iteration
		baseReserveFloat = newBaseReserve
		quoteReserveFloat = newQuoteReserve
	}

	// Sort levels by price (bids lower, asks higher)
	sort.Slice(levels, func(i, j int) bool {
		return levels[i].Price < levels[j].Price
	})

	return levels, nil
}

func (p *V2API) SupportedFeeTiers() []int64 {
	// Uniswap V2 only has 0.3% fee
	return []int64{3000}
}

func (p *V2API) fetchPoolData(poolAddr string) (*V2Pool, error) {
	addr := common.HexToAddress(poolAddr)

	// Get token0
	data, err := p.pairABI.Pack("token0")
	if err != nil {
		return nil, err
	}

	result, err := p.client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &addr,
		Data: data,
	}, nil)
	if err != nil {
		return nil, err
	}

	var token0 common.Address
	if err = p.pairABI.UnpackIntoInterface(&token0, "token0", result); err != nil {
		return nil, err
	}

	// Get token1 (similar process)
	data, err = p.pairABI.Pack("token1")
	if err != nil {
		return nil, err
	}

	result, err = p.client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &addr,
		Data: data,
	}, nil)
	if err != nil {
		return nil, err
	}

	var token1 common.Address
	if err = p.pairABI.UnpackIntoInterface(&token1, "token1", result); err != nil {
		return nil, err
	}

	// Get token metadata from registry
	token0Data, err := p.registry.GetTokenByAddress(token0.String())
	if err != nil {
		return nil, err
	}

	token1Data, err := p.registry.GetTokenByAddress(token1.String())
	if err != nil {
		return nil, err
	}

	return &V2Pool{
		Address:     addr,
		Token0:      token0,
		Token1:      token1,
		Decimals0:   token0Data.Decimals,
		Decimals1:   token1Data.Decimals,
		LastUpdated: time.Now(),
	}, nil
}

func (p *V2API) getReserves(poolAddr string) ([2]*big.Int, uint32, error) {
	addr := common.HexToAddress(poolAddr)

	data, err := p.pairABI.Pack("getReserves")
	if err != nil {
		return [2]*big.Int{}, 0, err
	}

	result, err := p.client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &addr,
		Data: data,
	}, nil)
	if err != nil {
		return [2]*big.Int{}, 0, err
	}

	unpacked, err := p.pairABI.Methods["getReserves"].Outputs.Unpack(result)
	if err != nil {
		return [2]*big.Int{}, 0, err
	}

	reserve0 := unpacked[0].(*big.Int)
	reserve1 := unpacked[1].(*big.Int)
	timestamp := unpacked[2].(uint32)

	return [2]*big.Int{reserve0, reserve1}, timestamp, nil
}
