package uniswapv2

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"time"

	"github.com/ethereum/go-ethereum/crypto"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/herenow/atomic-gtw/gateway"
)

// Pool event signatures
const (
	syncEventSignature = "Sync(uint112,uint112)"
	swapEventSignature = "Swap(address,uint256,uint256,uint256,uint256,address)"
	mintEventSignature = "Mint(address,uint256,uint256)"
	burnEventSignature = "Burn(address,uint256,uint256,address)"
)

type MarketDataGateway struct {
	api          *V2API
	wsClient     *ethclient.Client
	tickCh       chan gateway.Tick
	options      gateway.Options
	markets      []gateway.Market
	quit         chan bool
	poolToMarket map[common.Address]gateway.Market
}

func NewMarketDataGateway(api *V2API, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		api:          api,
		tickCh:       tickCh,
		options:      options,
		quit:         make(chan bool),
		poolToMarket: make(map[common.Address]gateway.Market),
	}
}

func (g *MarketDataGateway) connectWebsocket() error {
	var wsURL string
	switch g.api.chainID {
	case gateway.ChainEthereum:
		wsURL = fmt.Sprintf("wss://mainnet.infura.io/ws/v3/%s", g.options.ApiKey)
	case gateway.ChainArbitrum:
		wsURL = fmt.Sprintf("wss://arbitrum-mainnet.infura.io/ws/v3/%s", g.options.ApiKey)
	default:
		return fmt.Errorf("unsupported chain ID: %d", g.api.chainID)
	}

	wsClient, err := ethclient.Dial(wsURL)
	if err != nil {
		return fmt.Errorf("failed to connect to websocket endpoint: %w", err)
	}

	g.wsClient = wsClient
	return nil
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	g.markets = markets

	// Build pool address to market mapping
	for _, market := range markets {
		poolAddr, err := g.api.GetPoolAddress(market.Pair, market.FeeTier)
		if err != nil {
			return fmt.Errorf("failed to get pool address for %s: %w", market.Symbol, err)
		}
		g.poolToMarket[common.HexToAddress(poolAddr)] = market
	}

	// Connect to WebSocket endpoint
	if err := g.connectWebsocket(); err != nil {
		return fmt.Errorf("websocket connection failed: %w", err)
	}

	// Get initial state
	if err := g.fetchInitialState(); err != nil {
		return fmt.Errorf("failed to fetch initial state: %w", err)
	}

	// Start block header subscription
	go g.subscribeToNewBlocks()

	return nil
}

func (g *MarketDataGateway) subscribeToNewBlocks() {
	for {
		if err := g.startBlockSubscription(); err != nil {
			log.Printf("Block subscription error: %v, retrying in 5s...", err)
			time.Sleep(5 * time.Second)

			// Attempt to reconnect websocket
			if err = g.connectWebsocket(); err != nil {
				log.Printf("Failed to reconnect websocket: %v", err)
				continue
			}
		}
	}
}

func (g *MarketDataGateway) startBlockSubscription() error {
	headers := make(chan *types.Header)
	sub, err := g.wsClient.SubscribeNewHead(context.Background(), headers)
	if err != nil {
		return fmt.Errorf("failed to subscribe to new headers: %w", err)
	}
	defer sub.Unsubscribe()

	// Get all pool addresses
	addresses := make([]common.Address, 0, len(g.markets))
	for addr := range g.poolToMarket {
		addresses = append(addresses, addr)
	}

	// Create filter query for pool events
	query := ethereum.FilterQuery{
		Addresses: addresses,
		Topics: [][]common.Hash{{
			crypto.Keccak256Hash([]byte(syncEventSignature)),
			crypto.Keccak256Hash([]byte(swapEventSignature)),
			crypto.Keccak256Hash([]byte(mintEventSignature)),
			crypto.Keccak256Hash([]byte(burnEventSignature)),
		}},
	}

	for {
		select {
		case err = <-sub.Err():
			return fmt.Errorf("subscription error: %w", err)
		case header := <-headers:
			// Update query for the new block
			query.FromBlock = header.Number
			query.ToBlock = header.Number

			// Get logs for this block using websocket client
			logs, err := g.wsClient.FilterLogs(context.Background(), query)
			if err != nil {
				log.Printf("Failed to get logs for block %s: %v", header.Number.String(), err)
				continue
			}

			// Process any events in this block
			for _, vLog := range logs {
				if err = g.processPoolEvent(vLog); err != nil {
					log.Printf("Error processing pool event: %v", err)
				}
			}

		case <-g.quit:
			return nil
		}
	}
}

func (g *MarketDataGateway) processPoolEvent(vLog types.Log) error {
	// Find corresponding market
	market, ok := g.poolToMarket[vLog.Address]
	if !ok {
		return fmt.Errorf("no market found for pool address %s", vLog.Address.Hex())
	}

	var poolState V2Pool

	// For Sync events, parse reserves directly from event data
	if vLog.Topics[0].Hex() == common.HexToHash(syncEventSignature).Hex() {
		reserve0, reserve1 := new(big.Int), new(big.Int)
		if len(vLog.Data) >= 64 {
			reserve0.SetBytes(vLog.Data[:32])
			reserve1.SetBytes(vLog.Data[32:64])

			// Get token addresses for this pool
			token0Addr, err := g.api.registry.GetTokenAddress(market.Pair.Base)
			if err != nil {
				return fmt.Errorf("failed to get base token address: %w", err)
			}
			token1Addr, err := g.api.registry.GetTokenAddress(market.Pair.Quote)
			if err != nil {
				return fmt.Errorf("failed to get quote token address: %w", err)
			}

			poolState = V2Pool{
				Address:     vLog.Address,
				Token0:      common.HexToAddress(token0Addr),
				Token1:      common.HexToAddress(token1Addr),
				Reserve0:    reserve0,
				Reserve1:    reserve1,
				LastUpdated: time.Now(),
			}
		}
	} else {
		// For other events (Swap, Mint, Burn), get full state
		var err error
		poolState, err = g.api.GetPoolState(vLog.Address.String())
		if err != nil {
			return fmt.Errorf("failed to get pool state: %w", err)
		}
	}

	return g.updateMarketState(market, poolState)
}

func (g *MarketDataGateway) updateMarketState(market gateway.Market, poolState V2Pool) error {
	// Calculate new price levels
	levels, err := g.api.GeneratePriceLevels(poolState, market)
	if err != nil {
		return fmt.Errorf("failed to generate price levels: %w", err)
	}

	// Calculate spot price for side determination
	spotPrice, err := g.api.CalculateSpotPrice(poolState, market)
	if err != nil {
		return fmt.Errorf("failed to calculate spot price: %w", err)
	}

	// Create market events
	events := make([]gateway.Event, 0, len(levels)+1)

	// Add snapshot event
	events = append(events, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: market.Symbol,
		},
	})

	// Split levels into bids and asks based on spot price
	for _, level := range levels {
		var side gateway.Side
		if level.Price <= spotPrice {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   side,
				Price:  level.Price,
				Amount: level.Amount,
			},
		})
	}

	// Send tick with updated order book
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}

	return nil
}

func (g *MarketDataGateway) fetchInitialState() error {
	for _, market := range g.markets {
		poolAddr, err := g.api.GetPoolAddress(market.Pair, 0)
		if err != nil {
			return fmt.Errorf("failed to get pool address for %s: %w", market.Symbol, err)
		}

		poolState, err := g.api.GetPoolState(poolAddr)
		if err != nil {
			return fmt.Errorf("failed to get pool state for %s: %w", market.Symbol, err)
		}

		if err = g.updateMarketState(market, poolState); err != nil {
			return fmt.Errorf("failed to update initial state for %s: %w", market.Symbol, err)
		}
	}

	return nil
}

func (g *MarketDataGateway) Close() error {
	close(g.quit)
	if g.wsClient != nil {
		g.wsClient.Close()
	}
	return nil
}
