package uniswapv2

import (
	"fmt"
	"math"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

func NewExchange(chainID gateway.ChainID) gateway.Exchange {
	return gateway.Exchange{
		Name:    "UniswapV3",
		ChainID: chainID,
	}
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *MarketDataGateway
	api               *V2API
	tickCh            chan gateway.Tick
	exchange          gateway.Exchange
}

func NewGateway(chainID gateway.ChainID) gateway.NewGateway {
	return func(options gateway.Options) gateway.Gateway {
		gtw := &Gateway{
			options:  options,
			tickCh:   make(chan gateway.Tick, 1000),
			exchange: NewExchange(chainID),
		}
		gtw.Gateway = base.NewGateway(gtw)
		return gtw
	}
}
func (g *Gateway) Connect() error {
	// Initialize API
	api, err := NewV2API(g.exchange.ChainID, g.options)
	if err != nil {
		return fmt.Errorf("failed to create UniswapV2 API: %w", err)
	}
	g.api = api

	// Initialize market data gateway
	g.marketDataGateway = NewMarketDataGateway(g.api, g.options, g.tickCh)
	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	if err := g.marketDataGateway.SubscribeMarkets(markets); err != nil {
		return fmt.Errorf("failed to subscribe to markets: %w", err)
	}
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return g.exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	if g.api == nil {
		return nil, fmt.Errorf("gateway not connected")
	}

	markets := make([]gateway.Market, 0)

	// Get all tokens registered for this chain
	tokens := g.api.registry.GetAvailableTokens()

	// Generate all possible default pairs
	for i := 0; i < len(tokens); i++ {
		for j := i + 1; j < len(tokens); j++ {
			token0, err := g.api.registry.GetTokenConfig(tokens[i])
			if err != nil {
				continue
			}

			token1, err := g.api.registry.GetTokenConfig(tokens[j])
			if err != nil {
				continue
			}

			// Create market for both directions
			pairs := []gateway.Pair{
				{Base: token0.Symbol, Quote: token1.Symbol},
				{Base: token1.Symbol, Quote: token0.Symbol},
			}

			for _, pair := range pairs {
				baseToken, _ := g.api.registry.GetTokenConfig(pair.Base)
				quoteToken, _ := g.api.registry.GetTokenConfig(pair.Quote)

				market := gateway.Market{
					Exchange:   g.exchange,
					Symbol:     pair.String(),
					Pair:       pair,
					PriceTick:  1.0 / math.Pow10(int(quoteToken.Decimals)),
					AmountTick: 1.0 / math.Pow10(int(baseToken.Decimals)),
					FeeTier:    3000,
				}
				markets = append(markets, market)
			}
		}
	}

	// If user specified additional markets
	if len(g.options.LoadMarket) > 0 {
		for _, marketStr := range g.options.LoadMarket {
			pair, err := gateway.StringToPair(marketStr)
			if err != nil {
				return nil, fmt.Errorf("invalid market format %s: %w", marketStr, err)
			}

			baseToken, err := g.api.registry.GetTokenConfig(pair.Base)
			if err != nil {
				return nil, fmt.Errorf("base token not found: %w", err)
			}

			quoteToken, err := g.api.registry.GetTokenConfig(pair.Quote)
			if err != nil {
				return nil, fmt.Errorf("quote token not found: %w", err)
			}

			market := gateway.Market{
				Exchange:   g.exchange,
				Symbol:     pair.String(),
				Pair:       pair,
				PriceTick:  1.0 / math.Pow10(int(quoteToken.Decimals)),
				AmountTick: 1.0 / math.Pow10(int(baseToken.Decimals)),
				FeeTier:    3000,
			}

			markets = append(markets, market)
		}
	}

	if len(markets) == 0 {
		return nil, fmt.Errorf("no valid markets found")
	}

	return markets, nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
