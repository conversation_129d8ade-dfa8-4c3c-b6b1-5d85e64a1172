package uniswapv2

import (
	"fmt"
	"math/big"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Helper function to create properly initialized API for testing
func setupTestAPI(t *testing.T) *V2API {
	t.Helper()

	options := gateway.Options{
		ApiKey:   "bff2bf9b5e8542559dd3a324fabaaca2",
		ChainID:  "ethereum",
		DEXProto: "uniswap_v2",
	}

	api, err := NewV2API(options)
	require.NoError(t, err)

	return api
}

func TestNewV2API(t *testing.T) {
	tests := []struct {
		name    string
		options gateway.Options
		wantErr bool
	}{
		{
			name: "valid ethereum setup",
			options: gateway.Options{
				ApiKey:   "test-key",
				ChainID:  "ethereum",
				DEXProto: "uniswap_v2",
			},
			wantErr: false,
		},
		{
			name: "valid arbitrum setup",
			options: gateway.Options{
				ApiKey:   "test-key",
				ChainID:  "arbitrum",
				DEXProto: "uniswap_v2",
			},
			wantErr: false,
		},
		{
			name: "missing api key",
			options: gateway.Options{
				ChainID:  "ethereum",
				DEXProto: "uniswap_v2",
			},
			wantErr: true,
		},
		{
			name: "unsupported chain",
			options: gateway.Options{
				ApiKey:   "test-key",
				ChainID:  "unsupported",
				DEXProto: "uniswap_v2",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewV2API(tt.options)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestCalculateSpotPrice(t *testing.T) {
	api := &V2API{
		registry: gateway.NewTokenRegistry(gateway.ChainEthereum),
	}

	// Register test tokens
	api.registry.RegisterToken("WETH", "Wrapped Ether", 18, map[gateway.ChainID]string{
		gateway.ChainEthereum: "******************************************",
	})
	api.registry.RegisterToken("USDC", "USD Coin", 6, map[gateway.ChainID]string{
		gateway.ChainEthereum: "******************************************",
	})

	// Create test pool state
	poolState := V2Pool{
		Address:     common.HexToAddress("0x123"),
		Token0:      common.HexToAddress("******************************************"), // WETH
		Token1:      common.HexToAddress("******************************************"), // USDC
		Reserve0:    big.NewInt(1e18),                                                  // 1 WETH
		Reserve1:    big.NewInt(1500e6),                                                // 1500 USDC
		LastUpdated: time.Now(),
	}

	// Create market for WETH/USDC
	market := gateway.Market{
		Exchange: gateway.Exchange{Name: "UniswapV2"},
		Symbol:   "WETH/USDC",
		Pair: gateway.Pair{
			Base:  "WETH",
			Quote: "USDC",
		},
		PriceTick:  0.01,
		AmountTick: 0.00001,
	}

	price, err := api.CalculateSpotPrice(poolState, market)
	assert.NoError(t, err)
	assert.InDelta(t, 1500.0, price, 0.01)

	// Test reverse market (USDC/WETH)
	reverseMarket := gateway.Market{
		Exchange: gateway.Exchange{Name: "UniswapV2"},
		Symbol:   "USDC/WETH",
		Pair: gateway.Pair{
			Base:  "USDC",
			Quote: "WETH",
		},
		PriceTick:  0.00001,
		AmountTick: 0.01,
	}

	reversePrice, err := api.CalculateSpotPrice(poolState, reverseMarket)
	assert.NoError(t, err)
	assert.InDelta(t, 1.0/1500.0, reversePrice, 0.0000001)
}

func TestGeneratePriceLevels(t *testing.T) {
	api := &V2API{
		registry: gateway.NewTokenRegistry(gateway.ChainEthereum),
	}

	pool := V2Pool{
		Token0:   common.HexToAddress("******************************************"), // WETH
		Token1:   common.HexToAddress("******************************************"), // USDC
		Reserve0: new(big.Int).Mul(big.NewInt(10), big.NewInt(1e18)),                // 10 ETH
		Reserve1: new(big.Int).Mul(big.NewInt(18000), big.NewInt(1e6)),              // 18000 USDC
	}

	// Create market for WETH/USDC
	market := gateway.Market{
		Exchange: gateway.Exchange{Name: "UniswapV2"},
		Symbol:   "WETH/USDC",
		Pair: gateway.Pair{
			Base:  "******************************************",
			Quote: "******************************************",
		},
		PriceTick:  0.01,
		AmountTick: 0.00001,
	}

	levels, err := api.GeneratePriceLevels(pool, market)
	assert.NoError(t, err)
	assert.NotEmpty(t, levels)

	// Verify price levels are properly ordered and within reasonable bounds
	for i := 1; i < len(levels); i++ {
		assert.Greater(t, levels[i].Price, levels[i-1].Price)
		assert.Greater(t, levels[i].Amount, 0.0)
	}

	// Test with reverse market (USDC/WETH)
	reverseMarket := gateway.Market{
		Exchange: gateway.Exchange{Name: "UniswapV2"},
		Symbol:   "USDC/WETH",
		Pair: gateway.Pair{
			Base:  "USDC",
			Quote: "WETH",
		},
		PriceTick:  0.00001,
		AmountTick: 0.01,
	}

	reverseLevels, err := api.GeneratePriceLevels(pool, reverseMarket)
	assert.NoError(t, err)
	assert.NotEmpty(t, reverseLevels)

	// Verify reverse price levels
	for i := 1; i < len(reverseLevels); i++ {
		assert.Greater(t, reverseLevels[i].Price, reverseLevels[i-1].Price)
		assert.Greater(t, reverseLevels[i].Amount, 0.0)
	}
}

func TestGetPoolAddress(t *testing.T) {
	api := setupTestAPI(t)

	tests := []struct {
		name    string
		pair    gateway.Pair
		wantErr bool
	}{
		{
			name: "valid pair",
			pair: gateway.Pair{
				Base:  "USDC",
				Quote: "WETH",
			},
			wantErr: false,
		},
		{
			name: "invalid base token",
			pair: gateway.Pair{
				Base:  "INVALID",
				Quote: "USDC",
			},
			wantErr: true,
		},
		{
			name: "invalid quote token",
			pair: gateway.Pair{
				Base:  "WETH",
				Quote: "INVALID",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			addr, err := api.GetPoolAddress(tt.pair, 0)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				fmt.Printf("address: %+v\n", addr)
				assert.NoError(t, err)
				assert.NotEmpty(t, addr)
				assert.NotEqual(t, "******************************************", addr)
			}
		})
	}
}

func TestGetPoolState(t *testing.T) {
	api := setupTestAPI(t)

	tests := []struct {
		name        string
		poolAddr    string
		shouldCache bool
		wantErr     bool
	}{
		{
			name:        "WETH/USDC pool",
			poolAddr:    "******************************************", // Mainnet WETH/USDC pool
			shouldCache: true,
			wantErr:     false,
		},
		{
			name:        "invalid pool address",
			poolAddr:    "******************************************",
			shouldCache: false,
			wantErr:     true,
		},
		{
			name:        "malformed address",
			poolAddr:    "not-an-address",
			shouldCache: false,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// First call to get pool state
			state1, err1 := api.GetPoolState(tt.poolAddr)

			if tt.wantErr {
				assert.Error(t, err1)
				return
			}

			require.NoError(t, err1)
			assert.NotNil(t, state1)
			assert.Equal(t, tt.poolAddr, state1.Address.String())
			assert.NotNil(t, state1.Reserve0)
			assert.NotNil(t, state1.Reserve1)
			assert.NotZero(t, state1.LastUpdated.Unix())

			if tt.shouldCache {
				// Test cache behavior with second call
				state2, err2 := api.GetPoolState(tt.poolAddr)
				require.NoError(t, err2)

				// Basic state should match
				assert.Equal(t, state1.Address, state2.Address)
				assert.Equal(t, state1.Token0, state2.Token0)
				assert.Equal(t, state1.Token1, state2.Token1)

				// Reserves might be different due to real pool updates
				assert.NotNil(t, state2.Reserve0)
				assert.NotNil(t, state2.Reserve1)

				// LastUpdateTime should be more recent or equal
				assert.GreaterOrEqual(t, state2.LastUpdated.Unix(), state1.LastUpdated.Unix())
			}
		})
	}
}

func TestGetPoolStateCache(t *testing.T) {
	api := setupTestAPI(t)
	poolAddr := "******************************************" // WETH/USDC pool

	// Get initial state
	state1, err := api.GetPoolState(poolAddr)
	require.NoError(t, err)

	// Verify cache entry exists
	api.mu.RLock()
	pool, exists := api.poolCache[poolAddr]
	api.mu.RUnlock()
	assert.True(t, exists)
	assert.NotNil(t, pool)

	// Get state again to test cache hit
	state2, err := api.GetPoolState(poolAddr)
	require.NoError(t, err)

	// Basic pool data should be cached and match
	assert.Equal(t, state1.Token0, state2.Token0)
	assert.Equal(t, state1.Token1, state2.Token1)

	// Only reserves and timestamp should potentially differ
	assert.NotNil(t, state2.Reserve0)
	assert.NotNil(t, state2.Reserve1)
	assert.GreaterOrEqual(t, state2.LastUpdated.Unix(), state1.LastUpdated.Unix())
}

func TestSupportedFeeTiers(t *testing.T) {
	api := setupTestAPI(t)
	tiers := api.SupportedFeeTiers()
	assert.Equal(t, []uint64{3000}, tiers) // UniswapV2 only supports 0.3% fee
}
