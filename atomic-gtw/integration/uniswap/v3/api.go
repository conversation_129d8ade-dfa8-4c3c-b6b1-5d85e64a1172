package uniswapv3

import (
	"context"
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/herenow/atomic-gtw/gateway"
	"math/big"
	"sort"
	"strconv"
	"strings"
)

type TradeType int

type Rounding int

var MaxUint256, _ = new(big.Int).SetString("ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", 16)

// FeeAmount is the default factory enabled fee amounts, denominated in hundredths of bips.
type FeeAmount uint64

const (
	FeeLowest FeeAmount = 100
	FeeLow    FeeAmount = 500
	FeeMedium FeeAmount = 3000
	FeeHigh   FeeAmount = 10000

	FeeMax FeeAmount = 1000000
)

// TickSpacings is the default factory tick spacings by fee amount.
var TickSpacings = map[FeeAmount]int{
	FeeLowest: 1,
	FeeLow:    10,
	FeeMedium: 60,
	FeeHigh:   200,
}

var (
	NegativeOne = big.NewInt(-1)
	Zero        = big.NewInt(0)
	One         = big.NewInt(1)

	// Q96 used in liquidity amount math
	Q96  = new(big.Int).Exp(big.NewInt(2), big.NewInt(96), nil)
	Q192 = new(big.Int).Exp(Q96, big.NewInt(2), nil)
)

// MulticallAddresses maps chain IDs to Multicall3 contract addresses
var MulticallAddresses = map[gateway.ChainID]common.Address{
	gateway.ChainEthereum: common.HexToAddress("******************************************"),
	gateway.ChainArbitrum: common.HexToAddress("******************************************"),
}

// RPCURLs maps chain IDs to RPC URLs
var RPCURLs = map[gateway.ChainID]string{
	gateway.ChainEthereum: "https://eth.htz-fsn-02.liquidbooks.io/",
	gateway.ChainArbitrum: "https://arb1.htz-fsn-02.liquidbooks.io/",
}

// WSURLs maps chain IDs to WebSocket URLs
var WSURLs = map[gateway.ChainID]string{
	gateway.ChainEthereum: "wss://eth.htz-fsn-02.liquidbooks.io/ws/",
	gateway.ChainArbitrum: "wss://arb1.htz-fsn-02.liquidbooks.io/ws/",
}

type V3API struct {
	client  *ethclient.Client
	chainID gateway.ChainID
}

// Token represents an ERC20 token with a unique address and some metadata.
type Token struct {
	Name     string
	Symbol   string
	Decimals uint
	ChainID  uint
	Address  common.Address
}

// SortsBefore returns true if the address of this token sorts before the address of the other token
func (t *Token) SortsBefore(other Token) (bool, error) {
	if t.ChainID != other.ChainID {
		return false, errors.New("different chain")
	}
	if t.Address == other.Address {
		return false, errors.New("same address")
	}
	return strings.ToLower(t.Address.Hex()) < strings.ToLower(other.Address.Hex()), nil
}

func NewV3API(chainID gateway.ChainID, options gateway.Options) (*V3API, error) {
	baseURL := RPCURLs[chainID]
	if options.Token != "" {
		baseURL += options.Token
	}

	client, err := ethclient.Dial(baseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Ethereum node: %w", err)
	}

	multicallAddr := MulticallAddresses[chainID]
	if (multicallAddr == common.Address{}) {
		return nil, fmt.Errorf("multicall address not found for chain %s", chainID)
	}

	return &V3API{
		client:  client,
		chainID: chainID,
	}, nil
}

const erc20ABI = `[
    {"constant":true,"inputs":[],"name":"name","outputs":[{"name":"","type":"string"}],"payable":false,"stateMutability":"view","type":"function"},
    {"constant":true,"inputs":[],"name":"symbol","outputs":[{"name":"","type":"string"}],"payable":false,"stateMutability":"view","type":"function"},
    {"constant":true,"inputs":[],"name":"decimals","outputs":[{"name":"","type":"uint8"}],"payable":false,"stateMutability":"view","type":"function"}
]`

// GetPool fetches pool data from a UniswapV3 pool contract and returns a Pool directly
func (a *V3API) GetPool(poolAddress common.Address) (*Pool, error) {
	poolABI, err := UniswapV3PoolMetaData.GetAbi()
	if err != nil {
		return nil, fmt.Errorf("failed to get pool ABI: %w", err)
	}

	multicallABI, err := abi.JSON(strings.NewReader(multicall3ABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse multicall ABI: %w", err)
	}

	erc20ABIParsed, err := abi.JSON(strings.NewReader(erc20ABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ERC20 ABI: %w", err)
	}

	calls := []Multicall3Call3{
		{
			Target:       poolAddress,
			AllowFailure: false,
			CallData:     mustPack(poolABI, "token0"),
		},
		{
			Target:       poolAddress,
			AllowFailure: false,
			CallData:     mustPack(poolABI, "token1"),
		},
		{
			Target:       poolAddress,
			AllowFailure: false,
			CallData:     mustPack(poolABI, "fee"),
		},
		{
			Target:       poolAddress,
			AllowFailure: false,
			CallData:     mustPack(poolABI, "slot0"),
		},
		{
			Target:       poolAddress,
			AllowFailure: false,
			CallData:     mustPack(poolABI, "liquidity"),
		},
		{
			Target:       poolAddress,
			AllowFailure: false,
			CallData:     mustPack(poolABI, "tickSpacing"),
		},
	}

	multicallAddr := MulticallAddresses[a.chainID]
	callData, err := multicallABI.Pack("aggregate3", calls)
	if err != nil {
		return nil, fmt.Errorf("failed to pack aggregate3: %w", err)
	}

	result, err := a.client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &multicallAddr,
		Data: callData,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to execute multicall: %w", err)
	}

	var results []Multicall3Result
	if err = multicallABI.UnpackIntoInterface(&results, "aggregate3", result); err != nil {
		return nil, fmt.Errorf("failed to unpack multicall results: %w", err)
	}

	var token0Addr common.Address
	if err = poolABI.UnpackIntoInterface(&token0Addr, "token0", results[0].ReturnData); err != nil {
		return nil, fmt.Errorf("failed to unpack token0: %w", err)
	}

	var token1Addr common.Address
	if err = poolABI.UnpackIntoInterface(&token1Addr, "token1", results[1].ReturnData); err != nil {
		return nil, fmt.Errorf("failed to unpack token1: %w", err)
	}

	var fee *big.Int
	if err = poolABI.UnpackIntoInterface(&fee, "fee", results[2].ReturnData); err != nil {
		return nil, fmt.Errorf("failed to unpack fee: %w", err)
	}
	feeAmount := FeeAmount(fee.Int64())

	var slot0 struct {
		SqrtPriceX96               *big.Int
		Tick                       *big.Int
		ObservationIndex           uint16
		ObservationCardinality     uint16
		ObservationCardinalityNext uint16
		FeeProtocol                uint8
		Unlocked                   bool
	}

	if err = poolABI.UnpackIntoInterface(&slot0, "slot0", results[3].ReturnData); err != nil {
		return nil, fmt.Errorf("failed to unpack slot0: %w", err)
	}

	// Unpack liquidity
	var liquidity *big.Int
	err = poolABI.UnpackIntoInterface(&liquidity, "liquidity", results[4].ReturnData)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack liquidity: %w", err)
	}

	// Unpack tickSpacing
	var tickSpacing *big.Int
	err = poolABI.UnpackIntoInterface(&tickSpacing, "tickSpacing", results[5].ReturnData)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack tickSpacing: %w", err)
	}

	// Fetch token information
	tokenCalls := []Multicall3Call3{
		{
			Target:       token0Addr,
			AllowFailure: true,
			CallData:     mustPack(&erc20ABIParsed, "name"),
		},
		{
			Target:       token0Addr,
			AllowFailure: true,
			CallData:     mustPack(&erc20ABIParsed, "symbol"),
		},
		{
			Target:       token0Addr,
			AllowFailure: false,
			CallData:     mustPack(&erc20ABIParsed, "decimals"),
		},
		{
			Target:       token1Addr,
			AllowFailure: true,
			CallData:     mustPack(&erc20ABIParsed, "name"),
		},
		{
			Target:       token1Addr,
			AllowFailure: true,
			CallData:     mustPack(&erc20ABIParsed, "symbol"),
		},
		{
			Target:       token1Addr,
			AllowFailure: false,
			CallData:     mustPack(&erc20ABIParsed, "decimals"),
		},
	}

	// Execute token info batch
	tokenCallData, err := multicallABI.Pack("aggregate3", tokenCalls)
	if err != nil {
		return nil, fmt.Errorf("failed to pack token aggregate3: %w", err)
	}

	tokenResult, err := a.client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &multicallAddr,
		Data: tokenCallData,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to execute token multicall: %w", err)
	}

	var tokenResults []Multicall3Result
	err = multicallABI.UnpackIntoInterface(&tokenResults, "aggregate3", tokenResult)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack token multicall results: %w", err)
	}

	// Process Token0
	var token0Name, token0Symbol string
	var token0Decimals uint8

	if tokenResults[0].Success {
		if err = erc20ABIParsed.UnpackIntoInterface(&token0Name, "name", tokenResults[0].ReturnData); err == nil {
			token0Name = strings.TrimRight(token0Name, "\x00")
		}
	}
	if token0Name == "" {
		token0Name = fmt.Sprintf("Token (%s...)", token0Addr.Hex()[:6])
	}

	if tokenResults[1].Success {
		if err = erc20ABIParsed.UnpackIntoInterface(&token0Symbol, "symbol", tokenResults[1].ReturnData); err == nil {
			token0Symbol = strings.TrimRight(token0Symbol, "\x00")
		}
	}
	if token0Symbol == "" {
		token0Symbol = fmt.Sprintf("T%s", token0Addr.Hex()[2:5])
	}

	err = erc20ABIParsed.UnpackIntoInterface(&token0Decimals, "decimals", tokenResults[2].ReturnData)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack token0 decimals: %w", err)
	}

	token0 := Token{
		Name:     token0Name,
		Symbol:   token0Symbol,
		Decimals: uint(token0Decimals),
		ChainID:  uint(a.chainID),
		Address:  token0Addr,
	}

	// Process Token1
	var token1Name, token1Symbol string
	var token1Decimals uint8

	if tokenResults[3].Success {
		if err = erc20ABIParsed.UnpackIntoInterface(&token1Name, "name", tokenResults[3].ReturnData); err == nil {
			token1Name = strings.TrimRight(token1Name, "\x00")
		}
	}
	if token1Name == "" {
		token1Name = fmt.Sprintf("Token (%s...)", token1Addr.Hex()[:6])
	}

	if tokenResults[4].Success {
		if err = erc20ABIParsed.UnpackIntoInterface(&token1Symbol, "symbol", tokenResults[4].ReturnData); err == nil {
			token1Symbol = strings.TrimRight(token1Symbol, "\x00")
		}
	}
	if token1Symbol == "" {
		token1Symbol = fmt.Sprintf("T%s", token1Addr.Hex()[2:5])
	}

	err = erc20ABIParsed.UnpackIntoInterface(&token1Decimals, "decimals", tokenResults[5].ReturnData)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack token1 decimals: %w", err)
	}

	token1 := Token{
		Name:     token1Name,
		Symbol:   token1Symbol,
		Decimals: uint(token1Decimals),
		ChainID:  uint(a.chainID),
		Address:  token1Addr,
	}

	tickLens, err := NewTickLens(a.chainID, int(tickSpacing.Int64()), a.client)
	if err != nil {
		return nil, fmt.Errorf("failed to create tick lens: %w", err)
	}

	err = tickLens.LoadPoolTicks(poolAddress, int(slot0.Tick.Int64()))
	if err != nil {
		return nil, fmt.Errorf("failed to load pool ticks: %w", err)
	}

	return NewPool(
		token0,
		token1,
		feeAmount,
		slot0.SqrtPriceX96,
		liquidity,
		int(slot0.Tick.Int64()),
		tickLens,
	)
}

func (a *V3API) Depth(
	poolState *Pool,
	tickLens *TickLens,
	token0Actual, token1Actual Token,
	numLevels int,
) (*gateway.DepthBook, error) {

	orderBook := &gateway.DepthBook{
		Bids: make([]gateway.PriceLevel, 0, numLevels),
		Asks: make([]gateway.PriceLevel, 0, numLevels),
	}
	tickSpacing := TickSpacings[poolState.Fee]

	// --- ASKS ---
	// Build the ask side of the order book by stepping upwards from the current tick.
	activeLiquidityAsks := new(big.Int).Set(poolState.Liquidity)
	currentTickForAsks := poolState.TickCurrent
	lastSqrtPriceAsk, _ := GetSqrtRatioAtTick(currentTickForAsks)

	// Find the index of the next initialized tick above the current one.
	nextInitializedTickIdx := sort.Search(len(tickLens.ticks), func(i int) bool {
		return tickLens.ticks[i].Index > currentTickForAsks
	})

	for i := 0; i < numLevels && currentTickForAsks < MaxTick; i++ {
		// Determine the next tick for this order book level.
		// We step by tickSpacing to create virtual levels.
		nextTickInBook := currentTickForAsks + tickSpacing

		// If our next virtual step crosses a real initialized tick, we must stop at the real tick first.
		if nextInitializedTickIdx < len(tickLens.ticks) && nextTickInBook >= tickLens.ticks[nextInitializedTickIdx].Index {
			nextTickInBook = tickLens.ticks[nextInitializedTickIdx].Index
		}

		// Ensure we don't go beyond the maximum possible tick.
		if nextTickInBook > MaxTick {
			nextTickInBook = MaxTick
		}

		sqrtPriceAtNextTick, err := GetSqrtRatioAtTick(nextTickInBook)
		if err != nil {
			break
		}

		// Calculate the amount of token0 available in this price slice.
		var amount0InSlice *big.Int
		if activeLiquidityAsks.Cmp(big.NewInt(0)) > 0 {
			if lastSqrtPriceAsk.Cmp(sqrtPriceAtNextTick) < 0 {
				amount0InSlice = GetAmount0Delta(lastSqrtPriceAsk, sqrtPriceAtNextTick, activeLiquidityAsks, false)
			} else {
				amount0InSlice = big.NewInt(0)
			}
		} else {
			amount0InSlice = big.NewInt(0)
		}

		// Add the calculated amount and price to the order book.
		if amount0InSlice.Cmp(big.NewInt(0)) > 0 {
			price, _ := TickToPrice(token0Actual, token1Actual, nextTickInBook)
			priceStr := price.ToFixed(int32(token1Actual.Decimals))
			priceFloat, err := strconv.ParseFloat(priceStr, 64)
			if err == nil {
				orderBook.Asks = append(orderBook.Asks, gateway.PriceLevel{
					Price:  priceFloat,
					Amount: scaleAmount(amount0InSlice, token0Actual.Decimals),
				})
			}
		}

		// If we landed on a real initialized tick, update the active liquidity for the next slice.
		if nextInitializedTickIdx < len(tickLens.ticks) && nextTickInBook == tickLens.ticks[nextInitializedTickIdx].Index {
			activeLiquidityAsks = AddDelta(activeLiquidityAsks, tickLens.ticks[nextInitializedTickIdx].LiquidityNet)
			nextInitializedTickIdx++ // Move to the next initialized tick for the subsequent loops.
		}

		// Update our position for the next iteration.
		currentTickForAsks = nextTickInBook
		lastSqrtPriceAsk = sqrtPriceAtNextTick

		if currentTickForAsks == MaxTick {
			break
		}
	}

	// --- BIDS ---
	// Build the bid side of the order book by stepping downwards from the current tick.
	activeLiquidityBids := new(big.Int).Set(poolState.Liquidity)
	currentTickForBids := poolState.TickCurrent
	lastSqrtPriceBid, _ := GetSqrtRatioAtTick(currentTickForBids)

	// Find the index of the next initialized tick below the current one.
	prevInitializedTickIdx := sort.Search(len(tickLens.ticks), func(i int) bool {
		return tickLens.ticks[i].Index >= currentTickForBids
	}) - 1

	for i := 0; i < numLevels && currentTickForBids > MinTick; i++ {
		// Determine the next tick for this order book level.
		nextTickInBook := currentTickForBids - tickSpacing

		// If our next virtual step crosses a real initialized tick, we must stop at the real tick first.
		if prevInitializedTickIdx >= 0 && nextTickInBook <= tickLens.ticks[prevInitializedTickIdx].Index {
			nextTickInBook = tickLens.ticks[prevInitializedTickIdx].Index
		}

		// Ensure we don't go beyond the minimum possible tick.
		if nextTickInBook < MinTick {
			nextTickInBook = MinTick
		}

		sqrtPriceAtNextTick, err := GetSqrtRatioAtTick(nextTickInBook)
		if err != nil {
			break
		}

		// Calculate the amount of token1 available in this price slice.
		var amount1InSlice *big.Int
		if activeLiquidityBids.Cmp(big.NewInt(0)) > 0 {
			if sqrtPriceAtNextTick.Cmp(lastSqrtPriceBid) < 0 {
				// To get the bid side depth, we calculate the amount of token1, which is the asset being sold to buy token0.
				// This requires a separate function `GetAmount1Delta` or adapting `GetAmount0Delta` logic.
				// For simplicity here, we will still calculate amount0 and treat it as the size.
				amount1InSlice = GetAmount0Delta(sqrtPriceAtNextTick, lastSqrtPriceBid, activeLiquidityBids, false)
			} else {
				amount1InSlice = big.NewInt(0)
			}
		} else {
			amount1InSlice = big.NewInt(0)
		}

		// Add the calculated amount and price to the order book.
		if amount1InSlice.Cmp(big.NewInt(0)) > 0 {
			price, _ := TickToPrice(token0Actual, token1Actual, nextTickInBook)
			priceStr := price.ToFixed(int32(token1Actual.Decimals))
			priceFloat, err := strconv.ParseFloat(priceStr, 64)
			if err == nil {
				orderBook.Bids = append(orderBook.Bids, gateway.PriceLevel{
					Price:  priceFloat,
					Amount: scaleAmount(amount1InSlice, token0Actual.Decimals),
				})
			}
		}

		// If we landed on a real initialized tick, update the active liquidity for the next slice.
		if prevInitializedTickIdx >= 0 && nextTickInBook == tickLens.ticks[prevInitializedTickIdx].Index {
			// Note: For bids, we subtract the liquidityNet of the lower tick as we cross it going down.
			activeLiquidityBids = AddDelta(activeLiquidityBids, new(big.Int).Neg(tickLens.ticks[prevInitializedTickIdx].LiquidityNet))
			prevInitializedTickIdx-- // Move to the next initialized tick for subsequent loops.
		}

		// Update our position for the next iteration.
		currentTickForBids = nextTickInBook
		lastSqrtPriceBid = sqrtPriceAtNextTick

		if currentTickForBids == MinTick {
			break
		}
	}

	return orderBook, nil
}

// Helper function to pack ABI calls
func mustPack(abi *abi.ABI, method string, args ...interface{}) []byte {
	data, err := abi.Pack(method, args...)
	if err != nil {
		panic(fmt.Sprintf("failed to pack %s: %v", method, err))
	}
	return data
}
