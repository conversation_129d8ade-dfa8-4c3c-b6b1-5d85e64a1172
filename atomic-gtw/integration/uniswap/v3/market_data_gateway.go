package uniswapv3

import (
	"context"
	"fmt"
	"log"
	"math"
	"math/big"
	"sort"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/herenow/atomic-gtw/gateway"
)

type MarketDataGateway struct {
	api           *V3API
	wsClient      *ethclient.Client
	tickCh        chan gateway.Tick
	options       gateway.Options
	markets       []gateway.Market
	pools         map[string]*Pool
	subscriptions map[string][]ethereum.Subscription // Store active subscriptions per market
	mu            sync.Mutex                         // To protect shared data like pools, poolStates, Tick<PERSON><PERSON>
	quit          chan bool
}

func NewMarketDataGateway(api *V3API, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		api:           api,
		tickCh:        tickCh,
		options:       options,
		pools:         make(map[string]*Pool),
		subscriptions: make(map[string][]ethereum.Subscription),
		quit:          make(chan bool),
	}
}

// connectWebsocket establishes a new WebSocket client connection.
func (g *MarketDataGateway) connectWebsocket(chainID gateway.ChainID) error {
	wsURL, ok := WSURLs[chainID]
	if !ok {
		return fmt.Errorf("websocket URL not found for chain ID: %s", chainID)
	}

	if g.options.Token != "" {
		wsURL += g.options.Token
	}

	client, err := ethclient.DialContext(context.Background(), wsURL)
	if err != nil {
		return fmt.Errorf("failed to connect to WebSocket endpoint %s: %w", wsURL, err)
	}

	g.wsClient = client

	return nil
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	g.markets = markets

	if len(markets) > 0 {
		chainID := g.api.chainID
		if err := g.connectWebsocket(chainID); err != nil {
			log.Panicf("failed to connect WebSocket: %v", err)
		}
	}

	for _, market := range markets {
		if err := g.initializePool(market); err != nil {
			return fmt.Errorf("failed to initialize pool for market %s: %w", market.Symbol, err)
		}
		go g.subscribeToPoolEvents(market)
	}

	return nil
}

func (g *MarketDataGateway) initializePool(market gateway.Market) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	poolAddr := common.HexToAddress(market.Symbol)

	pool, err := g.api.GetPool(poolAddr)
	if err != nil {
		return fmt.Errorf("failed to get pool: %w", err)
	}

	g.pools[market.Symbol] = pool

	g.sendOrderBookSnapshot(market, pool)

	return nil
}

func (g *MarketDataGateway) subscribeToPoolEvents(market gateway.Market) {
	filterer, err := NewUniswapV3PoolFilterer(common.HexToAddress(market.Symbol), g.wsClient)
	if err != nil {
		log.Printf("Failed to create pool filterer for %s: %v. No WebSocket events.", market.Symbol, err)
		return
	}

	var marketSubscriptions []ethereum.Subscription

	// Swap Events
	swapEvents := make(chan *UniswapV3PoolSwap)
	swapSub, err := filterer.WatchSwap(&bind.WatchOpts{Context: context.Background()}, swapEvents, nil, nil)
	if err != nil {
		log.Panicf("Failed to subscribe to Swap events for %s: %v", market.Symbol, err)
	} else {
		marketSubscriptions = append(marketSubscriptions, swapSub)
		go func() {
			for {
				select {
				case event := <-swapEvents:
					g.handleSwapEvent(market, event)
				case err = <-swapSub.Err():
					log.Panicf("Swap event subscription error for %s: %v.", market.Symbol, err)
				case <-g.quit:
					log.Printf("Closing Swap event subscription for %s", market.Symbol)
					return
				}
			}
		}()
	}

	// Mint Events
	mintEvents := make(chan *UniswapV3PoolMint)
	mintSub, err := filterer.WatchMint(&bind.WatchOpts{Context: context.Background()}, mintEvents, nil, nil, nil)
	if err != nil {
		log.Panicf("Failed to subscribe to Mint events for %s: %v", market.Symbol, err)
	} else {
		marketSubscriptions = append(marketSubscriptions, mintSub)
		go func() {
			for {
				select {
				case event := <-mintEvents:
					g.handleMintEvent(market, event)
				case err = <-mintSub.Err():
					log.Panicf("Mint event subscription error for %s: %v", market.Symbol, err)
				case <-g.quit:
					log.Printf("Closing Mint event subscription for %s", market.Symbol)
					return
				}
			}
		}()
	}

	// Burn Events
	burnEvents := make(chan *UniswapV3PoolBurn)
	burnSub, err := filterer.WatchBurn(&bind.WatchOpts{Context: context.Background()}, burnEvents, nil, nil, nil)
	if err != nil {
		log.Panicf("Failed to subscribe to Burn events for %s: %v", market.Symbol, err)
	} else {
		marketSubscriptions = append(marketSubscriptions, burnSub)
		go func() {
			for {
				select {
				case event := <-burnEvents:
					g.handleBurnEvent(market, event)
				case err = <-burnSub.Err():
					log.Panicf("Burn event subscription error for %s: %v", market.Symbol, err)
				case <-g.quit:
					log.Printf("Closing Burn event subscription for %s", market.Symbol)
					return
				}
			}
		}()
	}
	g.mu.Lock()
	g.subscriptions[market.Symbol] = marketSubscriptions
	g.mu.Unlock()
}

func (g *MarketDataGateway) handleSwapEvent(market gateway.Market, event *UniswapV3PoolSwap) {
	g.mu.Lock()
	defer g.mu.Unlock()

	pool, ok := g.pools[market.Symbol]
	if !ok {
		log.Printf("Swap event for unknown pool: %s", market.Symbol)
		return
	}

	pool.SqrtPriceX96 = event.SqrtPriceX96
	pool.Liquidity = event.Liquidity
	pool.TickCurrent = int(event.Tick.Int64())

	g.sendOrderBookUpdate(market, pool)

	g.sendTradeEvent(market, pool, event)
}

func (g *MarketDataGateway) sendTradeEvent(market gateway.Market, pool *Pool, event *UniswapV3PoolSwap) {
	amount0 := new(big.Int).Set(event.Amount0)
	amount1 := new(big.Int).Set(event.Amount1)

	var tradeAmount float64
	var tradePrice float64
	var direction gateway.Side

	// If amount0 is negative, token0 was sold (ask/sell)
	// If amount0 is positive, token0 was bought (bid/buy)
	if amount0.Sign() < 0 {
		direction = gateway.Ask
		amount0.Neg(amount0)
		tradeAmount = scaleAmount(amount0, pool.Token0.Decimals)

		if amount0.Sign() > 0 {
			price := new(big.Float).Quo(
				new(big.Float).SetInt(amount1),
				new(big.Float).SetInt(amount0),
			)

			decimalAdjustment := new(big.Float).SetFloat64(
				math.Pow(10, float64(pool.Token0.Decimals)-float64(pool.Token1.Decimals)),
			)
			price.Mul(price, decimalAdjustment)

			tradePrice, _ = price.Float64()
		}
	} else {
		direction = gateway.Bid
		tradeAmount = scaleAmount(amount0, pool.Token0.Decimals)
		amount1.Neg(amount1)

		if amount0.Sign() > 0 {
			price := new(big.Float).Quo(
				new(big.Float).SetInt(amount1),
				new(big.Float).SetInt(amount0),
			)

			decimalAdjustment := new(big.Float).SetFloat64(
				math.Pow(10, float64(pool.Token0.Decimals)-float64(pool.Token1.Decimals)),
			)
			price.Mul(price, decimalAdjustment)

			tradePrice, _ = price.Float64()
		}
	}

	trade := gateway.Trade{
		Timestamp: time.Now(),
		Symbol:    market.Symbol,
		ID:        event.Raw.TxHash.Hex(),
		Direction: direction,
		Price:     tradePrice,
		Amount:    tradeAmount,
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog: []gateway.Event{
			{
				Type: gateway.TradeEvent,
				Data: trade,
			},
		},
	}
}

func (g *MarketDataGateway) handleMintEvent(market gateway.Market, event *UniswapV3PoolMint) {
	g.mu.Lock()
	defer g.mu.Unlock()

	pool, ok := g.pools[market.Symbol]
	if !ok {
		log.Printf("Mint event for unknown pool: %s", market.Symbol)
		return
	}

	tickLower := int(event.TickLower.Int64())
	tickUpper := int(event.TickUpper.Int64())
	amount := event.Amount

	// Update overall pool liquidity if current tick is within the minted range
	if pool.TickCurrent >= tickLower && pool.TickCurrent < tickUpper {
		pool.Liquidity = new(big.Int).Add(pool.Liquidity, amount)
	}

	// Update TickLens
	if pool.TickLens != nil {
		// For tickLower: LiquidityNet increases, LiquidityGross increases
		g.updateTickInLens(pool.TickLens, tickLower, amount, amount, true)
		// For tickUpper: LiquidityNet decreases, LiquidityGross increases
		g.updateTickInLens(pool.TickLens, tickUpper, new(big.Int).Neg(amount), amount, true)
	} else {
		log.Printf("Warning: TickLens is nil for pool %s during Mint event. Cannot update tick liquidity.", market.Symbol)
	}

	g.sendOrderBookUpdate(market, pool)
}

func (g *MarketDataGateway) handleBurnEvent(market gateway.Market, event *UniswapV3PoolBurn) {
	g.mu.Lock()
	defer g.mu.Unlock()

	pool, ok := g.pools[market.Symbol]
	if !ok {
		log.Printf("Burn event for unknown pool: %s", market.Symbol)
		return
	}

	tickLower := int(event.TickLower.Int64())
	tickUpper := int(event.TickUpper.Int64())
	amount := event.Amount

	// Update overall pool liquidity if current tick is within the burned range
	if pool.TickCurrent >= tickLower && pool.TickCurrent < tickUpper {
		pool.Liquidity = new(big.Int).Sub(pool.Liquidity, amount)
		if pool.Liquidity.Cmp(big.NewInt(0)) < 0 { // Should not happen in a healthy pool
			pool.Liquidity = big.NewInt(0)
		}
	}

	// Update TickLens
	if pool.TickLens != nil {
		// For tickLower: LiquidityNet decreases, LiquidityGross decreases
		g.updateTickInLens(pool.TickLens, tickLower, new(big.Int).Neg(amount), new(big.Int).Neg(amount), false)
		// For tickUpper: LiquidityNet increases, LiquidityGross decreases
		g.updateTickInLens(pool.TickLens, tickUpper, amount, new(big.Int).Neg(amount), false)
	} else {
		log.Panicf("Warning: TickLens is nil for pool %s during Burn event. Cannot update tick liquidity.", market.Symbol)
	}

	g.sendOrderBookUpdate(market, pool)
}

// updateTickInLens updates a specific tick's liquidity in the TickLens, or adds it if new.
func (g *MarketDataGateway) updateTickInLens(tickLensInstance *TickLens, tickIndex int, netChange *big.Int, grossChange *big.Int, isNewTickPossible bool) {
	if tickLensInstance == nil {
		log.Printf("Error: tickLensInstance is nil for tick %d", tickIndex)
		return
	}
	found := false
	for i := range tickLensInstance.ticks {
		if tickLensInstance.ticks[i].Index == tickIndex {
			tickLensInstance.ticks[i].LiquidityNet = new(big.Int).Add(tickLensInstance.ticks[i].LiquidityNet, netChange)
			tickLensInstance.ticks[i].LiquidityGross = new(big.Int).Add(tickLensInstance.ticks[i].LiquidityGross, grossChange)
			// Ensure gross liquidity doesn't go negative (it shouldn't with correct logic but as a safeguard)
			if tickLensInstance.ticks[i].LiquidityGross.Cmp(big.NewInt(0)) < 0 {
				log.Printf("Warning: Tick %d gross liquidity became negative (%s), setting to 0.", tickIndex, tickLensInstance.ticks[i].LiquidityGross.String())
				tickLensInstance.ticks[i].LiquidityGross = big.NewInt(0)
			}
			found = true
			break
		}
	}

	if !found && isNewTickPossible && grossChange.Cmp(big.NewInt(0)) > 0 { // Only add if it's a mint for a new tick
		newTick := Tick{
			Index:          tickIndex,
			LiquidityNet:   netChange,
			LiquidityGross: grossChange,
		}
		// Insert newTick into tickLensInstance.ticks while maintaining sorted order by Index.
		idxToInsert := sort.Search(len(tickLensInstance.ticks), func(k int) bool {
			return tickLensInstance.ticks[k].Index >= tickIndex
		})

		// Expand slice to make room for new element
		tickLensInstance.ticks = append(tickLensInstance.ticks, Tick{})
		// Shift elements to the right
		copy(tickLensInstance.ticks[idxToInsert+1:], tickLensInstance.ticks[idxToInsert:])
		// Insert the new tick
		tickLensInstance.ticks[idxToInsert] = newTick
	} else if !found {
		log.Printf("Warning: Mint/Burn event for tick %d not found in TickLens and not added (isNewTickPossible: %v, grossChange: %s). Tick data might be stale until next TickLens refresh.", tickIndex, isNewTickPossible, grossChange.String())
	}
}

// sendOrderBookSnapshot should use the locked pool data if called from a locked context
// For initialization, it's fine.
func (g *MarketDataGateway) sendOrderBookSnapshot(market gateway.Market, poolInstance *Pool) {
	bids, asks := g.generateOrderBookFromPool(market, poolInstance)

	eventLog := []gateway.Event{
		{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: market.Symbol,
			},
		},
	}

	for _, bid := range bids {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Bid,
				Price:  bid.Price,
				Amount: bid.Amount,
			},
		})
	}

	for _, ask := range asks {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Ask,
				Price:  ask.Price,
				Amount: ask.Amount,
			},
		})
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

// sendOrderBookUpdate must use the pool data that's consistent with the event/update
// It's called within a g.mu.Lock() critical section, so accessing g.pools and g.poolStates is safe.
func (g *MarketDataGateway) sendOrderBookUpdate(market gateway.Market, poolInstance *Pool) {
	bids, asks := g.generateOrderBookFromPool(market, poolInstance) // Pass the specific pool instance

	var eventLog []gateway.Event

	// For updates, we usually send all levels.
	// A more advanced implementation might send diffs, but full book is simpler here.
	// To signal a full book update, some protocols use a special sequence or clear event first.
	// Here, we just send all current levels.
	eventLog = append(eventLog, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: market.Symbol,
		},
	})

	for _, bid := range bids {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Bid,
				Price:  bid.Price,
				Amount: bid.Amount,
			},
		})
	}

	for _, ask := range asks {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Ask,
				Price:  ask.Price,
				Amount: ask.Amount,
			},
		})
	}

	if len(eventLog) > 1 { // Check if there are actual depth events
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          eventLog,
		}
	}
}

// generateOrderBookFromPool now takes a specific pool instance to ensure consistency
func (g *MarketDataGateway) generateOrderBookFromPool(market gateway.Market, pool *Pool) ([]gateway.PriceLevel, []gateway.PriceLevel) {
	// This function is called within a g.mu.Lock() from handlers, or with a consistent pool instance.
	if pool == nil {
		log.Panicf("Pool not found for market %s during order book generation", market.Symbol)
	}
	// State is implicitly pool.Liquidity, pool.TickCurrent, pool.SqrtPriceX96 and pool.TickLens
	// which are all now updated by WS events before this is called.

	if pool.TickLens == nil || len(pool.TickLens.ticks) == 0 {
		log.Panicf("TickLens data not available or empty for market %s. Order book cannot be generated.", market.Symbol)
	}

	depthBook, err := g.api.Depth(
		pool,
		pool.TickLens,
		pool.Token0,
		pool.Token1,
		10, // Number of levels, make configurable if needed
	)
	if err != nil {
		log.Printf("Failed to build order book for market %s: %v", market.Symbol, err)
		return nil, nil
	}

	// Sort the price levels before returning
	gateway.PriceLevelsSortBids(depthBook.Bids)
	gateway.PriceLevelsSortAsks(depthBook.Asks)

	return depthBook.Bids, depthBook.Asks
}

func scaleAmount(amount *big.Int, decimals uint) float64 {
	if amount == nil {
		return 0.0
	}
	divisor := new(big.Float).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimals)), nil))
	amountFloat := new(big.Float).SetInt(amount)
	result, _ := new(big.Float).Quo(amountFloat, divisor).Float64()
	return result
}

func (g *MarketDataGateway) Close() error {
	close(g.quit)

	g.mu.Lock()
	defer g.mu.Unlock()

	for marketSymbol, subs := range g.subscriptions {
		for _, sub := range subs {
			sub.Unsubscribe()
		}
		log.Printf("Unsubscribed from events for market %s", marketSymbol)
	}
	g.subscriptions = make(map[string][]ethereum.Subscription)

	if g.wsClient != nil {
		g.wsClient.Close()
		g.wsClient = nil
	}
	return nil
}
