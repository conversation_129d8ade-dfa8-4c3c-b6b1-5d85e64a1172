package uniswapv3

import (
	"errors"
	"github.com/ethereum/go-ethereum/common"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/shopspring/decimal"
	"math/big"
)

// PoolManagerAddresses maps chain IDs to official Uniswap PoolManager contract addresses
var PoolManagerAddresses = map[gateway.ChainID]common.Address{
	gateway.ChainEthereum: common.HexToAddress("******************************************"),
	gateway.ChainArbitrum: common.HexToAddress("******************************************"),
}

// StateViewAddresses maps chain IDs to official Uniswap StateView contract addresses
var StateViewAddresses = map[gateway.ChainID]common.Address{
	gateway.ChainEthereum: common.HexToAddress("******************************************"),
	gateway.ChainArbitrum: common.HexToAddress("******************************************"),
}

var (
	ErrFeeTooHigh          = errors.New("fee too high")
	ErrInvalidSqrtRatioX96 = errors.New("invalid sqrtRatioX96")
)

// Pool Represents a V3 pool
type Pool struct {
	Token0       Token
	Token1       Token
	Fee          FeeAmount
	SqrtPriceX96 *big.Int
	Liquidity    *big.Int
	TickCurrent  int
	TickLens     *TickLens
}

// NewPool Construct a pool
func NewPool(tokenA, tokenB Token, fee FeeAmount, sqrtRatioX96 *big.Int, liquidity *big.Int, tickCurrent int, tickLens *TickLens) (*Pool, error) {
	if fee >= FeeMax {
		return nil, ErrFeeTooHigh
	}

	tickCurrentSqrtRatioX96, err := GetSqrtRatioAtTick(tickCurrent)
	if err != nil {
		return nil, err
	}
	nextTickSqrtRatioX96, err := GetSqrtRatioAtTick(tickCurrent + 1)
	if err != nil {
		return nil, err
	}

	if sqrtRatioX96.Cmp(tickCurrentSqrtRatioX96) < 0 || sqrtRatioX96.Cmp(nextTickSqrtRatioX96) > 0 {
		return nil, ErrInvalidSqrtRatioX96
	}
	token0 := tokenA
	token1 := tokenB
	isSorted, err := tokenA.SortsBefore(tokenB)
	if err != nil {
		return nil, err
	}
	if !isSorted {
		token0 = tokenB
		token1 = tokenA
	}

	return &Pool{
		Token0:       token0,
		Token1:       token1,
		Fee:          fee,
		SqrtPriceX96: sqrtRatioX96,
		Liquidity:    liquidity,
		TickCurrent:  tickCurrent,
		TickLens:     tickLens,
	}, nil
}

type Price struct {
	*Fraction
	Base   Token
	Quote  Token
	Scalar *Fraction // used to adjust the raw fraction w/r/t the decimals of the {base,quote}Token
}

// NewPrice construct a price, either with the base and quote currency amount, or the args
func NewPrice(base, quote Token, denominator, numerator *big.Int) *Price {
	return &Price{
		Fraction: NewFraction(numerator, denominator),
		Base:     base,
		Quote:    quote,
		Scalar: NewFraction(
			new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(base.Decimals)), nil),
			new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(quote.Decimals)), nil)),
	}
}

func (p *Price) ToFixed(decimalPlaces int32) string {
	return p.Fraction.Multiply(p.Scalar).ToFixed(decimalPlaces)
}

type Fraction struct {
	Numerator   *big.Int
	Denominator *big.Int
}

// NewFraction creates a new fraction
func NewFraction(numerator, denominator *big.Int) *Fraction {
	return &Fraction{
		Numerator:   numerator,
		Denominator: denominator,
	}
}

// Multiply multiplies two fractions
func (f *Fraction) Multiply(other *Fraction) *Fraction {
	return NewFraction(new(big.Int).Mul(f.Numerator, other.Numerator), new(big.Int).Mul(f.Denominator, other.Denominator))
}

// ToFixed returns a fixed string representation of the fraction
func (f *Fraction) ToFixed(decimalPlaces int32) string {
	return decimal.NewFromBigInt(f.Numerator, 0).Div(decimal.NewFromBigInt(f.Denominator, 0)).StringFixed(decimalPlaces)
}
