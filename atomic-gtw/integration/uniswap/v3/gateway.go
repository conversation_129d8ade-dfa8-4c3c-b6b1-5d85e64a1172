package uniswapv3

import (
	"fmt"
	"github.com/ethereum/go-ethereum/common"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

func NewExchange(chainID gateway.ChainID) gateway.Exchange {
	return gateway.Exchange{
		Name:    "UniswapV3",
		ChainID: chainID,
	}
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *MarketDataGateway
	api               *V3API
	tickCh            chan gateway.Tick
	exchange          gateway.Exchange
}

func NewGateway(chainID gateway.ChainID) gateway.NewGateway {
	return func(options gateway.Options) gateway.Gateway {
		gtw := &Gateway{
			options:  options,
			tickCh:   make(chan gateway.Tick, 1000),
			exchange: NewExchange(chainID),
		}
		gtw.Gateway = base.NewGateway(gtw)
		return gtw
	}
}

func (g *Gateway) Connect() error {
	api, err := NewV3API(g.Exchange().ChainID, g.options)
	if err != nil {
		return fmt.Errorf("failed to create UniswapV3 API: %w", err)
	}
	g.api = api

	g.marketDataGateway = NewMarketDataGateway(g.api, g.options, g.tickCh)
	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	if err := g.marketDataGateway.SubscribeMarkets(markets); err != nil {
		return fmt.Errorf("failed to subscribe to markets: %w", err)
	}
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return g.exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	if len(g.options.PoolAddresses) == 0 {
		return nil, fmt.Errorf("no pool addresses provided. Use --poolAddresses flag")
	}

	markets := make([]gateway.Market, 0, len(g.options.PoolAddresses))

	for _, poolAddr := range g.options.PoolAddresses {
		if poolAddr == "" {
			continue
		}

		poolAddress := common.HexToAddress(poolAddr)

		pool, err := g.api.GetPool(poolAddress)
		if err != nil {
			return nil, fmt.Errorf("failed to get pool data for %s: %w", poolAddr, err)
		}

		// Base tick size on tick spacing
		tickSpacing := pool.TickLens.tickSpacing
		baseTick := 0.0001
		if tickSpacing == 1 {
			baseTick = 0.00001
		} else if tickSpacing == 10 {
			baseTick = 0.0001
		} else if tickSpacing == 60 {
			baseTick = 0.001
		} else if tickSpacing == 200 {
			baseTick = 0.01
		}

		// For tokens with 18 decimals, we might want 6 decimal places (0.000001)
		// For tokens with 6 decimals (like USDC), we might want 2 decimal places (0.01)
		amountTick := 1.0
		token0Decimals := pool.Token0.Decimals
		if token0Decimals >= 18 {
			amountTick = 0.000001
		} else if token0Decimals >= 8 {
			amountTick = 0.0001
		} else if token0Decimals >= 6 {
			amountTick = 0.01
		}

		market := gateway.Market{
			Exchange: g.exchange,
			Symbol:   poolAddr,
			Pair: gateway.Pair{
				Base:  pool.Token0.Address.Hex(),
				Quote: pool.Token1.Address.Hex(),
			},
			FeeTier:          int64(pool.Fee),
			PriceTick:        baseTick,
			AmountTick:       amountTick,
			MinimumOrderSize: 0.000001,
		}

		markets = append(markets, market)
	}

	return markets, nil
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	poolAddr := common.HexToAddress(market.Symbol)

	pool, err := g.api.GetPool(poolAddr)
	if err != nil {
		return gateway.DepthBook{}, fmt.Errorf("failed to get pool data: %w", err)
	}

	numLevels := params.Limit
	if numLevels == 0 {
		numLevels = 10
	}

	depthBook, err := g.api.Depth(
		pool,
		pool.TickLens,
		pool.Token0,
		pool.Token1,
		numLevels,
	)
	if err != nil {
		return gateway.DepthBook{}, fmt.Errorf("failed to build order book: %w", err)
	}

	// Sort the price levels
	gateway.PriceLevelsSortBids(depthBook.Bids)
	gateway.PriceLevelsSortAsks(depthBook.Asks)

	return *depthBook, nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
