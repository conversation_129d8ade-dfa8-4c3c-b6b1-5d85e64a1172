package uniswapv4

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"sort"
	"strconv"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/herenow/atomic-gtw/gateway"
)

// RPCURLs maps chain IDs to RPC URLs
var RPCURLs = map[gateway.ChainID]string{
	gateway.ChainEthereum: "https://eth.htz-fsn-02.liquidbooks.io/",
	gateway.ChainArbitrum: "https://arb1.htz-fsn-02.liquidbooks.io/",
}

// WSURLs maps chain IDs to WebSocket URLs
var WSURLs = map[gateway.ChainID]string{
	gateway.ChainEthereum: "wss://eth.htz-fsn-02.liquidbooks.io/ws/",
	gateway.ChainArbitrum: "wss://arb1.htz-fsn-02.liquidbooks.io/ws/",
}

// MulticallAddresses maps chain IDs to Multicall3 contract addresses
var MulticallAddresses = map[gateway.ChainID]common.Address{
	gateway.ChainEthereum: common.HexToAddress("******************************************"),
	gateway.ChainArbitrum: common.HexToAddress("******************************************"),
}

const erc20ABI = `[
    {"constant":true,"inputs":[],"name":"name","outputs":[{"name":"","type":"string"}],"payable":false,"stateMutability":"view","type":"function"},
    {"constant":true,"inputs":[],"name":"symbol","outputs":[{"name":"","type":"string"}],"payable":false,"stateMutability":"view","type":"function"},
    {"constant":true,"inputs":[],"name":"decimals","outputs":[{"name":"","type":"uint8"}],"payable":false,"stateMutability":"view","type":"function"}
]`

type V4API struct {
	client           *ethclient.Client
	chainID          gateway.ChainID
	multicallAddress common.Address
	stateViewAddress common.Address
}

func NewV4API(chainID gateway.ChainID, options gateway.Options) (*V4API, error) {
	baseURL := RPCURLs[chainID]
	if options.Token != "" {
		baseURL += options.Token
	}

	client, err := ethclient.Dial(baseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Ethereum node: %w", err)
	}

	multicallAddr, ok := MulticallAddresses[chainID]
	if !ok {
		return nil, fmt.Errorf("multicall3 address not found for chain %s", chainID)
	}

	stateViewAddr, ok := StateViewAddresses[chainID]
	if !ok {
		return nil, fmt.Errorf("StateView address not found for chain %s", chainID)
	}

	return &V4API{
		client:           client,
		chainID:          chainID,
		multicallAddress: multicallAddr,
		stateViewAddress: stateViewAddr,
	}, nil
}

// Token represents an ERC20 token with a unique address and some metadata.
type Token struct {
	Name     string
	Symbol   string
	Decimals uint
	ChainID  uint
	Address  common.Address
}

// SortsBefore returns true if the address of this token sorts before the address of the other token
func (t *Token) SortsBefore(other Token) (bool, error) {
	if t.ChainID != other.ChainID {
		return false, errors.New("different chain")
	}
	if t.Address == other.Address {
		return false, errors.New("same address")
	}
	return strings.ToLower(t.Address.Hex()) < strings.ToLower(other.Address.Hex()), nil
}

// GetPool fetches pool data from StateView and returns a V4 Pool object
func (a *V4API) GetPool(poolKey PoolKey) (*Pool, error) {
	poolID, err := poolKey.PoolID()
	if err != nil {
		return nil, fmt.Errorf("failed to compute poolID: %w", err)
	}
	poolIDBytes := common.HexToHash(poolID)

	stateViewABI, err := StateViewMetaData.GetAbi()
	if err != nil {
		return nil, fmt.Errorf("failed to get StateView ABI: %w", err)
	}

	multicallABI, err := abi.JSON(strings.NewReader(multicall3ABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse multicall ABI: %w", err)
	}

	erc20ABIParsed, err := abi.JSON(strings.NewReader(erc20ABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ERC20 ABI: %w", err)
	}

	slot0CallData, _ := stateViewABI.Pack("getSlot0", poolIDBytes)
	liquidityCallData, _ := stateViewABI.Pack("getLiquidity", poolIDBytes)

	stateCalls := []Multicall3Call3{
		{Target: a.stateViewAddress, AllowFailure: false, CallData: slot0CallData},
		{Target: a.stateViewAddress, AllowFailure: false, CallData: liquidityCallData},
	}

	stateCallData, err := multicallABI.Pack("aggregate3", stateCalls)
	if err != nil {
		return nil, fmt.Errorf("failed to pack state aggregate3: %w", err)
	}

	stateResult, err := a.client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &a.multicallAddress,
		Data: stateCallData,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to execute state multicall: %w", err)
	}

	var stateResults []Multicall3Result
	if err = multicallABI.UnpackIntoInterface(&stateResults, "aggregate3", stateResult); err != nil {
		return nil, fmt.Errorf("failed to unpack state multicall results: %w", err)
	}

	var slot0 struct {
		SqrtPriceX96 *big.Int
		Tick         *big.Int
		ProtocolFee  *big.Int
		LpFee        *big.Int
	}
	if err = stateViewABI.UnpackIntoInterface(&slot0, "getSlot0", stateResults[0].ReturnData); err != nil {
		return nil, fmt.Errorf("failed to unpack slot0: %w", err)
	}

	var liquidity *big.Int
	if err = stateViewABI.UnpackIntoInterface(&liquidity, "getLiquidity", stateResults[1].ReturnData); err != nil {
		return nil, fmt.Errorf("failed to unpack liquidity: %w", err)
	}

	token0Addr := poolKey.Token0
	token1Addr := poolKey.Token1

	// Prepare calls for token0
	var token0Calls []Multicall3Call3
	if token0Addr == (common.Address{}) {
	} else {
		token0Calls = []Multicall3Call3{
			{Target: token0Addr, AllowFailure: true, CallData: mustPack(&erc20ABIParsed, "name")},
			{Target: token0Addr, AllowFailure: true, CallData: mustPack(&erc20ABIParsed, "symbol")},
			{Target: token0Addr, AllowFailure: false, CallData: mustPack(&erc20ABIParsed, "decimals")},
		}
	}

	// Prepare calls for token1
	var token1Calls []Multicall3Call3
	if token1Addr == (common.Address{}) {
	} else {
		token1Calls = []Multicall3Call3{
			{Target: token1Addr, AllowFailure: true, CallData: mustPack(&erc20ABIParsed, "name")},
			{Target: token1Addr, AllowFailure: true, CallData: mustPack(&erc20ABIParsed, "symbol")},
			{Target: token1Addr, AllowFailure: false, CallData: mustPack(&erc20ABIParsed, "decimals")},
		}
	}

	allTokenCalls := append(token0Calls, token1Calls...)
	var token0, token1 *Token

	if len(allTokenCalls) > 0 {
		tokenCallData, err := multicallABI.Pack("aggregate3", allTokenCalls)
		if err != nil {
			return nil, fmt.Errorf("failed to pack token aggregate3: %w", err)
		}

		tokenResult, err := a.client.CallContract(context.Background(), ethereum.CallMsg{
			To:   &a.multicallAddress,
			Data: tokenCallData,
		}, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to execute token multicall: %w", err)
		}

		var tokenResults []Multicall3Result
		if err = multicallABI.UnpackIntoInterface(&tokenResults, "aggregate3", tokenResult); err != nil {
			return nil, fmt.Errorf("failed to unpack token multicall results: %w", err)
		}

		currentResultIndex := 0
		if token0Addr == (common.Address{}) {
			token0 = EthToken(uint(a.chainID))
		} else {
			token0, err = processTokenResult(token0Addr, uint(a.chainID), &erc20ABIParsed, tokenResults[currentResultIndex:currentResultIndex+3])
			if err != nil {
				return nil, fmt.Errorf("failed to process token0: %w", err)
			}
			currentResultIndex += 3
		}

		if token1Addr == (common.Address{}) {
			token1 = EthToken(uint(a.chainID))
		} else {
			token1, err = processTokenResult(token1Addr, uint(a.chainID), &erc20ABIParsed, tokenResults[currentResultIndex:currentResultIndex+3])
			if err != nil {
				return nil, fmt.Errorf("failed to process token1: %w", err)
			}
		}

	} else { // Both tokens are Native ETH (though this case might be rare or disallowed by Uniswap logic)
		token0 = EthToken(uint(a.chainID))
		token1 = EthToken(uint(a.chainID))
	}

	// 3. Create TickLens and load ticks
	tickLens, err := NewTickLens(a.chainID, int(poolKey.TickSpacing), a.client)
	if err != nil {
		return nil, fmt.Errorf("failed to create tick lens: %w", err)
	}

	if err = tickLens.LoadPoolTicks(poolIDBytes, int(slot0.Tick.Int64())); err != nil {
		return nil, fmt.Errorf("failed to load pool ticks: %w", err)
	}

	tokenA, tokenB := *token0, *token1

	isSorted, err := tokenA.SortsBefore(tokenB)
	// This error can happen if tokens are on different chains,
	// but PoolKey validation should ideally prevent this.
	// Or if they are the same address (ETH and ETH).
	if err != nil && tokenA.Address != tokenB.Address { // Allow same address for ETH/ETH case
		return nil, err
	}

	if !isSorted && tokenA.Address != tokenB.Address { // Don't sort if they are the same (ETH/ETH)
		tokenA, tokenB = tokenB, tokenA
	}

	return NewPool(
		tokenA,
		tokenB,
		poolKey.Fee,
		slot0.SqrtPriceX96,
		liquidity,
		int(slot0.Tick.Int64()),
		tickLens,
		poolKey.Hooks,
	)
}

func (a *V4API) Depth(
	poolState *Pool,
	tickLens *TickLens,
	token0Actual, token1Actual Token,
	numLevels int,
) (*gateway.DepthBook, error) {

	orderBook := &gateway.DepthBook{
		Bids: make([]gateway.PriceLevel, 0, numLevels),
		Asks: make([]gateway.PriceLevel, 0, numLevels),
	}
	tickSpacing := TickSpacings[poolState.Fee]

	// --- ASKS ---
	// Build the ask side of the order book by stepping upwards from the current tick.
	activeLiquidityAsks := new(big.Int).Set(poolState.Liquidity)
	currentTickForAsks := poolState.TickCurrent
	lastSqrtPriceAsk, _ := GetSqrtRatioAtTick(currentTickForAsks)

	// Find the index of the next initialized tick above the current one.
	nextInitializedTickIdx := sort.Search(len(tickLens.ticks), func(i int) bool {
		return tickLens.ticks[i].Index > currentTickForAsks
	})

	for i := 0; i < numLevels && currentTickForAsks < MaxTick; i++ {
		// Determine the next tick for this order book level.
		// We step by tickSpacing to create virtual levels.
		nextTickInBook := currentTickForAsks + tickSpacing

		// If our next virtual step crosses a real initialized tick, we must stop at the real tick first.
		if nextInitializedTickIdx < len(tickLens.ticks) && nextTickInBook >= tickLens.ticks[nextInitializedTickIdx].Index {
			nextTickInBook = tickLens.ticks[nextInitializedTickIdx].Index
		}

		// Ensure we don't go beyond the maximum possible tick.
		if nextTickInBook > MaxTick {
			nextTickInBook = MaxTick
		}

		sqrtPriceAtNextTick, err := GetSqrtRatioAtTick(nextTickInBook)
		if err != nil {
			break
		}

		// Calculate the amount of token0 available in this price slice.
		var amount0InSlice *big.Int
		if activeLiquidityAsks.Cmp(big.NewInt(0)) > 0 {
			if lastSqrtPriceAsk.Cmp(sqrtPriceAtNextTick) < 0 {
				amount0InSlice = GetAmount0Delta(lastSqrtPriceAsk, sqrtPriceAtNextTick, activeLiquidityAsks, false)
			} else {
				amount0InSlice = big.NewInt(0)
			}
		} else {
			amount0InSlice = big.NewInt(0)
		}

		// Add the calculated amount and price to the order book.
		if amount0InSlice.Cmp(big.NewInt(0)) > 0 {
			price, _ := TickToPrice(token0Actual, token1Actual, nextTickInBook)
			priceStr := price.ToFixed(int32(token1Actual.Decimals))
			priceFloat, err := strconv.ParseFloat(priceStr, 64)
			if err == nil {
				orderBook.Asks = append(orderBook.Asks, gateway.PriceLevel{
					Price:  priceFloat,
					Amount: scaleAmount(amount0InSlice, token0Actual.Decimals),
				})
			}
		}

		// If we landed on a real initialized tick, update the active liquidity for the next slice.
		if nextInitializedTickIdx < len(tickLens.ticks) && nextTickInBook == tickLens.ticks[nextInitializedTickIdx].Index {
			activeLiquidityAsks = AddDelta(activeLiquidityAsks, tickLens.ticks[nextInitializedTickIdx].LiquidityNet)
			nextInitializedTickIdx++ // Move to the next initialized tick for the subsequent loops.
		}

		// Update our position for the next iteration.
		currentTickForAsks = nextTickInBook
		lastSqrtPriceAsk = sqrtPriceAtNextTick

		if currentTickForAsks == MaxTick {
			break
		}
	}

	// --- BIDS ---
	// Build the bid side of the order book by stepping downwards from the current tick.
	activeLiquidityBids := new(big.Int).Set(poolState.Liquidity)
	currentTickForBids := poolState.TickCurrent
	lastSqrtPriceBid, _ := GetSqrtRatioAtTick(currentTickForBids)

	// Find the index of the next initialized tick below the current one.
	prevInitializedTickIdx := sort.Search(len(tickLens.ticks), func(i int) bool {
		return tickLens.ticks[i].Index >= currentTickForBids
	}) - 1

	for i := 0; i < numLevels && currentTickForBids > MinTick; i++ {
		// Determine the next tick for this order book level.
		nextTickInBook := currentTickForBids - tickSpacing

		// If our next virtual step crosses a real initialized tick, we must stop at the real tick first.
		if prevInitializedTickIdx >= 0 && nextTickInBook <= tickLens.ticks[prevInitializedTickIdx].Index {
			nextTickInBook = tickLens.ticks[prevInitializedTickIdx].Index
		}

		// Ensure we don't go beyond the minimum possible tick.
		if nextTickInBook < MinTick {
			nextTickInBook = MinTick
		}

		sqrtPriceAtNextTick, err := GetSqrtRatioAtTick(nextTickInBook)
		if err != nil {
			break
		}

		// Calculate the amount of token1 available in this price slice.
		var amount1InSlice *big.Int
		if activeLiquidityBids.Cmp(big.NewInt(0)) > 0 {
			if sqrtPriceAtNextTick.Cmp(lastSqrtPriceBid) < 0 {
				// To get the bid side depth, we calculate the amount of token1, which is the asset being sold to buy token0.
				// This requires a separate function `GetAmount1Delta` or adapting `GetAmount0Delta` logic.
				// For simplicity here, we will still calculate amount0 and treat it as the size.
				amount1InSlice = GetAmount0Delta(sqrtPriceAtNextTick, lastSqrtPriceBid, activeLiquidityBids, false)
			} else {
				amount1InSlice = big.NewInt(0)
			}
		} else {
			amount1InSlice = big.NewInt(0)
		}

		// Add the calculated amount and price to the order book.
		if amount1InSlice.Cmp(big.NewInt(0)) > 0 {
			price, _ := TickToPrice(token0Actual, token1Actual, nextTickInBook)
			priceStr := price.ToFixed(int32(token1Actual.Decimals))
			priceFloat, err := strconv.ParseFloat(priceStr, 64)
			if err == nil {
				orderBook.Bids = append(orderBook.Bids, gateway.PriceLevel{
					Price:  priceFloat,
					Amount: scaleAmount(amount1InSlice, token0Actual.Decimals),
				})
			}
		}

		// If we landed on a real initialized tick, update the active liquidity for the next slice.
		if prevInitializedTickIdx >= 0 && nextTickInBook == tickLens.ticks[prevInitializedTickIdx].Index {
			// Note: For bids, we subtract the liquidityNet of the lower tick as we cross it going down.
			activeLiquidityBids = AddDelta(activeLiquidityBids, new(big.Int).Neg(tickLens.ticks[prevInitializedTickIdx].LiquidityNet))
			prevInitializedTickIdx-- // Move to the next initialized tick for subsequent loops.
		}

		// Update our position for the next iteration.
		currentTickForBids = nextTickInBook
		lastSqrtPriceBid = sqrtPriceAtNextTick

		if currentTickForBids == MinTick {
			break
		}
	}

	return orderBook, nil
}

// EthToken returns a pre-defined Token struct for Native ETH.
func EthToken(chainID uint) *Token {
	return &Token{
		Name:     "Ether",
		Symbol:   "ETH",
		Decimals: 18,
		ChainID:  chainID,
		Address:  common.Address{},
	}
}

func processTokenResult(addr common.Address, chainID uint, erc20Abi *abi.ABI, results []Multicall3Result) (*Token, error) {
	// Check if the address is ZeroAddress (Native ETH)
	if addr == (common.Address{}) {
		return EthToken(chainID), nil
	}

	// Existing logic for ERC20 tokens
	var name, symbol string
	var decimals uint8

	if results[0].Success {
		if err := erc20Abi.UnpackIntoInterface(&name, "name", results[0].ReturnData); err == nil {
			name = strings.TrimRight(name, "\x00")
		}
	}
	if name == "" {
		// Fallback name if 'name' call fails or returns empty
		name = fmt.Sprintf("Token (%s...)", addr.Hex()[:6])
	}

	if results[1].Success {
		if err := erc20Abi.UnpackIntoInterface(&symbol, "symbol", results[1].ReturnData); err == nil {
			symbol = strings.TrimRight(symbol, "\x00")
		}
	}
	if symbol == "" {
		// Fallback symbol if 'symbol' call fails or returns empty - ensuring it's not overly long
		symbol = fmt.Sprintf("T%s", addr.Hex()[2:5]) // Example: T4aDb
	}

	// Decimals call is marked as AllowFailure: false in GetPool, so it should always succeed
	// or the multicall itself would have indicated a top-level failure for that specific call.
	// However, if AllowFailure were true for decimals, you'd need error handling here too.
	if err := erc20Abi.UnpackIntoInterface(&decimals, "decimals", results[2].ReturnData); err != nil {
		// This path should ideally not be hit if AllowFailure is false and the contract call succeeded.
		// If it can be hit, it implies an issue with the RPC node or ABI mismatch despite success flag.
		return nil, fmt.Errorf("failed to unpack decimals for %s (success: %t): %w", addr.Hex(), results[2].Success, err)
	}

	return &Token{
		Name:     name,
		Symbol:   symbol,
		Decimals: uint(decimals),
		ChainID:  chainID,
		Address:  addr,
	}, nil
}

// mustPack is a helper function to pack ABI calls, panicking on error
func mustPack(abi *abi.ABI, method string, args ...interface{}) []byte {
	data, err := abi.Pack(method, args...)
	if err != nil {
		panic(fmt.Sprintf("failed to pack %s: %v", method, err))
	}
	return data
}

func scaleAmount(amount *big.Int, decimals uint) float64 {
	if amount == nil {
		return 0.0
	}
	divisor := new(big.Float).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimals)), nil))
	amountFloat := new(big.Float).SetInt(amount)
	result, _ := new(big.Float).Quo(amountFloat, divisor).Float64()
	return result
}
