package uniswapv4

import (
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/shopspring/decimal"
	"math/big"
)

var (
	ErrFeeTooHigh          = errors.New("fee too high")
	ErrInvalidSqrtRatioX96 = errors.New("invalid sqrtRatioX96")
)

type TradeType int

type Rounding int

var MaxUint256, _ = new(big.Int).SetString("ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", 16)

// FeeAmount is the default factory enabled fee amounts, denominated in hundredths of bips.
type FeeAmount uint32

const (
	FeeLowest FeeAmount = 100
	FeeLow    FeeAmount = 500
	FeeMedium FeeAmount = 3000
	FeeHigh   FeeAmount = 10000
	FeeMax    FeeAmount = 1000000
)

// TickSpacings is the default factory tick spacings by fee amount.
var TickSpacings = map[FeeAmount]int{
	FeeLowest: 1,
	FeeLow:    10,
	FeeMedium: 60,
	FeeHigh:   200,
}

var (
	NegativeOne = big.NewInt(-1)
	Zero        = big.NewInt(0)
	One         = big.NewInt(1)

	// Q96 used in liquidity amount math
	Q96  = new(big.Int).Exp(big.NewInt(2), big.NewInt(96), nil)
	Q192 = new(big.Int).Exp(Q96, big.NewInt(2), nil)
)

// Pool Represents a V4 pool
type Pool struct {
	PoolKey      PoolKey
	Token0       Token
	Token1       Token
	Fee          FeeAmount
	SqrtPriceX96 *big.Int
	Liquidity    *big.Int
	TickCurrent  int
	TickLens     *TickLens
}

// NewPool Construct a pool
func NewPool(tokenA, tokenB Token, fee FeeAmount, sqrtRatioX96 *big.Int, liquidity *big.Int, tickCurrent int, tickLens *TickLens, hook common.Address) (*Pool, error) {
	if fee >= FeeMax {
		return nil, ErrFeeTooHigh
	}

	tickSpacing, ok := TickSpacings[fee]
	if !ok {
		return nil, fmt.Errorf("invalid fee amount: %d. No tick spacing defined for this fee", fee)
	}

	tickCurrentSqrtRatioX96, err := GetSqrtRatioAtTick(tickCurrent)
	if err != nil {
		return nil, err
	}
	nextTickSqrtRatioX96, err := GetSqrtRatioAtTick(tickCurrent + 1)
	if err != nil {
		return nil, err
	}

	if sqrtRatioX96.Cmp(tickCurrentSqrtRatioX96) < 0 || sqrtRatioX96.Cmp(nextTickSqrtRatioX96) > 0 {
		return nil, ErrInvalidSqrtRatioX96
	}
	token0 := tokenA
	token1 := tokenB
	isSorted, err := tokenA.SortsBefore(tokenB)
	if err != nil {
		return nil, err
	}
	if !isSorted {
		token0 = tokenB
		token1 = tokenA
	}

	return &Pool{
		PoolKey: PoolKey{
			Token0:      token0.Address,
			Token1:      token1.Address,
			Fee:         fee,
			TickSpacing: int32(tickSpacing),
			Hooks:       hook,
		},
		Token0:       token0,
		Token1:       token1,
		Fee:          fee,
		SqrtPriceX96: sqrtRatioX96,
		Liquidity:    liquidity,
		TickCurrent:  tickCurrent,
		TickLens:     tickLens,
	}, nil
}

type Price struct {
	*Fraction
	Base   Token
	Quote  Token
	Scalar *Fraction
}

// NewPrice construct a price, either with the base and quote currency amount, or the args
func NewPrice(base, quote Token, denominator, numerator *big.Int) *Price {
	return &Price{
		Fraction: NewFraction(numerator, denominator),
		Base:     base,
		Quote:    quote,
		Scalar: NewFraction(
			new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(base.Decimals)), nil),
			new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(quote.Decimals)), nil)),
	}
}

func (p *Price) ToFixed(decimalPlaces int32) string {
	return p.Fraction.Multiply(p.Scalar).ToFixed(decimalPlaces)
}

type Fraction struct {
	Numerator   *big.Int
	Denominator *big.Int
}

// NewFraction creates a new fraction
func NewFraction(numerator, denominator *big.Int) *Fraction {
	return &Fraction{
		Numerator:   numerator,
		Denominator: denominator,
	}
}

// Multiply multiplies two fractions
func (f *Fraction) Multiply(other *Fraction) *Fraction {
	return NewFraction(new(big.Int).Mul(f.Numerator, other.Numerator), new(big.Int).Mul(f.Denominator, other.Denominator))
}

// ToFixed returns a fixed string representation of the fraction
func (f *Fraction) ToFixed(decimalPlaces int32) string {
	return decimal.NewFromBigInt(f.Numerator, 0).Div(decimal.NewFromBigInt(f.Denominator, 0)).StringFixed(decimalPlaces)
}

type PoolKey struct {
	Token0      common.Address
	Token1      common.Address
	Fee         FeeAmount
	TickSpacing int32
	Hooks       common.Address
}

// PoolID calculates the keccak256 hash of the ABI-encoded PoolKey, producing the PoolID.
// This is the primary identifier for a pool in many PoolManager interactions.
// The encoding scheme follows the Uniswap v4 SDK.
func (key PoolKey) PoolID() (string, error) {
	// Define the ABI types for the PoolKey struct fields, matching the SDK.
	addressType, _ := abi.NewType("address", "", nil)
	uint24Type, _ := abi.NewType("uint24", "", nil)
	int24Type, _ := abi.NewType("int24", "", nil)

	// Construct the arguments for ABI encoding.
	arguments := abi.Arguments{
		{Type: addressType, Name: "currency0"},
		{Type: addressType, Name: "currency1"},
		{Type: uint24Type, Name: "fee"},
		{Type: int24Type, Name: "tickSpacing"},
		{Type: addressType, Name: "hooks"},
	}

	// Pack the PoolKey fields into a byte slice.
	// IMPORTANT: We must convert the Fee and TickSpacing to *big.Int for the ABI packer.
	packed, err := arguments.Pack(
		key.Token0,
		key.Token1,
		new(big.Int).SetUint64(uint64(key.Fee)),
		big.NewInt(int64(key.TickSpacing)),
		key.Hooks,
	)
	if err != nil {
		return "", fmt.Errorf("failed to abi-encode pool key: %w", err)
	}

	// Compute the Keccak256 hash of the packed data and return its hex string representation.
	return crypto.Keccak256Hash(packed).Hex(), nil
}
