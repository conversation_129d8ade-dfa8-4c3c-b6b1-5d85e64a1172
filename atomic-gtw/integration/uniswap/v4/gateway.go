package uniswapv4

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/ethereum/go-ethereum/common"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

func NewExchange(chainID gateway.ChainID) gateway.Exchange {
	return gateway.Exchange{
		Name:    "UniswapV4",
		ChainID: chainID,
	}
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *MarketDataGateway
	api               *V4API
	tickCh            chan gateway.Tick
	exchange          gateway.Exchange
}

func NewGateway(chainID gateway.ChainID) gateway.NewGateway {
	return func(options gateway.Options) gateway.Gateway {
		gtw := &Gateway{
			options:  options,
			tickCh:   make(chan gateway.Tick, 1000),
			exchange: NewExchange(chainID),
		}
		gtw.Gateway = base.NewGateway(gtw)
		return gtw
	}
}

// ParsePoolKey parses a string in the format "token0:token1:fee:hooks"
func ParsePoolKey(keyStr string) (*PoolKey, error) {
	parts := strings.Split(keyStr, ":")
	if len(parts) != 4 {
		return nil, fmt.Errorf("invalid pool key format: %s. Expected 4 parts (token0:token1:fee:hooks), got %d", keyStr, len(parts))
	}

	token0Str := strings.TrimSpace(parts[0])
	token1Str := strings.TrimSpace(parts[1])
	feeStr := strings.TrimSpace(parts[2])
	hooksStr := strings.TrimSpace(parts[3])

	if !common.IsHexAddress(token0Str) {
		return nil, fmt.Errorf("invalid token0 address in pool key: %s", token0Str)
	}
	if !common.IsHexAddress(token1Str) {
		return nil, fmt.Errorf("invalid token1 address in pool key: %s", token1Str)
	}
	if hooksStr == "0" {
		hooksStr = common.Address{}.String()
	} else if !common.IsHexAddress(hooksStr) {
		return nil, fmt.Errorf("invalid hooks address in pool key: %s", hooksStr)
	}

	token0 := common.HexToAddress(token0Str)
	token1 := common.HexToAddress(token1Str)

	fee, err := strconv.ParseUint(feeStr, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid fee in pool key '%s': %w", keyStr, err)
	}
	feeAmount := FeeAmount(fee)

	tickSpacing, ok := TickSpacings[feeAmount]
	if !ok {
		return nil, fmt.Errorf("invalid fee in pool key '%s': no tick spacing defined for fee %d", keyStr, fee)
	}

	hooks := common.HexToAddress(hooksStr)

	// Ensure token0 sorts before token1 for a canonical key.
	if strings.ToLower(token1.Hex()) < strings.ToLower(token0.Hex()) {
		token0, token1 = token1, token0
	}

	return &PoolKey{
		Token0:      token0,
		Token1:      token1,
		Fee:         feeAmount,
		TickSpacing: int32(tickSpacing),
		Hooks:       hooks,
	}, nil
}

func (g *Gateway) Connect() error {
	api, err := NewV4API(g.Exchange().ChainID, g.options)
	if err != nil {
		return fmt.Errorf("failed to create UniswapV4 API: %w", err)
	}
	g.api = api

	g.marketDataGateway = NewMarketDataGateway(g.api, g.options, g.tickCh)
	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	if err := g.marketDataGateway.SubscribeMarkets(markets); err != nil {
		return fmt.Errorf("failed to subscribe to markets: %w", err)
	}
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return g.exchange
}

// GetMarkets now constructs markets from PoolKeys defined in options
func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	if len(g.options.PoolAddresses) == 0 {
		return nil, fmt.Errorf("no pool definitions provided. Use --poolAddresses flag with V4 Pool Keys in format 'token0:token1:fee:hooks'")
	}

	var markets []gateway.Market
	for _, poolKeyStr := range g.options.PoolAddresses {
		if poolKeyStr == "" {
			continue
		}

		poolKey, err := ParsePoolKey(poolKeyStr)
		if err != nil {
			return nil, fmt.Errorf("failed to parse pool key string '%s': %w", poolKeyStr, err)
		}

		pool, err := g.api.GetPool(*poolKey)
		if err != nil {
			return nil, fmt.Errorf("failed to get pool data for key %s: %w", poolKeyStr, err)
		}

		// Base tick size on tick spacing from the key
		baseTick := 0.0001 // Default
		switch poolKey.TickSpacing {
		case 1:
			baseTick = 0.00001
		case 10:
			baseTick = 0.0001
		case 60:
			baseTick = 0.001
		case 200:
			baseTick = 0.01
		}

		// Determine amount tick based on token0 decimals
		amountTick := 1.0
		token0Decimals := pool.Token0.Decimals
		if token0Decimals >= 18 {
			amountTick = 0.000001
		} else if token0Decimals >= 8 {
			amountTick = 0.0001
		} else if token0Decimals >= 6 {
			amountTick = 0.01
		}

		market := gateway.Market{
			Exchange: g.exchange,
			Symbol:   poolKeyStr,
			Pair: gateway.Pair{
				Base:  pool.Token0.Address.Hex(),
				Quote: pool.Token1.Address.Hex(),
			},
			FeeTier:          int64(pool.Fee),
			PriceTick:        baseTick,
			AmountTick:       amountTick,
			MinimumOrderSize: 0.000001,
		}

		markets = append(markets, market)
	}
	return markets, nil
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	// Parse the market symbol to get the PoolKey
	poolKey, err := ParsePoolKey(market.Symbol)
	if err != nil {
		return gateway.DepthBook{}, fmt.Errorf("failed to parse pool key from market symbol '%s': %w", market.Symbol, err)
	}

	// Calculate the canonical PoolID
	poolID, err := poolKey.PoolID()
	if err != nil {
		return gateway.DepthBook{}, fmt.Errorf("failed to calculate poolID from market symbol '%s': %w", market.Symbol, err)
	}

	g.marketDataGateway.mu.Lock()
	pool, ok := g.marketDataGateway.pools[poolID]
	g.marketDataGateway.mu.Unlock()

	// If pool is not in the cache (e.g., not subscribed), fetch it on-demand.
	if !ok {
		p, fetchErr := g.api.GetPool(*poolKey)
		if fetchErr != nil {
			return gateway.DepthBook{}, fmt.Errorf("pool not found for market symbol '%s' and failed to fetch on-demand: %w", market.Symbol, fetchErr)
		}
		pool = p
	}

	numLevels := params.Limit
	if numLevels == 0 {
		numLevels = 10
	}

	depthBook, err := g.api.Depth(
		pool,
		pool.TickLens,
		pool.Token0,
		pool.Token1,
		numLevels,
	)
	if err != nil {
		return gateway.DepthBook{}, fmt.Errorf("failed to build order book: %w", err)
	}

	gateway.PriceLevelsSortBids(depthBook.Bids)
	gateway.PriceLevelsSortAsks(depthBook.Asks)

	return *depthBook, nil
}

func (g *Gateway) Close() error {
	if g.marketDataGateway != nil {
		return g.marketDataGateway.Close()
	}
	return nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
