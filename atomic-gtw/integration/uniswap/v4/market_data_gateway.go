package uniswapv4

import (
	"context"
	"fmt"
	"log"
	"math"
	"math/big"
	"sort"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/herenow/atomic-gtw/gateway"
)

// PoolManagerAddresses maps chain IDs to official Uniswap PoolManager contract addresses
var PoolManagerAddresses = map[gateway.ChainID]common.Address{
	gateway.ChainEthereum: common.HexToAddress("******************************************"),
	gateway.ChainArbitrum: common.HexToAddress("******************************************"),
}

type MarketDataGateway struct {
	api           *V4API
	wsClient      *ethclient.Client
	tickCh        chan gateway.Tick
	options       gateway.Options
	markets       map[string]gateway.Market // Map from PoolID to Market
	pools         map[string]*Pool          // Map from PoolID to Pool
	subscriptions map[string]ethereum.Subscription
	mu            sync.Mutex
	quit          chan bool
}

func NewMarketDataGateway(api *V4API, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		api:           api,
		tickCh:        tickCh,
		options:       options,
		markets:       make(map[string]gateway.Market),
		pools:         make(map[string]*Pool),
		subscriptions: make(map[string]ethereum.Subscription),
		quit:          make(chan bool),
	}
}

func (g *MarketDataGateway) connectWebsocket(chainID gateway.ChainID) error {
	wsURL, ok := WSURLs[chainID]
	if !ok {
		return fmt.Errorf("websocket URL not found for chain ID: %s", chainID)
	}

	if g.options.Token != "" {
		wsURL += g.options.Token
	}

	client, err := ethclient.DialContext(context.Background(), wsURL)
	if err != nil {
		return fmt.Errorf("failed to connect to WebSocket endpoint %s: %w", wsURL, err)
	}
	g.wsClient = client
	return nil
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	if len(markets) == 0 {
		return nil
	}
	if err := g.connectWebsocket(g.api.chainID); err != nil {
		log.Panicf("failed to connect WebSocket: %v", err)
	}

	poolManagerAddress, ok := PoolManagerAddresses[g.api.chainID]
	if !ok {
		return fmt.Errorf("PoolManager address not found for chain %s", g.api.chainID)
	}

	for _, market := range markets {
		// market.Symbol now holds the "token0:token1:fee:tickSpacing:hooks" string.
		poolKey, err := ParsePoolKey(market.Symbol)
		if err != nil {
			log.Printf("Error parsing pool key from market symbol '%s', skipping: %v", market.Symbol, err)
			continue
		}
		if err = g.initializePool(market, *poolKey); err != nil {
			return fmt.Errorf("failed to initialize pool for market %s: %w", market.Symbol, err)
		}
	}

	if g.wsClient != nil {
		g.subscribeToManagerEvents(poolManagerAddress)
	}

	return nil
}

func (g *MarketDataGateway) initializePool(market gateway.Market, poolKey PoolKey) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	pool, err := g.api.GetPool(poolKey)
	if err != nil {
		return fmt.Errorf("failed to get pool: %w", err)
	}
	// The PoolID is the canonical identifier for chain interactions and internal mapping.
	poolID, err := pool.PoolKey.PoolID()
	if err != nil {
		return fmt.Errorf("failed to calculate poolID from poolKey %+v: %w", pool.PoolKey, err)
	}

	g.pools[poolID] = pool
	g.markets[poolID] = market

	g.sendOrderBookSnapshot(market, pool)
	return nil
}

func (g *MarketDataGateway) subscribeToManagerEvents(managerAddress common.Address) {
	filterer, err := NewPoolManagerFilterer(managerAddress, g.wsClient)
	if err != nil {
		log.Printf("Failed to create PoolManager filterer: %v. No WebSocket events.", err)
		return
	}

	var poolIDs [][32]byte
	g.mu.Lock()
	for poolID := range g.pools {
		poolIDs = append(poolIDs, common.HexToHash(poolID))
	}
	g.mu.Unlock()

	// Subscribe to Swap Events
	swapEvents := make(chan *PoolManagerSwap)
	swapSub, err := filterer.WatchSwap(&bind.WatchOpts{Context: context.Background()}, swapEvents, poolIDs, nil)
	if err != nil {
		log.Panicf("Failed to subscribe to Swap events: %v", err)
	} else {
		g.subscriptions["swap"] = swapSub
		go g.handleSwapEvents(swapEvents, swapSub.Err())
	}

	// Subscribe to ModifyLiquidity Events
	modifyLiqEvents := make(chan *PoolManagerModifyLiquidity)
	modifyLiqSub, err := filterer.WatchModifyLiquidity(&bind.WatchOpts{Context: context.Background()}, modifyLiqEvents, poolIDs, nil)
	if err != nil {
		log.Panicf("Failed to subscribe to ModifyLiquidity events: %v", err)
	} else {
		g.subscriptions["modifyLiquidity"] = modifyLiqSub
		go g.handleModifyLiquidityEvents(modifyLiqEvents, modifyLiqSub.Err())
	}
}

func (g *MarketDataGateway) handleSwapEvents(events chan *PoolManagerSwap, errChan <-chan error) {
	for {
		select {
		case event := <-events:
			g.mu.Lock()
			poolIDHex := common.BytesToHash(event.Id[:]).Hex()
			pool, ok := g.pools[poolIDHex]
			if !ok {
				g.mu.Unlock()
				continue
			}
			market := g.markets[poolIDHex]

			pool.SqrtPriceX96 = event.SqrtPriceX96
			pool.Liquidity = event.Liquidity
			pool.TickCurrent = int(event.Tick.Int64())

			g.sendOrderBookUpdate(market, pool)
			g.sendTradeEvent(market, pool, event)
			g.mu.Unlock()

		case err := <-errChan:
			log.Panicf("Swap event subscription error: %v.", err)
		case <-g.quit:
			log.Printf("Closing Swap event subscription")
			return
		}
	}
}

func (g *MarketDataGateway) handleModifyLiquidityEvents(events chan *PoolManagerModifyLiquidity, errChan <-chan error) {
	for {
		select {
		case event := <-events:
			g.mu.Lock()
			poolIDHex := common.BytesToHash(event.Id[:]).Hex()
			pool, ok := g.pools[poolIDHex]
			if !ok {
				g.mu.Unlock()
				continue
			}
			market := g.markets[poolIDHex]
			tickLower := int(event.TickLower.Int64())
			tickUpper := int(event.TickUpper.Int64())
			liquidityDelta := event.LiquidityDelta

			// Update pool's total liquidity if the current price is within the modified range
			if pool.TickCurrent >= tickLower && pool.TickCurrent < tickUpper {
				pool.Liquidity = new(big.Int).Add(pool.Liquidity, liquidityDelta)
			}

			// Update TickLens in memory
			if pool.TickLens != nil {
				isMint := liquidityDelta.Cmp(big.NewInt(0)) > 0
				grossDelta := new(big.Int).Set(liquidityDelta)
				if !isMint {
					grossDelta.Neg(grossDelta) // For burns, gross liquidity decreases
				}

				// Update lower tick
				g.updateTickInLens(pool.TickLens, tickLower, liquidityDelta, grossDelta, isMint)
				// Update upper tick
				g.updateTickInLens(pool.TickLens, tickUpper, new(big.Int).Neg(liquidityDelta), grossDelta, isMint)
			} else {
				log.Printf("Warning: TickLens is nil for pool %s during ModifyLiquidity event. Cannot update tick liquidity.", poolIDHex)
			}

			g.sendOrderBookUpdate(market, pool)
			g.mu.Unlock()

		case err := <-errChan:
			log.Printf("ModifyLiquidity event subscription error: %v.", err)
			return
		case <-g.quit:
			log.Printf("Closing ModifyLiquidity event subscription")
			return
		}
	}
}

// updateTickInLens updates a specific tick's liquidity in the TickLens, or adds it if it's new.
func (g *MarketDataGateway) updateTickInLens(tickLensInstance *TickLens, tickIndex int, netChange *big.Int, grossChange *big.Int, isNewTickPossible bool) {
	if tickLensInstance == nil {
		log.Printf("Error: tickLensInstance is nil for tick %d", tickIndex)
		return
	}

	// Find the position of the tick or where it should be inserted
	idx := sort.Search(len(tickLensInstance.ticks), func(i int) bool {
		return tickLensInstance.ticks[i].Index >= tickIndex
	})

	// Check if the tick already exists
	if idx < len(tickLensInstance.ticks) && tickLensInstance.ticks[idx].Index == tickIndex {
		// Tick exists, update it
		tickLensInstance.ticks[idx].LiquidityNet = new(big.Int).Add(tickLensInstance.ticks[idx].LiquidityNet, netChange)
		tickLensInstance.ticks[idx].LiquidityGross = new(big.Int).Add(tickLensInstance.ticks[idx].LiquidityGross, grossChange)

		// Safeguard against negative gross liquidity
		if tickLensInstance.ticks[idx].LiquidityGross.Cmp(big.NewInt(0)) < 0 {
			log.Printf("Warning: Tick %d gross liquidity became negative (%s), setting to 0.", tickIndex, tickLensInstance.ticks[idx].LiquidityGross.String())
			tickLensInstance.ticks[idx].LiquidityGross = big.NewInt(0)
		}
	} else if isNewTickPossible {
		// Tick doesn't exist and it's a mint, so insert it
		newTick := Tick{
			Index:          tickIndex,
			LiquidityNet:   netChange,
			LiquidityGross: grossChange,
		}

		// Insert newTick into the slice at the correct sorted position
		tickLensInstance.ticks = append(tickLensInstance.ticks[:idx], append([]Tick{newTick}, tickLensInstance.ticks[idx:]...)...)
	} else {
		// This case can happen if a burn event arrives for a tick that is not in our lens.
		// This might indicate a temporary desync. A robust solution might queue a refresh.
		log.Panicf("Warning: ModifyLiquidity (burn) event for tick %d not found in TickLens. Tick data might be stale.", tickIndex)
	}
}

func (g *MarketDataGateway) sendOrderBookSnapshot(market gateway.Market, poolInstance *Pool) {
	bids, asks := g.generateOrderBookFromPool(market, poolInstance)

	eventLog := []gateway.Event{
		{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: market.Symbol,
			},
		},
	}

	for _, bid := range bids {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Bid,
				Price:  bid.Price,
				Amount: bid.Amount,
			},
		})
	}

	for _, ask := range asks {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Ask,
				Price:  ask.Price,
				Amount: ask.Amount,
			},
		})
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

func (g *MarketDataGateway) sendOrderBookUpdate(market gateway.Market, poolInstance *Pool) {
	bids, asks := g.generateOrderBookFromPool(market, poolInstance)

	var eventLog []gateway.Event

	eventLog = append(eventLog, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: market.Symbol,
		},
	})

	for _, bid := range bids {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Bid,
				Price:  bid.Price,
				Amount: bid.Amount,
			},
		})
	}

	for _, ask := range asks {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Ask,
				Price:  ask.Price,
				Amount: ask.Amount,
			},
		})
	}

	if len(eventLog) > 1 {
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          eventLog,
		}
	}
}

func (g *MarketDataGateway) sendTradeEvent(market gateway.Market, pool *Pool, event *PoolManagerSwap) {
	amount0 := new(big.Int).Set(event.Amount0)
	amount1 := new(big.Int).Set(event.Amount1)

	var tradeAmount float64
	var tradePrice float64
	var direction gateway.Side

	if amount0.Sign() < 0 {
		direction = gateway.Ask
		amount0.Neg(amount0)
		tradeAmount = scaleAmount(amount0, pool.Token0.Decimals)

		if amount0.Sign() > 0 {
			price := new(big.Float).Quo(
				new(big.Float).SetInt(amount1),
				new(big.Float).SetInt(amount0),
			)
			decimalAdjustment := new(big.Float).SetFloat64(math.Pow(10, float64(pool.Token0.Decimals-pool.Token1.Decimals)))
			price.Mul(price, decimalAdjustment)
			tradePrice, _ = price.Float64()
		}
	} else {
		direction = gateway.Bid
		tradeAmount = scaleAmount(amount0, pool.Token0.Decimals)
		negAmount1 := new(big.Int).Neg(amount1)
		if amount0.Sign() > 0 {
			price := new(big.Float).Quo(
				new(big.Float).SetInt(negAmount1),
				new(big.Float).SetInt(amount0),
			)
			decimalAdjustment := new(big.Float).SetFloat64(math.Pow(10, float64(pool.Token0.Decimals-pool.Token1.Decimals)))
			price.Mul(price, decimalAdjustment)
			tradePrice, _ = price.Float64()
		}
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog: []gateway.Event{
			{
				Type: gateway.TradeEvent,
				Data: gateway.Trade{
					Timestamp: time.Now(),
					Symbol:    market.Symbol,
					ID:        event.Raw.TxHash.Hex(),
					Direction: direction,
					Price:     tradePrice,
					Amount:    tradeAmount,
				},
			},
		},
	}
}

func (g *MarketDataGateway) generateOrderBookFromPool(market gateway.Market, pool *Pool) ([]gateway.PriceLevel, []gateway.PriceLevel) {
	if pool == nil || pool.TickLens == nil {
		log.Printf("Pool or TickLens data not available for market %s. Order book cannot be generated.", market.Symbol)
		return nil, nil
	}

	depthBook, err := g.api.Depth(
		pool,
		pool.TickLens,
		pool.Token0,
		pool.Token1,
		10, // Number of levels
	)
	if err != nil {
		log.Printf("Failed to build order book for market %s: %v", market.Symbol, err)
		return nil, nil
	}

	gateway.PriceLevelsSortBids(depthBook.Bids)
	gateway.PriceLevelsSortAsks(depthBook.Asks)

	return depthBook.Bids, depthBook.Asks
}

func (g *MarketDataGateway) Close() error {
	close(g.quit)

	g.mu.Lock()
	defer g.mu.Unlock()

	for key, sub := range g.subscriptions {
		sub.Unsubscribe()
		log.Printf("Unsubscribed from %s events", key)
	}
	g.subscriptions = make(map[string]ethereum.Subscription)

	if g.wsClient != nil {
		g.wsClient.Close()
		g.wsClient = nil
	}
	return nil
}
