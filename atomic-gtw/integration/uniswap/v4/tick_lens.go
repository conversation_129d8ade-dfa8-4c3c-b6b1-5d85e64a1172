package uniswapv4

import (
	"context"
	"errors"
	"fmt"
	"github.com/ethereum/go-ethereum/ethclient"
	"log"
	"math"
	"math/big"
	"sort"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/herenow/atomic-gtw/gateway"
)

var (
	ErrZeroTickSpacing    = errors.New("tick spacing must be greater than 0")
	ErrInvalidTickSpacing = errors.New("invalid tick spacing")
	ErrZeroNet            = errors.New("tick net delta must be zero")
	ErrSorted             = errors.New("ticks must be sorted")
)

// StateViewAddresses maps chain IDs to official Uniswap StateView contract addresses
var StateViewAddresses = map[gateway.ChainID]common.Address{
	gateway.ChainEthereum: common.HexToAddress("******************************************"),
	gateway.ChainArbitrum: common.HexToAddress("******************************************"),
}

// Multicall3 ABI remains the same
const multicall3ABI = `[{"inputs":[{"components":[{"internalType":"address","name":"target","type":"address"},{"internalType":"bool","name":"allowFailure","type":"bool"},{"internalType":"bytes","name":"callData","type":"bytes"}],"internalType":"struct Multicall3.Call3[]","name":"calls","type":"tuple[]"}],"name":"aggregate3","outputs":[{"components":[{"internalType":"bool","name":"success","type":"bool"},{"internalType":"bytes","name":"returnData","type":"bytes"}],"internalType":"struct Multicall3.Result[]","name":"returnData","type":"tuple[]"}],"stateMutability":"payable","type":"function"}]`

type Multicall3Call3 struct {
	Target       common.Address
	AllowFailure bool
	CallData     []byte
}

type Multicall3Result struct {
	Success    bool
	ReturnData []byte
}

type Tick struct {
	Index          int
	LiquidityGross *big.Int
	LiquidityNet   *big.Int
}

// TickLens is a utility for fetching and providing tick data from Uniswap V4 pools
// It now uses StateView under the hood.
type TickLens struct {
	client           *ethclient.Client
	multicallAddress common.Address
	multicallABI     abi.ABI
	stateViewAddress common.Address
	stateViewABI     abi.ABI
	ticks            []Tick
	tickSpacing      int
}

// NewTickLens creates a new TickLens instance for V4
func NewTickLens(chainID gateway.ChainID, tickSpacing int, client *ethclient.Client) (*TickLens, error) {
	multicallAddress, exists := MulticallAddresses[chainID]
	if !exists {
		return nil, fmt.Errorf("multicall3 contract not available on chain %s", chainID)
	}

	stateViewAddress, exists := StateViewAddresses[chainID]
	if !exists {
		return nil, fmt.Errorf("StateView contract not available on chain %s", chainID)
	}

	parsedMulticallABI, err := abi.JSON(strings.NewReader(multicall3ABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse Multicall3 ABI: %w", err)
	}

	parsedStateViewABI, err := StateViewMetaData.GetAbi()
	if err != nil {
		return nil, fmt.Errorf("failed to parse StateView ABI: %w", err)
	}

	return &TickLens{
		client:           client,
		multicallAddress: multicallAddress,
		multicallABI:     parsedMulticallABI,
		stateViewAddress: stateViewAddress,
		stateViewABI:     *parsedStateViewABI,
		ticks:            []Tick{},
		tickSpacing:      tickSpacing,
	}, nil
}

// LoadPoolTicks loads tick data for a specific pool using its PoolID.
// It implements the V3 strategy: first, it attempts a fast, limited fetch
// around the current price. If no ticks are found (common in pools with
// sparse liquidity), it falls back to a full scan to ensure correctness.
func (t *TickLens) LoadPoolTicks(poolId [32]byte, currentTick int) error {
	const wordsToFetch = 5 // Number of words to fetch on each side of the current tick

	// Determine the word (bitmap index) containing the current tick
	currentWord := int16((currentTick / t.tickSpacing) >> 8)

	// Define the range of words for the initial, fast query
	startWord := currentWord - wordsToFetch
	endWord := currentWord + wordsToFetch

	// Clamp the range to the valid word boundaries
	minWord := int16((MinTick / t.tickSpacing) >> 8)
	maxWord := int16((MaxTick / t.tickSpacing) >> 8)

	if startWord < minWord {
		startWord = minWord
	}
	if endWord > maxWord {
		endWord = maxWord
	}

	// Perform the initial, limited fetch
	ticks, err := t.fetchTicksInWordRange(poolId, startWord, endWord)
	if err != nil {
		return fmt.Errorf("failed to get ticks in word range: %w", err)
	}

	// Fallback for sparse pools: if the initial fetch yields no ticks,
	// try fetching all ticks. This is slower but necessary for correctness.
	if len(ticks) == 0 {
		log.Printf("No ticks found in initial fetch for pool %x. Falling back to full scan.", poolId)
		ticks, err = t.fetchTicksInWordRange(poolId, minWord, maxWord)
		if err != nil {
			return fmt.Errorf("failed to get all ticks on fallback: %w", err)
		}
	}

	// Validate and sort the final list of ticks
	for _, tick := range ticks {
		if tick.Index%t.tickSpacing != 0 {
			return ErrInvalidTickSpacing
		}
	}

	sort.Slice(ticks, func(i, j int) bool {
		return ticks[i].Index < ticks[j].Index
	})

	// Final check for sorting consistency
	for i := 0; i < len(ticks)-1; i++ {
		if ticks[i].Index > ticks[i+1].Index {
			return ErrSorted
		}
	}

	t.ticks = ticks
	return nil
}

// fetchTicksInWordRange fetches ticks within a given range of bitmap words.
// It uses a two-step multicall process:
// 1. Fetch all tick bitmaps in the specified word range.
// 2. For each initialized tick found in the bitmaps, fetch its full info.
func (t *TickLens) fetchTicksInWordRange(poolId [32]byte, startWord, endWord int16) ([]Tick, error) {
	// --- Step 1: Fetch Tick Bitmaps ---
	var bitmapCalls []Multicall3Call3
	wordPositions := make([]int16, 0, endWord-startWord+1)
	for wordPos := startWord; wordPos <= endWord; wordPos++ {
		callData, err := t.stateViewABI.Pack("getTickBitmap", poolId, wordPos)
		if err != nil {
			return nil, fmt.Errorf("failed to pack getTickBitmap call for word %d: %w", wordPos, err)
		}
		bitmapCalls = append(bitmapCalls, Multicall3Call3{
			Target:       t.stateViewAddress,
			AllowFailure: true, // Allow failures for words that have no initialized ticks
			CallData:     callData,
		})
		wordPositions = append(wordPositions, wordPos)
	}

	bitmapResults, err := t.executeMulticall(bitmapCalls)
	if err != nil {
		return nil, fmt.Errorf("multicall for bitmaps failed: %w", err)
	}

	// --- Step 2: Decode Bitmaps and Prepare Tick Info Calls ---
	var tickInfoCalls []Multicall3Call3
	tickIndices := make([]int, 0)

	for i, res := range bitmapResults {
		if !res.Success || len(res.ReturnData) == 0 {
			continue
		}

		var tickBitmap *big.Int
		if err := t.stateViewABI.UnpackIntoInterface(&tickBitmap, "getTickBitmap", res.ReturnData); err != nil {
			continue // Skip if unpack fails
		}

		if tickBitmap.Cmp(big.NewInt(0)) == 0 {
			continue // Skip empty bitmaps
		}

		wordPos := wordPositions[i]
		for j := 0; j < 256; j++ {
			// Check if the j-th bit is set in the bitmap
			if new(big.Int).And(tickBitmap, new(big.Int).Lsh(big.NewInt(1), uint(j))).Cmp(big.NewInt(0)) > 0 {
				uncompressedTick := (int(wordPos) << 8) + j
				tickIndex := uncompressedTick * t.tickSpacing

				// Prepare a call to get the info for this specific tick
				tickInfoCallData, err := t.stateViewABI.Pack("getTickInfo", poolId, big.NewInt(int64(tickIndex)))
				if err != nil {
					return nil, fmt.Errorf("failed to pack getTickInfo call for tick %d: %w", tickIndex, err)
				}
				tickInfoCalls = append(tickInfoCalls, Multicall3Call3{
					Target:       t.stateViewAddress,
					AllowFailure: false, // Should not fail for an initialized tick
					CallData:     tickInfoCallData,
				})
				tickIndices = append(tickIndices, tickIndex)
			}
		}
	}

	if len(tickInfoCalls) == 0 {
		return []Tick{}, nil // No initialized ticks in the given range
	}

	// --- Step 3: Fetch Tick Infos ---
	tickInfoResults, err := t.executeMulticall(tickInfoCalls)
	if err != nil {
		return nil, fmt.Errorf("multicall for tick info failed: %w", err)
	}

	// --- Step 4: Unpack Tick Infos and Construct Tick Structs ---
	allTicks := make([]Tick, 0, len(tickInfoResults))
	for i, res := range tickInfoResults {
		if !res.Success {
			log.Printf("Warning: getTickInfo call failed for tick %d", tickIndices[i])
			continue
		}

		var tickInfo struct {
			LiquidityGross        *big.Int
			LiquidityNet          *big.Int
			FeeGrowthOutside0X128 *big.Int
			FeeGrowthOutside1X128 *big.Int
		}

		if err := t.stateViewABI.UnpackIntoInterface(&tickInfo, "getTickInfo", res.ReturnData); err != nil {
			log.Printf("Warning: failed to unpack tick info for tick %d: %v", tickIndices[i], err)
			continue
		}

		allTicks = append(allTicks, Tick{
			Index:          tickIndices[i],
			LiquidityGross: tickInfo.LiquidityGross,
			LiquidityNet:   tickInfo.LiquidityNet,
		})
	}

	return allTicks, nil
}

// executeMulticall is a helper to run a batch of calls using Multicall3
func (t *TickLens) executeMulticall(calls []Multicall3Call3) ([]Multicall3Result, error) {
	callData, err := t.multicallABI.Pack("aggregate3", calls)
	if err != nil {
		return nil, fmt.Errorf("failed to pack aggregate3 call: %w", err)
	}

	result, err := t.client.CallContract(context.Background(), ethereum.CallMsg{
		To:   &t.multicallAddress,
		Data: callData,
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to execute multicall: %w", err)
	}

	var results []Multicall3Result
	if err = t.multicallABI.UnpackIntoInterface(&results, "aggregate3", result); err != nil {
		return nil, fmt.Errorf("failed to unpack multicall results: %w", err)
	}
	return results, nil
}

// GetTick returns tick data for a specific tick index
func (t *TickLens) GetTick(index int) Tick {
	i := sort.Search(len(t.ticks), func(i int) bool { return t.ticks[i].Index >= index })
	if i < len(t.ticks) && t.ticks[i].Index == index {
		return t.ticks[i]
	}
	// This should ideally not be reached if the tick is initialized.
	// Returning an empty tick if not found.
	return Tick{Index: index, LiquidityGross: big.NewInt(0), LiquidityNet: big.NewInt(0)}
}

func (t *TickLens) NextInitializedTickWithinOneWord(tick int, lte bool, tickSpacing int) (int, bool) {
	compressed := math.Floor(float64(tick) / float64(tickSpacing))

	if lte {
		wordPos := int(compressed) >> 8
		minimum := (wordPos << 8) * tickSpacing
		if isBelowSmallest(t.ticks, tick) {
			return minimum, false
		}
		nextTick := nextInitializedTick(t.ticks, tick, lte)
		nextTickValue := math.Max(float64(minimum), float64(nextTick.Index))
		return int(nextTickValue), int(nextTickValue) == nextTick.Index
	} else {
		wordPos := int(compressed+1) >> 8
		maximum := ((wordPos+1)<<8)*tickSpacing - 1
		if isAtOrAboveLargest(t.ticks, tick) {
			return maximum, false
		}
		nextTick := nextInitializedTick(t.ticks, tick, lte)
		nextTickValue := math.Min(float64(maximum), float64(nextTick.Index))
		return int(nextTickValue), int(nextTickValue) == nextTick.Index
	}
}

func nextInitializedTick(ticks []Tick, tick int, lte bool) Tick {
	if lte {
		if isBelowSmallest(ticks, tick) {
			panic("below smallest")
		}
		i := sort.Search(len(ticks), func(i int) bool { return ticks[i].Index >= tick })
		if i < len(ticks) && ticks[i].Index == tick {
			return ticks[i]
		}
		// if tick is not initialized, we want the one before it
		if i > 0 {
			return ticks[i-1]
		}
		// This should be unreachable due to isBelowSmallest guard
		panic("logic error in nextInitializedTick (lte)")

	} else {
		if isAtOrAboveLargest(ticks, tick) {
			panic("at or above largest")
		}
		i := sort.Search(len(ticks), func(i int) bool { return ticks[i].Index > tick })
		// This should be a valid index due to isAtOrAboveLargest guard
		return ticks[i]
	}
}

func isAtOrAboveLargest(ticks []Tick, tick int) bool {
	if len(ticks) == 0 {
		return true // No ticks, so any tick is "at or above"
	}
	return tick >= ticks[len(ticks)-1].Index
}

func isBelowSmallest(ticks []Tick, tick int) bool {
	if len(ticks) == 0 {
		return true // No ticks, so any tick is "below"
	}
	return tick < ticks[0].Index
}
