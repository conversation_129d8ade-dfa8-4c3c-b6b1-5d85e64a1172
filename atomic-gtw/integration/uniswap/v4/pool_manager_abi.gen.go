// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package uniswapv4

import (
	"errors"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
)

// Reference imports to suppress errors if they are not otherwise used.
var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

// PoolManagerMetaData contains all meta data concerning the PoolManager contract.
var PoolManagerMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"initialOwner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"PoolId\",\"name\":\"id\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"tickLower\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"tickUpper\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"liquidityDelta\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"}],\"name\":\"ModifyLiquidity\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"PoolId\",\"name\":\"id\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"int128\",\"name\":\"amount0\",\"type\":\"int128\"},{\"indexed\":false,\"internalType\":\"int128\",\"name\":\"amount1\",\"type\":\"int128\"},{\"indexed\":false,\"internalType\":\"uint160\",\"name\":\"sqrtPriceX96\",\"type\":\"uint160\"},{\"indexed\":false,\"internalType\":\"uint128\",\"name\":\"liquidity\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"int24\",\"name\":\"tick\",\"type\":\"int24\"},{\"indexed\":false,\"internalType\":\"uint24\",\"name\":\"fee\",\"type\":\"uint24\"}],\"name\":\"Swap\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"slot\",\"type\":\"bytes32\"}],\"name\":\"extsload\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"startSlot\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"nSlots\",\"type\":\"uint256\"}],\"name\":\"extsload\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"slots\",\"type\":\"bytes32[]\"}],\"name\":\"extsload\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"slots\",\"type\":\"bytes32[]\"}],\"name\":\"exttload\",\"outputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"\",\"type\":\"bytes32[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"slot\",\"type\":\"bytes32\"}],\"name\":\"exttload\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"}]",
}

// PoolManagerABI is the input ABI used to generate the binding from.
// Deprecated: Use PoolManagerMetaData.ABI instead.
var PoolManagerABI = PoolManagerMetaData.ABI

// PoolManager is an auto generated Go binding around an Ethereum contract.
type PoolManager struct {
	PoolManagerCaller     // Read-only binding to the contract
	PoolManagerTransactor // Write-only binding to the contract
	PoolManagerFilterer   // Log filterer for contract events
}

// PoolManagerCaller is an auto generated read-only Go binding around an Ethereum contract.
type PoolManagerCaller struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// PoolManagerTransactor is an auto generated write-only Go binding around an Ethereum contract.
type PoolManagerTransactor struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// PoolManagerFilterer is an auto generated log filtering Go binding around an Ethereum contract events.
type PoolManagerFilterer struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// PoolManagerSession is an auto generated Go binding around an Ethereum contract,
// with pre-set call and transact options.
type PoolManagerSession struct {
	Contract     *PoolManager      // Generic contract binding to set the session for
	CallOpts     bind.CallOpts     // Call options to use throughout this session
	TransactOpts bind.TransactOpts // Transaction auth options to use throughout this session
}

// PoolManagerCallerSession is an auto generated read-only Go binding around an Ethereum contract,
// with pre-set call options.
type PoolManagerCallerSession struct {
	Contract *PoolManagerCaller // Generic contract caller binding to set the session for
	CallOpts bind.CallOpts      // Call options to use throughout this session
}

// PoolManagerTransactorSession is an auto generated write-only Go binding around an Ethereum contract,
// with pre-set transact options.
type PoolManagerTransactorSession struct {
	Contract     *PoolManagerTransactor // Generic contract transactor binding to set the session for
	TransactOpts bind.TransactOpts      // Transaction auth options to use throughout this session
}

// PoolManagerRaw is an auto generated low-level Go binding around an Ethereum contract.
type PoolManagerRaw struct {
	Contract *PoolManager // Generic contract binding to access the raw methods on
}

// PoolManagerCallerRaw is an auto generated low-level read-only Go binding around an Ethereum contract.
type PoolManagerCallerRaw struct {
	Contract *PoolManagerCaller // Generic read-only contract binding to access the raw methods on
}

// PoolManagerTransactorRaw is an auto generated low-level write-only Go binding around an Ethereum contract.
type PoolManagerTransactorRaw struct {
	Contract *PoolManagerTransactor // Generic write-only contract binding to access the raw methods on
}

// NewPoolManager creates a new instance of PoolManager, bound to a specific deployed contract.
func NewPoolManager(address common.Address, backend bind.ContractBackend) (*PoolManager, error) {
	contract, err := bindPoolManager(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &PoolManager{PoolManagerCaller: PoolManagerCaller{contract: contract}, PoolManagerTransactor: PoolManagerTransactor{contract: contract}, PoolManagerFilterer: PoolManagerFilterer{contract: contract}}, nil
}

// NewPoolManagerCaller creates a new read-only instance of PoolManager, bound to a specific deployed contract.
func NewPoolManagerCaller(address common.Address, caller bind.ContractCaller) (*PoolManagerCaller, error) {
	contract, err := bindPoolManager(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &PoolManagerCaller{contract: contract}, nil
}

// NewPoolManagerTransactor creates a new write-only instance of PoolManager, bound to a specific deployed contract.
func NewPoolManagerTransactor(address common.Address, transactor bind.ContractTransactor) (*PoolManagerTransactor, error) {
	contract, err := bindPoolManager(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &PoolManagerTransactor{contract: contract}, nil
}

// NewPoolManagerFilterer creates a new log filterer instance of PoolManager, bound to a specific deployed contract.
func NewPoolManagerFilterer(address common.Address, filterer bind.ContractFilterer) (*PoolManagerFilterer, error) {
	contract, err := bindPoolManager(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &PoolManagerFilterer{contract: contract}, nil
}

// bindPoolManager binds a generic wrapper to an already deployed contract.
func bindPoolManager(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := PoolManagerMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_PoolManager *PoolManagerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _PoolManager.Contract.PoolManagerCaller.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_PoolManager *PoolManagerRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _PoolManager.Contract.PoolManagerTransactor.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_PoolManager *PoolManagerRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _PoolManager.Contract.PoolManagerTransactor.contract.Transact(opts, method, params...)
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_PoolManager *PoolManagerCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _PoolManager.Contract.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_PoolManager *PoolManagerTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _PoolManager.Contract.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_PoolManager *PoolManagerTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _PoolManager.Contract.contract.Transact(opts, method, params...)
}

// Extsload is a free data retrieval call binding the contract method 0x1e2eaeaf.
//
// Solidity: function extsload(bytes32 slot) view returns(bytes32)
func (_PoolManager *PoolManagerCaller) Extsload(opts *bind.CallOpts, slot [32]byte) ([32]byte, error) {
	var out []interface{}
	err := _PoolManager.contract.Call(opts, &out, "extsload", slot)

	if err != nil {
		return *new([32]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([32]byte)).(*[32]byte)

	return out0, err

}

// Extsload is a free data retrieval call binding the contract method 0x1e2eaeaf.
//
// Solidity: function extsload(bytes32 slot) view returns(bytes32)
func (_PoolManager *PoolManagerSession) Extsload(slot [32]byte) ([32]byte, error) {
	return _PoolManager.Contract.Extsload(&_PoolManager.CallOpts, slot)
}

// Extsload is a free data retrieval call binding the contract method 0x1e2eaeaf.
//
// Solidity: function extsload(bytes32 slot) view returns(bytes32)
func (_PoolManager *PoolManagerCallerSession) Extsload(slot [32]byte) ([32]byte, error) {
	return _PoolManager.Contract.Extsload(&_PoolManager.CallOpts, slot)
}

// Extsload0 is a free data retrieval call binding the contract method 0x35fd631a.
//
// Solidity: function extsload(bytes32 startSlot, uint256 nSlots) view returns(bytes32[])
func (_PoolManager *PoolManagerCaller) Extsload0(opts *bind.CallOpts, startSlot [32]byte, nSlots *big.Int) ([][32]byte, error) {
	var out []interface{}
	err := _PoolManager.contract.Call(opts, &out, "extsload0", startSlot, nSlots)

	if err != nil {
		return *new([][32]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([][32]byte)).(*[][32]byte)

	return out0, err

}

// Extsload0 is a free data retrieval call binding the contract method 0x35fd631a.
//
// Solidity: function extsload(bytes32 startSlot, uint256 nSlots) view returns(bytes32[])
func (_PoolManager *PoolManagerSession) Extsload0(startSlot [32]byte, nSlots *big.Int) ([][32]byte, error) {
	return _PoolManager.Contract.Extsload0(&_PoolManager.CallOpts, startSlot, nSlots)
}

// Extsload0 is a free data retrieval call binding the contract method 0x35fd631a.
//
// Solidity: function extsload(bytes32 startSlot, uint256 nSlots) view returns(bytes32[])
func (_PoolManager *PoolManagerCallerSession) Extsload0(startSlot [32]byte, nSlots *big.Int) ([][32]byte, error) {
	return _PoolManager.Contract.Extsload0(&_PoolManager.CallOpts, startSlot, nSlots)
}

// Extsload1 is a free data retrieval call binding the contract method 0xdbd035ff.
//
// Solidity: function extsload(bytes32[] slots) view returns(bytes32[])
func (_PoolManager *PoolManagerCaller) Extsload1(opts *bind.CallOpts, slots [][32]byte) ([][32]byte, error) {
	var out []interface{}
	err := _PoolManager.contract.Call(opts, &out, "extsload1", slots)

	if err != nil {
		return *new([][32]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([][32]byte)).(*[][32]byte)

	return out0, err

}

// Extsload1 is a free data retrieval call binding the contract method 0xdbd035ff.
//
// Solidity: function extsload(bytes32[] slots) view returns(bytes32[])
func (_PoolManager *PoolManagerSession) Extsload1(slots [][32]byte) ([][32]byte, error) {
	return _PoolManager.Contract.Extsload1(&_PoolManager.CallOpts, slots)
}

// Extsload1 is a free data retrieval call binding the contract method 0xdbd035ff.
//
// Solidity: function extsload(bytes32[] slots) view returns(bytes32[])
func (_PoolManager *PoolManagerCallerSession) Extsload1(slots [][32]byte) ([][32]byte, error) {
	return _PoolManager.Contract.Extsload1(&_PoolManager.CallOpts, slots)
}

// Exttload is a free data retrieval call binding the contract method 0x9bf6645f.
//
// Solidity: function exttload(bytes32[] slots) view returns(bytes32[])
func (_PoolManager *PoolManagerCaller) Exttload(opts *bind.CallOpts, slots [][32]byte) ([][32]byte, error) {
	var out []interface{}
	err := _PoolManager.contract.Call(opts, &out, "exttload", slots)

	if err != nil {
		return *new([][32]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([][32]byte)).(*[][32]byte)

	return out0, err

}

// Exttload is a free data retrieval call binding the contract method 0x9bf6645f.
//
// Solidity: function exttload(bytes32[] slots) view returns(bytes32[])
func (_PoolManager *PoolManagerSession) Exttload(slots [][32]byte) ([][32]byte, error) {
	return _PoolManager.Contract.Exttload(&_PoolManager.CallOpts, slots)
}

// Exttload is a free data retrieval call binding the contract method 0x9bf6645f.
//
// Solidity: function exttload(bytes32[] slots) view returns(bytes32[])
func (_PoolManager *PoolManagerCallerSession) Exttload(slots [][32]byte) ([][32]byte, error) {
	return _PoolManager.Contract.Exttload(&_PoolManager.CallOpts, slots)
}

// Exttload0 is a free data retrieval call binding the contract method 0xf135baaa.
//
// Solidity: function exttload(bytes32 slot) view returns(bytes32)
func (_PoolManager *PoolManagerCaller) Exttload0(opts *bind.CallOpts, slot [32]byte) ([32]byte, error) {
	var out []interface{}
	err := _PoolManager.contract.Call(opts, &out, "exttload0", slot)

	if err != nil {
		return *new([32]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([32]byte)).(*[32]byte)

	return out0, err

}

// Exttload0 is a free data retrieval call binding the contract method 0xf135baaa.
//
// Solidity: function exttload(bytes32 slot) view returns(bytes32)
func (_PoolManager *PoolManagerSession) Exttload0(slot [32]byte) ([32]byte, error) {
	return _PoolManager.Contract.Exttload0(&_PoolManager.CallOpts, slot)
}

// Exttload0 is a free data retrieval call binding the contract method 0xf135baaa.
//
// Solidity: function exttload(bytes32 slot) view returns(bytes32)
func (_PoolManager *PoolManagerCallerSession) Exttload0(slot [32]byte) ([32]byte, error) {
	return _PoolManager.Contract.Exttload0(&_PoolManager.CallOpts, slot)
}

// PoolManagerModifyLiquidityIterator is returned from FilterModifyLiquidity and is used to iterate over the raw logs and unpacked data for ModifyLiquidity events raised by the PoolManager contract.
type PoolManagerModifyLiquidityIterator struct {
	Event *PoolManagerModifyLiquidity // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *PoolManagerModifyLiquidityIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(PoolManagerModifyLiquidity)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(PoolManagerModifyLiquidity)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *PoolManagerModifyLiquidityIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *PoolManagerModifyLiquidityIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// PoolManagerModifyLiquidity represents a ModifyLiquidity event raised by the PoolManager contract.
type PoolManagerModifyLiquidity struct {
	Id             [32]byte
	Sender         common.Address
	TickLower      *big.Int
	TickUpper      *big.Int
	LiquidityDelta *big.Int
	Salt           [32]byte
	Raw            types.Log // Blockchain specific contextual infos
}

// FilterModifyLiquidity is a free log retrieval operation binding the contract event 0xf208f4912782fd25c7f114ca3723a2d5dd6f3bcc3ac8db5af63baa85f711d5ec.
//
// Solidity: event ModifyLiquidity(bytes32 indexed id, address indexed sender, int24 tickLower, int24 tickUpper, int256 liquidityDelta, bytes32 salt)
func (_PoolManager *PoolManagerFilterer) FilterModifyLiquidity(opts *bind.FilterOpts, id [][32]byte, sender []common.Address) (*PoolManagerModifyLiquidityIterator, error) {

	var idRule []interface{}
	for _, idItem := range id {
		idRule = append(idRule, idItem)
	}
	var senderRule []interface{}
	for _, senderItem := range sender {
		senderRule = append(senderRule, senderItem)
	}

	logs, sub, err := _PoolManager.contract.FilterLogs(opts, "ModifyLiquidity", idRule, senderRule)
	if err != nil {
		return nil, err
	}
	return &PoolManagerModifyLiquidityIterator{contract: _PoolManager.contract, event: "ModifyLiquidity", logs: logs, sub: sub}, nil
}

// WatchModifyLiquidity is a free log subscription operation binding the contract event 0xf208f4912782fd25c7f114ca3723a2d5dd6f3bcc3ac8db5af63baa85f711d5ec.
//
// Solidity: event ModifyLiquidity(bytes32 indexed id, address indexed sender, int24 tickLower, int24 tickUpper, int256 liquidityDelta, bytes32 salt)
func (_PoolManager *PoolManagerFilterer) WatchModifyLiquidity(opts *bind.WatchOpts, sink chan<- *PoolManagerModifyLiquidity, id [][32]byte, sender []common.Address) (event.Subscription, error) {

	var idRule []interface{}
	for _, idItem := range id {
		idRule = append(idRule, idItem)
	}
	var senderRule []interface{}
	for _, senderItem := range sender {
		senderRule = append(senderRule, senderItem)
	}

	logs, sub, err := _PoolManager.contract.WatchLogs(opts, "ModifyLiquidity", idRule, senderRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(PoolManagerModifyLiquidity)
				if err := _PoolManager.contract.UnpackLog(event, "ModifyLiquidity", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseModifyLiquidity is a log parse operation binding the contract event 0xf208f4912782fd25c7f114ca3723a2d5dd6f3bcc3ac8db5af63baa85f711d5ec.
//
// Solidity: event ModifyLiquidity(bytes32 indexed id, address indexed sender, int24 tickLower, int24 tickUpper, int256 liquidityDelta, bytes32 salt)
func (_PoolManager *PoolManagerFilterer) ParseModifyLiquidity(log types.Log) (*PoolManagerModifyLiquidity, error) {
	event := new(PoolManagerModifyLiquidity)
	if err := _PoolManager.contract.UnpackLog(event, "ModifyLiquidity", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// PoolManagerSwapIterator is returned from FilterSwap and is used to iterate over the raw logs and unpacked data for Swap events raised by the PoolManager contract.
type PoolManagerSwapIterator struct {
	Event *PoolManagerSwap // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *PoolManagerSwapIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(PoolManagerSwap)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(PoolManagerSwap)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *PoolManagerSwapIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *PoolManagerSwapIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// PoolManagerSwap represents a Swap event raised by the PoolManager contract.
type PoolManagerSwap struct {
	Id           [32]byte
	Sender       common.Address
	Amount0      *big.Int
	Amount1      *big.Int
	SqrtPriceX96 *big.Int
	Liquidity    *big.Int
	Tick         *big.Int
	Fee          *big.Int
	Raw          types.Log // Blockchain specific contextual infos
}

// FilterSwap is a free log retrieval operation binding the contract event 0x40e9cecb9f5f1f1c5b9c97dec2917b7ee92e57ba5563708daca94dd84ad7112f.
//
// Solidity: event Swap(bytes32 indexed id, address indexed sender, int128 amount0, int128 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick, uint24 fee)
func (_PoolManager *PoolManagerFilterer) FilterSwap(opts *bind.FilterOpts, id [][32]byte, sender []common.Address) (*PoolManagerSwapIterator, error) {

	var idRule []interface{}
	for _, idItem := range id {
		idRule = append(idRule, idItem)
	}
	var senderRule []interface{}
	for _, senderItem := range sender {
		senderRule = append(senderRule, senderItem)
	}

	logs, sub, err := _PoolManager.contract.FilterLogs(opts, "Swap", idRule, senderRule)
	if err != nil {
		return nil, err
	}
	return &PoolManagerSwapIterator{contract: _PoolManager.contract, event: "Swap", logs: logs, sub: sub}, nil
}

// WatchSwap is a free log subscription operation binding the contract event 0x40e9cecb9f5f1f1c5b9c97dec2917b7ee92e57ba5563708daca94dd84ad7112f.
//
// Solidity: event Swap(bytes32 indexed id, address indexed sender, int128 amount0, int128 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick, uint24 fee)
func (_PoolManager *PoolManagerFilterer) WatchSwap(opts *bind.WatchOpts, sink chan<- *PoolManagerSwap, id [][32]byte, sender []common.Address) (event.Subscription, error) {

	var idRule []interface{}
	for _, idItem := range id {
		idRule = append(idRule, idItem)
	}
	var senderRule []interface{}
	for _, senderItem := range sender {
		senderRule = append(senderRule, senderItem)
	}

	logs, sub, err := _PoolManager.contract.WatchLogs(opts, "Swap", idRule, senderRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(PoolManagerSwap)
				if err := _PoolManager.contract.UnpackLog(event, "Swap", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseSwap is a log parse operation binding the contract event 0x40e9cecb9f5f1f1c5b9c97dec2917b7ee92e57ba5563708daca94dd84ad7112f.
//
// Solidity: event Swap(bytes32 indexed id, address indexed sender, int128 amount0, int128 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick, uint24 fee)
func (_PoolManager *PoolManagerFilterer) ParseSwap(log types.Log) (*PoolManagerSwap, error) {
	event := new(PoolManagerSwap)
	if err := _PoolManager.contract.UnpackLog(event, "Swap", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}
