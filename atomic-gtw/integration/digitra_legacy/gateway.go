package digitra_legacy

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Digitra",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) initJwtTokenUpdater(token string) error {
	expiresIn, err := g.api.UpdateJwtToken(token)
	if err != nil {
		return err
	}

	go func() {
		for {
			log.Printf("%s updating jwt token in %d seconds...", Exchange, expiresIn)

			time.Sleep(time.Duration(expiresIn-60) * time.Second) // Update 60 seconds before expiration

			log.Printf("%s updating jwt token...", Exchange)

			expiresIn, err = g.api.UpdateJwtToken(token)
			if err != nil {
				panic(fmt.Errorf("failed updateJwtToken, err: %s", err))
			}
		}
	}()

	return nil
}

func (g *Gateway) Connect() error {
	// Fetch jwt token
	if g.options.Token != "" {
		err := g.initJwtTokenUpdater(g.options.Token)
		if err != nil {
			return fmt.Errorf("Failed to init jwt token updater, err %s", err)
		}
	}

	g.accountGateway = NewAccountGateway(g.options, g.api, g.tickCh)
	if g.options.Token != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.options, g.api, g.tickCh)
	if err := g.marketDataGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.Markets()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0)
	for _, market := range res {
		commonMarkets = append(commonMarkets, gateway.Market{
			Exchange: Exchange,
			Symbol:   market.ID,
			Pair: gateway.Pair{
				Base:  strings.ToUpper(market.BaseCurrency),
				Quote: strings.ToUpper(market.QuoteCurrency),
			},
			PriceTick:        market.PriceIncrementSize,
			AmountTick:       market.IncrementSize,
			MinimumOrderSize: market.MinimumOrderSize,
			MarketType:       gateway.SpotMarket,
		})
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

type APIPriceLevel struct {
	Price  string `json:"price"`
	Amount string `json:"size"`
}

func parsePriceLevel(pl APIPriceLevel) (gateway.PriceLevel, error) {
	price, err := strconv.ParseFloat(pl.Price, 64)
	if err != nil {
		return gateway.PriceLevel{}, err
	}
	amount, err := strconv.ParseFloat(pl.Amount, 64)
	if err != nil {
		return gateway.PriceLevel{}, err
	}
	return gateway.PriceLevel{
		Price:  price,
		Amount: amount,
	}, nil
}

func convertPriceLevelsToDepth(priceLevels APIDepthBook) (gateway.DepthBook, error) {
	asks := make([]gateway.PriceLevel, 0, len(priceLevels.Asks))
	bids := make([]gateway.PriceLevel, 0, len(priceLevels.Bids))

	for _, ask := range priceLevels.Asks {
		priceLevel, err := parsePriceLevel(ask)
		if err != nil {
			return gateway.DepthBook{}, err
		}
		asks = append(asks, priceLevel)
	}
	for _, bid := range priceLevels.Bids {
		priceLevel, err := parsePriceLevel(bid)
		if err != nil {
			return gateway.DepthBook{}, err
		}
		bids = append(bids, priceLevel)
	}

	return gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook, err := convertPriceLevelsToDepth(depth)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	return depthBook, nil
}
