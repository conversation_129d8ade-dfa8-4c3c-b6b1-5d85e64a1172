package digitra_legacy

import (
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type MarketDataGateway struct {
	options            gateway.Options
	api                *API
	tickCh             chan gateway.Tick
	lastTradeTimeMicro int64
}

func NewMarketDataGateway(options gateway.Options, api *API, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options: options,
		api:     api,
		tickCh:  tickCh,
	}
}

func (g *MarketDataGateway) Connect() error {
	return nil
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	var refreshInterval time.Duration
	if g.options.RefreshIntervalMs > 0 {
		refreshInterval = time.Duration(g.options.RefreshIntervalMs * int(time.Millisecond))
	} else {
		refreshInterval = 2500 * time.Millisecond
	}

	// Subscribe to market updates
	log.Printf("Digitra polling for %d order book updates every %v", len(markets), refreshInterval)

	for _, market := range markets {
		go func(market gateway.Market) {
			for {
				err := g.updateMarket(market)
				if err != nil {
					log.Printf("Digitra market data failed update %s, err: %s", market.Symbol, err)
					time.Sleep(5 * time.Second)
					continue
				}

				time.Sleep(refreshInterval)
			}
		}(market)
	}

	return nil
}

func (g *MarketDataGateway) updateMarket(market gateway.Market) error {
	err := g.updateBooksFromAPI(market)
	if err != nil {
		return fmt.Errorf("updateBooksFromAPI err: %s", err)
	}

	err = g.updateTradesFromAPI(market)
	if err != nil {
		return fmt.Errorf("updateTradesFromAPI err: %s", err)
	}

	return nil
}

func (g *MarketDataGateway) updateTradesFromAPI(market gateway.Market) error {
	params := make(map[string]interface{})
	params["limit"] = 100
	// TODO: They are returning us with error 500 when we use this param
	// This is a bug on their side, we should send this so we can receive
	// less repeated data from their API.
	// var tradesFromTimeMicro int64
	// if g.lastTradeTimeMicro > 0 {
	// 	tradesFromTimeMicro = g.lastTradeTimeMicro
	// } else {
	// 	tradesFromTimeMicro = time.Now().UnixMicro()
	// }
	//params["time"] = tradesFromTimeMicro

	res, err := g.api.MarketTrades(market.Symbol, params)
	if err != nil {
		return err
	}

	err = g.processAPIMarketTrades(market, res)
	if err != nil {
		return err
	}

	return nil
}

func (g *MarketDataGateway) processAPIMarketTrades(market gateway.Market, trades []APITrade) error {
	events := make([]gateway.Event, 0, len(trades))

	var oldestTimeMicro int64
	for _, t := range trades {
		if t.TimeMicro > g.lastTradeTimeMicro {
			events = append(events, gateway.NewTradeEvent(gateway.Trade{
				Timestamp: time.Unix(0, t.TimeMicro*int64(time.Microsecond)),
				ID:        t.ID,
				Symbol:    market.Symbol,
				Direction: apiSideToGtw(t.Side),
				Price:     t.Price,
				Amount:    t.Size,
			}))
		}

		if t.TimeMicro > oldestTimeMicro {
			oldestTimeMicro = t.TimeMicro
		}
	}

	// Update last tracked trade time
	if oldestTimeMicro > g.lastTradeTimeMicro {
		g.lastTradeTimeMicro = oldestTimeMicro
	}

	if len(events) > 0 {
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          events,
		}
	}

	return nil
}

func (g *MarketDataGateway) updateBooksFromAPI(market gateway.Market) error {
	params := make(map[string]interface{})
	params["expand"] = "ORDER_BOOK"

	res, err := g.api.Market(market.Symbol, params)
	if err != nil {
		return err
	}

	return g.processAPIBookOrders(market, res.OrderBook)
}

func (g *MarketDataGateway) processAPIBookOrders(market gateway.Market, depth APIOrderBook) error {
	events := make([]gateway.Event, 0, len(depth.Bids)+len(depth.Asks))
	events = append(events, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: market.Symbol,
		},
	})

	for _, p := range depth.Bids {
		events = append(events, bookOrderToGtwEvent(market.Symbol, gateway.Bid, p))
	}
	for _, p := range depth.Asks {
		events = append(events, bookOrderToGtwEvent(market.Symbol, gateway.Ask, p))
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}

	return nil
}

func bookOrderToGtwEvent(symbol string, side gateway.Side, p APIOrderBookPrice) gateway.Event {
	return gateway.Event{
		Type: gateway.DepthEvent,
		Data: gateway.Depth{
			Symbol: symbol,
			Side:   side,
			Price:  p.Price,
			Amount: p.Size,
		},
	}
}
