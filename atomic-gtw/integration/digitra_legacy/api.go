package digitra_legacy

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"reflect"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiProductionSubdomain = "api"
	apiStagingSubdomain    = "stg-hb.cloud"
	apiBase                = "https://api.digitra.com"
	apiTradeBase           = "https://trade.%s.digitra.com"
	apiBalanceBase         = "https://balance.%s.digitra.com"
	apiMarkets             = "/v1/markets"
	apiMarket              = "/v1/markets/%s"
	apiTrades              = apiBase + "/v1/markets/%s/trades"
	apiOrders              = "/v1/orders"
	apiOrder               = "/v1/orders/%s"
	apiBalances            = "/v3/balances"
	apiFills               = "/v1/fills"
	apiDepthBook           = "/v1/markets/%s/orderbook?max_depth=%d"
)

func (a *API) buildURL(base, path string) string {
	var baseStr string
	if a.options.Staging {
		baseStr = fmt.Sprintf(base, apiStagingSubdomain)
	} else {
		baseStr = fmt.Sprintf(base, apiProductionSubdomain)
	}

	return baseStr + path
}

type APIMarket struct {
	ID                   string       `json:"id"`
	BaseCurrency         string       `json:"base_currency"`
	QuoteCurrency        string       `json:"quote_currency"`
	Enabled              bool         `json:"enabled"`
	IncrementSize        float64      `json:"increment_size"`
	MarketOrderTolerance float64      `json:"market_order_tolerance"`
	MinimumOrderSize     float64      `json:"minimum_order_size"`
	PriceIncrementSize   float64      `json:"price_increment_size"`
	OrderBook            APIOrderBook `json:"order_book"`
}

type APIOrderBook struct {
	Bids []APIOrderBookPrice `json:"bids"`
	Asks []APIOrderBookPrice `json:"asks"`
}

type APIOrderBookPrice struct {
	Price float64 `json:"price"`
	Size  float64 `json:"size"`
}

type APIOrder struct {
	CancelReason        string  `json:"cancel_reason"`
	CreatedAt           string  `json:"created_at"`
	CustomID            string  `json:"custom_id"`
	Fee                 float64 `json:"fee"`
	Filled              float64 `json:"filled"`
	FilledWeightedPrice float64 `json:"filled_weighted_price"`
	ID                  string  `json:"id"`
	Market              string  `json:"market"`
	Price               float64 `json:"price"`
	Side                string  `json:"side"`
	Size                float64 `json:"size"`
	Status              string  `json:"status"`
	TimeInForce         string  `json:"time_in_force"`
	TradingAccount      string  `json:"trading_account"`
	Type                string  `json:"type"`
	UpdatedAt           string  `json:"updated_at"`
}

type APIBalance struct {
	Amount        float64 `json:"amount,string"`
	AmountTrading float64 `json:"amount_trading,string"`
	Asset         string  `json:"asset"`
	UpdatedAt     string  `json:"updated_at"`
}

type APIFill struct {
	ID      string  `json:"id"`
	OrderID string  `json:"order_id"`
	Time    string  `json:"time"`
	Side    string  `json:"side"`
	Price   float64 `json:"price"`
	Size    float64 `json:"size"`
}

type APITrade struct {
	ID        string  `json:"id"`
	Price     float64 `json:"price,string"`
	Side      string  `json:"side"`
	Size      float64 `json:"size,string"`
	TimeMicro int64   `json:"time"`
}

type APIResponse struct {
	Result *json.RawMessage `json:"result,omitempty"`
	Errors []APIError       `json:"errors"`
	Msg    string           `json:"msg"`
}

type APIError struct {
	Field string `json:"field"`
	Msg   string `json:"msg"`
}

type API struct {
	options gateway.Options
	client  *utils.HttpClient
	token   string
}

func NewAPI(options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options: options,
		client:  client,
	}
}

func (a *API) SetToken(token string) {
	a.token = token
}

type APIJWTRes struct {
	Error       string `json:"error"`
	AccessToken string `json:"access_token"`
	ExpiresIn   int64  `json:"expires_in"`
	IDToken     string `json:"id_token"`
	TokenType   string `json:"token_type"`
}

func (a *API) UpdateJwtToken(refreshToken string) (expiresIn int64, err error) {
	var oauthURL, clientID string
	if a.options.Staging {
		oauthURL = "https://digitra-stg-hb.auth.us-east-1.amazoncognito.com/oauth2/token"
		clientID = "4iq9qprd7h5a31crrurl5dnomi"
	} else {
		oauthURL = "https://digitra-hb.auth.us-east-1.amazoncognito.com/oauth2/token"
		clientID = "6fqg49bracpr7ee7k1i0qlr21e"
	}

	params := url.Values{
		"grant_type":    {"refresh_token"},
		"client_id":     {clientID},
		"refresh_token": {refreshToken},
	}

	res, err := http.PostForm(oauthURL, params)
	if err != nil {
		return expiresIn, fmt.Errorf("failed oauth2 req, err: %s", err)
	}

	var data APIJWTRes
	err = json.NewDecoder(res.Body).Decode(&data)
	if err != nil {
		return expiresIn, fmt.Errorf("failed decode oauth2 body, err: %s", err)
	}

	if data.Error != "" {
		return expiresIn, fmt.Errorf("failed generate oauth2, err: %s", data.Error)
	}

	if data.AccessToken == "" {
		return expiresIn, fmt.Errorf("expected access_token to be present, instead got: %+v", data)
	}

	a.SetToken(data.AccessToken)

	if data.ExpiresIn > 0 {
		expiresIn = data.ExpiresIn
	} else {
		return expiresIn, fmt.Errorf("expected expiresIn to be present, instead got: %+v", data)
	}

	return expiresIn, nil
}

func (a *API) Markets() (res []APIMarket, err error) {
	req, err := a.newHttpRequest(http.MethodGet, a.buildURL(apiTradeBase, apiMarkets), nil, false)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) Market(symbol string, params map[string]interface{}) (res APIMarket, err error) {
	req, err := a.newHttpRequest(
		http.MethodGet,
		fmt.Sprintf(a.buildURL(apiTradeBase, apiMarket), symbol),
		params,
		false,
	)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) MarketTrades(symbol string, params map[string]interface{}) (res []APITrade, err error) {
	req, err := a.newHttpRequest(
		http.MethodGet,
		fmt.Sprintf(apiTrades, symbol),
		params,
		false,
	)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) Balances() (res []APIBalance, err error) {
	req, err := a.newHttpRequest(http.MethodGet, a.buildURL(apiBalanceBase, apiBalances), nil, true)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) CreateOrder(params map[string]interface{}) (res APIOrder, err error) {
	req, err := a.newHttpRequest(http.MethodPost, a.buildURL(apiTradeBase, apiOrders), params, true)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) CancelOrder(orderID string) (err error) {
	req, err := a.newHttpRequest(
		http.MethodDelete,
		fmt.Sprintf(a.buildURL(apiTradeBase, apiOrder), orderID),
		nil,
		true,
	)
	if err != nil {
		return err
	}

	err = a.makeHttpRequest(req, nil)
	return err
}

func (a *API) GetOrders(params map[string]interface{}) (res []APIOrder, err error) {
	req, err := a.newHttpRequest(http.MethodGet, a.buildURL(apiTradeBase, apiOrders), params, true)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) GetOrder(orderID string, params map[string]interface{}) (res APIOrder, err error) {
	req, err := a.newHttpRequest(
		http.MethodGet,
		fmt.Sprintf(a.buildURL(apiTradeBase, apiOrder), orderID),
		params,
		true,
	)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) GetFills(params map[string]interface{}) (res []APIFill, err error) {
	req, err := a.newHttpRequest(http.MethodGet, a.buildURL(apiTradeBase, apiFills), params, true)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APIDepthBook struct {
	Asks []struct {
		Price  string `json:"price"`
		Amount string `json:"size"`
	} `json:"asks"`
	Bids []struct {
		Price  string `json:"price"`
		Amount string `json:"size"`
	} `json:"bids"`
}

func (a *API) DepthBook(symbol string, params gateway.GetDepthParams) (res APIDepthBook, err error) {
	if params.Limit == 0 {
		params.Limit = 100
	}
	reqUrl := apiBase + fmt.Sprintf(apiDepthBook, symbol, params.Limit)
	req, err := a.newHttpRequest(http.MethodGet, reqUrl, nil, false)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) newHttpRequest(method string, uri string, params map[string]interface{}, signed bool) (*http.Request, error) {
	var reqBody io.Reader

	if method != http.MethodGet && params != nil {
		reqData, err := json.Marshal(params)
		if err != nil {
			return nil, fmt.Errorf("params json marshal err: %s", err)
		}
		reqBody = bytes.NewReader(reqData)
	}

	req, err := http.NewRequest(method, uri, reqBody)
	if err != nil {
		return nil, err
	}

	if method == http.MethodGet && params != nil {
		queryParams := url.Values{}
		for k, v := range params {
			value := reflect.ValueOf(v)

			if value.Kind() == reflect.Slice || value.Kind() == reflect.Array {
				for i := 0; i < value.Len(); i++ {
					queryParams.Add(k, fmt.Sprintf("%v", value.Index(i).Interface()))
				}
			} else {
				queryParams.Add(k, fmt.Sprintf("%v", v))
			}
		}

		req.URL.RawQuery = queryParams.Encode()
	}

	if signed {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", a.token))
	}

	if reqBody != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return req, nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.client.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	if res.StatusCode < 200 || res.StatusCode >= 300 {
		return fmt.Errorf("err [%s] http status code [%d] body: %s", req.URL.String(), res.StatusCode, body)
	}

	if len(body) > 0 {
		var apiRes APIResponse
		err = json.Unmarshal(body, &apiRes)
		if err != nil {
			return fmt.Errorf("failed to unmarshal body [%s] status code [%d] into APIResponse, err: %s", string(body), res.StatusCode, err)
		}

		if len(apiRes.Errors) > 0 {
			return fmt.Errorf("api errors res, %s", string(body))
		}

		if responseObject != nil {
			var data []byte
			if apiRes.Result != nil {
				data = *apiRes.Result
			} else {
				data = body
			}

			err = json.Unmarshal(data, responseObject)
			if err != nil {
				return fmt.Errorf("failed to unmarshal data [%s] into responseObject, err: %s", string(data), err)
			}
		}
	}

	return nil
}
