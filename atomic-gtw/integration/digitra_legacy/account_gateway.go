package digitra_legacy

import (
	"fmt"
	"log"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api                *API
	tickCh             chan gateway.Tick
	options            gateway.Options
	trackedFills       map[string]struct{}
	trackingFills      bool
	trackingFillsMutex sync.Mutex
}

func NewAccountGateway(options gateway.Options, api *API, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		options:      options,
		api:          api,
		tickCh:       tickCh,
		trackedFills: make(map[string]struct{}),
	}
}

func (g *AccountGateway) Connect() error {
	err := g.setInitialFills()
	if err != nil {
		return fmt.Errorf("set fills err: %s", err)
	}

	return nil
}

func (g *AccountGateway) fetchFills() ([]APIFill, error) {
	params := make(map[string]interface{})
	params["page_size"] = 100
	fills, err := g.api.GetFills(params)
	if err != nil {
		return nil, fmt.Errorf("failed to get fills, err: %s", err)
	}

	return fills, nil
}

func (g *AccountGateway) setInitialFills() error {
	fills, err := g.fetchFills()
	if err != nil {
		return err
	}

	for _, fill := range fills {
		g.trackedFills[fill.ID] = struct{}{}
	}

	return nil
}

func (g *AccountGateway) trackOrderUpdates() {
	var refreshInterval time.Duration
	if g.options.RefreshIntervalMs > 0 {
		refreshInterval = time.Duration(g.options.RefreshIntervalMs * int(time.Millisecond))
	} else {
		refreshInterval = 2500 * time.Millisecond
	}

	log.Printf("Digitra polling for account fills every %v", refreshInterval)

	for {
		fills, err := g.fetchFills()
		if err != nil {
			log.Printf("Digitra failed to get fills, err: %s", err)
			time.Sleep(refreshInterval)
			continue
		}

		events := make([]gateway.Event, 0)
		for _, fill := range fills {
			if _, ok := g.trackedFills[fill.ID]; !ok {
				var side gateway.Side
				if fill.Side == "SELL" {
					side = gateway.Ask
				} else {
					side = gateway.Bid
				}

				events = append(events, gateway.NewFillEvent(gateway.Fill{
					ID:      fill.ID,
					OrderID: fill.OrderID,
					Side:    side,
					Amount:  fill.Size,
					Price:   fill.Price,
				}))
			}

			g.trackedFills[fill.ID] = struct{}{}
		}

		if len(events) > 0 {
			g.tickCh <- gateway.TickWithEvents(events...)
		}

		time.Sleep(refreshInterval)
	}
}

func apiOrderStatusToGtw(status string) gateway.OrderState {
	switch status {
	case "SUBMITTING":
		return gateway.OrderSent
	case "OPEN":
		return gateway.OrderOpen
	case "PENDING_CANCELING":
		return gateway.OrderOpen
	case "CANCELED":
		return gateway.OrderCancelled
	case "CANCELED_PENDING_BALANCE":
		return gateway.OrderCancelled
	case "FILLED":
		return gateway.OrderFullyFilled
	case "PARTIALLY_FILLED":
		return gateway.OrderPartiallyFilled
	}

	return gateway.OrderUnknown
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	res, err := g.api.Balances()
	if err != nil {
		return []gateway.Balance{}, err
	}

	balances := make([]gateway.Balance, 0)
	for _, bal := range res {
		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(bal.Asset),
			Total:     bal.Amount + bal.AmountTrading,
			Available: bal.Amount,
		})
	}

	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	params := make(map[string]interface{})
	params["status"] = []string{"OPEN", "PARTIALLY_FILLED", "SUBMITTING"}
	params["market"] = market.Symbol
	params["page_size"] = "100"

	openOrders, err := g.api.GetOrders(params)
	if err != nil {
		return []gateway.Order{}, err
	}

	orders := make([]gateway.Order, 0)
	for _, resOrder := range openOrders {
		orders = append(orders, gateway.Order{
			Market:           market,
			ID:               resOrder.ID,
			State:            apiOrderStatusToGtw(resOrder.Status),
			Side:             apiSideToGtw(resOrder.Side),
			Amount:           resOrder.Size,
			Price:            resOrder.Price,
			FilledAmount:     resOrder.Filled,
			FilledMoneyValue: resOrder.Filled * resOrder.FilledWeightedPrice,
			AvgPrice:         resOrder.FilledWeightedPrice,
		})
	}

	// Start order tracing if not started yet
	g.trackingFillsMutex.Lock()
	if !g.trackingFills {
		go g.trackOrderUpdates()
		g.trackingFills = true
	}
	g.trackingFillsMutex.Unlock()

	return orders, nil
}

func apiSideToGtw(side string) gateway.Side {
	if side == "SELL" {
		return gateway.Ask
	} else if side == "BUY" {
		return gateway.Bid
	}

	return ""
}

var insuficientBalanceMatch = regexp.MustCompile(`insufficient_balance`)
var orderNotFoundMatch = regexp.MustCompile(`Not found`)

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	params := make(map[string]interface{})

	if order.Side == gateway.Bid {
		params["side"] = "BUY"
	} else {
		params["side"] = "SELL"
	}

	params["type"] = "LIMIT"
	params["market"] = order.Market.Symbol
	params["size"] = utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick)
	params["price"] = utils.FloatToStringWithTick(order.Price, order.Market.PriceTick)

	// Tag the order with a custom order ID for later trackig if necessary
	customOrderID := order.ClientOrderID
	if customOrderID == "" {
		customOrderID = uuid.New().String()
	}
	params["custom_id"] = customOrderID

	res, err := g.api.CreateOrder(params)
	if err != nil {
		switch {
		case insuficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		default:
			// Check if order was created, so we don't return an unkown error
			// and cause the bot to restart
			log.Printf("%s [sendOrder] unkown error [%s] returned, checking if order was created by custom_id [%s]", Exchange, err, customOrderID)

			// Wait a second, the order might still be being commited to their DB
			time.Sleep(1 * time.Second)

			getOrderRes, getOrderErr := g.api.GetOrder("custom:"+customOrderID, nil)
			if getOrderErr != nil {
				if orderNotFoundMatch.MatchString(getOrderErr.Error()) {
					return "", &gateway.OrderNotOpenedErr{Err: err}
				} else {
					return "", fmt.Errorf("send order err: %w, custom get order err: %s", err, getOrderErr)
				}
			}

			if getOrderRes.ID != "" {
				log.Printf("%s [sendOrder] order custom_id [%s] was actually created with id [%s]", Exchange, customOrderID, getOrderRes.ID)
				res = getOrderRes
			} else {
				return "", &gateway.OrderNotOpenedErr{Err: err}
			}
		}
	}

	if res.ID == "" {
		return "", fmt.Errorf("empty order id")
	}

	if g.options.WaitForOrderEntry {
		// Check if order is not already passed the SUBMITTING status
		if res.Status != "SUBMITTING" {
			log.Printf("%s [waitForOrderEntry] skipping, already in status [%s] immediately after open", Exchange, res.Status)
			return res.ID, nil
		}

		maxWait := 60 * time.Second
		if g.options.MaxWaitForOrderEntry > 0 {
			maxWait = g.options.MaxWaitForOrderEntry
		}

		// Wait for order to be open
		startWait := time.Now()
		lastLog := time.Now()
		waitUntil := startWait.Add(maxWait)
		waitErr := error(nil)
		for {
			orderRes, err := g.api.GetOrder(res.ID, nil)
			if err != nil {
				log.Printf("%s [waitForOrderEntry] error getting order %s: %s", Exchange, res.ID, err)

				if time.Now().After(waitUntil) {
					return res.ID, fmt.Errorf("timeout waiting for order to be open, last err: %s", err)
				}

				time.Sleep(3 * time.Second)
				continue
			}

			if orderRes.Status != "SUBMITTING" {
				break
			}

			if time.Now().After(waitUntil) {
				waitErr = fmt.Errorf("timeout waiting for order to be open")
				break
			}

			if time.Since(lastLog).Seconds() >= 10 {
				waitingSince := time.Since(startWait)
				log.Printf("%s [waitForOrderEntry] order [%s] max wait [%v] already waiting for confirmation for [%v]", Exchange, res.ID, maxWait, waitingSince)
				lastLog = time.Now()
			}

			time.Sleep(1 * time.Second)
		}

		return res.ID, waitErr
	}

	return res.ID, nil
}

var alreadyCancelledMatch = regexp.MustCompile(`while in status CANCELED`)

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	err := g.api.CancelOrder(order.ID)
	if err != nil {
		switch {
		case alreadyCancelledMatch.MatchString(err.Error()):
			return gateway.AlreadyCancelledErr
		default:
			return err
		}
	}

	return err
}
