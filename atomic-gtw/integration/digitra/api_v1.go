package digitra

import (
	"fmt"
	"net/http"
)

const (
	apiBaseV1      = "https://api.digitra.com"
	apiMarketsV1   = "/v1/markets?limit=200"
	apiOrderBookV1 = "/v1/markets/%s/orderbook?limit=100"
	apiTradesV1    = "/v1/markets/%s/trades"
	apiBalancesv1  = "/v1/wallet/balances?limit=200"
	apiOrdersV1    = "/v1/trade/orders"
	apiOrderV1     = "/v1/trade/orders/%s"
	apiFillsV1     = "/v1/trade/fills"
)

func (a *API) MarketsV1() (res []APIMarket, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiMarketsV1, nil, false)
	if err != nil {
		return res, err
	}

	err = MakeHttpRequest[APIMarket](a, req, &res)
	return res, err
}

func (a *API) OrderBookV1(symbol string) (res APIOrderBook, err error) {
	req, err := a.newHttpRequest(http.MethodGet, fmt.Sprintf(apiOrderBookV1, symbol), nil, false)
	if err != nil {
		return res, err
	}

	err = MakeHttpRequest[APIOrderBook](a, req, &res)
	return res, err
}

func (a *API) MarketTradesV1(symbol string, params map[string]interface{}) (res []APITrade, err error) {
	req, err := a.newHttpRequest(http.MethodGet, fmt.Sprintf(apiTradesV1, symbol), params, false)
	if err != nil {
		return res, err
	}

	err = MakeHttpRequest[APITrade](a, req, &res)
	return res, err
}

func (a *API) BalancesV1() (res []APIBalance, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiBalancesv1, nil, true)
	if err != nil {
		return res, err
	}

	err = MakeHttpRequest[APIBalance](a, req, &res)
	return res, err
}

func (a *API) GetOrdersV1(params map[string]interface{}) (res []APIOrder, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiOrdersV1, params, true)
	if err != nil {
		return res, err
	}

	err = MakeHttpRequest[APIOrder](a, req, &res)
	return res, err
}

func (a *API) GetOrderV1(orderID string, params map[string]interface{}) (res APIOrder, err error) {
	req, err := a.newHttpRequest(http.MethodGet, fmt.Sprintf(apiOrderV1, orderID), params, true)
	if err != nil {
		return res, err
	}

	err = MakeHttpRequest[APIOrder](a, req, &res)
	return res, err
}

func (a *API) CreateOrderV1(params map[string]interface{}) (res APIOrder, err error) {
	req, err := a.newHttpRequest(http.MethodPost, apiOrdersV1, params, true)
	if err != nil {
		return res, err
	}

	err = MakeHttpRequest[APIOrder](a, req, &res)
	return res, err
}

func (a *API) CancelOrderV1(orderID string) (err error) {
	req, err := a.newHttpRequest(http.MethodDelete, fmt.Sprintf(apiOrderV1, orderID), nil, true)
	if err != nil {
		return err
	}

	err = MakeHttpRequest[any](a, req, nil)
	return err
}

func (a *API) GetFillsV1(params map[string]interface{}) (res []APIFill, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiFillsV1, params, true)
	if err != nil {
		return res, err
	}

	err = MakeHttpRequest[APIFill](a, req, &res)
	return res, err
}
