package digitra

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api                *API
	tickCh             chan gateway.Tick
	options            gateway.Options
	trackedFills       map[string]struct{}
	trackingFills      bool
	trackingFillsMutex sync.Mutex
}

func NewAccountGateway(options gateway.Options, api *API, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		options:      options,
		api:          api,
		tickCh:       tickCh,
		trackedFills: make(map[string]struct{}),
	}
}

func (g *AccountGateway) Connect() error {
	if g.options.PollAccountData {
		if err := g.TrackPolling(); err != nil {
			return fmt.Errorf("track polling err: %s", err)
		}
	} else {
		if err := g.TrackWebsocket(); err != nil {
			return fmt.Errorf("track websocket err: %s", err)
		}
	}

	return nil
}

func (g *AccountGateway) TrackWebsocket() error {
	ws := utils.NewWsClient()

	err := ws.Connect("wss://ws.digitra.com")
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	timestamp := time.Now().UnixMicro()
	signature := getSignatureWebSocket(timestamp, g.options.ApiSecret)
	authRequest := fmt.Sprintf(`{"op": "AUTH", "api_key": "%s", "timestamp": "%d", "signature": "%s"}`, g.options.ApiKey, timestamp, signature)
	if err = ws.WriteMessage([]byte(authRequest)); err != nil {
		return fmt.Errorf("failed write account msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)

	authErr := make(chan error)
	go matchNextMessage(ch, authErr, "AUTHENTICATED")
	select {
	case err := <-authErr:
		if err != nil {
			closeErr := ws.Close()
			if closeErr != nil {
				log.Printf("Failed to close ws connection after auth err: %s, authErr: %s", closeErr, err)
			}

			return fmt.Errorf("auth err: %s", err)
		}
	case <-time.After(5 * time.Second):
		err := ws.Close()
		if err != nil {
			log.Printf("Failed to close ws connection after timeout: %s", err)
		}

		return fmt.Errorf("Timed out waiting for auth response")
	}

	fillsSubRequest := fmt.Sprintf(`{"op": "SUBSCRIBE", "channel": "FILLS"}`)
	if err = ws.WriteMessage([]byte(fillsSubRequest)); err != nil {
		return fmt.Errorf("failed write fills sub request to ws: %s", err)
	}

	subErr := make(chan error)
	go matchNextMessage(ch, subErr, "SUBSCRIBED")
	select {
	case err := <-subErr:
		if err != nil {
			closeErr := ws.Close()
			if closeErr != nil {
				log.Printf("Failed to close ws connection after sub err: %s, subErr: %s", closeErr, err)
			}

			return fmt.Errorf("sub err: %s", err)
		}
	case <-time.After(5 * time.Second):
		err := ws.Close()
		if err != nil {
			log.Printf("Failed to close ws connection after timeout: %s", err)
		}

		return fmt.Errorf("Timed out waiting for sub response")
	}

	go g.messageHandler(ch)
	go websocketPinger(ws)

	return nil
}

func matchNextMessage(ch chan []byte, errCh chan error, match string) {
	var err error

	msg := <-ch
	if !strings.Contains(string(msg), match) {
		err = fmt.Errorf("failed match: %s", string(msg))
	}

	errCh <- err
}

func getSignatureWebSocket(timestamp int64, secret string) string {
	payload := fmt.Sprintf("%dauth", timestamp)
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(payload))
	return hex.EncodeToString(h.Sum(nil))
}

func websocketPinger(ws *utils.WsClient) {
	ticker := time.NewTicker(15 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := ws.WriteMessage([]byte(`{"op":"PING"}`)); err != nil {
				log.Printf("Failed to write ping message to ws: %s", err)
			}
		}
	}
}

type WsMessage struct {
	Type    string          `json:"type"`
	Channel string          `json:"channel"`
	Market  string          `json:"market"`
	Data    json.RawMessage `json:"data"`
}

type WsFill struct {
	Aggressor     bool    `json:"aggressor"`
	Fee           float64 `json:"fee,string"`
	FeeCurrency   string  `json:"fee_currency"`
	ID            string  `json:"id"`
	OrderCustomID string  `json:"order_custom_id"`
	OrderID       string  `json:"order_id"`
	Price         float64 `json:"price,string"`
	Side          string  `json:"side"`
	Size          float64 `json:"size,string"`
	Time          int64   `json:"time"`
}

func (g *AccountGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		msg := WsMessage{}
		if err := json.Unmarshal(data, &msg); err != nil {
			log.Printf("%s failed to unmarshal msg [%s], err: %s", Exchange, string(data), err)
			continue
		}

		if msg.Channel == "FILLS" {
			fill := WsFill{}
			if err := json.Unmarshal(msg.Data, &fill); err != nil {
				log.Printf("%s failed to unmarshal fill [%s], err: %s", Exchange, string(msg.Data), err)
				continue
			}

			var side gateway.Side
			if fill.Side == "SELL" {
				side = gateway.Ask
			} else {
				side = gateway.Bid
			}

			g.tickCh <- gateway.TickWithEvents(gateway.NewFillEvent(gateway.Fill{
				Timestamp:     gateway.ParseTimestamp(fill.Time),
				ID:            fill.ID,
				OrderID:       fill.OrderID,
				ClientOrderID: fill.OrderCustomID,
				Side:          side,
				Amount:        fill.Size,
				Price:         fill.Price,
				Fee:           fill.Fee,
				FeeAsset:      fill.FeeCurrency,
			}))
		}
	}
}

func (g *AccountGateway) TrackPolling() error {
	err := g.setInitialFills()
	if err != nil {
		return fmt.Errorf("set fills err: %s", err)
	}
	go g.trackOrderUpdates()

	return nil
}

func (g *AccountGateway) fetchFills() ([]APIFill, error) {
	params := make(map[string]interface{})
	var err error
	var fills []APIFill

	params["limit"] = 20
	fills, err = g.api.GetFillsV1(params)
	if err != nil {
		return nil, fmt.Errorf("failed to get fills, err: %s", err)
	}

	return fills, nil
}

func (g *AccountGateway) setInitialFills() error {
	fills, err := g.fetchFills()
	if err != nil {
		return err
	}

	for _, fill := range fills {
		g.trackedFills[fill.ID] = struct{}{}
	}

	return nil
}

func (g *AccountGateway) trackOrderUpdates() {
	var refreshInterval time.Duration
	if g.options.RefreshIntervalMs > 0 {
		refreshInterval = time.Duration(g.options.RefreshIntervalMs * int(time.Millisecond))
	} else {
		refreshInterval = 2500 * time.Millisecond
	}

	log.Printf("Digitra polling for account fills every %v", refreshInterval)

	for {
		fills, err := g.fetchFills()
		if err != nil {
			log.Printf("Digitra failed to get fills, err: %s", err)
			time.Sleep(refreshInterval)
			continue
		}

		events := make([]gateway.Event, 0)
		for _, fill := range fills {
			if _, ok := g.trackedFills[fill.ID]; !ok {
				var side gateway.Side
				if fill.Side == "SELL" {
					side = gateway.Ask
				} else {
					side = gateway.Bid
				}

				events = append(events, gateway.NewFillEvent(gateway.Fill{
					Timestamp:     gateway.ParseTimestamp(fill.Time),
					ID:            fill.ID,
					OrderID:       fill.OrderID,
					ClientOrderID: fill.OrderCustomID,
					Side:          side,
					Amount:        fill.Size,
					Price:         fill.Price,
					Fee:           fill.Fee,
					FeeAsset:      fill.FeeCurrency,
				}))
			}

			g.trackedFills[fill.ID] = struct{}{}
		}

		if len(events) > 0 {
			g.tickCh <- gateway.TickWithEvents(events...)
		}

		time.Sleep(refreshInterval)
	}
}

func apiOrderStatusToGtw(status string) gateway.OrderState {
	switch status {
	case "SUBMITTING":
		return gateway.OrderSent
	case "OPEN":
		return gateway.OrderOpen
	case "PENDING_CANCELING":
		return gateway.OrderOpen
	case "CANCELED":
		return gateway.OrderCancelled
	case "CANCELED_PENDING_BALANCE":
		return gateway.OrderCancelled
	case "FILLED":
		return gateway.OrderFullyFilled
	case "PARTIALLY_FILLED":
		return gateway.OrderPartiallyFilled
	}

	return gateway.OrderUnknown
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	balanceRes, err := g.api.BalancesV1()
	if err != nil {
		return []gateway.Balance{}, err
	}

	return balancesToCommon(balanceRes), nil
}

func balancesToCommon(balances []APIBalance) []gateway.Balance {
	commonBalances := make([]gateway.Balance, len(balances))
	for i, bal := range balances {
		commonBalances[i] = gateway.Balance{
			Asset:     strings.ToUpper(bal.Asset),
			Total:     bal.Amount + bal.AmountOrders,
			Available: bal.Amount,
		}
	}

	return commonBalances
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	fetchOrders := func(status string) ([]APIOrder, error) {
		params := make(map[string]interface{})
		params["limit"] = 200
		params["market"] = market.Symbol
		params["status"] = status
		return g.api.GetOrdersV1(params)
	}

	openOrders, err := fetchOrders("OPEN")
	if err != nil {
		return nil, fmt.Errorf("failed to get open orders, err: %s", err)
	}
	partiallyFilledOrders, err := fetchOrders("PARTIALLY_FILLED")
	if err != nil {
		return nil, fmt.Errorf("failed to get partially filled orders, err: %s", err)
	}
	submittingOrders, err := fetchOrders("SUBMITTING")
	if err != nil {
		return nil, fmt.Errorf("failed to get submitting orders, err: %s", err)
	}

	allOrders := append(
		ordersToCommon(openOrders, market),
		append(
			ordersToCommon(partiallyFilledOrders, market),
			ordersToCommon(submittingOrders, market)...,
		)...,
	)

	return allOrders, nil
}

func ordersToCommon(orders []APIOrder, market gateway.Market) []gateway.Order {
	commonOrders := make([]gateway.Order, len(orders))
	for i, o := range orders {
		commonOrders[i] = gateway.Order{
			Market:           market,
			ID:               o.ID,
			ClientOrderID:    o.CustomID,
			State:            apiOrderStatusToGtw(o.Status),
			Side:             apiSideToGtw(o.Side),
			Amount:           o.Size,
			Price:            o.Price,
			FilledAmount:     o.Filled,
			FilledMoneyValue: o.Filled * o.FilledWeightedPrice,
			AvgPrice:         o.FilledWeightedPrice,
			Fee:              o.Fee,
			FeeAsset:         o.FeeCurrency,
		}
	}
	return commonOrders
}

func apiSideToGtw(side string) gateway.Side {
	if side == "SELL" {
		return gateway.Ask
	} else if side == "BUY" {
		return gateway.Bid
	}

	return ""
}

var insufficientFundsMatch = regexp.MustCompile(`insufficient_balance`)
var minOrderSizeMatch = regexp.MustCompile(`size is greater or equal`)

func (g *AccountGateway) SendOrder(order gateway.Order) (string, error) {
	params := g.buildOrderParams(order)
	res, err := g.api.CreateOrderV1(params)
	if err != nil {
		switch {
		case insufficientFundsMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		case minOrderSizeMatch.MatchString(err.Error()):
			return "", gateway.MinOrderSizeErr
		default:
			return "", err
		}
	}

	if res.ID == "" {
		return "", fmt.Errorf("order id is empty")
	}

	if g.options.WaitForOrderEntry {
		// Check if order is not already passed the SUBMITTING status
		if res.Status != "SUBMITTING" {
			log.Printf("%s [waitForOrderEntry] skipping, already in status [%s] immediately after open", Exchange, res.Status)
			return res.ID, nil
		}

		maxWait := 60 * time.Second
		if g.options.MaxWaitForOrderEntry > 0 {
			maxWait = g.options.MaxWaitForOrderEntry
		}

		// Wait for order to be open
		startWait := time.Now()
		lastLog := time.Now()
		waitUntil := startWait.Add(maxWait)
		waitErr := error(nil)
		for {
			orderRes, err := g.api.GetOrderV1(res.ID, nil)
			if err != nil {
				log.Printf("%s [waitForOrderEntry] error getting order %s: %s", Exchange, res.ID, err)

				if time.Now().After(waitUntil) {
					return res.ID, fmt.Errorf("timeout waiting for order to be open, last err: %s", err)
				}

				time.Sleep(3 * time.Second)
				continue
			}

			if orderRes.Status != "SUBMITTING" {
				break
			}

			if time.Now().After(waitUntil) {
				waitErr = fmt.Errorf("timeout waiting for order to be open")
				break
			}

			if time.Since(lastLog).Seconds() >= 10 {
				waitingSince := time.Since(startWait)
				log.Printf("%s [waitForOrderEntry] order [%s] max wait [%v] already waiting for confirmation for [%v]", Exchange, res.ID, maxWait, waitingSince)
				lastLog = time.Now()
			}

			time.Sleep(1 * time.Second)
		}

		return res.ID, waitErr
	}

	return res.ID, nil
}

// buildOrderParams build order parameters
func (g *AccountGateway) buildOrderParams(order gateway.Order) map[string]any {
	params := make(map[string]interface{})

	if order.Side == gateway.Bid {
		params["side"] = "BUY"
	} else if order.Side == gateway.Ask {
		params["side"] = "SELL"
	}
	params["type"] = "LIMIT"
	params["market"] = order.Market.Symbol
	params["size"] = utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick)
	params["price"] = utils.FloatToStringWithTick(order.Price, order.Market.PriceTick)
	if order.ClientOrderID != "" {
		params["custom_id"] = order.ClientOrderID
	}

	return params
}

var alreadyCancelledMatch = regexp.MustCompile(`while in status CANCELED`)

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	err := g.api.CancelOrderV1(order.ID)
	if err != nil {
		switch {
		case alreadyCancelledMatch.MatchString(err.Error()):
			return gateway.AlreadyCancelledErr
		default:
			return err
		}
	}
	return err
}
