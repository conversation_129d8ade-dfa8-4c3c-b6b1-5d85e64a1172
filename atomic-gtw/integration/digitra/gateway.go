package digitra

import (
	"fmt"
	"log"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/integration/digitra_legacy"
)

var Exchange = gateway.Exchange{
	Name: "Digitra",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	if options.ApiVersion == "legacy" {
		log.Printf("%s using legacy gateway", Exchange.Name)
		return digitra_legacy.NewGateway(options)
	}

	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.options, g.api, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.options, g.api, g.tickCh)
	if err := g.marketDataGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.MarketsV1()
	if err != nil {
		return nil, err
	}

	commonMarkets := parseToCommonMarket(res)
	return commonMarkets, nil
}

func parseToCommonMarket(res []APIMarket) []gateway.Market {
	commonMarkets := make([]gateway.Market, 0, len(res))
	for _, market := range res {
		commonMarkets = append(commonMarkets, gateway.Market{
			Exchange: Exchange,
			Symbol:   market.ID,
			Pair: gateway.Pair{
				Base:  strings.ToUpper(market.BaseCurrency),
				Quote: strings.ToUpper(market.QuoteCurrency),
			},
			PriceTick:        market.PriceIncrementSize,
			AmountTick:       market.IncrementSize,
			MinimumOrderSize: market.MinimumOrderSize,
			MarketType:       gateway.SpotMarket,
		})
	}

	return commonMarkets
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

type APIPriceLevel struct {
	Price  float64 `json:"price,string"`
	Amount float64 `json:"size,string"`
}

func convertPriceLevelsToDepth(priceLevels APIOrderBook) (gateway.DepthBook, error) {
	asks := make([]gateway.PriceLevel, 0, len(priceLevels.Asks))
	bids := make([]gateway.PriceLevel, 0, len(priceLevels.Bids))

	for _, ask := range priceLevels.Asks {
		asks = append(asks, gateway.PriceLevel{
			Price:  ask.Price,
			Amount: ask.Size,
		})
	}
	for _, bid := range priceLevels.Bids {
		bids = append(bids, gateway.PriceLevel{
			Price:  bid.Price,
			Amount: bid.Size,
		})
	}

	return gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.OrderBookV1(market.Symbol)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook, err := convertPriceLevelsToDepth(depth)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	return depthBook, nil
}
