package digitra

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"reflect"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type APIMarket struct {
	ID                 string  `json:"id"`
	BaseCurrency       string  `json:"base_currency"`
	QuoteCurrency      string  `json:"quote_currency"`
	Enabled            bool    `json:"enabled"`
	IncrementSize      float64 `json:"increment_size,string"`
	MinimumOrderSize   float64 `json:"minimum_order_size,string"`
	PriceIncrementSize float64 `json:"price_increment_size,string"`
}

type APIOrderBook struct {
	Bids []APIOrderBookPrice `json:"bids"`
	Asks []APIOrderBookPrice `json:"asks"`
}

type APIOrderBookPrice struct {
	Price float64 `json:"price,string"`
	Size  float64 `json:"size,string"`
}

type APIOrder struct {
	CancelReason        string  `json:"cancel_reason"`
	CustomID            string  `json:"custom_id"`
	Fee                 float64 `json:"fee,string"`
	FeeCurrency         string  `json:"fee_currency"`
	Filled              float64 `json:"filled,string"`
	FilledWeightedPrice float64 `json:"filled_weighted_price,string"`
	ID                  string  `json:"id"`
	Market              string  `json:"market"`
	Price               float64 `json:"price,string"`
	Side                string  `json:"side"`
	Size                float64 `json:"size,string"`
	Status              string  `json:"status"`
	TimeInForce         string  `json:"time_in_force"`
	TradingAccount      string  `json:"trading_account"`
	Type                string  `json:"type"`
}

type APIBalance struct {
	Amount       float64 `json:"amount,string"`
	AmountOrders float64 `json:"amount_orders,string"`
	Asset        string  `json:"asset"`
}

type APIFill struct {
	ID            string  `json:"id"`
	OrderID       string  `json:"order_id"`
	OrderCustomID string  `json:"order_custom_id"`
	Time          int64   `json:"time"`
	Side          string  `json:"side"`
	Price         float64 `json:"price,string"`
	Size          float64 `json:"size,string"`
	Fee           float64 `json:"fee,string"`
	FeeCurrency   string  `json:"fee_currency"`
}

type APITrade struct {
	ID        string  `json:"id"`
	Price     float64 `json:"price,string"`
	Side      string  `json:"side"`
	Size      float64 `json:"size,string"`
	TimeMicro int64   `json:"time"`
}

type APIResponse struct {
	Result json.RawMessage `json:"result,omitempty"`
	Errors []APIError      `json:"errors"`
	Msg    string          `json:"msg"`
}

type APIError struct {
	Field string `json:"field"`
	Msg   string `json:"msg"`
}

type API struct {
	options gateway.Options
	client  *utils.HttpClient
	token   string
	baseURL string
}

func NewAPI(options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	baseURL := options.APIBaseURL
	if baseURL == "" {
		baseURL = apiBaseV1
	}

	return &API{
		baseURL: trimSlash(baseURL),
		options: options,
		client:  client,
	}
}

func (a *API) newHttpRequest(method string, path string, params map[string]interface{}, signed bool) (*http.Request, error) {
	var reqBody io.Reader

	if method != http.MethodGet && params != nil {
		reqData, err := json.Marshal(params)
		if err != nil {
			return nil, fmt.Errorf("params json marshal err: %s", err)
		}
		reqBody = bytes.NewReader(reqData)
	}

	uri := a.baseURL + path
	req, err := http.NewRequest(method, uri, reqBody)
	if err != nil {
		return nil, err
	}

	if method == http.MethodGet && params != nil {
		queryParams := url.Values{}
		for k, v := range params {
			value := reflect.ValueOf(v)

			if value.Kind() == reflect.Slice || value.Kind() == reflect.Array {
				for i := 0; i < value.Len(); i++ {
					queryParams.Add(k, fmt.Sprintf("%v", value.Index(i).Interface()))
				}
			} else {
				queryParams.Add(k, fmt.Sprintf("%v", v))
			}
		}

		req.URL.RawQuery = queryParams.Encode()
	}

	if signed {
		var reqBodyString string
		if reqBody != nil {
			reqBodyBytes, err := io.ReadAll(reqBody)
			if err != nil {
				return nil, err
			}
			reqBodyString = string(reqBodyBytes)
			req.Body = io.NopCloser(bytes.NewBuffer(reqBodyBytes))
		}

		if params != nil && method != "POST" {
			queryParams := url.Values{}
			for k, v := range params {
				queryParams.Add(k, fmt.Sprintf("%v", v))
			}

			uri = fmt.Sprintf("%s?%s", uri, queryParams.Encode())
		}

		parsedURL, err := url.Parse(uri)
		if err != nil {
			return nil, err
		}

		var fullReqURL string
		if parsedURL.RawQuery != "" {
			fullReqURL = fmt.Sprintf("%s?%s", parsedURL.Path, parsedURL.RawQuery)
		} else {
			fullReqURL = parsedURL.Path
		}

		signature, timestamp, err := generateSignature(a.options.ApiSecret, method, fullReqURL, reqBodyString)
		if err != nil {
			return nil, err
		}

		req.Header.Set("digitra-api-key", a.options.ApiKey)
		req.Header.Set("digitra-timestamp", timestamp)
		req.Header.Set("digitra-signature", signature)
	}

	if reqBody != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return req, nil
}

func generateSignature(apiSecret, method, path, body string) (string, string, error) {
	timestamp := fmt.Sprintf("%d", time.Now().UnixNano()/int64(time.Microsecond))
	signaturePayload := fmt.Sprintf("%s%s%s%s", timestamp, method, path, body)
	h := hmac.New(sha256.New, []byte(apiSecret))
	_, err := h.Write([]byte(signaturePayload))
	if err != nil {
		return "", "", err
	}

	signature := hex.EncodeToString(h.Sum(nil))
	return signature, timestamp, nil
}

func MakeHttpRequest[T any](a *API, req *http.Request, responseObject interface{}) error {
	res, err := a.client.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}

	if res.StatusCode < 200 || res.StatusCode >= 300 {
		return fmt.Errorf("err [%s] http status code [%d] body: %s", req.URL.String(), res.StatusCode, body)
	}

	if len(body) > 0 {
		var result APIResponse
		if err = json.Unmarshal(body, &result); err != nil {
			return fmt.Errorf("failed to unmarshal response into type %T, body: %s, err: %s", result, body, err)
		}

		if responseObject != nil {
			if err = json.Unmarshal(result.Result, &responseObject); err != nil {
				return fmt.Errorf("failed to unmarshal response into type %T, body: %s, err: %s", result, body, err)
			}
		}
	}

	return nil
}

func trimSlash(s string) string {
	return strings.TrimRight(s, "/")
}
