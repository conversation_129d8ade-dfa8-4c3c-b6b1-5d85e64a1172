package bybit

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseProd       = "https://api.bybit.com"
	apiSymbols        = "/v5/market/instruments-info?category=spot"
	apiAccountBalance = "/v5/account/wallet-balance?accountType=UNIFIED"
	apiOpenOrders     = "/v5/order/realtime?category=spot"
	apiPlaceOrder     = "/v5/order/create"
	apiCancelOrder    = "/v5/order/cancel"
	apiDepthBook      = "/v5/market/orderbook?category=spot&symbol=%s&limit=%d"
)

type API struct {
	options       gateway.Options
	httpClient    *utils.HttpClient
	baseURL       string
	lastNonceUsed int64
	lock          sync.Mutex
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

func (api *API) getNonce() int64 {
	api.lock.Lock()
	defer api.lock.Unlock()

	// Their nonce must be in milliseconds
	// They actually expect a millisecond nonce
	// not an incremental nonce
	nonce := time.Now().UnixMilli()
	if nonce <= api.lastNonceUsed {
		nonce = api.lastNonceUsed + 1
	}

	api.lastNonceUsed = nonce
	return api.lastNonceUsed
}

type APIResponse struct {
	Code   int             `json:"retCode"`
	Msg    string          `json:"retMsg"`
	Result json.RawMessage `json:"result"`
}

type ApiAssetDetails struct {
	List []AssetDetails `json:"list"`
}
type AssetDetails struct {
	Symbol         string `json:"symbol"`
	ContractType   string `json:"contractType"`
	Status         string `json:"status"`
	BaseCoin       string `json:"baseCoin"`
	QuoteCoin      string `json:"quoteCoin"`
	LaunchTime     string `json:"launchTime"`
	PriceScale     string `json:"priceScale"`
	LeverageFilter struct {
		MinLeverage  string `json:"minLeverage"`
		MaxLeverage  string `json:"maxLeverage"`
		LeverageStep string `json:"leverageStep"`
	} `json:"leverageFilter"`
	PriceFilter struct {
		MinPrice float64 `json:"minPrice,string"`
		MaxPrice string  `json:"maxPrice"`
		TickSize float64 `json:"tickSize,string"`
	} `json:"priceFilter"`
	LotSizeFilter struct {
		BasePrecision       float64 `json:"basePrecision,string"`
		QuotePrecision      string  `json:"quotePrecision"`
		MaxOrderQty         string  `json:"maxOrderQty"`
		MinOrderQty         float64 `json:"minOrderQty,string"` // Quantity in stock
		MinOrderAmt         float64 `json:"minOrderAmt,string"` // Notional in money
		QtyStep             float64 `json:"qtyStep,string"`
		PostOnlyMaxOrderQty string  `json:"postOnlyMaxOrderQty"`
	} `json:"lotSizeFilter"`
	UnifiedMarginTrade bool   `json:"unifiedMarginTrade"`
	FundingInterval    int    `json:"fundingInterval"`
	SettleCoin         string `json:"settleCoin"`
}

func (a *API) Symbols() (symbols []AssetDetails, err error) {
	signed := a.options.ApiKey != ""
	req, err := a.newHttpRequest(http.MethodGet, apiSymbols, nil, signed)
	if err != nil {
		return symbols, err
	}
	var resp ApiAssetDetails
	err = a.makeHttpRequest(req, &resp)
	if err != nil {
		return nil, err
	}

	return resp.List, nil
}

type ApiBalances struct {
	List []struct {
		AccountType string  `json:"accountType"`
		Coin        []Coins `json:"coin"`
	} `json:"list"`
}

type Coins struct {
	Coin          string  `json:"coin"`
	Equity        string  `json:"equity"`
	UsdValue      string  `json:"usdValue"`
	WalletBalance float64 `json:"walletBalance,string"`
	Locked        float64 `json:"locked,string"`
}

func (a *API) AccountBalance() (resp []Coins, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiAccountBalance, bytes.NewReader(nil), true)
	if err != nil {
		return nil, err
	}
	var apiBalance ApiBalances
	if err = a.makeHttpRequest(req, &apiBalance); err != nil {
		return nil, err
	}
	for _, v := range apiBalance.List {
		for _, c := range v.Coin {
			resp = append(resp, c)
		}
	}
	return resp, nil
}

type ApiOrder struct {
	List []Order `json:"list"`
}
type Order struct {
	OrderLinkId        string  `json:"orderLinkId"`
	OrderId            string  `json:"orderId"`
	BlockTradeId       string  `json:"blockTradeId"`
	Symbol             string  `json:"symbol"`
	Price              float64 `json:"price,string"`
	IsLeverage         string  `json:"isLeverage"`
	PositionIdx        int     `json:"positionIdx"`
	Qty                float64 `json:"qty,string"`
	Side               string  `json:"side"`
	OrderStatus        string  `json:"orderStatus"`
	CancelType         string  `json:"cancelType"`
	RejectReason       string  `json:"rejectReason"`
	AvgPrice           string  `json:"avgPrice"`
	LeavesQty          string  `json:"leavesQty"`
	LeavesValue        string  `json:"leavesValue"`
	CumExecQty         string  `json:"cumExecQty"`
	CumExecValue       string  `json:"cumExecValue"`
	CumExecFee         string  `json:"cumExecFee"`
	TimeInForce        string  `json:"timeInForce"`
	OrderType          string  `json:"orderType"`
	StopOrderType      string  `json:"stopOrderType"`
	OrderIv            string  `json:"orderIv"`
	TriggerPrice       string  `json:"triggerPrice"`
	TakeProfit         string  `json:"takeProfit"`
	StopLoss           string  `json:"stopLoss"`
	TpTriggerBy        string  `json:"tpTriggerBy"`
	SlTriggerBy        string  `json:"slTriggerBy"`
	TriggerDirection   int     `json:"triggerDirection"`
	TriggerBy          string  `json:"triggerBy"`
	LastPriceOnCreated string  `json:"lastPriceOnCreated"`
	ReduceOnly         bool    `json:"reduceOnly"`
	CloseOnTrigger     bool    `json:"closeOnTrigger"`
	CreatedTime        string  `json:"createdTime"`
	UpdatedTime        string  `json:"updatedTime"`
	SmpType            string  `json:"smpType"`
	SmpGroup           int     `json:"smpGroup"`
	SmpOrderId         string  `json:"smpOrderId"`
}

func (a *API) OpenOrders(currency string) (resp []Order, err error) {
	urlOpenOrder := fmt.Sprintf("%s&symbol=%s", apiOpenOrders, currency)
	req, err := a.newHttpRequest(http.MethodGet, urlOpenOrder, bytes.NewReader(nil), true)
	if err != nil {
		return nil, err
	}
	var apiOpenOrder ApiOrder
	if err = a.makeHttpRequest(req, &apiOpenOrder); err != nil {
		return nil, err
	}
	return apiOpenOrder.List, nil
}

type ApiPlaceOrder struct {
	OrderId     string `json:"orderId"`
	OrderLinkId string `json:"orderLinkId"`
}

func (a *API) PlaceOrder(order map[string]string) (orderID string, err error) {
	dataByte, err := json.Marshal(order)
	if err != nil {
		return orderID, err
	}
	req, err := a.newHttpRequest(http.MethodPost, apiPlaceOrder, bytes.NewReader(dataByte), true)
	if err != nil {
		return orderID, err
	}
	var apiOrder ApiPlaceOrder
	if err = a.makeHttpRequest(req, &apiOrder); err != nil {
		return orderID, err
	}
	orderID = apiOrder.OrderId
	return orderID, nil
}

func (a *API) CancelOrder(orderID string, symbol string) (err error) {
	data := map[string]string{
		"orderId":  orderID,
		"symbol":   symbol,
		"category": "spot",
	}
	dataByte, err := json.Marshal(data)
	if err != nil {
		return err
	}
	req, err := a.newHttpRequest(http.MethodPost, apiCancelOrder, bytes.NewReader(dataByte), true)
	if err != nil {
		return err
	}
	var apiOrder ApiPlaceOrder
	if err = a.makeHttpRequest(req, &apiOrder); err != nil {
		return err
	}
	return nil
}

type APIDepthBook struct {
	Asks     [][]string `json:"a"`
	Bids     [][]string `json:"b"`
	Sequence int64      `json:"seq"`
}

func (a *API) DepthBook(symbol string, params gateway.GetDepthParams) (APIDepthBook, error) {
	urlDepthBook := fmt.Sprintf(apiDepthBook, symbol, 200)
	req, err := a.newHttpRequest(http.MethodGet, urlDepthBook, nil, false)
	if err != nil {
		return APIDepthBook{}, err
	}
	response := APIDepthBook{}
	if err = a.makeHttpRequest(req, &response); err != nil {
		return APIDepthBook{}, err
	}
	return response, nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	var apiRes APIResponse
	err = json.Unmarshal(body, &apiRes)
	if err != nil {
		return fmt.Errorf("unmarshal err [%s] body:\n%s", err, string(body))
	}

	if apiRes.Msg != "OK" {
		return fmt.Errorf("api error: %s, body: %s", apiRes.Msg, string(body))
	}

	if responseObject != nil {
		err = json.Unmarshal(apiRes.Result, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return nil
}

func (a *API) getSignature(recvWindow, timestamp int64, method string, endpoint string, params url.Values, bodyString string) string {
	queryString := params.Encode()
	msgSignStr := strconv.FormatInt(timestamp, 10) + a.options.ApiKey + strconv.FormatInt(recvWindow, 10) + bodyString + queryString

	mac := hmac.New(sha256.New, []byte(a.options.ApiSecret))
	mac.Write([]byte(msgSignStr))
	signBytes := mac.Sum(nil)
	sign := hex.EncodeToString(signBytes)

	return sign
}

func (a *API) getWebsocketSignature(timestamp string) string {
	msgSignStr := "GET/realtime" + timestamp

	mac := hmac.New(sha256.New, []byte(a.options.ApiSecret))
	mac.Write([]byte(msgSignStr))
	signBytes := mac.Sum(nil)
	sign := hex.EncodeToString(signBytes)

	return sign
}

func (a *API) newHttpRequest(method string, path string, data io.Reader, signed bool) (*http.Request, error) {
	urlRequest := a.baseURL + path
	req, err := http.NewRequest(method, urlRequest, data)
	if err != nil {
		return nil, err
	}

	if signed {
		timestamp := a.getNonce()
		recWindow := int64(60000)
		bodyData := ""
		if req.Body != nil {
			body, err := ioutil.ReadAll(req.Body)
			if err != nil {
				return nil, err
			}
			bodyData = string(body)

			// Put back the body into the request
			req.Body = ioutil.NopCloser(bytes.NewReader(body))
		}

		signature := a.getSignature(recWindow, timestamp, method, path, req.URL.Query(), bodyData)
		req.Header.Set("X-BAPI-API-KEY", a.options.ApiKey)
		req.Header.Set("X-BAPI-SIGN", signature)
		req.Header.Set("X-BAPI-TIMESTAMP", strconv.FormatInt(timestamp, 10))
		req.Header.Set("X-BAPI-RECV-WINDOW", strconv.FormatInt(recWindow, 10))
		req.Header.Set("Content-Type", "application/json; charset=utf-8")
	}

	return req, nil
}
