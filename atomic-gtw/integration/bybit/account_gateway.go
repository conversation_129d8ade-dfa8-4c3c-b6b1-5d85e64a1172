package bybit

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api       *API
	options   gateway.Options
	tickCh    chan gateway.Tick
	baseUrlWs string
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick, baseUrlWs string) *AccountGateway {
	return &AccountGateway{
		api:       api,
		options:   options,
		tickCh:    tickCh,
		baseUrlWs: baseUrlWs,
	}
}

func (g *AccountGateway) Connect() error {
	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)

	err := ws.Connect(g.baseUrlWs)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}
	expire := fmt.Sprintf("%d", time.Now().UnixNano()/int64(time.Millisecond)+1000)

	signature := g.api.getWebsocketSignature(expire)

	authRequest := WsAuthRequest{
		Op: "auth",
		Args: []string{
			g.options.ApiKey,
			expire,
			signature,
		},
	}

	dataAuthRequest, err := json.Marshal(authRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}
	if err := ws.WriteMessage(dataAuthRequest); err != nil {
		return fmt.Errorf("failed write account msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	authErr := make(chan error)
	go func() {
		msg := <-ch

		var err error
		var res WsAuthResponse
		if unmarshalErr := json.Unmarshal(msg, &res); err != nil {
			err = fmt.Errorf("failed to unmarshal account response, err: %s", unmarshalErr)
		}

		if !res.Success {
			err = fmt.Errorf("failed to subscribe to account, err: %s", res.RetMsg)
		}

		authErr <- err
	}()

	select {
	case err := <-authErr:
		if err != nil {
			closeErr := ws.Close()
			if closeErr != nil {
				log.Printf("Failed to close ws connection after auth err: %s, authErr: %s", closeErr, err)
			}

			return fmt.Errorf("auth err: %s", err)
		}
	case <-time.After(5 * time.Second):
		err := ws.Close()
		if err != nil {
			log.Printf("Failed to close ws connection after timeout: %s", err)
		}

		return fmt.Errorf("Timed out waiting for auth response")
	}
	orderRequest := WsAuthRequest{
		Op: "subscribe",
		Args: []string{
			"execution",
		},
	}
	dataOrderRequest, err := json.Marshal(orderRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}
	if err := ws.WriteMessage(dataOrderRequest); err != nil {
		return fmt.Errorf("failed write sub msg to ws: %s", err)
	}

	go g.messageHandler(ch)
	go startWsHeartbeat(ws, "AccountGateway")

	return nil
}

func startWsHeartbeat(ws *utils.WsClient, origin string) {
	var ch chan []byte
	every := 15 * time.Second

	go func() {
		for {
			time.Sleep(every)

			// Send ping request
			if err := ws.WriteMessage([]byte("{\"req_id\": \"ping\", \"op\": \"ping\"}")); err != nil {
				panic(fmt.Errorf("%s [%s] websocket ping/pong Failed to write ping message err [%s]", Exchange, origin, err))
			}

			// Read pong response
			pongCh := make(chan struct{})
			ch = make(chan []byte, 100)
			ws.SubscribeMessages(ch)
			go func() {
				for data := range ch {
					var msg WsAuthResponse
					err := json.Unmarshal(data, &msg)
					if err != nil {
						log.Printf("%s websocket ping/pong Failed to unmarhsal [%s] err [%s]", Exchange, string(data), err)
						continue
					}

					if msg.ReqId == "ping" {
						pongCh <- struct{}{}
						break
					}
				}
			}()

			select {
			case <-time.After(5 * time.Second):
				close(ch)
				panic(fmt.Errorf("%s [%s] failed to receive pong (timeout)", Exchange, origin))
			case <-pongCh:
			}

			ws.RemoveSubscriber(ch)
		}
	}()
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	accountBalance, err := g.api.AccountBalance()
	if err != nil {
		return balances, err
	}

	balances = make([]gateway.Balance, 0, len(accountBalance))
	for _, balance := range accountBalance {
		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(balance.Coin),
			Available: balance.WalletBalance - balance.Locked,
			Total:     balance.WalletBalance,
		})
	}

	return
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrders, err := g.api.OpenOrders(market.Symbol)
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders))
	for _, order := range openOrders {
		orders = append(orders, mapAPIOrderToCommon(order, order.OrderId, market))
	}
	return
}

func mapAPIOrderToCommon(o Order, id string, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market: market,
		ID:     id,
		Side:   mapAPIOrderSideToCommonSide(o.Side),
		State:  mapAPIOrderStateToCommon(o.OrderStatus),
		Amount: o.Qty,
		Price:  o.Price,
	}
}

func mapAPIOrderSideToCommonSide(side string) gateway.Side {
	if side == "Buy" {
		return gateway.Bid
	} else if side == "Sell" {
		return gateway.Ask
	} else {
		log.Printf("Bybit invalid order side \"%s\"", side)
		return ""
	}
}

var insuficientBalanceMatch = regexp.MustCompile(`170131`) // Insufficient balance error code
var lowerLimitValueMatch = regexp.MustCompile(`170140`)    // Insufficient order value
var lowerLimitAmountMatch = regexp.MustCompile(`170136`)   // Insufficient order amount

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	data := map[string]string{
		"category":  "spot",
		"symbol":    order.Market.Symbol,
		"orderType": "Limit",
		"qty":       utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		"price":     utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
	}

	if order.Side == gateway.Bid {
		data["side"] = "Buy"
	} else if order.Side == gateway.Ask {
		data["side"] = "Sell"
	}

	if order.PostOnly {
		data["timeInForce"] = "PostOnly"
	}

	orderID, err := g.api.PlaceOrder(data)
	if err != nil {
		switch {
		case insuficientBalanceMatch.MatchString(err.Error()):
			return orderID, gateway.InsufficientBalanceErr
		case lowerLimitValueMatch.MatchString(err.Error()):
			return orderID, gateway.MinOrderSizeErr
		case lowerLimitAmountMatch.MatchString(err.Error()):
			return orderID, gateway.MinOrderSizeErr
		default:
			return orderID, err
		}
	}
	return orderID, nil

}

var orderDoesNotExistsMatch = regexp.MustCompile(`170213`) // Order does not exists error code

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	err := g.api.CancelOrder(order.ID, order.Market.Symbol)
	if err != nil {
		switch {
		case orderDoesNotExistsMatch.MatchString(err.Error()):
			log.Printf("%s order %s:%s does not exists, probably already cancelled", Exchange, order.Market.Symbol, order.ID)
			return nil
		default:
		}
	}
	return nil
}

type WsAuthRequest struct {
	Op   string   `json:"op"`
	Args []string `json:"args"`
}

type WsAuthResponse struct {
	Success bool   `json:"success"`
	RetMsg  string `json:"ret_msg"`
	Op      string `json:"op"`
	ConnId  string `json:"conn_id"`
	ReqId   string `json:"req_id"`
}

var authTradeRegex = regexp.MustCompile(`execution.*`)

func (g *AccountGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		var msg WsResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		switch {
		case authTradeRegex.MatchString(msg.Topic):
			err := g.processTrade(msg.Data)
			if err != nil {
				log.Printf("%s error processing trade update \"%s\": %s", g.baseUrlWs, data, err)
			}
		}
	}
}

type WsAuthTrade struct {
	Category        string  `json:"category"`
	Symbol          string  `json:"symbol"`
	ExecFee         float64 `json:"execFee,string"`
	ExecId          string  `json:"execId"`
	ExecPrice       float64 `json:"execPrice,string"`
	ExecQty         float64 `json:"execQty,string"`
	ExecType        string  `json:"execType"`
	ExecValue       string  `json:"execValue"`
	IsMaker         bool    `json:"isMaker"`
	FeeRate         string  `json:"feeRate"`
	TradeIv         string  `json:"tradeIv"`
	MarkIv          string  `json:"markIv"`
	BlockTradeId    string  `json:"blockTradeId"`
	MarkPrice       string  `json:"markPrice"`
	IndexPrice      string  `json:"indexPrice"`
	UnderlyingPrice string  `json:"underlyingPrice"`
	LeavesQty       string  `json:"leavesQty"`
	OrderId         string  `json:"orderId"`
	OrderLinkId     string  `json:"orderLinkId"`
	OrderPrice      string  `json:"orderPrice"`
	OrderQty        string  `json:"orderQty"`
	OrderType       string  `json:"orderType"`
	StopOrderType   string  `json:"stopOrderType"`
	Side            string  `json:"side"`
	ExecTime        string  `json:"execTime"`
	IsLeverage      string  `json:"isLeverage"`
}

func (g *AccountGateway) processTrade(data []byte) error {
	if data == nil {
		return nil
	}
	var matches []WsAuthTrade
	err := json.Unmarshal(data, &matches)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}

	eventLog := make([]gateway.Event, 0, len(matches))

	for _, match := range matches {
		event := gateway.Event{
			Type: gateway.FillEvent,
			Data: gateway.Fill{
				Symbol:  match.Symbol,
				ID:      match.ExecId,
				OrderID: match.OrderId,
				Side:    mapAPIOrderSideToCommonSide(match.Side),
				Price:   match.ExecPrice,
				Amount:  match.ExecQty,
				Fee:     match.ExecFee,
			},
		}

		eventLog = append(eventLog, event)

	}
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}
