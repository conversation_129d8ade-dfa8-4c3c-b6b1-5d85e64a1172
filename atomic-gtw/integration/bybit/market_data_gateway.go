package bybit

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	baseURL string
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewMarketDataGateway(baseURL string, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		baseURL: baseURL,
		options: options,
		tickCh:  tickCh,
	}
}

type WsRequest struct {
	Op   string   `json:"op"`
	Args []string `json:"args"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)

	err := ws.Connect(g.baseURL)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}
	for _, market := range markets {
		requestBook := fmt.Sprintf("orderbook.50.%s", market.Symbol)
		requestTrade := fmt.Sprintf("publicTrade.%s", market.Symbol)
		bookRequest := WsRequest{
			Op: "subscribe",
			Args: []string{
				requestBook, requestTrade},
		}

		data, err := json.Marshal(bookRequest)
		if err != nil {
			return fmt.Errorf("failed to marshal sub request, err: %s", err)
		}

		if err := ws.WriteMessage(data); err != nil {
			return fmt.Errorf("failed write sub msg to ws: %s", err)
		}
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ch)
	go startWsHeartbeat(ws, "MarketDataGateway")

	return nil
}

type WsResponse struct {
	Topic string          `json:"topic"`
	Ts    int64           `json:"ts"`
	Type  string          `json:"type"`
	Data  json.RawMessage `json:"data"`
}

type WsBook struct {
	Asks   []gateway.PriceArray `json:"a"`
	Bids   []gateway.PriceArray `json:"b"`
	Symbol string               `json:"s"`
	Ts     string               `json:"ts"`
}

type WsTrade struct {
	ID          string  `json:"i"`
	TimestampMs int64   `json:"T"`
	Price       float64 `json:"p,string"`
	Size        float64 `json:"v,string"`
	TakerSide   string  `json:"S"`
	Symbol      string  `json:"s"`
	BlockTrade  bool    `json:"BT"`
}

var userBookRegex = regexp.MustCompile(`orderbook.*`)
var userTradeRegex = regexp.MustCompile(`publicTrade.*`)

func (g *MarketDataGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		var msg WsResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		switch {
		case userBookRegex.MatchString(msg.Topic):
			if msg.Type == "delta" {
				err := g.processBookMsg(msg.Data)
				if err != nil {
					log.Printf("%s error processing book update \"%s\": %s", g.baseURL, data, err)
				}
			} else {
				err := g.processSnapshotMsg(msg.Data)
				if err != nil {
					log.Printf("%s error processing book snapshot \"%s\": %s", g.baseURL, data, err)
				}
			}
		case userTradeRegex.MatchString(msg.Topic):
			err := g.processTradeUpdates(msg.Data)
			if err != nil {
				log.Printf("%s error processing trade update \"%s\": %s", g.baseURL, data, err)
			}
		}
	}
}

type WsSnapShot struct {
	Asks   []gateway.PriceArray `json:"a"`
	Bids   []gateway.PriceArray `json:"b"`
	Ts     string               `json:"ts"`
	Symbol string               `json:"s"`
}

func (g *MarketDataGateway) processSnapshotMsg(data []byte) error {
	var wsResponse WsSnapShot
	err := json.Unmarshal(data, &wsResponse)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}
	bids := wsResponse.Bids
	asks := wsResponse.Asks

	g.processDepthUpdate(wsResponse.Symbol, bids, asks, true)

	return nil
}

func (g *MarketDataGateway) processBookMsg(data []byte) error {
	var wsResponse WsBook
	err := json.Unmarshal(data, &wsResponse)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}
	bids := wsResponse.Bids
	asks := wsResponse.Asks

	g.processDepthUpdate(wsResponse.Symbol, bids, asks, false)

	return nil
}

func (g *MarketDataGateway) processDepthUpdate(symbol string, bids []gateway.PriceArray, asks []gateway.PriceArray, snapshot bool) {
	eventLog := make([]gateway.Event, 0, len(bids)+len(asks)+1)
	appendEvents := func(symbol string, side gateway.Side, prices []gateway.PriceArray) {
		for _, order := range prices {
			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  order.Price,
					Amount: order.Amount,
				},
			}

			eventLog = append(eventLog, event)
		}
	}

	if snapshot {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: symbol,
			},
		})
	}

	appendEvents(symbol, gateway.Ask, asks)
	appendEvents(symbol, gateway.Bid, bids)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

func (g *MarketDataGateway) processTradeUpdates(data []byte) error {
	if data == nil {
		return nil
	}
	var matches []WsTrade
	err := json.Unmarshal(data, &matches)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}

	eventLog := make([]gateway.Event, 0, 1)

	for _, match := range matches {
		event := gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				ID:        match.ID,
				Symbol:    match.Symbol,
				Direction: mapAPIOrderSideToCommonSide(match.TakerSide),
				Timestamp: time.Unix(0, match.TimestampMs*int64(time.Millisecond)),
				Price:     match.Price,
				Amount:    match.Size,
			},
		}

		eventLog = append(eventLog, event)

	}
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}
