package cointiger

import (
	"fmt"
	"math"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "CoinTiger",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.options, g.api, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.options, g.Markets(), g.api, g.tickCh)
	if err := g.marketDataGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.Currencies()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0)
	for _, pairs := range res {
		for _, pair := range pairs {
			commonMarkets = append(commonMarkets, gateway.Market{
				Exchange: Exchange,
				Symbol:   fmt.Sprintf("%s%s", pair.BaseCurrency, pair.QuoteCurrency),
				Pair: gateway.Pair{
					Base:  strings.ToUpper(pair.BaseCurrency),
					Quote: strings.ToUpper(pair.QuoteCurrency),
				},
				PriceTick:              1 / math.Pow10(int(pair.PricePrecision)),
				AmountTick:             1 / math.Pow10(int(pair.AmountPrecision)),
				MinimumOrderSize:       pair.AmountMin,
				MinimumOrderMoneyValue: pair.MinTurnover,
				MarketType:             gateway.SpotMarket,
			})
		}
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
