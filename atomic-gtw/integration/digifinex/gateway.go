package digifinex

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"math"
	"net/http"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

var Exchange = gateway.Exchange{
	Name: "DigiFinex",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	api               APIClient
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPIClient(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	if g.options.ApiKey != "" {
		err := g.accountGateway.Connect()
		if err != nil {
			return fmt.Errorf("account gtw connect: %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.api, g.options, g.Markets(), g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return fmt.Errorf("failed to subscribe market data, err: %s", err)
	}

	if g.options.ApiKey != "" {
		log.Printf("Requesting account orders updates...")

		err := g.accountGateway.subscribeMarketsUpdate(markets)
		if err != nil {
			return fmt.Errorf("failed to subscribe account order updates, err: %s", err)
		}
	}

	return nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	markets, err := g.api.GetMarkets()
	if err != nil {
		return nil, err
	}

	commonMarkets := marketsToCommonMarket(markets)
	return commonMarkets, nil
}

// TODO: The regular api is not returning all pairs, we will use the
// websites api for now
type WebAPIPairRes struct {
	Errcode int `json:"errcode"`
	Data    struct {
		List []WebAPIPair `json:"list"`
	} `json:"data"`
}

type WebAPIPair struct {
	TradePair       string  `json:"trade_pair"`
	PricePrecision  int     `json:"price_precision"`
	AmountPrecision int     `json:"amount_precision"`
	AmountMinimum   float64 `json:"amount_minimum,string"` // Min order amount
	MinVolume       float64 `json:"min_volume,string"`     // Min order money value
}

func (g *Gateway) loadMarketsFromWebAPI() ([]gateway.Market, error) {
	client := utils.NewHttpClient()
	client.UseProxies(g.options.Proxies)

	req, err := http.NewRequest(http.MethodGet, "https://api.digifinex.com/order/limit", nil)
	if err != nil {
		err = fmt.Errorf("http new req err: %s", err)
		return nil, err
	}

	res, err := client.SendRequest(req)
	if err != nil {
		err = fmt.Errorf("http send req err: %s", err)
		return nil, err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		err = fmt.Errorf("http read body err: %s", err)
		return nil, err
	}

	var apiRes WebAPIPairRes
	err = json.Unmarshal(body, &apiRes)
	if err != nil {
		err = fmt.Errorf("http body unmarshal err: %s", err)
		return nil, err
	}

	if apiRes.Errcode != 0 {
		err = fmt.Errorf("load markets api err code res: %d, body: %s", apiRes.Errcode, string(body))
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0)
	for _, market := range apiRes.Data.List {

		symbol := market.TradePair
		parts := strings.Split(symbol, "_")
		if len(parts) < 2 {
			err = fmt.Errorf("DigiFinex failed to translate symbol [%s] into base/quote", symbol)
			log.Println(err)
			continue
		}
		base := strings.ToUpper(parts[0])
		quote := strings.ToUpper(parts[1])
		priceTick := 1 / math.Pow10(int(market.PricePrecision))
		amountTick := 1 / math.Pow10(int(market.AmountPrecision))

		commonMarkets = append(commonMarkets, gateway.Market{
			Exchange: Exchange,
			Pair: gateway.Pair{
				Base:  base,
				Quote: quote,
			},
			Symbol:                 strings.ToLower(symbol),
			PriceTick:              priceTick,
			AmountTick:             amountTick,
			MinimumOrderSize:       market.AmountMinimum,
			MinimumOrderMoneyValue: market.MinVolume,
		})
	}

	return commonMarkets, nil
}

func marketsToCommonMarket(markets []APIMarket) []gateway.Market {
	commonMarkets := make([]gateway.Market, 0, len(markets))

	for _, market := range markets {
		mkt, err := marketToCommonMarket(market)
		if err == nil {
			commonMarkets = append(commonMarkets, mkt)
		}
	}

	return commonMarkets
}

func marketToCommonMarket(market APIMarket) (mkt gateway.Market, err error) {
	symbol := market.Market
	parts := strings.Split(symbol, "_")
	if len(parts) < 2 {
		err = fmt.Errorf("DigiFinex failed to translate symbol [%s] into base/quote", symbol)
		log.Println(err)
		return mkt, err
	}
	base := strings.ToUpper(parts[0])
	quote := strings.ToUpper(parts[1])
	priceTick := 1 / math.Pow10(int(market.PricePrecision))
	amountTick := 1 / math.Pow10(int(market.VolumePrecision))

	return gateway.Market{
		Exchange: Exchange,
		Pair: gateway.Pair{
			Base:  base,
			Quote: quote,
		},
		Symbol:                 symbol,
		TakerFee:               0.0025,
		MakerFee:               0.00,
		PriceTick:              priceTick,
		AmountTick:             amountTick,
		MinimumOrderSize:       market.MinAmount,
		MinimumOrderMoneyValue: market.MinVolume,
		MarketType:             gateway.SpotMarket,
	}, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func parsePriceLevelsToDepth(levels []gateway.PriceArray) []gateway.PriceLevel {
	var priceLevels []gateway.PriceLevel

	for _, level := range levels {
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  level.Price,
			Amount: level.Amount,
		})
	}

	return priceLevels
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	
	asks := parsePriceLevelsToDepth(depth.Asks)
	bids := parsePriceLevelsToDepth(depth.Bids)
	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
