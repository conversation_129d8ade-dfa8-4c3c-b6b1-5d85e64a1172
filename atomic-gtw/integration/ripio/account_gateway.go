package ripio

import (
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"regexp"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	options gateway.Options
	api     *API
	tickCh  chan gateway.Tick
}

func NewAccountGateway(options gateway.Options, api *API, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		options: options,
		api:     api,
		tickCh:  tickCh,
	}
}

func (g *AccountGateway) Connect() error {
	// Get account ticket for WS authenticated connection
	ticketRes, err := g.api.Ticket()
	if err != nil {
		return fmt.Errorf("failed to get ticket, err: %s", err)
	}

	ticket := ticketRes.Ticket
	log.Printf("%s account gateway using ticket [%s] for ws private topics", Exchange.Name, ticket)

	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)
	if err := ws.Connect(wsURL); err != nil {
		return err
	}

	ch := make(chan []byte, 1)
	ws.SubscribeMessages(ch)

	req := WsReq{
		Method: "subscribe",
		Topics: []string{
			"user_trades",
		},
		Ticket: ticket,
	}
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}
	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write sub msg to ws: %s", err)
	}

	// Check if websocket responded with error
	select {
	case data := <-ch:
		// Check if error
		var msg WsMessage
		if err := json.Unmarshal(data, &msg); err != nil {
			return fmt.Errorf("account gateway failed to unmarshal ws response, err: %s", err)
		}
		if msg.Error != "" {
			return fmt.Errorf("account gateway ws responded with error: %s", msg.Error)
		}

		// If not error, put msg back into channel for the messageHandler to process
		go func() {
			ch <- data
		}()
	case <-time.After(5 * time.Second):
		return fmt.Errorf("websocket timed out waiting for user_trades subscription")
	}

	if err := startWebsocketPinger(ws); err != nil {
		return fmt.Errorf("failed to start websocket pinger, err: %s", err)
	}

	go g.messageHandler(ch)

	return nil
}

func (g *AccountGateway) messageHandler(ch chan []byte) {
	sequenceValidator := utils.NewSequenceValidator(utils.DefaultMaxMemorizedSeqs)

	for data := range ch {
		var msg WsMessage
		if err := json.Unmarshal(data, &msg); err != nil {
			log.Printf("%s account gateway failed to unmarhsal WsMessage [%s] err [%s]", Exchange, string(data), err)
			continue
		}

		if !sequenceValidator.Validate(msg.ID) {
			log.Printf("%s account gateway message with ID [%d] already processed, skipping....", Exchange, msg.ID)
			continue
		}

		switch msg.Topic {
		case "order_status":
			if err := g.processOrderUpdate(msg.Body); err != nil {
				log.Printf("%s account gateway failed to process user order [%s] err [%s]", Exchange, string(msg.Body), err)
			}
		case "user_trades":
			if err := g.processUserTrade(msg.Body); err != nil {
				log.Printf("%s account gateway failed to process user trade [%s] err [%s]", Exchange, string(msg.Body), err)
			}
		}
	}
}

type WsUserTradeMsg struct {
	Trade  WsUserTrade `json:"trade"`
	UserID string      `json:"user_id"`
}

type WsUserTrade struct {
	Amount       float64 `json:"amount"`
	Date         string  `json:"date"`
	Fee          float64 `json:"fee"`
	FeeCurrency  string  `json:"fee_currency"`
	ID           string  `json:"id"`
	PairCode     string  `json:"pair_code"`
	Price        float64 `json:"price"`
	Side         string  `json:"side"`
	TakerOrMaker string  `json:"taker_or_maker"`
	TakerOrderID string  `json:"taker_order_id"`
	MakerOrderID string  `json:"maker_order_id"`
	Timestamp    int64   `json:"timestamp"`
	TotalValue   float64 `json:"total_value"`
	Type         string  `json:"type"`
}

func (g *AccountGateway) processUserTrade(body json.RawMessage) error {
	if string(body) == "" {
		return nil
	}

	var msg WsUserTradeMsg
	if err := json.Unmarshal(body, &msg); err != nil {
		return fmt.Errorf("account gateway failed to unmarshal WsUserTradeMsg [%s] err [%s]", string(body), err)
	}

	trade := msg.Trade
	tradeSide := mapAPISideToGtw(trade.Side)

	tradeTime, err := apiTimestampToTime(trade.Timestamp)
	if err != nil {
		log.Printf("%s account gateway failed to parse trade timestamp [%d] err [%s]", Exchange, trade.Timestamp, err)
	}

	eventLog := make([]gateway.Event, 0, 2)
	// We need to track twice the fill event, because, when we match orders against ourselves,
	// which shouldn't happen, but might, the websocket sends us only on trade event, for both
	// orders matched, so we need to track the fill event for both orders. We don't know when
	// this self-trade happens, so we need to always track twice.
	eventLog = append(eventLog, gateway.Event{
		Type: gateway.FillEvent,
		Data: gateway.Fill{
			OrderID:   trade.TakerOrderID,
			Taker:     true,
			ID:        trade.ID + "-tkr",
			Timestamp: tradeTime,
			Side:      tradeSide,
			Price:     trade.Price,
			Amount:    trade.Amount,
			Fee:       trade.Fee,
			FeeAsset:  trade.FeeCurrency,
		},
	})
	eventLog = append(eventLog, gateway.Event{
		Type: gateway.FillEvent,
		Data: gateway.Fill{
			OrderID:   trade.MakerOrderID,
			Taker:     false,
			ID:        trade.ID + "-mkr",
			Timestamp: tradeTime,
			Side:      tradeSide,
			Price:     trade.Price,
			Amount:    trade.Amount,
			Fee:       trade.Fee,
			FeeAsset:  trade.FeeCurrency,
		},
	})
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

type WsOrderUpdate struct {
	Amount                float64 `json:"amount"`
	AverageExecutionPrice float64 `json:"average_execution_price"`
	CreatedAt             string  `json:"created_at"`
	ExecutedAmount        float64 `json:"executed_amount"`
	ID                    string  `json:"id"`
	Pair                  string  `json:"pair"`
	Price                 float64 `json:"price"`
	RemainingAmount       float64 `json:"remaining_amount"`
	Side                  string  `json:"side"`
	Status                string  `json:"status"`
	Type                  string  `json:"type"`
	UpdatedAt             string  `json:"updated_at"`
	UserID                string  `json:"user_id"`
}

func (g *AccountGateway) processOrderUpdate(body json.RawMessage) error {
	if string(body) == "" {
		return nil
	}

	var order WsOrderUpdate
	if err := json.Unmarshal(body, &order); err != nil {
		return fmt.Errorf("account gateway failed to unmarshal WsOrderUpdate [%s] err [%s]", string(body), err)
	}

	eventLog := make([]gateway.Event, 0, 1)
	event := gateway.Event{
		Type: gateway.OrderUpdateEvent,
		Data: gateway.Order{
			ID:           order.ID,
			Side:         mapAPISideToGtw(order.Side),
			Price:        order.Price,
			Amount:       order.Amount,
			AvgPrice:     order.AverageExecutionPrice,
			FilledAmount: order.ExecutedAmount,
			State:        mapAPIStateToGtw(order.Status),
		},
	}
	eventLog = append(eventLog, event)
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	res, err := g.api.GetBalances()
	if err != nil {
		return nil, err
	}

	balances := make([]gateway.Balance, 0, len(res))
	for _, balance := range res {
		balances = append(balances, gateway.Balance{
			Asset:     balance.CurrencyCode,
			Total:     balance.AvailableAmount + balance.LockedAmount,
			Available: balance.AvailableAmount,
		})
	}

	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	params := &url.Values{}
	params.Set("pair", market.Symbol)
	res, err := g.api.GetOpenOrders(params)
	if err != nil {
		return nil, err
	}

	orders := make([]gateway.Order, len(res))
	for index, resOrder := range res {
		orders[index] = mapAPIOrderToGtw(market, resOrder)
	}

	return orders, nil
}

var insuficientBalanceMatch = regexp.MustCompile(`40011`)

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	var side string
	if order.Side == gateway.Bid {
		side = "buy"
	} else {
		side = "sell"
	}

	res, err := g.api.CreateOrder(APICreateOrderReq{
		Pair:   order.Market.Symbol,
		Side:   side,
		Type:   "limit",
		Amount: order.Amount,
		Price:  order.Price,
	})
	if err != nil {
		switch {
		case insuficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		default:
			return "", err
		}
	}

	if res.ID == "" {
		return "", fmt.Errorf("empty order id")
	}

	if g.options.WaitForOrderEntry {
		maxWait := 30 * time.Second
		if g.options.MaxWaitForOrderEntry > 0 {
			maxWait = g.options.MaxWaitForOrderEntry
		}

		err := utils.WaitForOrderEntry(
			maxWait,
			1*time.Second,
			func() (string, error) {
				orderRes, err := g.api.GetOrder(res.ID)
				return orderRes.Status, err
			},
			checkAPIPendingOrderStatus,
		)
		if err != nil {
			return res.ID, err
		}
	}

	return res.ID, nil
}

var alreadyCancelledMatch = regexp.MustCompile(`40020`)
var orderExecutedCompletlyMatch = regexp.MustCompile(`40021`)

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	req := APICancelOrderReq{
		ID: order.ID,
	}
	_, err := g.api.CancelOrder(req)
	if err != nil {
		switch {
		case alreadyCancelledMatch.MatchString(err.Error()):
			return gateway.AlreadyCancelledErr
		case orderExecutedCompletlyMatch.MatchString(err.Error()):
			return gateway.AlreadyCancelledErr
		default:
			return err
		}
	}

	return nil
}

func mapAPIOrderToGtw(market gateway.Market, order APIOrder) gateway.Order {
	return gateway.Order{
		Market:       market,
		ID:           order.ID,
		Side:         mapAPISideToGtw(order.Side),
		State:        mapAPIStateToGtw(order.Status),
		Amount:       order.RequestedAmount,
		Price:        order.Price,
		FilledAmount: order.ExecutedAmount,
	}
}

func checkAPIPendingOrderStatus(status string) bool {
	return status == "pending_creation" || status == "waiting"
}

func mapAPIStateToGtw(status string) gateway.OrderState {
	switch status {
	case "open":
		return gateway.OrderOpen
	case "executed_partially":
		return gateway.OrderPartiallyFilled
	case "executed_completely":
		return gateway.OrderFullyFilled
	case "canceled":
		return gateway.OrderCancelled
	case "pending_creation", "waiting":
		return gateway.OrderSent
	default:
		return gateway.OrderUnknown
	}
}

func mapAPISideToGtw(side string) gateway.Side {
	switch side {
	case "buy":
		return gateway.Bid
	case "sell":
		return gateway.Ask
	default:
		return ""
	}
}
