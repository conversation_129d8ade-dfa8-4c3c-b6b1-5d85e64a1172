package ripio

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const wsURL = "wss://ws.ripiotrade.co"

type MarketDataGateway struct {
	options gateway.Options
	api     *API
	tickCh  chan gateway.Tick
}

func NewMarketDataGateway(options gateway.Options, api *API, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options: options,
		api:     api,
		tickCh:  tickCh,
	}
}

type WsReq struct {
	Method string   `json:"method"`
	Topics []string `json:"topics,omitempty"`
	Ticket string   `json:"ticket,omitempty"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)
	if err := ws.Connect(wsURL); err != nil {
		return err
	}

	topics := make([]string, 0, len(markets))
	for _, market := range markets {
		topics = append(topics, fmt.Sprintf("orderbook/level_2@%s", market.Symbol))
		topics = append(topics, fmt.Sprintf("trade@%s", market.Symbol))
	}
	req := WsReq{
		Method: "subscribe",
		Topics: topics,
	}
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}
	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write sub msg to ws: %s", err)
	}

	if err := startWebsocketPinger(ws); err != nil {
		return fmt.Errorf("failed to start websocket pinger, err: %s", err)
	}

	ch := make(chan []byte)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ch)

	return nil
}

func startWebsocketPinger(ws *utils.WsClient) error {
	pingReq := WsReq{
		Method: "ping",
	}
	pingData, err := json.Marshal(pingReq)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}

	go func() {
		for {
			time.Sleep(15 * time.Second)
			err := ws.WriteMessage(pingData)
			if err != nil {
				panic(fmt.Errorf("%s ws failed to send ping, err: %s", Exchange.Name, err))
			}
		}
	}()

	return nil
}

type WsMessage struct {
	Body  json.RawMessage `json:"body"`
	Topic string          `json:"topic"`
	Error string          `json:"error"`
	ID    int64           `json:"id"`
}

func (g *MarketDataGateway) messageHandler(ch chan []byte) {
	sequenceValidator := utils.NewSequenceValidator(utils.DefaultMaxMemorizedSeqs)

	for data := range ch {
		var msg WsMessage
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("%s failed to unmarhsal WsMessage [%s] err [%s]", Exchange, string(data), err)
			continue
		}

		if !sequenceValidator.Validate(msg.ID) {
			log.Printf("%s market data gateway message with ID [%d] already processed, skipping....", Exchange, msg.ID)
			continue
		}

		parts := strings.Split(msg.Topic, "@")
		if len(parts) < 2 {
			// Ignore not supported topics
			continue
		}

		topic := parts[0]
		symbol := parts[1]

		switch topic {
		case "orderbook/level_2":
			if err := g.processBookUpdateMsg(symbol, msg.Body); err != nil {
				log.Printf("%s error processing book update \"%s\": %s", Exchange, string(msg.Body), err)
			}
		case "trade":
			if err := g.processTradeMsg(symbol, msg.Body); err != nil {
				log.Printf("%s error processing matches \"%s\": %s", Exchange, string(msg.Body), err)
			}

		default:
			log.Printf("%s unprocessable message type [%s], data [%s]", Exchange, topic, string(data))
		}
	}
}

type WsOrderBookUpdate struct {
	Asks []gateway.PriceLevel `json:"asks"`
	Bids []gateway.PriceLevel `json:"bids"`
}

func (g *MarketDataGateway) processBookUpdateMsg(symbol string, data json.RawMessage) error {
	// Ignore empty updates
	if string(data) == "" {
		return nil
	}

	var update WsOrderBookUpdate
	if err := json.Unmarshal(data, &update); err != nil {
		return fmt.Errorf("failed to unmarshal order book update, err: %s", err)
	}

	events := make([]gateway.Event, 0, len(update.Bids)+len(update.Asks)+1)
	events = append(events, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: symbol,
		},
	})

	appendEvents := func(symbol string, side gateway.Side, prices []gateway.PriceLevel) {
		for _, price := range prices {
			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  price.Price,
					Amount: price.Amount,
				},
			}

			events = append(events, event)
		}
	}

	appendEvents(symbol, gateway.Bid, update.Bids)
	appendEvents(symbol, gateway.Ask, update.Asks)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}

	return nil
}

type WsTradeUpdate struct {
	ID           string  `json:"id"`
	Amount       float64 `json:"amount"`
	MakerOrderID string  `json:"maker_order_id"`
	MakerSide    string  `json:"maker_side"`
	MakerType    string  `json:"maker_type"`
	Pair         string  `json:"pair"`
	Price        float64 `json:"price"`
	TakerOrderID string  `json:"taker_order_id"`
	TakerSide    string  `json:"taker_side"`
	TakerType    string  `json:"taker_type"`
	Timestamp    int64   `json:"timestamp"`
	TotalValue   float64 `json:"total_value"`
}

func apiTimestampToTime(tsMilli int64) (time.Time, error) {
	return time.Unix(0, tsMilli*int64(time.Millisecond)), nil
}

func (g *MarketDataGateway) processTradeMsg(symbol string, data json.RawMessage) error {
	// Ignore empty updates
	if string(data) == "" {
		return nil
	}

	var trade WsTradeUpdate
	if err := json.Unmarshal(data, &trade); err != nil {
		return fmt.Errorf("failed to unmarshal trade update, err: %s", err)
	}

	tradeTime, err := apiTimestampToTime(trade.Timestamp)
	if err != nil {
		return fmt.Errorf("failed to parse trade timestamp, err: %s", err)
	}

	event := gateway.Event{
		Type: gateway.TradeEvent,
		Data: gateway.Trade{
			ID:        trade.ID,
			Timestamp: tradeTime,
			Symbol:    symbol,
			Direction: mapAPISideToGtw(trade.TakerSide),
			Price:     trade.Price,
			Amount:    trade.Amount,
		},
	}

	events := make([]gateway.Event, 0, 1)
	events = append(events, event)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}

	return nil
}
