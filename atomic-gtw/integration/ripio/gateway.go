package ripio

import (
	"fmt"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Ripio",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		api:     NewAPI(options),
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.options, g.api, g.tickCh)
	if g.options.Token != "" {
		err := g.accountGateway.Connect()
		if err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	g.marketDataGateway = NewMarketDataGateway(g.options, g.api, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	pairs, err := g.api.GetPairs()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0)
	for _, pair := range pairs {
		commonMarkets = append(commonMarkets, gateway.Market{
			Exchange: Exchange,
			Symbol:   pair.Symbol,
			Pair: gateway.Pair{
				Base:  pair.Base,
				Quote: pair.Quote,
			},
			MakerFee:               0.001,
			TakerFee:               0.002,
			PriceTick:              pair.PriceTick,
			AmountTick:             pair.AmountTick,
			Closed:                 !pair.Enabled,
			MinimumOrderSize:       pair.MinAmount,
			MinimumOrderMoneyValue: pair.MinValue,
		})
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	var bids []gateway.PriceLevel
	var asks []gateway.PriceLevel
	depth, err := gtw.api.DepthBookLevel3(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	for _, ask := range depth.Asks {
		asks = append(asks, gateway.PriceLevel{
			Price:  ask.Price,
			Amount: ask.Amount,
		})
	}
	for _, bid := range depth.Bids {
		bids = append(bids, gateway.PriceLevel{
			Price:  bid.Price,
			Amount: bid.Amount,
		})
	}
	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
