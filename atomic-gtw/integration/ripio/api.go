package ripio

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBase            = "https://api.ripiotrade.co"
	apiPairs           = "/v4/public/pairs"
	apiOpenOrders      = "/v4/orders/open"
	apiBalances        = "/v4/user/balances"
	apiOrders          = "/v4/orders"
	apiCancelOrder     = "/v4/orders/cancel"
	apiTicket          = "/v4/ticket"
	apiGetOrder        = "/v4/orders/%s"
	apiDepthBookLevel3 = "/v4/public/orders/level-2?pair=%s&limit=%d"
)

type API struct {
	options gateway.Options
	client  *utils.HttpClient
}

func NewAPI(options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options: options,
		client:  client,
	}
}

type APIRes struct {
	Code    int             `json:"code"`
	Error   string          `json:"error"`
	Message string          `json:"message"`
	Data    json.RawMessage `json:"data"`
}

type APIPair struct {
	Base       string  `json:"base"`
	BaseName   string  `json:"base_name"`
	Enabled    bool    `json:"enabled"`
	MinAmount  float64 `json:"min_amount"`
	MinValue   float64 `json:"min_value"`
	PriceTick  float64 `json:"price_tick"`
	AmountTick float64 `json:"amount_tick"`
	Quote      string  `json:"quote"`
	QuoteName  string  `json:"quote_name"`
	Symbol     string  `json:"symbol"`
}

func (a *API) GetPairs() ([]APIPair, error) {
	var res []APIPair
	req, err := a.newHttpRequest(http.MethodGet, apiPairs, nil, false)
	if err != nil {
		return res, err
	}
	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APIBalance struct {
	CurrencyCode    string  `json:"currency_code"`
	AvailableAmount float64 `json:"available_amount"`
	LockedAmount    float64 `json:"locked_amount"`
}

func (a *API) GetBalances() ([]APIBalance, error) {
	var res []APIBalance
	req, err := a.newHttpRequest(http.MethodGet, apiBalances, nil, true)
	if err != nil {
		return res, err
	}
	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APIOrder struct {
	ID              string  `json:"id"`
	CreateDate      string  `json:"create_date"`
	Side            string  `json:"side"`
	Status          string  `json:"status"`
	ExternalID      string  `json:"external_id"`
	Pair            string  `json:"pair"`
	Type            string  `json:"type"`
	UpdateDate      string  `json:"update_date"`
	ExecutedAmount  float64 `json:"executed_amount"`
	Price           float64 `json:"price"`
	RemainingAmount float64 `json:"remaining_amount"`
	RemainingValue  float64 `json:"remaining_value"`
	RequestedAmount float64 `json:"requested_amount"`
	TotalValue      float64 `json:"total_value"`
}

type APIOrderRes struct {
	Orders []APIOrder `json:"orders"`
}

func (a *API) GetOpenOrders(params *url.Values) ([]APIOrder, error) {
	var res APIOrderRes
	req, err := a.newHttpRequest(http.MethodGet, apiOpenOrders, nil, true)
	if err != nil {
		return nil, err
	}

	if params != nil {
		req.URL.RawQuery = params.Encode()
	}

	err = a.makeHttpRequest(req, &res)
	return res.Orders, err
}

func (a *API) GetOrder(id string) (APIOrder, error) {
	url := fmt.Sprintf(apiGetOrder, id)
	req, err := a.newHttpRequest(http.MethodGet, url, nil, true)
	if err != nil {
		return APIOrder{}, err
	}
	var res APIOrder
	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APICreateOrderReq struct {
	Pair   string  `json:"pair"`
	Side   string  `json:"side"`
	Type   string  `json:"type"`
	Amount float64 `json:"amount"`
	Price  float64 `json:"price"`
}

func (a *API) CreateOrder(orderReq APICreateOrderReq) (APIOrder, error) {
	var res APIOrder
	data, err := json.Marshal(orderReq)
	if err != nil {
		return res, err
	}

	req, err := a.newHttpRequest(http.MethodPost, apiOrders, bytes.NewBuffer(data), true)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APICancelOrderReq struct {
	ID string `json:"id"`
}

func (a *API) CancelOrder(cancelReq APICancelOrderReq) (APIOrder, error) {
	var res APIOrder
	data, err := json.Marshal(cancelReq)
	if err != nil {
		return res, err
	}

	req, err := a.newHttpRequest(http.MethodPost, apiCancelOrder, bytes.NewBuffer(data), true)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APITicket struct {
	Ticket string `json:"ticket"`
}

// Create a new ticket for websocket authentication
func (a *API) Ticket() (APITicket, error) {
	var res APITicket
	req, err := a.newHttpRequest(http.MethodPost, apiTicket, nil, true)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

type Depth struct {
	Amount float64 `json:"amount"`
	Price  float64 `json:"price"`
}

type APIDepthBook struct {
	Asks []Depth `json:"asks"`
	Bids []Depth `json:"bids"`
}

func (a *API) DepthBookLevel3(symbol string, params gateway.GetDepthParams) (APIDepthBook, error) {
	if params.Limit == 0 {
		params.Limit = 50
	}

	url := fmt.Sprintf(apiDepthBookLevel3, symbol, params.Limit)
	req, err := a.newHttpRequest(http.MethodGet, url, nil, false)
	if err != nil {
		return APIDepthBook{}, err
	}

	var res APIDepthBook
	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) newHttpRequest(method string, path string, data io.Reader, auth bool) (*http.Request, error) {
	if string(path[0]) != "/" {
		path = "/" + path
	}
	url := apiBase + path

	req, err := http.NewRequest(method, url, data)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")

	if auth == true {
		req.Header.Set("Authorization", a.options.Token)
	}

	return req, nil
}

var ErrTooManyRequests = errors.New("too many requests, throttled")

func (a *API) makeHttpRequestWithRetry(req *http.Request, responseObject interface{}, maxRetries int) error {
	retryCount := 0

L:
	for retryCount < maxRetries {
		err := a.makeHttpRequest(req, responseObject)
		if err != nil {
			switch err {
			case ErrTooManyRequests:
				log.Printf("Ripio request to %s throttled, retrying in 1 second, retry count: %d", req.URL, retryCount)
				retryCount += 1
				time.Sleep(1 * time.Second)
				continue L
			default:
				return err
			}
		}

		return nil
	}

	return ErrTooManyRequests
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	body, err := a.sendHttpRequest(req)
	if err != nil {
		return err
	}

	var res APIRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", body, err)
	}

	if res.Error != "" {
		return fmt.Errorf("api responded with error message: %s", res.Error)
	}

	if responseObject != nil {
		err = json.Unmarshal(res.Data, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, res.Data: %s, unmarshal err: %s", string(res.Data), err)
		}
	}

	return nil
}

func (a *API) sendHttpRequest(req *http.Request) ([]byte, error) {
	res, err := a.client.SendRequest(req)
	if err != nil {
		return nil, err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	// Check non 200 status codes range
	if res.StatusCode < 200 || res.StatusCode >= 300 {
		return body, fmt.Errorf("Unauthorized request, msg: %s", string(body))
	}

	return body, nil
}
