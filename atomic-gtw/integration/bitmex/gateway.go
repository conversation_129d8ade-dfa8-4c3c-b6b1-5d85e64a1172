package bitmex

import (
	"fmt"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/qct/bitmex-go/swagger"
)

var Exchange = gateway.Exchange{
	Name: "BitMEX",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *MarketDataGateway
	accountGateway    *AccountGateway
	tickCh            chan gateway.Tick
	sdk               *swagger.APIClient
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		sdk:     swagger.NewAPIClient(swagger.NewConfiguration()),
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	var err error
	ws := NewWsSession(g.options)
	if err := ws.Connect(); err != nil {
		return err
	}

	g.accountGateway = NewAccountGateway(g.options, g.api, g.tickCh)
	if g.options.ApiKey != "" {
		err = g.accountGateway.Connect(ws)
		if err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.options, g.tickCh)
	if err := g.marketDataGateway.Connect(ws); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	intruments, _, err := g.sdk.InstrumentApi.InstrumentGetActive()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, len(intruments))
	for i, intrument := range intruments {
		commonMarkets[i] = gateway.Market{
			Exchange: Exchange,
			Symbol:   intrument.Symbol,
			Pair: gateway.Pair{
				Base:  intrument.RootSymbol,
				Quote: intrument.QuoteCurrency,
			},
			MakerFee:           -0.00025,
			TakerFee:           0.00075,
			PriceTick:          intrument.TickSize,
			AmountTick:         1, // BitMEX doesn't negotiate fractional contracts
			MarketType:         gateway.FuturesMarket,
			FuturesMarginAsset: "XBT",
		}
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	var bids []gateway.PriceLevel
	var asks []gateway.PriceLevel

	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	for _, level := range depth {
		priceLevel := gateway.PriceLevel{
			Price:  level.Price,
			Amount: level.Size,
		}
		if level.Side == "Sell" {
			asks = append(asks, priceLevel)
		} else {
			bids = append(bids, priceLevel)
		}
	}

	return gateway.DepthBook{
		Bids: bids,
		Asks: asks,
	}, nil
}
