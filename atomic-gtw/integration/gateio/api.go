package gateio

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/gateio/gateapi-go/v6"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type API struct {
	options          gateway.Options
	httpClient       *gateapi.APIClient
	directHttpClient *utils.HttpClient
	baseURL          string
}

func NewAPI(options gateway.Options) *API {
	client := gateapi.NewAPIClient(gateapi.NewConfiguration())
	directClient := utils.NewHttpClient()
	directClient.UseProxies(options.Proxies)

	return &API{
		options:          options,
		httpClient:       client,
		directHttpClient: directClient,
		baseURL:          "https://api.gateio.ws/api/v4",
	}
}

// https://github.com/gateio/gateapi-go/blob/master/docs/SpotApi.md#listcurrencypairs
// Return type: https://github.com/gateio/gateapi-go/blob/master/docs/CurrencyPair.md
func (api *API) FetchSymbols() (*[]gateapi.CurrencyPair, error) {
	ctx := api.buildRequestContext()
	symbols, _, err := api.httpClient.SpotApi.ListCurrencyPairs(ctx)
	if err != nil {
		return nil, err
	}

	return &symbols, nil
}

// https://github.com/gateio/gateapi-go/blob/master/docs/SpotApi.md#listspotaccounts
// Return type: https://github.com/gateio/gateapi-go/blob/master/docs/SpotAccount.md
func (api *API) FetchBalances() ([]gateapi.SpotAccount, error) {
	ctx := api.buildRequestContext()
	result, _, err := api.httpClient.SpotApi.ListSpotAccounts(ctx, nil)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// https://github.com/gateio/gateapi-go/blob/master/docs/SpotApi.md#listallopenorders
// Return type: https://github.com/gateio/gateapi-go/blob/master/docs/OpenOrders.md
func (api *API) FetchOrders(pair, status string) ([]gateapi.Order, error) {
	ctx := api.buildRequestContext()
	result, _, err := api.httpClient.SpotApi.ListOrders(ctx, pair, status, nil)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// https://github.com/gateio/gateapi-go/blob/master/docs/SpotApi.md#createorder
// Return type: https://github.com/gateio/gateapi-go/blob/master/docs/Order.md
func (api *API) SendOrder(order gateapi.Order) (*gateapi.Order, error) {
	ctx := api.buildRequestContext()
	result, _, err := api.httpClient.SpotApi.CreateOrder(ctx, order)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// https://github.com/gateio/gateapi-go/blob/master/docs/SpotApi.md#cancelorder
// Return type: https://github.com/gateio/gateapi-go/blob/master/docs/Order.md
func (api *API) CancelOrder(orderID string, pair string) (*gateapi.Order, error) {
	ctx := api.buildRequestContext()
	result, _, err := api.httpClient.SpotApi.CancelOrder(ctx, orderID, pair, nil)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

type APIDepthBook struct {
	Asks []gateway.PriceArray `json:"asks"`
	Bids []gateway.PriceArray `json:"bids"`
}

func (api *API) DepthBook(symbol string, params gateway.GetDepthParams) (APIDepthBook, error) {
	if params.Limit == 0 {
		params.Limit = 100
	}

	req, err := api.newHttpRequest(http.MethodGet, "/spot/order_book", nil)
	if err != nil {
		return APIDepthBook{}, err
	}

	q := req.URL.Query()
	q.Set("currency_pair", symbol)
	q.Set("limit", strconv.Itoa(params.Limit))
	q.Set("with_id", "true")
	req.URL.RawQuery = q.Encode()

	var depth APIDepthBook
	err = api.makeHttpRequest(req, &depth)
	if err != nil {
		return APIDepthBook{}, err
	}

	return depth, nil
}

func (api *API) newHttpRequest(method string, path string, data io.Reader) (*http.Request, error) {
	url := api.baseURL + path
	req, err := http.NewRequest(method, url, data)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	return req, nil
}

func (api *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	req = req.WithContext(ctx)

	res, err := api.directHttpClient.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}

	if res.StatusCode >= 400 {
		return fmt.Errorf("api request failed with status code %d, body: %s", res.StatusCode, string(body))
	}

	if responseObject != nil {
		err = json.Unmarshal(body, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return nil
}

func (api *API) buildRequestContext() context.Context {
	ctx := context.WithValue(context.Background(), gateapi.ContextGateAPIV4, gateapi.GateAPIV4{
		Key:    api.options.ApiKey,
		Secret: api.options.ApiSecret,
	})

	return ctx
}
