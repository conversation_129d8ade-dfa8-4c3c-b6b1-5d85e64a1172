package gateio

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/gateio/gateapi-go/v6"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Gate.io",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (gtw *Gateway) Connect() error {
	ws := NewWsSession(gtw.options)
	if err := ws.Connect(); err != nil {
		return err
	}

	gtw.accountGateway = NewAccountGateway(gtw.api, gtw.tickCh, gtw.options)
	if gtw.options.ApiKey != "" {
		if err := gtw.accountGateway.Connect(ws); err != nil {
			return err
		}
	}

	gtw.marketDataGateway = NewMarketDataGateway(gtw.options, gtw.tickCh)
	if err := gtw.marketDataGateway.Connect(ws); err != nil {
		return err
	}

	return nil
}

func (gtw *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return gtw.marketDataGateway.SubscribeMarkets(markets)
}

func (gtw *Gateway) Close() error {
	return nil
}

func (gtw *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.FetchSymbols()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(*symbols))
	for _, symbol := range *symbols {
		if symbol.TradeStatus == "untradable" {
			continue
		}

		commonMarkets = append(commonMarkets, symbolToCommonMarket(symbol))
	}

	return commonMarkets, nil
}

func (gtw *Gateway) AccountGateway() gateway.AccountGateway {
	return gtw.accountGateway
}

func (gtw *Gateway) Tick() chan gateway.Tick {
	return gtw.tickCh
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: gateway.PriceArrayToPriceLevels(depth.Asks),
		Bids: gateway.PriceArrayToPriceLevels(depth.Bids),
	}

	return depthBook, nil
}

func symbolToCommonMarket(symbol gateapi.CurrencyPair) gateway.Market {
	priceTick := 1 / math.Pow10(int(symbol.Precision))
	amountTick := 1 / math.Pow10(int(symbol.AmountPrecision))

	pair := gateway.Pair{
		Base:  strings.ToUpper(symbol.Base),
		Quote: strings.ToUpper(symbol.Quote),
	}

	feePct, err := strconv.ParseFloat(symbol.Fee, 64)
	if err != nil {
		panic(fmt.Sprintf("Problem converting Gate.io fee, err: %s", err))
	}

	fee := feePct / 100.0

	var minOrderSize float64 = 0 // zero means there's no limit

	if symbol.MinBaseAmount != "" {
		minOrderSize, err = strconv.ParseFloat(symbol.MinBaseAmount, 64)
		if err != nil {
			panic(fmt.Sprintf("Problem converting Gate.io MinBaseAmount, err: %s", err))
		}
	}

	var minOrderMoneyValue float64 = 0 // zero means there's no limit

	if symbol.MinQuoteAmount != "" {
		minOrderMoneyValue, err = strconv.ParseFloat(symbol.MinQuoteAmount, 64)
		if err != nil {
			panic(fmt.Sprintf("Problem converting Gate.io MinQuoteAmount, err: %s", err))
		}
	}

	return gateway.Market{
		Exchange:               Exchange,
		Pair:                   pair,
		Symbol:                 symbol.Id,
		TakerFee:               fee,
		MakerFee:               fee,
		PriceTick:              priceTick,
		AmountTick:             amountTick,
		MinimumOrderSize:       minOrderSize,
		MinimumOrderMoneyValue: minOrderMoneyValue,
		PaysFeeInStock:         true, // TODO: Check, I'm not sure about the value here
		MarketType:             gateway.SpotMarket,
	}
}
