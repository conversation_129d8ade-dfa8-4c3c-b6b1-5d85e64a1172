package foxbit

import (
	"fmt"
	"log"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

var insuficientBalanceMatch = regexp.MustCompile(`Insufficient funds`)
var cannotSubmitOrderMatch = regexp.MustCompile(`Cannot submit order`)
var tooManyRequestsMatch = regexp.MustCompile(`Too many requests`)

type AccountGateway struct {
	base.AccountGateway
	tickCh     chan gateway.Tick
	api        *API
	openOrders map[string]gateway.Order
}

func NewAccountGateway(tickCh chan gateway.Tick, api *API) *AccountGateway {
	return &AccountGateway{
		tickCh:     tickCh,
		api:        api,
		openOrders: make(map[string]gateway.Order),
	}
}

func (g *AccountGateway) Connect() error {
	// TODO: Use this once order updates are supported by their websocket
	//g.handleAuth()

	go g.tradesUpdateHandler()

	return nil
}

func (g *AccountGateway) tradesUpdateHandler() {
	for {
		for id, order := range g.openOrders {
			res, err := g.api.GetOrder(id)
			if err != nil {
				log.Printf("Failed to get order update for order [%s], err: %s", id, err)
				continue
			}
			order.State = apiStateToGateway(res.State)
			order.FilledAmount = res.QuantityExecuted
			order.AvgPrice = res.PriceAvg

			g.tickCh <- gateway.TickWithEvents(gateway.NewOrderUpdateEvent(order))

			// Check if order still open, if not, stop checking for updates
			if order.State != gateway.OrderOpen && order.State != gateway.OrderPartiallyFilled {
				delete(g.openOrders, id)
			}
		}

		time.Sleep(3 * time.Second)
	}
}

func apiStateToGateway(st string) gateway.OrderState {
	switch st {
	case "CANCELED":
		return gateway.OrderCancelled
	case "ACTIVE":
		return gateway.OrderOpen
	case "FILLED":
		return gateway.OrderFullyFilled
	case "PARTIALLY_FILLED":
		return gateway.OrderPartiallyFilled
	case "PARTIALLY_CANCELED":
		return gateway.OrderCancelled
	default:
		return gateway.OrderUnknown
	}
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	query := url.Values{}
	query.Set("page_size", "100")
	query.Set(
		"market_symbol",
		market.Symbol,
	)
	query.Set("state", "ACTIVE")

	res, err := g.api.Orders(query)
	if err != nil {
		return []gateway.Order{}, err
	}

	orders := make([]gateway.Order, 0, len(res))
	for _, resOrder := range res {
		orders = append(orders, apiOrderToGateway(market, resOrder))
	}

	return orders, nil
}

func apiOrderToGateway(market gateway.Market, apiOrder APIOrder) gateway.Order {
	return gateway.Order{
		Market:           market,
		ID:               apiOrder.Sn,
		ClientOrderID:    apiOrder.ClientOrderID,
		Side:             apiSideToGateway(apiOrder.Side),
		State:            apiStateToGateway(apiOrder.State),
		Amount:           apiOrder.Quantity,
		Price:            apiOrder.Price,
		AvgPrice:         apiOrder.PriceAvg,
		FilledAmount:     apiOrder.QuantityExecuted,
		FilledMoneyValue: apiOrder.QuantityExecuted * apiOrder.PriceAvg,
	}
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	res, err := g.api.Accounts()
	if err != nil {
		return []gateway.Balance{}, fmt.Errorf("Failed to get balances, err: %s", err)
	}

	balances := make([]gateway.Balance, 0, len(res))
	for _, resBalance := range res {
		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(resBalance.CurrencySymbol),
			Total:     resBalance.Balance,
			Available: resBalance.BalanceAvailable,
		})
	}

	return balances, nil
}

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	req := APIPlaceOrderReq{
		MarketSymbol: order.Market.Symbol,
		Price:        utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
		Quantity:     utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		Side:         gatewaySideToAPI(order.Side),
		Type:         "LIMIT",
		PostOnly:     order.PostOnly,
	}

	res, err := g.api.PlaceOrder(req)
	if err != nil {
		switch {
		case insuficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		case cannotSubmitOrderMatch.MatchString(err.Error()):
			return "", &gateway.OrderNotOpenedErr{Err: err}
		case tooManyRequestsMatch.MatchString(err.Error()):
			return "", gateway.RateLimitErr
		default:
			return "", err
		}
	}

	if res.Sn == "" {
		return "", fmt.Errorf("exptected api res to contain order id (sn), returned nil")
	}

	// Register order
	order.ID = res.Sn
	g.openOrders[res.Sn] = order

	return res.Sn, err
}

func gatewaySideToAPI(side gateway.Side) string {
	if side == gateway.Bid {
		return "BUY"
	} else if side == gateway.Ask {
		return "SELL"
	}
	return ""
}

func apiSideToGateway(side string) gateway.Side {
	if side == "BUY" {
		return gateway.Bid
	} else if side == "SELL" {
		return gateway.Ask
	}
	return ""
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID)
}
