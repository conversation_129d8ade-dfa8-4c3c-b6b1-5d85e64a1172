package foxbit

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

// The order book response is represented by a single array.
// The indexes bellow help us to know what each array position means.
const (
	orderInstrumentIdIdx = 7
	orderSideIdx         = 9
	orderPriceIdx        = 6
	orderAmountIdx       = 8
	tradeIdIdx           = 0
	tradeInstrumentIdIdx = 1
	tradeAmountIdx       = 2
	tradePriceIdx        = 3
	tradeTimestampIdx    = 6
	tradeSideIdx         = 8
)

type MarketDataGateway struct {
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewMarketDataGateway(opts gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options: opts,
		tickCh:  tickCh,
	}
}

func (mg *MarketDataGateway) Connect() error {
	return nil
}

func (mg *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	log.Printf("%s subscribing to %d markets...", Exchange.Name, len(markets))

	ws := NewWsSession(mg.options)
	err := ws.Connect()
	if err != nil {
		return fmt.Errorf("failed to open ws, err: %s", err)
	}

	respCh := make(chan WsMessage)

	// Load instruments to markets
	instrumentsMap := make(map[int]gateway.Market)
	ws.SubscribeMessages("GetInstruments", respCh, nil)
	request := WsMessage{
		Type:           0,
		SequenceNumber: 0,
		FunctionName:   "GetInstruments",
		Payload:        `{"OMSId": 1}`,
	}

	done := make(chan error)
	quit := make(chan bool)
	go func() {
		for {
			select {
			case msg := <-respCh:
				if msg.Type == 0 {
					var instruments []WsInstrument
					err := json.Unmarshal([]byte(msg.Payload), &instruments)
					if err != nil {
						done <- fmt.Errorf("%s error on unmarshal markets. Err: %s", Exchange.Name, err)
						return
					}

					// Map markets to instruments
					for _, mkt := range markets {
						present := false
						for _, inst := range instruments {
							if inst.Product1Symbol == mkt.Pair.Base && inst.Product2Symbol == mkt.Pair.Quote {
								instrumentsMap[inst.InstrumentId] = mkt
								present = true
								break
							}
						}

						if !present {
							done <- fmt.Errorf("expected market [%s] to be present on listed instruments on ws", mkt)
							return
						}
					}

					done <- nil
					return
				} else if msg.Type == 5 {
					done <- fmt.Errorf("ws err res: %s", msg.Payload)
					return
				}

			case <-quit:
				return
			}
		}
	}()

	err = ws.SendRequest(request)
	if err != nil {
		return fmt.Errorf("send req err: %s", err)
	}

	select {
	case err := <-done:
		ws.Close()
		if err != nil {
			return fmt.Errorf("failed to get instruments, err: %s", err)
		}
	case <-time.After(5 * time.Second):
		quit <- true
		ws.Close()
		return fmt.Errorf("timed out waiting for GetInstruments response")
	}

	// Subscribe to market data
	orderBookFunctionName := "SubscribeLevel2"
	tradeFunctionName := "SubscribeTrades"

	go mg.subscriptionMessageHandler(respCh, instrumentsMap)

	var requests []WsMessage

	for instrumentId, mkt := range instrumentsMap {
		ws := NewWsSession(mg.options)
		err := ws.Connect()
		if err != nil {
			return fmt.Errorf("failed to open ws for mkt [%s], err: %s", err, mkt)
		}

		orderBookRequest := WsMessage{
			Type:           0,
			SequenceNumber: 0,
			FunctionName:   orderBookFunctionName,
			Payload:        fmt.Sprintf(`{"OMSId": 1, "InstrumentId": %v, "Depth": 100}`, instrumentId),
		}

		tradesRequest := WsMessage{
			Type:           0,
			SequenceNumber: 0,
			FunctionName:   tradeFunctionName,
			Payload:        fmt.Sprintf(`{"OMSId": 1, "InstrumentId": %v, "Depth": 100}`, instrumentId),
		}

		requests = append(requests, orderBookRequest, tradesRequest)

		ws.SubscribeMessages(orderBookFunctionName, respCh, nil)
		ws.SubscribeMessages(tradeFunctionName, respCh, nil)
		ws.SubscribeMessages("Level2UpdateEvent", respCh, nil)
		ws.SubscribeMessages("TradeDataUpdateEvent", respCh, nil)

		err = ws.RequestSubscriptions(requests)
		if err != nil {
			return fmt.Errorf("failed to open ws for instrument [%d] market [%s], err: %s", instrumentId, mkt, err)
		}
	}

	return nil
}

func (mg *MarketDataGateway) subscriptionMessageHandler(ch chan WsMessage, instrumentsMap map[int]gateway.Market) {
	printError := func(err error, functionName string) {
		if err != nil {
			log.Printf("%s error processing msg from channel %q, err: %s\n", Exchange.Name, functionName, err)
		}
	}

	for msg := range ch {
		// After a subscription we receive type 0 with a response.
		// The subsequent messages have type 3 representing an event
		// with another response.
		if msg.Type != 0 && msg.Type != 3 {
			continue
		}

		switch msg.FunctionName {
		// Althoug we subscribe for "SubscribeLevel2", the subsequent responses
		// are named "Level2UpdateEvent" (msg.Type == 3). The same logic applyes
		// for the "SubscribeTrades".
		case "SubscribeLevel2", "Level2UpdateEvent":
			err := mg.processOrderBook(msg, instrumentsMap)
			printError(err, msg.FunctionName)
		case "SubscribeTrades", "TradeDataUpdateEvent":
			err := mg.processTrades(msg, instrumentsMap)
			printError(err, msg.FunctionName)
		}
	}
}

func (mg *MarketDataGateway) processOrderBook(msg WsMessage, instrumentsMap map[int]gateway.Market) error {
	var eventLogs []gateway.Event

	var orders [][]float64

	if err := json.Unmarshal([]byte(msg.Payload), &orders); err != nil {
		log.Printf("Failed to unmarshal %s ws order book update. Err: %s. \n\nData: %s", Exchange.Name, err, msg.Payload)
		return err
	}

	var snapshotSequence bool
	for _, order := range orders {
		instrumentId := int(order[orderInstrumentIdIdx])
		side := normalizeSide(int(order[orderSideIdx]))
		price := order[orderPriceIdx]
		amount := order[orderAmountIdx]

		mkt, ok := instrumentsMap[instrumentId]
		if !ok {
			return fmt.Errorf("market with instrument id %d not found", instrumentId)
		}

		// Consider the first message of the sequence as the snapshot
		if msg.SequenceNumber == 0 && !snapshotSequence {
			snapshotSequence = true
			eventLogs = append(eventLogs, gateway.Event{
				Type: gateway.SnapshotSequenceEvent,
				Data: gateway.SnapshotSequence{
					Symbol: mkt.Symbol,
				},
			})
		}

		event := gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: mkt.Symbol,
				Side:   side,
				Price:  price,
				Amount: amount,
			},
		}

		eventLogs = append(eventLogs, event)
	}

	mg.dispatchEventLogs(eventLogs)

	return nil
}

func (mg *MarketDataGateway) processTrades(msg WsMessage, instrumentsMap map[int]gateway.Market) error {
	var eventLogs []gateway.Event

	var trades [][11]interface{}

	if err := json.Unmarshal([]byte(msg.Payload), &trades); err != nil {
		log.Printf("Failed to unmarshal %s ws trades. Err: %s. \n\nData: %s", Exchange.Name, err, msg.Payload)
		return err
	}

	for _, trade := range trades {
		tradeIdENotation, _ := strconv.ParseFloat(fmt.Sprint(trade[tradeIdIdx]), 64) // e.g., 6.485983e+06
		tradeId := fmt.Sprint(int(tradeIdENotation))

		instrumentId, _ := strconv.Atoi(fmt.Sprint(trade[tradeInstrumentIdIdx]))
		side, _ := strconv.Atoi(fmt.Sprint(trade[tradeSideIdx]))
		amount, _ := strconv.ParseFloat(fmt.Sprint(trade[tradeAmountIdx]), 64)
		price, _ := strconv.ParseFloat(fmt.Sprint(trade[tradePriceIdx]), 64)

		mkt, ok := instrumentsMap[instrumentId]
		if !ok {
			return fmt.Errorf("market with instrument id %d not found", instrumentId)
		}

		// Foxbit numbers might come in scientific notation. It's easier to convert
		// to a float64 then an int64
		var timestampData string = fmt.Sprint(trade[tradeTimestampIdx])
		timestampMs, err := strconv.ParseFloat(timestampData, 64)
		if err != nil {
			log.Printf("%s failed to convert timestamp [%s] to float64, err: %s", Exchange, timestampData, err)
		}

		eventLogs = append(eventLogs, gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				Symbol:    mkt.Symbol,
				ID:        tradeId,
				Timestamp: time.Unix(0, int64(timestampMs)*int64(time.Millisecond)),
				Direction: normalizeSide(side),
				Amount:    amount,
				Price:     price,
			},
		})
	}

	mg.dispatchEventLogs(eventLogs)

	return nil
}

func (mg *MarketDataGateway) dispatchEventLogs(events []gateway.Event) {
	mg.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}
}

func normalizeSide(side int) gateway.Side {
	if side == 0 {
		return gateway.Bid
	}

	return gateway.Ask
}
