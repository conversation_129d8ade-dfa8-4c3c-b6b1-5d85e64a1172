package foxbit

import (
	"fmt"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Foxbit",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.tickCh, g.api)
	if g.options.ApiKey != "" {
		err := g.accountGateway.Connect()
		if err != nil {
			return err
		}
	}

	g.marketDataGateway = NewMarketDataGateway(g.options, g.tickCh)
	if err := g.marketDataGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.Markets()
	if err != nil {
		err = fmt.Errorf("failed to load pairs: %s", err)
		return nil, err
	}

	commonMarkets := make([]gateway.Market, len(res))
	for i, apiMarket := range res {
		base := strings.ToUpper(apiMarket.Base.Symbol)
		quote := strings.ToUpper(apiMarket.Quote.Symbol)

		commonMarkets[i] = gateway.Market{
			Exchange:         Exchange,
			Pair:             gateway.Pair{Base: base, Quote: quote},
			Symbol:           apiMarket.Symbol,
			PriceTick:        apiMarket.PriceIncrement,
			AmountTick:       apiMarket.QuantityIncrement,
			MinimumOrderSize: apiMarket.QuantityMin,
			MarketType:       gateway.SpotMarket,
		}
	}

	return commonMarkets, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: gateway.PriceArrayToPriceLevels(depth.Asks),
		Bids: gateway.PriceArrayToPriceLevels(depth.Bids),
	}

	return depthBook, nil
}
