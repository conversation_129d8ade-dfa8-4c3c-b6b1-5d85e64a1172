package temp_uniswapv4_evawbtc

import (
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type MarketDataGateway struct {
	api             *API
	options         gateway.Options
	refreshInterval time.Duration
	tickCh          chan gateway.Tick
	connected       bool
}

func NewMarketDataGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	refreshInterval := time.Duration(options.RefreshIntervalMs) * time.Millisecond
	if refreshInterval == 0*time.Second {
		log.Printf("TempUniswapV4EVAWBTC defaulting refreshInterval to 60 seconds updates...")
		refreshInterval = 60 * time.Second
	} else if refreshInterval < 10*time.Second {
		log.Printf("TempUniswapV4EVAWBTC refreshInterval cannot be less than 10 seconds, defaulting to 10 seconds updates...")
		refreshInterval = 10 * time.Second
	}

	return &MarketDataGateway{
		api:             api,
		options:         options,
		refreshInterval: refreshInterval,
		tickCh:          tickCh,
	}
}

func (g *MarketDataGateway) SubscribeMarkets() error {
	if g.connected {
		return nil
	}

	lastRefresh := time.Now()
	err := g.refreshQuotes()
	if err != nil {
		return err
	}

	// Refresh quotes
	go func() {
		for {
			time.Sleep(g.refreshInterval)

			err := g.refreshQuotes()
			if err != nil {
				log.Printf("TempUniswapV4EVAWBTC will retry in %s, after err to refresh: %s", g.refreshInterval, err)

				if time.Since(lastRefresh) > 5*time.Minute {
					log.Printf("TempUniswapV4EVAWBTC failed to refresh quotes for 5 minutes, exiting...")
					panic(err)
				}
			} else {
				lastRefresh = time.Now()
			}
		}
	}()

	g.connected = true
	return nil
}

func (g *MarketDataGateway) refreshQuotes() error {
	priceHistory, err := g.api.GetPoolPriceHistory()
	if err != nil {
		return err
	}

	if len(priceHistory) == 0 {
		log.Printf("TempUniswapV4EVAWBTC no price history data received")
		return nil
	}

	// Get the latest price (last entry in the array)
	latestPrice := priceHistory[len(priceHistory)-1]

	// Use token0Price as the EVA/WBTC rate
	price := latestPrice.Token0Price
	if price <= 0 {
		log.Printf("TempUniswapV4EVAWBTC invalid price received: %f", price)
		return nil
	}

	symbol := "EVAWBTC"

	// Create a minimal spread (0.01% each side)
	spread := price * 0.0001
	bidPrice := price - spread
	askPrice := price + spread

	// Large liquidity amount
	liquidity := 1000000000.0

	events := []gateway.Event{
		{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: symbol,
			},
		},
		{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Bid,
				Price:  bidPrice,
				Amount: liquidity,
			},
		},
		{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Ask,
				Price:  askPrice,
				Amount: liquidity,
			},
		},
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}

	return nil
}
