package temp_uniswapv4_evawbtc

import (
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name:    "TempUniswapV4EVAWBTC",
	ChainID: gateway.ChainArbitrum,
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *MarketDataGateway
	api               *API
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		api:     NewAPI(),
		options: options,
		tickCh:  make(chan gateway.Tick, 10),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.marketDataGateway = NewMarketDataGateway(g.api, g.options, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets()
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

// Price tick for crypto pairs
const PRICE_TICK = 0.00000001
const AMOUNT_TICK = 0.00000001

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	return []gateway.Market{
		{
			Exchange: Exchange,
			Pair: gateway.Pair{
				Base:  "EVA",
				Quote: "WBTC",
			},
			Symbol:     "EVAWBTC",
			PriceTick:  PRICE_TICK,
			AmountTick: AMOUNT_TICK,
		},
	}, nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
