package temp_uniswapv4_evawbtc

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"

	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBase = "https://interface.gateway.uniswap.org/v1/graphql"
	poolID  = "0x87258b75824906c350fe0175af168ed137cb3c91af558269667c005cc746b9d5"
)

type API struct {
	httpClient *utils.HttpClient
}

func NewAPI() *API {
	client := utils.NewHttpClient()
	return &API{
		httpClient: client,
	}
}

type GraphQLRequest struct {
	OperationName string                 `json:"operationName"`
	Variables     map[string]interface{} `json:"variables"`
	Query         string                 `json:"query"`
}

type PriceHistoryEntry struct {
	ID          string  `json:"id"`
	Token0Price float64 `json:"token0Price"`
	Token1Price float64 `json:"token1Price"`
	Timestamp   int64   `json:"timestamp"`
}

type V4Pool struct {
	ID           string              `json:"id"`
	PriceHistory []PriceHistoryEntry `json:"priceHistory"`
}

type GraphQLResponse struct {
	Data struct {
		V4Pool V4Pool `json:"v4Pool"`
	} `json:"data"`
	Errors []struct {
		Message string `json:"message"`
	} `json:"errors"`
}

func (a *API) GetPoolPriceHistory() ([]PriceHistoryEntry, error) {
	query := `query PoolPriceHistory($chain: Chain!, $addressOrId: String!, $duration: HistoryDuration!, $isV4: Boolean!, $isV3: Boolean!, $isV2: Boolean!) {
		v4Pool(chain: $chain, poolId: $addressOrId) @include(if: $isV4) {
			id
			priceHistory(duration: $duration) {
				id
				token0Price
				token1Price
				timestamp
				__typename
			}
			__typename
		}
		v3Pool(chain: $chain, address: $addressOrId) @include(if: $isV3) {
			id
			priceHistory(duration: $duration) {
				id
				token0Price
				token1Price
				timestamp
				__typename
			}
			__typename
		}
		v2Pair(chain: $chain, address: $addressOrId) @include(if: $isV2) {
			id
			priceHistory(duration: $duration) {
				id
				token0Price
				token1Price
				timestamp
				__typename
			}
			__typename
		}
	}`

	request := GraphQLRequest{
		OperationName: "PoolPriceHistory",
		Variables: map[string]interface{}{
			"addressOrId": poolID,
			"chain":       "ARBITRUM",
			"duration":    "DAY",
			"isV4":        true,
			"isV3":        false,
			"isV2":        false,
		},
		Query: query,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", apiBase, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set required headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Origin", "https://app.uniswap.org")
	req.Header.Set("Referer", "https://app.uniswap.org/")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	req.Header.Set("_dd-custom-header-graph-ql-operation-name", "PoolPriceHistory")
	req.Header.Set("_dd-custom-header-graph-ql-operation-type", "query")
	req.Header.Set("sec-fetch-dest", "empty")
	req.Header.Set("sec-fetch-mode", "cors")
	req.Header.Set("sec-fetch-site", "same-site")

	resp, err := a.httpClient.SendRequest(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var response GraphQLResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(response.Errors) > 0 {
		return nil, fmt.Errorf("GraphQL errors: %v", response.Errors)
	}

	return response.Data.V4Pool.PriceHistory, nil
}
