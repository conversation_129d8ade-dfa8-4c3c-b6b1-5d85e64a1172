package klever

import (
	"fmt"
	"log"
	"reflect"
	"testing"

	"github.com/herenow/atomic-gtw/gateway"
	managerpbv1 "github.com/herenow/atomic-gtw/integration/klever/api/manager"
)

// Test for sortFillsAsc
func Test_SortFillsAsc(t *testing.T) {
	// Test data
	fills := []Fill{
		{ID: 33044979, OrderID: "9b104d17-1844-42c3-ae0b-3c66226d9357", Price: 0.059809, Quantity: 141, Side: managerpbv1.OrderSide_BUY, UpdatedAt: 1717937836},
		{ID: 33044980, OrderID: "c52fe753-e749-487d-9f10-ce3ed278c3ff", Price: 0.059809, Quantity: 141, Side: managerpbv1.OrderSide_SELL, UpdatedAt: 1717937836},
		{ID: 33044975, OrderID: "9b104d17-1844-42c3-ae0b-3c66226d9357", Price: 0.059809, Quantity: 474, Side: managerpbv1.OrderSide_BUY, UpdatedAt: 1717937836},
		{ID: 33044976, OrderID: "8d29a940-7c34-44ef-b9db-eb9f684e9140", Price: 0.059809, Quantity: 474, Side: managerpbv1.OrderSide_SELL, UpdatedAt: 1717937836},
		{ID: 33044939, OrderID: "8b344556-5cbb-4b73-ba70-0001ac18c57f", Price: 0.059787, Quantity: 27, Side: managerpbv1.OrderSide_SELL, UpdatedAt: 1717937728},
		{ID: 33044940, OrderID: "203945b0-7f95-48a3-a236-4dba7ac2f64f", Price: 0.059787, Quantity: 27, Side: managerpbv1.OrderSide_BUY, UpdatedAt: 1717937728},
		{ID: 33044937, OrderID: "8b344556-5cbb-4b73-ba70-0001ac18c57f", Price: 0.059787, Quantity: 177, Side: managerpbv1.OrderSide_SELL, UpdatedAt: 1717937728},
		{ID: 33044938, OrderID: "2a135987-46b4-4f0b-9d0c-7d66cc604dc7", Price: 0.059787, Quantity: 177, Side: managerpbv1.OrderSide_BUY, UpdatedAt: 1717937728},
		{ID: 33044915, OrderID: "47da4a0c-9e35-41ee-9379-2321f02513db", Price: 0.059763, Quantity: 74, Side: managerpbv1.OrderSide_BUY, UpdatedAt: 1717937611},
		{ID: 33044916, OrderID: "3c31479d-8878-4778-a72c-6f7365f2cff0", Price: 0.059763, Quantity: 74, Side: managerpbv1.OrderSide_SELL, UpdatedAt: 1717937611},
	}

	expected := []Fill{
		{ID: 33044915, OrderID: "47da4a0c-9e35-41ee-9379-2321f02513db", Price: 0.059763, Quantity: 74, Side: managerpbv1.OrderSide_BUY, UpdatedAt: 1717937611},
		{ID: 33044916, OrderID: "3c31479d-8878-4778-a72c-6f7365f2cff0", Price: 0.059763, Quantity: 74, Side: managerpbv1.OrderSide_SELL, UpdatedAt: 1717937611},
		{ID: 33044937, OrderID: "8b344556-5cbb-4b73-ba70-0001ac18c57f", Price: 0.059787, Quantity: 177, Side: managerpbv1.OrderSide_SELL, UpdatedAt: 1717937728},
		{ID: 33044938, OrderID: "2a135987-46b4-4f0b-9d0c-7d66cc604dc7", Price: 0.059787, Quantity: 177, Side: managerpbv1.OrderSide_BUY, UpdatedAt: 1717937728},
		{ID: 33044939, OrderID: "8b344556-5cbb-4b73-ba70-0001ac18c57f", Price: 0.059787, Quantity: 27, Side: managerpbv1.OrderSide_SELL, UpdatedAt: 1717937728},
		{ID: 33044940, OrderID: "203945b0-7f95-48a3-a236-4dba7ac2f64f", Price: 0.059787, Quantity: 27, Side: managerpbv1.OrderSide_BUY, UpdatedAt: 1717937728},
		{ID: 33044975, OrderID: "9b104d17-1844-42c3-ae0b-3c66226d9357", Price: 0.059809, Quantity: 474, Side: managerpbv1.OrderSide_BUY, UpdatedAt: 1717937836},
		{ID: 33044976, OrderID: "8d29a940-7c34-44ef-b9db-eb9f684e9140", Price: 0.059809, Quantity: 474, Side: managerpbv1.OrderSide_SELL, UpdatedAt: 1717937836},
		{ID: 33044979, OrderID: "9b104d17-1844-42c3-ae0b-3c66226d9357", Price: 0.059809, Quantity: 141, Side: managerpbv1.OrderSide_BUY, UpdatedAt: 1717937836},
		{ID: 33044980, OrderID: "c52fe753-e749-487d-9f10-ce3ed278c3ff", Price: 0.059809, Quantity: 141, Side: managerpbv1.OrderSide_SELL, UpdatedAt: 1717937836},
	}

	sortFillsAsc(fills)

	if !reflect.DeepEqual(fills, expected) {
		t.Errorf("sortFillsAsc() = %v, want %v", fills, expected)
	}
}

func Test_updateTrades(t *testing.T) {
	gtwOpts := gateway.Options{}
	var tickCh chan gateway.Tick

	api := NewAPI(gtwOpts)

	NewGateway(gtwOpts).GetMarkets()

	mktDataGtw := NewMarketDataGateway(gtwOpts, tickCh, api)

	lastFillID, err := mktDataGtw.updateFills(0, 33068473, gateway.Market{Symbol: "KUNAI-USDT"})
	if err != nil {
		log.Fatal(err)
	}

	fmt.Println(lastFillID)

}
