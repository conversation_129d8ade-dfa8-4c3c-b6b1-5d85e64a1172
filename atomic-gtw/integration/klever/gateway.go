package klever

import (
	"fmt"
	"log"
	"strconv"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

var Exchange = gateway.Exchange{
	Name: "Klever",
}

type Gateway struct {
	base.Gateway
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	options           gateway.Options
	api               *API
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	g.marketDataGateway = NewMarketDataGateway(g.options, g.tickCh, g.api)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.Symbols()
	if err != nil {
		return nil, fmt.Errorf("failed to load pairs: %s", err)
	}

	commonMarkets := make([]gateway.Market, len(*symbols))
	for i, s := range *symbols {
		fMinAmt, pErr := strconv.ParseFloat(s.MinAmount, 64)
		if pErr != nil {
			log.Printf(fmt.Errorf("[%s] failed to parse min order size: [%w]", Exchange, err).Error())
			continue
		}

		commonMarkets[i] = gateway.Market{
			Exchange:         Exchange,
			Pair:             gateway.Pair{Base: s.BaseAbbr, Quote: s.QuoteAbbr},
			Symbol:           s.Name,
			TakerFee:         0.001,
			MakerFee:         0.002,
			PriceTick:        utils.PrecisionToTick(int(s.QuotePrecision)),
			AmountTick:       utils.PrecisionToTick(int(s.BasePrecision)),
			MinimumOrderSize: fMinAmt,
			MarketType:       gateway.SpotMarket,
		}
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) GetDepthBook(market gateway.Market, limit gateway.GetDepthParams) (gateway.DepthBook, error) {
	var bids, asks []gateway.PriceLevel

	if limit.Limit == 0 {
		limit.Limit = 100
	}

	depth, err := g.api.Orderbook(market.Symbol, int32(limit.Limit), market)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	for _, ask := range depth.Asks {
		asks = append(asks, gateway.PriceLevel(ask))
	}
	for _, bid := range depth.Bids {
		bids = append(bids, gateway.PriceLevel(bid))
	}

	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}.Sort()

	return depthBook, nil
}
