package klever

import (
	"fmt"
	"log"
	"sort"
	"sync/atomic"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	managerpbv1 "github.com/herenow/atomic-gtw/integration/klever/api/manager"
)

type MarketDataGateway struct {
	tickCh  chan gateway.Tick
	options gateway.Options
	api     *API
}

func NewMarketDataGateway(options gateway.Options, tickCh chan gateway.Tick, api *API) *MarketDataGateway {
	return &MarketDataGateway{
		options: options,
		tickCh:  tickCh,
		api:     api,
	}
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	for _, market := range markets {
		go g.pollMarketDataFor(market)
	}
	return nil
}

// pollMarketDataFor polls market data for the given market at a specified refresh interval.
func (g *MarketDataGateway) pollMarketDataFor(market gateway.Market) {
	refreshInterval := 3 * time.Second
	if g.options.RefreshIntervalMs > 0 {
		refreshInterval = time.Duration(g.options.RefreshIntervalMs * int(time.Millisecond))
	}
	log.Printf("[%s] polling market data of [%s] every [%s]", market.Exchange, market.Symbol, refreshInterval)

	var seq int64

	// Polling order book updates
	bookTicker := time.NewTicker(refreshInterval)
	go func(market gateway.Market) {
		defer bookTicker.Stop()

		for range bookTicker.C {
			err := g.processOrderbookUpdate(atomic.LoadInt64(&seq), market)
			if err != nil {
				log.Printf(fmt.Errorf("[%s] market data failed to fetch orderbook [%s], err: [%w]", market.Exchange, market.Symbol, err).Error())
				bookTicker.Reset(5 * time.Second)
				continue
			}

			atomic.AddInt64(&seq, 1)
			bookTicker.Reset(refreshInterval)
		}
	}(market)

	// Polling order fills
	tradeTicker := time.NewTicker(refreshInterval)
	go func(market gateway.Market) {
		defer tradeTicker.Stop()

		var lastFillID int32
		for range tradeTicker.C {
			lastFillIDUpdate, err := g.updateFills(seq, lastFillID, market)
			if err != nil {
				log.Printf(fmt.Errorf("[%s] market data failed to fetch trades fills [%s], err: [%w]", market.Exchange, market.Symbol, err).Error())
				tradeTicker.Reset(5 * time.Second)
				continue
			}

			lastFillID = lastFillIDUpdate
			atomic.AddInt64(&seq, 1)
			tradeTicker.Reset(refreshInterval)
		}
	}(market)
}

func (g *MarketDataGateway) updateFills(seq int64, sinceLastFillID int32, market gateway.Market) (int32, error) {
	var fills []Fill
	var skip int32
	lastFillID := sinceLastFillID
	foundLastFillID := false

	for {
		newFills, err := g.api.MarketFills(market.Symbol, skip)
		if err != nil {
			return 0, err
		}

		if len(newFills) == 0 {
			break
		}

		sortFillsAsc(newFills)

		for _, fill := range newFills {
			if sinceLastFillID != 0 && fill.ID == sinceLastFillID {
				foundLastFillID = true
				break
			}
			fills = append(fills, fill)
		}

		if foundLastFillID || len(newFills) < 100 {
			break
		}

		// Update the skip value to fetch the next batch of fills
		skip += 100
	}

	sortFillsAsc(fills)

	if len(fills) > 0 {
		lastFillID = fills[len(fills)-1].ID
	}

	g.processFillsUpdate(seq, market, fills)

	return lastFillID, nil
}

// sortFillsAsc sorts fills in ascending order based on the Fill.ID
func sortFillsAsc(fills []Fill) {
	sort.Slice(fills, func(i, j int) bool {
		return fills[i].ID < fills[j].ID
	})
}

func (g *MarketDataGateway) processFillsUpdate(seq int64, market gateway.Market, fills []Fill) {
	events := make([]gateway.Event, 0, len(fills))

	for _, f := range fills {
		var side gateway.Side
		if f.Side == managerpbv1.OrderSide_BUY {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		events = append(events, gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				ID:        string(f.ID),
				OrderID:   f.OrderID,
				Symbol:    market.Symbol,
				Direction: side,
				Price:     f.Price,
				Amount:    f.Quantity,
				Timestamp: time.Unix(f.UpdatedAt, 0),
			},
		})
	}

	g.tickCh <- gateway.Tick{
		Sequence:          seq,
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}
}

func (g *MarketDataGateway) processOrderbookUpdate(seq int64, market gateway.Market) error {
	ob, err := g.api.Orderbook(market.Symbol, 100, market)
	if err != nil {
		return err
	}

	evLog := make([]gateway.Event, 0)
	evLog = append(evLog, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: market.Symbol,
		},
	})

	appendEventsToEventLog(&evLog, market.Symbol, gateway.Ask, ob.Asks)
	appendEventsToEventLog(&evLog, market.Symbol, gateway.Bid, ob.Bids)

	g.tickCh <- gateway.Tick{
		Sequence:          seq,
		ReceivedTimestamp: time.Now(),
		EventLog:          evLog,
	}
	return nil
}

// appendEventsToEventLog appends events to the eventLog slice for a given symbol and side.
func appendEventsToEventLog(evLog *[]gateway.Event, symbol string, side gateway.Side, prices []gateway.PriceArray) {
	for _, order := range prices {
		ev := gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   side,
				Price:  order.Price,
				Amount: order.Amount,
			},
		}
		*evLog = append(*evLog, ev)
	}
}
