package klever

import (
	"log"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	managerpbv1 "github.com/herenow/atomic-gtw/integration/klever/api/manager"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api           *API
	options       gateway.Options
	tickCh        chan gateway.Tick
	orderTracking *utils.OrderTracking
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	accGtw := &AccountGateway{
		api:           api,
		options:       options,
		tickCh:        tickCh,
		orderTracking: utils.NewOrderTracking(),
	}

	accGtw.orderTracking.SetUpdateFunc(accGtw.updateOrderStatus)

	return accGtw
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	accBalances, err := g.api.Balances()
	if err != nil {
		return []gateway.Balance{}, err
	}

	balances := make([]gateway.Balance, len(accBalances))
	for i, b := range accBalances {
		balances[i] = gateway.Balance{
			Asset:     b.Abbr,
			Available: b.Available,
			Total:     b.Balance,
		}
	}

	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	opOrders, err := g.api.OpenOrders(market.Symbol)
	if err != nil {
		return []gateway.Order{}, err
	}

	orders := make([]gateway.Order, len(opOrders))
	for i, o := range opOrders {

		var avg float64

		cost := o.Price * o.Quantity
		if o.Filled > 0 {
			avg = cost / o.Filled
		}

		orders[i] = gateway.Order{
			ID:               o.ID,
			Market:           market,
			State:            mapOrderStateToCommon(o.Status),
			Type:             mapOrderTypeToCommonType(o.Operation),
			Side:             mapOrderSideToCommonSide(o.Side),
			Price:            o.Price,
			AvgPrice:         avg,
			Amount:           o.Quantity,
			FilledAmount:     o.Filled,
			FilledMoneyValue: avg * o.Filled,
			Fee:              o.FeeAmount,
			FeeAsset:         o.FeeToken,
			PostOnly:         false,
			RemainingAmount:  o.Quantity - o.Filled,
		}
	}

	return orders, nil
}

func (g *AccountGateway) SendOrder(order gateway.Order) (string, error) {
	var side managerpbv1.OrderSide
	if order.Side == gateway.Bid {
		side = managerpbv1.OrderSide_BUY
	} else if order.Side == gateway.Ask {
		side = managerpbv1.OrderSide_SELL
	}

	orderID, err := g.api.PlaceOrder(PlaceOrderRequest{
		Symbol:   order.Market.Symbol,
		Side:     side,
		Quantity: utils.FloorToTick(order.Amount, order.Market.AmountTick),
		Price:    utils.FloorToTick(order.Price, order.Market.PriceTick),
	})
	if err != nil {
		switch {
		case strings.Contains(err.Error(), "insufficient available balance"):
			return "", gateway.InsufficientBalanceErr
		default:
			return "", err
		}
	}

	g.orderTracking.TrackAndMonitorOrder(orderID)

	return orderID, nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	// Stop monitoring the order while we cancel it.
	// This is necessary, since the monitoring might be triggered
	// while we wait for the cancel order to be processed.
	g.orderTracking.LockMonitoring(order.ID)

	err := g.api.CancelOrder(order.ID, order.Market.Symbol)
	if err != nil {
		switch {
		// Order in final stage, stop tracking
		case strings.Contains(err.Error(), "this order is already closed"):
			err = gateway.AlreadyCancelledErr
		default:
			g.orderTracking.UnlockMonitoring(order.ID)
			return err
		}
	}

	g.orderTracking.UntrackOrderWithUpdate(order.ID)
	g.orderTracking.UnlockMonitoring(order.ID)

	return err
}

// updateOrderStatus sends the state of the order to the gateway.Tick channel.
// It retrieves the current order status from the API for the specified order ID.
// If an error occurs during the retrieval, the error is returned.
// Otherwise, it constructs a gateway.OrderUpdateEvent based on the order status
// and sends it through the tickCh channel.
func (g *AccountGateway) updateOrderStatus(orderID string, step utils.OrderTrackingStep) error {
	o, err := g.api.OrderStatus(orderID)
	if err != nil {
		notFoundErr := strings.Contains(err.Error(), "no rows in result set")

		if step == utils.OrderTrackingUntrackStep && notFoundErr {
			// If the order is not found, it means it was not filled at all, and it was not saved.
			// In this case, the API will return 404m, and we should not return an error.
			// Consider nothing was filled.
			return nil
		} else {
			if notFoundErr {
				g.orderTracking.UntrackOrder(orderID)
				log.Printf("[%s] order tracking WARNING - order [%s] not found, stopped tracking", Exchange, orderID)
			}
			return err
		}
	}

	var avg float64
	cost := o.Price * o.Quantity
	if o.Filled > 0 {
		avg = cost / o.Filled
	}

	evOrder := gateway.Order{
		ID:               o.ID,
		Type:             gateway.LimitOrder,
		State:            mapOrderStateToCommon(o.Status),
		Side:             mapOrderSideToCommonSide(o.Side),
		Price:            o.Price,
		Amount:           o.Quantity,
		FilledAmount:     o.Filled,
		RemainingAmount:  o.Quantity - o.Filled,
		FilledMoneyValue: cost,
		Fee:              o.FeeAmount,
		AvgPrice:         avg,
	}

	// If the status of the order is different from "OPEN" or "PARTIALLY" then we can untrack the order.
	if o.Status != managerpbv1.OrderStatus_OPEN.String() &&
		o.Status != managerpbv1.OrderStatus_PARTIALLY.String() {
		g.orderTracking.UntrackOrder(orderID)
	}

	g.tickCh <- gateway.TickWithEvents(gateway.NewOrderUpdateEvent(evOrder))

	return nil
}

func mapOrderStateToCommon(st string) gateway.OrderState {
	switch st {
	case managerpbv1.OrderStatus_OPEN.String():
		return gateway.OrderOpen
	case managerpbv1.OrderStatus_PARTIALLY.String():
		return gateway.OrderPartiallyFilled
	case managerpbv1.OrderStatus_DONE.String():
		return gateway.OrderFullyFilled
	case managerpbv1.OrderStatus_CANCELED.String():
		return gateway.OrderCancelled
	default:
		return gateway.OrderUnknown
	}
}

func mapOrderSideToCommonSide(side managerpbv1.OrderSide) gateway.Side {
	if side == managerpbv1.OrderSide_BUY {
		return gateway.Bid
	} else if side == managerpbv1.OrderSide_SELL {
		return gateway.Ask
	} else {
		log.Printf("Klever invalid order side \"%s\"", side)
		return ""
	}
}

func mapOrderTypeToCommonType(orderType managerpbv1.OrderOperation) gateway.OrderType {
	switch orderType {
	case managerpbv1.OrderOperation_LIMIT:
		return gateway.LimitOrder
	case managerpbv1.OrderOperation_MARKET:
		return gateway.MarketOrder
	default:
		log.Printf("Klever invalid order type \"%s\"", orderType)
		return ""
	}
}
