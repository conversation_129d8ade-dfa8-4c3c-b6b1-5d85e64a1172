package klever

import (
	"context"
	"errors"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	managerpbv1 "github.com/herenow/atomic-gtw/integration/klever/api/manager"
	"github.com/herenow/atomic-gtw/utils"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	apiBase               = "manager-grpc.exchange.klever.io:443"
	defaultRequestTimeOut = time.Second * 5
)

type API struct {
	options gateway.Options
	client  *grpc.ClientConn
	api     managerpbv1.ManagerServiceClient

	symbolToIDMap  map[string]int32
	symbolMapMutex sync.RWMutex
}

func NewAPI(gtwOpt gateway.Options) *API {
	apiMiddleware := func(ctx context.Context, method string, req, reply any, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		// We don't want to pass apikey to depth
		// endpoint because we want all orders from market
		// passing our apikey will return only our orders.
		if method == managerpbv1.ManagerService_GetOrderGroups_FullMethodName {
			return invoker(ctx, method, req, reply, cc, opts...)
		}

		// Create a new context with the API key added to the metadata
		md := metadata.Pairs("apikey", gtwOpt.ApiKey)
		ctx = metadata.NewOutgoingContext(ctx, md)

		// Forward the request with the updated context
		return invoker(ctx, method, req, reply, cc, opts...)
	}

	cc, err := utils.NewGrpcClient(apiBase, utils.NewTLS(), apiMiddleware)
	if err != nil {
		log.Fatalf(fmt.Errorf("[%s] failed to start grpc client: [%w]", Exchange, err).Error())
	}

	api := &API{
		options: gtwOpt,
		client:  cc,
		api:     managerpbv1.NewManagerServiceClient(cc),
	}

	api.initializeSymbolMap()

	return api
}

// initializeSymbolMap fetches all symbols and map their id,
// so we can retrieve based on symbol instead symbol name
func (a *API) initializeSymbolMap() {
	a.symbolToIDMap = make(map[string]int32)
	a.symbolMapMutex = sync.RWMutex{}

	symbols, err := a.Symbols()
	if err != nil {
		log.Fatalf(fmt.Errorf("[%s] failed to load market symbols: [%w]", Exchange, err).Error())
	}

	a.symbolMapMutex.Lock()
	for _, s := range *symbols {
		a.symbolToIDMap[s.Name] = s.ID
	}
	defer a.symbolMapMutex.Unlock()

}

// getSymbolID safely retrieves the ID for a given symbol name
func (a *API) getSymbolID(symbol string) (int32, error) {
	a.symbolMapMutex.RLock()
	defer a.symbolMapMutex.RUnlock()

	id, found := a.symbolToIDMap[symbol]
	if !found {
		return 0, fmt.Errorf("symbol id from symbol [%s] not found in symbol to id map", symbol)
	}

	return id, nil
}

type apiError struct {
	Endpoint string
	Exchange string
	Err      error
}

func (e *apiError) Error() string {
	return fmt.Errorf("[%s] failed to request api [%s] error: %w", e.Exchange, e.Endpoint, e.Err).Error()
}

func (a *API) newAPIError(endpoint string, err error) *apiError {
	return &apiError{endpoint, Exchange.String(), err}
}

type Balance struct {
	Abbr       string
	Balance    float64
	Available  float64
	Locked     float64
	InOrder    float64
	InWithdraw float64
}

func (a *API) Balances() ([]Balance, error) {
	ctx, cancel := context.WithTimeout(context.Background(), defaultRequestTimeOut)
	defer cancel()

	res, err := a.api.GetBalances(ctx, &managerpbv1.BalancesRequest{})
	if err != nil {
		return nil, a.newAPIError("Balances", err)
	}

	balances := make([]Balance, len(res.GetBalances()))
	for i, balance := range res.GetBalances() {
		balances[i] = Balance{
			Abbr:       balance.GetAbbr(),
			Balance:    balance.GetBalance(),
			Available:  balance.GetAvailable(),
			Locked:     balance.GetLocked(),
			InOrder:    balance.GetInOrder(),
			InWithdraw: balance.GetInWithdraw(),
		}
	}

	return balances, nil
}

type PlaceOrderRequest struct {
	Symbol   string
	Side     managerpbv1.OrderSide
	Quantity float64
	Price    float64
}

func (a *API) PlaceOrder(req PlaceOrderRequest) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), defaultRequestTimeOut)
	defer cancel()

	res, err := a.api.LimitOrder(ctx, &managerpbv1.LimitOrderRequest{
		KeypairName: req.Symbol,
		Side:        req.Side,
		Quantity:    req.Quantity,
		Price:       req.Price,
	})
	if err != nil {
		return "", a.newAPIError("PlaceOrder", err)
	}

	return res.GetOrderID(), nil
}

func (a *API) CancelOrder(id, symbol string) error {
	ctx, cancel := context.WithTimeout(context.Background(), defaultRequestTimeOut)
	defer cancel()

	symbolID, err := a.getSymbolID(symbol)
	if err != nil {
		return a.newAPIError("CancelOrder", err)
	}

	if _, err = a.api.CancelOrder(ctx, &managerpbv1.CancelOrderRequest{
		OrderID:   id,
		KeypairID: symbolID,
	}); err != nil {
		return a.newAPIError("CancelOrder", err)
	}

	return nil
}

type Orderbook struct {
	Bids []gateway.PriceArray `json:"bids"`
	Asks []gateway.PriceArray `json:"asks"`
}

type Symbol struct {
	ID             int32
	Name           string
	BaseAbbr       string
	QuoteAbbr      string
	MinAmount      string
	BasePrecision  int32
	QuotePrecision int32
}

func (a *API) Symbols() (*[]Symbol, error) {
	ctx, cancel := context.WithTimeout(context.Background(), defaultRequestTimeOut)
	defer cancel()

	res, err := a.api.GetKeypairs(ctx, &managerpbv1.GetKeypairsRequest{})
	if err != nil {
		return nil, a.newAPIError("Symbols", err)
	}

	symbols := make([]Symbol, len(res.GetKeypairs()))
	for i, k := range res.GetKeypairs() {
		symbols[i] = Symbol{
			ID:             k.GetID(),
			Name:           k.GetName(),
			BaseAbbr:       k.GetBase().GetAbbr(),
			QuoteAbbr:      k.GetQuote().GetAbbr(),
			MinAmount:      k.GetMinAmount(),
			BasePrecision:  k.GetBasePrecision(),
			QuotePrecision: k.GetDecimals().GetMax(),
		}
	}

	return &symbols, nil
}

func (a *API) Orderbook(symbol string, limit int32, market gateway.Market) (*Orderbook, error) {
	ctx, cancel := context.WithTimeout(context.Background(), defaultRequestTimeOut)
	defer cancel()

	symbolID, err := a.getSymbolID(symbol)
	if err != nil {
		return nil, a.newAPIError("Orderbook", err)
	}

	res, err := a.api.GetOrderGroups(ctx, &managerpbv1.GetOrderGroupsRequest{
		KeypairID: symbolID,
		Status: []managerpbv1.OrderStatus{
			managerpbv1.OrderStatus_OPEN,
			managerpbv1.OrderStatus_PARTIALLY,
		},
		Decimals: int32(utils.TickPrecision(market.PriceTick)),
		Limit:    limit,
	})
	if err != nil {
		return nil, a.newAPIError("Orderbook", err)
	}

	bids := make([]gateway.PriceArray, len(res.GetBuyOrders()))
	for i, bid := range res.GetBuyOrders() {
		bids[i] = gateway.PriceArray{
			Price:  bid.GetPrice(),
			Amount: bid.GetQuantity(),
		}
	}

	asks := make([]gateway.PriceArray, len(res.GetSellOrders()))
	for i, ask := range res.GetSellOrders() {
		asks[i] = gateway.PriceArray{
			Price:  ask.GetPrice(),
			Amount: ask.GetQuantity(),
		}
	}

	return &Orderbook{
		Bids: bids,
		Asks: asks,
	}, nil
}

type Order struct {
	ID        string
	Symbol    string
	Status    string
	Side      managerpbv1.OrderSide
	Operation managerpbv1.OrderOperation
	Price     float64
	Quantity  float64
	Filled    float64
	FeeAmount float64
	FeeToken  string
	UpdatedAt *timestamppb.Timestamp
}

func (a *API) OpenOrders(symbol string) ([]Order, error) {
	ctx, cancel := context.WithTimeout(context.Background(), defaultRequestTimeOut)
	defer cancel()

	symbolID, err := a.getSymbolID(symbol)
	if err != nil {
		return nil, a.newAPIError("OpenOrders", err)
	}

	res, err := a.api.GetOrders(ctx, &managerpbv1.GetOrdersRequest{
		KeypairID: symbolID,
		Status: []managerpbv1.OrderStatus{
			managerpbv1.OrderStatus_OPEN,
			managerpbv1.OrderStatus_PARTIALLY,
		},
		Limit:         100,
		SortDirection: managerpbv1.SortOrderType_SORT_ORDER_TYPE_DESC,
		Sort:          managerpbv1.OrderSort_UPDATEDAT,
	})
	if err != nil {
		return nil, a.newAPIError("OpenOrders", err)
	}

	opOrders := make([]Order, len(res.GetOrders()))
	for i, o := range res.GetOrders() {
		opOrders[i] = Order{
			ID:        o.GetOrderID(),
			Status:    o.GetStatus(),
			Symbol:    o.GetKeypairName(),
			Side:      o.GetSide(),
			Operation: o.GetOperation(),
			Price:     o.GetPrice(),
			Quantity:  o.GetQuantity(),
			Filled:    o.GetFilled(),
			FeeAmount: o.GetFeeAmount(),
			FeeToken:  o.GetFeeInfo().GetTokenAbbr(),
			UpdatedAt: o.GetUpdatedAt(),
		}
	}

	return opOrders, nil
}

type OrderDetail struct {
	ID        string
	Symbol    string
	Status    string
	Side      managerpbv1.OrderSide
	Operation managerpbv1.OrderOperation
	Price     float64
	Quantity  float64
	Filled    float64
	FeeAmount float64
	FeeToken  string
}

func (a *API) OrderStatus(id string) (*OrderDetail, error) {
	ctx, cancel := context.WithTimeout(context.Background(), defaultRequestTimeOut)
	defer cancel()

	res, err := a.api.GetOrderDetails(ctx, &managerpbv1.GetOrderDetailsRequest{
		OrderID: id,
	})
	if err != nil {
		return nil, a.newAPIError("OrderStatus", err)
	}

	if len(res.GetDetails()) == 0 {
		return nil, a.newAPIError("OrderStatus", errors.New("order not found"))
	}

	o := res.GetDetails()[0]
	return &OrderDetail{
		ID:        o.GetOrderID(),
		Symbol:    o.GetKeypairName(),
		Operation: o.GetOperation(),
		Side:      o.GetSide(),
		Status:    o.GetStatus(),
		Price:     o.GetPrice(),
		Quantity:  o.GetQuantity(),
		Filled:    o.GetFilled(),
		FeeAmount: o.GetFeeAmount(),
	}, nil
}

type Fill struct {
	ID        int32
	OrderID   string
	Price     float64
	Quantity  float64
	Side      managerpbv1.OrderSide
	UpdatedAt int64
}

func (a *API) MarketFills(symbol string, skip int32) ([]Fill, error) {
	ctx, cancel := context.WithTimeout(context.Background(), defaultRequestTimeOut)
	defer cancel()

	symbolID, err := a.getSymbolID(symbol)
	if err != nil {
		return nil, a.newAPIError("MarketFills", err)
	}

	res, err := a.api.GetFills(ctx, &managerpbv1.GetFillsRequest{
		KeypairID: symbolID,
		Limit:     100,
		Skip:      skip,
	})
	if err != nil {
		return nil, a.newAPIError("MarketFills", err)
	}

	if len(res.GetFills()) == 0 {
		return nil, a.newAPIError("MarketFills", fmt.Errorf("fills not found"))
	}

	fills := make([]Fill, len(res.GetFills()))
	for i, f := range res.GetFills() {
		fills[i] = Fill{
			ID:        f.GetID(),
			OrderID:   f.GetOrderID(),
			Price:     f.GetPrice(),
			Quantity:  f.GetQuantity(),
			Side:      f.GetSide(),
			UpdatedAt: f.GetUpdatedAt().GetSeconds(),
		}
	}

	return fills, nil
}
