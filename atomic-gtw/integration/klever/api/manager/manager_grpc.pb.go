// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             (unknown)
// source: api/klever/manager/v1/manager.proto

package managerpbv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	ManagerService_GetKeypairs_FullMethodName                          = "/manager.ManagerService/GetKeypairs"
	ManagerService_GetNetworks_FullMethodName                          = "/manager.ManagerService/GetNetworks"
	ManagerService_GetOrderbookStatus_FullMethodName                   = "/manager.ManagerService/GetOrderbookStatus"
	ManagerService_GetTickers_FullMethodName                           = "/manager.ManagerService/GetTickers"
	ManagerService_GetBalances_FullMethodName                          = "/manager.ManagerService/GetBalances"
	ManagerService_GetTokens_FullMethodName                            = "/manager.ManagerService/GetTokens"
	ManagerService_GetOrders_FullMethodName                            = "/manager.ManagerService/GetOrders"
	ManagerService_GetStopOrders_FullMethodName                        = "/manager.ManagerService/GetStopOrders"
	ManagerService_GetOrderDetails_FullMethodName                      = "/manager.ManagerService/GetOrderDetails"
	ManagerService_GetOrderGroups_FullMethodName                       = "/manager.ManagerService/GetOrderGroups"
	ManagerService_LimitOrder_FullMethodName                           = "/manager.ManagerService/LimitOrder"
	ManagerService_StopLimitOrder_FullMethodName                       = "/manager.ManagerService/StopLimitOrder"
	ManagerService_MarketOrder_FullMethodName                          = "/manager.ManagerService/MarketOrder"
	ManagerService_CancelOrder_FullMethodName                          = "/manager.ManagerService/CancelOrder"
	ManagerService_CancelStopOrder_FullMethodName                      = "/manager.ManagerService/CancelStopOrder"
	ManagerService_HealthCheck_FullMethodName                          = "/manager.ManagerService/HealthCheck"
	ManagerService_ConfirmAddresses_FullMethodName                     = "/manager.ManagerService/ConfirmAddresses"
	ManagerService_CreateTotp_FullMethodName                           = "/manager.ManagerService/CreateTotp"
	ManagerService_ActivateTotp_FullMethodName                         = "/manager.ManagerService/ActivateTotp"
	ManagerService_ValidateTotpCode_FullMethodName                     = "/manager.ManagerService/ValidateTotpCode"
	ManagerService_DeactivateTotp_FullMethodName                       = "/manager.ManagerService/DeactivateTotp"
	ManagerService_GetTotpConfig_FullMethodName                        = "/manager.ManagerService/GetTotpConfig"
	ManagerService_GetDepositAddress_FullMethodName                    = "/manager.ManagerService/GetDepositAddress"
	ManagerService_GetFills_FullMethodName                             = "/manager.ManagerService/GetFills"
	ManagerService_GetDepositByID_FullMethodName                       = "/manager.ManagerService/GetDepositByID"
	ManagerService_GetDeposits_FullMethodName                          = "/manager.ManagerService/GetDeposits"
	ManagerService_GetWithdrawByID_FullMethodName                      = "/manager.ManagerService/GetWithdrawByID"
	ManagerService_GetWithdrawals_FullMethodName                       = "/manager.ManagerService/GetWithdrawals"
	ManagerService_GetUserHistory_FullMethodName                       = "/manager.ManagerService/GetUserHistory"
	ManagerService_GenerateAccount_FullMethodName                      = "/manager.ManagerService/GenerateAccount"
	ManagerService_GenerateAccountAuthHook_FullMethodName              = "/manager.ManagerService/GenerateAccountAuthHook"
	ManagerService_VerifyAuthHook_FullMethodName                       = "/manager.ManagerService/VerifyAuthHook"
	ManagerService_AddWithdraw_FullMethodName                          = "/manager.ManagerService/AddWithdraw"
	ManagerService_CalculateMarketPrice_FullMethodName                 = "/manager.ManagerService/CalculateMarketPrice"
	ManagerService_GetAccountLimitInfo_FullMethodName                  = "/manager.ManagerService/GetAccountLimitInfo"
	ManagerService_GenerateKey_FullMethodName                          = "/manager.ManagerService/GenerateKey"
	ManagerService_GetKeys_FullMethodName                              = "/manager.ManagerService/GetKeys"
	ManagerService_DeleteKey_FullMethodName                            = "/manager.ManagerService/DeleteKey"
	ManagerService_GetUser_FullMethodName                              = "/manager.ManagerService/GetUser"
	ManagerService_SetUserAddress_FullMethodName                       = "/manager.ManagerService/SetUserAddress"
	ManagerService_GetKycStatus_FullMethodName                         = "/manager.ManagerService/GetKycStatus"
	ManagerService_UpdateTransactionKycStatus_FullMethodName           = "/manager.ManagerService/UpdateTransactionKycStatus"
	ManagerService_CancelAllOrders_FullMethodName                      = "/manager.ManagerService/CancelAllOrders"
	ManagerService_CancelAllStopOrders_FullMethodName                  = "/manager.ManagerService/CancelAllStopOrders"
	ManagerService_InitiateKyc_FullMethodName                          = "/manager.ManagerService/InitiateKyc"
	ManagerService_GetVipLevelTable_FullMethodName                     = "/manager.ManagerService/GetVipLevelTable"
	ManagerService_GetUserVipLevel_FullMethodName                      = "/manager.ManagerService/GetUserVipLevel"
	ManagerService_GetKeypairsCard_FullMethodName                      = "/manager.ManagerService/GetKeypairsCard"
	ManagerService_AddIP_FullMethodName                                = "/manager.ManagerService/AddIP"
	ManagerService_GetIPList_FullMethodName                            = "/manager.ManagerService/GetIPList"
	ManagerService_DeleteIP_FullMethodName                             = "/manager.ManagerService/DeleteIP"
	ManagerService_ActivateIPProtection_FullMethodName                 = "/manager.ManagerService/ActivateIPProtection"
	ManagerService_ConvertDust_FullMethodName                          = "/manager.ManagerService/ConvertDust"
	ManagerService_SetUserSettings_FullMethodName                      = "/manager.ManagerService/SetUserSettings"
	ManagerService_EstimatedConvertDust_FullMethodName                 = "/manager.ManagerService/EstimatedConvertDust"
	ManagerService_GetUserConversion_FullMethodName                    = "/manager.ManagerService/GetUserConversion"
	ManagerService_GetConvertDustHistory_FullMethodName                = "/manager.ManagerService/GetConvertDustHistory"
	ManagerService_GetLiquidityPoolEstimatedQuoteAmount_FullMethodName = "/manager.ManagerService/GetLiquidityPoolEstimatedQuoteAmount"
	ManagerService_AddLiquidity_FullMethodName                         = "/manager.ManagerService/AddLiquidity"
	ManagerService_GetLiquidities_FullMethodName                       = "/manager.ManagerService/GetLiquidities"
	ManagerService_GetLiquidityPools_FullMethodName                    = "/manager.ManagerService/GetLiquidityPools"
	ManagerService_ClaimRewards_FullMethodName                         = "/manager.ManagerService/ClaimRewards"
	ManagerService_RemoveLiquidity_FullMethodName                      = "/manager.ManagerService/RemoveLiquidity"
	ManagerService_GetClaimHistory_FullMethodName                      = "/manager.ManagerService/GetClaimHistory"
	ManagerService_GetLPHistory_FullMethodName                         = "/manager.ManagerService/GetLPHistory"
	ManagerService_CreateUserTicket_FullMethodName                     = "/manager.ManagerService/CreateUserTicket"
	ManagerService_DeleteUserAccount_FullMethodName                    = "/manager.ManagerService/DeleteUserAccount"
	ManagerService_StopMarketOrder_FullMethodName                      = "/manager.ManagerService/StopMarketOrder"
	ManagerService_GetTopTraders_FullMethodName                        = "/manager.ManagerService/GetTopTraders"
	ManagerService_ListWalletAddresses_FullMethodName                  = "/manager.ManagerService/ListWalletAddresses"
	ManagerService_ListExchangeWallets_FullMethodName                  = "/manager.ManagerService/ListExchangeWallets"
	ManagerService_ListExchangeBalances_FullMethodName                 = "/manager.ManagerService/ListExchangeBalances"
)

// ManagerServiceClient is the client API for ManagerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ManagerServiceClient interface {
	// Returns a collection of trading pairs.
	GetKeypairs(ctx context.Context, in *GetKeypairsRequest, opts ...grpc.CallOption) (*GetKeypairsResponse, error)
	// Fetches Blockchain networks.
	GetNetworks(ctx context.Context, in *GetNetworksRequest, opts ...grpc.CallOption) (*GetNetworksResponse, error)
	// Returns Order book data for a given trading pair.
	GetOrderbookStatus(ctx context.Context, in *OrderbookStatusRequest, opts ...grpc.CallOption) (*OrderbookStatusResponse, error)
	// Returns a report with transaction and price data.
	GetTickers(ctx context.Context, in *TickersRequest, opts ...grpc.CallOption) (*TickersResponse, error)
	// Retrieves balances.
	GetBalances(ctx context.Context, in *BalancesRequest, opts ...grpc.CallOption) (*BalancesResponse, error)
	// Returns information about tokens.
	GetTokens(ctx context.Context, in *GetTokensRequest, opts ...grpc.CallOption) (*GetTokensResponse, error)
	// Returns orders.
	GetOrders(ctx context.Context, in *GetOrdersRequest, opts ...grpc.CallOption) (*GetOrdersResponse, error)
	// Returns stop orders.
	GetStopOrders(ctx context.Context, in *GetStopOrdersRequest, opts ...grpc.CallOption) (*GetStopOrdersResponse, error)
	// Shows details of a particular order.
	GetOrderDetails(ctx context.Context, in *GetOrderDetailsRequest, opts ...grpc.CallOption) (*GetOrderDetailsResponse, error)
	// Used for order grouping.
	GetOrderGroups(ctx context.Context, in *GetOrderGroupsRequest, opts ...grpc.CallOption) (*GetOrderGroupsResponse, error)
	// Places a limit order.
	LimitOrder(ctx context.Context, in *LimitOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	// Places a stop limit order.
	StopLimitOrder(ctx context.Context, in *StopLimitOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	// Places a market order.
	MarketOrder(ctx context.Context, in *MarketOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	// Cancels an order.
	CancelOrder(ctx context.Context, in *CancelOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	// Cancels an stop order.
	CancelStopOrder(ctx context.Context, in *CancelOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	// Checks application health.
	HealthCheck(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HealthCheckResponse, error)
	// Confirms if an address belongs to our exchange.
	ConfirmAddresses(ctx context.Context, in *ConfirmAddressesRequest, opts ...grpc.CallOption) (*ConfirmAddressesResponse, error)
	// Creates TOTP.
	CreateTotp(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*CreateTotpResponse, error)
	// Activates TOTP.
	ActivateTotp(ctx context.Context, in *ActivateTotpRequest, opts ...grpc.CallOption) (*ActivateTotpResponse, error)
	// Validates TOTP code.
	ValidateTotpCode(ctx context.Context, in *ValidateTotpRequest, opts ...grpc.CallOption) (*ValidateTotpResponse, error)
	// Deactivates TOTP.
	DeactivateTotp(ctx context.Context, in *DeactivateTotpRequest, opts ...grpc.CallOption) (*DeactivateTotpResponse, error)
	// Returns TOTP settings.
	GetTotpConfig(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetTotpConfigResponse, error)
	// Returns deposit addresses from all networks.
	GetDepositAddress(ctx context.Context, in *GetDepositAddressRequest, opts ...grpc.CallOption) (*GetDepositAddressResponse, error)
	// Returns order fills.
	GetFills(ctx context.Context, in *GetFillsRequest, opts ...grpc.CallOption) (*GetFillsResponse, error)
	// Returns deposits by Deposit ID.
	GetDepositByID(ctx context.Context, in *DepositRequest, opts ...grpc.CallOption) (*DepositResponse, error)
	// Returns deposits.
	GetDeposits(ctx context.Context, in *DepositsRequest, opts ...grpc.CallOption) (*DepositsResponse, error)
	// Returns withdrawals by ID.
	GetWithdrawByID(ctx context.Context, in *WithdrawRequest, opts ...grpc.CallOption) (*WithdrawResponse, error)
	// Returns withdrawals.
	GetWithdrawals(ctx context.Context, in *WithdrawalsRequest, opts ...grpc.CallOption) (*WithdrawalsResponse, error)
	// Returns the history of deposit and withdraw transactions.
	GetUserHistory(ctx context.Context, in *HistoryRequest, opts ...grpc.CallOption) (*HistoryResponse, error)
	// Deprecated. Previously used with Firebase.
	GenerateAccount(ctx context.Context, in *GenerateAccountRequest, opts ...grpc.CallOption) (*GenerateAccountResponse, error)
	// After the users log into Exchange, their account is validated and this call is used to create the users account in our database, for each blockchain network.
	GenerateAccountAuthHook(ctx context.Context, in *GenerateAccountHookRequest, opts ...grpc.CallOption) (*GenerateAccountResponse, error)
	// Initial verification of the external service endpoint.
	VerifyAuthHook(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*VerifyAuthHookResponse, error)
	// Adds Withdrawals.
	AddWithdraw(ctx context.Context, in *AddWithdrawRequest, opts ...grpc.CallOption) (*AddWithdrawResponse, error)
	// Calculates market price.
	CalculateMarketPrice(ctx context.Context, in *CalcMarketRequest, opts ...grpc.CallOption) (*CalcMarketResponse, error)
	// Returns the transaction limit value for a give account.
	GetAccountLimitInfo(ctx context.Context, in *GetAccountLimitInfoRequest, opts ...grpc.CallOption) (*GetAccountLimitInfoResponse, error)
	// Generates an API key to establish communication between services.
	GenerateKey(ctx context.Context, in *GenerateKeyRequest, opts ...grpc.CallOption) (*GenerateKeyResponse, error)
	// Returns API keys of a user.
	GetKeys(ctx context.Context, in *GetKeysRequest, opts ...grpc.CallOption) (*GetKeysResponse, error)
	// Deletes API keys.
	DeleteKey(ctx context.Context, in *DeleteKeyRequest, opts ...grpc.CallOption) (*DeleteKeyResponse, error)
	// Retrieves user data.
	GetUser(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetUserResponse, error)
	// Defines user address.
	SetUserAddress(ctx context.Context, in *SetUserAddressRequest, opts ...grpc.CallOption) (*SetUserAddressResponse, error)
	// Returns KYC Status.
	GetKycStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetKycStatusResponse, error)
	UpdateTransactionKycStatus(ctx context.Context, in *UpdateKycStatusRequest, opts ...grpc.CallOption) (*UpdateKycStatusResponse, error)
	// Cancel all orders for a user.
	CancelAllOrders(ctx context.Context, in *CancelAllOrdersRequest, opts ...grpc.CallOption) (*CancelAllOrdersResponse, error)
	// Cancel all stop orders for a user.
	CancelAllStopOrders(ctx context.Context, in *CancelAllOrdersRequest, opts ...grpc.CallOption) (*CancelAllOrdersResponse, error)
	// Initiates KYC.
	InitiateKyc(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*InitiateKycResponse, error)
	// Shows VIP Level Table.
	GetVipLevelTable(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*VipLevelTableResponse, error)
	// Returns the VIP level of a given user.
	GetUserVipLevel(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*UserVipLevelResponse, error)
	// Returns a collection of trading pairs with tickers.
	GetKeypairsCard(ctx context.Context, in *GetKeypairsCardRequest, opts ...grpc.CallOption) (*GetKeypairsCardResponse, error)
	AddIP(ctx context.Context, in *AddIPRequest, opts ...grpc.CallOption) (*AddIPResponse, error)
	GetIPList(ctx context.Context, in *GetIPListRequest, opts ...grpc.CallOption) (*GetIPListResponse, error)
	DeleteIP(ctx context.Context, in *DeleteIPRequest, opts ...grpc.CallOption) (*DeleteIPResponse, error)
	ActivateIPProtection(ctx context.Context, in *ActivateIPProtectionRequest, opts ...grpc.CallOption) (*ActivateIPProtectionResponse, error)
	ConvertDust(ctx context.Context, in *ConvertDustRequest, opts ...grpc.CallOption) (*ConvertDustResponse, error)
	SetUserSettings(ctx context.Context, in *SetUserSettingsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	EstimatedConvertDust(ctx context.Context, in *EstimatedConvertDustRequest, opts ...grpc.CallOption) (*EstimatedConvertDustResponse, error)
	GetUserConversion(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetUserConversionResponse, error)
	// Returns ConvertDust Orders.
	GetConvertDustHistory(ctx context.Context, in *GetConvertDustHistoryRequest, opts ...grpc.CallOption) (*GetOrdersResponse, error)
	GetLiquidityPoolEstimatedQuoteAmount(ctx context.Context, in *GetLiquidityPoolEstimatedQuoteRequest, opts ...grpc.CallOption) (*GetLiquidityPoolEstimatedQuoteResponse, error)
	AddLiquidity(ctx context.Context, in *AddLiquidityRequest, opts ...grpc.CallOption) (*AddLiquidityResponse, error)
	GetLiquidities(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetLiquiditiesResponse, error)
	GetLiquidityPools(ctx context.Context, in *GetLiquidityPoolsRequest, opts ...grpc.CallOption) (*GetLiquidityPoolsResponse, error)
	ClaimRewards(ctx context.Context, in *ClaimRewardsRequest, opts ...grpc.CallOption) (*ClaimRewardsResponse, error)
	RemoveLiquidity(ctx context.Context, in *RemoveLiquidityRequest, opts ...grpc.CallOption) (*RemoveLiquidityResponse, error)
	GetClaimHistory(ctx context.Context, in *GetClaimHistoryRequest, opts ...grpc.CallOption) (*GetClaimHistoryResponse, error)
	GetLPHistory(ctx context.Context, in *GetLPHistoryRequest, opts ...grpc.CallOption) (*GetLPHistoryResponse, error)
	CreateUserTicket(ctx context.Context, in *CreateUserTicketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	DeleteUserAccount(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*DeleteUserAccountResponse, error)
	// StopMarketOrder places a stop market order by given StopMarketOrderRequest and returns a OrderResponse.
	StopMarketOrder(ctx context.Context, in *StopMarketOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
	// GetTopTraders is used to rank the winners from trading competitions
	GetTopTraders(ctx context.Context, in *GetTopTradersRequest, opts ...grpc.CallOption) (*GetTopTradersResponse, error)
	// ListWalletAddresses list all users wallets adresseses created by exchange
	ListWalletAddresses(ctx context.Context, in *ListWalletAddressesRequest, opts ...grpc.CallOption) (*ListWalletAddressesResponse, error)
	// ListExchangeWallets list all exchange wallets
	ListExchangeWallets(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListExchangeWalletsResponse, error)
	// ListExchangeBalances list all token balances in custody of the exchange
	ListExchangeBalances(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListExchangeBalancesResponse, error)
}

type managerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewManagerServiceClient(cc grpc.ClientConnInterface) ManagerServiceClient {
	return &managerServiceClient{cc}
}

func (c *managerServiceClient) GetKeypairs(ctx context.Context, in *GetKeypairsRequest, opts ...grpc.CallOption) (*GetKeypairsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetKeypairsResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetKeypairs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetNetworks(ctx context.Context, in *GetNetworksRequest, opts ...grpc.CallOption) (*GetNetworksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetNetworksResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetNetworks_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetOrderbookStatus(ctx context.Context, in *OrderbookStatusRequest, opts ...grpc.CallOption) (*OrderbookStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderbookStatusResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetOrderbookStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetTickers(ctx context.Context, in *TickersRequest, opts ...grpc.CallOption) (*TickersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TickersResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetTickers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetBalances(ctx context.Context, in *BalancesRequest, opts ...grpc.CallOption) (*BalancesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BalancesResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetBalances_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetTokens(ctx context.Context, in *GetTokensRequest, opts ...grpc.CallOption) (*GetTokensResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTokensResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetTokens_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetOrders(ctx context.Context, in *GetOrdersRequest, opts ...grpc.CallOption) (*GetOrdersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOrdersResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetOrders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetStopOrders(ctx context.Context, in *GetStopOrdersRequest, opts ...grpc.CallOption) (*GetStopOrdersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetStopOrdersResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetStopOrders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetOrderDetails(ctx context.Context, in *GetOrderDetailsRequest, opts ...grpc.CallOption) (*GetOrderDetailsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOrderDetailsResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetOrderDetails_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetOrderGroups(ctx context.Context, in *GetOrderGroupsRequest, opts ...grpc.CallOption) (*GetOrderGroupsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOrderGroupsResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetOrderGroups_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) LimitOrder(ctx context.Context, in *LimitOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, ManagerService_LimitOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) StopLimitOrder(ctx context.Context, in *StopLimitOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, ManagerService_StopLimitOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) MarketOrder(ctx context.Context, in *MarketOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, ManagerService_MarketOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) CancelOrder(ctx context.Context, in *CancelOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, ManagerService_CancelOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) CancelStopOrder(ctx context.Context, in *CancelOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, ManagerService_CancelStopOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) HealthCheck(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HealthCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResponse)
	err := c.cc.Invoke(ctx, ManagerService_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) ConfirmAddresses(ctx context.Context, in *ConfirmAddressesRequest, opts ...grpc.CallOption) (*ConfirmAddressesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConfirmAddressesResponse)
	err := c.cc.Invoke(ctx, ManagerService_ConfirmAddresses_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) CreateTotp(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*CreateTotpResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTotpResponse)
	err := c.cc.Invoke(ctx, ManagerService_CreateTotp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) ActivateTotp(ctx context.Context, in *ActivateTotpRequest, opts ...grpc.CallOption) (*ActivateTotpResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActivateTotpResponse)
	err := c.cc.Invoke(ctx, ManagerService_ActivateTotp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) ValidateTotpCode(ctx context.Context, in *ValidateTotpRequest, opts ...grpc.CallOption) (*ValidateTotpResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ValidateTotpResponse)
	err := c.cc.Invoke(ctx, ManagerService_ValidateTotpCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) DeactivateTotp(ctx context.Context, in *DeactivateTotpRequest, opts ...grpc.CallOption) (*DeactivateTotpResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeactivateTotpResponse)
	err := c.cc.Invoke(ctx, ManagerService_DeactivateTotp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetTotpConfig(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetTotpConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTotpConfigResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetTotpConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetDepositAddress(ctx context.Context, in *GetDepositAddressRequest, opts ...grpc.CallOption) (*GetDepositAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDepositAddressResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetDepositAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetFills(ctx context.Context, in *GetFillsRequest, opts ...grpc.CallOption) (*GetFillsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFillsResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetFills_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetDepositByID(ctx context.Context, in *DepositRequest, opts ...grpc.CallOption) (*DepositResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DepositResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetDepositByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetDeposits(ctx context.Context, in *DepositsRequest, opts ...grpc.CallOption) (*DepositsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DepositsResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetDeposits_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetWithdrawByID(ctx context.Context, in *WithdrawRequest, opts ...grpc.CallOption) (*WithdrawResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WithdrawResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetWithdrawByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetWithdrawals(ctx context.Context, in *WithdrawalsRequest, opts ...grpc.CallOption) (*WithdrawalsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WithdrawalsResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetWithdrawals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetUserHistory(ctx context.Context, in *HistoryRequest, opts ...grpc.CallOption) (*HistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HistoryResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetUserHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GenerateAccount(ctx context.Context, in *GenerateAccountRequest, opts ...grpc.CallOption) (*GenerateAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateAccountResponse)
	err := c.cc.Invoke(ctx, ManagerService_GenerateAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GenerateAccountAuthHook(ctx context.Context, in *GenerateAccountHookRequest, opts ...grpc.CallOption) (*GenerateAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateAccountResponse)
	err := c.cc.Invoke(ctx, ManagerService_GenerateAccountAuthHook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) VerifyAuthHook(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*VerifyAuthHookResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifyAuthHookResponse)
	err := c.cc.Invoke(ctx, ManagerService_VerifyAuthHook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) AddWithdraw(ctx context.Context, in *AddWithdrawRequest, opts ...grpc.CallOption) (*AddWithdrawResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddWithdrawResponse)
	err := c.cc.Invoke(ctx, ManagerService_AddWithdraw_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) CalculateMarketPrice(ctx context.Context, in *CalcMarketRequest, opts ...grpc.CallOption) (*CalcMarketResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CalcMarketResponse)
	err := c.cc.Invoke(ctx, ManagerService_CalculateMarketPrice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetAccountLimitInfo(ctx context.Context, in *GetAccountLimitInfoRequest, opts ...grpc.CallOption) (*GetAccountLimitInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountLimitInfoResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetAccountLimitInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GenerateKey(ctx context.Context, in *GenerateKeyRequest, opts ...grpc.CallOption) (*GenerateKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateKeyResponse)
	err := c.cc.Invoke(ctx, ManagerService_GenerateKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetKeys(ctx context.Context, in *GetKeysRequest, opts ...grpc.CallOption) (*GetKeysResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetKeysResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetKeys_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) DeleteKey(ctx context.Context, in *DeleteKeyRequest, opts ...grpc.CallOption) (*DeleteKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteKeyResponse)
	err := c.cc.Invoke(ctx, ManagerService_DeleteKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetUser(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetUserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) SetUserAddress(ctx context.Context, in *SetUserAddressRequest, opts ...grpc.CallOption) (*SetUserAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetUserAddressResponse)
	err := c.cc.Invoke(ctx, ManagerService_SetUserAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetKycStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetKycStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetKycStatusResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetKycStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) UpdateTransactionKycStatus(ctx context.Context, in *UpdateKycStatusRequest, opts ...grpc.CallOption) (*UpdateKycStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateKycStatusResponse)
	err := c.cc.Invoke(ctx, ManagerService_UpdateTransactionKycStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) CancelAllOrders(ctx context.Context, in *CancelAllOrdersRequest, opts ...grpc.CallOption) (*CancelAllOrdersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelAllOrdersResponse)
	err := c.cc.Invoke(ctx, ManagerService_CancelAllOrders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) CancelAllStopOrders(ctx context.Context, in *CancelAllOrdersRequest, opts ...grpc.CallOption) (*CancelAllOrdersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelAllOrdersResponse)
	err := c.cc.Invoke(ctx, ManagerService_CancelAllStopOrders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) InitiateKyc(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*InitiateKycResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InitiateKycResponse)
	err := c.cc.Invoke(ctx, ManagerService_InitiateKyc_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetVipLevelTable(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*VipLevelTableResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VipLevelTableResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetVipLevelTable_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetUserVipLevel(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*UserVipLevelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserVipLevelResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetUserVipLevel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetKeypairsCard(ctx context.Context, in *GetKeypairsCardRequest, opts ...grpc.CallOption) (*GetKeypairsCardResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetKeypairsCardResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetKeypairsCard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) AddIP(ctx context.Context, in *AddIPRequest, opts ...grpc.CallOption) (*AddIPResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddIPResponse)
	err := c.cc.Invoke(ctx, ManagerService_AddIP_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetIPList(ctx context.Context, in *GetIPListRequest, opts ...grpc.CallOption) (*GetIPListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIPListResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetIPList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) DeleteIP(ctx context.Context, in *DeleteIPRequest, opts ...grpc.CallOption) (*DeleteIPResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteIPResponse)
	err := c.cc.Invoke(ctx, ManagerService_DeleteIP_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) ActivateIPProtection(ctx context.Context, in *ActivateIPProtectionRequest, opts ...grpc.CallOption) (*ActivateIPProtectionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActivateIPProtectionResponse)
	err := c.cc.Invoke(ctx, ManagerService_ActivateIPProtection_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) ConvertDust(ctx context.Context, in *ConvertDustRequest, opts ...grpc.CallOption) (*ConvertDustResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConvertDustResponse)
	err := c.cc.Invoke(ctx, ManagerService_ConvertDust_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) SetUserSettings(ctx context.Context, in *SetUserSettingsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ManagerService_SetUserSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) EstimatedConvertDust(ctx context.Context, in *EstimatedConvertDustRequest, opts ...grpc.CallOption) (*EstimatedConvertDustResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EstimatedConvertDustResponse)
	err := c.cc.Invoke(ctx, ManagerService_EstimatedConvertDust_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetUserConversion(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetUserConversionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserConversionResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetUserConversion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetConvertDustHistory(ctx context.Context, in *GetConvertDustHistoryRequest, opts ...grpc.CallOption) (*GetOrdersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOrdersResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetConvertDustHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetLiquidityPoolEstimatedQuoteAmount(ctx context.Context, in *GetLiquidityPoolEstimatedQuoteRequest, opts ...grpc.CallOption) (*GetLiquidityPoolEstimatedQuoteResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLiquidityPoolEstimatedQuoteResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetLiquidityPoolEstimatedQuoteAmount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) AddLiquidity(ctx context.Context, in *AddLiquidityRequest, opts ...grpc.CallOption) (*AddLiquidityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddLiquidityResponse)
	err := c.cc.Invoke(ctx, ManagerService_AddLiquidity_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetLiquidities(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GetLiquiditiesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLiquiditiesResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetLiquidities_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetLiquidityPools(ctx context.Context, in *GetLiquidityPoolsRequest, opts ...grpc.CallOption) (*GetLiquidityPoolsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLiquidityPoolsResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetLiquidityPools_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) ClaimRewards(ctx context.Context, in *ClaimRewardsRequest, opts ...grpc.CallOption) (*ClaimRewardsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ClaimRewardsResponse)
	err := c.cc.Invoke(ctx, ManagerService_ClaimRewards_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) RemoveLiquidity(ctx context.Context, in *RemoveLiquidityRequest, opts ...grpc.CallOption) (*RemoveLiquidityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveLiquidityResponse)
	err := c.cc.Invoke(ctx, ManagerService_RemoveLiquidity_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetClaimHistory(ctx context.Context, in *GetClaimHistoryRequest, opts ...grpc.CallOption) (*GetClaimHistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClaimHistoryResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetClaimHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetLPHistory(ctx context.Context, in *GetLPHistoryRequest, opts ...grpc.CallOption) (*GetLPHistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLPHistoryResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetLPHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) CreateUserTicket(ctx context.Context, in *CreateUserTicketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ManagerService_CreateUserTicket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) DeleteUserAccount(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*DeleteUserAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteUserAccountResponse)
	err := c.cc.Invoke(ctx, ManagerService_DeleteUserAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) StopMarketOrder(ctx context.Context, in *StopMarketOrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, ManagerService_StopMarketOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) GetTopTraders(ctx context.Context, in *GetTopTradersRequest, opts ...grpc.CallOption) (*GetTopTradersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTopTradersResponse)
	err := c.cc.Invoke(ctx, ManagerService_GetTopTraders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) ListWalletAddresses(ctx context.Context, in *ListWalletAddressesRequest, opts ...grpc.CallOption) (*ListWalletAddressesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListWalletAddressesResponse)
	err := c.cc.Invoke(ctx, ManagerService_ListWalletAddresses_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) ListExchangeWallets(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListExchangeWalletsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListExchangeWalletsResponse)
	err := c.cc.Invoke(ctx, ManagerService_ListExchangeWallets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *managerServiceClient) ListExchangeBalances(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListExchangeBalancesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListExchangeBalancesResponse)
	err := c.cc.Invoke(ctx, ManagerService_ListExchangeBalances_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ManagerServiceServer is the server API for ManagerService service.
// All implementations must embed UnimplementedManagerServiceServer
// for forward compatibility
type ManagerServiceServer interface {
	// Returns a collection of trading pairs.
	GetKeypairs(context.Context, *GetKeypairsRequest) (*GetKeypairsResponse, error)
	// Fetches Blockchain networks.
	GetNetworks(context.Context, *GetNetworksRequest) (*GetNetworksResponse, error)
	// Returns Order book data for a given trading pair.
	GetOrderbookStatus(context.Context, *OrderbookStatusRequest) (*OrderbookStatusResponse, error)
	// Returns a report with transaction and price data.
	GetTickers(context.Context, *TickersRequest) (*TickersResponse, error)
	// Retrieves balances.
	GetBalances(context.Context, *BalancesRequest) (*BalancesResponse, error)
	// Returns information about tokens.
	GetTokens(context.Context, *GetTokensRequest) (*GetTokensResponse, error)
	// Returns orders.
	GetOrders(context.Context, *GetOrdersRequest) (*GetOrdersResponse, error)
	// Returns stop orders.
	GetStopOrders(context.Context, *GetStopOrdersRequest) (*GetStopOrdersResponse, error)
	// Shows details of a particular order.
	GetOrderDetails(context.Context, *GetOrderDetailsRequest) (*GetOrderDetailsResponse, error)
	// Used for order grouping.
	GetOrderGroups(context.Context, *GetOrderGroupsRequest) (*GetOrderGroupsResponse, error)
	// Places a limit order.
	LimitOrder(context.Context, *LimitOrderRequest) (*OrderResponse, error)
	// Places a stop limit order.
	StopLimitOrder(context.Context, *StopLimitOrderRequest) (*OrderResponse, error)
	// Places a market order.
	MarketOrder(context.Context, *MarketOrderRequest) (*OrderResponse, error)
	// Cancels an order.
	CancelOrder(context.Context, *CancelOrderRequest) (*OrderResponse, error)
	// Cancels an stop order.
	CancelStopOrder(context.Context, *CancelOrderRequest) (*OrderResponse, error)
	// Checks application health.
	HealthCheck(context.Context, *emptypb.Empty) (*HealthCheckResponse, error)
	// Confirms if an address belongs to our exchange.
	ConfirmAddresses(context.Context, *ConfirmAddressesRequest) (*ConfirmAddressesResponse, error)
	// Creates TOTP.
	CreateTotp(context.Context, *emptypb.Empty) (*CreateTotpResponse, error)
	// Activates TOTP.
	ActivateTotp(context.Context, *ActivateTotpRequest) (*ActivateTotpResponse, error)
	// Validates TOTP code.
	ValidateTotpCode(context.Context, *ValidateTotpRequest) (*ValidateTotpResponse, error)
	// Deactivates TOTP.
	DeactivateTotp(context.Context, *DeactivateTotpRequest) (*DeactivateTotpResponse, error)
	// Returns TOTP settings.
	GetTotpConfig(context.Context, *emptypb.Empty) (*GetTotpConfigResponse, error)
	// Returns deposit addresses from all networks.
	GetDepositAddress(context.Context, *GetDepositAddressRequest) (*GetDepositAddressResponse, error)
	// Returns order fills.
	GetFills(context.Context, *GetFillsRequest) (*GetFillsResponse, error)
	// Returns deposits by Deposit ID.
	GetDepositByID(context.Context, *DepositRequest) (*DepositResponse, error)
	// Returns deposits.
	GetDeposits(context.Context, *DepositsRequest) (*DepositsResponse, error)
	// Returns withdrawals by ID.
	GetWithdrawByID(context.Context, *WithdrawRequest) (*WithdrawResponse, error)
	// Returns withdrawals.
	GetWithdrawals(context.Context, *WithdrawalsRequest) (*WithdrawalsResponse, error)
	// Returns the history of deposit and withdraw transactions.
	GetUserHistory(context.Context, *HistoryRequest) (*HistoryResponse, error)
	// Deprecated. Previously used with Firebase.
	GenerateAccount(context.Context, *GenerateAccountRequest) (*GenerateAccountResponse, error)
	// After the users log into Exchange, their account is validated and this call is used to create the users account in our database, for each blockchain network.
	GenerateAccountAuthHook(context.Context, *GenerateAccountHookRequest) (*GenerateAccountResponse, error)
	// Initial verification of the external service endpoint.
	VerifyAuthHook(context.Context, *emptypb.Empty) (*VerifyAuthHookResponse, error)
	// Adds Withdrawals.
	AddWithdraw(context.Context, *AddWithdrawRequest) (*AddWithdrawResponse, error)
	// Calculates market price.
	CalculateMarketPrice(context.Context, *CalcMarketRequest) (*CalcMarketResponse, error)
	// Returns the transaction limit value for a give account.
	GetAccountLimitInfo(context.Context, *GetAccountLimitInfoRequest) (*GetAccountLimitInfoResponse, error)
	// Generates an API key to establish communication between services.
	GenerateKey(context.Context, *GenerateKeyRequest) (*GenerateKeyResponse, error)
	// Returns API keys of a user.
	GetKeys(context.Context, *GetKeysRequest) (*GetKeysResponse, error)
	// Deletes API keys.
	DeleteKey(context.Context, *DeleteKeyRequest) (*DeleteKeyResponse, error)
	// Retrieves user data.
	GetUser(context.Context, *emptypb.Empty) (*GetUserResponse, error)
	// Defines user address.
	SetUserAddress(context.Context, *SetUserAddressRequest) (*SetUserAddressResponse, error)
	// Returns KYC Status.
	GetKycStatus(context.Context, *emptypb.Empty) (*GetKycStatusResponse, error)
	UpdateTransactionKycStatus(context.Context, *UpdateKycStatusRequest) (*UpdateKycStatusResponse, error)
	// Cancel all orders for a user.
	CancelAllOrders(context.Context, *CancelAllOrdersRequest) (*CancelAllOrdersResponse, error)
	// Cancel all stop orders for a user.
	CancelAllStopOrders(context.Context, *CancelAllOrdersRequest) (*CancelAllOrdersResponse, error)
	// Initiates KYC.
	InitiateKyc(context.Context, *emptypb.Empty) (*InitiateKycResponse, error)
	// Shows VIP Level Table.
	GetVipLevelTable(context.Context, *emptypb.Empty) (*VipLevelTableResponse, error)
	// Returns the VIP level of a given user.
	GetUserVipLevel(context.Context, *emptypb.Empty) (*UserVipLevelResponse, error)
	// Returns a collection of trading pairs with tickers.
	GetKeypairsCard(context.Context, *GetKeypairsCardRequest) (*GetKeypairsCardResponse, error)
	AddIP(context.Context, *AddIPRequest) (*AddIPResponse, error)
	GetIPList(context.Context, *GetIPListRequest) (*GetIPListResponse, error)
	DeleteIP(context.Context, *DeleteIPRequest) (*DeleteIPResponse, error)
	ActivateIPProtection(context.Context, *ActivateIPProtectionRequest) (*ActivateIPProtectionResponse, error)
	ConvertDust(context.Context, *ConvertDustRequest) (*ConvertDustResponse, error)
	SetUserSettings(context.Context, *SetUserSettingsRequest) (*emptypb.Empty, error)
	EstimatedConvertDust(context.Context, *EstimatedConvertDustRequest) (*EstimatedConvertDustResponse, error)
	GetUserConversion(context.Context, *emptypb.Empty) (*GetUserConversionResponse, error)
	// Returns ConvertDust Orders.
	GetConvertDustHistory(context.Context, *GetConvertDustHistoryRequest) (*GetOrdersResponse, error)
	GetLiquidityPoolEstimatedQuoteAmount(context.Context, *GetLiquidityPoolEstimatedQuoteRequest) (*GetLiquidityPoolEstimatedQuoteResponse, error)
	AddLiquidity(context.Context, *AddLiquidityRequest) (*AddLiquidityResponse, error)
	GetLiquidities(context.Context, *emptypb.Empty) (*GetLiquiditiesResponse, error)
	GetLiquidityPools(context.Context, *GetLiquidityPoolsRequest) (*GetLiquidityPoolsResponse, error)
	ClaimRewards(context.Context, *ClaimRewardsRequest) (*ClaimRewardsResponse, error)
	RemoveLiquidity(context.Context, *RemoveLiquidityRequest) (*RemoveLiquidityResponse, error)
	GetClaimHistory(context.Context, *GetClaimHistoryRequest) (*GetClaimHistoryResponse, error)
	GetLPHistory(context.Context, *GetLPHistoryRequest) (*GetLPHistoryResponse, error)
	CreateUserTicket(context.Context, *CreateUserTicketRequest) (*emptypb.Empty, error)
	DeleteUserAccount(context.Context, *emptypb.Empty) (*DeleteUserAccountResponse, error)
	// StopMarketOrder places a stop market order by given StopMarketOrderRequest and returns a OrderResponse.
	StopMarketOrder(context.Context, *StopMarketOrderRequest) (*OrderResponse, error)
	// GetTopTraders is used to rank the winners from trading competitions
	GetTopTraders(context.Context, *GetTopTradersRequest) (*GetTopTradersResponse, error)
	// ListWalletAddresses list all users wallets adresseses created by exchange
	ListWalletAddresses(context.Context, *ListWalletAddressesRequest) (*ListWalletAddressesResponse, error)
	// ListExchangeWallets list all exchange wallets
	ListExchangeWallets(context.Context, *emptypb.Empty) (*ListExchangeWalletsResponse, error)
	// ListExchangeBalances list all token balances in custody of the exchange
	ListExchangeBalances(context.Context, *emptypb.Empty) (*ListExchangeBalancesResponse, error)
	mustEmbedUnimplementedManagerServiceServer()
}

// UnimplementedManagerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedManagerServiceServer struct {
}

func (UnimplementedManagerServiceServer) GetKeypairs(context.Context, *GetKeypairsRequest) (*GetKeypairsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKeypairs not implemented")
}
func (UnimplementedManagerServiceServer) GetNetworks(context.Context, *GetNetworksRequest) (*GetNetworksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNetworks not implemented")
}
func (UnimplementedManagerServiceServer) GetOrderbookStatus(context.Context, *OrderbookStatusRequest) (*OrderbookStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderbookStatus not implemented")
}
func (UnimplementedManagerServiceServer) GetTickers(context.Context, *TickersRequest) (*TickersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTickers not implemented")
}
func (UnimplementedManagerServiceServer) GetBalances(context.Context, *BalancesRequest) (*BalancesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBalances not implemented")
}
func (UnimplementedManagerServiceServer) GetTokens(context.Context, *GetTokensRequest) (*GetTokensResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTokens not implemented")
}
func (UnimplementedManagerServiceServer) GetOrders(context.Context, *GetOrdersRequest) (*GetOrdersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrders not implemented")
}
func (UnimplementedManagerServiceServer) GetStopOrders(context.Context, *GetStopOrdersRequest) (*GetStopOrdersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStopOrders not implemented")
}
func (UnimplementedManagerServiceServer) GetOrderDetails(context.Context, *GetOrderDetailsRequest) (*GetOrderDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderDetails not implemented")
}
func (UnimplementedManagerServiceServer) GetOrderGroups(context.Context, *GetOrderGroupsRequest) (*GetOrderGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderGroups not implemented")
}
func (UnimplementedManagerServiceServer) LimitOrder(context.Context, *LimitOrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LimitOrder not implemented")
}
func (UnimplementedManagerServiceServer) StopLimitOrder(context.Context, *StopLimitOrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopLimitOrder not implemented")
}
func (UnimplementedManagerServiceServer) MarketOrder(context.Context, *MarketOrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarketOrder not implemented")
}
func (UnimplementedManagerServiceServer) CancelOrder(context.Context, *CancelOrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelOrder not implemented")
}
func (UnimplementedManagerServiceServer) CancelStopOrder(context.Context, *CancelOrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelStopOrder not implemented")
}
func (UnimplementedManagerServiceServer) HealthCheck(context.Context, *emptypb.Empty) (*HealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedManagerServiceServer) ConfirmAddresses(context.Context, *ConfirmAddressesRequest) (*ConfirmAddressesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmAddresses not implemented")
}
func (UnimplementedManagerServiceServer) CreateTotp(context.Context, *emptypb.Empty) (*CreateTotpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTotp not implemented")
}
func (UnimplementedManagerServiceServer) ActivateTotp(context.Context, *ActivateTotpRequest) (*ActivateTotpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivateTotp not implemented")
}
func (UnimplementedManagerServiceServer) ValidateTotpCode(context.Context, *ValidateTotpRequest) (*ValidateTotpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateTotpCode not implemented")
}
func (UnimplementedManagerServiceServer) DeactivateTotp(context.Context, *DeactivateTotpRequest) (*DeactivateTotpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeactivateTotp not implemented")
}
func (UnimplementedManagerServiceServer) GetTotpConfig(context.Context, *emptypb.Empty) (*GetTotpConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTotpConfig not implemented")
}
func (UnimplementedManagerServiceServer) GetDepositAddress(context.Context, *GetDepositAddressRequest) (*GetDepositAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDepositAddress not implemented")
}
func (UnimplementedManagerServiceServer) GetFills(context.Context, *GetFillsRequest) (*GetFillsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFills not implemented")
}
func (UnimplementedManagerServiceServer) GetDepositByID(context.Context, *DepositRequest) (*DepositResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDepositByID not implemented")
}
func (UnimplementedManagerServiceServer) GetDeposits(context.Context, *DepositsRequest) (*DepositsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeposits not implemented")
}
func (UnimplementedManagerServiceServer) GetWithdrawByID(context.Context, *WithdrawRequest) (*WithdrawResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWithdrawByID not implemented")
}
func (UnimplementedManagerServiceServer) GetWithdrawals(context.Context, *WithdrawalsRequest) (*WithdrawalsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWithdrawals not implemented")
}
func (UnimplementedManagerServiceServer) GetUserHistory(context.Context, *HistoryRequest) (*HistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserHistory not implemented")
}
func (UnimplementedManagerServiceServer) GenerateAccount(context.Context, *GenerateAccountRequest) (*GenerateAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateAccount not implemented")
}
func (UnimplementedManagerServiceServer) GenerateAccountAuthHook(context.Context, *GenerateAccountHookRequest) (*GenerateAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateAccountAuthHook not implemented")
}
func (UnimplementedManagerServiceServer) VerifyAuthHook(context.Context, *emptypb.Empty) (*VerifyAuthHookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyAuthHook not implemented")
}
func (UnimplementedManagerServiceServer) AddWithdraw(context.Context, *AddWithdrawRequest) (*AddWithdrawResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddWithdraw not implemented")
}
func (UnimplementedManagerServiceServer) CalculateMarketPrice(context.Context, *CalcMarketRequest) (*CalcMarketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateMarketPrice not implemented")
}
func (UnimplementedManagerServiceServer) GetAccountLimitInfo(context.Context, *GetAccountLimitInfoRequest) (*GetAccountLimitInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountLimitInfo not implemented")
}
func (UnimplementedManagerServiceServer) GenerateKey(context.Context, *GenerateKeyRequest) (*GenerateKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateKey not implemented")
}
func (UnimplementedManagerServiceServer) GetKeys(context.Context, *GetKeysRequest) (*GetKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKeys not implemented")
}
func (UnimplementedManagerServiceServer) DeleteKey(context.Context, *DeleteKeyRequest) (*DeleteKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteKey not implemented")
}
func (UnimplementedManagerServiceServer) GetUser(context.Context, *emptypb.Empty) (*GetUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUser not implemented")
}
func (UnimplementedManagerServiceServer) SetUserAddress(context.Context, *SetUserAddressRequest) (*SetUserAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserAddress not implemented")
}
func (UnimplementedManagerServiceServer) GetKycStatus(context.Context, *emptypb.Empty) (*GetKycStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKycStatus not implemented")
}
func (UnimplementedManagerServiceServer) UpdateTransactionKycStatus(context.Context, *UpdateKycStatusRequest) (*UpdateKycStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTransactionKycStatus not implemented")
}
func (UnimplementedManagerServiceServer) CancelAllOrders(context.Context, *CancelAllOrdersRequest) (*CancelAllOrdersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelAllOrders not implemented")
}
func (UnimplementedManagerServiceServer) CancelAllStopOrders(context.Context, *CancelAllOrdersRequest) (*CancelAllOrdersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelAllStopOrders not implemented")
}
func (UnimplementedManagerServiceServer) InitiateKyc(context.Context, *emptypb.Empty) (*InitiateKycResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateKyc not implemented")
}
func (UnimplementedManagerServiceServer) GetVipLevelTable(context.Context, *emptypb.Empty) (*VipLevelTableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVipLevelTable not implemented")
}
func (UnimplementedManagerServiceServer) GetUserVipLevel(context.Context, *emptypb.Empty) (*UserVipLevelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserVipLevel not implemented")
}
func (UnimplementedManagerServiceServer) GetKeypairsCard(context.Context, *GetKeypairsCardRequest) (*GetKeypairsCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKeypairsCard not implemented")
}
func (UnimplementedManagerServiceServer) AddIP(context.Context, *AddIPRequest) (*AddIPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddIP not implemented")
}
func (UnimplementedManagerServiceServer) GetIPList(context.Context, *GetIPListRequest) (*GetIPListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIPList not implemented")
}
func (UnimplementedManagerServiceServer) DeleteIP(context.Context, *DeleteIPRequest) (*DeleteIPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIP not implemented")
}
func (UnimplementedManagerServiceServer) ActivateIPProtection(context.Context, *ActivateIPProtectionRequest) (*ActivateIPProtectionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivateIPProtection not implemented")
}
func (UnimplementedManagerServiceServer) ConvertDust(context.Context, *ConvertDustRequest) (*ConvertDustResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConvertDust not implemented")
}
func (UnimplementedManagerServiceServer) SetUserSettings(context.Context, *SetUserSettingsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserSettings not implemented")
}
func (UnimplementedManagerServiceServer) EstimatedConvertDust(context.Context, *EstimatedConvertDustRequest) (*EstimatedConvertDustResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EstimatedConvertDust not implemented")
}
func (UnimplementedManagerServiceServer) GetUserConversion(context.Context, *emptypb.Empty) (*GetUserConversionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserConversion not implemented")
}
func (UnimplementedManagerServiceServer) GetConvertDustHistory(context.Context, *GetConvertDustHistoryRequest) (*GetOrdersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConvertDustHistory not implemented")
}
func (UnimplementedManagerServiceServer) GetLiquidityPoolEstimatedQuoteAmount(context.Context, *GetLiquidityPoolEstimatedQuoteRequest) (*GetLiquidityPoolEstimatedQuoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLiquidityPoolEstimatedQuoteAmount not implemented")
}
func (UnimplementedManagerServiceServer) AddLiquidity(context.Context, *AddLiquidityRequest) (*AddLiquidityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddLiquidity not implemented")
}
func (UnimplementedManagerServiceServer) GetLiquidities(context.Context, *emptypb.Empty) (*GetLiquiditiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLiquidities not implemented")
}
func (UnimplementedManagerServiceServer) GetLiquidityPools(context.Context, *GetLiquidityPoolsRequest) (*GetLiquidityPoolsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLiquidityPools not implemented")
}
func (UnimplementedManagerServiceServer) ClaimRewards(context.Context, *ClaimRewardsRequest) (*ClaimRewardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClaimRewards not implemented")
}
func (UnimplementedManagerServiceServer) RemoveLiquidity(context.Context, *RemoveLiquidityRequest) (*RemoveLiquidityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveLiquidity not implemented")
}
func (UnimplementedManagerServiceServer) GetClaimHistory(context.Context, *GetClaimHistoryRequest) (*GetClaimHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClaimHistory not implemented")
}
func (UnimplementedManagerServiceServer) GetLPHistory(context.Context, *GetLPHistoryRequest) (*GetLPHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLPHistory not implemented")
}
func (UnimplementedManagerServiceServer) CreateUserTicket(context.Context, *CreateUserTicketRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserTicket not implemented")
}
func (UnimplementedManagerServiceServer) DeleteUserAccount(context.Context, *emptypb.Empty) (*DeleteUserAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserAccount not implemented")
}
func (UnimplementedManagerServiceServer) StopMarketOrder(context.Context, *StopMarketOrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopMarketOrder not implemented")
}
func (UnimplementedManagerServiceServer) GetTopTraders(context.Context, *GetTopTradersRequest) (*GetTopTradersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTopTraders not implemented")
}
func (UnimplementedManagerServiceServer) ListWalletAddresses(context.Context, *ListWalletAddressesRequest) (*ListWalletAddressesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWalletAddresses not implemented")
}
func (UnimplementedManagerServiceServer) ListExchangeWallets(context.Context, *emptypb.Empty) (*ListExchangeWalletsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExchangeWallets not implemented")
}
func (UnimplementedManagerServiceServer) ListExchangeBalances(context.Context, *emptypb.Empty) (*ListExchangeBalancesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExchangeBalances not implemented")
}
func (UnimplementedManagerServiceServer) mustEmbedUnimplementedManagerServiceServer() {}

// UnsafeManagerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ManagerServiceServer will
// result in compilation errors.
type UnsafeManagerServiceServer interface {
	mustEmbedUnimplementedManagerServiceServer()
}

func RegisterManagerServiceServer(s grpc.ServiceRegistrar, srv ManagerServiceServer) {
	s.RegisterService(&ManagerService_ServiceDesc, srv)
}

func _ManagerService_GetKeypairs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKeypairsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetKeypairs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetKeypairs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetKeypairs(ctx, req.(*GetKeypairsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetNetworks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNetworksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetNetworks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetNetworks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetNetworks(ctx, req.(*GetNetworksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetOrderbookStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderbookStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetOrderbookStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetOrderbookStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetOrderbookStatus(ctx, req.(*OrderbookStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetTickers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TickersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetTickers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetTickers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetTickers(ctx, req.(*TickersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetBalances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BalancesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetBalances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetBalances_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetBalances(ctx, req.(*BalancesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetTokens_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTokensRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetTokens(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetTokens_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetTokens(ctx, req.(*GetTokensRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrdersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetOrders(ctx, req.(*GetOrdersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetStopOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStopOrdersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetStopOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetStopOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetStopOrders(ctx, req.(*GetStopOrdersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetOrderDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetOrderDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetOrderDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetOrderDetails(ctx, req.(*GetOrderDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetOrderGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetOrderGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetOrderGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetOrderGroups(ctx, req.(*GetOrderGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_LimitOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LimitOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).LimitOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_LimitOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).LimitOrder(ctx, req.(*LimitOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_StopLimitOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopLimitOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).StopLimitOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_StopLimitOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).StopLimitOrder(ctx, req.(*StopLimitOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_MarketOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarketOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).MarketOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_MarketOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).MarketOrder(ctx, req.(*MarketOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_CancelOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).CancelOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_CancelOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).CancelOrder(ctx, req.(*CancelOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_CancelStopOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).CancelStopOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_CancelStopOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).CancelStopOrder(ctx, req.(*CancelOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).HealthCheck(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_ConfirmAddresses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmAddressesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).ConfirmAddresses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_ConfirmAddresses_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).ConfirmAddresses(ctx, req.(*ConfirmAddressesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_CreateTotp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).CreateTotp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_CreateTotp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).CreateTotp(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_ActivateTotp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivateTotpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).ActivateTotp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_ActivateTotp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).ActivateTotp(ctx, req.(*ActivateTotpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_ValidateTotpCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateTotpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).ValidateTotpCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_ValidateTotpCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).ValidateTotpCode(ctx, req.(*ValidateTotpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_DeactivateTotp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeactivateTotpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).DeactivateTotp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_DeactivateTotp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).DeactivateTotp(ctx, req.(*DeactivateTotpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetTotpConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetTotpConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetTotpConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetTotpConfig(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetDepositAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDepositAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetDepositAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetDepositAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetDepositAddress(ctx, req.(*GetDepositAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetFills_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFillsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetFills(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetFills_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetFills(ctx, req.(*GetFillsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetDepositByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DepositRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetDepositByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetDepositByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetDepositByID(ctx, req.(*DepositRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetDeposits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DepositsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetDeposits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetDeposits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetDeposits(ctx, req.(*DepositsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetWithdrawByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithdrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetWithdrawByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetWithdrawByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetWithdrawByID(ctx, req.(*WithdrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetWithdrawals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithdrawalsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetWithdrawals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetWithdrawals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetWithdrawals(ctx, req.(*WithdrawalsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetUserHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetUserHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetUserHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetUserHistory(ctx, req.(*HistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GenerateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GenerateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GenerateAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GenerateAccount(ctx, req.(*GenerateAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GenerateAccountAuthHook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateAccountHookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GenerateAccountAuthHook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GenerateAccountAuthHook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GenerateAccountAuthHook(ctx, req.(*GenerateAccountHookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_VerifyAuthHook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).VerifyAuthHook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_VerifyAuthHook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).VerifyAuthHook(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_AddWithdraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddWithdrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).AddWithdraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_AddWithdraw_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).AddWithdraw(ctx, req.(*AddWithdrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_CalculateMarketPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalcMarketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).CalculateMarketPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_CalculateMarketPrice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).CalculateMarketPrice(ctx, req.(*CalcMarketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetAccountLimitInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountLimitInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetAccountLimitInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetAccountLimitInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetAccountLimitInfo(ctx, req.(*GetAccountLimitInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GenerateKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GenerateKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GenerateKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GenerateKey(ctx, req.(*GenerateKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetKeys(ctx, req.(*GetKeysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_DeleteKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).DeleteKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_DeleteKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).DeleteKey(ctx, req.(*DeleteKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetUser(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_SetUserAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).SetUserAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_SetUserAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).SetUserAddress(ctx, req.(*SetUserAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetKycStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetKycStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetKycStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetKycStatus(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_UpdateTransactionKycStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKycStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).UpdateTransactionKycStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_UpdateTransactionKycStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).UpdateTransactionKycStatus(ctx, req.(*UpdateKycStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_CancelAllOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelAllOrdersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).CancelAllOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_CancelAllOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).CancelAllOrders(ctx, req.(*CancelAllOrdersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_CancelAllStopOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelAllOrdersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).CancelAllStopOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_CancelAllStopOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).CancelAllStopOrders(ctx, req.(*CancelAllOrdersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_InitiateKyc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).InitiateKyc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_InitiateKyc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).InitiateKyc(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetVipLevelTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetVipLevelTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetVipLevelTable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetVipLevelTable(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetUserVipLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetUserVipLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetUserVipLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetUserVipLevel(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetKeypairsCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKeypairsCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetKeypairsCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetKeypairsCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetKeypairsCard(ctx, req.(*GetKeypairsCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_AddIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).AddIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_AddIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).AddIP(ctx, req.(*AddIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetIPList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIPListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetIPList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetIPList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetIPList(ctx, req.(*GetIPListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_DeleteIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).DeleteIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_DeleteIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).DeleteIP(ctx, req.(*DeleteIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_ActivateIPProtection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivateIPProtectionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).ActivateIPProtection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_ActivateIPProtection_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).ActivateIPProtection(ctx, req.(*ActivateIPProtectionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_ConvertDust_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertDustRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).ConvertDust(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_ConvertDust_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).ConvertDust(ctx, req.(*ConvertDustRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_SetUserSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).SetUserSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_SetUserSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).SetUserSettings(ctx, req.(*SetUserSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_EstimatedConvertDust_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EstimatedConvertDustRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).EstimatedConvertDust(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_EstimatedConvertDust_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).EstimatedConvertDust(ctx, req.(*EstimatedConvertDustRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetUserConversion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetUserConversion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetUserConversion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetUserConversion(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetConvertDustHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConvertDustHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetConvertDustHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetConvertDustHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetConvertDustHistory(ctx, req.(*GetConvertDustHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetLiquidityPoolEstimatedQuoteAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiquidityPoolEstimatedQuoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetLiquidityPoolEstimatedQuoteAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetLiquidityPoolEstimatedQuoteAmount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetLiquidityPoolEstimatedQuoteAmount(ctx, req.(*GetLiquidityPoolEstimatedQuoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_AddLiquidity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLiquidityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).AddLiquidity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_AddLiquidity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).AddLiquidity(ctx, req.(*AddLiquidityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetLiquidities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetLiquidities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetLiquidities_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetLiquidities(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetLiquidityPools_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiquidityPoolsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetLiquidityPools(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetLiquidityPools_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetLiquidityPools(ctx, req.(*GetLiquidityPoolsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_ClaimRewards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimRewardsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).ClaimRewards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_ClaimRewards_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).ClaimRewards(ctx, req.(*ClaimRewardsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_RemoveLiquidity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveLiquidityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).RemoveLiquidity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_RemoveLiquidity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).RemoveLiquidity(ctx, req.(*RemoveLiquidityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetClaimHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClaimHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetClaimHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetClaimHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetClaimHistory(ctx, req.(*GetClaimHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetLPHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLPHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetLPHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetLPHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetLPHistory(ctx, req.(*GetLPHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_CreateUserTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserTicketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).CreateUserTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_CreateUserTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).CreateUserTicket(ctx, req.(*CreateUserTicketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_DeleteUserAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).DeleteUserAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_DeleteUserAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).DeleteUserAccount(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_StopMarketOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopMarketOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).StopMarketOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_StopMarketOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).StopMarketOrder(ctx, req.(*StopMarketOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_GetTopTraders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopTradersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).GetTopTraders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_GetTopTraders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).GetTopTraders(ctx, req.(*GetTopTradersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_ListWalletAddresses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWalletAddressesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).ListWalletAddresses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_ListWalletAddresses_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).ListWalletAddresses(ctx, req.(*ListWalletAddressesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_ListExchangeWallets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).ListExchangeWallets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_ListExchangeWallets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).ListExchangeWallets(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ManagerService_ListExchangeBalances_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManagerServiceServer).ListExchangeBalances(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManagerService_ListExchangeBalances_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManagerServiceServer).ListExchangeBalances(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// ManagerService_ServiceDesc is the grpc.ServiceDesc for ManagerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ManagerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "manager.ManagerService",
	HandlerType: (*ManagerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetKeypairs",
			Handler:    _ManagerService_GetKeypairs_Handler,
		},
		{
			MethodName: "GetNetworks",
			Handler:    _ManagerService_GetNetworks_Handler,
		},
		{
			MethodName: "GetOrderbookStatus",
			Handler:    _ManagerService_GetOrderbookStatus_Handler,
		},
		{
			MethodName: "GetTickers",
			Handler:    _ManagerService_GetTickers_Handler,
		},
		{
			MethodName: "GetBalances",
			Handler:    _ManagerService_GetBalances_Handler,
		},
		{
			MethodName: "GetTokens",
			Handler:    _ManagerService_GetTokens_Handler,
		},
		{
			MethodName: "GetOrders",
			Handler:    _ManagerService_GetOrders_Handler,
		},
		{
			MethodName: "GetStopOrders",
			Handler:    _ManagerService_GetStopOrders_Handler,
		},
		{
			MethodName: "GetOrderDetails",
			Handler:    _ManagerService_GetOrderDetails_Handler,
		},
		{
			MethodName: "GetOrderGroups",
			Handler:    _ManagerService_GetOrderGroups_Handler,
		},
		{
			MethodName: "LimitOrder",
			Handler:    _ManagerService_LimitOrder_Handler,
		},
		{
			MethodName: "StopLimitOrder",
			Handler:    _ManagerService_StopLimitOrder_Handler,
		},
		{
			MethodName: "MarketOrder",
			Handler:    _ManagerService_MarketOrder_Handler,
		},
		{
			MethodName: "CancelOrder",
			Handler:    _ManagerService_CancelOrder_Handler,
		},
		{
			MethodName: "CancelStopOrder",
			Handler:    _ManagerService_CancelStopOrder_Handler,
		},
		{
			MethodName: "HealthCheck",
			Handler:    _ManagerService_HealthCheck_Handler,
		},
		{
			MethodName: "ConfirmAddresses",
			Handler:    _ManagerService_ConfirmAddresses_Handler,
		},
		{
			MethodName: "CreateTotp",
			Handler:    _ManagerService_CreateTotp_Handler,
		},
		{
			MethodName: "ActivateTotp",
			Handler:    _ManagerService_ActivateTotp_Handler,
		},
		{
			MethodName: "ValidateTotpCode",
			Handler:    _ManagerService_ValidateTotpCode_Handler,
		},
		{
			MethodName: "DeactivateTotp",
			Handler:    _ManagerService_DeactivateTotp_Handler,
		},
		{
			MethodName: "GetTotpConfig",
			Handler:    _ManagerService_GetTotpConfig_Handler,
		},
		{
			MethodName: "GetDepositAddress",
			Handler:    _ManagerService_GetDepositAddress_Handler,
		},
		{
			MethodName: "GetFills",
			Handler:    _ManagerService_GetFills_Handler,
		},
		{
			MethodName: "GetDepositByID",
			Handler:    _ManagerService_GetDepositByID_Handler,
		},
		{
			MethodName: "GetDeposits",
			Handler:    _ManagerService_GetDeposits_Handler,
		},
		{
			MethodName: "GetWithdrawByID",
			Handler:    _ManagerService_GetWithdrawByID_Handler,
		},
		{
			MethodName: "GetWithdrawals",
			Handler:    _ManagerService_GetWithdrawals_Handler,
		},
		{
			MethodName: "GetUserHistory",
			Handler:    _ManagerService_GetUserHistory_Handler,
		},
		{
			MethodName: "GenerateAccount",
			Handler:    _ManagerService_GenerateAccount_Handler,
		},
		{
			MethodName: "GenerateAccountAuthHook",
			Handler:    _ManagerService_GenerateAccountAuthHook_Handler,
		},
		{
			MethodName: "VerifyAuthHook",
			Handler:    _ManagerService_VerifyAuthHook_Handler,
		},
		{
			MethodName: "AddWithdraw",
			Handler:    _ManagerService_AddWithdraw_Handler,
		},
		{
			MethodName: "CalculateMarketPrice",
			Handler:    _ManagerService_CalculateMarketPrice_Handler,
		},
		{
			MethodName: "GetAccountLimitInfo",
			Handler:    _ManagerService_GetAccountLimitInfo_Handler,
		},
		{
			MethodName: "GenerateKey",
			Handler:    _ManagerService_GenerateKey_Handler,
		},
		{
			MethodName: "GetKeys",
			Handler:    _ManagerService_GetKeys_Handler,
		},
		{
			MethodName: "DeleteKey",
			Handler:    _ManagerService_DeleteKey_Handler,
		},
		{
			MethodName: "GetUser",
			Handler:    _ManagerService_GetUser_Handler,
		},
		{
			MethodName: "SetUserAddress",
			Handler:    _ManagerService_SetUserAddress_Handler,
		},
		{
			MethodName: "GetKycStatus",
			Handler:    _ManagerService_GetKycStatus_Handler,
		},
		{
			MethodName: "UpdateTransactionKycStatus",
			Handler:    _ManagerService_UpdateTransactionKycStatus_Handler,
		},
		{
			MethodName: "CancelAllOrders",
			Handler:    _ManagerService_CancelAllOrders_Handler,
		},
		{
			MethodName: "CancelAllStopOrders",
			Handler:    _ManagerService_CancelAllStopOrders_Handler,
		},
		{
			MethodName: "InitiateKyc",
			Handler:    _ManagerService_InitiateKyc_Handler,
		},
		{
			MethodName: "GetVipLevelTable",
			Handler:    _ManagerService_GetVipLevelTable_Handler,
		},
		{
			MethodName: "GetUserVipLevel",
			Handler:    _ManagerService_GetUserVipLevel_Handler,
		},
		{
			MethodName: "GetKeypairsCard",
			Handler:    _ManagerService_GetKeypairsCard_Handler,
		},
		{
			MethodName: "AddIP",
			Handler:    _ManagerService_AddIP_Handler,
		},
		{
			MethodName: "GetIPList",
			Handler:    _ManagerService_GetIPList_Handler,
		},
		{
			MethodName: "DeleteIP",
			Handler:    _ManagerService_DeleteIP_Handler,
		},
		{
			MethodName: "ActivateIPProtection",
			Handler:    _ManagerService_ActivateIPProtection_Handler,
		},
		{
			MethodName: "ConvertDust",
			Handler:    _ManagerService_ConvertDust_Handler,
		},
		{
			MethodName: "SetUserSettings",
			Handler:    _ManagerService_SetUserSettings_Handler,
		},
		{
			MethodName: "EstimatedConvertDust",
			Handler:    _ManagerService_EstimatedConvertDust_Handler,
		},
		{
			MethodName: "GetUserConversion",
			Handler:    _ManagerService_GetUserConversion_Handler,
		},
		{
			MethodName: "GetConvertDustHistory",
			Handler:    _ManagerService_GetConvertDustHistory_Handler,
		},
		{
			MethodName: "GetLiquidityPoolEstimatedQuoteAmount",
			Handler:    _ManagerService_GetLiquidityPoolEstimatedQuoteAmount_Handler,
		},
		{
			MethodName: "AddLiquidity",
			Handler:    _ManagerService_AddLiquidity_Handler,
		},
		{
			MethodName: "GetLiquidities",
			Handler:    _ManagerService_GetLiquidities_Handler,
		},
		{
			MethodName: "GetLiquidityPools",
			Handler:    _ManagerService_GetLiquidityPools_Handler,
		},
		{
			MethodName: "ClaimRewards",
			Handler:    _ManagerService_ClaimRewards_Handler,
		},
		{
			MethodName: "RemoveLiquidity",
			Handler:    _ManagerService_RemoveLiquidity_Handler,
		},
		{
			MethodName: "GetClaimHistory",
			Handler:    _ManagerService_GetClaimHistory_Handler,
		},
		{
			MethodName: "GetLPHistory",
			Handler:    _ManagerService_GetLPHistory_Handler,
		},
		{
			MethodName: "CreateUserTicket",
			Handler:    _ManagerService_CreateUserTicket_Handler,
		},
		{
			MethodName: "DeleteUserAccount",
			Handler:    _ManagerService_DeleteUserAccount_Handler,
		},
		{
			MethodName: "StopMarketOrder",
			Handler:    _ManagerService_StopMarketOrder_Handler,
		},
		{
			MethodName: "GetTopTraders",
			Handler:    _ManagerService_GetTopTraders_Handler,
		},
		{
			MethodName: "ListWalletAddresses",
			Handler:    _ManagerService_ListWalletAddresses_Handler,
		},
		{
			MethodName: "ListExchangeWallets",
			Handler:    _ManagerService_ListExchangeWallets_Handler,
		},
		{
			MethodName: "ListExchangeBalances",
			Handler:    _ManagerService_ListExchangeBalances_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/klever/manager/v1/manager.proto",
}
