package xt

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "XT",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	api               *API
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)

	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("account gtw connect: %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.api, g.options, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return fmt.Errorf("failed to subscribe market data, err: %s", err)
	}

	return nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.GetSymbols()
	if err != nil {
		return nil, err
	}

	return marketsToCommonMarket(res), nil
}

func marketsToCommonMarket(res APISymbolRes) []gateway.Market {
	commonMarkets := make([]gateway.Market, 0, len(res.Symbols))

	for _, symbol := range res.Symbols {
		mkt, err := marketToCommonMarket(symbol)
		if err == nil {
			commonMarkets = append(commonMarkets, mkt)
		}
	}

	return commonMarkets
}

func marketToCommonMarket(symbol APISymbol) (mkt gateway.Market, err error) {
	priceTick := 1 / math.Pow10(symbol.PricePrecision)
	amountTick := 1 / math.Pow10(symbol.QuantityPrecision)

	var minAmount, minValue float64
	for _, filter := range symbol.Filters {
		switch filter.Filter {
		case "QUOTE_QTY":
			minValue = filter.Min
		case "QUANTITY":
			minAmount = filter.Min
		}
	}

	// If minAmount is still 0, but we have minValue, calculate a reasonable default
	if minAmount == 0 && minValue > 0 {
		// Fallback to a reasonable minimum amount
		// This assumes an average price - adjust as needed
		minAmount = amountTick * 10
	}

	return gateway.Market{
		Exchange: Exchange,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(symbol.BaseCurrency),
			Quote: strings.ToUpper(symbol.QuoteCurrency),
		},
		Symbol:                 symbol.Symbol,
		PriceTick:              priceTick,
		AmountTick:             amountTick,
		MinimumOrderSize:       minAmount,
		MinimumOrderMoneyValue: minValue,
		TakerFee:               symbol.TakerFeeRate,
		MakerFee:               symbol.MakerFeeRate,
	}, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) convertPriceLevelsToDepth(priceLevels APIDepth) gateway.DepthBook {
	var result gateway.DepthBook
	var asks, bids []gateway.PriceLevel

	for _, ask := range priceLevels.Asks {
		asks = append(asks, gateway.PriceLevel(ask))
	}
	for _, bid := range priceLevels.Bids {
		bids = append(bids, gateway.PriceLevel(bid))
	}
	result = gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return result
}

func (g *Gateway) GetDepthBook(market gateway.Market, gtwParams gateway.GetDepthParams) (gateway.DepthBook, error) {
	params := make(map[string]string)
	params["symbol"] = market.Symbol

	if gtwParams.Limit == 0 {
		gtwParams.Limit = 500
	}

	params["limit"] = strconv.Itoa(gtwParams.Limit)
	depth, err := g.api.Depth(params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthLevels := g.convertPriceLevelsToDepth(depth)
	depthBook := gateway.DepthBook{
		Asks: depthLevels.Asks,
		Bids: depthLevels.Bids,
	}

	return depthBook, nil
}
