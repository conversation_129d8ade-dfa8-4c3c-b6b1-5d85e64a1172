package xt

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	wsURL = "wss://stream.xt.com/public"
)

// DepthBuffer stores and manages orderbook updates received from the WebSocket
type DepthBuffer struct {
	Symbol       string
	LastUpdateID int64
	Initialized  bool
	Queue        []WsDepthUpdate
	Mutex        sync.Mutex
}

// MarketDataGateway handles market data subscription and processing
type MarketDataGateway struct {
	api               *API
	options           gateway.Options
	tickCh            chan gateway.Tick
	depthBuffers      map[string]*DepthBuffer
	wsClient          *utils.WsClient
	mutex             sync.RWMutex
	subscribedMarkets map[string]gateway.Market
}

type WsSubscribeMessage struct {
	Method string   `json:"method"`
	Params []string `json:"params"`
	ID     string   `json:"id"`
}

type WsResponse struct {
	ID   string `json:"id"`
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type WsDepthUpdate struct {
	Topic string     `json:"topic"`
	Event string     `json:"event"`
	Data  DepthState `json:"data"`
}

type DepthState struct {
	Symbol  string               `json:"s"`
	FirstID int64                `json:"fi"`
	LastID  int64                `json:"i"`
	Asks    []gateway.PriceArray `json:"a"`
	Bids    []gateway.PriceArray `json:"b"`
}

type WsTradeMessage struct {
	Topic string    `json:"topic"`
	Event string    `json:"event"`
	Data  TradeData `json:"data"`
}

type TradeData struct {
	Symbol     string  `json:"s"`
	TradeID    string  `json:"i"`
	Time       int64   `json:"t"`
	Price      float64 `json:"p,string"`
	Quantity   float64 `json:"q,string"`
	BuyerMaker bool    `json:"m"`
}

// NewMarketDataGateway creates a new MarketDataGateway instance
func NewMarketDataGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		api:               api,
		options:           options,
		tickCh:            tickCh,
		depthBuffers:      make(map[string]*DepthBuffer),
		subscribedMarkets: make(map[string]gateway.Market),
	}
}

// SubscribeMarkets subscribes to market data for the given markets
func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	// Initialize depth buffers for each market
	g.mutex.Lock()
	for _, market := range markets {
		g.depthBuffers[market.Symbol] = &DepthBuffer{
			Symbol: market.Symbol,
			Queue:  make([]WsDepthUpdate, 0, 100),
		}
		// Track subscribed markets
		g.subscribedMarkets[market.Symbol] = market
	}
	g.mutex.Unlock()

	// Create and connect WebSocket client
	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)
	ws.SetTag("XT-PublicWS")

	if err := ws.Connect(wsURL); err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	g.wsClient = ws

	// Set up WebSocket message handling first
	msgCh := make(chan []byte, 100)
	ws.SubscribeMessages(msgCh)
	go g.messageHandler(msgCh)
	go g.startHeartbeat(ws)

	// Subscribe to orderbook and trade updates for each market
	for _, market := range markets {
		// Subscribe to depth updates
		depthSub := WsSubscribeMessage{
			Method: "subscribe",
			Params: []string{fmt.Sprintf("depth_update@%s", strings.ToLower(market.Symbol))},
			ID:     fmt.Sprintf("depth_%d", time.Now().UnixNano()),
		}

		if err := g.sendSubscription(ws, depthSub); err != nil {
			log.Printf("Failed to subscribe to depth for %s: %s", market.Symbol, err)
		}

		// Subscribe to trade updates
		tradeSub := WsSubscribeMessage{
			Method: "subscribe",
			Params: []string{fmt.Sprintf("trade@%s", strings.ToLower(market.Symbol))},
			ID:     fmt.Sprintf("trade_%d", time.Now().UnixNano()),
		}

		if err := g.sendSubscription(ws, tradeSub); err != nil {
			log.Printf("Failed to subscribe to trades for %s: %s", market.Symbol, err)
		}

		// Slight delay between subscriptions to avoid rate limiting
		time.Sleep(20 * time.Millisecond)
	}

	// Start fetching orderbook snapshots after subscriptions
	for _, market := range markets {
		// Add delay between API calls to avoid rate limiting
		time.Sleep(100 * time.Millisecond)
		go g.fetchAndInitializeOrderbook(market)
	}

	return nil
}

// sendSubscription sends a subscription message over WebSocket
func (g *MarketDataGateway) sendSubscription(ws *utils.WsClient, msg WsSubscribeMessage) error {
	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("marshal sub message: %s", err)
	}

	return ws.WriteMessage(data)
}

// fetchAndInitializeOrderbook fetches an orderbook snapshot and initializes the orderbook
func (g *MarketDataGateway) fetchAndInitializeOrderbook(market gateway.Market) {
	params := make(map[string]string)
	params["symbol"] = market.Symbol
	params["limit"] = "500"

	depth, err := g.api.Depth(params)
	if err != nil {
		log.Printf("Failed to get orderbook snapshot for %s: %s", market.Symbol, err)
		return
	}

	// Get depth buffer for this market
	g.mutex.RLock()
	buffer, exists := g.depthBuffers[market.Symbol]
	g.mutex.RUnlock()

	if !exists {
		log.Printf("No depth buffer found for %s", market.Symbol)
		return
	}

	buffer.Mutex.Lock()

	if buffer.Initialized {
		buffer.Mutex.Unlock()
		return
	}

	events := []gateway.Event{
		{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: market.Symbol,
			},
		},
	}

	for _, bid := range depth.Bids {
		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Bid,
				Price:  bid.Price,
				Amount: bid.Amount,
			},
		})
	}

	for _, ask := range depth.Asks {
		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Ask,
				Price:  ask.Price,
				Amount: ask.Amount,
			},
		})
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}

	// Update buffer state
	buffer.LastUpdateID = depth.LastUpdateID
	buffer.Initialized = true

	// Process any buffered updates that occurred after this snapshot
	validUpdates := make([]WsDepthUpdate, 0)
	lastUpdateID := depth.LastUpdateID
	for _, update := range buffer.Queue {
		if update.Data.LastID > depth.LastUpdateID {
			validUpdates = append(validUpdates, update)
			lastUpdateID = update.Data.LastID
		}
	}

	// Clear queue since we've filtered what we need
	buffer.Queue = nil
	buffer.LastUpdateID = lastUpdateID
	buffer.Mutex.Unlock()

	// Process valid updates outside the lock
	for _, update := range validUpdates {
		g.processDepthUpdate(market.Symbol, update)
	}
}

type EventTopicMessage struct {
	Topic string `json:"topic"`
}

// messageHandler processes incoming WebSocket messages
func (g *MarketDataGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		var msg EventTopicMessage
		if err := json.Unmarshal(data, &msg); err != nil {
			log.Printf("Failed to unmarshal event topic message: %v", err)
			continue
		}

		switch msg.Topic {
		case "depth_update":
			var depthMsg WsDepthUpdate
			if err := json.Unmarshal(data, &depthMsg); err != nil {
				log.Printf("Failed to unmarshal depth update: %v", err)
				continue
			}
			g.handleDepthUpdate(depthMsg.Data.Symbol, depthMsg)

		case "trade":
			var tradeMsg WsTradeMessage
			if err := json.Unmarshal(data, &tradeMsg); err != nil {
				log.Printf("Failed to unmarshal trade update: %v", err)
				continue
			}
			g.processTradeUpdate(tradeMsg)
		}
	}
}

// handleDepthUpdate processes an orderbook depth update
func (g *MarketDataGateway) handleDepthUpdate(symbol string, update WsDepthUpdate) {
	g.mutex.RLock()
	buffer, exists := g.depthBuffers[symbol]
	g.mutex.RUnlock()

	if !exists {
		return
	}

	buffer.Mutex.Lock()

	// If not initialized yet, buffer the update
	if !buffer.Initialized {
		buffer.Queue = append(buffer.Queue, update)
		buffer.Mutex.Unlock()
		return
	}

	// Check sequence continuity
	lastUpdateID := buffer.LastUpdateID
	if update.Data.LastID <= lastUpdateID {
		log.Printf("%s [%s] receive depth update with old sequence id %d, lastUpdateID %d, diff %d, ignoring message...", Exchange, symbol, update.Data.LastID, lastUpdateID, lastUpdateID-update.Data.LastID)
		buffer.Mutex.Unlock()
		return
	}

	// Check sequence strick continuity
	nextUpdateID := lastUpdateID + 1
	if update.Data.FirstID != nextUpdateID {
		log.Printf("%s [%s] sequence gap detected expected %d, got %d, diff %d", Exchange, symbol, nextUpdateID, update.Data.FirstID, update.Data.FirstID-nextUpdateID)
	}

	// Process the update
	buffer.LastUpdateID = update.Data.LastID
	buffer.Mutex.Unlock()

	g.processDepthUpdate(symbol, update)
}

// processDepthUpdate converts a depth update to gateway events and sends them
func (g *MarketDataGateway) processDepthUpdate(symbol string, update WsDepthUpdate) {
	events := make([]gateway.Event, 0, len(update.Data.Bids)+len(update.Data.Asks))

	for _, bid := range update.Data.Bids {
		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Bid,
				Price:  bid.Price,
				Amount: bid.Amount,
			},
		})
	}

	for _, ask := range update.Data.Asks {
		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Ask,
				Price:  ask.Price,
				Amount: ask.Amount,
			},
		})
	}

	if len(events) > 0 {
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          events,
		}
	}
}

// processTradeUpdate processes a trade update and sends it to the tick channel
func (g *MarketDataGateway) processTradeUpdate(msg WsTradeMessage) {
	var direction gateway.Side
	if msg.Data.BuyerMaker {
		direction = gateway.Ask
	} else {
		direction = gateway.Bid
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog: []gateway.Event{
			{
				Type: gateway.TradeEvent,
				Data: gateway.Trade{
					ID:        msg.Data.TradeID,
					Symbol:    msg.Data.Symbol,
					Direction: direction,
					Price:     msg.Data.Price,
					Amount:    msg.Data.Quantity,
					Timestamp: gateway.ParseTimestamp(msg.Data.Time),
				},
			},
		},
	}
}

// startHeartbeat sends periodic ping messages to keep the WebSocket connection alive
func (g *MarketDataGateway) startHeartbeat(ws *utils.WsClient) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		err := ws.WriteMessage([]byte(`{"type":"ping"}`))
		if err != nil {
			err := fmt.Errorf("failed to send ping: %s", err)
			panic(err)
		}
	}
}
