package xt

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	privateWsURL = "wss://stream.xt.com/private"
)

type AccountGateway struct {
	base.AccountGateway
	api     *API
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		api:     api,
		options: options,
		tickCh:  tickCh,
	}
}

func (g *AccountGateway) Connect() error {
	listenKey, err := g.api.GetWSToken()
	if err != nil {
		return fmt.Errorf("failed to get ws token: %s", err)
	}

	// Connect to private WebSocket
	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)

	err = ws.Connect(privateWsURL)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	// Subscribe to private channels
	subMsg := map[string]interface{}{
		"method": "subscribe",
		"params": []string{
			"trade",
		},
		"listenKey": listenKey,
		"id":        fmt.Sprintf("sub_%d", time.Now().UnixNano()),
	}

	subData, err := json.Marshal(subMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal subscription message: %s", err)
	}

	err = ws.WriteMessage(subData)
	if err != nil {
		return fmt.Errorf("failed to subscribe: %s", err)
	}

	// Start listening for messages
	msgCh := make(chan []byte, 100)
	ws.SubscribeMessages(msgCh)
	go g.handlePrivateMessages(msgCh)
	go g.startHeartbeat(ws)

	// Start token refresh routine
	go g.refreshToken(listenKey)

	return nil
}

func (g *AccountGateway) refreshToken(listenKey string) {
	ticker := time.NewTicker(23 * time.Hour) // Refresh daily
	defer ticker.Stop()

	for range ticker.C {
		newToken, err := g.api.GetWSToken()
		if err != nil {
			log.Printf("XT failed to refresh ws token: %s", err)
			continue
		}

		log.Printf("XT successfully refreshed WebSocket token")
		listenKey = newToken
	}
}

func (g *AccountGateway) startHeartbeat(ws *utils.WsClient) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		err := ws.WriteMessage([]byte(`{"type":"ping"}`))
		if err != nil {
			log.Printf("Failed to send ping: %s", err)
			return
		}
	}
}

func (g *AccountGateway) handlePrivateMessages(ch chan []byte) {
	for msg := range ch {
		var genericEvent struct {
			Topic string          `json:"topic"`
			Event string          `json:"event"`
			Data  json.RawMessage `json:"data"`
		}

		if err := json.Unmarshal(msg, &genericEvent); err != nil {
			log.Printf("XT failed to unmarshal private ws message: %s", err)
			continue
		}

		switch genericEvent.Topic {
		case "trade":
			g.handleTradeUpdate(genericEvent.Data)
		}
	}
}

type TradeUpdate struct {
	Symbol     string  `json:"s"`
	TradeID    string  `json:"i"`
	Time       int64   `json:"t"`
	OrderID    string  `json:"oi"`
	Price      float64 `json:"p,string"`
	Quantity   float64 `json:"q,string"`
	QuoteQty   float64 `json:"v,string"`
	BuyerMaker bool    `json:"b"`
	TakerMaker int     `json:"tm"` // 1-taker 2-maker
}

func (g *AccountGateway) handleTradeUpdate(data json.RawMessage) {
	var update TradeUpdate
	if err := json.Unmarshal(data, &update); err != nil {
		log.Printf("XT failed to unmarshal trade update: %s", err)
		return
	}

	// Determine if this is a fill for our order
	var side gateway.Side
	if update.BuyerMaker {
		side = gateway.Bid
	} else {
		side = gateway.Ask
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog: []gateway.Event{
			gateway.NewFillEvent(gateway.Fill{
				Symbol:    update.Symbol,
				ID:        update.TradeID,
				OrderID:   update.OrderID,
				Side:      side,
				Amount:    update.Quantity,
				Price:     update.Price,
				Timestamp: gateway.ParseTimestamp(update.Time),
				Taker:     update.TakerMaker == 1,
			}),
		},
	}
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	res, err := g.api.Balances()
	if err != nil {
		return balances, err
	}

	balances = make([]gateway.Balance, 0)
	for _, asset := range res.Assets {
		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(asset.Currency),
			Total:     asset.TotalAmount,
			Available: asset.AvailableAmount,
		})
	}

	return balances, nil
}

func mapOrderToState(state string) gateway.OrderState {
	switch state {
	case "NEW":
		return gateway.OrderOpen
	case "PARTIALLY_FILLED":
		return gateway.OrderPartiallyFilled
	case "FILLED":
		return gateway.OrderFullyFilled
	case "CANCELED":
		return gateway.OrderCancelled
	case "REJECTED":
		return gateway.OrderCancelled
	case "EXPIRED":
		return gateway.OrderCancelled
	}
	return gateway.OrderUnknown
}

func mapOrderTypeToSide(_side string) gateway.Side {
	if _side == "BUY" {
		return gateway.Bid
	} else {
		return gateway.Ask
	}
}

func mapAPIOrderToGtw(order APIOrder) gateway.Order {
	return gateway.Order{
		ID:               order.OrderID,
		Side:             mapOrderTypeToSide(order.Side),
		State:            mapOrderToState(order.State),
		Price:            order.Price,
		Amount:           order.OrigQty,
		AvgPrice:         order.AvgPrice,
		FilledAmount:     order.ExecutedQty,
		FilledMoneyValue: order.ExecutedQty * order.AvgPrice,
		Fee:              order.Fee,
		FeeAsset:         order.FeeCurrency,
	}
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	params := make(map[string]string)
	params["market"] = market.Symbol

	apiOrders, err := g.api.OpenOrders(params)
	if err != nil {
		return []gateway.Order{}, err
	}

	orders = make([]gateway.Order, len(apiOrders))
	for i, record := range apiOrders {
		order := mapAPIOrderToGtw(record)
		order.Market = market
		orders[i] = order
	}

	return orders, nil
}

func mapOrderSideToAPI(side gateway.Side) string {
	if side == gateway.Bid {
		return "BUY"
	} else {
		return "SELL"
	}
}

func mapOrderTypeToAPI(_type gateway.OrderType) string {
	if _type == gateway.MarketOrder {
		return "MARKET"
	} else {
		return "LIMIT"
	}
}

var insufficientBalanceMatch = regexp.MustCompile(`ORDER_002`)
var invalidAmountMatch = regexp.MustCompile(`ORDER_008`)
var orderNotOpenedMatch = regexp.MustCompile(`ORDER_*`)

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	params := make(map[string]string)
	params["symbol"] = order.Market.Symbol
	params["side"] = mapOrderSideToAPI(order.Side)
	params["type"] = mapOrderTypeToAPI(order.Type)
	params["timeInForce"] = "GTC"
	params["bizType"] = "SPOT"
	params["quantity"] = utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick)
	params["price"] = utils.FloatToStringWithTick(order.Price, order.Market.PriceTick)

	res, err := g.api.NewOrder(params)
	if err != nil {
		switch {
		case insufficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		case invalidAmountMatch.MatchString(err.Error()):
			return "", gateway.MinOrderSizeErr
		case strings.Contains(err.Error(), "ORDER_F0301"):
			return "", gateway.MinOrderSizeErr
		case orderNotOpenedMatch.MatchString(err.Error()):
			return "", &gateway.OrderNotOpenedErr{
				Err: err,
			}
		default:
			return "", err
		}
	}

	return res, err
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	err := g.api.CancelOrder(order.ID)
	if err != nil {
		if strings.Contains(err.Error(), "ORDER_005") {
			return gateway.OrderNotFoundErr
		}
	}
	return nil
}
