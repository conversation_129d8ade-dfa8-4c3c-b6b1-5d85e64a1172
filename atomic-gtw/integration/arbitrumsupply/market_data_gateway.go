package arbitrumsupply

import (
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type MarketDataGateway struct {
	api             *InfuraAPI
	options         gateway.Options
	refreshInterval time.Duration
	tickCh          chan gateway.Tick
	tokenPairs      []TokenPair
	markets         []gateway.Market
}

func NewMarketDataGateway(api *InfuraAPI, options gateway.Options, pairs []TokenPair, tickCh chan gateway.Tick) *MarketDataGateway {
	refreshInterval := time.Duration(options.RefreshIntervalMs) * time.Millisecond
	if refreshInterval < 1*time.Second {
		log.Printf("ArbitrumSupply market data refreshInterval cannot be less than 1 second, defaulting to 10 seconds")
		refreshInterval = 60 * time.Second
	}

	return &MarketDataGateway{
		api:             api,
		options:         options,
		refreshInterval: refreshInterval,
		tickCh:          tickCh,
		tokenPairs:      pairs,
	}
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	g.markets = markets
	go g.pollMarketData()
	return nil
}

func (g *MarketDataGateway) pollMarketData() {
	for {
		for i, pair := range g.tokenPairs {
			market := g.markets[i]

			// Calculate token price
			price, err := g.api.CalculateTokenPrice(pair)
			if err != nil {
				log.Printf("Failed to calculate price: %s", err)
				continue
			}

			// Create market events
			events := make([]gateway.Event, 0, 3)

			// Add snapshot event
			events = append(events, gateway.Event{
				Type: gateway.SnapshotSequenceEvent,
				Data: gateway.SnapshotSequence{
					Symbol: market.Symbol,
				},
			})

			// Add bid event (actual price)
			bidEvent := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: market.Symbol,
					Side:   gateway.Bid,
					Price:  price,
					Amount: 999999999,
				},
			}
			events = append(events, bidEvent)

			// Add ask event (price + tick)
			askEvent := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: market.Symbol,
					Side:   gateway.Ask,
					Price:  price + market.PriceTick,
					Amount: 999999999,
				},
			}
			events = append(events, askEvent)

			// Send tick
			g.tickCh <- gateway.Tick{
				ReceivedTimestamp: time.Now(),
				EventLog:          events,
			}
		}

		time.Sleep(g.refreshInterval)
	}
}
