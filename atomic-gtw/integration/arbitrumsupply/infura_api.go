package arbitrumsupply

import (
	"context"
	"fmt"
	"math"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
)

// Minimal ERC20 ABI for getting balance and total supply
const erc20ABI = `[
    {
        "constant": true,
        "inputs": [],
        "name": "totalSupply",
        "outputs": [{"name": "", "type": "uint256"}],
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [{"name": "_owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"name": "balance", "type": "uint256"}],
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [],
        "name": "decimals",
        "outputs": [{"name": "", "type": "uint8"}],
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [],
        "name": "symbol",
        "outputs": [{"name": "", "type": "string"}],
        "type": "function"
    }
]`

type InfuraAPI struct {
	client  *ethclient.Client
	chainID int64
}

func NewInfuraAPI(apikey string, network ChainNetwork) (*InfuraAPI, error) {
	var endpoint string
	var chainID int64

	switch network {
	case ChainArbitrum:
		endpoint = fmt.Sprintf("https://arbitrum-mainnet.infura.io/v3/%s", apikey)
		chainID = 42161
	case ChainEthereum:
		endpoint = fmt.Sprintf("https://mainnet.infura.io/v3/%s", apikey)
		chainID = 1
	default:
		return nil, fmt.Errorf("unsupported network: %s", network)
	}

	client, err := ethclient.Dial(endpoint)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Infura: %w", err)
	}

	return &InfuraAPI{
		client:  client,
		chainID: chainID,
	}, nil
}

func (a *InfuraAPI) GetTokenBalance(walletAddress string, token TokenInfo) (*TokenBalance, error) {
	parsed, err := abi.JSON(strings.NewReader(erc20ABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ABI: %w", err)
	}

	tokenAddress := common.HexToAddress(token.Address)
	wallet := common.HexToAddress(walletAddress)

	// Call balanceOf
	data, err := parsed.Pack("balanceOf", wallet)
	if err != nil {
		return nil, fmt.Errorf("failed to pack balanceOf data: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &tokenAddress,
		Data: data,
	}

	result, err := a.client.CallContract(context.Background(), msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call balanceOf: %w", err)
	}

	balance := new(big.Int)
	balance.SetBytes(result)

	// Convert balance to float64 considering decimals
	balanceFloat := new(big.Float)
	balanceFloat.SetInt(balance)

	// Convert to decimal representation by dividing by 10^decimals
	divisor := math.Pow10(token.Decimals)
	balanceFormatted, _ := new(big.Float).Quo(balanceFloat, new(big.Float).SetFloat64(divisor)).Float64()

	return &TokenBalance{
		TokenAddress:     token.Address,
		Balance:          balance.String(),
		BalanceFormatted: balanceFormatted,
		Decimals:         int32(token.Decimals),
	}, nil
}

func (a *InfuraAPI) GetTokenMetadata(token TokenInfo) (*TokenMetadata, error) {
	parsed, err := abi.JSON(strings.NewReader(erc20ABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ABI: %w", err)
	}

	tokenAddress := common.HexToAddress(token.Address)

	// Get total supply
	data, err := parsed.Pack("totalSupply")
	if err != nil {
		return nil, fmt.Errorf("failed to pack totalSupply data: %w", err)
	}

	msg := ethereum.CallMsg{
		To:   &tokenAddress,
		Data: data,
	}

	result, err := a.client.CallContract(context.Background(), msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call totalSupply: %w", err)
	}

	totalSupply := new(big.Int)
	totalSupply.SetBytes(result)

	// Convert total supply to float64 considering decimals
	supplyFloat := new(big.Float)
	supplyFloat.SetInt(totalSupply)

	// Convert to decimal representation by dividing by 10^decimals
	divisor := math.Pow10(token.Decimals)
	totalSupplyFormatted, _ := new(big.Float).Quo(supplyFloat, new(big.Float).SetFloat64(divisor)).Float64()

	// Get symbol
	data, err = parsed.Pack("symbol")
	if err != nil {
		return nil, fmt.Errorf("failed to pack symbol data: %w", err)
	}

	msg.Data = data
	result, err = a.client.CallContract(context.Background(), msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call symbol: %w", err)
	}

	symbol := string(result)

	return &TokenMetadata{
		Address:              token.Address,
		Symbol:               symbol,
		Decimals:             int32(token.Decimals),
		TotalSupply:          float64(totalSupply.Int64()),
		TotalSupplyFormatted: totalSupplyFormatted,
	}, nil
}

func (a *InfuraAPI) CalculateTokenPrice(pair TokenPair) (float64, error) {
	// Get quote token balance in vault
	quoteBalance, err := a.GetTokenBalance(pair.VaultAddress, pair.QuoteToken)
	if err != nil {
		return 0, fmt.Errorf("failed to get quote token balance: %w", err)
	}

	// Get base token metadata for total supply
	baseMetadata, err := a.GetTokenMetadata(pair.BaseToken)
	if err != nil {
		return 0, fmt.Errorf("failed to get base token metadata: %w", err)
	}

	if baseMetadata.TotalSupplyFormatted == 0 {
		return 0, fmt.Errorf("total supply is zero")
	}

	// Price = (WBTC Balance in Vault / EVA Total Supply)
	return quoteBalance.BalanceFormatted / baseMetadata.TotalSupplyFormatted, nil
}
