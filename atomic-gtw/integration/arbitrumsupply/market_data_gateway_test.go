package arbitrumsupply

import (
	"log"
	"testing"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

func TestMarketDataGateway_LiveAPI(t *testing.T) {
	// Get API key from env
	apiKey := "bff2bf9b5e8542559dd3a324fabaaca2"

	// Initialize real API
	api, err := NewInfuraAPI(apiKey, ChainArbitrum)
	if err != nil {
		t.Fatal(err)
	}

	// Create test pair using real addresses
	testPair := TokenPair{
		BaseToken: TokenInfo{
			Address:  "******************************************", // EVA
			Chain:    ChainArbitrum,
			Decimals: 18,
		},
		QuoteToken: TokenInfo{
			Address:  "******************************************", // WBTC
			Chain:    ChainArbitrum,
			Decimals: 8,
		},
		VaultAddress: "******************************************",
	}

	// Test initial data fetch
	t.Run("Test direct API calls", func(t *testing.T) {
		// Calculate price
		log.Printf("Test Pair: %+v", testPair)

		price, err := api.CalculateTokenPrice(testPair)
		if err != nil {
			t.Fatalf("Failed to calculate price: %v", err)
		}
		log.Printf("Calculated EVA/WBTC price: %f", price)
		if price <= 0 {
			t.Errorf("Expected positive price, got %f", price)
		}
	})

	// Test market data gateway with real API
	t.Run("Test market data gateway", func(t *testing.T) {
		tickCh := make(chan gateway.Tick, 10)
		options := gateway.Options{
			RefreshIntervalMs: 100, // Fast refresh for testing
		}

		mdg := NewMarketDataGateway(api, options, []TokenPair{testPair}, tickCh)

		markets := []gateway.Market{
			{
				Exchange: Exchange,
				Symbol:   "EVA/WBTC",
				Pair: gateway.Pair{
					Base:  "EVA",
					Quote: "WBTC",
				},
				PriceTick:  0.00000001, // 1 satoshi
				AmountTick: 0.00000001,
			},
		}

		if err = mdg.SubscribeMarkets(markets); err != nil {
			t.Fatalf("Failed to subscribe to markets: %v", err)
		}

		// Wait for and validate first tick
		select {
		case tick := <-tickCh:
			validateLiveTick(t, tick)
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for market data tick")
		}
	})
}

func validateLiveTick(t *testing.T, tick gateway.Tick) {
	t.Helper()

	// Should have 3 events: snapshot, bid, and ask
	if len(tick.EventLog) != 3 {
		t.Errorf("Expected 3 events in tick, got %d", len(tick.EventLog))
		for i, event := range tick.EventLog {
			t.Logf("Event %d: Type=%v Data=%+v", i, event.Type, event.Data)
		}
		return
	}

	// Track what we've found
	foundSnapshot := false
	foundBid := false
	foundAsk := false
	var bidPrice, askPrice float64

	// Validate each event
	for _, event := range tick.EventLog {
		switch event.Type {
		case gateway.SnapshotSequenceEvent:
			foundSnapshot = true
			snapshot := event.Data.(gateway.SnapshotSequence)
			if snapshot.Symbol != "EVA/WBTC" {
				t.Errorf("Expected symbol EVA/WBTC, got %s", snapshot.Symbol)
			}
			t.Logf("Found snapshot event: %+v", snapshot)

		case gateway.DepthEvent:
			depth := event.Data.(gateway.Depth)
			if depth.Symbol != "EVA/WBTC" {
				t.Errorf("Expected symbol EVA/WBTC, got %s", depth.Symbol)
			}
			if depth.Amount != 999999999 {
				t.Errorf("Expected amount 999999999, got %f", depth.Amount)
			}

			switch depth.Side {
			case gateway.Bid:
				foundBid = true
				bidPrice = depth.Price
				t.Logf("Found bid event: Price=%f Amount=%f", depth.Price, depth.Amount)
			case gateway.Ask:
				foundAsk = true
				askPrice = depth.Price
				t.Logf("Found ask event: Price=%f Amount=%f", depth.Price, depth.Amount)
			}
		}
	}

	// Verify we found all expected events
	if !foundSnapshot {
		t.Error("Missing snapshot event")
	}
	if !foundBid {
		t.Error("Missing bid event")
	}
	if !foundAsk {
		t.Error("Missing ask event")
	}

	// Verify bid/ask relationship
	if askPrice <= bidPrice {
		t.Errorf("Ask price (%f) should be greater than bid price (%f)", askPrice, bidPrice)
	}

	// Verify ask is exactly 1 satoshi above bid
	expectedSpread := 0.00000001 // 1 satoshi
	actualSpread := askPrice - bidPrice
	if actualSpread != expectedSpread {
		t.Errorf("Expected spread of %f, got %f", expectedSpread, actualSpread)
	}

	// Log final prices for reference
	t.Logf("Final prices - Bid: %f WBTC, Ask: %f WBTC, Spread: %f",
		bidPrice, askPrice, actualSpread)
}
