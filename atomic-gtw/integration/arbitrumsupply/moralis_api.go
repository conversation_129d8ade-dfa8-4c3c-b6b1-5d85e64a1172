package arbitrumsupply

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	moralisBaseURL = "https://deep-index.moralis.io/api/v2.2"
)

type ChainNetwork string

const (
	ChainArbitrum  ChainNetwork = "arbitrum"
	ChainEthereum  ChainNetwork = "eth"
	ChainPolygon   ChainNetwork = "polygon"
	ChainBSC       ChainNetwork = "bsc"
	ChainAvalanche ChainNetwork = "avalanche"
)

// TokenInfo represents basic info needed to monitor a token
type TokenInfo struct {
	Address  string       // Contract address
	Chain    ChainNetwork // Which blockchain network
	Decimals int          // Token decimals
}

// TokenBalance represents token balance information from Moralis
type TokenBalance struct {
	TokenAddress     string  `json:"token_address"`
	Symbol           string  `json:"symbol"`
	Name             string  `json:"name"`
	Decimals         int32   `json:"decimals"`
	Balance          string  `json:"balance"`
	BalanceFormatted float64 `json:"balance_formatted,string"`
	UsdPrice         float64 `json:"usd_price"`
}

// TokenMetadata represents token metadata from Moralis
type TokenMetadata struct {
	Address              string  `json:"address"`
	Name                 string  `json:"name"`
	Symbol               string  `json:"symbol"`
	Decimals             int32   `json:"decimals,string"`
	TotalSupply          float64 `json:"total_supply,string"`
	TotalSupplyFormatted float64 `json:"total_supply_formatted,string"`
}

// TokenPair represents a trading pair of tokens to monitor
type TokenPair struct {
	BaseToken    TokenInfo // The main token (e.g. EVA)
	QuoteToken   TokenInfo // The token it's priced in (e.g. WBTC)
	VaultAddress string    // Address holding the quote token (if applicable)
}

// MoralisResponse wraps Moralis paginated responses
type MoralisResponse struct {
	Cursor   string          `json:"cursor"`
	Page     int             `json:"page"`
	PageSize int             `json:"page_size"`
	Result   json.RawMessage `json:"result"`
}

type API struct {
	apiKey     string
	httpClient *utils.HttpClient
}

func NewAPI(apiKey string) *API {
	client := utils.NewHttpClient()

	return &API{
		apiKey:     apiKey,
		httpClient: client,
	}
}

// MoralisError represents the error response from Moralis API
type MoralisError struct {
	Message string `json:"message"`
}

func (e *MoralisError) Error() string {
	return e.Message
}

func (a *API) makeRequest(endpoint string) ([]byte, error) {
	url := fmt.Sprintf("%s%s", moralisBaseURL, endpoint)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Accept", "application/json")
	req.Header.Add("X-API-Key", a.apiKey)

	return a.sendRequest(req)
}

func (a *API) sendRequest(req *http.Request) ([]byte, error) {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return nil, &utils.HttpError{
			Exchange: Exchange.String(),
			Origin:   utils.HttpRequestError,
			Err:      fmt.Sprintf("failed to send request: %s", err),
		}
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, &utils.HttpError{
			Exchange: Exchange.String(),
			Origin:   utils.HttpRequestError,
			Err:      fmt.Sprintf("failed to read response body: %s", err),
		}
	}

	// Handle error responses
	if res.StatusCode != http.StatusOK {
		var moralisErr MoralisError
		if err = json.Unmarshal(body, &moralisErr); err != nil {
			// If we can't unmarshal the error, return the raw response
			return nil, &utils.HttpError{
				Exchange: Exchange.String(),
				Origin:   utils.HttpRequestError,
				Err:      fmt.Sprintf("HTTP %d: %s", res.StatusCode, string(body)),
			}
		}

		// Handle specific error cases
		switch {
		case strings.Contains(moralisErr.Message, "Rate limit"):
			return nil, gateway.RateLimitErr
		case res.StatusCode == http.StatusUnauthorized:
			return nil, fmt.Errorf("invalid API key")
		default:
			return nil, &utils.HttpError{
				Exchange: Exchange.String(),
				Origin:   utils.HttpRequestError,
				Err:      moralisErr.Message,
			}
		}
	}

	return body, nil
}

// GetTokenBalance with improved error handling
func (a *API) GetTokenBalance(walletAddress string, token TokenInfo) (*TokenBalance, error) {
	endpoint := fmt.Sprintf("/wallets/%s/tokens?chain=%s&token_addresses%%5B0%%5D=%s&exclude_spam=true",
		walletAddress,
		token.Chain,
		token.Address)

	body, err := a.makeRequest(endpoint)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch token balance: %w", err)
	}

	var response MoralisResponse
	if err = json.Unmarshal(body, &response); err != nil {
		return nil, &utils.HttpError{
			Exchange: Exchange.String(),
			Origin:   utils.UnmarshalError,
			Err:      fmt.Sprintf("failed to unmarshal response: %s", err),
		}
	}

	var balances []TokenBalance
	if err = json.Unmarshal(response.Result, &balances); err != nil {
		return nil, &utils.HttpError{
			Exchange: Exchange.String(),
			Origin:   utils.UnmarshalError,
			Err:      fmt.Sprintf("failed to unmarshal balances: %s", err),
		}
	}

	if len(balances) == 0 {
		return nil, fmt.Errorf("no balance found for token %s", token.Address)
	}

	return &balances[0], nil
}

// GetTokenMetadata with improved error handling
func (a *API) GetTokenMetadata(token TokenInfo) (*TokenMetadata, error) {
	endpoint := fmt.Sprintf("/erc20/metadata?chain=%s&addresses%%5B0%%5D=%s",
		token.Chain,
		token.Address)

	body, err := a.makeRequest(endpoint)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch token metadata: %w", err)
	}

	var metadataList []TokenMetadata
	if err = json.Unmarshal(body, &metadataList); err != nil {
		return nil, &utils.HttpError{
			Exchange: Exchange.String(),
			Origin:   utils.UnmarshalError,
			Err:      fmt.Sprintf("failed to unmarshal metadata: %s", err),
		}
	}

	if len(metadataList) == 0 {
		return nil, fmt.Errorf("no metadata found for token %s", token.Address)
	}

	return &metadataList[0], nil
}

// CalculateTokenPrice calculates price of base token in terms of quote token
func (a *API) CalculateTokenPrice(pair TokenPair) (float64, error) {
	// Get quote token balance in vault
	quoteBalance, err := a.GetTokenBalance(pair.VaultAddress, pair.QuoteToken)
	if err != nil {
		return 0, fmt.Errorf("failed to get quote token balance: %w", err)
	}

	// Get base token metadata for total supply
	baseMetadata, err := a.GetTokenMetadata(pair.BaseToken)
	if err != nil {
		return 0, fmt.Errorf("failed to get base token metadata: %w", err)
	}

	if baseMetadata.TotalSupplyFormatted == 0 {
		return 0, fmt.Errorf("total supply is zero")
	}

	return (quoteBalance.BalanceFormatted / baseMetadata.TotalSupplyFormatted) * quoteBalance.UsdPrice, nil
}
