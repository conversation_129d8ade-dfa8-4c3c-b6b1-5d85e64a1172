package arbitrumsupply

import (
	"fmt"
	"math"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "ArbitrumSupply",
}

// Default symbol for EVA/WBTC pair
const DefaultSymbol = "EVA/WBTC"

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *MarketDataGateway
	api               *InfuraAPI
	tickCh            chan gateway.Tick
	tokenPairs        []TokenPair
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options:    options,
		tickCh:     make(chan gateway.Tick, 10),
		tokenPairs: make([]TokenPair, 0),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func parseTokenPairsFromOptions(options gateway.Options) ([]TokenPair, error) {
	// Default to EVA/WBTC pair if no specific pairs provided
	if len(options.LoadMarket) == 0 {
		return []TokenPair{
			{
				BaseToken: TokenInfo{
					Address:  "******************************************",
					Chain:    ChainArbitrum,
					Decimals: 18,
				},
				QuoteToken: TokenInfo{
					Address:  "******************************************",
					Chain:    ChainArbitrum,
					Decimals: 8, // WBTC uses 8 decimals like BTC
				},
				VaultAddress: "******************************************",
			},
		}, nil
	}

	pairs := make([]TokenPair, 0)
	for _, market := range options.LoadMarket {
		// Expected format: baseAddress:quoteAddress:vaultAddress:chain:baseDecimals:quoteDecimals
		parts := strings.Split(market, ":")
		if len(parts) != 6 {
			return nil, fmt.Errorf("invalid market format: %s, expected baseAddress:quoteAddress:vaultAddress:chain:baseDecimals:quoteDecimals", market)
		}

		baseDecimals, err := parseDecimals(parts[4])
		if err != nil {
			return nil, fmt.Errorf("invalid base decimals for market %s: %w", market, err)
		}

		quoteDecimals, err := parseDecimals(parts[5])
		if err != nil {
			return nil, fmt.Errorf("invalid quote decimals for market %s: %w", market, err)
		}

		pair := TokenPair{
			BaseToken: TokenInfo{
				Address:  parts[0],
				Chain:    ChainNetwork(parts[3]),
				Decimals: baseDecimals,
			},
			QuoteToken: TokenInfo{
				Address:  parts[1],
				Chain:    ChainNetwork(parts[3]),
				Decimals: quoteDecimals,
			},
			VaultAddress: parts[2],
		}
		pairs = append(pairs, pair)
	}

	return pairs, nil
}

func parseDecimals(s string) (int, error) {
	var decimals int
	_, err := fmt.Sscanf(s, "%d", &decimals)
	if err != nil {
		return 0, err
	}
	return decimals, nil
}

func (g *Gateway) Connect() error {
	if g.options.ApiKey == "" {
		return fmt.Errorf("ArbitrumSupply requires API key")
	}

	// Initialize API
	api, err := NewInfuraAPI(g.options.ApiKey, ChainArbitrum)
	if err != nil {
		return fmt.Errorf("failed to create ArbitrumSupply gateway: %w", err)
	}
	g.api = api

	// Parse token pairs from options
	tokenPairs, err := parseTokenPairsFromOptions(g.options)
	if err != nil {
		return fmt.Errorf("failed to parse token pairs: %w", err)
	}
	g.tokenPairs = tokenPairs

	g.marketDataGateway = NewMarketDataGateway(g.api, g.options, g.tokenPairs, g.tickCh)
	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	if err := g.marketDataGateway.SubscribeMarkets(markets); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	if g.api == nil {
		return nil, fmt.Errorf("gateway not connected")
	}

	markets := make([]gateway.Market, len(g.tokenPairs))

	for i, pair := range g.tokenPairs {
		// For the default EVA/WBTC pair, use predefined symbol
		var symbol string
		if len(g.tokenPairs) == 1 && g.options.LoadMarket == nil {
			symbol = DefaultSymbol
		} else {
			// For custom pairs, fetch metadata and construct symbol
			baseMetadata, err := g.api.GetTokenMetadata(pair.BaseToken)
			if err != nil {
				return nil, fmt.Errorf("failed to get base token metadata: %w", err)
			}

			quoteMetadata, err := g.api.GetTokenMetadata(pair.QuoteToken)
			if err != nil {
				return nil, fmt.Errorf("failed to get quote token metadata: %w", err)
			}

			symbol = fmt.Sprintf("%s/%s", baseMetadata.Symbol, quoteMetadata.Symbol)
		}

		// Configure ticks based on quote token decimals
		priceTick := getPriceTick(pair.QuoteToken)
		amountTick := getAmountTick(pair.BaseToken)

		markets[i] = gateway.Market{
			Exchange: Exchange,
			Symbol:   symbol,
			Pair: gateway.Pair{
				Base:  "EVA", // Default for now, will be replaced with actual metadata for custom pairs
				Quote: "WBTC",
			},
			PriceTick:  priceTick,
			AmountTick: amountTick,
			MarketType: gateway.SpotMarket,
		}

		// Update pair info for custom pairs
		if len(g.tokenPairs) != 1 || g.options.LoadMarket != nil {
			baseMetadata, _ := g.api.GetTokenMetadata(pair.BaseToken)
			quoteMetadata, _ := g.api.GetTokenMetadata(pair.QuoteToken)
			markets[i].Pair.Base = baseMetadata.Symbol
			markets[i].Pair.Quote = quoteMetadata.Symbol
		}
	}

	return markets, nil
}

func getPriceTick(token TokenInfo) float64 {
	// Special case for BTC/WBTC with 8 decimals
	if token.Decimals == 8 {
		return 0.00000001 // 1 satoshi
	}

	// For other tokens, use their decimal precision
	return 1.0 / math.Pow10(token.Decimals)
}

func getAmountTick(token TokenInfo) float64 {
	// Base token amount tick
	return 1.0 / math.Pow10(token.Decimals)
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
