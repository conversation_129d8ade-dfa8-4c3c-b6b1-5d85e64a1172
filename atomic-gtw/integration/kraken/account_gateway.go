package kraken

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api       *API
	options   gateway.Options
	tickCh    chan gateway.Tick
	baseUrlWs string
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick, baseUrlWs string) *AccountGateway {
	return &AccountGateway{
		api:       api,
		options:   options,
		tickCh:    tickCh,
		baseUrlWs: baseUrlWs,
	}
}

func (g *AccountGateway) Connect() error {
	return nil
}

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	data := map[string]string{
		"pair":      order.Market.Symbol,
		"ordertype": "limit",
		"volume":    utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		"price":     utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
	}

	if order.Side == gateway.Bid {
		data["type"] = "buy"
	} else if order.Side == gateway.Ask {
		data["type"] = "sell"
	}

	if order.PostOnly {
		data["oflags"] = "post"
	}

	orderID, err := g.api.PlaceOrder(data)
	if err != nil {
		return orderId, err
	}
	return orderID, nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID)
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	accountBalance, err := g.api.AccountBalanceEx()
	if err != nil {
		return balances, err
	}

	// Use a map to aggregate balances by cleaned asset name
	aggregatedBalances := make(map[string]gateway.Balance)

	for asset, balance := range accountBalance {
		cleanedAsset := cleanBalanceAssetName(asset)
		available := balance.Balance + balance.Credit - balance.CreditUsed - balance.HoldTrade
		total := balance.Balance

		if existing, exists := aggregatedBalances[cleanedAsset]; exists {
			// Aggregate the balances
			aggregatedBalances[cleanedAsset] = gateway.Balance{
				Asset:     cleanedAsset,
				Available: existing.Available + available,
				Total:     existing.Total + total,
			}
		} else {
			aggregatedBalances[cleanedAsset] = gateway.Balance{
				Asset:     cleanedAsset,
				Available: available,
				Total:     total,
			}
		}
	}

	// Convert map to slice
	balances = make([]gateway.Balance, 0, len(aggregatedBalances))
	for _, balance := range aggregatedBalances {
		balances = append(balances, balance)
	}

	return
}

// Kraken might append .B, .M or .F as a suffix on the asset name.
// .B, which represents balances in new yield-bearing products, similar to .S (staked) and .M (opt-in rewards) balances
// .F, which represents balances earning automatically in Kraken Rewards
func cleanBalanceAssetName(asset string) string {
	asset = strings.TrimSuffix(asset, ".B") // .B is used for balances in new yield-bearing products
	asset = strings.TrimSuffix(asset, ".S") // .S is used for staked balances
	asset = strings.TrimSuffix(asset, ".M") // .M is used for opt-in rewards
	asset = strings.TrimSuffix(asset, ".F") // .F is used for balances earning automatically in Kraken Rewards
	asset = strings.TrimSuffix(asset, ".P") // Parachain assets like DOT, KSM, etc. may have .P suffix
	asset = strings.TrimSpace(asset)
	asset = strings.ToUpper(asset)

	return asset
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrders, err := g.api.OpenOrders()
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders))
	for id, order := range openOrders {
		orders = append(orders, mapAPIOrderToCommon(order, id, market))
	}

	return
}

func mapAPIOrderToCommon(o Order, id string, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market:   market,
		ID:       id,
		Side:     mapAPIOrderTypeToCommonSide(o.Descr.Type),
		State:    mapAPIOrderStateToCommon(o.Status),
		Amount:   o.Vol,
		Price:    o.Descr.Price,
		AvgPrice: o.Descr.Price,
		Fee:      o.Fee,
	}
}

func mapAPIOrderTypeToCommonSide(orderType string) gateway.Side {
	if orderType == "buy" {
		return gateway.Bid
	} else if orderType == "sell" {
		return gateway.Ask
	} else {
		return ""
	}
}

func (g *AccountGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	err := ws.Connect(g.baseUrlWs)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	pairs := make([]string, 0)
	for _, market := range markets {
		pairs = append(pairs, fmt.Sprintf("%s/%s", market.Pair.Base, market.Pair.Quote))
	}

	channels := []string{"ownTrades"}
	for _, channel := range channels {
		err := g.makeSubscribeMessage(ws, channel)
		if err != nil {
			return fmt.Errorf("failed to send sub msg, err: %s", err)
		}
	}
	return nil
}

type WsRequestAuth struct {
	Event        string `json:"event"`
	Subscription struct {
		Name  string `json:"name"`
		Token string `json:"token"`
	} `json:"subscription"`
}

func (g *AccountGateway) makeSubscribeMessage(ws *utils.WsClient, channel string) error {
	token, err := g.api.GetWebSocketsToken()
	if err != nil {
		return fmt.Errorf("failed to get ws token, err: %s", err)
	}
	subAuthRequest := WsRequestAuth{
		Event: "subscribe",
		Subscription: struct {
			Name  string `json:"name"`
			Token string `json:"token"`
		}{
			Name:  channel,
			Token: token,
		},
	}
	data, err := json.Marshal(subAuthRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}

	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write sub msg to ws: %s", err)
	}
	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ch)

	return nil
}

func (g *AccountGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		// At this point, we are expecting a channel msg update
		// Which is sent as an array, some mesages are sent as json objects
		if len(data) > 0 && string(data[0]) == "[" {
			g.wsArrayMessageHandler(data)
		}
	}
}

func (g *AccountGateway) wsArrayMessageHandler(data []byte) {
	var msg []json.RawMessage
	err := json.Unmarshal(data, &msg)
	if err != nil {
		log.Printf("%s failed to unmarhsal msg [%s] err [%s]", Exchange, string(data), err)
		return
	}

	var channel string
	err = json.Unmarshal(msg[1], &channel)
	if err != nil {
		log.Printf("%s failed to unmarhsal channel from data [%s] err [%s]", Exchange, string(msg[2]), err)
		return
	}

	switch {
	case strings.Contains(channel, "ownTrades"):
		if err := g.processUserFill(msg[0]); err != nil {
			log.Printf("%s processing ownTrades update, err: %s", Exchange, err)
		}
	default:
		log.Printf("%s messageHandler received unknown channel [%s]", Exchange, channel)
	}

}

type WsUserTrade struct {
	Cost      string  `json:"cost"`
	Fee       float64 `json:"fee,string"`
	Ordertxid string  `json:"ordertxid"`
	Ordertype string  `json:"ordertype"`
	Pair      string  `json:"pair"`
	Postxid   string  `json:"postxid"`
	Price     float64 `json:"price,string"`
	Type      string  `json:"type"`
	Vol       float64 `json:"vol,string"`
}

func (g *AccountGateway) processUserFill(data json.RawMessage) error {
	var listTrades []map[string]WsUserTrade
	err := json.Unmarshal(data, &listTrades)
	if err != nil {
		return fmt.Errorf("unmarhsal price from data [%s] err [%s]", string(data), err)
	}

	eventLog := make([]gateway.Event, 0, 1)
	for _, t := range listTrades {
		for _, trade := range t {
			side := mapAPIOrderTypeToCommonSide(trade.Type)
			event := gateway.Event{
				Type: gateway.FillEvent,
				Data: gateway.Fill{
					OrderID: trade.Ordertxid,
					Symbol:  trade.Pair,
					Price:   trade.Price,
					Amount:  trade.Vol,
					Fee:     trade.Fee,
					Side:    side,
				},
			}

			eventLog = append(eventLog, event)
		}

	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
	return nil
}
