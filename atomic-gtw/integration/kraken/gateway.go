package kraken

import (
	"fmt"
	"math"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Kraken",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	tickCh            chan gateway.Tick
	api               *API
	marketDataGateway *MarketDataGateway
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(apiBaseProd, options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

const wsURL = "wss://ws.kraken.com"
const wsURLAuth = "wss://ws-auth.kraken.com"

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh, wsURLAuth)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	symbolsMarkets, err := g.getSymbolsAndMarkets()
	if err != nil {
		return fmt.Errorf("Failed to get markets, err %s", err)
	}

	symbolsMap := make(map[string]SymbolDetails)
	for symbol, market := range symbolsMarkets {
		symbolsMap[market.Symbol] = symbol
	}

	g.marketDataGateway = NewMarketDataGateway(wsURL, g.options, g.tickCh, g.api, symbolsMap)
	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return fmt.Errorf("Failed to subscribe to market data, err %s", err)
	}

	if g.options.ApiKey != "" {
		err := g.accountGateway.SubscribeMarkets(markets)
		if err != nil {
			return fmt.Errorf("Failed to subscribe to account updates, err %s", err)
		}
	}
	return nil
}

func (g *Gateway) Close() error {
	return gateway.NotImplementedErr
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbolsMarkets, err := g.getSymbolsAndMarkets()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(symbolsMarkets))
	for _, market := range symbolsMarkets {
		commonMarkets = append(commonMarkets, market)
	}

	return commonMarkets, nil
}

func (g *Gateway) getSymbolsAndMarkets() (map[SymbolDetails]gateway.Market, error) {
	symbols, err := g.api.Symbols()
	if err != nil {
		return nil, err
	}

	symbolsMarkets := make(map[SymbolDetails]gateway.Market)
	for _, symbol := range symbols {
		market := symbolToCommonMarket(symbol)
		symbolsMarkets[symbol] = market
	}

	return symbolsMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func symbolToCommonMarket(symbol SymbolDetails) gateway.Market {
	priceTick := symbol.TickSize
	amountTick := 1 / math.Pow10(symbol.LotDecimals)
	return gateway.Market{
		Exchange: Exchange,
		Symbol:   symbol.Altname,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(symbol.Base),
			Quote: strings.ToUpper(symbol.Quote),
		},
		TakerFee:         0.001,
		MakerFee:         0.001,
		PriceTick:        priceTick,
		AmountTick:       amountTick,
		MinimumOrderSize: symbol.OrderMin,
		MarketType:       gateway.SpotMarket,
	}
}

func mapAPIOrderStateToCommon(st string) gateway.OrderState {
	switch st {
	case "open":
		return gateway.OrderOpen
	case "pending":
		return gateway.OrderOpen
	case "expired":
		return gateway.OrderCancelled
	case "canceled":
		return gateway.OrderCancelled
	case "closed":
		return gateway.OrderClosed
	}
	return gateway.OrderUnknown
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	if params.Limit == 0 {
		params.Limit = 1000
	}

	depth, err := g.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, fmt.Errorf("error getting depth book: %w", err)
	}

	data, ok := depth[market.Symbol]
	if !ok {
		return gateway.DepthBook{}, fmt.Errorf("no depth data for market %s", market.Symbol)
	}

	return gateway.DepthBook{
		Bids: gateway.PriceArrayToPriceLevels(data.Bids),
		Asks: gateway.PriceArrayToPriceLevels(data.Asks),
	}, nil
}
