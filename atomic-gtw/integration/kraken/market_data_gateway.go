package kraken

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	baseURL    string
	options    gateway.Options
	tickCh     chan gateway.Tick
	api        *API
	symbolsMap map[string]SymbolDetails
}

func NewMarketDataGateway(baseURL string, options gateway.Options, tickCh chan gateway.Tick, api *API, marketsMap map[string]SymbolDetails) *MarketDataGateway {
	return &MarketDataGateway{
		baseURL:    baseURL,
		options:    options,
		tickCh:     tickCh,
		api:        api,
		symbolsMap: marketsMap,
	}
}

type WsRequest struct {
	Event        string         `json:"event"`
	Pair         []string       `json:"pair"`
	Subscription WsSubscription `json:"subscription"`
}

type WsSubscription struct {
	Name  string `json:"name"`
	Token string `json:"token"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	err := ws.Connect(g.baseURL)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	pairs := make([]string, 0)
	for _, market := range markets {
		assetDetail, ok := g.symbolsMap[market.Symbol]
		if ok {
			pairs = append(pairs, assetDetail.WSName)
		} else {
			return fmt.Errorf("faield to find asssetDetail for market [%s]", market)
		}
	}
	channels := []string{"book", "trade"}

	for _, channel := range channels {
		err := g.makeSubscribeMessage(ws, pairs, channel)
		if err != nil {
			return fmt.Errorf("failed to send sub msg, err: %s", err)
		}
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ch)

	return nil
}

func (g *MarketDataGateway) makeSubscribeMessage(ws *utils.WsClient, pairs []string, channel string) error {
	subRequest := WsRequest{
		Event: "subscribe",
		Pair:  pairs,
		Subscription: WsSubscription{
			Name: channel,
		},
	}
	data, err := json.Marshal(subRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}

	if err := ws.WriteMessage(data); err != nil {
		return fmt.Errorf("failed write sub msg to ws: %s", err)
	}

	return nil
}

type WsBook struct {
	AsksSnapshot []gateway.PriceArray `json:"as"`
	BidsSnapshot []gateway.PriceArray `json:"bs"`
	Asks         []gateway.PriceArray `json:"a"`
	Bids         []gateway.PriceArray `json:"b"`
	Checksum     string               `json:"c"`
}

func (g *MarketDataGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		// At this point, we are expecting a channel msg update
		// Which is sent as an array, some mesages are sent as json objects
		if len(data) > 0 && string(data[0]) == "[" {
			g.wsArrayMessageHandler(data)
		} else {
			g.wsDefaultMessageHandler(data)
		}
	}
}

type WsMessage struct {
	Event string `json:"event"`
}

func (g *MarketDataGateway) wsDefaultMessageHandler(data []byte) {
	var msg WsMessage
	err := json.Unmarshal(data, &msg)
	if err != nil {
		log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
		return
	}

	switch msg.Event {
	case "heartbeat":
		// TODO: Respond to this ping request, as required by the documentation
	default:
		log.Printf("%s unprocessable message from ws:\n%s", Exchange, string(data))
	}
}

func (g *MarketDataGateway) wsArrayMessageHandler(data []byte) {
	var msg []json.RawMessage
	err := json.Unmarshal(data, &msg)
	if err != nil {
		log.Printf("Failed to unmarhsal []json.RawMessage [%s] err [%s]", string(data), err)
		return
	}

	if len(msg) < 4 {
		log.Printf("%s messageHandler expected ws msg to contain at least 3 parts, instead received data:\n%s", Exchange, string(data))
		return
	}

	var channel string
	err = json.Unmarshal(msg[2], &channel)
	if err != nil {
		log.Printf("Failed to unmarhsal channel from data [%s] err [%s]", string(msg[2]), err)
		return
	}

	var symbol string
	err = json.Unmarshal(msg[3], &symbol)
	if err != nil {
		log.Printf("Failed to unmarhsal symbol from data [%s] err [%s]", string(msg[3]), err)
		return
	}

	// Fix ws symbol to expected rest format
	symbol = wsSymbolToCommon(symbol)

	switch {
	case strings.Contains(channel, "book"):
		if err := g.processBookUpdate(symbol, msg[1]); err != nil {
			log.Printf("%s processing book update, err: %s", Exchange, err)
		}
	case strings.Contains(channel, "trade"):
		if err := g.processTradeUpdate(symbol, msg[1]); err != nil {
			log.Printf("%s processing trade update, err: %s", Exchange, err)
		}
	default:
		log.Printf("%s received unprocessable channel [%s] for symbol [%s] with data:\n%s", Exchange, channel, symbol, string(data))
	}

}

func (g *MarketDataGateway) processBookUpdate(symbol string, data json.RawMessage) error {
	var bookUpdate WsBook
	err := json.Unmarshal(data, &bookUpdate)
	if err != nil {
		return fmt.Errorf("unmarhsal bookUpdate from data [%s] err [%s]", string(data), err)
	}

	var bids []gateway.PriceArray
	var asks []gateway.PriceArray
	var snapshot bool

	if bookUpdate.Checksum == "" {
		bids = bookUpdate.BidsSnapshot
		asks = bookUpdate.AsksSnapshot
		snapshot = true
	} else {
		bids = bookUpdate.Bids
		asks = bookUpdate.Asks
	}

	eventLog := make([]gateway.Event, 0, len(bids)+len(asks)+1)
	if snapshot {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: symbol,
			},
		})
	}

	appendEvents := func(symbol string, side gateway.Side, pxs []gateway.PriceArray) {
		for _, px := range pxs {
			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  px.Price,
					Amount: px.Amount,
				},
			}

			eventLog = append(eventLog, event)
		}
	}

	appendEvents(symbol, gateway.Bid, bids)
	appendEvents(symbol, gateway.Ask, asks)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

func (g *MarketDataGateway) processTradeUpdate(symbol string, data json.RawMessage) error {
	var side gateway.Side
	var WsTrade [][]string

	err := json.Unmarshal(data, &WsTrade)
	if err != nil {
		return fmt.Errorf("unmarhsal price from data [%s] err [%s]", string(data), err)
	}

	eventLog := make([]gateway.Event, 0, 1)

	for _, data := range WsTrade {
		price, err := strconv.ParseFloat(data[0], 64)
		if err != nil {
			return fmt.Errorf("unmarhsal price from data [%s] err [%s]", data[0], err)
		}

		volume, err := strconv.ParseFloat(data[1], 64)
		if err != nil {
			return fmt.Errorf("unmarhsal volume from data [%s] err [%s]", data[1], err)
		}

		tradeTime, err := parseTradeTimestamp(data[2])
		if err != nil {
			return fmt.Errorf("unmarhsal trade timestamp from data [%s] err [%s]", data[2], err)
		}

		sideStr := data[3]
		if sideStr == "b" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		event := gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				Timestamp: tradeTime,
				Symbol:    symbol,
				Direction: side,
				Price:     price,
				Amount:    volume,
			},
		}

		eventLog = append(eventLog, event)
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
	return nil
}

// Ws symbols are different from the REST api
// We must coerce them to the standard symbol, so they match
func wsSymbolToCommon(wsSymbol string) string {
	return strings.ReplaceAll(wsSymbol, "/", "")
}

func parseTradeTimestamp(timestampStr string) (time.Time, error) {
	parts := strings.Split(timestampStr, ".")
	if len(parts) != 2 {
		return time.Time{}, fmt.Errorf("invalid timestamp format: expected 'seconds.fractional', got '%s'", timestampStr)
	}

	sec, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return time.Time{}, fmt.Errorf("error parsing seconds: %w", err)
	}

	// Ensure the nanoseconds part is exactly 9 digits long.
	nanosecStr := parts[1]
	if len(nanosecStr) < 9 {
		nanosecStr = nanosecStr + strings.Repeat("0", 9-len(nanosecStr))
	} else if len(nanosecStr) > 9 {
		nanosecStr = nanosecStr[:9]
	}

	nsec, err := strconv.ParseInt(nanosecStr, 10, 64)
	if err != nil {
		return time.Time{}, fmt.Errorf("error parsing nanoseconds: %w", err)
	}

	return time.Unix(sec, nsec), nil
}
