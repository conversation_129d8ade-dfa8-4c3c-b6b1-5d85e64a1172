package kraken

import (
	"crypto/hmac"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseProd           = "https://api.kraken.com"
	apiSymbols            = "/0/public/AssetPairs"
	apiDepthBook          = "/0/public/Depth?pair=%s&count=%d"
	apiOpenOrders         = "/0/private/OpenOrders"
	apiPlaceOrder         = "/0/private/AddOrder"
	apiCancelOrder        = "/0/private/CancelOrder"
	apiAccountBalanceEx   = "/0/private/BalanceEx"
	apiGetWebSocketsToken = "/0/private/GetWebSocketsToken"
)

type API struct {
	options       gateway.Options
	httpClient    *utils.HttpClient
	baseURL       string
	lastNonceUsed int64
	lock          sync.Mutex
	requestMutex  sync.Mutex // Serializes all authenticated requests
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

func (api *API) getNonce() int64 {
	api.lock.Lock()
	defer api.lock.Unlock()

	// Their nonce must be in milliseconds
	// They actually expect a millisecond nonce
	// not an incremental nonce
	nonce := time.Now().UnixMilli()
	if nonce <= api.lastNonceUsed {
		nonce = api.lastNonceUsed + 1
	}

	api.lastNonceUsed = nonce
	return api.lastNonceUsed
}

type APIResponse struct {
	Error  []interface{}   `json:"error"`
	Result json.RawMessage `json:"result"`
}

type SymbolDetails struct {
	Altname     string  `json:"altname,omitempty"`
	WSName      string  `json:"wsname,omitempty"`
	AclassBase  string  `json:"aclass_base,omitempty"`
	Base        string  `json:"base",omitempty`
	AclassQuote string  `json:"aclass_quote,omitempty"`
	Quote       string  `json:"quote"`
	OrderMin    float64 `json:"ordermin,string"`
	TickSize    float64 `json:"tick_size,string"`
	Status      string  `json:"status"`
	LotDecimals int     `json:"lot_decimals"`
}

func (a *API) Symbols() (symbols []SymbolDetails, err error) {
	signed := false
	req, err := a.newHttpRequest(http.MethodGet, apiSymbols, nil, signed)
	if err != nil {
		return symbols, err
	}

	var listSymbols map[string]SymbolDetails
	err = a.makeHttpRequest(req, &listSymbols, signed)
	if err != nil {
		return nil, err
	}

	for _, v := range listSymbols {
		symbols = append(symbols, v)
	}

	return symbols, nil
}

type ExtendedBalance struct {
	Balance    float64 `json:"balance,string"`
	Credit     float64 `json:"credit,string"`
	CreditUsed float64 `json:"credit_used,string"`
	HoldTrade  float64 `json:"hold_trade,string"`
}

func (a *API) AccountBalanceEx() (res map[string]ExtendedBalance, err error) {
	signed := true
	req, err := a.newHttpRequest(http.MethodPost, apiAccountBalanceEx, nil, signed)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res, signed)
	if err != nil {
		return nil, err
	}

	return res, err
}

type APIOpenOrder struct {
	Open json.RawMessage `json:"open"`
}

type Order struct {
	Status string `json:"status"`
	Descr  struct {
		Pair      string  `json:"pair"`
		Type      string  `json:"type"`
		Ordertype string  `json:"ordertype"`
		Price     float64 `json:"price,string"`
		Order     string  `json:"order"`
	} `json:"descr"`
	Vol float64 `json:"vol,string"`
	Fee float64 `json:"fee,string"`
}

func (a *API) OpenOrders() (orders map[string]Order, err error) {
	signed := true
	req, err := a.newHttpRequest(http.MethodPost, apiOpenOrders, nil, signed)
	if err != nil {
		return orders, err
	}

	var apiOpenOrder APIOpenOrder
	err = a.makeHttpRequest(req, &apiOpenOrder, signed)
	if err != nil {
		return orders, err
	}

	listOpenOrder := map[string]Order{}
	err = json.Unmarshal(apiOpenOrder.Open, &listOpenOrder)
	if err != nil {
		return orders, err
	}
	return listOpenOrder, nil
}

type ApiPlaceOrder struct {
	Descr struct {
		Order string `json:"order"`
	} `json:"descr"`
	Txid []string `json:"txid"`
}

func (a *API) PlaceOrder(order map[string]string) (orderID string, err error) {
	signed := true
	req, err := a.newHttpRequest(http.MethodPost, apiPlaceOrder, order, signed)
	if err != nil {
		return orderID, err
	}
	var apiOrder ApiPlaceOrder
	err = a.makeHttpRequest(req, &apiOrder, signed)
	if err != nil {
		return orderID, err
	}

	return apiOrder.Txid[0], nil

}

type ApiCancelOrder struct {
	Count int `json:"count"`
}

func (a *API) CancelOrder(orderID string) (err error) {
	signed := true
	req, err := a.newHttpRequest(http.MethodPost, apiCancelOrder, map[string]string{"txid": orderID}, signed)
	if err != nil {
		return err
	}

	var cancelOrder ApiCancelOrder
	err = a.makeHttpRequest(req, &cancelOrder, signed)
	if err != nil {
		err = fmt.Errorf("error cancel order: %s", err)
		return err
	}

	return nil
}

type apiToken struct {
	Expires int    `json:"expires"`
	Token   string `json:"token"`
}

func (a *API) GetWebSocketsToken() (token string, err error) {
	signed := true
	req, err := a.newHttpRequest(http.MethodPost, apiGetWebSocketsToken, nil, signed)
	if err != nil {
		return "", err
	}

	var apiWebsocketToken apiToken
	err = a.makeHttpRequest(req, &apiWebsocketToken, signed)
	if err != nil {
		err = fmt.Errorf("error cancel order: %s", err)
		return "", err
	}

	token = apiWebsocketToken.Token
	return token, nil
}

type APIDepthData struct {
	Asks []gateway.PriceArray `json:"asks"`
	Bids []gateway.PriceArray `json:"bids"`
}

type APIDepthBook map[string]APIDepthData

func (a *API) DepthBook(market string, params gateway.GetDepthParams) (APIDepthBook, error) {
	if params.Limit == 0 {
		params.Limit = 500
	}
	signed := false
	var res APIDepthBook
	req, err := a.newHttpRequest(http.MethodGet, fmt.Sprintf(apiDepthBook, market, params.Limit), nil, signed)
	if err != nil {
		return APIDepthBook{}, err
	}
	err = a.makeHttpRequest(req, &res, signed)
	return res, err
}

var rateLimitRegex = regexp.MustCompile(`Rate limit exceeded`)
var invalidNonceRegex = regexp.MustCompile(`Invalid nonce`)

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}, signed bool) error {
	// Serialize all authenticated requests to prevent nonce collisions
	if signed {
		a.requestMutex.Lock()
		defer a.requestMutex.Unlock()
	}

	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}

	var apiRes APIResponse
	err = json.Unmarshal(body, &apiRes)
	if err != nil {
		return fmt.Errorf("unmarshal err [%s] body:\n%s", err, string(body))
	}

	if len(apiRes.Error) != 0 {
		errorMsg := fmt.Sprintf("%v", apiRes.Error[0])
		// Check for invalid nonce errors
		if invalidNonceRegex.MatchString(errorMsg) {
			return gateway.InvalidNonceErr
		} else if rateLimitRegex.MatchString(errorMsg) {
			return gateway.RateLimitErr
		}
		return fmt.Errorf("error: %s", errorMsg)
	}

	if responseObject != nil {
		err = json.Unmarshal(apiRes.Result, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return nil
}

func (a *API) getSignature(url_path string, values url.Values) string {
	sha := sha256.New()
	sha.Write([]byte(values.Get("nonce") + values.Encode()))
	shasum := sha.Sum(nil)
	b64DecodedSecret, _ := base64.StdEncoding.DecodeString(a.options.ApiSecret)

	mac := hmac.New(sha512.New, []byte(b64DecodedSecret))
	mac.Write(append([]byte(url_path), shasum...))
	macsum := mac.Sum(nil)
	return base64.StdEncoding.EncodeToString(macsum)
}

func (a *API) newHttpRequest(method string, path string, data map[string]string, signed bool) (*http.Request, error) {
	urlRequest := a.baseURL + path
	req, err := http.NewRequest(method, urlRequest, nil)
	if err != nil {
		return nil, err
	}

	if signed {
		nonce := a.getNonce()
		payload := url.Values{}
		for key, value := range data {
			payload.Add(key, fmt.Sprintf("%v", value))
		}

		payload.Set("nonce", fmt.Sprintf("%d", nonce))
		signature := a.getSignature(path, payload)
		req.Body = io.NopCloser(strings.NewReader(payload.Encode()))
		req.Header.Set("API-Key", a.options.ApiKey)
		req.Header.Set("API-Sign", signature)
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=utf-8")
	}

	return req, nil
}
