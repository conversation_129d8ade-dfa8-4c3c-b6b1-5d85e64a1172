package probit

import (
	"fmt"
	"math"
	"strconv"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "ProBit",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *MarketDataGateway
	accountGateway    *AccountGateway
	api               *API
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.options, g.api, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.api.InitAuthentication(); err != nil {
			return fmt.Errorf("Failed to init api authentication, err %s", err)
		}
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g, g.tickCh)
	err := g.marketDataGateway.Connect()
	if err != nil {
		return err
	}

	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	apiMarkets, err := g.api.Markets()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, len(apiMarkets))
	for i, m := range apiMarkets {
		commonMarkets[i] = gateway.Market{
			Exchange: Exchange,
			Symbol:   m.ID,
			Pair: gateway.Pair{
				Base:  m.BaseCurrencyID,
				Quote: m.QuoteCurrencyID,
			},
			MakerFee:         0.002,
			TakerFee:         0.002,
			PriceTick:        m.PriceIncrement,
			AmountTick:       1 / math.Pow10(int(m.QuantityPrecision)),
			MinimumOrderSize: m.MinQuantity,
			Closed:           m.Closed,
		}
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	var bids []gateway.PriceLevel
	var asks []gateway.PriceLevel

	apiDepthBook, err := g.api.DepthBook(market.Symbol)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	for _, level := range apiDepthBook {
		price, _ := strconv.ParseFloat(level.Price, 64)
		amount, _ := strconv.ParseFloat(level.Amount, 64)
		priceLevel := gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		}
		if level.Side == "buy" {
			bids = append(bids, priceLevel)
		} else {
			asks = append(asks, priceLevel)
		}
	}
	return gateway.DepthBook{
		Bids: bids,
		Asks: asks,
	}, nil
}
