package upbit

import (
	"math"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Upbit",
}

type Gateway struct {
	base.Gateway
	options            gateway.Options
	api                *API
	marketDataGateway  *MarketDataGateway
	tickCh             chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.options, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	markets, err := g.api.ActiveMarkets()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, len(markets))
	for i, market := range markets {
		var minimumOrderMoneyValue float64
		switch market.QuoteCurrencyCode {
		case "KRW":
			minimumOrderMoneyValue = 500
		case "BTC":
			minimumOrderMoneyValue = 0.0005
		case "ETH":
			minimumOrderMoneyValue = 0.0005
		}

		commonMarkets[i] = gateway.Market{
			Exchange: Exchange,
			Symbol:   market.Code,
			Pair: gateway.Pair{
				Base:  market.BaseCurrencyCode,
				Quote: market.QuoteCurrencyCode,
			},
			MakerFee:               0.0025,
			TakerFee:               0.0025,
			PriceTick:              1 / math.Pow10(int(market.QuoteCurrencyDecimalPlace)),
			AmountTick:             1 / math.Pow10(int(market.BaseCurrencyDecimalPlace)),
			MinimumOrderMoneyValue: minimumOrderMoneyValue,
		}
	}

	return commonMarkets, nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
