package trubit

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api     *API
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		api:     api,
		options: options,
		tickCh:  tickCh,
	}
}

func (g *AccountGateway) Connect() error {
	if err := g.subscribeToUserData(); err != nil {
		return fmt.Errorf("failed to subscribe to user data: %s", err)
	}

	return nil
}

func (g *AccountGateway) subscribeToUserData() error {
	listenKey, err := g.api.GetListenKey()
	if err != nil {
		return fmt.Errorf("failed to get listen key: %s", err)
	}

	wsUserStreamURL := fmt.Sprintf("%s/%s", apiWsUserStream, listenKey)
	ws := utils.NewWsClient()
	if err = ws.Connect(wsUserStreamURL); err != nil {
		return fmt.Errorf("failed to connect to ws: %s", err)
	}
	ws.SetTag(fmt.Sprintf("%s - account gateway", Exchange))

	go func() {
		ticker := time.NewTicker(30 * time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			if err := g.api.KeepAliveListenKey(listenKey); err != nil {
				log.Printf("failed to keep alive listen key: %s", err)
			}
		}
	}()

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ch)

	return nil
}

type WSUpdate struct {
	EventType string `json:"e"`
	EventTime string `json:"E"` // Ignore, need this to be able to unmarshal EventType "e" correctly
}

type WSOrderUpdateReport struct {
	EventType                 string  `json:"e"`
	EventTime                 string  `json:"E"`
	Symbol                    string  `json:"s"`        // Symbol
	ClientOrderID             string  `json:"c"`        // Client order ID
	C                         bool    `json:"C"`        // Not sure what this is, but need to unmarshal it
	Side                      string  `json:"S"`        // Side
	Quantity                  float64 `json:"q,string"` // Order quantity
	Price                     float64 `json:"p,string"` // Order price
	CurrentOrderStatus        string  `json:"X"`        // Current order status
	OrderID                   string  `json:"i"`        // Order ID
	LastExecutedQuantity      float64 `json:"l,string"` // Last executed quantity
	CumulativeFilledQuantity  float64 `json:"z,string"` // Cumulative filled quantity
	LastExecutedPrice         float64 `json:"L,string"` // Last executed price
	CommissionAmount          float64 `json:"n,string"` // Commission amount
	CommissionAsset           string  `json:"N"`        // Commission asset
	CumulativeFilledQuote     float64 `json:"Z,string"` // Cumulative quote asset transacted quantity
	LastQuoteExecutedQuantity float64 `json:"Y,string"` // Last quote asset transacted quantity (i.e. lastPrice * lastQty)
	QuoteOrderQuantity        float64 `json:"Q,string"`
}

func (g *AccountGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		// Check if {"ping" message
		if len(data) > 6 && string(data[0:6]) == "{\"ping" {
			continue
		}

		var updates []json.RawMessage
		err := json.Unmarshal(data, &updates)
		if err != nil {
			log.Printf("%s failed to unmarshal updates array err [%s] body:\n%s", Exchange, err, string(data))
			continue
		}

		eventLog := make([]gateway.Event, 0, len(updates))
		for _, data := range updates {
			var msg WSUpdate
			err = json.Unmarshal(data, &msg)
			if err != nil {
				log.Printf("%s failed to unmarshal WSUpdate err [%s] body:\n%s", Exchange, err, string(data))
				continue
			}

			if msg.EventType == "executionReport" {
				var order WSOrderUpdateReport
				err = json.Unmarshal(data, &order)
				if err != nil {
					log.Printf("%s failed to unmarshal WSOrderUpdateReport err [%s] body:\n%s", Exchange, err, string(data))
					continue
				}

				avgPrice := 0.0
				if order.CumulativeFilledQuantity > 0 {
					avgPrice = order.CumulativeFilledQuote / order.CumulativeFilledQuantity
				}

				eventLog = append(eventLog, gateway.NewOrderUpdateEvent(gateway.Order{
					ID:               order.OrderID,
					ClientOrderID:    order.ClientOrderID,
					State:            mapOrderStateToCommon(order.CurrentOrderStatus),
					Side:             mapAPIOrderTypeToCommonSide(order.Side),
					Price:            order.Price,
					Amount:           order.Quantity,
					AvgPrice:         avgPrice,
					FilledAmount:     order.CumulativeFilledQuantity,
					FilledMoneyValue: order.CumulativeFilledQuote,
					Fee:              order.CommissionAmount,
					FeeAsset:         order.CommissionAsset,
				}))
			}
		}

		if len(eventLog) > 0 {
			g.tickCh <- gateway.Tick{
				ReceivedTimestamp: time.Now(),
				EventLog:          eventLog,
			}
		}
	}
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	accountBalance, err := g.api.GetBalance()
	if err != nil {
		return nil, err
	}

	accBalances := make([]gateway.Balance, 0, len(accountBalance.Balances))
	for _, balance := range accountBalance.Balances {
		accBalances = append(accBalances, gateway.Balance{
			Asset:     balance.Asset,
			Available: balance.Free,
			Total:     balance.Free + balance.Locked,
		})
	}

	return accBalances, err
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrders, err := g.api.GetOpenOrders(market.Symbol)
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders))
	for _, order := range openOrders {
		orders = append(orders, mapAPIOrderToCommon(order, market))
	}

	return
}

func mapAPIOrderToCommon(o APIOpenOrders, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market:        market,
		ID:            o.OrderID,
		ClientOrderID: o.ClientOrderID,
		Side:          mapAPIOrderTypeToCommonSide(o.Side),
		State:         mapOrderStateToCommon(o.Status),
		Amount:        o.OrigQty,
		Price:         o.Price,
		FilledAmount:  o.ExecutedQty,
		AvgPrice:      o.AvgPrice,
	}
}

func mapOrderStateToCommon(status string) gateway.OrderState {
	switch status {
	case "NEW":
		return gateway.OrderOpen
	case "CANCELLED", "PENDING_CANCEL", "REJECTED", "CANCELED":
		return gateway.OrderCancelled
	case "FILLED":
		return gateway.OrderFullyFilled
	case "PARTIALLY_FILLED":
		return gateway.OrderPartiallyFilled
	default:
		return gateway.OrderUnknown
	}
}

func mapAPIOrderTypeToCommonSide(orderSide string) gateway.Side {
	if orderSide == "BUY" {
		return gateway.Bid
	} else if orderSide == "SELL" {
		return gateway.Ask
	} else {
		log.Printf("%s invalid order side \"%s\"", Exchange, orderSide)
		return ""
	}
}

var insuficientBalanceMatch = regexp.MustCompile(`Balance insufficient`)
var quantityLowerMatch = regexp.MustCompile(`Order quantity lower`)
var newOrderRejectedMatch = regexp.MustCompile(`New order rejected`)

func (g *AccountGateway) SendOrder(order gateway.Order) (string, error) {
	var side string
	if order.Side == gateway.Bid {
		side = "BUY"
	} else {
		side = "SELL"
	}

	newOrder := APINewOrder{
		NewClientOrderID: order.ClientOrderID,
		Symbol:           order.Market.Symbol,
		Side:             side,
		Type:             "LIMIT",
		Quantity:         utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		Price:            utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
		TimeInForce:      "GTC",
		RecvWindow:       5000,
		Timestamp:        time.Now().UnixNano() / int64(time.Millisecond),
	}

	if order.PostOnly {
		newOrder.Type = "LIMIT_MAKER"
	}

	orderID, err := g.api.NewOrder(newOrder)
	if err != nil {
		switch {
		case insuficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		case quantityLowerMatch.MatchString(err.Error()):
			return "", gateway.MinOrderSizeErr
		case newOrderRejectedMatch.MatchString(err.Error()):
			return "", &gateway.OrderNotOpenedErr{
				Err: err,
			}
		default:
			return "", err
		}
	}

	return orderID, nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID)
}
