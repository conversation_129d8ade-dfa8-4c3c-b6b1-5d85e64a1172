package trubit

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	baseURL string
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewMarketDataGateway(baseURL string, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		baseURL: baseURL,
		options: options,
		tickCh:  tickCh,
	}
}

type WSRequest struct {
	Symbol string `json:"symbol"`
	Topic  string `json:"topic"`
	Event  string `json:"event"`
	Params struct {
		Binary bool `json:"binary"`
	} `json:"params"`
}

type WSPingRequest struct {
	Ping int64 `json:"ping"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	if err := ws.Connect(g.baseURL); err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}
	ws.SetTag(fmt.Sprintf("%s - market data gateway", Exchange))

	symbols := make([]string, 0)
	for _, market := range markets {
		symbols = append(symbols, market.Symbol)
	}

	subscribe := func(symbol, topic string) error {
		subRequest := WSRequest{
			Symbol: symbol,
			Topic:  topic,
			Event:  "sub",
			Params: struct {
				Binary bool `json:"binary"`
			}{Binary: false},
		}

		data, err := json.Marshal(subRequest)
		if err != nil {
			return fmt.Errorf("failed to marshal sub request, err: %s", err)
		}

		if err := ws.WriteMessage(data); err != nil {
			return fmt.Errorf("failed write sub msg to ws: %s", err)
		}

		return nil
	}

	for _, symbol := range symbols {
		if err := subscribe(symbol, "trade"); err != nil {
			return err
		}

		if err := subscribe(symbol, "depth"); err != nil {
			return err
		}
	}

	go func() {
		ticker := time.NewTicker(1 * time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			ping := WSPingRequest{Ping: time.Now().UnixNano() / int64(time.Millisecond)}
			pingData, err := json.Marshal(ping)
			if err != nil {
				log.Printf("failed to marshal ping request, err: %s", err)
				continue
			}
			if err := ws.WriteMessage(pingData); err != nil {
				log.Printf("failed write ping msg to ws: %s", err)
				continue
			}
		}
	}()

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ch)

	return nil
}

type WSResponse struct {
	Symbol string          `json:"symbol"`
	Topic  string          `json:"topic"`
	Data   json.RawMessage `json:"data"`
}

func (g *MarketDataGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		var msg WSResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		switch msg.Topic {
		case "error":
			log.Printf("WS [%s] msg w/ error msg: %s", g.baseURL, string(data))
			continue
		case "trade":
			if err := g.processTradeUpdates(msg.Data, msg.Symbol); err != nil {
				log.Printf("%s error processing matches \"%s\": %s", g.baseURL, data, err)
			}
		case "depth":
			if err := g.proccessDepthUpdates(msg.Data); err != nil {
				log.Printf("%s error processing l2update \"%s\": %s", g.baseURL, data, err)
			}
		default:
			log.Printf("Coinbase unprocessable message type [%s], data [%s]", msg.Topic, string(data))
		}
	}
}

type WSTradeData struct {
	Version   string  `json:"v"`
	Timestamp int64   `json:"t"`
	Price     float64 `json:"p,string"`
	Amount    float64 `json:"q,string"`
	Bid       bool    `json:"m"`
}

func (g *MarketDataGateway) processTradeUpdates(data json.RawMessage, symbol string) error {
	var trades []WSTradeData
	err := json.Unmarshal(data, &trades)
	if err != nil {
		return fmt.Errorf("unmarhsal WsMatchData [%s] err [%s]", string(data), err)
	}

	eventLog := make([]gateway.Event, 0, len(trades))

	var side gateway.Side
	for _, trade := range trades {
		if trade.Bid {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		event := gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				Symbol:    symbol,
				Timestamp: gateway.ParseTimestamp(trade.Timestamp),
				ID:        trade.Version,
				Direction: side,
				Price:     trade.Price,
				Amount:    trade.Amount,
			},
		}

		eventLog = append(eventLog, event)
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

type WSDepthData struct {
	Symbol    string               `json:"s"`
	Timestamp int64                `json:"t"`
	Version   string               `json:"v"`
	Bids      []gateway.PriceArray `json:"b"`
	Asks      []gateway.PriceArray `json:"a"`
}

func (g *MarketDataGateway) proccessDepthUpdates(data json.RawMessage) error {
	var depth []WSDepthData
	if err := json.Unmarshal(data, &depth); err != nil {
		return fmt.Errorf("failed to unmarshal depth data: %s", err)
	}

	for _, d := range depth {
		eventLog := make([]gateway.Event, 0, len(d.Bids)+len(d.Asks)+1)

		eventLog = append(eventLog, gateway.NewSnapshotSequenceEvent(gateway.SnapshotSequence{
			Symbol: d.Symbol,
		}))

		eventLog = append(eventLog, gateway.PriceArrayToDepthEvents(d.Symbol, gateway.Bid, d.Bids)...)
		eventLog = append(eventLog, gateway.PriceArrayToDepthEvents(d.Symbol, gateway.Ask, d.Asks)...)

		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          eventLog,
		}
	}

	return nil
}
