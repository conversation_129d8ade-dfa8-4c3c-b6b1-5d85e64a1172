package trubit

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
	baseURL    string
}

const (
	apiBaseProd     = "https://api-spot.trubit.com"
	apiWsBase       = "wss://ws.trubit.com/openapi/quote/ws/v1"
	apiWsUserStream = "wss://ws.trubit.com/openapi/ws"
	apiMarket       = "/openapi/v1/exchange"
	apiDepth        = "/openapi/quote/v1/depth?symbol=%s&limit=%d"
	apiBalance      = "/openapi/v1/account"
	apiOpenOrders   = "/openapi/v1/openOrders"
	apiOrder        = "/openapi/v1/order"
	apiListenKey    = "/openapi/v1/userDataStream"
)

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

type APISymbols struct {
	BaseAssetName       string      `json:"baseAssetName"`
	QuoteAssetName      string      `json:"quoteAssetName"`
	Symbol              string      `json:"symbol"`
	Status              string      `json:"status"`
	BaseAssetPrecision  float64     `json:"baseAssetPrecision,string"`
	QuoteAssetPrecision float64     `json:"quotePrecision,string"`
	Filters             []APIFilter `json:"filters"`
}

type APIFilter struct {
	FilterType  string  `json:"filterType"`
	MinPrice    float64 `json:"minPrice,string"`
	TickSize    float64 `json:"tickSize,string"`
	MinQty      float64 `json:"minQty,string"`
	MaxQty      float64 `json:"maxQty,string"`
	StepSize    float64 `json:"stepSize,string"`
	MinNotional float64 `json:"minNotional,string"`
}

type APIMarket struct {
	Symbols []APISymbols `json:"symbols"`
}

func (a *API) GetMarkets() (APIMarket, error) {
	req, err := a.newHttpRequest(http.MethodGet, apiMarket, nil, nil, false)
	if err != nil {
		return APIMarket{}, err
	}

	var markets APIMarket
	err = a.makeHttpRequest(req, &markets)
	if err != nil {
		return APIMarket{}, err
	}

	return markets, nil
}

type APIDepth struct {
	Bids []gateway.PriceArray `json:"bids"`
	Asks []gateway.PriceArray `json:"asks"`
}

func (a *API) GetDepth(market gateway.Market, params gateway.GetDepthParams) (APIDepth, error) {
	if params.Limit == 0 {
		params.Limit = 100
	}
	req, err := a.newHttpRequest(http.MethodGet, fmt.Sprintf(apiDepth, market.Symbol, params.Limit), nil, nil, false)
	if err != nil {
		return APIDepth{}, err
	}

	var depth APIDepth
	err = a.makeHttpRequest(req, &depth)
	if err != nil {
		return APIDepth{}, err
	}

	return depth, nil
}

type APIBalance struct {
	Asset  string  `json:"asset"`
	Free   float64 `json:"free,string"`
	Locked float64 `json:"locked,string"`
}

type APIAccount struct {
	Balances []APIBalance `json:"balances"`
}

func (a *API) GetBalance() (APIAccount, error) {
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	params := url.Values{}
	params.Set("timestamp", strconv.FormatInt(timestamp, 10))

	req, err := a.newHttpRequest(http.MethodGet, apiBalance, nil, &params, true)
	if err != nil {
		return APIAccount{}, err
	}

	var account APIAccount
	err = a.makeHttpRequest(req, &account)
	if err != nil {
		return APIAccount{}, err
	}

	return account, nil
}

type APIOpenOrders struct {
	Symbol              string  `json:"symbol"`
	OrderID             string  `json:"orderId"`
	ClientOrderID       string  `json:"clientOrderId"`
	Price               float64 `json:"price,string"`
	OrigQty             float64 `json:"origQty,string"`
	ExecutedQty         float64 `json:"executedQty,string"`
	CummulativeQuoteQty float64 `json:"cummulativeQuoteQty,string"`
	AvgPrice            float64 `json:"avgPrice,string"`
	Status              string  `json:"status"`
	TimeInForce         string  `json:"timeInForce"`
	Type                string  `json:"type"`
	Side                string  `json:"side"`
}

func (a *API) GetOpenOrders(symbol string) ([]APIOpenOrders, error) {
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	params := url.Values{}
	params.Set("symbol", symbol)
	params.Set("timestamp", strconv.FormatInt(timestamp, 10))
	req, err := a.newHttpRequest(http.MethodGet, apiOpenOrders, nil, &params, true)
	if err != nil {
		return nil, err
	}

	var orders []APIOpenOrders
	err = a.makeHttpRequest(req, &orders)
	if err != nil {
		return nil, err
	}

	return orders, nil
}

type APINewOrder struct {
	NewClientOrderID string `json:"newClientOrderId"`
	Symbol           string `json:"symbol"`
	Side             string `json:"side"`
	Type             string `json:"type"`
	Quantity         string `json:"quantity"`
	Price            string `json:"price"`
	Timestamp        int64  `json:"timestamp"`
	RecvWindow       int64  `json:"recvWindow"`
	TimeInForce      string `json:"timeInForce"`
}

type APINewOrderResponse struct {
	OrderID string `json:"orderId"`
}

func (a *API) NewOrder(order APINewOrder) (string, error) {
	// Convert order to params url.Values
	params := url.Values{
		"newClientOrderId": {order.NewClientOrderID},
		"symbol":           {order.Symbol},
		"side":             {order.Side},
		"type":             {order.Type},
		"quantity":         {order.Quantity},
		"price":            {order.Price},
		"timestamp":        {strconv.FormatInt(order.Timestamp, 10)},
		"recvWindow":       {strconv.FormatInt(order.RecvWindow, 10)},
		"timeInForce":      {order.TimeInForce},
	}

	req, err := a.newHttpRequest(http.MethodPost, apiOrder, nil, &params, true)
	if err != nil {
		return "", err
	}

	var resp APINewOrderResponse
	if err = a.makeHttpRequest(req, &resp); err != nil {
		return "", err
	}

	return resp.OrderID, nil
}

func (a *API) CancelOrder(orderID string) error {
	params := url.Values{
		"orderId":   {orderID},
		"timestamp": {strconv.FormatInt(time.Now().UnixNano()/int64(time.Millisecond), 10)},
	}
	req, err := a.newHttpRequest(http.MethodDelete, apiOrder, nil, &params, true)
	if err != nil {
		return err
	}

	return a.makeHttpRequest(req, nil)
}

type APIListenKey struct {
	ListenKey string `json:"listenKey"`
}

func (a *API) GetListenKey() (string, error) {
	params := url.Values{
		"timestamp": {strconv.FormatInt(time.Now().UnixNano()/int64(time.Millisecond), 10)},
	}
	req, err := a.newHttpRequest(http.MethodPost, apiListenKey, nil, &params, true)
	if err != nil {
		return "", err
	}

	var listenKey APIListenKey
	if err = a.makeHttpRequest(req, &listenKey); err != nil {
		return "", err
	}

	return listenKey.ListenKey, nil
}

func (a *API) KeepAliveListenKey(listenKey string) error {
	params := url.Values{
		"listenKey": {listenKey},
		"timestamp": {strconv.FormatInt(time.Now().UnixNano()/int64(time.Millisecond), 10)},
	}
	req, err := a.newHttpRequest(http.MethodPut, apiListenKey, nil, &params, true)
	if err != nil {
		return err
	}

	return a.makeHttpRequest(req, nil)
}

type APIErrResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}

	// Check if not an array response
	// In this case, it's not an error response
	if len(body) > 0 && body[0] != '[' {
		errResponse := APIErrResponse{}
		err = json.Unmarshal(body, &errResponse)
		if err != nil {
			return fmt.Errorf("failed to unmarshal err response json, body: %s, unmarshal err: %s", string(body), err)
		}

		if errResponse.Code != 0 {
			return fmt.Errorf("error response code [%d] msg [%s]", errResponse.Code, errResponse.Msg)
		}
	}

	if responseObject != nil {
		err = json.Unmarshal(body, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return nil
}

func (a *API) newHttpRequest(method string, path string, data io.Reader, params *url.Values, signed bool) (*http.Request, error) {
	url := a.baseURL + path
	req, err := http.NewRequest(method, url, data)
	if err != nil {
		return nil, err
	}

	if signed {
		req.Header.Set("X-BH-APIKEY", a.options.ApiKey)

		if params != nil {
			params.Set("timestamp", strconv.FormatInt(time.Now().UnixNano()/int64(time.Millisecond), 10))
			signature := generateSignature(a.options.ApiSecret, params)
			params.Set("signature", signature)
			req.URL.RawQuery = params.Encode()
		}
	}

	req.Header.Set("Content-Type", "application/json")

	// OriginSource used to identify the MM that is sending the request
	if a.options.OriginSource != "" {
		req.Header.Set("origin_source", a.options.OriginSource)
	}

	return req, nil
}

func generateSignature(secretKey string, params *url.Values) string {
	key := []byte(secretKey)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(params.Encode()))
	signature := hex.EncodeToString(h.Sum(nil))
	return signature
}
