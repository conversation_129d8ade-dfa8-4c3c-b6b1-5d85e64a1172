package trubit

import (
	"fmt"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "TruBit",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(apiBaseProd, options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(apiWsBase, g.options, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return fmt.Errorf("Failed to subscribe to market data, err %s", err)
	}

	return nil
}

func (g *Gateway) Close() error {
	return gateway.NotImplementedErr
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	markets, err := g.api.GetMarkets()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, len(markets.Symbols))
	for i, symbol := range markets.Symbols {
		commonMarkets[i] = symbolToCommonMarket(symbol)
	}

	return commonMarkets, nil
}

func symbolToCommonMarket(market APISymbols) gateway.Market {
	priceTick := market.QuoteAssetPrecision
	amountTick := market.BaseAssetPrecision

	lotSizeFilter, _ := getFilterByType(market.Filters, "LOT_SIZE")
	minNotionalFilter, _ := getFilterByType(market.Filters, "MIN_NOTIONAL")

	return gateway.Market{
		Exchange: Exchange,
		Symbol:   market.Symbol,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(market.BaseAssetName),
			Quote: strings.ToUpper(market.QuoteAssetName),
		},
		Closed:                 market.Status != "TRADING",
		TakerFee:               0.001,
		MakerFee:               0.001,
		PriceTick:              priceTick,
		AmountTick:             amountTick,
		MinimumOrderSize:       lotSizeFilter.MinQty,
		MinimumOrderMoneyValue: minNotionalFilter.MinNotional,
	}
}

func getFilterByType(filters []APIFilter, filterType string) (APIFilter, bool) {
	for _, filter := range filters {
		if filter.FilterType == filterType {
			return filter, true
		}
	}
	return APIFilter{}, false
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := g.api.GetDepth(market, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	return gateway.DepthBook{
		Asks: gateway.PriceArrayToPriceLevels(depth.Asks),
		Bids: gateway.PriceArrayToPriceLevels(depth.Bids),
	}, nil
}

func (g *Gateway) SupportedMethods() gateway.Methods {
	return gateway.Methods{
		CIDMapping: true,
	}
}
