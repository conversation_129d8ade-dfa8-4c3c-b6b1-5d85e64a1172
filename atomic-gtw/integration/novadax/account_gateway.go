package novadax

import (
	"fmt"
	"log"
	"net/url"
	"regexp"
	"sort"
	"strconv"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

var insuficientBalanceMatch = regexp.MustCompile(`Insufficient balance`)
var orderWasClosedMatch = regexp.MustCompile(`Order was closed`)

type AccountGateway struct {
	base.AccountGateway
	api             *API
	tickCh          chan gateway.Tick
	openOrders      map[string]gateway.Order
	openOrdersMutex sync.RWMutex
	options         gateway.Options
}

func NewAccountGateway(api *API, tickCh chan gateway.Tick, options gateway.Options) *AccountGateway {
	return &AccountGateway{
		api:        api,
		tickCh:     tickCh,
		openOrders: make(map[string]gateway.Order),
		options:    options,
	}
}

func (g *AccountGateway) Connect() error {
	go g.trackOpenOrders()

	return nil
}

func (g *AccountGateway) trackOpenOrders() {
	var refreshInterval time.Duration
	if g.options.RefreshIntervalMs > 0 {
		refreshInterval = time.Duration(g.options.RefreshIntervalMs * int(time.Millisecond))
	} else {
		refreshInterval = 1500 * time.Millisecond
	}

	for {
		g.openOrdersMutex.Lock()
		oids := make([]int64, 0, len(g.openOrders))
		for key := range g.openOrders {
			oid, err := strconv.ParseInt(key, 10, 64)
			if err != nil {
				log.Printf("%s account gtw trackOpenOrders parsing order id: %s, err: %s", Exchange, key, err)
				continue
			}

			oids = append(oids, oid)
		}
		g.openOrdersMutex.Unlock()

		if len(oids) > 0 {
			// Sort order ids in ascending order
			sort.Slice(oids, func(i, j int) bool {
				return oids[i] < oids[j]
			})

			firstOrderID := oids[0]
			lastOrderID := oids[len(oids)-1]
			params := url.Values{}

			params.Set("fromId", fmt.Sprintf("%d", firstOrderID-1))
			params.Set("toId", fmt.Sprintf("%d", lastOrderID+1))
			params.Set("limit", "100")

			orders, err := g.api.GetOrderHistory(params)
			if err != nil {
				log.Printf("%s account gtw trackOpenOrders GetOrderHistory error: %s", Exchange, err)
				continue
			}

			events := make([]gateway.Event, 0, 5)
			g.openOrdersMutex.Lock()
			for _, order := range orders {
				trackedOrder, ok := g.openOrders[order.Id]
				if ok {
					updatedOrder := g.parseToGatewayOrder(order, gateway.Market{
						Symbol: order.Symbol,
					})
					hasOrderChanged := trackedOrder.State != updatedOrder.State ||
						trackedOrder.FilledAmount != updatedOrder.FilledAmount

					if hasOrderChanged {
						if g.isOrderOpen(updatedOrder) {
							g.openOrders[order.Id] = updatedOrder
						} else {
							delete(g.openOrders, updatedOrder.ID)
						}

						events = append(events, gateway.NewOrderUpdateEvent(updatedOrder))
					}
				}
			}
			g.openOrdersMutex.Unlock()

			if len(events) > 0 {
				g.tickCh <- gateway.TickWithEvents(events...)
			}

			time.Sleep(refreshInterval)
		} else {
			time.Sleep(500 * time.Millisecond)
		}
	}
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	balances, err := g.api.GetBalances()
	if err != nil {
		return nil, err
	}

	gtwBalances := make([]gateway.Balance, len(balances))

	for i, b := range balances {
		gtwBalances[i] = gateway.Balance{
			Asset:     b.Currency,
			Available: b.Available,
			Total:     b.Available + b.Hold,
		}
	}

	return gtwBalances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	params := url.Values{}
	params.Set("symbol", market.Symbol)
	params.Set("status", "UNFINISHED")
	orders, err := g.api.GetOrderHistory(params)
	if err != nil {
		return nil, err
	}

	gtwOrders := make([]gateway.Order, len(orders))

	for i, order := range orders {
		gtwOrders[i] = g.parseToGatewayOrder(order, market)
	}

	return gtwOrders, nil
}

func (g *AccountGateway) SendOrder(order gateway.Order) (string, error) {
	paramsReq := ApiOrderRequest{
		Symbol: order.Market.Symbol,
		Type:   "LIMIT",
		Side:   g.toExchangeOrderSide(order.Side),
		Amount: utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		Price:  utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
	}

	apiOrder, err := g.api.PostOrder(paramsReq)
	if err != nil {
		switch {
		case insuficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		default:
			return "", err
		}
	}

	// Register open order
	order.ID = apiOrder.Id
	g.openOrdersMutex.Lock()
	g.openOrders[order.ID] = order
	g.openOrdersMutex.Unlock()

	return apiOrder.Id, nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	err := g.api.CancelOrder(order.ID)
	if err != nil {
		switch {
		case orderWasClosedMatch.MatchString(err.Error()):
			err = gateway.AlreadyCancelledErr
		default:
			return err
		}
	}

	return err
}

func (g *AccountGateway) parseToGatewayOrder(order ApiOrder, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market:       market,
		ID:           order.Id,
		State:        g.toGatewayState(order.Status),
		Amount:       order.Amount,
		Price:        order.Price,
		AvgPrice:     order.AveragePrice,
		FilledAmount: order.FilledAmount,
		Side:         g.toGatewaySide(order.Side),
	}
}

func (g *AccountGateway) toGatewayState(status string) gateway.OrderState {
	switch status {
	case "SUBMITTED", "PROCESSING":
		return gateway.OrderOpen
	case "PARTIAL_FILLED":
		return gateway.OrderPartiallyFilled
	case "PARTIAL_CANCELED", "PARTIAL_REJECTED", "CANCELED", "CANCELING", "REJECTED":
		return gateway.OrderCancelled
	case "FILLED":
		return gateway.OrderFullyFilled
	default:
		return gateway.OrderUnknown
	}
}

func (g *AccountGateway) isOrderOpen(order gateway.Order) bool {
	return order.State == gateway.OrderOpen || order.State == gateway.OrderPartiallyFilled
}

func (g *AccountGateway) toGatewaySide(side string) gateway.Side {
	if side == "BUY" {
		return gateway.Bid
	}

	return gateway.Ask
}

func (g *AccountGateway) toExchangeOrderSide(side gateway.Side) string {
	if side == gateway.Bid {
		return "BUY"
	}

	return "SELL"
}
