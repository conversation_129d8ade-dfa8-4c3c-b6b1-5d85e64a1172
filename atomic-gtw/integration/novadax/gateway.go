package novadax

import (
	"fmt"
	"math"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "NovaDAX",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
	webApi            *WebAPI
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		webApi:  NewWebAPI(options),
		tickCh:  make(chan gateway.Tick, 10000),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (gtw *Gateway) Connect() error {
	gtw.accountGateway = NewAccountGateway(gtw.api, gtw.tickCh, gtw.options)
	gtw.marketDataGateway = NewMarketDataGateway(gtw.options, gtw.tickCh, gtw.api, gtw.webApi)

	if err := gtw.marketDataGateway.Connect(); err != nil {
		return err
	}

	if gtw.options.ApiKey != "" {
		if err := gtw.accountGateway.Connect(); err != nil {
			return err
		}
	}

	return nil
}

func (gtw *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return gtw.marketDataGateway.SubscribeMarkets(markets)
}

func (gtw *Gateway) Close() error {
	return nil
}

func (gtw *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.webApi.GetSymbols()
	if err != nil {
		err = fmt.Errorf("failed to get symbols: %w", err)
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(symbols))
	for _, symbol := range symbols {
		commonMarkets = append(commonMarkets, symbolToCommonMarket(symbol))
	}

	return commonMarkets, nil
}

func (gtw *Gateway) AccountGateway() gateway.AccountGateway {
	return gtw.accountGateway
}

func (gtw *Gateway) Tick() chan gateway.Tick {
	return gtw.tickCh
}

func symbolToCommonMarket(symbol ApiSymbolData) gateway.Market {
	priceTick := 1 / math.Pow10(int(symbol.PricePrecision))
	amountTick := 1 / math.Pow10(int(symbol.AmountPrecision))
	pair := gateway.Pair{
		Base:  strings.ToUpper(symbol.BaseCurrency),
		Quote: strings.ToUpper(symbol.QuoteCurrency),
	}
	fee := 0.25 / 100

	return gateway.Market{
		Exchange:               Exchange,
		Pair:                   pair,
		Symbol:                 symbol.Symbol,
		TakerFee:               fee,
		MakerFee:               fee,
		PriceTick:              priceTick,
		AmountTick:             amountTick,
		MinimumOrderSize:       symbol.MinOrderAmount,
		MinimumOrderMoneyValue: symbol.MinimumOrderValue,
	}
}

func parsePriceLevelsToDepth(levels []gateway.PriceArray) []gateway.PriceLevel {
	var priceLevels []gateway.PriceLevel
	for _, level := range levels {
		priceLevels = append(priceLevels, gateway.PriceLevel(level))
	}
	return priceLevels
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	if params.Limit == 0 {
		params.Limit = 5000
	}
	paramsReq := ApiOrderBookRequest{
		Limit:  params.Limit,
		Symbol: market.Symbol,
	}
	depth, err := gtw.webApi.GetOrderBook(paramsReq)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks := parsePriceLevelsToDepth(depth.Asks)
	bids := parsePriceLevelsToDepth(depth.Bids)
	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
