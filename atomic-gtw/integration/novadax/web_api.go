package novadax

import (
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiWebBase    = "https://www.novadax.com.br"
	apiWebSymbols = apiWebBase + "/api/transaction/symbols"
	apiWebDepth   = apiWebBase + "/api/transaction/market/depth?symbol=%s&groupLevel=0"
)

type WebAPI struct {
	options    gateway.Options
	httpClient *utils.HttpClient
}

func NewWebAPI(options gateway.Options) *WebAPI {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &WebAPI{
		options:    options,
		httpClient: client,
	}
}

type webApiResponse struct {
	ErrorDescription string          `json:"errorDescription"`
	Successful       string          `json:"successful"`
	Data             json.RawMessage `json:"data"`
}

func (a *WebAPI) GetSymbols() ([]ApiSymbolData, error) {
	req, err := a.newHttpRequest(http.MethodGet, apiWebSymbols, nil)
	if err != nil {
		return nil, err
	}

	symbols := make([]ApiSymbolData, 0)
	err = a.makeHttpRequest(req, &symbols)
	if err != nil {
		return nil, err
	}

	return symbols, err
}

func (a *WebAPI) GetOrderBook(paramsReq ApiOrderBookRequest) (*apiOrderBookData, error) {
	url := fmt.Sprintf(apiWebDepth, paramsReq.Symbol)
	req, err := a.newHttpRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}

	book := apiOrderBookData{}

	err = a.makeHttpRequest(req, &book)
	if err != nil {
		return nil, err
	}

	return &book, nil
}

func (a *WebAPI) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	body, err := a.sendHttpRequest(req)
	if err != nil {
		return err
	}

	response := webApiResponse{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return utils.NewHttpError(
			Exchange.Name,
			utils.UnmarshalError,
			req,
			err.Error(),
			string(body),
		).AsError()
	}

	if response.ErrorDescription != "success" {
		return utils.NewHttpError(
			Exchange.Name,
			utils.HttpRequestError,
			req,
			response.ErrorDescription,
			string(body),
		).AsError()
	}

	if responseObject != nil {
		err = json.Unmarshal(response.Data, responseObject)
		if err != nil {
			return utils.NewHttpError(
				Exchange.Name,
				utils.UnmarshalError,
				req,
				err.Error(),
				string(body),
			).AsError()
		}
	}

	return nil
}

func (a *WebAPI) sendHttpRequest(req *http.Request) ([]byte, error) {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return nil, err
	}

	body, err := ioutil.ReadAll(res.Body)

	return body, err
}

func (a *WebAPI) newHttpRequest(method string, url string, data io.Reader) (*http.Request, error) {
	req, err := http.NewRequest(method, url, data)
	if err != nil {
		return nil, err
	}

	if req.Method == "POST" {
		req.Header.Set("Content-Type", "application/json")
	}

	return req, nil
}
