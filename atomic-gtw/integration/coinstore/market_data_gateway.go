package coinstore

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	wsURL = "wss://ws.coinstore.com/s/ws"
)

type MarketDataGateway struct {
	options gateway.Options
	tickCh  chan gateway.Tick
}

type WsSubscribeMessage struct {
	Op      string   `json:"op"`
	Channel []string `json:"channel"`
	ID      int      `json:"id"`
}

type WsDepthUpdate struct {
	Level        int                  `json:"level"`
	InstrumentID int                  `json:"instrumentId"`
	Symbol       string               `json:"symbol"`
	Bids         []gateway.PriceArray `json:"b"`
	Asks         []gateway.PriceArray `json:"a"`
	Seq          int64                `json:"seq,omitempty"`
}

type WsTradeMessage struct {
	S    int         `json:"S"`
	T    string      `json:"T"`
	Data []TradeData `json:"data,omitempty"`
}

type TradeData struct {
	Channel      string  `json:"channel"`
	InstrumentID int     `json:"instrumentId"`
	Symbol       string  `json:"symbol"`
	TradeID      int     `json:"tradeId"`
	Seq          int64   `json:"seq"`
	TakerSide    string  `json:"takerSide"`
	Price        float64 `json:"price,string"`
	Volume       float64 `json:"volume,string"`
	Time         int64   `json:"time"`
	Ts           int64   `json:"ts"`
}

func NewMarketDataGateway(options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options: options,
		tickCh:  tickCh,
	}
}

func (g *MarketDataGateway) Connect() error {
	return nil
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)
	ws.SetTag("Coinstore-PublicWS")

	if err := ws.Connect(wsURL); err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	msgCh := make(chan []byte, 100)
	ws.SubscribeMessages(msgCh)
	go g.messageHandler(msgCh)
	go g.startHeartbeat(ws)

	channels := make([]string, 0, len(markets)*2)
	for _, market := range markets {
		channels = append(channels, fmt.Sprintf("%s@depth", market.Symbol))
		channels = append(channels, fmt.Sprintf("%s@trade", market.Symbol))
	}

	sub := WsSubscribeMessage{
		Op:      "SUB",
		Channel: channels,
		ID:      1,
	}

	if err := g.sendSubscription(ws, sub); err != nil {
		return fmt.Errorf("failed to subscribe to market data: %s", err)
	}

	return nil
}

func (g *MarketDataGateway) sendSubscription(ws *utils.WsClient, msg WsSubscribeMessage) error {
	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("marshal sub message: %s", err)
	}

	return ws.WriteMessage(data)
}

func (g *MarketDataGateway) messageHandler(ch chan []byte) {
	var baseMsg struct {
		T string `json:"T"`
	}

	for data := range ch {
		if err := json.Unmarshal(data, &baseMsg); err != nil {
			log.Printf("Failed to unmarshal base message: %v", err)
			continue
		}

		switch baseMsg.T {
		case "depth":
			var depthMsg WsDepthUpdate
			if err := json.Unmarshal(data, &depthMsg); err != nil {
				log.Printf("Failed to unmarshal depth update: %v", err)
				continue
			}
			g.processDepthUpdate(depthMsg.Symbol, depthMsg)

		case "trade":
			var tradeMsg WsTradeMessage
			if err := json.Unmarshal(data, &tradeMsg); err != nil {
				log.Printf("Failed to unmarshal trade update: %v", err)
				continue
			}
			if len(tradeMsg.Data) > 0 {
				for _, trade := range tradeMsg.Data {
					g.processTradeUpdate(trade)
				}
			} else {
				// If there is no trade data, it means this is a incremental trade update
				// in this case, we need to parse the msg as a single trade data
				var trade TradeData
				if err := json.Unmarshal(data, &trade); err != nil {
					log.Printf("Failed to unmarshal incremental trade update: %v", err)
					continue
				}

				g.processTradeUpdate(trade)
			}
		}
	}
}

func (g *MarketDataGateway) processDepthUpdate(symbol string, update WsDepthUpdate) {
	events := make([]gateway.Event, 0, len(update.Bids)+len(update.Asks)+1)

	events = append(events, gateway.NewSnapshotSequenceEvent(gateway.SnapshotSequence{
		Symbol: symbol,
	}))
	events = append(events, gateway.PriceArrayToDepthEvents(symbol, gateway.Bid, update.Bids)...)
	events = append(events, gateway.PriceArrayToDepthEvents(symbol, gateway.Ask, update.Asks)...)

	if len(events) > 0 {
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          events,
		}
	}
}

func (g *MarketDataGateway) processTradeUpdate(trade TradeData) {
	var direction gateway.Side
	switch trade.TakerSide {
	case "BUY", "BULL":
		direction = gateway.Bid
	case "SELL":
		direction = gateway.Ask
	default:
		log.Printf("Unknown taker side: %s", trade.TakerSide)
		return
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog: []gateway.Event{
			{
				Type: gateway.TradeEvent,
				Data: gateway.Trade{
					ID:        fmt.Sprintf("%d", trade.TradeID),
					Symbol:    trade.Symbol,
					Direction: direction,
					Price:     trade.Price,
					Amount:    trade.Volume,
					Timestamp: gateway.ParseTimestamp(trade.Time),
				},
			},
		},
	}
}

func (g *MarketDataGateway) startHeartbeat(ws *utils.WsClient) {
	ticker := time.NewTicker(30 * time.Second) // Send heartbeat every 30 seconds
	defer ticker.Stop()

	for range ticker.C {
		pingMsg := map[string]interface{}{
			"op":          "pong",
			"epochMillis": time.Now().UnixNano() / int64(time.Millisecond),
		}

		data, err := json.Marshal(pingMsg)
		if err != nil {
			log.Printf("Failed to marshal ping message: %s", err)
			continue
		}

		err = ws.WriteMessage(data)
		if err != nil {
			err := fmt.Errorf("failed to send ping: %s", err)
			panic(err)
		}
	}
}
