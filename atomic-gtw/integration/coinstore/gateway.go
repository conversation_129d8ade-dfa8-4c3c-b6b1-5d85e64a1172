package coinstore

import (
	"fmt"
	"log"
	"math"
	"strconv"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "CoinStore",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.options, g.api, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.options, g.tickCh)
	if err := g.marketDataGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.SpotSymbols()
	if err != nil {
		log.Printf("Failed to load markets from API, error [%s], loading fallbacks...", err)
		return fallbackMarkets(), nil
	}

	commonMarkets, err := symbolsToCommonMarket(res)
	if err != nil {
		return nil, err
	}

	return commonMarkets, nil
}

// Register fallback markets in case of API failure
// Coinstore get symbols API is unstable, and Cloudflare blocks requests
func fallbackMarkets() []gateway.Market {
	return []gateway.Market{
		{
			Exchange: Exchange,
			Symbol:   "BBLLUSDT",
			Pair: gateway.Pair{
				Base:  "BBLL",
				Quote: "USDT",
			},
			PriceTick:        0.01,
			AmountTick:       0.001,
			MinimumOrderSize: 1,
		},
		{
			Exchange: Exchange,
			Symbol:   "BTCUSDT",
			Pair: gateway.Pair{
				Base:  "BTC",
				Quote: "USDT",
			},
			PriceTick:        0.01,
			AmountTick:       0.000001,
			MinimumOrderSize: 0.00001,
		},
	}
}

func symbolsToCommonMarket(symbols []APISpotSymbol) ([]gateway.Market, error) {
	commonMarkets := make([]gateway.Market, 0, len(symbols))

	for _, symbol := range symbols {
		market, err := symbolToCommonMarket(symbol)
		if err != nil {
			return nil, err
		}

		commonMarkets = append(commonMarkets, market)
	}

	return commonMarkets, nil
}

func symbolToCommonMarket(symbol APISpotSymbol) (gateway.Market, error) {
	return gateway.Market{
		Exchange: Exchange,
		Symbol:   strings.ToUpper(symbol.SymbolCode),
		Pair: gateway.Pair{
			Base:  strings.ToUpper(symbol.TradeCurrencyCode),
			Quote: strings.ToUpper(symbol.QuoteCurrencyCode),
		},
		Closed:           symbol.OpenTrade == false,
		TakerFee:         symbol.TakerFee,
		MakerFee:         symbol.MakerFee,
		PriceTick:        1 / math.Pow10(int(symbol.TickSz)),
		AmountTick:       1 / math.Pow10(int(symbol.LotSz)),
		MinimumOrderSize: symbol.MinLmtSz,
		MarketType:       gateway.SpotMarket,
	}, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	limit := "100"
	if params.Limit != 0 && params.Limit < 100 {
		limit = strconv.Itoa(params.Limit)
	}

	res, err := gtw.api.Depth(market.Symbol, "3", limit)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: gateway.PriceArrayToPriceLevels(res.Asks),
		Bids: gateway.PriceArrayToPriceLevels(res.Bids),
	}

	return depthBook, nil
}
