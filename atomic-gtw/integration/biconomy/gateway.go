package biconomy

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/integration/viabtc"
)

var Exchange = gateway.Exchange{
	Name: "Biconomy",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *viabtc.MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(options, apiBase),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.options, g.api, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("account gtw connect: %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = viabtc.NewMarketDataGateway(
		Exchange,
		g.options,
		g.tickCh,
		"wss://bei.biconomy.com/ws",
		"https://www.biconomy.com",
		viabtc.V1Version,
	)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.ExchangeInfo()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0)
	for _, exchangeInfo := range res {
		commonMarkets = append(commonMarkets, exchangeInfoToMarket(exchangeInfo))
	}

	return commonMarkets, nil
}

func exchangeInfoToMarket(exchangeInfo APIExchangeInfo) gateway.Market {
	priceTick := 1 / math.Pow10(int(exchangeInfo.QuoteAssetPrecision))
	amountTick := 1 / math.Pow10(int(exchangeInfo.BaseAssetPrecision))

	return gateway.Market{
		Exchange: Exchange,
		Symbol:   exchangeInfo.Symbol,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(exchangeInfo.BaseAsset),
			Quote: strings.ToUpper(exchangeInfo.QuoteAsset),
		},
		PriceTick:  priceTick,
		AmountTick: amountTick,
		MarketType: gateway.SpotMarket,
	}
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func getLevels(levels [][]string) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel
	for _, level := range levels {
		if len(level) < 2 {
			return nil, fmt.Errorf("invalid level: %v", level)
		}
		price, _ := strconv.ParseFloat(level[0], 64)
		amount, _ := strconv.ParseFloat(level[1], 64)
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		})
	}

	return priceLevels, nil
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	var bids []gateway.PriceLevel
	var asks []gateway.PriceLevel
	depth, err := g.api.Depth(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	bids, err = getLevels(depth.Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	asks, err = getLevels(depth.Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	return gateway.DepthBook{
		Bids: bids,
		Asks: asks,
	}, nil
}
