package lbank

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type MarketDataGateway struct {
	options    gateway.Options
	marketsMap map[string]gateway.Market
	tickCh     chan gateway.Tick
	ch         chan WsGenericMessage
}

func NewMarketDataGateway(opts gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options:    opts,
		marketsMap: make(map[string]gateway.Market),
		tickCh:     tickCh,
		ch:         make(chan WsGenericMessage, 100),
	}
}

func (g *MarketDataGateway) Connect() error {
	go g.messageHandler(g.ch)

	return nil
}

func (g *MarketDataGateway) connectAndSubMarkets(markets []gateway.Market) error {
	ws := NewWsSession(g.options)
	err := ws.Connect()
	if err != nil {
		return err
	}

	ws.SetOnDisconnect(g.onDisconnect(markets))

	for _, market := range markets {
		err := ws.WriteMessage([]byte("{\"action\":\"subscribe\",\"subscribe\":\"depth\",\"depth\":\"100\",\"pair\":\"" + market.Symbol + "\"}"))
		if err != nil {
			return fmt.Errorf("failed to write depth sub: %s", err)
		}
		err = ws.WriteMessage([]byte("{\"action\":\"subscribe\",\"subscribe\":\"trade\",\"pair\":\"" + market.Symbol + "\"}"))
		if err != nil {
			return fmt.Errorf("failed to write trade sub: %s", err)
		}
	}

	ws.SubscribeMessages(g.ch, nil)

	return nil
}

func (g *MarketDataGateway) onDisconnect(markets []gateway.Market) func(disconnectErr error) {
	return func(disconnectErr error) {
		log.Printf("LBank market data gateway disconnected err: %s, reconnecting immediately...", disconnectErr)

		err := g.connectAndSubMarkets(markets)
		if err != nil {
			err = fmt.Errorf("Failed to reconnect to market data ws, err: %s", err)
			panic(err)
		}
	}
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.connectAndSubMarkets(markets)
	if err != nil {
		return err
	}

	return nil
}

type WsTrade struct {
	Ts        time.Time `json:"TS"`
	Direction string    `json:"direction"`
	Price     float64   `json:"price"`
	Volume    float64   `json:"volume"`
}

// Custom time layout to parse the timestamp without time zone information
const timeLayout = "2006-01-02T15:04:05.000"

// Custom unmarshaling for the Trade struct
func (t *WsTrade) UnmarshalJSON(data []byte) error {
	type Alias WsTrade
	aux := &struct {
		Ts string `json:"TS"`
		*Alias
	}{
		Alias: (*Alias)(t),
	}

	if err := json.Unmarshal(data, aux); err != nil {
		return err
	}

	parsedTime, err := time.ParseInLocation(timeLayout, aux.Ts, time.UTC)
	if err != nil {
		return err
	}

	t.Ts = parsedTime
	return nil
}

type WsTradeMsg struct {
	Trade WsTrade `json:"trade"`
	Pair  string  `json:"pair"`
}

type WsDepth struct {
	Asks []gateway.PriceArray `json:"asks"`
	Bids []gateway.PriceArray `json:"bids"`
}

type WsDepthMsg struct {
	Depth WsDepth `json:"depth"`
	Pair  string  `json:"pair"`
}

func (g *MarketDataGateway) messageHandler(ch chan WsGenericMessage) {
	for msg := range ch {
		switch msg.Type {
		case "depth":
			g.processDepthUpdate(msg.Data)
		case "trade":
			g.processTradeUpdate(msg.Data)
		}
	}
}

func (g *MarketDataGateway) processDepthUpdate(data []byte) {
	msg := WsDepthMsg{}
	err := json.Unmarshal(data, &msg)
	if err != nil {
		log.Printf("LBank processDepthUpdate json.Unmarshal, body: %s err: %s", string(data), err)
		return
	}

	symbol := msg.Pair
	depth := msg.Depth

	events := make([]gateway.Event, 0, len(depth.Bids)+len(depth.Asks)+1)

	events = append(events, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: symbol,
		},
	})

	for _, price := range depth.Bids {
		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Bid,
				Price:  price.Price,
				Amount: price.Amount,
			},
		})
	}

	for _, price := range depth.Asks {
		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Ask,
				Price:  price.Price,
				Amount: price.Amount,
			},
		})
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}
}

func (g *MarketDataGateway) processTradeUpdate(data []byte) {
	msg := WsTradeMsg{}
	err := json.Unmarshal(data, &msg)
	if err != nil {
		log.Printf("LBank processTradeUpdate json.Unmarshal, body: %s err: %s", string(data), err)
		return
	}

	symbol := msg.Pair
	trade := msg.Trade

	events := make([]gateway.Event, 0, 1)
	events = append(events, gateway.Event{
		Type: gateway.TradeEvent,
		Data: gateway.Trade{
			Timestamp: trade.Ts,
			Symbol:    symbol,
			ID:        tradeCheckSum(symbol, trade.Direction, trade.Ts, trade.Price, trade.Volume),
			Direction: tradeSideToGtw(trade.Direction),
			Price:     trade.Price,
			Amount:    trade.Volume,
		},
	})

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}
}

func tradeSideToGtw(side string) gateway.Side {
	if len(side) >= 3 && side[:3] == "buy" {
		return gateway.Bid
	} else if len(side) >= 4 && side[:4] == "sell" {
		return gateway.Ask
	}
	return ""
}

func tradeCheckSum(symbol, direction string, ts time.Time, price, amount float64) string {
	str := fmt.Sprintf("%s_%s_%s_%f_%f", symbol, direction, ts.Format(time.RFC3339Nano), price, amount)
	hash := md5.Sum([]byte(str))
	return fmt.Sprintf("%x", hash)
}
