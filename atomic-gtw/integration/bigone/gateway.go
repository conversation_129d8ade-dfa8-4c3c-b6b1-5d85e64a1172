package bigone

import (
	"fmt"
	"log"
	"math"
	"strconv"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "BigONE",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	api               APIClient
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPIClient(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	if err := g.accountGateway.Connect(); err != nil {
		return fmt.Errorf("account gtw connect: %s", err)
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.api, g.options, g.Markets(), g.tickCh)
	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return fmt.Errorf("failed to subscribe market data, err: %s", err)
	}

	if g.options.ApiKey != "" {
		log.Printf("Requesting account orders updates...")

		err = g.accountGateway.subscribeMarketsUpdate(markets)
		if err != nil {
			return fmt.Errorf("failed to subscribe account order updates, err: %s", err)
		}
	}

	return nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	pairs, err := g.api.AssetPairs()
	if err != nil {
		return nil, err
	}

	return pairsToCommonMarket(pairs), nil
}

func pairsToCommonMarket(pairs []APIAssetPair) []gateway.Market {
	commonMarkets := make([]gateway.Market, 0, len(pairs))

	for _, pair := range pairs {
		mkt, err := pairToCommonMarket(pair)
		if err == nil {
			commonMarkets = append(commonMarkets, mkt)
		}
	}

	return commonMarkets
}

func pairToCommonMarket(pair APIAssetPair) (mkt gateway.Market, err error) {
	priceTick := 1 / math.Pow10(int(pair.QuoteScale))
	amountTick := 1 / math.Pow10(int(pair.BaseScale))

	return gateway.Market{
		Exchange: Exchange,
		Pair: gateway.Pair{
			Base:  pair.BaseAsset.Symbol,
			Quote: pair.QuoteAsset.Symbol,
		},
		Symbol:                 pair.Name,
		TakerFee:               0.00,
		MakerFee:               0.00,
		PriceTick:              priceTick,
		AmountTick:             amountTick,
		MinimumOrderMoneyValue: pair.MinQuoteValue,
		MarketType:             gateway.SpotMarket,
	}, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func parsePriceLevelsToDepth(levels []Depth) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel

	for _, level := range levels {
		price, err := strconv.ParseFloat(level.Price, 64)
		if err != nil {
			return nil, err
		}
		amount, err := strconv.ParseFloat(level.Amount, 64)
		if err != nil {
			return nil, err
		}
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		})
	}

	return priceLevels, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	asks, err := parsePriceLevelsToDepth(depth.Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	bids, err := parsePriceLevelsToDepth(depth.Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
