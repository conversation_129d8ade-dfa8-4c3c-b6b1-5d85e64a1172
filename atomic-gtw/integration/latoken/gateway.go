package latoken

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "LAToken",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	api               *ApiClient
	ws                *WsSession
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewApiClient(options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	// Get user detail if logged in
	var userID string
	if g.options.ApiKey != "" || g.options.Cookie != "" {
		user, err := g.api.GetUser()
		if err != nil {
			return fmt.Errorf("failed to get user detail, err: %s", err)
		}

		log.Printf("LAToken user id: %s", user.ID)

		userID = user.ID
	}

	// Channels to connect websocket w/ order entry gateway and market data
	orderUpdateCh := make(chan OrderUpdate, 5)
	depthUpdateCh := make(chan DepthUpdate, 5)

	currencies, symbolsMap, marketsMap, err := g.getCurrenciesSymbolsAndMarkets()
	if err != nil {
		return fmt.Errorf("Failed to get markets, err %s", err)
	}

	// Connect to websocket
	g.ws = NewWsSession(g.options, userID, orderUpdateCh, depthUpdateCh, symbolsMap, marketsMap, currencies)
	err = g.ws.Connect()
	if err != nil {
		return err
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(depthUpdateCh, g.ws, g.tickCh)
	err = g.marketDataGateway.Connect()
	if err != nil {
		return err
	}

	// Order entry gateway
	g.accountGateway = NewAccountGateway(g.api, orderUpdateCh, currencies, g.tickCh)
	err = g.accountGateway.Init()
	if err != nil {
		return err
	}

	// Check if session is still active
	// This will also prevent the session from going stale
	if g.options.ApiKey != "" || g.options.Cookie != "" {
		go func() {
			for {
				err := g.checkSessionActive()
				if err != nil {
					panic(err)
				}

				time.Sleep(15 * time.Second)
			}
		}()
	}

	return nil
}

func (g *Gateway) checkSessionActive() error {
	user, err := g.api.GetUser()
	if err != nil {
		return fmt.Errorf("checkSessionActive failed to get user detail, err: %s", err)
	}

	if user.Status != "ACTIVE" {
		return fmt.Errorf("checkSessionActive user status was not active, instead returned: %s", user.Status)
	}

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	_, _, marketMap, err := g.getCurrenciesSymbolsAndMarkets()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(marketMap))

	for market := range marketMap {
		commonMarkets = append(commonMarkets, market)
	}

	return commonMarkets, nil
}

func (g *Gateway) getCurrenciesSymbolsAndMarkets() ([]APICurrency, map[string]gateway.Market, map[gateway.Market]APIPair, error) {
	currencies, err := g.api.GetCurrencies()
	if err != nil {
		err = fmt.Errorf("api get currencies err: %s", err)
		return nil, nil, nil, err
	}

	log.Printf("LAToken currencies loaded: %d", len(currencies))

	pairs, err := g.api.GetPairs()
	if err != nil {
		err = fmt.Errorf("api get currencies err: %s", err)
		return currencies, nil, nil, err
	}

	log.Printf("LAToken pairs loaded: %d", len(pairs))

	symbolsMap := make(map[string]gateway.Market)
	marketsMap := make(map[gateway.Market]APIPair)
	for _, pair := range pairs {
		baseCurrency, ok := findCurrency(currencies, pair.BaseCurrency)
		if !ok {
			log.Printf("%s failed to locate base currency id %s", Exchange, pair.BaseCurrency)
			continue
		}
		quoteCurrency, ok := findCurrency(currencies, pair.QuoteCurrency)
		if !ok {
			log.Printf("%s failed to locate quote currency id %s", Exchange, pair.QuoteCurrency)
			continue
		}
		symbol := pairToSymbol(baseCurrency.Tag, quoteCurrency.Tag)

		market := gateway.Market{
			Exchange: Exchange,
			Pair: gateway.Pair{
				Base:  baseCurrency.Tag,
				Quote: quoteCurrency.Tag,
			},
			Symbol:           symbol,
			TakerFee:         0.001,
			MakerFee:         0.001,
			PriceTick:        pair.PriceTick,
			AmountTick:       pair.QuantityTick,
			MinimumOrderSize: pair.MinOrderQuantity,
			MarketType:       gateway.SpotMarket,
		}

		symbolsMap[symbol] = market
		marketsMap[market] = pair
	}

	return currencies, symbolsMap, marketsMap, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
func parsePriceLevelsToDepth(levels []DepthLevels) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel

	for _, level := range levels {
		price, err := strconv.ParseFloat(level.Price, 64)
		if err != nil {
			return nil, err
		}
		amount, err := strconv.ParseFloat(level.Amount, 64)
		if err != nil {
			return nil, err
		}
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		})
	}

	return priceLevels, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	symbol := strings.ReplaceAll(market.Symbol, "_", "")
	depth, err := gtw.api.GetDepthbook(symbol[:len(symbol)/2], symbol[len(symbol)/2:], params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks, err := parsePriceLevelsToDepth(depth.Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	bids, err := parsePriceLevelsToDepth(depth.Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
