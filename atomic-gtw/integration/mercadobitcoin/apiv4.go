package mercadobitcoin

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBase = "https://api.mercadobitcoin.net"

	// private data
	apiAuthorize   = "/api/v4/authorize"
	apiAccounts    = "/api/v4/accounts"
	apiListOrders  = "/api/v4/accounts/%s/%s/orders"
	apiGetOrder    = "/api/v4/accounts/%s/%s/orders/%s"
	apiBalances    = "/api/v4/accounts/%s/balances"
	apiPlaceOrder  = "/api/v4/accounts/%s/%s/orders"
	apiCancelOrder = "/api/v4/accounts/%s/%s/orders/%s"

	// public data
	apiSymbols   = "/api/v4/symbols"
	apiOrderbook = "/api/v4/%s/orderbook"
	apiTrades    = "/api/v4/%s/trades"
)

type API struct {
	options     gateway.Options
	client      *utils.HttpClient
	accessToken string
	baseURL     string
}

func NewAPI(options gateway.Options, baseURL string) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options: options,
		client:  client,
		baseURL: baseURL,
	}
}

func (a *API) InitAuth() error {
	expiresIn, err := a.renewAPIAccessToken()
	if err != nil {
		return err
	}

	ticker := time.NewTicker(expiresIn)
	go func() {
		defer ticker.Stop()

		for range ticker.C {
			expiresIn, err = a.renewAPIAccessToken()
			if err != nil {
				log.Printf("Failed to renew api v4 access token [err: %s], retrying in 5 seconds...", err)
				ticker.Reset(5 * time.Second) // Reset the Ticker to retry after 5 seconds
			} else {
				ticker.Reset(expiresIn) // Reset the Ticker with the new expiresIn duration after a successful renewal
			}
		}
	}()

	return nil
}

func (a *API) renewAPIAccessToken() (time.Duration, error) {
	auth, err := a.Authorize(a.options.ApiKey, a.options.ApiSecret)
	if err != nil {
		return 0, err
	}

	if auth.AccessToken == "" {
		return 0, fmt.Errorf("access token returned empty")
	}

	expiresAt := time.Unix(auth.Expiration, 0)
	expiresIn := time.Until(expiresAt)

	log.Printf("MercadoBitcoin setting API access token [%s] expires in [%s]", auth.AccessToken, expiresIn)

	a.SetAccessToken(auth.AccessToken)

	return expiresIn, nil
}

func (a *API) SetAccessToken(token string) {
	a.accessToken = token
}

type APIAuthorize struct {
	AccessToken string `json:"access_token"`
	Expiration  int64  `json:"expiration"`
}

func (a *API) Authorize(login, password string) (APIAuthorize, error) {
	bodyStr := fmt.Sprintf(`{"login": "%s", "password": "%s"}`, login, password)
	req, err := a.newAPIRequest(http.MethodPost, apiAuthorize, bytes.NewBufferString(bodyStr), nil)
	if err != nil {
		return APIAuthorize{}, err
	}

	var res APIAuthorize
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return APIAuthorize{}, err
	}

	return res, nil
}

type APISymbols struct {
	BaseCurrency []string `json:"base-currency"`
	Currency     []string `json:"currency"`
	PriceScale   []int    `json:"pricescale"`
	MinMovement  []string `json:"minmovement"`
	MinVolume    []string `json:"min-volume"`
	MinCost      []string `json:"min-cost"`
	Type         []string `json:"type"`
}

func (a *API) Symbols() (APISymbols, error) {
	req, err := a.newAPIRequest(http.MethodGet, apiSymbols, nil, nil)
	if err != nil {
		return APISymbols{}, err
	}

	var res APISymbols
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return APISymbols{}, err
	}

	return res, nil
}

type APIOrderBook struct {
	Asks []gateway.PriceArray `json:"asks"`
	Bids []gateway.PriceArray `json:"bids"`
}

func (a *API) OrderBook(sym string, params gateway.GetDepthParams) (APIOrderBook, error) {
	if params.Limit == 0 {
		params.Limit = 1000
	}
	limitParam := fmt.Sprintf("%d", params.Limit)
	queryParams := url.Values{}
	queryParams.Set("limit", limitParam)
	req, err := a.newAPIRequest(http.MethodGet, fmt.Sprintf(apiOrderbook, sym), nil, nil)
	if err != nil {
		return APIOrderBook{}, err
	}

	var res APIOrderBook
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return APIOrderBook{}, err
	}

	return res, nil
}

type APITrade struct {
	TID    int64   `json:"tid"`
	Date   int64   `json:"date"`
	Type   string  `json:"type"`
	Price  float64 `json:"price,string"`
	Amount float64 `json:"amount,string"`
}

func (a *API) Trades(sym string, sinceTradeID string) ([]APITrade, error) {
	req, err := a.newAPIRequest(http.MethodGet, fmt.Sprintf(apiTrades, sym), nil, nil)
	if err != nil {
		return []APITrade{}, err
	}

	if sinceTradeID != "0" {
		params := url.Values{}
		params.Set("since", sinceTradeID)
		req.URL.RawQuery = params.Encode()
	}

	var res []APITrade
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return []APITrade{}, err
	}

	return res, nil
}

type APIAccount struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	Type         string `json:"type"`
	Currency     string `json:"currency"`
	CurrencySign string `json:"currencySign"`
}

func (a *API) ListAccounts() ([]APIAccount, error) {
	req, err := a.newAPIRequest(http.MethodGet, apiAccounts, nil, nil)
	if err != nil {
		return []APIAccount{}, err
	}

	var res []APIAccount
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return []APIAccount{}, err
	}

	return res, nil
}

type APIOrder struct {
	ID         string      `json:"id"`
	ExternalID string      `json:"externalId"`
	Instrument string      `json:"instrument"`
	Side       string      `json:"side"`
	Status     string      `json:"status"`
	Type       string      `json:"type"`
	FilledQty  float64     `json:"filledQty,string"`
	AvgPrice   json.Number `json:"avgPrice"`
	LimitPrice json.Number `json:"limitPrice"`
	Qty        float64     `json:"qty,string"`
	CreatedAt  int64       `json:"created_at"`
	UpdatedAt  int64       `json:"updated_at"`
	Executions []struct {
		ID         string      `json:"id"`
		Instrument string      `json:"instrument"`
		Side       string      `json:"side"`
		Price      json.Number `json:"price"`
		Qty        float64     `json:"qty,string"`
		FeeRate    float64     `json:"fee_rate,string"`
		ExecutedAt int64       `json:"executed_at"`
		Liquidity  string      `json:"liquidity"`
	} `json:"executions"`
}

func (a *API) ListOrders(accountID, symbol string, status string) ([]APIOrder, error) {
	var params *url.Values
	if status != "" {
		params = &url.Values{}
		params.Set("status", status)
	}

	return a.ListOrdersWithParams(accountID, symbol, params)
}

func (a *API) ListOrdersWithParams(accountID, symbol string, params *url.Values) ([]APIOrder, error) {
	if accountID == "" || symbol == "" {
		return []APIOrder{}, fmt.Errorf("accountID and symbol is required")
	}

	req, err := a.newAPIRequest(http.MethodGet, fmt.Sprintf(apiListOrders, accountID, symbol), nil, nil)
	if err != nil {
		return []APIOrder{}, err
	}

	if params != nil {
		req.URL.RawQuery = params.Encode()
	}

	var res []APIOrder
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return []APIOrder{}, err
	}

	return res, nil
}

func (a *API) GetOrder(accountID, symbol, orderID string) (APIOrder, error) {
	req, err := a.newAPIRequest(http.MethodGet, fmt.Sprintf(apiGetOrder, accountID, symbol, orderID), nil, nil)
	if err != nil {
		return APIOrder{}, err
	}

	var res APIOrder
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return APIOrder{}, err
	}

	return res, nil
}

type APIBalances struct {
	Symbol    string  `json:"symbol"`
	Total     float64 `json:"total,string"`
	Available float64 `json:"available,string"`
	OnHold    float64 `json:"on_hold,string"`
}

func (a *API) ListBalances(accountID string) ([]APIBalances, error) {
	req, err := a.newAPIRequest(http.MethodGet, fmt.Sprintf(apiBalances, accountID), nil, nil)
	if err != nil {
		return []APIBalances{}, err
	}

	var res []APIBalances
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return []APIBalances{}, err
	}

	return res, nil
}

type APIPlaceOrderRequest struct {
	ExternalID string  `json:"externalId,omitempty"`
	Type       string  `json:"type"`
	Qty        string  `json:"qty"`
	Side       string  `json:"side"`
	LimitPrice float64 `json:"limitPrice"`
	Async      bool    `json:"async"`
}

func (a *API) PlaceOrder(accountID, symbol string, r APIPlaceOrderRequest) (string, error) {
	jsonPayload, err := json.Marshal(r)
	if err != nil {
		return "", err
	}

	req, err := a.newAPIRequest(http.MethodPost, fmt.Sprintf(apiPlaceOrder, accountID, symbol), bytes.NewBuffer(jsonPayload), nil)
	if err != nil {
		return "", err
	}

	var order struct {
		OrderID string `json:"orderId"`
	}
	if err = a.makeHTTPRequest(req, &order); err != nil {
		return "", err
	}

	return order.OrderID, nil
}

func (a *API) CancelOrder(accountID, symbol string, orderID string) error {
	req, err := a.newAPIRequest(http.MethodDelete, fmt.Sprintf(apiCancelOrder, accountID, symbol, orderID), nil, nil)
	if err != nil {
		return err
	}

	return a.makeHTTPRequest(req, &struct{}{})
}

func (a *API) newAPIRequest(method, endpoint string, body io.Reader, queryParams url.Values) (*http.Request, error) {
	parsedURL, err := a.client.ParseURLRequest(a.baseURL, endpoint)
	if err != nil {
		return nil, err
	}

	if len(queryParams) > 0 {
		parsedURL.RawQuery = queryParams.Encode()
	}

	req, err := http.NewRequest(method, parsedURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	if a.accessToken != "" {
		req.Header.Set("Authorization", a.accessToken)
	}

	return req, nil
}

func (a *API) makeHTTPRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.client.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body, err: %s", err)
	}

	if err = validateHTTPRequest(body, req.Method, req.URL.Path); err != nil {
		return err
	}

	if responseObject != nil {
		err = json.Unmarshal(body, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return nil
}

func validateHTTPRequest(body []byte, method, path string) error {
	if len(body) > 0 && string(body[0]) == "[" {
		return nil
	}

	var apiResError struct {
		Code string `json:"code"`
		Msg  string `json:"message"`
	}

	if err := json.Unmarshal(body, &apiResError); err != nil {
		return fmt.Errorf("[MercadoBitcoin] %s %s failed to unmarshal json. body: %s", method, path, string(body))
	}

	if apiResError.Msg != "" {
		return fmt.Errorf("[MercadoBitcoin] %s %s failed to make http request. code: %s: msg: %s", method, path, apiResError.Code, apiResError.Msg)
	}
	return nil
}
