package mercadobitcoin

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"testing"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
	"github.com/stretchr/testify/assert"
)

var (
	errRes           = `{"code": "API|ROUTE_NOT_FOUND", "message": "This route not found"}`
	apiBalancesRes   = `[{"symbol": "BTB10","total": "0.********","available": "0.********","on_hold": "0.********"}]`
	apiOrderbookRes  = `{"asks": [["40.********","20.********"],["40.********","67.********"]],"bids": [["40.********","38.********"],["40.********","19.********"]],"timestamp": 1689891703391817077}`
	apiTradesRes     = `[{"tid": ********,"date": **********,"type": "sell","price": "144573.********","amount": "0.********"}]`
	apiListOrdersRes = `[{"id": "*********","instrument": "LTC-BRL","fee": "0.02989980","qty": "0.********","side": "sell","type": "market","filledQty": "0.********","avgPrice": "436.********","limitPrice": "0","status": "filled","executions": [{"id": "3812167","instrument": "LTC-BRL","price": 436.********,"qty": "0.********","side": "sell","fee_rate": "2.********","executed_at": **********,"liquidity": "taker"}],"created_at": **********,"updated_at": **********}]`

	apiPlaceOrderRes = `{"orderId": "01H5TB4XY8T6MQ8FP0Y1TEJ4DQ"}`

	apiBalancesResponse = []APIBalances{
		{
			Symbol:    "BTB10",
			Total:     0.********,
			Available: 0.********,
			OnHold:    0.********,
		},
	}
	accountID = "653b8c6b5871010f0dbba6ed861bc3e4ade9d25d525f199924b7b4a6e5429283"

	apiOrderbookResponse = APIOrderBook{
		Asks: []gateway.PriceArray{{40.********, 20.********}, {40.********, 67.********}},
		Bids: []gateway.PriceArray{{40.********, 38.********}, {40.********, 19.********}},
	}

	apiTradesResponse = []APITrade{
		{
			TID:    ********,
			Date:   **********,
			Type:   "sell",
			Price:  144573.********,
			Amount: 0.********,
		},
	}

	apiListOrdersResponse = []APIOrder{
		{
			ID:         "*********",
			Instrument: "LTC-BRL",
			Side:       "sell",
			Status:     "filled",
			Type:       "market",
			FilledQty:  0.********,
			AvgPrice:   "436.********",
			LimitPrice: "0",
			Qty:        0.********,
			CreatedAt:  **********,
			UpdatedAt:  **********,
			Executions: []struct {
				ID         string      `json:"id"`
				Instrument string      `json:"instrument"`
				Side       string      `json:"side"`
				Price      json.Number `json:"price"`
				Qty        float64     `json:"qty,string"`
				FeeRate    float64     `json:"fee_rate,string"`
				ExecutedAt int64       `json:"executed_at"`
				Liquidity  string      `json:"liquidity"`
			}{
				{
					ID:         "3812167",
					Instrument: "LTC-BRL",
					Side:       "sell",
					Price:      "436.********",
					Qty:        0.********,
					FeeRate:    2.********,
					ExecutedAt: **********,
					Liquidity:  "taker",
				},
			},
		},
	}
)

func newAPI(baseURL string) *API {
	return &API{
		options: gateway.Options{
			ApiSecret: "",
			ApiKey:    "",
		},
		accessToken: "",
		client:      &utils.HttpClient{},
		baseURL:     baseURL,
	}
}

func TestBalances(t *testing.T) {
	tests := []struct {
		purpose          string
		mockHTTPRes      utils.MockResponse
		expectedError    error
		expectedBalances []APIBalances
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   fmt.Sprintf(apiBalances, accountID),
				StatusCode: http.StatusOK,
				Response:   errRes,
			},
			expectedBalances: []APIBalances{},
			expectedError:    errors.New("[MercadoBitcoin] GET /api/v4/accounts/653b8c6b5871010f0dbba6ed861bc3e4ade9d25d525f199924b7b4a6e5429283/balances failed to make http request. code: API|ROUTE_NOT_FOUND: msg: This route not found"),
		},
		{
			purpose: "Success",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   fmt.Sprintf(apiBalances, accountID),
				StatusCode: http.StatusOK,
				Response:   apiBalancesRes,
			},
			expectedBalances: apiBalancesResponse,
			expectedError:    nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, tt.mockHTTPRes.StatusCode, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			balances, err := newAPI(mockServer.URL).ListBalances(accountID)
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedBalances, balances)
		})
	}
}

func TestPlaceOrder(t *testing.T) {
	tests := []struct {
		purpose         string
		mockHTTPRes     utils.MockResponse
		expectedError   error
		expectedOrderID string
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   fmt.Sprintf(apiPlaceOrder, accountID, "LTC-BRL"),
				StatusCode: http.StatusOK,
				Response:   errRes,
			},
			expectedOrderID: "",
			expectedError:   errors.New("[MercadoBitcoin] POST /api/v4/accounts/653b8c6b5871010f0dbba6ed861bc3e4ade9d25d525f199924b7b4a6e5429283/LTC-BRL/orders failed to make http request. code: API|ROUTE_NOT_FOUND: msg: This route not found"),
		},
		{
			purpose: "Success",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   fmt.Sprintf(apiPlaceOrder, accountID, "LTC-BRL"),
				StatusCode: http.StatusOK,
				Response:   apiPlaceOrderRes,
			},
			expectedOrderID: "01H5TB4XY8T6MQ8FP0Y1TEJ4DQ",
			expectedError:   nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, tt.mockHTTPRes.StatusCode, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			orderID, err := newAPI(mockServer.URL).PlaceOrder(accountID, "LTC-BRL", APIPlaceOrderRequest{
				Type:       "post-only",
				Qty:        "0.01",
				Side:       "sell",
				LimitPrice: 500,
				Async:      true,
			})
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedOrderID, orderID)
		})
	}
}

func TestCancelOrder(t *testing.T) {
	tests := []struct {
		purpose       string
		mockHTTPRes   utils.MockResponse
		expectedError error
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint: fmt.Sprintf(apiCancelOrder, accountID, "LTC-BRL", "01H5TEVJE6ZK544A6BHV04658R"),
				Response: errRes,
			},
			expectedError: errors.New("[MercadoBitcoin] DELETE /api/v4/accounts/653b8c6b5871010f0dbba6ed861bc3e4ade9d25d525f199924b7b4a6e5429283/LTC-BRL/orders/01H5TEVJE6ZK544A6BHV04658R failed to make http request. code: API|ROUTE_NOT_FOUND: msg: This route not found"),
		},
		{
			purpose: "Success",
			mockHTTPRes: utils.MockResponse{
				Endpoint: fmt.Sprintf(apiCancelOrder, accountID, "LTC-BRL", "01H5TEVJE6ZK544A6BHV04658R"),
				Response: apiPlaceOrderRes,
			},
			expectedError: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, http.StatusOK, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			err := newAPI(mockServer.URL).CancelOrder(accountID, "LTC-BRL", "01H5TEVJE6ZK544A6BHV04658R")
			assert.Equal(t, tt.expectedError, err)
		})
	}
}

func TestOrderbook(t *testing.T) {
	tests := []struct {
		purpose           string
		mockHTTPRes       utils.MockResponse
		expectedError     error
		expectedOrderbook APIOrderBook
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint: fmt.Sprintf(apiOrderbook, "LTC-BRL"),
				Response: errRes,
			},
			expectedOrderbook: APIOrderBook{},
			expectedError:     errors.New("[MercadoBitcoin] GET /api/v4/LTC-BRL/orderbook failed to make http request. code: API|ROUTE_NOT_FOUND: msg: This route not found"),
		},
		{
			purpose: "Success",
			mockHTTPRes: utils.MockResponse{
				Endpoint: fmt.Sprintf(apiOrderbook, "LTC-BRL"),
				Response: apiOrderbookRes,
			},
			expectedError:     nil,
			expectedOrderbook: apiOrderbookResponse,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, http.StatusOK, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			ob, err := newAPI(mockServer.URL).OrderBook("LTC-BRL", gateway.GetDepthParams{})
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedOrderbook, ob)
		})
	}
}

func TestTrades(t *testing.T) {
	tests := []struct {
		purpose        string
		mockHTTPRes    utils.MockResponse
		expectedError  error
		expectedTrades []APITrade
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint: fmt.Sprintf(apiTrades, "LTC-BRL"),
				Response: errRes,
			},
			expectedTrades: []APITrade{},
			expectedError:  errors.New("[MercadoBitcoin] GET /api/v4/LTC-BRL/trades failed to make http request. code: API|ROUTE_NOT_FOUND: msg: This route not found"),
		},
		{
			purpose: "Success",
			mockHTTPRes: utils.MockResponse{
				Endpoint: fmt.Sprintf(apiTrades, "LTC-BRL"),
				Response: apiTradesRes,
			},
			expectedError:  nil,
			expectedTrades: apiTradesResponse,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, http.StatusOK, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			ob, err := newAPI(mockServer.URL).Trades("LTC-BRL", "0")
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedTrades, ob)
		})
	}
}

func TestListOrders(t *testing.T) {
	tests := []struct {
		purpose        string
		mockHTTPRes    utils.MockResponse
		expectedError  error
		expectedTrades []APIOrder
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint: fmt.Sprintf(apiListOrders, accountID, "LTC-BRL"),
				Response: errRes,
			},
			expectedTrades: []APIOrder{},
			expectedError:  errors.New("[MercadoBitcoin] GET /api/v4/accounts/653b8c6b5871010f0dbba6ed861bc3e4ade9d25d525f199924b7b4a6e5429283/LTC-BRL/orders failed to make http request. code: API|ROUTE_NOT_FOUND: msg: This route not found"),
		},
		{
			purpose: "Success",
			mockHTTPRes: utils.MockResponse{
				Endpoint: fmt.Sprintf(apiListOrders, accountID, "LTC-BRL"),
				Response: apiListOrdersRes,
			},
			expectedError:  nil,
			expectedTrades: apiListOrdersResponse,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, http.StatusOK, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			ob, err := newAPI(mockServer.URL).ListOrders(accountID, "LTC-BRL", "filled")
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedTrades, ob)
		})
	}
}
