package mercadobitcoin

import (
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	insufficientBalance = "Insufficient balance to carry out the operation"
	orderProcessed      = "This order have already been processed or canceled"
	orderNotFound       = "Order not found"
)

type AccountGateway struct {
	base.AccountGateway
	options            gateway.Options
	markets            []gateway.Market
	api                *API
	trackedFills       map[string]struct{}
	trackedFillsMutex  sync.RWMutex
	trackedOrders      map[string]gateway.Market
	trackedOrdersMutex sync.RWMutex
	tickCh             chan gateway.Tick
	accountID          string
	orderTracking      *utils.OrderTracking
}

func NewAccountGateway(options gateway.Options, markets []gateway.Market, tickCh chan gateway.Tick, api *API, accountID string) *AccountGateway {
	accGtw := &AccountGateway{
		options:       options,
		markets:       markets,
		trackedFills:  make(map[string]struct{}),
		trackedOrders: make(map[string]gateway.Market),
		tickCh:        tickCh,
		api:           api,
		accountID:     accountID,
	}

	orderTracking := utils.NewOrderTracking()
	orderTracking.SetUpdateFunc(accGtw.updateOrderStatus)
	accGtw.orderTracking = orderTracking

	return accGtw
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	res, err := g.api.ListBalances(g.accountID)
	if err != nil {
		return []gateway.Balance{}, err
	}

	balances := make([]gateway.Balance, 0)
	for _, balance := range res {
		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(balance.Symbol),
			Total:     balance.Total,
			Available: balance.Available,
		})
	}

	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	res, err := g.api.ListOrders(g.accountID, market.Symbol, "working")
	if err != nil {
		return []gateway.Order{}, err
	}

	orders := make([]gateway.Order, 0)
	for _, resOrder := range res {
		orders = append(orders, mapOrderToCommon(market, resOrder))
	}

	return orders, nil
}

func (g *AccountGateway) SendOrder(order gateway.Order) (string, error) {
	var side string
	if order.Side == gateway.Bid {
		side = "buy"
	} else if order.Side == gateway.Ask {
		side = "sell"
	}

	orderType := "limit"
	if order.PostOnly {
		orderType = "post-only"
	}

	orderID, err := g.api.PlaceOrder(
		g.accountID,
		order.Market.Symbol,
		APIPlaceOrderRequest{
			ExternalID: order.ClientOrderID,
			LimitPrice: utils.FloorToTick(order.Price, order.Market.PriceTick),
			Qty:        utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
			Side:       side,
			Type:       orderType,
		},
	)
	if err != nil {
		switch {
		case strings.Contains(err.Error(), insufficientBalance):
			return "", gateway.InsufficientBalanceErr
		default:
			return "", err
		}
	}

	if orderID == "" {
		return "", fmt.Errorf("orderID returned empty")
	}

	// Start order tracking
	g.trackedOrdersMutex.Lock()
	g.trackedOrders[orderID] = order.Market
	g.trackedOrdersMutex.Unlock()

	g.orderTracking.TrackAndMonitorOrder(orderID)

	return orderID, nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	g.orderTracking.LockMonitoring(order.ID)

	err := g.api.CancelOrder(g.accountID, order.Market.Symbol, order.ID)
	if err != nil {
		switch {
		// Order in final stage, stop tracking
		case strings.Contains(err.Error(), orderProcessed):
			err = gateway.AlreadyCancelledErr
		// All errors below, are not final stage errors, we should keep tracking
		default:
			g.orderTracking.UnlockMonitoring(order.ID)
			return err
		}
	}

	g.orderTracking.UntrackOrderWithUpdate(order.ID)
	g.orderTracking.UnlockMonitoring(order.ID)

	return err
}

func (g *AccountGateway) updateOrderStatus(orderID string, _ utils.OrderTrackingStep) error {
	g.trackedOrdersMutex.RLock()
	market, ok := g.trackedOrders[orderID]
	g.trackedOrdersMutex.RUnlock()
	if !ok {
		return fmt.Errorf("order [%s] not being tracked, symbol not found", orderID)
	}

	res, err := g.api.GetOrder(g.accountID, market.Symbol, orderID)
	if err != nil {
		// Order not found, this is a problem, we should stop tracking
		if strings.Contains(err.Error(), orderNotFound) {
			g.untrackOrder(orderID)
			log.Printf("%s order tracking WARNING - order [%s] not found, stopped tracking", Exchange, orderID)
		}
		return err
	}

	events := make([]gateway.Event, 0)
	for _, exec := range res.Executions {
		g.trackedFillsMutex.RLock()
		_, isTracked := g.trackedFills[exec.ID]
		g.trackedFillsMutex.RUnlock()
		if !isTracked {
			g.trackedFillsMutex.Lock()
			g.trackedFills[exec.ID] = struct{}{}
			g.trackedFillsMutex.Unlock()

			feeAsset := ""
			feeRate := exec.FeeRate / 100
			fee := 0.0
			side := mapSideToCommon(exec.Side)
			execPrice, _ := exec.Price.Float64()

			if side == gateway.Bid {
				feeAsset = market.Pair.Base
				fee = exec.Qty * feeRate
			} else {
				feeAsset = market.Pair.Quote
				fee = exec.Qty * execPrice * feeRate
			}

			events = append(events, gateway.NewFillEvent(gateway.Fill{
				ID:            exec.ID,
				Timestamp:     time.Unix(exec.ExecutedAt, 0),
				Symbol:        market.Symbol,
				OrderID:       orderID,
				ClientOrderID: res.ExternalID,
				Side:          side,
				Amount:        exec.Qty,
				Price:         execPrice,
				Fee:           fee,
				FeeAsset:      feeAsset,
			}))
		}
	}

	// Stop tracking
	status := mapStatusToCommon(res.Status, res.FilledQty)
	if status != gateway.OrderOpen && status != gateway.OrderPartiallyFilled && status != gateway.OrderSent {
		g.untrackOrder(orderID)
	}

	if len(events) > 0 {
		g.tickCh <- gateway.TickWithEvents(events...)
	}

	return nil
}

func (g *AccountGateway) untrackOrder(orderID string) {
	g.orderTracking.UntrackOrder(orderID)
	g.trackedOrdersMutex.Lock()
	delete(g.trackedOrders, orderID)
	g.trackedOrdersMutex.Unlock()
}

func mapOrderToCommon(market gateway.Market, order APIOrder) gateway.Order {
	price, _ := order.LimitPrice.Float64()
	avgPrice, _ := order.AvgPrice.Float64()

	return gateway.Order{
		Market:           market,
		ID:               order.ID,
		ClientOrderID:    order.ExternalID,
		Side:             mapSideToCommon(order.Side),
		State:            mapStatusToCommon(order.Status, order.FilledQty),
		Amount:           order.Qty,
		Price:            price,
		AvgPrice:         avgPrice,
		FilledAmount:     order.FilledQty,
		FilledMoneyValue: order.FilledQty * avgPrice,
	}
}

func mapSideToCommon(side string) gateway.Side {
	switch side {
	case "buy":
		return gateway.Bid
	case "sell":
		return gateway.Ask
	default:
		return ""
	}
}

func mapStatusToCommon(status string, filledAmount float64) gateway.OrderState {
	switch status {
	case "pending":
		return gateway.OrderSent
	case "created":
		return gateway.OrderSent
	case "working":
		if filledAmount > 0 {
			return gateway.OrderPartiallyFilled
		}
		return gateway.OrderOpen
	case "filled":
		return gateway.OrderFullyFilled
	case "cancelled":
		return gateway.OrderCancelled
	}
	return gateway.OrderUnknown
}
