package mercadobitcoin

import (
	"fmt"
	"log"
	"strconv"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "MercadoBitcoin",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
	accountID         string
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		api:     NewAPI(options, apiBase),
		options: options,
		tickCh:  make(chan gateway.Tick, 10),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	if g.options.ApiKey != "" {
		err := g.api.InitAuth()
		if err != nil {
			return fmt.Errorf("failed to init api authentication, err: %s", err)
		}

		accounts, err := g.api.ListAccounts()
		if err != nil {
			return fmt.Errorf("failed to select account id from api, err: %s", err)
		}

		if len(accounts) == 0 {
			return fmt.Errorf("API didnt return any account, cant init API wihthout account id")
		}

		g.accountID = accounts[0].ID

		log.Printf("MercadoBitcoin returned [%d] accounts, using accounts[0] id [%s]", len(accounts), g.accountID)
	}

	g.accountGateway = NewAccountGateway(g.options, g.Markets(), g.tickCh, g.api, g.accountID)

	g.marketDataGateway = NewMarketDataGateway(g.options, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

var defaultAmountTick = 0.********

var customAmountTick = map[string]float64{}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.Symbols()
	if err != nil {
		err = fmt.Errorf("failed to load pairs: %s", err)
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(symbols.BaseCurrency))
	for i, base := range symbols.BaseCurrency {
		_type := symbols.Type[i]

		// Only include CRYPTO, UTILITY_TOKEN, DEFI
		if _type != "CRYPTO" && _type != "UTILITY_TOKEN" && _type != "DEFI" {
			continue
		}

		quote := symbols.Currency[i]
		amountTick := defaultAmountTick
		priceTick := stringToFloatWithDefault(symbols.MinMovement[i], 0.********)
		minAmount := stringToFloatWithDefault(symbols.MinVolume[i], 0)
		minOrderValue := stringToFloatWithDefault(symbols.MinCost[i], 1)

		customAmountTick, ok := customAmountTick[base]
		if ok {
			amountTick = customAmountTick
		}

		sym := fmt.Sprintf("%s-%s", base, quote)
		commonMarkets = append(commonMarkets, gateway.Market{
			Exchange:               Exchange,
			Pair:                   gateway.Pair{Base: base, Quote: quote},
			Symbol:                 sym,
			PriceTick:              priceTick,
			AmountTick:             amountTick,
			MinimumOrderSize:       minAmount,
			MinimumOrderMoneyValue: minOrderValue,
		})
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	var asks []gateway.PriceLevel
	var bids []gateway.PriceLevel

	depth, err := gtw.api.OrderBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	for _, ask := range depth.Asks {
		asks = append(asks, gateway.PriceLevel(ask))
	}
	for _, bid := range depth.Bids {
		bids = append(bids, gateway.PriceLevel(bid))
	}
	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}

func (g *Gateway) SupportedMethods() gateway.Methods {
	return gateway.Methods{
		CIDMapping: true,
	}
}

func stringToFloatWithDefault(s string, def float64) float64 {
	if s == "" {
		return def
	}
	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		log.Printf("%s parse string %s to float failed err \"%s\", using default value", Exchange, s, err)
		return def
	}
	return f

}
