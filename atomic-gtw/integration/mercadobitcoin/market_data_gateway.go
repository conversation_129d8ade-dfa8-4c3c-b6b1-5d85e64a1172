package mercadobitcoin

import (
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"strconv"
	"sync/atomic"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type MarketDataGateway struct {
	options gateway.Options
	tickCh  chan gateway.Tick
	api     *API
}

func NewMarketDataGateway(options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options: options,
		tickCh:  tickCh,
		api:     NewAPI(options, apiBase),
	}
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	var ws *WsSession
	if !g.options.PollMarketData {
		ws = NewWsSession(g.options)
		err := ws.Connect()
		if err != nil {
			return err
		}
	}

	for _, market := range markets {
		if g.options.PollMarketData {
			go g.pollMarketDataFor(market)
		} else {
			symbol := pairToV3Symbol(market.Pair)

			// Fetch order book snapshot
			if err := g.processAPIDepthUpdate(0, market); err != nil {
				return fmt.Errorf("failed to fetch initial depth snapshot for %s: %s", market.Symbol, err)
			}

			if err := ws.WriteMessage([]byte(fmt.Sprintf("{\"type\":\"subscribe\",\"subscription\":{\"name\":\"orderbook\",\"limit\":200,\"id\":\"%s\"}}", symbol))); err != nil {
				return fmt.Errorf("failed write orderbook sub msg to ws: %s", err)
			}
			if err := ws.WriteMessage([]byte(fmt.Sprintf("{\"type\":\"subscribe\",\"subscription\":{\"name\":\"trade\",\"id\":\"%s\"}}", symbol))); err != nil {
				return fmt.Errorf("failed write trade sub msg to ws: %s", err)
			}
		}
	}

	if !g.options.PollMarketData {
		ch := make(chan WsGenericMessage, 100)
		ws.SubscribeMessages(ch, nil)

		go g.messageHandler(ch)
	}

	return nil
}

func pairToV3Symbol(pair gateway.Pair) string {
	return fmt.Sprintf("%s%s", pair.Quote, pair.Base)
}

func V3SymbolToSymbol(v3Symbol string) string {
	if len(v3Symbol) < 6 {
		return ""
	}

	return fmt.Sprintf("%s-%s", v3Symbol[3:], v3Symbol[:3])
}

// pollMarketDataFor polls market data for the given market at a specified refresh interval.
func (g *MarketDataGateway) pollMarketDataFor(market gateway.Market) {
	refreshInterval := 2500 * time.Millisecond
	if g.options.RefreshIntervalMs > 0 {
		refreshInterval = time.Duration(g.options.RefreshIntervalMs * int(time.Millisecond))
	}

	log.Printf("MercadoBitcoin polling for market data of [%s] every [%s] instead of using websocket...", market.Symbol, refreshInterval)

	var seq int64

	// Poll order book updates
	bookTicker := time.NewTicker(refreshInterval)
	go func(market gateway.Market) {
		defer bookTicker.Stop()

		for range bookTicker.C {
			err := g.processAPIDepthUpdate(atomic.LoadInt64(&seq), market)
			if err != nil {
				log.Printf("MercadoBitcoin market data failed to fetch order book %s, err: %s", market.Symbol, err)
				bookTicker.Reset(5 * time.Second)
				continue
			}

			atomic.AddInt64(&seq, 1)
			bookTicker.Reset(refreshInterval)
		}
	}(market)

	// Poll trade updates
	tradeTicker := time.NewTicker(refreshInterval)
	go func(market gateway.Market) {
		defer tradeTicker.Stop()
		var sinceTID int64

		for range tradeTicker.C {
			lastTID, err := g.updateTradesFromAPI(seq, sinceTID, market)
			if err != nil {
				log.Printf("MercadoBitcoin market data failed to fetch trades %s, err: %s", market.Symbol, err)
				tradeTicker.Reset(5 * time.Second)
				continue
			}

			sinceTID = lastTID
			atomic.AddInt64(&seq, 1)
			tradeTicker.Reset(refreshInterval)
		}
	}(market)
}

func (g *MarketDataGateway) updateTradesFromAPI(seq, sinceTradeID int64, market gateway.Market) (lastTID int64, err error) {
	trades, err := g.api.Trades(market.Symbol, strconv.FormatInt(sinceTradeID, 10))
	if err != nil {
		return
	}

	// Trades in ascending order
	sort.Slice(trades, func(i, j int) bool {
		return trades[i].TID < trades[j].TID
	})

	lastTID = sinceTradeID
	if len(trades) > 0 {
		lastTID = trades[len(trades)-1].TID
	}

	g.processAPITradesUpdate(seq, market, trades)

	return lastTID, nil
}

func (g *MarketDataGateway) processAPITradesUpdate(seq int64, market gateway.Market, trades []APITrade) {
	events := make([]gateway.Event, 0, len(trades))

	for _, t := range trades {
		var side gateway.Side
		if t.Type == "buy" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		events = append(events, gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				Timestamp: time.Unix(t.Date, 0),
				Symbol:    market.Symbol,
				ID:        strconv.FormatInt(t.TID, 10),
				Direction: side,
				Price:     t.Price,
				Amount:    t.Amount,
			},
		})
	}

	g.tickCh <- gateway.Tick{
		Sequence:          seq,
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}
}

func (g *MarketDataGateway) processAPIDepthUpdate(seq int64, market gateway.Market) error {
	orderbook, err := g.api.OrderBook(market.Symbol, gateway.DefaultDepthParams)
	if err != nil {
		return err
	}

	eventLog := make([]gateway.Event, 0, len(orderbook.Asks)+len(orderbook.Bids)+1)
	eventLog = append(eventLog, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: market.Symbol,
		},
	})

	appendEventsToEventLog(&eventLog, market.Symbol, gateway.Ask, orderbook.Asks)
	appendEventsToEventLog(&eventLog, market.Symbol, gateway.Bid, orderbook.Bids)

	g.tickCh <- gateway.Tick{
		Sequence:          seq,
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
	return nil
}

func (g *MarketDataGateway) messageHandler(ch chan WsGenericMessage) {
	for msg := range ch {
		switch msg.Type {
		case "orderbook":
			if err := g.processDepthUpdate(msg); err != nil {
				log.Printf("%s error processing \"%+v : data: %s\": %s", Exchange.Name, msg, string(msg.Data), err)
			}
		case "trade":
			if err := g.processTradeUpdate(msg); err != nil {
				log.Printf("%s error processing \"%+v : data: %s\": %s", Exchange.Name, msg, string(msg.Data), err)
			}
		}
	}
}

func (g *MarketDataGateway) processTradeUpdate(msg WsGenericMessage) error {
	symbol := V3SymbolToSymbol(msg.ID)

	var trade WsTrade
	err := json.Unmarshal(msg.Data, &trade)
	if err != nil {
		return err
	}

	events := make([]gateway.Event, 0)

	var side gateway.Side
	if trade.Type == "buy" {
		side = gateway.Bid
	} else {
		side = gateway.Ask
	}

	events = append(events, gateway.Event{
		Type: gateway.TradeEvent,
		Data: gateway.Trade{
			Timestamp: time.Unix(trade.Date, 0),
			Symbol:    symbol,
			ID:        strconv.FormatInt(trade.TID, 10),
			Direction: side,
			Price:     trade.Price,
			Amount:    trade.Amount,
		},
	})

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}

	return nil
}

func (g *MarketDataGateway) processDepthUpdate(msg WsGenericMessage) error {
	symbol := V3SymbolToSymbol(msg.ID)

	var orderbook WsDepth
	err := json.Unmarshal(msg.Data, &orderbook)
	if err != nil {
		return err
	}

	eventLog := make([]gateway.Event, 0, len(orderbook.Asks)+len(orderbook.Bids)+1)

	eventLog = append(eventLog, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: symbol,
		},
	})

	appendEventsToEventLog(&eventLog, symbol, gateway.Ask, orderbook.Asks)
	appendEventsToEventLog(&eventLog, symbol, gateway.Bid, orderbook.Bids)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

// appendEventsToEventLog appends events to the eventLog slice for a given symbol and side.
func appendEventsToEventLog(eventLog *[]gateway.Event, symbol string, side gateway.Side, prices []gateway.PriceArray) {
	for _, order := range prices {
		event := gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   side,
				Price:  order.Price,
				Amount: order.Amount,
			},
		}

		*eventLog = append(*eventLog, event)
	}
}
