package cryptomkt

import (
	"fmt"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "CryptoMKT",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options, apiBaseProd),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to order entry gateway, err %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.tickCh, g.options)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return err
	}

	return nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.GetSymbols()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0)
	for symbol, data := range symbols {
		commonMarkets = append(commonMarkets, symbolToCommonMarket(symbol, data))
	}

	return commonMarkets, nil
}

func symbolToCommonMarket(symbol string, data APISymbol) gateway.Market {
	return gateway.Market{
		Exchange: Exchange,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(data.BaseCurrency),
			Quote: strings.ToUpper(data.QuoteCurrency),
		},
		Symbol:     symbol,
		TakerFee:   data.TakeRate,
		MakerFee:   data.MakeRate,
		PriceTick:  data.TickSize,
		AmountTick: data.QuantityIncrement,
		MarketType: gateway.SpotMarket,
	}
}

func priceArrayToLevel(pxs []gateway.PriceArray) []gateway.PriceLevel {
	priceLevels := make([]gateway.PriceLevel, len(pxs))
	for i, px := range pxs {
		priceLevels[i] = px.ToPriceLevel()
	}
	return priceLevels
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.GetDepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: priceArrayToLevel(depth.Asks),
		Bids: priceArrayToLevel(depth.Bids),
	}

	return depthBook, nil
}

func (g *Gateway) SupportedMethods() gateway.Methods {
	return gateway.Methods{
		CIDMapping: true,
	}
}
