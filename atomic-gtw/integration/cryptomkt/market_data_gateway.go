package cryptomkt

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewMarketDataGateway(tickCh chan gateway.Tick, options gateway.Options) *MarketDataGateway {
	return &MarketDataGateway{
		options: options,
		tickCh:  tickCh,
	}
}

type NewWsMessage struct {
	Method  string          `json:"method"`
	Channel string          `json:"ch"`
	Params  WsChannelParams `json:"params"`
	ID      int             `json:"id"`
}

type WsChannelParams struct {
	Symbols []string `json:"symbols"`
	Limit   int      `json:"limit"`
}

func (mg *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	log.Printf("%s subscribing to %d markets...", Exchange.Name, len(markets))
	ws := utils.NewWsClient()
	ws.SetProxies(mg.options.Proxies)
	err := ws.Connect(wsURLMarket)
	if err != nil {
		return fmt.Errorf("connect to market data err: %s", err)
	}

	instrumentCodes := make([]string, 0, len(markets))
	for _, market := range markets {
		instrumentCodes = append(instrumentCodes, market.Symbol)
	}

	sendWsSubRequest := func(channelName string, instrumentCodes []string, depth int) error {
		wsMessage := NewWsMessage{
			Method:  "subscribe",
			Channel: channelName,
			Params: WsChannelParams{
				Symbols: instrumentCodes,
				Limit:   depth,
			},
			ID: 1,
		}
		msgData, err := json.Marshal(wsMessage)
		if err != nil {
			return fmt.Errorf("marshal ws message err: %s", err)
		}

		if err := ws.WriteMessage(msgData); err != nil {
			return fmt.Errorf("write ws message err: %s", err)
		}

		return nil
	}

	if err := sendWsSubRequest("trades", instrumentCodes, 1000); err != nil {
		return fmt.Errorf("%s: %x", "TRADES SUB", err)
	}

	if err := sendWsSubRequest("orderbook/full", instrumentCodes, 0); err != nil {
		return fmt.Errorf("%s: %x", "ORDER BOOK SUB", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	go mg.messageHandler(ch)

	return nil
}

type WsResult struct {
	Channel       string   `json:"ch"`
	Subscriptions []string `json:"subscriptions"`
}

type WsMessage struct {
	ID       int             `json:"id"`
	Method   string          `json:"method"`
	Result   WsResult        `json:"result"`
	Channel  string          `json:"ch"`
	Snapshot json.RawMessage `json:"snapshot"`
	Update   json.RawMessage `json:"update"`
}

type WsBook struct {
	Asks []gateway.PriceArray `json:"a"`
	Bids []gateway.PriceArray `json:"b"`
}

type WsTrade struct {
	Timestamp int64   `json:"t"`
	ID        int64   `json:"i"`
	Price     float64 `json:"p,string"`
	Amount    float64 `json:"q,string"`
	Side      string  `json:"s"`
}

func (g *MarketDataGateway) messageHandler(ch chan []byte) {
	for data := range ch {
		var msg WsMessage
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		switch msg.Channel {
		case "orderbook/full":
			if len(msg.Snapshot) > 0 {
				if err := g.processDepthUpdate(msg.Snapshot, true); err != nil {
					log.Printf("%s error processing book snapshot \"%s\": %s", Exchange, data, err)
				}
			}
			if len(msg.Update) > 0 {
				if err := g.processDepthUpdate(msg.Update, false); err != nil {
					log.Printf("%s error processing book update \"%s\": %s", Exchange, data, err)
				}
			}
		case "trades":
			if len(msg.Snapshot) > 0 {
				if err := g.processTrade(msg.Snapshot); err != nil {
					log.Printf("%s error processing trades snapshot \"%s\": %s", Exchange, data, err)
				}
			}
			if len(msg.Update) > 0 {
				if err := g.processTrade(msg.Update); err != nil {
					log.Printf("%s error processing trades update \"%s\": %s", Exchange, data, err)
				}
			}
		}
	}
}

func (g *MarketDataGateway) processDepthUpdate(data json.RawMessage, snapshot bool) error {
	var bookUpdates map[string]WsBook
	err := json.Unmarshal(data, &bookUpdates)
	if err != nil {
		return fmt.Errorf("marketsUpdate unmarshal err: %s", err)
	}

	events := make([]gateway.Event, 0)
	for symbol, bookUpdate := range bookUpdates {
		if snapshot {
			events = append(events, gateway.NewSnapshotSequenceEvent(gateway.SnapshotSequence{
				Symbol: symbol,
			}))
		}

		for _, p := range bookUpdate.Bids {
			events = append(events, bookOrderToGtwEvent(symbol, gateway.Bid, p))
		}
		for _, p := range bookUpdate.Asks {
			events = append(events, bookOrderToGtwEvent(symbol, gateway.Ask, p))
		}
	}

	if len(events) > 0 {
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          events,
		}
	}

	return nil
}

func bookOrderToGtwEvent(symbol string, side gateway.Side, p gateway.PriceArray) gateway.Event {
	return gateway.NewDepthEvent(gateway.Depth{
		Symbol: symbol,
		Side:   side,
		Price:  p.Price,
		Amount: p.Amount,
	})
}

func (g *MarketDataGateway) processTrade(data json.RawMessage) error {
	var tradesUpdate map[string][]WsTrade
	err := json.Unmarshal(data, &tradesUpdate)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}

	eventLog := make([]gateway.Event, 0)
	for symbol, tradeUpdate := range tradesUpdate {
		for _, trade := range tradeUpdate {
			eventLog = append(eventLog, gateway.NewTradeEvent(gateway.Trade{
				Symbol:    symbol,
				ID:        strconv.FormatInt(trade.ID, 10),
				Direction: mapTradeSide(trade.Side),
				Price:     trade.Price,
				Amount:    trade.Amount,
				Timestamp: gateway.ParseTimestamp(trade.Timestamp),
			}))
		}
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

func mapTradeSide(side string) gateway.Side {
	switch side {
	case "buy":
		return gateway.Bid
	case "sell":
		return gateway.Ask
	default:
		return ""
	}
}
