package cryptomkt

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"regexp"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api     *API
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		api:     api,
		options: options,
		tickCh:  tickCh,
	}
}

func (g *AccountGateway) Connect() error {
	ws := utils.NewWsClient()
	if err := ws.Connect(wsURLTrading); err != nil {
		return fmt.Errorf("failed to connect to WebSocket: %w", err)
	}

	if err := g.authenticate(ws); err != nil {
		return fmt.Errorf("authentication failed: %w", err)
	}

	if err := g.subscribeToReports(ws); err != nil {
		return fmt.Errorf("failed to subscribe to reports: %w", err)
	}

	return nil
}

func (g *AccountGateway) authenticate(ws *utils.WsClient) error {
	timeStamp := strconv.FormatInt(time.Now().UnixNano()/int64(time.Millisecond), 10)
	timeStampInt, _ := strconv.Atoi(timeStamp)

	wsLoginMessage := WSLoginMessage{
		Method: "login",
		Params: WSLoginParams{
			Type:      "HS256",
			APIKey:    g.options.ApiKey,
			Timestamp: timeStampInt,
			Window:    10000,
			Signature: generateHMAC(g.options.ApiSecret, 10000),
		},
	}
	loginMsgData, err := json.Marshal(wsLoginMessage)
	if err != nil {
		return fmt.Errorf("failed to marshal login message: %w", err)
	}

	if err := ws.WriteMessage(loginMsgData); err != nil {
		return fmt.Errorf("failed to write login message: %w", err)
	}

	if err := g.authMessageHandler(ws); err != nil {
		return fmt.Errorf("auth err: %w", err)
	}

	return nil
}

func (g *AccountGateway) subscribeToReports(ws *utils.WsClient) error {
	wsReportsSubscribeMessage := WSReportsSubscribeMessage{
		Method: "spot_subscribe",
		ID:     123,
	}
	reportsSubscribeMsgData, err := json.Marshal(wsReportsSubscribeMessage)
	if err != nil {
		return fmt.Errorf("failed to marshal reports subscribe message: %w", err)
	}

	if err := ws.WriteMessage(reportsSubscribeMsgData); err != nil {
		return fmt.Errorf("failed to write reports subscribe message: %w", err)
	}

	reportsCh := make(chan []byte, 100)
	ws.SubscribeMessages(reportsCh)
	go g.reportMessageHandler(reportsCh)

	return nil
}

type WSError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type WSAuthMessageResult struct {
	Jsonrpc string  `json:"jsonrpc"`
	Result  bool    `json:"result"`
	ID      int     `json:"id"`
	Error   WSError `json:"error"`
}

type WSReportsSubscribeMessage struct {
	Method string `json:"method"`
	ID     int    `json:"id"`
}

type WSReportParams struct {
	ID            int64     `json:"id"`
	ClientOrderID string    `json:"client_order_id"`
	TradeID       int64     `json:"trade_id"`
	TradeQuantity float64   `json:"trade_quantity,string"`
	TradePrice    float64   `json:"trade_price,string"`
	TradeFee      float64   `json:"trade_fee,string"`
	TradeTaker    bool      `json:"trade_taker"`
	ReportType    string    `json:"report_type"`
	Side          string    `json:"side"`
	UpdatedAt     time.Time `json:"updated_at"`
}

type WSReportsResult struct {
	Jsonrpc string `json:"jsonrpc"`
	Method  string `json:"method"`
}
type WSSpotOrderParams struct {
	Params WSReportParams `json:"params"`
}

func (g *AccountGateway) reportMessageHandler(ch chan []byte) {
	for msg := range ch {
		var reportsResult WSReportsResult
		if err := json.Unmarshal(msg, &reportsResult); err != nil {
			log.Printf("failed to unmarshal CryptoMkt user stream order update error %s", err)
			continue
		}
		if reportsResult.Method == "spot_order" {
			var spotOrderParams WSSpotOrderParams
			if err := json.Unmarshal(msg, &spotOrderParams); err != nil {
				log.Printf("failed to unmarshal CryptoMkt user stream order update error %s", err)
				continue
			}
			if spotOrderParams.Params.ReportType == "trade" {
				if err := g.processExecutionEvent(spotOrderParams.Params); err != nil {
					log.Printf("failed to process execution event error %s", err)
				}
			}
		}
	}
}

func (g *AccountGateway) processExecutionEvent(data WSReportParams) error {
	eventLog := make([]gateway.Event, 0, 1)
	event := gateway.NewFillEvent(gateway.Fill{
		ID:        strconv.FormatInt(data.TradeID, 10),
		OrderID:   data.ClientOrderID,
		Amount:    data.TradeQuantity,
		Price:     data.TradePrice,
		Side:      mapAPISideToCommon(data.Side),
		Fee:       data.TradeFee,
		Timestamp: data.UpdatedAt,
	})

	eventLog = append(eventLog, event)
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}

type WSLoginParams struct {
	Type      string `json:"type"`
	APIKey    string `json:"api_key"`
	Timestamp int    `json:"timestamp"`
	Window    int    `json:"window"`
	Signature string `json:"signature"`
}

type WSLoginMessage struct {
	Method string        `json:"method"`
	Params WSLoginParams `json:"params"`
}

func (g *AccountGateway) authMessageHandler(ws *utils.WsClient) error {
	ch := make(chan []byte)
	ws.SubscribeMessages(ch)
	defer close(ch)
	defer ws.RemoveSubscriber(ch)

	authRes := make(chan error)
	go func() {
		for msg := range ch {
			var authResult WSAuthMessageResult
			if err := json.Unmarshal(msg, &authResult); err != nil {
				authRes <- fmt.Errorf("failed to unmarshal authentication message: %w", err)
			}
			if authResult.Result == true {
				authRes <- nil
			} else {
				authRes <- fmt.Errorf("authentication failed: %s [%d]", authResult.Error.Message, authResult.Error.Code)
			}
			return
		}
	}()

	select {
	case err := <-authRes:
		return err
	case <-time.After(5 * time.Second):
		return errors.New("authentication timeout")
	}
}

func generateHMAC(secretKey string, window int64) string {
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	message := fmt.Sprintf("%d", timestamp)
	if window != 0 {
		message += fmt.Sprintf("%d", window)
	}
	key := []byte(secretKey)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(message))
	signature := hex.EncodeToString(h.Sum(nil))
	return signature
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	accountBalance, err := g.api.GetBalance()
	if err != nil {
		return balances, err
	}

	balances = make([]gateway.Balance, 0, len(accountBalance))
	for _, balance := range accountBalance {
		balances = append(balances, gateway.Balance{
			Asset:     balance.Currency,
			Available: balance.Available,
			Total:     balance.Available + balance.Reserved,
		})
	}

	return
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrders, err := g.api.GetOpenOrders(market)
	if err != nil {
		return orders, err
	}

	// We need to filter out the returned openOrders because their API is broken
	// and it returns all open orders for all markets, not filtered by the market
	filteredOrders := make([]APIOrder, 0, len(openOrders))
	for _, order := range openOrders {
		if order.Symbol == market.Symbol {
			filteredOrders = append(filteredOrders, order)
		}
	}

	if len(filteredOrders) != len(openOrders) {
		log.Printf("%s open orders API returned orders for other markets, filtered out %d markets from response!", Exchange, len(openOrders)-len(filteredOrders))
	}

	orders = make([]gateway.Order, 0, len(openOrders))
	for _, order := range filteredOrders {
		order := mapAPIOrderToCommon(market, order)
		orders = append(orders, order)
	}

	return
}

func mapAPIOrderToCommon(market gateway.Market, o APIOrder) gateway.Order {
	return gateway.Order{
		Market: market,
		// We are mapping the ClientOrderID to the ID field because the API does not return the order ID
		// It generates a ClientOrderID if none is provided
		ID:           o.ClientOrderID,
		Side:         mapAPISideToCommon(o.Side),
		State:        mapAPIOrderStateToCommon(o.Status),
		Amount:       o.Quantity,
		Price:        o.Price,
		FilledAmount: o.QuantityCumulative,
	}
}

func mapAPISideToCommon(sd string) gateway.Side {
	if sd == "buy" {
		return gateway.Bid
	} else if sd == "sell" {
		return gateway.Ask
	} else {
		log.Printf("Cryptomkt invalid order side \"%s\"", sd)
	}

	return ""
}

func mapAPIOrderStateToCommon(st string) gateway.OrderState {
	switch st {
	case "new":
		return gateway.OrderOpen
	case "partiallyFilled":
		return gateway.OrderPartiallyFilled
	case "cancelled":
		return gateway.OrderCancelled
	case "expired":
		return gateway.OrderCancelled
	case "filled":
		return gateway.OrderFullyFilled
	}
	return gateway.OrderUnknown
}

var insuficientBalanceMatch = regexp.MustCompile(`Insufficient funds`)
var rateLimitMatch = regexp.MustCompile(`Rate limits`)

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	var side string
	if order.Side == gateway.Bid {
		side = "buy"
	} else {
		side = "sell"
	}

	newOrder := NewOrderBody{
		Symbol:        order.Market.Symbol,
		ClientOrderID: order.ClientOrderID,
		Side:          side,
		Amount:        order.Amount,
		Price:         order.Price,
		Type:          "limit",
		TimeInForce:   "GTC",
		IsPostOnly:    order.PostOnly,
	}
	res, err := g.api.PostOrder(newOrder)
	if err != nil {
		switch {
		case insuficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		case rateLimitMatch.MatchString(err.Error()):
			return "", gateway.RateLimitErr
		default:
			return "", err
		}
	}

	return res.ClientOrderID, err
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID)
}
