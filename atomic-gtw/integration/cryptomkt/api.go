package cryptomkt

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
	baseURL    string
}

const (
	apiBaseProd    = "https://api.exchange.cryptomkt.com"
	apiSymbols     = "/api/3/public/symbol"
	apiDepthBook   = "/api/3/public/orderbook/%s?depth=%d"
	apiBalance     = "/api/3/spot/balance"
	apiOpenOrders  = "/api/3/spot/order"
	apiPostOrder   = "/api/3/spot/order"
	apiCancelOrder = "/api/3/spot/order/%s"
	wsURLMarket    = "wss://api.exchange.cryptomkt.com/api/3/ws/public"
	wsURLTrading   = "wss://api.exchange.cryptomkt.com/api/3/ws/trading"
)

func NewAPI(options gateway.Options, baseURL string) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options:    options,
		httpClient: client,
		baseURL:    baseURL,
	}
}

type APISymbol struct {
	BaseCurrency      string  `json:"base_currency"`
	QuoteCurrency     string  `json:"quote_currency"`
	QuantityIncrement float64 `json:"quantity_increment,string"`
	TickSize          float64 `json:"tick_size,string"`
	TakeRate          float64 `json:"take_rate,string"`
	MakeRate          float64 `json:"make_rate,string"`
}

func (a *API) GetSymbols() (res map[string]APISymbol, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiSymbols, nil, nil, false)
	if err != nil {
		return nil, err
	}

	if err = a.makeHttpRequest(req, &res); err != nil {
		return nil, err
	}

	return res, nil
}

type APIDepthBook struct {
	Asks []gateway.PriceArray `json:"ask"`
	Bids []gateway.PriceArray `json:"bid"`
}

func (a *API) GetDepthBook(market string, params gateway.GetDepthParams) (*APIDepthBook, error) {
	if params.Limit == 0 {
		params.Limit = 100
	}
	req, err := a.newHttpRequest(http.MethodGet, fmt.Sprintf(apiDepthBook, market, params.Limit), nil, nil, false)
	if err != nil {
		return nil, err
	}

	var res APIDepthBook
	if err = a.makeHttpRequest(req, &res); err != nil {
		return nil, err
	}

	return &res, nil
}

type APIBalance struct {
	Currency  string  `json:"currency"`
	Available float64 `json:"available,string"`
	Reserved  float64 `json:"reserved,string"`
}

func (a *API) GetBalance() ([]APIBalance, error) {
	req, err := a.newHttpRequest(http.MethodGet, apiBalance, nil, nil, true)
	if err != nil {
		return nil, err
	}

	var res []APIBalance
	if err = a.makeHttpRequest(req, &res); err != nil {
		return nil, err
	}

	return res, nil
}

type APIOrder struct {
	ID                 int64   `json:"id"`
	ClientOrderID      string  `json:"client_order_id"`
	Symbol             string  `json:"symbol"`
	Side               string  `json:"side"`
	Status             string  `json:"status"`
	Type               string  `json:"type"`
	Quantity           float64 `json:"quantity,string"`
	Price              float64 `json:"price,string"`
	QuantityCumulative float64 `json:"quantity_cumulative,string"`
}

func (a *API) GetOpenOrders(market gateway.Market) ([]APIOrder, error) {
	var params = url.Values{}
	params.Add("symbol", market.Symbol)
	req, err := a.newHttpRequest(http.MethodGet, apiOpenOrders, nil, &params, true)
	if err != nil {
		return nil, err
	}

	var res []APIOrder
	if err = a.makeHttpRequest(req, &res); err != nil {
		return nil, err
	}

	return res, nil
}

type NewOrderBody struct {
	ClientOrderID string  `json:"client_order_id,omitempty"`
	Symbol        string  `json:"symbol"`
	Side          string  `json:"side"`
	Type          string  `json:"type"`
	Amount        float64 `json:"quantity"`
	Price         float64 `json:"price"`
	PostOnly      bool    `json:"post_only"`
	TimeInForce   string  `json:"time_in_force"`
	IsPostOnly    bool    `json:"is_post_only"`
}

func (a *API) PostOrder(order NewOrderBody) (res APIOrder, err error) {
	jsonData, err := json.Marshal(order)
	if err != nil {
		return res, err
	}

	req, err := a.newHttpRequest(http.MethodPost, apiPostOrder, bytes.NewReader(jsonData), nil, true)
	if err != nil {
		return res, err
	}

	if err = a.makeHttpRequest(req, &res); err != nil {
		return res, err
	}

	return res, nil
}

func (a *API) CancelOrder(orderID string) error {
	req, err := a.newHttpRequest(http.MethodDelete, fmt.Sprintf(apiCancelOrder, orderID), nil, nil, true)
	if err != nil {
		return err
	}

	var res struct{}
	if err = a.makeHttpRequest(req, &res); err != nil {
		return err
	}

	return nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}

	if res.StatusCode < 200 || res.StatusCode >= 300 {
		return fmt.Errorf("invalid status code: %d, body: %s", res.StatusCode, string(body))
	}

	err = json.Unmarshal(body, responseObject)
	if err != nil {
		return fmt.Errorf("unmarshal err [%s] body:\n%s", err, string(body))
	}

	return nil
}

func (a *API) newHttpRequest(method string, path string, data io.Reader, params *url.Values, signed bool) (*http.Request, error) {
	urlRequest := a.baseURL + path
	req, err := http.NewRequest(method, urlRequest, data)
	if err != nil {
		return nil, err
	}

	if params != nil {
		req.URL.RawQuery = params.Encode()
	}

	req.Header.Set("Content-Type", "application/json")
	if signed {
		apiKey := a.options.ApiKey
		apiSecret := a.options.ApiSecret
		queryParams := req.URL.Query().Encode()
		var body []byte
		if data != nil {
			body, err = io.ReadAll(data)
			if err != nil {
				return nil, err
			}

			// Put the body back into the request
			req.Body = io.NopCloser(bytes.NewBuffer(body))
		}
		encodedSignature, err := generateSignature(method, path, queryParams, string(body), apiKey, apiSecret, 5000)
		if err != nil {
			return nil, err
		}
		authSignature := fmt.Sprintf("HS256 %s", encodedSignature)
		req.Header.Add("Authorization", authSignature)
	}

	return req, nil
}

func generateSignature(method, path, query, body, apiKey, secretKey string, window int) (string, error) {
	var message []string
	message = append(message, method, path)
	if query != "" {
		message = append(message, "?", query)
	}
	message = append(message, body)

	// Calculate the timestamp within the sliding window
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	if window < 1000 || window > 60000 {
		window = 10000 // Default window
	}
	timestamp += int64(window)
	timestampStr := strconv.FormatInt(timestamp, 10)

	message = append(message, timestampStr)

	messageStr := strings.Join(message, "")
	mac := hmac.New(sha256.New, []byte(secretKey))
	mac.Write([]byte(messageStr))
	signature := mac.Sum(nil)
	data := []string{apiKey, fmt.Sprintf("%x", signature), timestampStr}
	base64Encoded := base64.StdEncoding.EncodeToString([]byte(strings.Join(data, ":")))
	return base64Encoded, nil
}
