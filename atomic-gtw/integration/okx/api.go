package okx

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseProd       = "https://www.okx.com"
	apiBaseProdAws    = "https://aws.okx.com"
	apiSymbols        = "/api/v5/public/instruments?instType=SPOT"
	apiAccountBalance = "/api/v5/account/balance"
	apiOpenOrders     = "/api/v5/trade/orders-pending"
	apiPlaceOrder     = "/api/v5/trade/order"
	apiCancelOrder    = "/api/v5/trade/cancel-order"
	apiDepthBook      = "/api/v5/market/books?instId=%s&sz=%d"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
	baseURL    string
}

func NewAPI(baseURL string, options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		baseURL:    baseURL,
		options:    options,
		httpClient: client,
	}
}

func (api *API) getTimestamp() string {
	return time.Now().UTC().Format("2006-01-02T15:04:05.000Z")
}

type APIResponse struct {
	Code string          `json:"code"`
	Msg  string          `json:"msg"`
	Data json.RawMessage `json:"data"`
}

type AssetDetails struct {
	Alias        string `json:"alias"`
	BaseCcy      string `json:"baseCcy"`
	Category     string `json:"category"`
	InstId       string `json:"instId"`
	InstType     string `json:"instType"`
	ListTime     string `json:"listTime"`
	LotSz        string `json:"lotSz"`
	MaxIcebergSz string `json:"maxIcebergSz"`
	MaxLmtSz     string `json:"maxLmtSz"`
	MaxMktSz     string `json:"maxMktSz"`
	MaxStopSz    string `json:"maxStopSz"`
	MaxTriggerSz string `json:"maxTriggerSz"`
	MaxTwapSz    string `json:"maxTwapSz"`
	MinSz        string `json:"minSz"`
	OptType      string `json:"optType"`
	QuoteCcy     string `json:"quoteCcy"`
	State        string `json:"state"`
	TickSz       string `json:"tickSz"`
}

func (a *API) Symbols() (symbols []AssetDetails, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiSymbols, nil, false)
	if err != nil {
		return symbols, err
	}

	err = a.makeHttpRequest(req, &symbols)
	if err != nil {
		return nil, err
	}

	return symbols, nil
}

type ApiBalances struct {
	AdjEq       string           `json:"adjEq"`
	Details     []AccountBalance `json:"details"`
	Imr         string           `json:"imr"`
	IsoEq       string           `json:"isoEq"`
	MgnRatio    string           `json:"mgnRatio"`
	Mmr         string           `json:"mmr"`
	NotionalUsd string           `json:"notionalUsd"`
	OrdFroz     string           `json:"ordFroz"`
	TotalEq     string           `json:"totalEq"`
	UTime       string           `json:"uTime"`
}

type AccountBalance struct {
	AvailBal     float64 `json:"availBal,string"`
	AvailEq      string  `json:"availEq"`
	CashBal      string  `json:"cashBal"`
	Ccy          string  `json:"ccy"`
	DisEq        string  `json:"disEq"`
	Eq           string  `json:"eq"`
	EqUsd        string  `json:"eqUsd"`
	FixedBal     string  `json:"fixedBal"`
	FrozenBal    float64 `json:"frozenBal,string"`
	SpotInUseAmt string  `json:"spotInUseAmt"`
	StgyEq       string  `json:"stgyEq"`
	Twap         string  `json:"twap"`
	UTime        string  `json:"uTime"`
}

func (a *API) AccountBalance() (resp []AccountBalance, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiAccountBalance, bytes.NewReader(nil), true)
	if err != nil {
		return nil, err
	}

	var apiBalance []ApiBalances
	if err = a.makeHttpRequest(req, &apiBalance); err != nil {
		return nil, err
	}
	if len(apiBalance) == 0 {
		return nil, errors.New("no balance returned")
	}
	resp = apiBalance[0].Details

	return resp, nil
}

type OpenOrders struct {
	AccFillSz       string  `json:"accFillSz"`
	AvgPx           string  `json:"avgPx"`
	CTime           string  `json:"cTime"`
	Category        string  `json:"category"`
	Ccy             string  `json:"ccy"`
	ClOrdId         string  `json:"clOrdId"`
	Fee             string  `json:"fee"`
	FeeCcy          string  `json:"feeCcy"`
	FillPx          string  `json:"fillPx"`
	FillSz          string  `json:"fillSz"`
	FillTime        string  `json:"fillTime"`
	InstId          string  `json:"instId"`
	InstType        string  `json:"instType"`
	Lever           string  `json:"lever"`
	OrdId           string  `json:"ordId"`
	OrdType         string  `json:"ordType"`
	Pnl             string  `json:"pnl"`
	PosSide         string  `json:"posSide"`
	Px              float64 `json:"px,string"`
	Rebate          string  `json:"rebate"`
	RebateCcy       string  `json:"rebateCcy"`
	Side            string  `json:"side"`
	SlOrdPx         string  `json:"slOrdPx"`
	SlTriggerPx     string  `json:"slTriggerPx"`
	SlTriggerPxType string  `json:"slTriggerPxType"`
	State           string  `json:"state"`
	Sz              float64 `json:"sz,string"`
	Tag             string  `json:"tag"`
	TgtCcy          string  `json:"tgtCcy"`
	TdMode          string  `json:"tdMode"`
	Source          string  `json:"source"`
	TpOrdPx         string  `json:"tpOrdPx"`
	TpTriggerPx     string  `json:"tpTriggerPx"`
	TpTriggerPxType string  `json:"tpTriggerPxType"`
	TradeId         string  `json:"tradeId"`
	ReduceOnly      string  `json:"reduceOnly"`
	QuickMgnType    string  `json:"quickMgnType"`
	AlgoClOrdId     string  `json:"algoClOrdId"`
	AlgoId          string  `json:"algoId"`
	UTime           string  `json:"uTime"`
}

func (a *API) OpenOrders(currency string) (resp []OpenOrders, err error) {
	url := apiOpenOrders + "?instId=" + currency
	req, err := a.newHttpRequest(http.MethodGet, url, bytes.NewReader(nil), true)
	if err != nil {
		return nil, err
	}

	var apiOrders []OpenOrders
	if err = a.makeHttpRequest(req, &apiOrders); err != nil {
		return nil, err
	}

	return apiOrders, nil
}

type ApiPlaceOrder struct {
	ClOrdId string `json:"clOrdId"`
	OrdId   string `json:"ordId"`
	Tag     string `json:"tag"`
	SCode   string `json:"sCode"`
	SMsg    string `json:"sMsg"`
}

func (a *API) PlaceOrder(order map[string]string) (orderID string, err error) {
	data, err := json.Marshal(order)
	if err != nil {
		return "", err
	}
	req, err := a.newHttpRequest(http.MethodPost, apiPlaceOrder, bytes.NewReader(data), true)
	if err != nil {
		return "", err
	}
	var resp []ApiPlaceOrder

	if err = a.makeHttpRequest(req, &resp); err != nil {
		return "", err
	}
	if len(resp) == 0 {
		return "", errors.New("no order id returned")
	}
	orderID = resp[0].OrdId

	return orderID, nil
}

func (a *API) CancelOrder(orderID string, symbol string) (err error) {
	data := map[string]string{
		"ordId":  orderID,
		"instId": symbol,
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	req, err := a.newHttpRequest(http.MethodPost, apiCancelOrder, bytes.NewReader(jsonData), true)
	if err != nil {
		return err
	}

	if err = a.makeHttpRequest(req, nil); err != nil {
		return err
	}

	return nil
}

type APIDepthBook struct {
	Asks [][]string `json:"asks"`
	Bids [][]string `json:"bids"`
}

func (a *API) DepthBook(symbol string, params gateway.GetDepthParams) ([]APIDepthBook, error) {
	if params.Limit == 0 {
		params.Limit = 400
	}
	req, err := a.newHttpRequest(http.MethodGet, fmt.Sprintf(apiDepthBook, symbol, params.Limit), nil, false)
	if err != nil {
		return []APIDepthBook{}, err
	}

	var res []APIDepthBook
	if err = a.makeHttpRequest(req, &res); err != nil {
		return []APIDepthBook{}, err
	}

	return res, nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	var apiRes APIResponse
	err = json.Unmarshal(body, &apiRes)
	if err != nil {
		return fmt.Errorf("unmarshal err [%s] body:\n%s", err, string(body))
	}

	if apiRes.Msg != "" {
		return fmt.Errorf("api error: %s", apiRes.Msg)
	}

	if responseObject != nil {
		err = json.Unmarshal(apiRes.Data, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", string(body), err)
		}
	}

	return nil
}

func (a *API) getSignature(requestPath string, timestamp string, method string, body string) string {
	message := timestamp + method + requestPath

	if len(body) > 0 {
		message += body
	}
	key := []byte(a.options.ApiSecret)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(message))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return signature
}

func (a *API) getSignatureWebSocket(timestamp string) (string, error) {
	method := "GET"
	requestPath := "/users/self/verify"

	signatureString := timestamp + method + requestPath

	hmacSha256 := hmac.New(sha256.New, []byte(a.options.ApiSecret))
	if _, err := hmacSha256.Write([]byte(signatureString)); err != nil {
		return "", err
	}

	signatureBytes := hmacSha256.Sum(nil)
	signature := base64.StdEncoding.EncodeToString(signatureBytes)

	return signature, nil
}

func (a *API) newHttpRequest(method string, path string, data io.Reader, signed bool) (*http.Request, error) {
	urlRequest := a.baseURL + path
	req, err := http.NewRequest(method, urlRequest, data)
	if err != nil {
		return nil, err
	}

	if signed {
		timestamp := a.getTimestamp()
		if err != nil {
			return nil, err
		}
		body, err := ioutil.ReadAll(req.Body)
		if err != nil {
			return nil, err
		}

		// Reset the request body to the original content
		req.Body = ioutil.NopCloser(bytes.NewBuffer(body))

		signature := a.getSignature(path, timestamp, method, string(body))
		req.Header.Set("OK-ACCESS-KEY", a.options.ApiKey)
		req.Header.Set("OK-ACCESS-SIGN", signature)
		req.Header.Set("OK-ACCESS-TIMESTAMP", timestamp)
		req.Header.Set("OK-ACCESS-PASSPHRASE", a.options.ApiPassphrase)
		req.Header.Set("Content-Type", "application/json; charset=utf-8")
	}

	return req, nil
}
