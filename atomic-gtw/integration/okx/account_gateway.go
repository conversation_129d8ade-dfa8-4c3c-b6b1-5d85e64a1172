package okx

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api              *API
	options          gateway.Options
	tickCh           chan gateway.Tick
	baseUrlWs        string
	lastFillIds      map[string]string
	lastFillIdsMutex sync.Mutex
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick, baseUrlWs string) *AccountGateway {
	return &AccountGateway{
		api:         api,
		options:     options,
		tickCh:      tickCh,
		baseUrlWs:   baseUrlWs,
		lastFillIds: make(map[string]string),
	}
}

func (g *AccountGateway) Connect() error {
	return nil
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	accountBalance, err := g.api.AccountBalance()
	if err != nil {
		return balances, err
	}

	balances = make([]gateway.Balance, 0, len(accountBalance))
	for _, balance := range accountBalance {
		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(balance.Ccy),
			Available: balance.AvailBal,
			Total:     balance.AvailBal + balance.FrozenBal,
		})
	}

	return
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrders, err := g.api.OpenOrders(market.Symbol)
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders))
	for _, order := range openOrders {
		orders = append(orders, mapAPIOrderToCommon(order, market))
	}

	return
}

func mapAPIOrderToCommon(o OpenOrders, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market: market,
		ID:     o.OrdId,
		Side:   mapAPIOrderTypeToCommonSide(o.Side),
		State:  mapAPIOrderStateToCommon(o.State),
		Amount: o.Sz,
		Price:  o.Px,
	}
}

func mapAPIOrderTypeToCommonSide(orderType string) gateway.Side {
	if orderType == "buy" {
		return gateway.Bid
	} else if orderType == "sell" {
		return gateway.Ask
	} else {
		log.Printf("Okx invalid order side \"%s\"", orderType)
		return ""
	}
}

type APIOrderReq struct {
	InstId  string `json:"instId"`
	TdMode  string `json:"tdMode"`
	Side    string `json:"side"`
	OrdType string `json:"ordType"`
	Px      string `json:"px"`
	Sz      string `json:"sz"`
}

const allOperationsFailedErr = "All operations failed"

func (g *AccountGateway) SendOrder(order gateway.Order) (string, error) {
	var side string
	if order.Side == gateway.Bid {
		side = "buy"
	} else {
		side = "sell"
	}
	params := map[string]string{
		"instId":  order.Market.Symbol,
		"tdMode":  "cash",
		"side":    side,
		"ordType": "limit",
		"sz":      utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		"px":      utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
	}
	if order.PostOnly {
		params["ordType"] = "post_only"
	}

	if g.options.BrokerID != "" {
		params["tag"] = g.options.BrokerID
	}

	orderID, err := g.api.PlaceOrder(params)
	if err != nil {
		switch {
		case strings.Contains(err.Error(), allOperationsFailedErr):
			return orderID, &gateway.OrderNotOpenedErr{Err: err}
		default:
			return orderID, err
		}
	}

	return orderID, err
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID, order.Market.Symbol)
}

type WsAuthRequest struct {
	Op   string `json:"op"`
	Args []struct {
		ApiKey     string `json:"apiKey"`
		Passphrase string `json:"passphrase"`
		Timestamp  string `json:"timestamp"`
		Sign       string `json:"sign"`
	} `json:"args"`
}

type WsUserOrderRequest struct {
	Op   string `json:"op"`
	Args []struct {
		Channel  string `json:"channel"`
		InstType string `json:"instType"`
		InstId   string `json:"instId"`
	} `json:"args"`
}

func (g *AccountGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()

	err := ws.Connect(g.baseUrlWs)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	timeStamp := fmt.Sprintf("%d", time.Now().Unix())
	signature, err := g.api.getSignatureWebSocket(timeStamp)
	if err != nil {
		return fmt.Errorf("failed to get signature, err: %s", err)
	}

	authRequest := WsAuthRequest{
		Op: "login",
		Args: []struct {
			ApiKey     string `json:"apiKey"`
			Passphrase string `json:"passphrase"`
			Timestamp  string `json:"timestamp"`
			Sign       string `json:"sign"`
		}{
			{
				ApiKey:     g.options.ApiKey,
				Passphrase: g.options.ApiPassphrase,
				Timestamp:  timeStamp,
				Sign:       signature,
			},
		},
	}
	dataAuthRequest, err := json.Marshal(authRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal sub request, err: %s", err)
	}
	if err = ws.WriteMessage(dataAuthRequest); err != nil {
		return fmt.Errorf("failed write account msg to ws: %s", err)
	}

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)
	authErr := make(chan error)
	go func() {
		msg := <-ch

		var err error
		var res WsAuthResponse
		if unmarshalErr := json.Unmarshal(msg, &res); err != nil {
			err = fmt.Errorf("failed to unmarshal account response, err: %s", unmarshalErr)
		}

		if res.Event == "error" {
			err = fmt.Errorf("failed to subscribe to account, err: %s", res.Msg)
		}

		authErr <- err
	}()

	select {
	case err := <-authErr:
		if err != nil {
			closeErr := ws.Close()
			if closeErr != nil {
				log.Printf("Failed to close ws connection after auth err: %s, authErr: %s", closeErr, err)
			}

			return fmt.Errorf("auth err: %s", err)
		}
	case <-time.After(5 * time.Second):
		err := ws.Close()
		if err != nil {
			log.Printf("Failed to close ws connection after timeout: %s", err)
		}

		return fmt.Errorf("Timed out waiting for auth response")
	}

	for _, market := range markets {
		orderRequest := WsUserOrderRequest{
			Op: "subscribe",
			Args: []struct {
				Channel  string `json:"channel"`
				InstType string `json:"instType"`
				InstId   string `json:"instId"`
			}{
				{
					Channel:  "orders",
					InstType: "SPOT",
					InstId:   market.Symbol,
				},
			},
		}
		dataOrderRequest, err := json.Marshal(orderRequest)
		if err != nil {
			return fmt.Errorf("failed to marshal sub request, err: %s", err)
		}
		if err := ws.WriteMessage(dataOrderRequest); err != nil {
			return fmt.Errorf("failed write order msg to ws: %s", err)
		}
	}

	pongCh := make(chan bool)
	go g.messageHandler(ch, pongCh)
	go websocketPinger(ws, pongCh, "accGtw")

	return nil
}

type WsAuthResponse struct {
	Event string `json:"event"`
	Msg   string `json:"msg"`
	Arg   struct {
		Channel  string `json:"channel"`
		InstType string `json:"instType"`
		InstId   string `json:"instId"`
		Uid      string `json:"uid"`
	} `json:"arg"`
	Data json.RawMessage `json:"data"`
}

func (g *AccountGateway) messageHandler(ch chan []byte, pongCh chan bool) {
	for data := range ch {
		if string(data) == "pong" {
			pongCh <- true
			continue
		}

		var msg WsAuthResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		if msg.Event == "error" {
			log.Printf("Failed to login to WS [%s] err [%s]", g.baseUrlWs, string(data))
			continue
		}

		switch msg.Arg.Channel {
		case "error":
			log.Printf("WS [%s] msg w/ error msg: %s", g.baseUrlWs, string(data))
			continue
		case "orders":
			if msg.Arg.InstType != "SPOT" {
				continue
			}
			if err := g.processOrderUpdates(msg.Data); err != nil {
				log.Printf("%s error processing matches \"%s\": %s", g.baseUrlWs, data, err)
			}
		default:
			log.Printf("Okx unprocessable message type [%s], data [%s]", msg.Data, string(data))
		}
	}
}

type FillDetails struct {
	Price  float64
	Amount float64
	Fee    float64
	Time   time.Time
}

func (o WsOrder) FillDetails() (details FillDetails, err error) {
	if o.FillPx != "" {
		details.Price, err = strconv.ParseFloat(o.FillPx, 64)
		if err != nil {
			return details, fmt.Errorf("fill px parse float err: %s", err)
		}
	}
	if o.FillSz != "" {
		details.Amount, err = strconv.ParseFloat(o.FillSz, 64)
		if err != nil {
			return details, fmt.Errorf("fill sz parse float err: %s", err)
		}
	}
	if o.FillFee != "" {
		details.Fee, err = strconv.ParseFloat(o.FillFee, 64)
		if err != nil {
			return details, fmt.Errorf("fill fee parse float err: %s", err)
		}
	}
	if o.FillTime != "" {
		fillTime, err := strconv.ParseInt(o.FillTime, 10, 64)
		if err != nil {
			return details, fmt.Errorf("fill time parse int err: %s", err)
		}

		details.Time = time.Unix(0, fillTime*int64(time.Millisecond))
	}

	return details, err
}

type WsOrder struct {
	AccFillSz       string `json:"accFillSz"`
	AmendResult     string `json:"amendResult"`
	AvgPx           string `json:"avgPx"`
	CTime           string `json:"cTime"`
	Category        string `json:"category"`
	Ccy             string `json:"ccy"`
	ClOrdId         string `json:"clOrdId"`
	Code            string `json:"code"`
	ExecType        string `json:"execType"`
	Fee             string `json:"fee"`
	FeeCcy          string `json:"feeCcy"`
	FillFee         string `json:"fillFee"`
	FillFeeCcy      string `json:"fillFeeCcy"`
	FillNotionalUsd string `json:"fillNotionalUsd"`
	FillPx          string `json:"fillPx"`
	FillSz          string `json:"fillSz"`
	FillTime        string `json:"fillTime"`
	InstId          string `json:"instId"`
	InstType        string `json:"instType"`
	Lever           string `json:"lever"`
	Msg             string `json:"msg"`
	NotionalUsd     string `json:"notionalUsd"`
	OrdId           string `json:"ordId"`
	OrdType         string `json:"ordType"`
	Pnl             string `json:"pnl"`
	PosSide         string `json:"posSide"`
	Px              string `json:"px"`
	Rebate          string `json:"rebate"`
	RebateCcy       string `json:"rebateCcy"`
	ReduceOnly      string `json:"reduceOnly"`
	ReqId           string `json:"reqId"`
	Side            string `json:"side"`
	SlOrdPx         string `json:"slOrdPx"`
	SlTriggerPx     string `json:"slTriggerPx"`
	SlTriggerPxType string `json:"slTriggerPxType"`
	Source          string `json:"source"`
	State           string `json:"state"`
	Sz              string `json:"sz"`
	Tag             string `json:"tag"`
	TdMode          string `json:"tdMode"`
	TgtCcy          string `json:"tgtCcy"`
	TpOrdPx         string `json:"tpOrdPx"`
	TpTriggerPx     string `json:"tpTriggerPx"`
	TpTriggerPxType string `json:"tpTriggerPxType"`
	TradeId         string `json:"tradeId"`
	QuickMgnType    string `json:"quickMgnType"`
	AlgoClOrdId     string `json:"algoClOrdId"`
	AlgoId          string `json:"algoId"`
	AmendSource     string `json:"amendSource"`
	CancelSource    string `json:"cancelSource"`
	UTime           string `json:"uTime"`
}

func checkOrderStateFinal(state string) bool {
	if state == "live" || state == "partially_filled" {
		return false
	}

	return true
}

func mapAPISideToGtw(side string) gateway.Side {
	if side == "buy" {
		return gateway.Bid
	} else if side == "sell" {
		return gateway.Ask
	}

	return ""
}

func (g *AccountGateway) processOrderUpdates(data []byte) error {
	if data == nil {
		return nil
	}

	var orders []WsOrder
	err := json.Unmarshal(data, &orders)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}

	eventLog := make([]gateway.Event, 0, len(orders))
	for _, order := range orders {
		fillID := order.TradeId
		orderID := order.OrdId
		finalOrderUpdate := checkOrderStateFinal(order.State)

		g.lastFillIdsMutex.Lock()
		lastFillID, _ := g.lastFillIds[orderID]
		// Check if repeated fill, this might occur if the order update
		// was triggered by a cancel
		if fillID == lastFillID {
			if finalOrderUpdate {
				// Cleanup, no more updates expected
				delete(g.lastFillIds, orderID)
			}
			g.lastFillIdsMutex.Unlock()
			continue
		}
		g.lastFillIds[orderID] = fillID
		g.lastFillIdsMutex.Unlock()

		fillDetails, err := order.FillDetails()
		if err != nil {
			log.Printf("%s parse order update fill details err: %s", Exchange, err)
			continue
		}

		event := gateway.Event{
			Type: gateway.FillEvent,
			Data: gateway.Fill{
				Timestamp: fillDetails.Time,
				ID:        fillID,
				OrderID:   orderID,
				Symbol:    order.InstId,
				Side:      mapAPISideToGtw(order.Side),
				Price:     fillDetails.Price,
				Amount:    fillDetails.Amount,
				// Their fee is returned as a negative, we need to multiply by -1
				// Since if the fee is positive, it might mean we received a rebate
				Fee:      -fillDetails.Fee,
				FeeAsset: order.FillFeeCcy,
			},
		}

		eventLog = append(eventLog, event)

	}
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}
