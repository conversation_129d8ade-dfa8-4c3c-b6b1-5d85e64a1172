package okx

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	baseURL string
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewMarketDataGateway(baseURL string, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		baseURL: baseURL,
		options: options,
		tickCh:  tickCh,
	}
}

type WsRequest struct {
	Op   string `json:"op"`
	Args []struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"args"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()

	err := ws.Connect(g.baseURL)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	for _, market := range markets {
		bookRequest := WsRequest{
			Op: "subscribe",
			Args: []struct {
				Channel string `json:"channel"`
				InstId  string `json:"instId"`
			}{
				{
					Channel: "books",
					InstId:  market.Symbol,
				},
				{
					Channel: "trades",
					InstId:  market.Symbol,
				},
			},
		}
		data, err := json.Marshal(bookRequest)
		if err != nil {
			return fmt.Errorf("failed to marshal sub request, err: %s", err)
		}

		if err := ws.WriteMessage(data); err != nil {
			return fmt.Errorf("failed write sub msg to ws: %s", err)
		}
	}

	ch := make(chan []byte, 100)
	pongCh := make(chan bool)
	ws.SubscribeMessages(ch)
	go g.messageHandler(ch, pongCh)
	go websocketPinger(ws, pongCh, "mktdGtw")

	return nil
}

func websocketPinger(ws *utils.WsClient, pongCh chan bool, source string) {
	for {
		select {
		case <-pongCh:
			// Reset ping timer
		case <-time.After(2 * time.Second):
			err := ws.WriteMessage([]byte("ping"))
			if err != nil {
				panic(fmt.Errorf("%s %s ws failed to send ping, err: %s", Exchange.Name, source, err))
			}

			// Check for pong response
			select {
			case <-pongCh:
			case <-time.After(3 * time.Second):
				panic(fmt.Errorf("%s %s pong not received after 3 seconds...", Exchange.Name, source))
			}
		}
	}
}

type WsResponse struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
	Action string          `json:"action"`
	Data   json.RawMessage `json:"data"`
}

type WsTrade struct {
	InstId  string  `json:"instId"`
	TradeId string  `json:"tradeId"`
	Px      float64 `json:"px,string"`
	Sz      float64 `json:"sz,string"`
	Side    string  `json:"side"`
	Ts      string  `json:"ts"`
}

func (t *WsTrade) Time() (time.Time, error) {
	// Parses the t.Ts which is a string of a unix timestamp in milliseconds
	ts, err := strconv.ParseInt(t.Ts, 10, 64)
	if err != nil {
		return time.Time{}, err
	}

	return time.Unix(0, ts*int64(time.Millisecond)), nil
}

func (g *MarketDataGateway) messageHandler(ch chan []byte, pongCh chan bool) {
	for data := range ch {
		if string(data) == "pong" {
			pongCh <- true
			continue
		}

		var msg WsResponse
		err := json.Unmarshal(data, &msg)
		if err != nil {
			log.Printf("Failed to unmarhsal WsMessage [%s] err [%s]", string(data), err)
			continue
		}

		switch msg.Arg.Channel {
		case "error":
			log.Printf("WS [%s] msg w/ error msg: %s", g.baseURL, string(data))
			continue
		case "trades":
			if err := g.processTradeUpdates(msg.Data); err != nil {
				log.Printf("%s error processing matches \"%s\": %s", g.baseURL, data, err)
			}

		case "books":
			if msg.Action == "snapshot" {
				err := g.processSnapshotMsg(data, msg.Arg.InstId)
				if err != nil {
					log.Printf("%s error processing snapshot \"%s\": %s", g.baseURL, data, err)
				}
			} else if msg.Action == "update" {
				err := g.processBookUpdateMsg(data, msg.Arg.InstId)
				if err != nil {
					log.Printf("%s error processing book update \"%s\": %s", g.baseURL, data, err)
				}
			}
		default:
			log.Printf("Okx unprocessable message type [%s], data [%s]", msg.Data, string(data))
		}
	}
}

type WsResponseUpdate struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
	Action string         `json:"action"`
	Data   []WsBookUpdate `json:"data"`
}

type WsBookUpdate struct {
	Asks     []gateway.PriceArray `json:"asks"`
	Bids     []gateway.PriceArray `json:"bids"`
	Ts       string               `json:"ts"`
	Checksum int                  `json:"checksum"`
}

type WsResponseSnapShot struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
	Action string       `json:"action"`
	Data   []WsSnapShot `json:"data"`
}

type WsSnapShot struct {
	Asks     []gateway.PriceArray `json:"asks"`
	Bids     []gateway.PriceArray `json:"bids"`
	Ts       string               `json:"ts"`
	Checksum int                  `json:"checksum"`
}

func (g *MarketDataGateway) processSnapshotMsg(data []byte, InstId string) error {
	var wsResponse WsResponseSnapShot
	err := json.Unmarshal(data, &wsResponse)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}
	if len(wsResponse.Data) == 0 {
		return fmt.Errorf("no data")
	}
	for _, dt := range wsResponse.Data {
		g.processDepthUpdate(InstId, dt.Bids, dt.Asks, true)
	}

	return nil
}

func (g *MarketDataGateway) processBookUpdateMsg(data []byte, instId string) error {
	var wsResponse WsResponseUpdate
	err := json.Unmarshal(data, &wsResponse)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}
	if len(wsResponse.Data) == 0 {
		return fmt.Errorf("no data")
	}
	for _, dt := range wsResponse.Data {
		g.processDepthUpdate(instId, dt.Bids, dt.Asks, false)
	}

	return nil
}

func (g *MarketDataGateway) processDepthUpdate(symbol string, bids []gateway.PriceArray, asks []gateway.PriceArray, snapshot bool) {
	eventLog := make([]gateway.Event, 0, len(bids)+len(asks)+1)
	appendEvents := func(symbol string, side gateway.Side, prices []gateway.PriceArray) {
		for _, order := range prices {
			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  order.Price,
					Amount: order.Amount,
				},
			}

			eventLog = append(eventLog, event)
		}
	}

	if snapshot {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: symbol,
			},
		})
	}

	appendEvents(symbol, gateway.Ask, asks)
	appendEvents(symbol, gateway.Bid, bids)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

func (g *MarketDataGateway) processTradeUpdates(data []byte) error {
	if data == nil {
		return nil
	}
	var matches []WsTrade
	err := json.Unmarshal(data, &matches)
	if err != nil {
		return fmt.Errorf("unmarshal err: %s", err)
	}

	eventLog := make([]gateway.Event, 0, 1)

	for _, match := range matches {
		var side gateway.Side
		if match.Side == "buy" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		tradeTime, err := match.Time()
		if err != nil {
			log.Printf("%s error parsing trade time %s: %s", Exchange, match.Ts, err)
		}

		event := gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				Timestamp: tradeTime,
				Symbol:    match.InstId,
				ID:        match.TradeId,
				Direction: side,
				Price:     match.Px,
				Amount:    match.Sz,
			},
		}

		eventLog = append(eventLog, event)

	}
	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}
