package okx

import (
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "OKX",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	tickCh            chan gateway.Tick
	api               *API
	marketDataGateway *MarketDataGateway
}

func NewGateway(options gateway.Options) gateway.Gateway {
	var apiBaseURL string
	if options.UseAWSEndpoints {
		apiBaseURL = apiBaseProdAws
	} else {
		apiBaseURL = apiBaseProd
	}

	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(apiBaseURL, options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

const wsURL = "wss://ws.okx.com:8443/ws/v5/public"
const wsURLAws = "wss://wsaws.okx.com:8443/ws/v5/public"
const wsURLAuth = "wss://ws.okx.com:8443/ws/v5/private"
const wsURLAuthAws = "wss://wsaws.okx.com:8443/ws/v5/private"

func (g *Gateway) Connect() error {
	var wsUrlForMktd string
	var wsUrlForAuth string
	if g.options.UseAWSEndpoints {
		wsUrlForMktd = wsURLAws
		wsUrlForAuth = wsURLAuthAws
	} else {
		wsUrlForMktd = wsURL
		wsUrlForAuth = wsURLAuth
	}

	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh, wsUrlForAuth)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	g.marketDataGateway = NewMarketDataGateway(wsUrlForMktd, g.options, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return fmt.Errorf("Failed to subscribe to market data, err %s", err)
	}

	if g.options.ApiKey != "" {
		err := g.accountGateway.SubscribeMarkets(markets)
		if err != nil {
			return fmt.Errorf("Failed to subscribe to account updates, err %s", err)
		}
	}
	return nil
}

func (g *Gateway) Close() error {
	return gateway.NotImplementedErr
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.Symbols()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(symbols))
	for _, symbol := range symbols {
		if market, err := symbolToCommonMarket(symbol); err != nil {
			log.Printf("%s failed to map symbol %s to gateway.Market, err: %s", Exchange, symbol.InstId, err)
		} else {
			commonMarkets = append(commonMarkets, market)
		}
	}

	return commonMarkets, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func symbolToCommonMarket(symbol AssetDetails) (gateway.Market, error) {
	priceTick, err := strconv.ParseFloat(symbol.TickSz, 64)
	if err != nil {
		return gateway.Market{}, fmt.Errorf("parse priceTick \"%s\", err: %s", symbol.TickSz, err)
	}
	amountTick, err := strconv.ParseFloat(symbol.LotSz, 64)
	if err != nil {
		return gateway.Market{}, fmt.Errorf("parse amountTick \"%s\", err: %s", symbol.TickSz, err)
	}
	minOrderSize, err := strconv.ParseFloat(symbol.MinSz, 64)
	if err != nil {
		return gateway.Market{}, fmt.Errorf("parse minOrderSize \"%s\", err: %s", symbol.TickSz, err)
	}

	return gateway.Market{
		Exchange: Exchange,
		Symbol:   symbol.InstId,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(symbol.BaseCcy),
			Quote: strings.ToUpper(symbol.QuoteCcy),
		},
		TakerFee:         0.001,
		MakerFee:         0.001,
		PriceTick:        priceTick,
		AmountTick:       amountTick,
		MinimumOrderSize: minOrderSize,
	}, nil
}

func mapAPIOrderStateToCommon(st string) gateway.OrderState {
	switch st {
	case "live":
		return gateway.OrderOpen
	case "partially_filled":
		return gateway.OrderPartiallyFilled
	case "filled":
		return gateway.OrderFullyFilled
	case "canceled":
		return gateway.OrderCancelled
	case "closed":
		return gateway.OrderClosed
	}
	return gateway.OrderUnknown
}

func parsePriceLevelsToDepth(levels [][]string) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel
	for _, level := range levels {
		if len(level) < 2 {
			return nil, fmt.Errorf("invalid price level: %v", level)
		}
		price, _ := strconv.ParseFloat(level[0], 64)
		amount, _ := strconv.ParseFloat(level[1], 64)
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		})
	}
	return priceLevels, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	var asks []gateway.PriceLevel
	var bids []gateway.PriceLevel

	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	asks, err = parsePriceLevelsToDepth(depth[0].Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	bids, err = parsePriceLevelsToDepth(depth[0].Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
