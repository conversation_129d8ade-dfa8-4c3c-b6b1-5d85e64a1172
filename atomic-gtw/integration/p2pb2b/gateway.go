package p2pb2b

import (
	"fmt"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/integration/viabtc"
)

var Exchange = gateway.Exchange{
	Name: "P2PB2B",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *viabtc.MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		api:     NewAPI(options),
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.options, g.Markets(), g.api, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	//g.marketDataGateway = NewMarketDataGateway(g.options, g.Markets(), g.api, g.tickCh)
	g.marketDataGateway = viabtc.NewMarketDataGateway(
		Exchange,
		g.options,
		g.tickCh,
		"wss://apiws.p2pb2b.com/",
		"https://p2pb2b.com",
		viabtc.V1Version,
	)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	commonMarkets := make([]gateway.Market, 0)

	res, err := g.api.Markets()
	if err != nil {
		return nil, err
	}

	for _, market := range res {
		commonMarkets = append(commonMarkets, gateway.Market{
			Exchange: Exchange,
			Symbol:   market.Name,
			Pair: gateway.Pair{
				Base:  market.Stock,
				Quote: market.Money,
			},
			TakerFee:         0.002,
			MakerFee:         0.002,
			PriceTick:        market.Limits.TickSize,
			AmountTick:       market.Limits.StepSize,
			MinimumOrderSize: market.Limits.MinAmount,
		})
	}

	// We need to add RIB_USDT manually if not present
	// They keep removing from the API list when we fall behind their trading KPIs
	if _, ok := gateway.MarketBySymbol(commonMarkets, "RIB_USDT"); !ok {
		commonMarkets = append(commonMarkets, gateway.Market{
			Exchange: Exchange,
			Symbol:   "RIB_USDT",
			Pair: gateway.Pair{
				Base:  "RIB",
				Quote: "USDT",
			},
			TakerFee:         0.002,
			MakerFee:         0.002,
			PriceTick:        0.00000001,
			AmountTick:       0.1,
			MinimumOrderSize: 100,
		})
	}

	return commonMarkets, nil
}

func parsePriceLevelsToDepth(levels []gateway.PriceArray) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel

	for _, level := range levels {
		priceLevels = append(priceLevels, gateway.PriceLevel(level))
	}

	return priceLevels, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.Depth(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks, err := parsePriceLevelsToDepth(depth.Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	bids, err := parsePriceLevelsToDepth(depth.Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
