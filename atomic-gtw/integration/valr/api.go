package valr

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBase          = "https://api.valr.com"
	apiCurrencyPairs = apiBase + "/v1/public/pairs"
	apiOrderbook     = apiBase + "/v1/public/%s/orderbook"
	apiTrades        = apiBase + "/v1/public/%s/trades"
	apiBalances      = apiBase + "/v1/account/balances"
	apiOrderTypes    = apiBase + "/v1/public/ordertypes?includeInactivePairs=true"
	apiOpenOrders    = apiBase + "/v1/orders/open"
	apiPlaceOrder    = apiBase + "/v2/orders/limit"
	apiCancelOrder   = apiBase + "/v2/orders/order"

	wsBase       = "wss://api.valr.com/ws"
	wsTradeURL   = wsBase + "/trade"
	wsAccountURL = wsBase + "/account"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
}

func NewAPI(options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options:    options,
		httpClient: client,
	}
}

type PairType string

const (
	PairTypeSpot   PairType = "SPOT"
	PairTypeFuture PairType = "FUTURE"
)

type APISymbol struct {
	Active            bool     `json:"active"`
	Symbol            string   `json:"symbol"`
	BaseCurrency      string   `json:"baseCurrency"`
	QuoteCurrency     string   `json:"quoteCurrency"`
	MinBaseAmount     float64  `json:"minBaseAmount,string"`
	MaxBaseAmount     float64  `json:"maxBaseAmount,string"`
	MinQuoteAmount    float64  `json:"minQuoteAmount,string"`
	MaxQuoteAmount    float64  `json:"maxQuoteAmount,string"`
	TickSize          float64  `json:"tickSize,string"`
	BaseDecimalPlaces int      `json:"baseDecimalPlaces,string"`
	CurrencyPairType  PairType `json:"currencyPairType"`
}

func (a *API) Symbols() ([]APISymbol, error) {
	var symbols []APISymbol
	err := a.makePublicRequest(http.MethodGet, apiCurrencyPairs, nil, &symbols)
	if err != nil {
		return nil, err
	}
	return symbols, nil
}

type APIOrderTypes struct {
	CurrencyPair string   `json:"currencyPair"`
	OrderTypes   []string `json:"orderTypes"`
}

func (a *API) OrderTypes() ([]APIOrderTypes, error) {
	var orderTypes []APIOrderTypes
	err := a.makePublicRequest(http.MethodGet, apiOrderTypes, nil, &orderTypes)
	if err != nil {
		return []APIOrderTypes{}, err
	}
	return orderTypes, nil
}

type APIOrderbookEntry struct {
	Price      float64 `json:"price,string"`
	Quantity   float64 `json:"quantity,string"`
	OrderCount int     `json:"orderCount"`
}

type APIOrderbook struct {
	Asks []APIOrderbookEntry `json:"Asks"`
	Bids []APIOrderbookEntry `json:"Bids"`
}

type APIDepth struct {
	Asks []gateway.PriceLevel `json:"asks"`
	Bids []gateway.PriceLevel `json:"bids"`
}

func (a *API) Depth(symbol string, params gateway.GetDepthParams) (APIDepth, error) {
	var orderbook APIOrderbook
	err := a.makePublicRequest(http.MethodGet, fmt.Sprintf(apiOrderbook, symbol), nil, &orderbook)
	if err != nil {
		return APIDepth{}, err
	}

	depth := APIDepth{
		Bids: make([]gateway.PriceLevel, len(orderbook.Bids)),
		Asks: make([]gateway.PriceLevel, len(orderbook.Asks)),
	}
	for i, bid := range orderbook.Bids {
		depth.Bids[i] = gateway.PriceLevel{
			Price:  bid.Price,
			Amount: bid.Quantity,
		}
	}
	for i, ask := range orderbook.Asks {
		depth.Asks[i] = gateway.PriceLevel{
			Price:  ask.Price,
			Amount: ask.Quantity,
		}
	}

	return depth, nil
}

type APIBalance struct {
	Currency  string  `json:"currency"`
	Available float64 `json:"available,string"`
	Reserved  float64 `json:"reserved,string"`
	Total     float64 `json:"total,string"`
}

func (a *API) GetBalances() ([]APIBalance, error) {
	var balances []APIBalance
	err := a.makeAuthRequest(http.MethodGet, apiBalances, nil, &balances)
	return balances, err
}

type APIOrder struct {
	OrderID               string    `json:"orderId"`
	CustomerOrderID       string    `json:"customerOrderId"`
	Side                  string    `json:"orderSide"`
	Type                  string    `json:"orderType"`
	AveragePrice          float64   `json:"averagePrice,string"`
	Price                 float64   `json:"originalPrice,string"`
	Status                string    `json:"orderStatusType"`
	Total                 float64   `json:"total,string"`
	TotalExecutedQuantity float64   `json:"totalExecutedQuantity,string"`
	TotalFee              float64   `json:"totalFee,string"`
	FeeCurrency           string    `json:"feeCurrency"`
	OriginalQuantity      float64   `json:"originalQuantity,string"`
	CurrencyPair          string    `json:"currencyPair"`
	TimeInForce           string    `json:"timeInForce"`
	RemainingQuantity     float64   `json:"remainingQuantity,string"`
	FailedReason          string    `json:"failedReason"`
	CreatedAt             time.Time `json:"createdAt"`
	OrderUpdatedAt        time.Time `json:"orderUpdatedAt"`
	OrderCreatedAt        time.Time `json:"orderCreatedAt"`
}

func (a *API) GetOpenOrders(symbol string) ([]APIOrder, error) {
	newURL, err := url.Parse(apiOpenOrders)
	if err != nil {
		return nil, err
	}

	var orders []APIOrder
	err = a.makeAuthRequest(http.MethodGet, newURL.String(), nil, &orders)
	return orders, err
}

var (
	insufficientBalanceMatch = regexp.MustCompile("Insufficient Balance")
	minimumOrderSizeMatch    = regexp.MustCompile("Minimum order size not met")
	rateLimitMatch           = regexp.MustCompile("status code 429")
)

func (a *API) PlaceOrder(params map[string]interface{}) (string, error) {
	body, err := json.Marshal(params)
	if err != nil {
		return "", err
	}

	var response struct {
		ID string `json:"id"`
	}

	err = a.makeAuthRequest(http.MethodPost, apiPlaceOrder, bytes.NewReader(body), &response)
	if err != nil {
		switch {
		case insufficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		case minimumOrderSizeMatch.MatchString(err.Error()):
			return "", gateway.MinOrderSizeErr
		case rateLimitMatch.MatchString(err.Error()):
			return "", gateway.RateLimitErr
		default:
			return "", err
		}
	}

	return response.ID, nil
}

func (a *API) CancelOrder(orderID string, symbol string) error {
	body := map[string]string{
		"orderId": orderID,
		"pair":    symbol,
	}

	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return err
	}

	return a.makeAuthRequest(
		http.MethodDelete,
		apiCancelOrder,
		bytes.NewReader(bodyBytes),
		nil,
	)
}

func (a *API) createAuthHeaders(method string, path string, body string) http.Header {
	timestamp := strconv.FormatInt(time.Now().UnixNano()/int64(time.Millisecond), 10)
	signature := a.generateSignature(timestamp, method, path, body)

	return http.Header{
		"X-VALR-API-KEY":   []string{a.options.ApiKey},
		"X-VALR-SIGNATURE": []string{signature},
		"X-VALR-TIMESTAMP": []string{timestamp},
	}
}

func (a *API) generateSignature(timestamp string, method string, path string, body string) string {
	message := timestamp + strings.ToUpper(method) + path + body

	h := hmac.New(sha512.New, []byte(a.options.ApiSecret))
	h.Write([]byte(message))

	return hex.EncodeToString(h.Sum(nil))
}

func (a *API) makePublicRequest(method, path string, body io.Reader, result interface{}) error {
	req, err := http.NewRequest(method, path, body)
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.StatusCode < 200 || res.StatusCode > 299 {
		body, _ := io.ReadAll(res.Body)
		return fmt.Errorf("%s API returned status code %d, body: %s", Exchange, res.StatusCode, string(body))
	}

	if result != nil {
		return json.NewDecoder(res.Body).Decode(result)
	}

	return nil
}

func (a *API) makeAuthRequest(method, path string, body io.Reader, result interface{}) error {
	if a.options.ApiKey == "" || a.options.ApiSecret == "" {
		return fmt.Errorf("API key and secret required for authenticated requests")
	}

	req, err := http.NewRequest(method, path, body)
	if err != nil {
		return err
	}

	var bodyStr string
	if body != nil {
		bodyBytes, err := io.ReadAll(body)
		if err != nil {
			return err
		}
		bodyStr = string(bodyBytes)
		req.Body = io.NopCloser(strings.NewReader(bodyStr))
	}

	signPath := strings.Replace(path, apiBase, "", 1)
	authHeaders := a.createAuthHeaders(method, signPath, bodyStr)
	for key, value := range authHeaders {
		req.Header.Set(key, value[0])
	}

	req.Header.Set("Content-Type", "application/json")

	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.StatusCode < 200 || res.StatusCode > 299 {
		body, _ := io.ReadAll(res.Body)
		return fmt.Errorf("%s API returned status code %d, body: %s", Exchange, res.StatusCode, string(body))
	}

	if result != nil {
		return json.NewDecoder(res.Body).Decode(result)
	}

	return nil
}
