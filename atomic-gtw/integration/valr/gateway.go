package valr

import (
	"fmt"
	"math"
	"slices"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "VALR",
}

type Gateway struct {
	base.Gateway
	api               *API
	tickCh            chan gateway.Tick
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(options),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(wsAccountURL, g.api, g.options, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("failed to connect to account gateway, err %s", err)
		}
	}

	g.marketDataGateway = NewMarketDataGateway(wsTradeURL, g.options, g.api, g.tickCh)
	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.Symbols()
	if err != nil {
		return nil, err
	}

	orderTypes, err := g.api.OrderTypes()
	if err != nil {
		return nil, err
	}

	return symbolsToCommonMarket(symbols, orderTypes), nil
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := g.api.Depth(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: depth.Asks,
		Bids: depth.Bids,
	}

	return depthBook, nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func symbolsToCommonMarket(symbols []APISymbol, orderTypes []APIOrderTypes) []gateway.Market {
	commonMarkets := make([]gateway.Market, 0, len(symbols))
	orderTypesMap := make(map[string]struct{})

	for _, orderType := range orderTypes {
		if !slices.Contains(orderType.OrderTypes, "LIMIT") {
			continue
		}
		orderTypesMap[orderType.CurrencyPair] = struct{}{}
	}

	for _, symbol := range symbols {
		if _, ok := orderTypesMap[symbol.Symbol]; !ok {
			continue
		}

		commonMarkets = append(commonMarkets, symbolToCommonMarket(symbol))
	}

	return commonMarkets
}

func symbolToCommonMarket(symbol APISymbol) gateway.Market {
	marketType := gateway.SpotMarket
	if symbol.CurrencyPairType == PairTypeFuture {
		marketType = gateway.FuturesMarket
	}
	return gateway.Market{
		Exchange: Exchange,
		Symbol:   symbol.Symbol,
		Pair: gateway.Pair{
			Base:  symbol.BaseCurrency,
			Quote: symbol.QuoteCurrency,
		},
		PriceTick:              symbol.TickSize,
		AmountTick:             1 / math.Pow10(symbol.BaseDecimalPlaces),
		Closed:                 !symbol.Active,
		MinimumOrderSize:       symbol.MinBaseAmount,
		MinimumOrderMoneyValue: symbol.MinQuoteAmount,
		MarketType:             marketType,
		FuturesMarginAsset:     symbol.Symbol,
	}
}

func (gtw *Gateway) SupportedMethods() gateway.Methods {
	return gateway.Methods{
		CIDMapping: true,
	}
}
