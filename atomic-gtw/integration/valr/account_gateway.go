package valr

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api     *API
	options gateway.Options
	tickCh  chan gateway.Tick
	wsURL   string
}

func NewAccountGateway(baseURL string, api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		api:     api,
		options: options,
		tickCh:  tickCh,
		wsURL:   baseURL,
	}
}

func (g *AccountGateway) Connect() error {
	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)
	ws.SetTag(fmt.Sprintf("%s AccountData", Exchange))
	ws.SetHeaders(g.api.createAuthHeaders(http.MethodGet, "/ws/account", ""))

	if err := ws.Connect(g.wsURL); err != nil {
		log.Printf("%s Failed to connect to account websocket: %s", Exchange, err)
		return err
	}

	go websocketPinger(ws, "AccountGateway")

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)

	go g.processWebsocketMessages(ws, ch)

	return nil
}

func websocketPinger(ws *utils.WsClient, origin string) {
	ticker := time.NewTicker(15 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		if err := ws.WriteMessage([]byte(`{"type":"PING"}`)); err != nil {
			log.Printf("%s [%s] Failed to write ping message to ws: %s", Exchange, origin, err)
		}
	}
}

const (
	NEW_ACCOUNT_TRADE   wsMessageTypeEvent = "NEW_ACCOUNT_TRADE"
	ORDER_STATUS_UPDATE wsMessageTypeEvent = "ORDER_STATUS_UPDATE"
)

func (g *AccountGateway) processWebsocketMessages(ws *utils.WsClient, ch chan []byte) {
	defer ws.Close()

	for msg := range ch {
		var message wsMessage
		err := json.Unmarshal(msg, &message)
		if err != nil {
			log.Printf("%s Failed to unmarshal websocket message: %s", Exchange, err)
			continue
		}

		switch message.Type {
		case NEW_ACCOUNT_TRADE:
			g.processTradeUpdate(message.Data)
		}
	}
}

type wsTradeUpdate struct {
	TradeID      string    `json:"id"`
	OrderID      string    `json:"orderId"`
	Side         string    `json:"side"`
	Price        float64   `json:"price,string"`
	Quantity     float64   `json:"quantity,string"`
	CurrencyPair string    `json:"currencyPair"`
	TradedAt     time.Time `json:"tradedAt"`
}

func (g *AccountGateway) processTradeUpdate(data []byte) {
	var tradeUpdate wsTradeUpdate
	if err := json.Unmarshal(data, &tradeUpdate); err != nil {
		log.Printf("%s Failed to unmarshal trade update: %s", Exchange, err)
		return
	}

	side := gateway.Ask
	if strings.ToUpper(tradeUpdate.Side) == "BUY" {
		side = gateway.Bid
	}

	g.tickCh <- gateway.TickWithEvents(gateway.NewFillEvent(gateway.Fill{
		ID:        tradeUpdate.TradeID,
		OrderID:   tradeUpdate.OrderID,
		Symbol:    tradeUpdate.CurrencyPair,
		Price:     tradeUpdate.Price,
		Amount:    tradeUpdate.Quantity,
		Side:      side,
		Timestamp: tradeUpdate.TradedAt,
	}))
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	apiBalances, err := g.api.GetBalances()
	if err != nil {
		return nil, err
	}

	var balances []gateway.Balance
	for _, apiBalance := range apiBalances {
		balances = append(balances, gateway.Balance{
			Asset:     apiBalance.Currency,
			Available: apiBalance.Available,
			Total:     apiBalance.Total,
		})
	}

	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	apiOrders, err := g.api.GetOpenOrders(market.Symbol)
	if err != nil {
		return nil, err
	}

	orders := make([]gateway.Order, 0, len(apiOrders))
	for _, order := range apiOrders {
		orders = append(orders, gateway.Order{
			ID:              order.OrderID,
			ClientOrderID:   order.CustomerOrderID,
			Market:          market,
			Side:            mapAPISideToCommon(order.Side),
			State:           mapAPIOrderStateToCommon(order.Status),
			Type:            gateway.OrderType(order.Type),
			AvgPrice:        order.AveragePrice,
			Price:           order.Price,
			Amount:          order.OriginalQuantity,
			FilledAmount:    order.OriginalQuantity - order.RemainingQuantity,
			Fee:             order.TotalFee,
			FeeAsset:        order.FeeCurrency,
			RemainingAmount: order.RemainingQuantity,
		})
	}

	return orders, nil
}

func mapAPISideToCommon(side string) gateway.Side {
	sd := strings.ToUpper(side)
	if sd == "BUY" {
		return gateway.Bid
	} else if sd == "SELL" {
		return gateway.Ask
	}
	return ""
}

func mapAPIOrderStateToCommon(status string) gateway.OrderState {
	switch strings.ToUpper(status) {
	case "PLACED", "ACTIVE", "ORDER_MODIFIED":
		return gateway.OrderOpen
	case "FAILED":
		return gateway.OrderRejected
	case "CANCELLED", "EXPIRED":
		return gateway.OrderCancelled
	case "FILLED":
		return gateway.OrderFullyFilled
	case "PARTIALLY_FILLED", "PARTIALLY_FILLED_DUE_TO_SLIPPAGE":
		return gateway.OrderPartiallyFilled
	default:
		return gateway.OrderUnknown
	}
}

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	side := "SELL"
	if order.Side == gateway.Bid {
		side = "BUY"
	}

	params := map[string]interface{}{
		"side":        side,
		"quantity":    utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		"price":       utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
		"pair":        order.Market.Symbol,
		"postOnly":    order.PostOnly,
		"timeInForce": "GTC", // Good Till Cancelled
	}

	if order.ClientOrderID != "" {
		params["customerOrderId"] = order.ClientOrderID
	}

	orderID, err := g.api.PlaceOrder(params)
	if err != nil {
		return "", err
	}

	return orderID, nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID, order.Market.Symbol)
}
