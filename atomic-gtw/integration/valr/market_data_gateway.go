package valr

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	TradeSubEvent = "NEW_TRADE"
	OBSubEvent    = "OB_L1_DIFF"
)

type MarketDataGateway struct {
	baseURL string
	options gateway.Options
	tickCh  chan gateway.Tick
	api     *API
}

func NewMarketDataGateway(baseURL string, options gateway.Options, api *API, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		baseURL: baseURL,
		options: options,
		tickCh:  tickCh,
		api:     api,
	}
}

type subscription struct {
	Event string   `json:"event"`
	Pairs []string `json:"pairs"`
}

type wsRequest struct {
	Type          string         `json:"type"`
	Subscriptions []subscription `json:"subscriptions"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)
	ws.SetTag(fmt.Sprintf("%s MarketData", Exchange))

	if g.options.ApiKey != "" {
		ws.SetHeaders(g.api.createAuthHeaders(http.MethodGet, "/ws/trade", ""))
	}

	err := ws.Connect(g.baseURL)
	if err != nil {
		return fmt.Errorf("market ws connect err: %s", err)
	}

	bookChannels := make([]string, 0, len(markets))
	tradeChannels := make([]string, 0, len(markets))
	for _, market := range markets {
		bookChannels = append(bookChannels, market.Symbol)
		tradeChannels = append(tradeChannels, market.Symbol)
	}

	ch := make(chan []byte, 1000)
	ws.SubscribeMessages(ch)

	bookSubRequest := wsRequest{
		Type: "SUBSCRIBE",
		Subscriptions: []subscription{
			{Event: OBSubEvent, Pairs: bookChannels},
			{Event: TradeSubEvent, Pairs: tradeChannels},
		},
	}

	bookRequest, err := json.Marshal(bookSubRequest)
	if err != nil {
		ws.Close()
		return fmt.Errorf("failed to marshal subscribe message: %s", err)
	}

	if err = ws.WriteMessage(bookRequest); err != nil {
		ws.Close()
		return fmt.Errorf("failed to send subscribe message: %s", err)
	}

	go websocketPinger(ws, "MarketDataGateway")
	go g.messageHandler(ws, ch)

	return nil
}

type wsMessageTypeEvent string

const (
	ERROR              wsMessageTypeEvent = "ERROR"
	SUBSCRIBED         wsMessageTypeEvent = "SUBSCRIBED"
	NEW_TRADE          wsMessageTypeEvent = "NEW_TRADE"
	ORDERBOOK_UPDATE   wsMessageTypeEvent = "OB_L1_DIFF"
	ORDERBOOK_SNAPSHOT wsMessageTypeEvent = "OB_L1_SNAPSHOT"
	PONG               wsMessageTypeEvent = "PONG"
)

type wsMessage struct {
	Type    wsMessageTypeEvent `json:"type"`
	Symbol  string             `json:"currencyPairSymbol,omitempty"`
	PS      string             `json:"ps,omitempty"`   // Alternative currency pair symbol
	Data    json.RawMessage    `json:"-"`              // Will be populated by UnmarshalJSON
	RawD    json.RawMessage    `json:"d,omitempty"`    // For newer API format
	RawData json.RawMessage    `json:"data,omitempty"` // For older API format
}

// UnmarshalJSON implements the json.Unmarshaler interface to normalize the different
// message formats from the VALR API
func (msg *wsMessage) UnmarshalJSON(data []byte) error {
	// Temporary struct to avoid infinite recursion
	type Alias wsMessage
	aux := &struct{ *Alias }{Alias: (*Alias)(msg)}

	if err := json.Unmarshal(data, aux); err != nil {
		return err
	}

	// Normalize the Symbol field
	if msg.Symbol == "" && msg.PS != "" {
		msg.Symbol = msg.PS
	}

	// Normalize the Data field
	switch {
	case len(msg.RawData) > 0:
		msg.Data = msg.RawData
	case len(msg.RawD) > 0:
		msg.Data = msg.RawD
	}

	return nil
}

type wsOrderBook struct {
	LastChange int64                `json:"lc"`
	Asks       []gateway.PriceArray `json:"a"`
	Bids       []gateway.PriceArray `json:"b"`
	Sequence   int64                `json:"sq"`
	Checksum   int64                `json:"cs"`
}

type wsTrade struct {
	Price        float64   `json:"price,string"`
	Quantity     float64   `json:"quantity,string"`
	CurrencyPair string    `json:"currencyPair"`
	TakerSide    string    `json:"takerSide"`
	TradedAt     time.Time `json:"tradedAt"`
}

func (g *MarketDataGateway) messageHandler(ws *utils.WsClient, ch chan []byte) {
	defer ws.Close()

	for data := range ch {
		message := wsMessage{}
		err := json.Unmarshal(data, &message)
		if err != nil {
			log.Println("Failed to unmarshal message:", err)
			continue
		}

		switch message.Type {
		case ORDERBOOK_SNAPSHOT:
			g.processDepthUpdate(message.Data, message.Symbol, true)
		case ORDERBOOK_UPDATE:
			g.processDepthUpdate(message.Data, message.Symbol, false)
		case NEW_TRADE:
			g.processTrade(message.Data, message.Symbol)
		case ERROR:
			log.Println("Error message:", string(data))
		case SUBSCRIBED, PONG:
		default:
			log.Println("Unknown message type:", message.Type, string(data))
		}
	}
}

func (g *MarketDataGateway) processDepthUpdate(data json.RawMessage, symbol string, snapshot bool) {
	var orderbook wsOrderBook
	if err := json.Unmarshal(data, &orderbook); err != nil {
		log.Println("Failed to unmarshal orderbook update:", err)
		return
	}

	events := make([]gateway.Event, 0)

	if snapshot {
		events = append(events, gateway.NewSnapshotSequenceEvent(gateway.SnapshotSequence{
			Symbol: symbol,
		}))
	}

	for _, ask := range orderbook.Asks {
		events = append(events, gateway.NewDepthEvent(gateway.Depth{
			Symbol: symbol,
			Side:   gateway.Ask,
			Price:  ask.Price,
			Amount: ask.Amount,
		}))
	}

	for _, bid := range orderbook.Bids {
		events = append(events, gateway.NewDepthEvent(gateway.Depth{
			Symbol: symbol,
			Side:   gateway.Bid,
			Price:  bid.Price,
			Amount: bid.Amount,
		}))
	}

	if len(events) > 0 {
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          events,
		}
	}
}

func (g *MarketDataGateway) processTrade(data json.RawMessage, symbol string) {
	var trade wsTrade
	if err := json.Unmarshal(data, &trade); err != nil {
		log.Println("Failed to unmarshal trade:", err)
		return
	}

	side := gateway.Ask
	if trade.TakerSide == "BUY" {
		side = gateway.Bid
	}

	g.tickCh <- gateway.TickWithEvents(gateway.NewTradeEvent(gateway.Trade{
		Symbol:    symbol,
		Price:     trade.Price,
		Amount:    trade.Quantity,
		Direction: side,
		Timestamp: trade.TradedAt,
	}))
}
