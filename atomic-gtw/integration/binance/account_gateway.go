package binance

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	baseWsMainURL    = "wss://stream.binance.com:9443/ws"
	baseWsTestnetURL = "wss://stream.testnet.binance.vision:9443/ws"
)

type AccountGateway struct {
	base.AccountGateway
	options   gateway.Options
	api       *API
	tickCh    chan gateway.Tick
	baseURLWs string
	paidFees  map[int64]float64 // TODO: this is ever growing, we should eventually clean this.
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	var wsURL string
	if options.Staging {
		wsURL = baseWsTestnetURL
	} else {
		wsURL = baseWsMainURL
	}

	return &AccountGateway{
		api:       api,
		options:   options,
		tickCh:    tickCh,
		baseURLWs: wsURL,
		paidFees:  make(map[int64]float64),
	}
}

type UserStreamGenericUpdate struct {
	EventType string `json:"e"`
	EventTime int64  `json:"E"`
}

type UserStreamOrderUpdate struct {
	EventType                 string  `json:"e"`
	EventTime                 int64   `json:"E"`
	Symbol                    string  `json:"s"`        // Symbol
	ClientOrderID             string  `json:"c"`        // Client order ID
	Side                      string  `json:"S"`        // Side
	Type                      string  `json:"o"`        // Order type
	TimeInForce               string  `json:"f"`        // Time in force
	Quantity                  float64 `json:"q,string"` // Order quantity
	Price                     float64 `json:"p,string"` // Order price
	StopPrice                 float64 `json:"P,string"` // Stop price
	IcebergQuantity           float64 `json:"F,string"` // Iceberg quantity
	OriginalClientOrderID     string  `json:"C"`        // Original client order ID; This is the ID of the order being canceled
	CurrentExecutionType      string  `json:"x"`        // Current execution type
	CurrentOrderStatus        string  `json:"X"`        // Current order status
	OrderRejectReason         string  `json:"r"`        // Order reject reason; will be an error code.
	OrderID                   int64   `json:"i"`        // Order ID
	LastExecutedQuantity      float64 `json:"l,string"` // Last executed quantity
	CumulativeFilledQuantity  float64 `json:"z,string"` // Cumulative filled quantity
	LastExecutedPrice         float64 `json:"L,string"` // Last executed price
	CommissionAmount          float64 `json:"n,string"` // Commission amount
	CommissionAsset           string  `json:"N"`        // Commission asset
	TradeID                   int64   `json:"t"`        // Trade ID
	TransactionTime           int64   `json:"T"`        // Transaction time
	Maker                     bool    `json:"m"`        // Is this trade the maker side?
	CreationTime              int64   `json:"O"`        // Order creation time
	CumulativeFilledQuote     float64 `json:"Z,string"` // Cumulative quote asset transacted quantity
	LastQuoteExecutedQuantity float64 `json:"Y,string"` // Last quote asset transacted quantity (i.e. lastPrice * lastQty)
	QuoteOrderQuantity        float64 `json:"Q,string"`

	// Ignore (not sure what this is)
	g int64 `json:"g"`
	I int64 `json:"I"`
	M bool  `json:"M"`
}

func (g *AccountGateway) Connect() error {
	// Start user data stream, to receive account updates
	listenKey, err := g.api.NewStartUserStreamService()
	if err != nil {
		return err
	}
	endpoint := fmt.Sprintf("%s/%s", g.baseURLWs, listenKey)
	ws := utils.NewWsClient()
	err = ws.Connect(endpoint)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	go websocketPinger(ws, "AccountGateway")

	ch := make(chan []byte, 100)
	ws.SubscribeMessages(ch)

	go func(ch chan []byte) {
		for msg := range ch {
			var genericUpdate UserStreamGenericUpdate
			err = json.Unmarshal(msg, &genericUpdate)
			if err != nil {
				log.Printf("failed to unmarshal Binance user stream update message error %s", err)
			}

			if genericUpdate.EventType == "executionReport" {
				var orderUpdate UserStreamOrderUpdate
				err = json.Unmarshal(msg, &orderUpdate)
				if err != nil {
					log.Printf("failed to unmarshal Binance user stream order update error %s", err)
				}
				g.processOrderUpdate(orderUpdate)
			}
		}
	}(ch)

	go func() {
		// Keepalive a user data stream to prevent a time-out. User data streams will close after 60 minutes.
		// It's recommended to send a ping about every 30 minutes.
		ticker := time.NewTicker(30 * time.Minute)
		defer ticker.Stop()

		for range ticker.C {
			err = g.api.KeepaliveUserStreamService(listenKey)
			if err != nil {
				log.Printf("failed to send keepalive request to binance user data stream, error %s", err)
				log.Printf("retrying keepalive request in 1 minute...")
				time.Sleep(1 * time.Minute)
				err = g.api.KeepaliveUserStreamService(listenKey)
				if err != nil {
					log.Printf("failed when RETRYING keepalive request to binance user data stream, error %s", err)
				}
			}
		}
	}()

	return nil
}

func (g *AccountGateway) processOrderUpdate(order UserStreamOrderUpdate) {
	// Skip empty updates
	if order.LastExecutedQuantity == 0 {
		return
	}

	eventLog := make([]gateway.Event, 0, 1)
	var side gateway.Side
	if order.Side == "BUY" {
		side = gateway.Bid
	} else {
		side = gateway.Ask
	}

	// Calculate current fee for this trade
	totalFee, _ := g.paidFees[order.OrderID]
	fee := order.CommissionAmount - totalFee
	g.paidFees[order.OrderID] = order.CommissionAmount

	event := gateway.Event{
		Type: gateway.FillEvent,
		Data: gateway.Fill{
			Timestamp:     gateway.ParseTimestamp(order.TransactionTime),
			Symbol:        order.Symbol,
			ID:            strconv.FormatInt(order.TradeID, 10),
			OrderID:       strconv.FormatInt(order.OrderID, 10),
			ClientOrderID: order.ClientOrderID,
			Side:          side,
			Price:         order.LastExecutedPrice,
			Amount:        order.LastExecutedQuantity,
			Fee:           fee,
			FeeAsset:      order.CommissionAsset,
		},
	}
	eventLog = append(eventLog, event)

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

func mapWsOrderStatus(status string) gateway.OrderState {
	switch status {
	case "INIT":
		return gateway.OrderOpen
	case "NEW":
		return gateway.OrderOpen
	case "TRADE":
		return gateway.OrderPartiallyFilled
	case "FILLED":
		return gateway.OrderFullyFilled
	case "CANCELED":
		return gateway.OrderCancelled
	case "EXPIRED":
		return gateway.OrderCancelled
	default:
		return gateway.OrderUnknown
	}
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	res, err := g.api.AccountBalance()
	if err != nil {
		return []gateway.Balance{}, err
	}

	if !res.CanTrade {
		return []gateway.Balance{}, fmt.Errorf("[Binance] API account does not have the required permissions for trading operations")
	}

	balances := make([]gateway.Balance, len(res.List))
	for i, balance := range res.List {
		balances[i] = gateway.Balance{
			Asset:     balance.Asset,
			Available: balance.Free,
			Total:     balance.Free + balance.Locked,
		}
	}

	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	res, err := g.api.OpenOrders(market.Symbol)
	if err != nil {
		return []gateway.Order{}, err
	}

	orders := make([]gateway.Order, 0, len(res))
	for _, order := range res {
		orders = append(orders, gateway.Order{
			Market:       market,
			ID:           strconv.FormatInt(order.OrderID, 10),
			State:        binanceOrderStatusToCommonStatus(order.Status),
			Amount:       order.OrigQty,
			Price:        order.Price,
			FilledAmount: order.ExecutedQty,
		})
	}

	return orders, nil
}

func (g *AccountGateway) SendOrder(order gateway.Order) (string, error) {
	orderSide := "SELL"
	if order.Side == gateway.Bid {
		orderSide = "BUY"
	}

	var orderType string
	var timeInForce string
	switch {
	case order.PostOnly:
		orderType = "LIMIT_MAKER"
	case order.Type == gateway.MarketOrder:
		orderType = "MARKET"
	default:
		orderType = "LIMIT"
		timeInForce = "GTC"
	}

	orderID, err := g.api.PlaceOrder(APIPlaceOrder{
		Quantity:    utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		Price:       utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
		Symbol:      order.Market.Symbol,
		Side:        orderSide,
		Type:        orderType,
		TimeInForce: timeInForce,
	})
	if err != nil {
		return "", err
	}

	return orderID, nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID, order.Market.Symbol)
}

func binanceOrderStatusToCommonStatus(status string) gateway.OrderState {
	switch status {
	case "NEW":
		return gateway.OrderOpen
	case "PARTIALLY_FILLED":
		return gateway.OrderPartiallyFilled
	case "FILLED":
		return gateway.OrderFullyFilled
	case "CANCELED":
		return gateway.OrderCancelled
	case "PENDING_CANCEL":
		return gateway.OrderOpen
	case "REJECTED":
		return gateway.OrderCancelled
	case "EXPIRED":
		return gateway.OrderCancelled
	default:
		return gateway.OrderUnknown
	}
}
