package binance

import (
	"fmt"
	"strconv"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Binance",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	api               *API
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	var apiBaseURL string
	if options.Staging {
		apiBaseURL = apiSandBox
	} else {
		apiBaseURL = apiSpot
	}

	gtw := &Gateway{
		options: options,
		api:     NewAPI(options, apiBaseURL, apiData),
		// We need to set this to something high, because initially we queue up a lot of ticks
		// because we are waiting for the initial snapshot of each orderbook.
		// The bot might not have started to process the ticks yet, so we need to buffer all
		// of them, while we are fetching all initial snapshots.
		tickCh: make(chan gateway.Tick, 100_000),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("account gtw connect: %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.options, g.api, false, g.tickCh)
	if err := g.marketDataGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.Symbols()
	if err != nil {
		return nil, err
	}

	markets, err := symbolsToCommonMarket(res)
	return markets, err
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	if params.Limit == 0 {
		params.Limit = 100
	}
	depth, err := g.api.Depth(market.Symbol, strconv.FormatInt(int64(params.Limit), 10))
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: gateway.PriceArrayToPriceLevels(depth.Asks),
		Bids: gateway.PriceArrayToPriceLevels(depth.Bids),
	}

	return depthBook, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
