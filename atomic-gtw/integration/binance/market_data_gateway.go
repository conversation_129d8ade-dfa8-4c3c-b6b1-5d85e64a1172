package binance

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
	"golang.org/x/sync/semaphore"
)

type MarketDataGateway struct {
	options    gateway.Options
	futuresAPI bool
	api        *API
	tickCh     chan gateway.Tick
}

func NewMarketDataGateway(options gateway.Options, api *API, futuresAPI bool, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options:    options,
		futuresAPI: futuresAPI,
		api:        api,
		tickCh:     tickCh,
	}
}

func (g *MarketDataGateway) Connect() error {
	return nil
}

type binanceStreamMessage struct {
	Stream string          `json:"stream"`
	Data   json.RawMessage `json:"data"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	batchesOf := 50
	batches := make([][]gateway.Market, ((len(markets)-1)/batchesOf)+1)

	log.Printf("Binance subscribing to %d markets, will need %d websocket connections, maximum of %d markets on each websocket.", len(markets), len(batches), batchesOf)

	for index, market := range markets {
		group := index / batchesOf

		if batches[group] == nil {
			batches[group] = make([]gateway.Market, 0)
		}

		batches[group] = append(batches[group], market)
	}

	for _, batch := range batches {
		err := g.subscribeMarketData(batch)
		if err != nil {
			return err
		}
	}

	return nil
}

type binanceDepthEvent struct {
	Type          string               `json:"e"`
	Time          int64                `json:"E"`
	Symbol        string               `json:"s"`
	FirstUpdateID int64                `json:"U"`
	LastUpdateID  int64                `json:"u"`
	BidDelta      []gateway.PriceArray `json:"b"`
	AskDelta      []gateway.PriceArray `json:"a"`
}

type binanceTradeEvent struct {
	Type          string  `json:"e"`
	Time          int64   `json:"E"`
	Symbol        string  `json:"s"`
	TradeId       int64   `json:"t"`
	Price         float64 `json:"p,string"`
	Quantity      float64 `json:"q,string"`
	BuyerOrderId  int64   `json:"b"`
	SellerOrderId int64   `json:"a"`
	TradeTime     int64   `json:"T"`
	BuyerMaker    bool    `json:"m"`
	// Ignore the "M" field. Not sure what the hell this is.
	// Don't remove this, or it will be parsed as the "m" field
	// for some reason. Seems like go's json.Unmarshal is not case sensitive.
	M bool `json:"M"`
}

type binanceDepthQueue struct {
	ReceivedSnapshot bool
	Queue            []binanceDepthEvent
	Lock             *sync.Mutex
}

type snapshotResult struct {
	market   gateway.Market
	snapshot APIDepth
	err      error
}

func (g *MarketDataGateway) subscribeMarketData(markets []gateway.Market) error {
	depthQueue := make(map[string]binanceDepthQueue)
	streams := make([]string, 0)
	marketsMap := make(map[string]gateway.Market)

	for _, market := range markets {
		streams = append(streams, fmt.Sprintf("%s@depth@100ms", strings.ToLower(market.Symbol)))
		streams = append(streams, fmt.Sprintf("%s@trade", strings.ToLower(market.Symbol)))
		marketsMap[market.Symbol] = market
		depthQueue[market.Symbol] = binanceDepthQueue{
			Queue: make([]binanceDepthEvent, 0),
			Lock:  &sync.Mutex{},
		}
	}

	host := "data-stream.binance.vision:443"
	if g.options.Staging {
		host = "stream.testnet.binance.vision:9443"
	}

	if g.futuresAPI {
		host = "fstream.binance.com"
	}

	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)

	wsUrl := fmt.Sprintf("wss://%s/stream?streams=%s", host, strings.Join(streams, "/"))
	err := ws.Connect(wsUrl)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	go websocketPinger(ws, "MarketDataGateway")

	msgChan := make(chan []byte)
	ws.SubscribeMessages(msgChan)

	go func() {
		for data := range msgChan {
			message := binanceStreamMessage{}
			err = json.Unmarshal(data, &message)
			if err != nil {
				log.Printf("Binance market data unmarshal err: %s", err)
				continue
			}

			// Process ws event message
			slices := strings.Split(message.Stream, "@")
			if len(slices) >= 2 {
				symbol := strings.ToUpper(slices[0])
				_type := slices[1]
				market, ok := marketsMap[symbol]
				if !ok {
					log.Printf("Binance stream %s unable to find market for this stream in marketsMap", message.Stream)
					continue
				}

				if _type == "trade" {
					var tradeEvent binanceTradeEvent
					err = json.Unmarshal(message.Data, &tradeEvent)
					if err != nil {
						log.Printf("Binance failed to unmarshal trade event, err: %s", err)
					}

					g.processTradeEvent(market, tradeEvent)
				} else if _type == "depth" {
					var depthEvent binanceDepthEvent
					err = json.Unmarshal(message.Data, &depthEvent)
					if err != nil {
						log.Printf("Binance failed to unmarshal depth event, err: %s", err)
					}

					queue, _ := depthQueue[symbol]
					queue.Lock.Lock()

					if queue.ReceivedSnapshot {
						// Sanity check
						if queue.Queue != nil && len(queue.Queue) > 0 {
							err = fmt.Errorf("Binance depth stream %s already received snapshot, expected queue to be empty", symbol)
							panic(err)
						}

						g.processDepthEvent(market, depthEvent, false)
					} else {
						queue.Queue = append(queue.Queue, depthEvent)
						depthQueue[symbol] = queue
					}

					queue.Lock.Unlock()
				}
			} else {
				log.Printf("Binance received unprocessable message on stream: \"%s\"", message.Stream)
			}
		}
	}()

	// Wait 1 second before starting to fetch order book snapshots
	// This will allow us to start queing the initial order book partial updates
	time.Sleep(1 * time.Second)

	// Fetch order book snapshots in 5 concurrent requests
	sem := semaphore.NewWeighted(5)
	ctx := context.Background()
	results := make(chan snapshotResult, len(markets))
	var wg sync.WaitGroup

	for _, market := range markets {
		wg.Add(1)
		go func(m gateway.Market) {
			defer wg.Done()

			if err := sem.Acquire(ctx, 1); err != nil {
				results <- snapshotResult{market: m, err: err}
				return
			}
			defer sem.Release(1)

			snapshot, err := g.getDepthSnapshot(m.Symbol)
			results <- snapshotResult{market: m, snapshot: snapshot, err: err}
		}(market)
	}

	go func() {
		wg.Wait()
		close(results)
	}()

	errs := make([]error, 0)
	for result := range results {
		if result.err != nil {
			log.Printf("Binance market %s failed to fetch OB snapshot: %s", result.market.Symbol, result.err)
			errs = append(errs, result.err)
			continue
		}

		market := result.market
		snapshot := result.snapshot

		queue := depthQueue[market.Symbol]
		queue.Lock.Lock()

		g.processDepthEvent(market, binanceDepthEvent{
			BidDelta: snapshot.Bids,
			AskDelta: snapshot.Asks,
		}, true)

		i := 0
		for _, depthEvent := range queue.Queue {
			if depthEvent.LastUpdateID > snapshot.LastUpdateID {
				i += 1
				g.processDepthEvent(market, depthEvent, false)
			}
		}

		if i > 0 {
			log.Printf("Binance market %s processed %d queued depth events after fetching OB snapshot", market.Symbol, i)
		}

		queue.ReceivedSnapshot = true
		queue.Queue = nil
		depthQueue[market.Symbol] = queue
		queue.Lock.Unlock()
	}

	if len(errs) > 0 {
		ws.Close()
		return fmt.Errorf("binance failed to fetch OB snapshots for %d markets", len(errs))
	}

	return nil
}

func websocketPinger(ws *utils.WsClient, origin string) {
	pongCh := make(chan struct{})
	go func() {
		ws.SetPongHandler(func(appData string) error {
			select {
			case pongCh <- struct{}{}:
			default:
				log.Printf("%s [%s] received unexpected pong message", Exchange, origin)
			}

			return nil
		})
	}()

	for {
		time.Sleep(10 * time.Second)

		err := ws.WriteRawMessage(websocket.PingMessage, nil)
		if err != nil {
			err = fmt.Errorf("Binance [%s] websocket ping error: %s", origin, err)
			panic(err)
		}

		select {
		case <-pongCh:
		case <-time.After(5 * time.Second):
			err = fmt.Errorf("Binance [%s] websocket ping timeout after 5 seconds", origin)
			panic(err)
		}
	}
}

func (g *MarketDataGateway) getDepthSnapshot(symbol string) (res APIDepth, err error) {
	var getDepth = g.api.Depth
	if g.futuresAPI {
		getDepth = g.api.FDepth
	}

	return getDepth(symbol, "100")
}

func (g *MarketDataGateway) processDepthEvent(market gateway.Market, depth binanceDepthEvent, snapshot bool) {
	eventLog := make([]gateway.Event, 0)

	if snapshot {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: market.Symbol,
			},
		})
	}

	for _, ask := range depth.AskDelta {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Ask,
				Price:  ask.Price,
				Amount: ask.Amount,
			},
		})
	}
	for _, bid := range depth.BidDelta {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: market.Symbol,
				Side:   gateway.Bid,
				Price:  bid.Price,
				Amount: bid.Amount,
			},
		})
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

func (g *MarketDataGateway) processTradeEvent(market gateway.Market, trade binanceTradeEvent) {
	var direction gateway.Side
	if trade.BuyerMaker {
		direction = gateway.Ask
	} else {
		direction = gateway.Bid
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog: []gateway.Event{
			{
				Type: gateway.TradeEvent,
				Data: gateway.Trade{
					ID:        strconv.FormatInt(trade.TradeId, 10),
					Timestamp: time.Unix(0, trade.Time*int64(time.Millisecond)),
					Symbol:    trade.Symbol,
					Direction: direction,
					Price:     trade.Price,
					Amount:    trade.Quantity,
				},
			},
		},
	}
}
