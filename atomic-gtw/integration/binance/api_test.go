package binance

import (
	"errors"
	"net/http"
	"testing"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
	"github.com/stretchr/testify/assert"
)

var (
	accBalancesRes = `{
    "makerCommission": 0,
    "takerCommission": 0,
    "buyerCommission": 0,
    "sellerCommission": 0,
    "commissionRates": {
        "maker": "0.********",
        "taker": "0.********",
        "buyer": "0.********",
        "seller": "0.********"
    },
    "canTrade": true,
    "canWithdraw": false,
    "canDeposit": false,
    "brokered": false,
    "requireSelfTradePrevention": false,
    "preventSor": false,
    "updateTime": *************,
    "accountType": "SPOT",
    "balances": [
        {
            "asset": "BNB",
            "free": "999.********",
            "locked": "0.********"
        },
        {
            "asset": "BTC",
            "free": "1.********",
            "locked": "0.********"
        },
        {
            "asset": "BUSD",
            "free": "10000.********",
            "locked": "0.********"
        },
        {
            "asset": "ETH",
            "free": "100.********",
            "locked": "0.********"
        },
        {
            "asset": "LTC",
            "free": "500.********",
            "locked": "0.********"
        },
        {
            "asset": "TRX",
            "free": "500000.********",
            "locked": "0.********"
        },
        {
            "asset": "USDT",
            "free": "10024.********",
            "locked": "0.********"
        },
        {
            "asset": "XRP",
            "free": "50000.********",
            "locked": "0.********"
        }
    ],
    "permissions": [
        "SPOT"
    ],
    "uid": 1688337860526255667
}`
	openOrdersRes = `[
    {
        "symbol": "BNBUSDT",
        "orderId": 1024677,
        "orderListId": -1,
        "clientOrderId": "DoJhIXZ3rH5t2MRQ65keMf",
        "price": "247.********",
        "origQty": "0.********",
        "executedQty": "0.********",
        "cummulativeQuoteQty": "24.********",
        "status": "FILLED",
        "timeInForce": "GTC",
        "type": "LIMIT",
        "side": "SELL",
        "stopPrice": "0.********",
        "icebergQty": "0.********",
        "time": *************,
        "updateTime": *************,
        "isWorking": true,
        "workingTime": *************,
        "origQuoteOrderQty": "0.********",
        "selfTradePreventionMode": "NONE"
    },
    {
        "symbol": "BNBUSDT",
        "orderId": 1174260,
        "orderListId": -1,
        "clientOrderId": "yBtoYgRjgQAtD5bsi04GUM",
        "price": "247.********",
        "origQty": "0.********",
        "executedQty": "0.********",
        "cummulativeQuoteQty": "24.84000000",
        "status": "FILLED",
        "timeInForce": "GTC",
        "type": "LIMIT",
        "side": "SELL",
        "stopPrice": "0.********",
        "icebergQty": "0.********",
        "time": 1689516118713,
        "updateTime": 1689516118713,
        "isWorking": true,
        "workingTime": 1689516118713,
        "origQuoteOrderQty": "0.********",
        "selfTradePreventionMode": "NONE"
    },
    {
        "symbol": "BNBUSDT",
        "orderId": 1183943,
        "orderListId": -1,
        "clientOrderId": "q9xqgl7f7n9ehWHQ9CezVn",
        "price": "300.********",
        "origQty": "0.********",
        "executedQty": "0.********",
        "cummulativeQuoteQty": "0.********",
        "status": "CANCELED",
        "timeInForce": "GTC",
        "type": "LIMIT",
        "side": "SELL",
        "stopPrice": "0.********",
        "icebergQty": "0.********",
        "time": 1689525222763,
        "updateTime": *************,
        "isWorking": true,
        "workingTime": 1689525222763,
        "origQuoteOrderQty": "0.********",
        "selfTradePreventionMode": "NONE"
    },
    {
        "symbol": "BNBUSDT",
        "orderId": 1186690,
        "orderListId": -1,
        "clientOrderId": "imsdWV4vj3YEoq8B7GlG8E",
        "price": "300.********",
        "origQty": "0.********",
        "executedQty": "0.********",
        "cummulativeQuoteQty": "0.********",
        "status": "NEW",
        "timeInForce": "GTC",
        "type": "LIMIT",
        "side": "SELL",
        "stopPrice": "0.********",
        "icebergQty": "0.********",
        "time": 1689527427497,
        "updateTime": 1689527427497,
        "isWorking": true,
        "workingTime": 1689527427497,
        "origQuoteOrderQty": "0.********",
        "selfTradePreventionMode": "NONE"
    }
]`
	placeOrderRes = `{
    "symbol": "BNBUSDT",
    "orderId": 1186690,
    "orderListId": -1,
    "clientOrderId": "imsdWV4vj3YEoq8B7GlG8E",
    "transactTime": 1689527427497,
    "price": "300.********",
    "origQty": "0.********",
    "executedQty": "0.********",
    "cummulativeQuoteQty": "0.********",
    "status": "NEW",
    "timeInForce": "GTC",
    "type": "LIMIT",
    "side": "SELL",
    "workingTime": 1689527427497,
    "fills": [],
    "selfTradePreventionMode": "NONE"
}`
	symbolsRes = `{"timezone":"UTC","serverTime":1689540440893,"rateLimits":[{"rateLimitType":"REQUEST_WEIGHT","interval":"MINUTE","intervalNum":1,"limit":1200},{"rateLimitType":"ORDERS","interval":"SECOND","intervalNum":10,"limit":50},{"rateLimitType":"ORDERS","interval":"DAY","intervalNum":1,"limit":160000},{"rateLimitType":"RAW_REQUESTS","interval":"MINUTE","intervalNum":5,"limit":6100}],"exchangeFilters":[],"symbols":[{"symbol":"BNBBUSD","status":"TRADING","baseAsset":"BNB","baseAssetPrecision":8,"quoteAsset":"BUSD","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.01000000","maxPrice":"10000.********","tickSize":"0.01000000"},{"filterType":"LOT_SIZE","minQty":"0.01000000","maxQty":"9000.********","stepSize":"0.01000000"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"1000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"BTCBUSD","status":"TRADING","baseAsset":"BTC","baseAssetPrecision":8,"quoteAsset":"BUSD","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.01000000","maxPrice":"1000000.********","tickSize":"0.01000000"},{"filterType":"LOT_SIZE","minQty":"0.00000100","maxQty":"900.********","stepSize":"0.00000100"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"100.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"ETHBUSD","status":"TRADING","baseAsset":"ETH","baseAssetPrecision":8,"quoteAsset":"BUSD","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.01000000","maxPrice":"100000.********","tickSize":"0.01000000"},{"filterType":"LOT_SIZE","minQty":"0.00001000","maxQty":"9000.********","stepSize":"0.00001000"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"1000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"LTCBUSD","status":"TRADING","baseAsset":"LTC","baseAssetPrecision":8,"quoteAsset":"BUSD","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.01000000","maxPrice":"100000.********","tickSize":"0.01000000"},{"filterType":"LOT_SIZE","minQty":"0.00001000","maxQty":"9000.********","stepSize":"0.00001000"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"1000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"TRXBUSD","status":"TRADING","baseAsset":"TRX","baseAssetPrecision":8,"quoteAsset":"BUSD","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00001000","maxPrice":"1000.********","tickSize":"0.00001000"},{"filterType":"LOT_SIZE","minQty":"0.********","maxQty":"90000.********","stepSize":"0.********"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"10000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"XRPBUSD","status":"TRADING","baseAsset":"XRP","baseAssetPrecision":8,"quoteAsset":"BUSD","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00010000","maxPrice":"1000.********","tickSize":"0.00010000"},{"filterType":"LOT_SIZE","minQty":"0.********","maxQty":"90000.********","stepSize":"0.********"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"10000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"BNBUSDT","status":"TRADING","baseAsset":"BNB","baseAssetPrecision":8,"quoteAsset":"USDT","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.01000000","maxPrice":"10000.********","tickSize":"0.01000000"},{"filterType":"LOT_SIZE","minQty":"0.01000000","maxQty":"9000.********","stepSize":"0.01000000"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"1000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"BTCUSDT","status":"TRADING","baseAsset":"BTC","baseAssetPrecision":8,"quoteAsset":"USDT","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.01000000","maxPrice":"1000000.********","tickSize":"0.01000000"},{"filterType":"LOT_SIZE","minQty":"0.00000100","maxQty":"900.********","stepSize":"0.00000100"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"100.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"ETHUSDT","status":"TRADING","baseAsset":"ETH","baseAssetPrecision":8,"quoteAsset":"USDT","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.01000000","maxPrice":"100000.********","tickSize":"0.01000000"},{"filterType":"LOT_SIZE","minQty":"0.00001000","maxQty":"9000.********","stepSize":"0.00001000"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"1000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"LTCUSDT","status":"TRADING","baseAsset":"LTC","baseAssetPrecision":8,"quoteAsset":"USDT","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.01000000","maxPrice":"100000.********","tickSize":"0.01000000"},{"filterType":"LOT_SIZE","minQty":"0.00001000","maxQty":"9000.********","stepSize":"0.00001000"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"1000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"TRXUSDT","status":"TRADING","baseAsset":"TRX","baseAssetPrecision":8,"quoteAsset":"USDT","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00001000","maxPrice":"1000.********","tickSize":"0.00001000"},{"filterType":"LOT_SIZE","minQty":"0.********","maxQty":"90000.********","stepSize":"0.********"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"10000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"XRPUSDT","status":"TRADING","baseAsset":"XRP","baseAssetPrecision":8,"quoteAsset":"USDT","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00010000","maxPrice":"1000.********","tickSize":"0.00010000"},{"filterType":"LOT_SIZE","minQty":"0.********","maxQty":"90000.********","stepSize":"0.********"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"10000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"10.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"BNBBTC","status":"TRADING","baseAsset":"BNB","baseAssetPrecision":8,"quoteAsset":"BTC","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00000100","maxPrice":"10.********","tickSize":"0.00000100"},{"filterType":"LOT_SIZE","minQty":"0.01000000","maxQty":"9000.********","stepSize":"0.01000000"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"1000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"0.00010000","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"ETHBTC","status":"TRADING","baseAsset":"ETH","baseAssetPrecision":8,"quoteAsset":"BTC","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00000100","maxPrice":"100.********","tickSize":"0.00000100"},{"filterType":"LOT_SIZE","minQty":"0.00001000","maxQty":"9000.********","stepSize":"0.00001000"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"1000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"0.00010000","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"LTCBTC","status":"TRADING","baseAsset":"LTC","baseAssetPrecision":8,"quoteAsset":"BTC","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00000100","maxPrice":"100.********","tickSize":"0.00000100"},{"filterType":"LOT_SIZE","minQty":"0.00001000","maxQty":"9000.********","stepSize":"0.00001000"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"1000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"0.00010000","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"TRXBTC","status":"TRADING","baseAsset":"TRX","baseAssetPrecision":8,"quoteAsset":"BTC","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00000001","maxPrice":"1.********","tickSize":"0.00000001"},{"filterType":"LOT_SIZE","minQty":"0.********","maxQty":"90000.********","stepSize":"0.********"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"10000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"0.00010000","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"XRPBTC","status":"TRADING","baseAsset":"XRP","baseAssetPrecision":8,"quoteAsset":"BTC","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00000001","maxPrice":"1.********","tickSize":"0.00000001"},{"filterType":"LOT_SIZE","minQty":"0.********","maxQty":"90000.********","stepSize":"0.********"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"10000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"0.00010000","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"LTCBNB","status":"TRADING","baseAsset":"LTC","baseAssetPrecision":8,"quoteAsset":"BNB","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00010000","maxPrice":"1000.********","tickSize":"0.00010000"},{"filterType":"LOT_SIZE","minQty":"0.00001000","maxQty":"9000.********","stepSize":"0.00001000"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"1000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"0.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"TRXBNB","status":"TRADING","baseAsset":"TRX","baseAssetPrecision":8,"quoteAsset":"BNB","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00000010","maxPrice":"10.********","tickSize":"0.00000010"},{"filterType":"LOT_SIZE","minQty":"0.********","maxQty":"90000.********","stepSize":"0.********"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"10000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"0.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]},{"symbol":"XRPBNB","status":"TRADING","baseAsset":"XRP","baseAssetPrecision":8,"quoteAsset":"BNB","quotePrecision":8,"quoteAssetPrecision":8,"baseCommissionPrecision":8,"quoteCommissionPrecision":8,"orderTypes":["LIMIT","LIMIT_MAKER","MARKET","STOP_LOSS_LIMIT","TAKE_PROFIT_LIMIT"],"icebergAllowed":true,"ocoAllowed":true,"quoteOrderQtyMarketAllowed":true,"allowTrailingStop":true,"cancelReplaceAllowed":true,"isSpotTradingAllowed":true,"isMarginTradingAllowed":false,"filters":[{"filterType":"PRICE_FILTER","minPrice":"0.00000100","maxPrice":"10.********","tickSize":"0.00000100"},{"filterType":"LOT_SIZE","minQty":"0.********","maxQty":"90000.********","stepSize":"0.********"},{"filterType":"ICEBERG_PARTS","limit":10},{"filterType":"MARKET_LOT_SIZE","minQty":"0.********","maxQty":"10000.********","stepSize":"0.********"},{"filterType":"TRAILING_DELTA","minTrailingAboveDelta":10,"maxTrailingAboveDelta":2000,"minTrailingBelowDelta":10,"maxTrailingBelowDelta":2000},{"filterType":"PERCENT_PRICE_BY_SIDE","bidMultiplierUp":"5","bidMultiplierDown":"0.2","askMultiplierUp":"5","askMultiplierDown":"0.2","avgPriceMins":1},{"filterType":"NOTIONAL","minNotional":"0.********","applyMinToMarket":true,"maxNotional":"9000000.********","applyMaxToMarket":false,"avgPriceMins":1},{"filterType":"MAX_NUM_ORDERS","maxNumOrders":200},{"filterType":"MAX_NUM_ALGO_ORDERS","maxNumAlgoOrders":5}],"permissions":["SPOT"],"defaultSelfTradePreventionMode":"NONE","allowedSelfTradePreventionModes":["NONE","EXPIRE_TAKER","EXPIRE_MAKER","EXPIRE_BOTH"]}]}`
	depthRes   = `{
    "lastUpdateId": 1996592,
    "bids": [
        [
            "247.41000000",
            "0.05000000"
        ]
    ],
    "asks": [
        [
            "247.44000000",
            "847.********"
        ]
    ]
}`

	cancelOrderRes = `{
    "symbol": "BNBUSDT",
    "origClientOrderId": "q9xqgl7f7n9ehWHQ9CezVn",
    "orderId": 1183943,
    "orderListId": -1,
    "clientOrderId": "o1woXIlO7sRGNvZoKxP2GB",
    "transactTime": *************,
    "price": "300.********",
    "origQty": "0.********",
    "executedQty": "0.********",
    "cummulativeQuoteQty": "0.********",
    "status": "CANCELED",
    "timeInForce": "GTC",
    "type": "LIMIT",
    "side": "SELL",
    "selfTradePreventionMode": "NONE"
}`

	errRes = `{
    "code": -1102,
    "msg": "Mandatory parameter 'signature' was not sent, was empty/null, or malformed."
}`

	accBalancesAPIRes = APIAccountBalance{
		CanTrade:    true,
		AccountType: "SPOT",
		Permissions: []string{"SPOT"},
		List: []struct {
			Asset  string  `json:"asset"`
			Free   float64 `json:"free,string"`
			Locked float64 `json:"locked,string"`
		}{
			{
				Asset:  "BNB",
				Free:   999.********,
				Locked: 0.********,
			},
			{
				Asset:  "BTC",
				Free:   1.********,
				Locked: 0.********,
			},
			{
				Asset:  "BUSD",
				Free:   10000.********,
				Locked: 0.********,
			},
			{
				Asset:  "ETH",
				Free:   100.********,
				Locked: 0.********,
			},
			{
				Asset:  "LTC",
				Free:   500.********,
				Locked: 0.********,
			},
			{
				Asset:  "TRX",
				Free:   500000.********,
				Locked: 0.********,
			},
			{
				Asset:  "USDT",
				Free:   10024.********,
				Locked: 0.********,
			},
			{
				Asset:  "XRP",
				Free:   50000.********,
				Locked: 0.********,
			},
		},
	}
	openOrdersResponse = []APIOrder{
		{
			Symbol:        "BNBUSDT",
			OrderID:       1024677,
			ClientOrderID: "DoJhIXZ3rH5t2MRQ65keMf",
			Price:         247.********,
			OrigQty:       0.********,
			ExecutedQty:   0.********,
			Status:        "FILLED",
			Type:          "LIMIT",
			Side:          "SELL",
		},
		{
			Symbol:        "BNBUSDT",
			OrderID:       1174260,
			ClientOrderID: "yBtoYgRjgQAtD5bsi04GUM",
			Price:         247.********,
			OrigQty:       0.********,
			ExecutedQty:   0.********,
			Status:        "FILLED",
			Type:          "LIMIT",
			Side:          "SELL",
		},
		{
			Symbol:        "BNBUSDT",
			OrderID:       1183943,
			ClientOrderID: "q9xqgl7f7n9ehWHQ9CezVn",
			Price:         300.********,
			OrigQty:       0.********,
			ExecutedQty:   0.********,
			Status:        "CANCELED",
			Type:          "LIMIT",
			Side:          "SELL",
		},
		{
			Symbol:        "BNBUSDT",
			OrderID:       1186690,
			ClientOrderID: "imsdWV4vj3YEoq8B7GlG8E",
			Price:         300.********,
			OrigQty:       0.********,
			ExecutedQty:   0.********,
			Status:        "NEW",
			Type:          "LIMIT",
			Side:          "SELL",
		},
	}
	placeOrderResponse = "1186690"
	depthResponse      = APIDepth{
		LastUpdateID: 1996592,
		Bids:         []gateway.PriceArray{{Price: 247.41000000, Amount: 0.05000000}},
		Asks:         []gateway.PriceArray{{Price: 247.44000000, Amount: 847.********}},
	}
)

func newAPI(serverURL string) *API {
	return &API{
		options: gateway.Options{
			ApiSecret: "",
			ApiKey:    "",
		},
		httpClient: &utils.HttpClient{},
		baseURL:    serverURL,
	}
}

func TestNewAPI(t *testing.T) {
	api := NewAPI(gateway.Options{}, apiSpot, apiData)
	assert.Equal(t, api.baseURL, apiSpot)
}

func TestBalances(t *testing.T) {
	tests := []struct {
		purpose          string
		mockHTTPRes      utils.MockResponse
		expectedError    error
		expectedBalances APIAccountBalance
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiAccountBalance,
				StatusCode: http.StatusOK,
				Response:   errRes,
			},
			expectedBalances: APIAccountBalance{},
			expectedError: &apiError{
				Endpoint: apiAccountBalance,
				Exchange: Exchange.String(),
				Err:      errors.New("failed to make http request. code: -1102: msg: Mandatory parameter 'signature' was not sent, was empty/null, or malformed."),
			},
		},
		{
			purpose: "Success",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiAccountBalance,
				StatusCode: http.StatusOK,
				Response:   accBalancesRes,
			},
			expectedBalances: accBalancesAPIRes,
			expectedError:    nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, tt.mockHTTPRes.StatusCode, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			balances, err := newAPI(mockServer.URL).AccountBalance()
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedBalances, balances)
		})
	}
}

func TestOpenOrders(t *testing.T) {
	tests := []struct {
		purpose            string
		symbol             string
		mockHTTPRes        utils.MockResponse
		expectedError      error
		expectedOpenOrders []APIOrder
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiOpenOrders,
				StatusCode: http.StatusOK,
				Response:   errRes,
			},
			expectedOpenOrders: []APIOrder{},
			expectedError: &apiError{
				Endpoint: apiOpenOrders,
				Exchange: Exchange.String(),
				Err:      errors.New("failed to make http request. code: -1102: msg: Mandatory parameter 'signature' was not sent, was empty/null, or malformed."),
			},
		},
		{
			purpose: "Success",
			symbol:  "BNBUSDT",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiOpenOrders,
				StatusCode: http.StatusOK,
				Response:   openOrdersRes,
			},
			expectedOpenOrders: openOrdersResponse,
			expectedError:      nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, tt.mockHTTPRes.StatusCode, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			balances, err := newAPI(mockServer.URL).OpenOrders(tt.symbol)
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedOpenOrders, balances)
		})
	}
}

func TestPlaceOrder(t *testing.T) {
	tests := []struct {
		purpose         string
		order           APIPlaceOrder
		mockHTTPRes     utils.MockResponse
		expectedError   error
		expectedOrderID string
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiPlaceOrder,
				StatusCode: http.StatusOK,
				Response:   errRes,
			},
			expectedOrderID: "",
			expectedError: &apiError{
				Endpoint: apiPlaceOrder,
				Exchange: Exchange.String(),
				Err:      errors.New("failed to make http request. code: -1102: msg: Mandatory parameter 'signature' was not sent, was empty/null, or malformed."),
			},
		},
		{
			purpose: "Success",
			order:   APIPlaceOrder{},
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiPlaceOrder,
				StatusCode: http.StatusOK,
				Response:   placeOrderRes,
			},
			expectedOrderID: placeOrderResponse,
			expectedError:   nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, tt.mockHTTPRes.StatusCode, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			orderID, err := newAPI(mockServer.URL).PlaceOrder(tt.order)
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectedOrderID, orderID)
		})
	}
}

func TestSymbols(t *testing.T) {
	tests := []struct {
		purpose       string
		order         APIPlaceOrder
		mockHTTPRes   utils.MockResponse
		expectedError error
		expectSymbol  string
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiSymbols,
				StatusCode: http.StatusOK,
				Response:   errRes,
			},
			expectSymbol: "",
			expectedError: &apiError{
				Endpoint: apiSymbols,
				Exchange: Exchange.String(),
				Err:      errors.New("failed to make http request. code: -1102: msg: Mandatory parameter 'signature' was not sent, was empty/null, or malformed."),
			},
		},
		{
			purpose: "Success",
			order:   APIPlaceOrder{},
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiSymbols,
				StatusCode: http.StatusOK,
				Response:   symbolsRes,
			},
			expectSymbol:  "BNBBUSD",
			expectedError: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, tt.mockHTTPRes.StatusCode, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			symbols, err := newAPI(mockServer.URL).Symbols()
			assert.Equal(t, tt.expectedError, err)
			if len(symbols) > 1 {
				assert.Equal(t, tt.expectSymbol, symbols[0].Symbol)
			}
		})
	}
}

func TestDepth(t *testing.T) {
	tests := []struct {
		purpose       string
		symbol        string
		mockHTTPRes   utils.MockResponse
		expectedError error
		expectDepth   APIDepth
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiDepth,
				StatusCode: http.StatusOK,
				Response:   errRes,
			},
			expectDepth: APIDepth{},
			expectedError: &apiError{
				Endpoint: apiDepth,
				Exchange: Exchange.String(),
				Err:      errors.New("failed to make http request. code: -1102: msg: Mandatory parameter 'signature' was not sent, was empty/null, or malformed."),
			},
		},
		{
			purpose: "Success",
			symbol:  "BNBUSDT",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiDepth,
				StatusCode: http.StatusOK,
				Response:   depthRes,
			},
			expectDepth:   depthResponse,
			expectedError: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, tt.mockHTTPRes.StatusCode, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			depth, err := newAPI(mockServer.URL).Depth(tt.symbol, "1")
			assert.Equal(t, tt.expectedError, err)
			assert.Equal(t, tt.expectDepth, depth)
		})
	}
}

func TestCancelOrder(t *testing.T) {
	tests := []struct {
		purpose       string
		mockHTTPRes   utils.MockResponse
		expectedError error
	}{
		{
			purpose: "Unhappy path missing apikey and signature",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiCancelOrder,
				StatusCode: http.StatusOK,
				Response:   errRes,
			},
			expectedError: &apiError{
				Endpoint: apiCancelOrder,
				Exchange: Exchange.String(),
				Err:      errors.New("failed to make http request. code: -1102: msg: Mandatory parameter 'signature' was not sent, was empty/null, or malformed."),
			},
		},
		{
			purpose: "Success",
			mockHTTPRes: utils.MockResponse{
				Endpoint:   apiCancelOrder,
				StatusCode: http.StatusOK,
				Response:   cancelOrderRes,
			},
			expectedError: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := utils.HttpMock(tt.mockHTTPRes.Endpoint, tt.mockHTTPRes.StatusCode, tt.mockHTTPRes.Response)
			defer mockServer.Close()

			err := newAPI(mockServer.URL).CancelOrder("1183943", "BNBUSDT")
			assert.Equal(t, tt.expectedError, err)
		})
	}
}
