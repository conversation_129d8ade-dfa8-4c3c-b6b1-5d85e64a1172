package binance

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiSpot    = "https://api.binance.com"
	apiSandBox = "https://testnet.binance.vision"
	apiData    = "https://data-api.binance.vision"

	public       = false
	apiTimestamp = "/api/v3/time"
	apiSymbols   = "/api/v3/exchangeInfo"
	apiDepth     = "/api/v3/depth"
	fAPISymbols  = "/fapi/v1/exchangeInfo"
	fAPIDepth    = "/fapi/v1/depth"

	private           = true
	apiPlaceOrder     = "/api/v3/order"
	apiCancelOrder    = "/api/v3/order"
	apiOpenOrders     = "/api/v3/openOrders"
	apiAccountBalance = "/api/v3/account"
	apiUserDataStream = "/api/v3/userDataStream"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
	baseURL    string
	dataURL    string
}

func NewAPI(options gateway.Options, baseURL, dataURL string) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options:    options,
		httpClient: client,
		baseURL:    baseURL,
		dataURL:    dataURL,
	}
}

type apiError struct {
	Endpoint string
	Exchange string
	Err      error
}

func (e *apiError) Error() string {
	return "[" + e.Exchange + "] " + "failed to request api [" + e.Endpoint + "] error: " + e.Err.Error()
}

func (a *API) newAPIError(endpoint string, err error) *apiError {
	return &apiError{endpoint, Exchange.String(), err}
}

func (a *API) Timestamp() (int64, error) {
	req, err := a.newHTTPRequest(http.MethodGet, map[string]string{}, apiTimestamp, private, includeSignature)
	if err != nil {
		return 0, a.newAPIError(apiTimestamp, err)
	}

	var ts int64
	if err = a.makeHTTPRequest(req, &ts); err != nil {
		return 0, a.newAPIError(apiTimestamp, err)
	}

	return ts, nil
}

type APIDepth struct {
	LastUpdateID int64                `json:"lastUpdateId"`
	Bids         []gateway.PriceArray `json:"bids"`
	Asks         []gateway.PriceArray `json:"asks"`
}

func (a *API) Depth(symbol, limit string) (APIDepth, error) {
	params := map[string]string{
		"symbol": symbol,
		"limit":  limit,
	}
	req, err := a.newHTTPRequest(http.MethodGet, params, apiDepth, public, excludeSignature)
	if err != nil {
		return APIDepth{}, a.newAPIError(apiDepth, err)
	}

	var res APIDepth
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return APIDepth{}, a.newAPIError(apiDepth, err)
	}

	return res, nil
}

func (a *API) FDepth(symbol, limit string) (APIDepth, error) {
	params := map[string]string{
		"symbol": symbol,
		"limit":  limit,
	}
	req, err := a.newHTTPRequest(http.MethodGet, params, fAPIDepth, public, excludeSignature)
	if err != nil {
		return APIDepth{}, a.newAPIError(fAPIDepth, err)
	}

	var res APIDepth
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return APIDepth{}, a.newAPIError(fAPIDepth, err)
	}

	return res, nil
}

type APISymbol struct {
	Symbol                   string `json:"symbol"`
	Status                   string `json:"status"`
	BaseAsset                string `json:"baseAsset"`
	QuoteAsset               string `json:"quoteAsset"`
	QuotePrecision           int64  `json:"quotePrecision"`
	QuoteAssetPrecision      int64  `json:"quoteAssetPrecision"`
	BaseAssetPrecision       int    `json:"baseAssetPrecision"`
	BaseCommissionPrecision  int    `json:"baseCommissionPrecision"`
	QuoteCommissionPrecision int    `json:"quoteCommissionPrecision"`
	Filters                  []struct {
		FilterType  string  `json:"filterType"`
		MinPrice    float64 `json:"minPrice,string"`
		TickSize    float64 `json:"tickSize,string"`
		MinQty      float64 `json:"minQty,string"`
		MaxQty      float64 `json:"maxQty,string"`
		StepSize    float64 `json:"stepSize,string"`
		MinNotional float64 `json:"minNotional,string"`
	} `json:"filters"`
}

func (a *API) Symbols() ([]APISymbol, error) {
	req, err := a.newHTTPRequest(http.MethodGet, map[string]string{}, apiSymbols, public, excludeSignature)
	if err != nil {
		return []APISymbol{}, a.newAPIError(apiSymbols, err)
	}

	var symbols struct {
		Symbols []APISymbol `json:"symbols"`
	}
	if err = a.makeHTTPRequest(req, &symbols); err != nil {
		return []APISymbol{}, a.newAPIError(apiSymbols, err)
	}

	return symbols.Symbols, nil
}

func (a *API) FSymbols() ([]APISymbol, error) {
	req, err := a.newHTTPRequest(http.MethodGet, map[string]string{}, fAPISymbols, public, excludeSignature)
	if err != nil {
		return []APISymbol{}, a.newAPIError(fAPISymbols, err)
	}

	var symbols struct {
		Symbols []APISymbol `json:"symbols"`
	}
	if err = a.makeHTTPRequest(req, &symbols); err != nil {
		return []APISymbol{}, a.newAPIError(fAPISymbols, err)
	}

	return symbols.Symbols, nil
}

type APIAccountBalance struct {
	CanTrade    bool     `json:"canTrade"`
	AccountType string   `json:"accountType"`
	Permissions []string `json:"permissions"`
	List        []struct {
		Asset  string  `json:"asset"`
		Free   float64 `json:"free,string"`
		Locked float64 `json:"locked,string"`
	} `json:"balances"`
}

func (a *API) AccountBalance() (APIAccountBalance, error) {
	req, err := a.newHTTPRequest(http.MethodGet, map[string]string{}, apiAccountBalance, private, includeSignature)
	if err != nil {
		return APIAccountBalance{}, a.newAPIError(apiAccountBalance, err)
	}

	var res APIAccountBalance
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return APIAccountBalance{}, a.newAPIError(apiAccountBalance, err)
	}

	return res, nil
}

type APIPlaceOrder struct {
	Quantity      string `json:"quantity"`
	Price         string `json:"price"`
	Symbol        string `json:"symbol"`
	Type          string `json:"type"`
	TimeInForce   string `json:"timeInForce,omitempty"`
	Side          string `json:"side"`
	Signature     string `json:"signature"`
	ClientOrderID string `json:"clientOrderId"`
	TimeStamp     int64  `json:"timestamp"`
}

func (a *API) PlaceOrder(order APIPlaceOrder) (string, error) {
	params := map[string]string{
		"symbol":   order.Symbol,
		"side":     order.Side,
		"type":     order.Type,
		"quantity": order.Quantity,
	}

	switch order.Type {
	case "LIMIT":
		params["price"] = order.Price
		params["timeInForce"] = order.TimeInForce
	case "LIMIT_MAKER":
		params["price"] = order.Price
	}

	req, err := a.newHTTPRequest(http.MethodPost, params, apiPlaceOrder, private, includeSignature)
	if err != nil {
		return "", a.newAPIError(apiPlaceOrder, err)
	}

	var res APIOrder
	if err = a.makeHTTPRequest(req, &res); err != nil {
		errStr := err.Error()
		switch {
		case strings.Contains(errStr, "Account has insufficient balance for requested action."):
			return "", gateway.InsufficientBalanceErr
		case strings.Contains(errStr, "Invalid quantity.") || strings.Contains(errStr, "Filter failure: NOTIONAL"):
			return "", gateway.MinOrderSizeErr
		}

		return "", a.newAPIError(apiPlaceOrder, err)
	}

	return strconv.FormatInt(res.OrderID, 10), nil
}

func (a *API) CancelOrder(orderID string, symbol string) error {
	params := map[string]string{
		"orderId": orderID,
		"symbol":  symbol,
	}
	req, err := a.newHTTPRequest(http.MethodDelete, params, apiCancelOrder, private, includeSignature)
	if err != nil {
		return a.newAPIError(apiPlaceOrder, err)
	}

	if err = a.makeHTTPRequest(req, nil); err != nil {
		return a.newAPIError(apiPlaceOrder, err)
	}

	return nil
}

type APIOrder struct {
	Symbol        string  `json:"symbol"`
	ClientOrderID string  `json:"clientOrderId"`
	Status        string  `json:"status"`
	Type          string  `json:"type"`
	Side          string  `json:"side"`
	OrderID       int64   `json:"orderId"`
	Price         float64 `json:"price,string"`
	OrigQty       float64 `json:"origQty,string"`
	ExecutedQty   float64 `json:"executedQty,string"`
}

func (a *API) OpenOrders(symbol string) ([]APIOrder, error) {
	req, err := a.newHTTPRequest(http.MethodGet, map[string]string{"symbol": symbol}, apiOpenOrders, private, includeSignature)
	if err != nil {
		return []APIOrder{}, a.newAPIError(apiOpenOrders, err)
	}

	var res []APIOrder
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return []APIOrder{}, a.newAPIError(apiOpenOrders, err)
	}

	return res, err
}

const includeSignature = false
const excludeSignature = true

func (a *API) NewStartUserStreamService() (string, error) {
	req, err := a.newHTTPRequest(http.MethodPost, map[string]string{}, apiUserDataStream, private, excludeSignature)
	if err != nil {
		return "", a.newAPIError(apiUserDataStream, err)
	}

	var res struct {
		ListenKey string `json:"listenKey"`
	}
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return "", a.newAPIError(apiUserDataStream, err)
	}

	return res.ListenKey, nil
}
func (a *API) KeepaliveUserStreamService(listenKey string) error {
	req, err := a.newHTTPRequest(http.MethodPut, map[string]string{"listenKey": listenKey}, apiUserDataStream, private, excludeSignature)
	if err != nil {
		return a.newAPIError(apiUserDataStream, err)
	}

	if err = a.makeHTTPRequest(req, struct{}{}); err != nil {
		return a.newAPIError(apiUserDataStream, err)
	}

	return nil
}

func (a *API) newHTTPRequest(method string, params map[string]string, endpoint string, isPrivate, excludeSignature bool) (*http.Request, error) {
	var baseURL string
	if isPrivate {
		baseURL = a.baseURL
	} else {
		baseURL = a.dataURL
	}

	parsedURL, err := a.httpClient.ParseURLRequest(baseURL, endpoint)
	if err != nil {
		return &http.Request{}, err
	}

	req, err := http.NewRequest(method, parsedURL.String(), nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")

	// mount the query
	q := req.URL.Query()
	for k, v := range params {
		q.Set(k, v)
	}

	if isPrivate {
		req.Header.Set("X-MBX-APIKEY", a.options.ApiKey)

		if !excludeSignature {
			q.Set("timestamp", fmt.Sprintf("%d", time.Now().UnixNano()/int64(time.Millisecond)))
			// encode query for signature
			req.URL.RawQuery = q.Encode()
			q.Set("signature", generateSignature(req.URL.RawQuery, a.options.ApiSecret))
		}
	}

	// encode final query
	req.URL.RawQuery = q.Encode()

	return req, nil
}

func (a *API) makeHTTPRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}

	if err = validateHTTPRequest(body); err != nil {
		return err
	}

	if responseObject != nil {
		if err = json.Unmarshal(body, responseObject); err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %w", string(body), err)
		}
	}

	return nil
}

func validateHTTPRequest(body []byte) error {
	if len(body) > 0 && string(body[0]) == "[" {
		return nil
	}

	var apiResError struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}

	if err := json.Unmarshal(body, &apiResError); err != nil {
		return err
	}

	if apiResError.Msg != "" {
		return fmt.Errorf("failed to make http request. code: %v: msg: %s", apiResError.Code, apiResError.Msg)
	}
	return nil
}

// generateSignature generates HMAC SHA256 signature
func generateSignature(query string, secretKey string) string {
	mac := hmac.New(sha256.New, []byte(secretKey))
	mac.Write([]byte(query))
	signingKey := fmt.Sprintf("%x", mac.Sum(nil))
	return signingKey
}
