package bitmart

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "BitMart",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.tickCh, g.api, g.options)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return err
		}
	}

	g.marketDataGateway = NewMarketDataGateway(g.options, g.tickCh)
	if err := g.marketDataGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (gtw *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := gtw.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return err
	}

	if gtw.options.ApiKey != "" {
		return gtw.accountGateway.SubscribeMarkets(markets)
	}

	return nil
}

func (gtw *Gateway) Close() error {
	return nil
}

func (gtw *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	markets, err := g.api.GetMarkets()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(markets))
	for _, market := range markets {
		commonMarkets = append(commonMarkets, normalizeMarket(market))
	}

	return commonMarkets, nil
}

func (gtw *Gateway) AccountGateway() gateway.AccountGateway {
	return gtw.accountGateway
}

func (gtw *Gateway) Tick() chan gateway.Tick {
	return gtw.tickCh
}

func normalizeMarket(market ApiMarket) gateway.Market {
	pair := gateway.Pair{
		Base:  strings.ToUpper(market.BaseCurrency),
		Quote: strings.ToUpper(market.QuoteCurrency),
	}

	var closed bool
	if market.TradeStatus != "trading" {
		closed = true
	}

	return gateway.Market{
		Exchange:               Exchange,
		Pair:                   pair,
		Symbol:                 market.Symbol,
		TakerFee:               0.0025,
		MakerFee:               0.0025,
		PriceTick:              1 / math.Pow10(market.PriceMaxPrecision),
		AmountTick:             market.QuoteIncrement,
		MinimumOrderSize:       market.BaseMinSize,
		MinimumOrderMoneyValue: market.MinBuyAmount,
		Closed:                 closed,
		MarketType:             gateway.SpotMarket,
	}
}

func parsePriceLevelsToDepth(levels [][]string) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel

	for _, level := range levels {
		if len(level) < 2 {
			return nil, fmt.Errorf("invalid level: %v", level)
		}
		price, err := strconv.ParseFloat(level[0], 64)
		if err != nil {
			return nil, err
		}
		amount, err := strconv.ParseFloat(level[1], 64)
		if err != nil {
			return nil, err
		}
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		})
	}

	return priceLevels, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks, err := parsePriceLevelsToDepth(depth.Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	bids, err := parsePriceLevelsToDepth(depth.Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}

func (g *Gateway) SupportedMethods() gateway.Methods {
	return gateway.Methods{
		CIDMapping: true,
	}
}
