package bitmart

import (
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"regexp"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type MarketDataGateway struct {
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewMarketDataGateway(opts gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options: opts,
		tickCh:  tickCh,
	}
}

func (mg *MarketDataGateway) Connect() error {
	return nil
}

const MAX_BOOK_DEPTH = 50

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	log.Printf("Bitmart opening %d websocket connections to subscribe to %d pairs", len(markets), len(markets))

	if len(g.options.Proxies) > 0 {
		log.Printf("Bitmart using %d proxy servers to distribute connections", len(g.options.Proxies))
	}

	type proxyAndMarkets struct {
		proxy   *url.URL
		markets []gateway.Market
	}

	batchesOf := 50
	batches := make([]proxyAndMarkets, 0)

	for index, market := range markets {
		group := index / batchesOf

		if len(batches) <= group {
			var proxy *url.URL
			if len(g.options.Proxies) > 0 {
				proxy = g.options.Proxies[group%len(g.options.Proxies)]
			}

			batches = append(batches, proxyAndMarkets{
				proxy:   proxy,
				markets: make([]gateway.Market, 0),
			})
		}

		markets := batches[group].markets
		markets = append(markets, market)
		batches[group].markets = markets
	}

	log.Printf("Bitmart has [%d] markets, will need to distribute market data subscriptions in %d websocket connections, maximum of [%d] subscriptions on each websocket.", len(markets), len(batches), batchesOf)

	for _, batch := range batches {
		err := g.connectWs(batch.markets, batch.proxy)
		if err != nil {
			return err
		}
	}

	return nil
}

func (mg *MarketDataGateway) connectWs(markets []gateway.Market, proxy *url.URL) error {
	ws := NewWsSession(mg.options)
	if proxy != nil {
		ws.SetProxy(proxy)
	}

	err := ws.Connect(WsPublicURL)
	if err != nil {
		return fmt.Errorf("mktd failed to connect to ws [using proxy %v], err: %s", proxy, err)
	}

	log.Printf("%s subscribing to [%d] markets on proxy [%s]...", Exchange.Name, len(markets), proxy)

	ch := make(chan WsResponse)
	ws.SubscribeMessages(ch, nil)
	go mg.subscriptionMessageHandler(ch)

	var requests []WsRequest

	for _, market := range markets {
		req := WsRequest{
			Op: "subscribe",
			Args: []string{
				fmt.Sprintf("spot/depth%d:%s", MAX_BOOK_DEPTH, market.Symbol),
				fmt.Sprintf("spot/trade:%s", market.Symbol),
			},
		}

		requests = append(requests, req)
	}

	err = ws.RequestSubscriptions(requests)
	if err != nil {
		return err
	}

	return nil
}

var orderbookRegex = regexp.MustCompile(`spot/depth`)
var tradesRegex = regexp.MustCompile(`spot/trade`)

func (mg *MarketDataGateway) subscriptionMessageHandler(ch chan WsResponse) {
	for msg := range ch {
		switch {
		case orderbookRegex.MatchString(msg.Table):
			if err := mg.processOrderBook(msg); err != nil {
				log.Printf("%s error processing \"%s\": %s", Exchange.Name, msg.Table, err)
			}
		case tradesRegex.MatchString(msg.Table):
			if err := mg.processTrades(msg); err != nil {
				log.Printf("%s error processing \"%s\": %s", Exchange.Name, msg.Table, err)
			}
		}
	}
}

func (mg *MarketDataGateway) processOrderBook(msg WsResponse) error {
	var eventLogs []gateway.Event

	orderBooks := []WsOrderBook{}
	err := json.Unmarshal(msg.Data, &orderBooks)
	if err != nil {
		return fmt.Errorf("%s - Failed to unmarshal order book. Err: %s", Exchange.Name, err)
	}

	appendEvents := func(symbol string, side gateway.Side, prices []gateway.PriceArray) {
		for _, order := range prices {
			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  order.Price,
					Amount: order.Amount,
				},
			}

			eventLogs = append(eventLogs, event)
		}
	}

	for _, orderBook := range orderBooks {
		// This is not specified, but seems like, Bitmart started always sending a full book
		// snapshot. Before they were sending incremental updates, so this behaviour might change.
		eventLogs = append(eventLogs, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: orderBook.Symbol,
			},
		})

		appendEvents(orderBook.Symbol, gateway.Bid, orderBook.Bids)
		appendEvents(orderBook.Symbol, gateway.Ask, orderBook.Asks)
	}

	mg.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLogs,
	}

	return nil
}

func (mg *MarketDataGateway) processTrades(msg WsResponse) error {
	trades := []WsTrade{}

	if err := json.Unmarshal(msg.Data, &trades); err != nil {
		return fmt.Errorf("%s - Failed to unmarshal trades. Err: %s", Exchange.Name, err)
	}

	eventLogs := make([]gateway.Event, len(trades))

	for i, trade := range trades {
		var direction gateway.Side
		if trade.Side == "sell" {
			direction = gateway.Ask
		} else {
			direction = gateway.Bid
		}

		price, err := convertToFloat(trade.Price)
		if err != nil {
			return fmt.Errorf("failed to parse filled price %s error: %s", trade.Price, err)
		}
		size, err := convertToFloat(trade.Size)
		if err != nil {
			return fmt.Errorf("failed to parse filled size %s error: %s", trade.Size, err)
		}

		eventLogs[i] = gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				Timestamp: gateway.ParseTimestamp(trade.Timestamp),
				Symbol:    trade.Symbol,
				Direction: direction,
				Amount:    size,
				Price:     price,
			},
		}
	}

	mg.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLogs,
	}

	return nil
}
