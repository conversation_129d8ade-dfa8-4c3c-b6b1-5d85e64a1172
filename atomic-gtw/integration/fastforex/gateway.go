package fastforex

import (
	"log"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "fastFOREX",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *MarketDataGateway
	api               *API
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	if options.FxSource == "" {
		options.FxSource = "USD" // Default base currency
	}

	gtw := &Gateway{
		api:     NewAPI(options.ApiKey),
		options: options,
		tickCh:  make(chan gateway.Tick, 10),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	log.Printf("FastForex using source currency [%s]", g.options.FxSource)

	g.marketDataGateway = NewMarketDataGateway(g.api, g.options, g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets()
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

// All theirs rates are in this given price tick
const PRICE_TICK = 0.00001

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.FetchAll(g.options.FxSource)
	if err != nil {
		log.Printf("%s failed to load markets while fetching quotes, err: %s", Exchange, err)
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(res.Results))
	for currency, _ := range res.Results {
		if currency == g.options.FxSource {
			continue
		}

		commonMarkets = append(commonMarkets, gateway.Market{
			Exchange: Exchange,
			Pair: gateway.Pair{
				Base:  g.options.FxSource,
				Quote: currency,
			},
			Symbol:     toSymbol(g.options.FxSource, currency),
			PriceTick:  PRICE_TICK,
			AmountTick: 0.01, // USD price tick. TODO: What if we use another source currency
			MarketType: gateway.SpotMarket,
		})
	}

	return commonMarkets, nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func toSymbol(base, quote string) string {
	return base + quote
}
