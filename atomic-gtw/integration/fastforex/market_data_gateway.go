package fastforex

import (
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type MarketDataGateway struct {
	api             *API
	options         gateway.Options
	refreshInterval time.Duration
	tickCh          chan gateway.Tick
	connected       bool
}

func NewMarketDataGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	refreshInterval := time.Duration(options.RefreshIntervalMs) * time.Millisecond
	if refreshInterval == 0*time.Second {
		log.Printf("FastForex defaulting refreshInterval to 60 seconds updates...")
		refreshInterval = 60 * time.Second
	} else if refreshInterval < 10*time.Second {
		log.Printf("FastForex refreshInterval canno't be less then 10 seconds, defaulting to 10 seconds updates...")
		refreshInterval = 10 * time.Second
	}

	return &MarketDataGateway{
		api:             api,
		options:         options,
		refreshInterval: refreshInterval,
		tickCh:          tickCh,
	}
}

func (g *MarketDataGateway) SubscribeMarkets() error {
	if g.connected {
		log.Printf("FastForex already connected, ignoring SubscribeMarkets...")
		return nil
	}

	lastRefresh := time.Now()
	err := g.refreshQuotes()
	if err != nil {
		return err
	}

	// Refresh quotes
	go func() {
		for {
			time.Sleep(g.refreshInterval)

			err := g.refreshQuotes()
			if err != nil {
				log.Printf("FastForex will retry in %s, after err to refresh: %s", g.refreshInterval, err)

				if time.Since(lastRefresh) > 1*time.Minute {
					log.Printf("FastForex failed to refresh quotes for 1 minute, exiting...")
					panic(err)
				}
			}

			lastRefresh = time.Now()
		}
	}()

	g.connected = true
	return nil
}

func (g *MarketDataGateway) refreshQuotes() error {
	res, err := g.api.FetchAll(g.options.FxSource)
	if err != nil {
		return err
	}

	events := make([]gateway.Event, 0, len(res.Results)*3)
	for currency, rate := range res.Results {
		symbol := toSymbol(g.options.FxSource, currency)

		events = append(events, gateway.Event{
			Type: gateway.SnapshotSequenceEvent,
			Data: gateway.SnapshotSequence{
				Symbol: symbol,
			},
		})

		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Bid,
				Price:  rate,
				Amount: 1000000000,
			},
		})

		events = append(events, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Ask,
				Price:  rate + PRICE_TICK,
				Amount: 1000000000,
			},
		})
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}

	return nil
}
