package fastforex

import (
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"

	"github.com/buger/jsonparser"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBase     = "https://api.fastforex.io"
	apiFetchAll = "/fetch-all"
)

type API struct {
	apiKey     string
	httpClient *utils.HttpClient
}

func NewAPI(apiKey string) *API {
	client := utils.NewHttpClient()

	return &API{
		apiKey:     apiKey,
		httpClient: client,
	}
}

type APIFetchAllRes struct {
	Base    string             `json:"base"`
	Results map[string]float64 `json:"results"`
}

func (a *API) FetchAll(source string) (res APIFetchAllRes, err error) {
	req, err := a.newHttpRequest(http.MethodGet, apiFetchAll, nil)
	if err != nil {
		return res, err
	}

	q := req.URL.Query()
	q.Add("from", source)
	req.URL.RawQuery = q.Encode()

	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) newHttpRequest(method string, path string, data io.Reader) (*http.Request, error) {
	url := apiBase + path
	req, err := http.NewRequest(method, url, data)
	if err != nil {
		return nil, err
	}

	q := req.URL.Query()
	q.Add("api_key", a.apiKey)
	req.URL.RawQuery = q.Encode()

	req.Header.Set("Content-Type", "application/json")

	return req, nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	body, err := a.sendHttpRequest(req)
	if err != nil {
		return err
	}

	errMessage, _ := jsonparser.GetString(body, "error")
	if errMessage != "" {
		return fmt.Errorf("api responded with error message: %s\nresponse body: %s", errMessage, string(body))
	}

	if responseObject != nil {
		err = json.Unmarshal(body, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", body, err)
		}
	}

	return nil
}

func (a *API) sendHttpRequest(req *http.Request) ([]byte, error) {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return nil, err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	return body, nil
}
