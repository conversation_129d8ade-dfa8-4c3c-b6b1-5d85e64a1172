package hotcoin

import (
	"fmt"
	"log"
	"math"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Hotcoin",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		tickCh:  make(chan gateway.Tick, 100),
		api:     NewAPI(options, apiBase),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.options, g.api, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	// Init market data
	g.marketDataGateway = NewMarketDataGateway(g.options, g.api, g.tickCh)
	if err := g.marketDataGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	res, err := g.api.Symbols()
	if err != nil {
		return nil, err
	}

	return symbolsToCommonMarket(res), nil
}

func symbolsToCommonMarket(symbols []APISymbol) []gateway.Market {
	commonMarkets := make([]gateway.Market, 0, len(symbols))

	for _, symbol := range symbols {
		market := symbolToCommonMarket(symbol)
		commonMarkets = append(commonMarkets, market)
	}

	return commonMarkets
}

func symbolToCommonMarket(symbol APISymbol) gateway.Market {
	parts := strings.Split(symbol.Symbol, "_")
	if len(parts) < 2 {
		log.Printf("Hotcoin market [%s] impossible to extract base/quote from symbol", symbol.Symbol)
		return gateway.Market{}
	}

	return gateway.Market{
		Exchange: Exchange,
		Symbol:   symbol.Symbol,
		Pair: gateway.Pair{
			Base:  strings.ToUpper(parts[0]),
			Quote: strings.ToUpper(parts[1]),
		},
		PriceTick:  1 / math.Pow10(int(symbol.PricePrecision)),
		AmountTick: 1 / math.Pow10(int(symbol.AmountPrecision)),
		MarketType: gateway.SpotMarket,
	}
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
func parsePriceLevelsToDepth(levels []gateway.PriceArray) []gateway.PriceLevel {
	var priceLevels []gateway.PriceLevel
	for _, level := range levels {
		priceLevels = append(priceLevels, gateway.PriceLevel(level))
	}
	return priceLevels
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depthRes, err := gtw.api.Depth(market.Symbol)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks := parsePriceLevelsToDepth(depthRes.Depth.Asks)
	bids := parsePriceLevelsToDepth(depthRes.Depth.Bids)
	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
