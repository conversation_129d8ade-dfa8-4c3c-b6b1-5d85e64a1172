package bitstamp

import (
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

var Exchange = gateway.Exchange{
	Name: "Bitstamp",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	marketDataGateway *MarketDataGateway
	accountGateway    *AccountGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options, apiBaseProd),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	if g.options.ApiKey != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("Failed to connect to account gateway, err %s", err)
		}
	}

	g.marketDataGateway = NewMarketDataGateway(g.tickCh)

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	err := g.marketDataGateway.SubscribeMarkets(markets)
	if err != nil {
		return fmt.Errorf("Failed to subscribe to market data, err %s", err)
	}

	if g.options.ApiKey != "" {
		err := g.accountGateway.SubscribeMarkets(markets)
		if err != nil {
			return fmt.Errorf("Failed to subscribe to account updates, err %s", err)
		}
	}
	return nil
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	tradingPairInfos, err := g.api.GetTradingPairsInfo()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(tradingPairInfos))
	for _, tradingPairInfo := range tradingPairInfos {
		market, err := g.parseToCommonMarket(tradingPairInfo)
		if err != nil {
			log.Printf("Failed to parse market %s, err %s", tradingPairInfo.Name, err)
			continue
		}
		commonMarkets = append(commonMarkets, market)
	}

	return commonMarkets, nil
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}

func (g *Gateway) parseToCommonMarket(tradingPairInfo ApiTradingPairInfo) (gateway.Market, error) {
	apiPair := strings.Split(tradingPairInfo.Name, "/")
	if len(apiPair) != 2 {
		return gateway.Market{}, fmt.Errorf("Failed to split pair name %s into base/quote", tradingPairInfo.Name)
	}

	minOrderPrice := strings.Split(tradingPairInfo.MinimumOrder, " ")
	minimumOrderMoneyValue, _ := strconv.ParseFloat(minOrderPrice[0], 64)
	pair := gateway.Pair{
		Base:  strings.ToUpper(apiPair[0]),
		Quote: strings.ToUpper(apiPair[1]),
	}
	priceTick := utils.PrecisionToTick(tradingPairInfo.CounterDecimals)
	tickSize := utils.PrecisionToTick(tradingPairInfo.BaseDecimals)

	marketClose := false
	if tradingPairInfo.InstantAndMarketOrders == "Enabled" {
		marketClose = true
	}

	return gateway.Market{
		Exchange:               Exchange,
		Pair:                   pair,
		Symbol:                 tradingPairInfo.URLSymbol,
		TakerFee:               0.005,
		MakerFee:               0.005,
		PriceTick:              priceTick,
		AmountTick:             tickSize,
		MinimumOrderSize:       0,
		Closed:                 marketClose,
		MinimumOrderMoneyValue: minimumOrderMoneyValue,
		MarketType:             gateway.SpotMarket,
	}, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	orderBook, err := g.api.GetOrderBook(market.Symbol)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: gateway.PriceArrayToPriceLevels(orderBook.Asks),
		Bids: gateway.PriceArrayToPriceLevels(orderBook.Bids),
	}

	return depthBook, nil
}
