package bitstamp

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBaseProd           = "https://www.bitstamp.net"
	apiTradingPairsInfo   = "/api/v2/trading-pairs-info/"
	apiAccountBalance     = "/api/v2/account_balances/"
	apiOpenOrders         = "/api/v2/open_orders/"
	apiGetWebSocketsToken = "/api/v2/websockets_token/"
	apiPlaceOrder         = "/api/v2/"
	apiCancelOrder        = "/api/v2/cancel_order/"
	apiOrderBook          = "/api/v2/order_book/%s/?group=1"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
	baseURL    string
}

func NewAPI(options gateway.Options, baseURL string) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options:    options,
		httpClient: client,
		baseURL:    baseURL,
	}
}

type ApiTradingPairInfo struct {
	Name                   string `json:"name"`
	URLSymbol              string `json:"url_symbol"`
	BaseDecimals           int    `json:"base_decimals"`
	MinimumOrder           string `json:"minimum_order"`
	CounterDecimals        int    `json:"counter_decimals"`
	InstantAndMarketOrders string `json:"instant_and_market_orders"`
}

func (a *API) GetTradingPairsInfo() ([]ApiTradingPairInfo, error) {
	req, err := a.newHttpRequest(http.MethodGet, apiTradingPairsInfo, nil, false)
	if err != nil {
		return nil, err
	}

	resp := make([]ApiTradingPairInfo, 0)
	if err = a.makeHttpRequest(req, &resp); err != nil {
		return nil, err
	}

	return resp, nil
}

type ApiAccountBalance struct {
	Currency  string  `json:"currency"`
	Total     float64 `json:"total,string"`
	Available float64 `json:"available,string"`
	Reserved  string  `json:"reserved"`
}

func (a *API) AccountBalance() (resp []ApiAccountBalance, err error) {
	req, err := a.newHttpRequest(http.MethodPost, apiAccountBalance, nil, true)
	if err != nil {
		return nil, err
	}

	var apiBalance []ApiAccountBalance
	if err = a.makeHttpRequest(req, &apiBalance); err != nil {
		return nil, err
	}

	return apiBalance, nil
}

type ApiOrder struct {
	Id             string  `json:"id"`
	Datetime       string  `json:"datetime"`
	Type           string  `json:"type"`
	Amount         float64 `json:"amount,string"`
	Price          float64 `json:"price,string"`
	AmountAtCreate string  `json:"amount_at_create"`
	CurrencyPair   string  `json:"currency_pair"`
}

type ApiError struct {
	Reason string `json:"reason"`
	Status string `json:"status"`
}

func (a *API) OpenOrders(currency string) (resp []ApiOrder, err error) {
	url := apiOpenOrders + currency + "/"
	req, err := a.newHttpRequest(http.MethodPost, url, nil, true)
	if err != nil {
		return nil, err
	}

	var apiOrders []ApiOrder
	if err = a.makeHttpRequest(req, &apiOrders); err != nil {
		return nil, err
	}

	return apiOrders, nil
}

type apiToken struct {
	ValidSec int    `json:"valid_sec"`
	Token    string `json:"token"`
	UserId   int    `json:"user_id"`
}

func (a *API) GetToken() (token string, userID int, err error) {
	req, err := a.newHttpRequest(http.MethodPost, apiGetWebSocketsToken, nil, true)
	if err != nil {
		return "", 0, err
	}

	var apiWebsocketToken apiToken
	err = a.makeHttpRequest(req, &apiWebsocketToken)
	if err != nil {
		err = fmt.Errorf("error Get Token: %s", err)
		return "", 0, err
	}

	token = apiWebsocketToken.Token
	userID = apiWebsocketToken.UserId
	return token, userID, nil
}

func (a *API) getSignature(url_path string, payload string, timestamp string, nonce string, content_type string) string {
	message := "BITSTAMP " + a.options.ApiKey + "POST" + "www.bitstamp.net" + url_path
	if payload != "" {
		message += content_type
	}
	message += nonce + timestamp + "v2" + payload
	message_bytes := []byte(message)
	signature := hmac.New(sha256.New, []byte(a.options.ApiSecret))
	signature.Write(message_bytes)
	signature_string := fmt.Sprintf("%x", signature.Sum(nil))
	return signature_string
}

func (a *API) newHttpRequest(method string, path string, params *url.Values, signed bool) (*http.Request, error) {
	var reqBody string
	if params != nil {
		reqBody = params.Encode()
	}

	urlRequest := a.baseURL + path
	req, err := http.NewRequest(method, urlRequest, strings.NewReader(reqBody))
	if err != nil {
		return nil, err
	}

	contentType := "application/x-www-form-urlencoded"
	if reqBody != "" {
		req.Header.Set("Content-Type", contentType)
	}

	if signed {
		timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
		nonce := uuid.New().String()
		signature := a.getSignature(path, reqBody, timestamp, nonce, contentType)
		req.Header.Set("X-Auth", "BITSTAMP "+a.options.ApiKey)
		req.Header.Set("X-Auth-Signature", signature)
		req.Header.Set("X-Auth-Nonce", nonce)
		req.Header.Set("X-Auth-Timestamp", timestamp)
		req.Header.Set("X-Auth-Version", "v2")
		req.ContentLength = int64(len(reqBody))
	}

	return req, nil
}

func (a *API) PlaceOrder(order APIOrderReq, side string, currency string) (orderID string, err error) {
	urlAPI := fmt.Sprintf("%s%s/%s/", apiPlaceOrder, side, currency)
	params := url.Values{}
	params.Set("price", order.Price)
	params.Set("amount", order.Amount)

	req, err := a.newHttpRequest(http.MethodPost, urlAPI, &params, true)
	if err != nil {
		return "", err
	}

	var apiOrder ApiOrder
	err = a.makeHttpRequest(req, &apiOrder)
	if err != nil {
		return "", err
	}

	return apiOrder.Id, nil
}

func (a *API) CancelOrder(orderID string) (err error) {
	params := url.Values{}
	params.Set("id", orderID)
	req, err := a.newHttpRequest(http.MethodPost, apiCancelOrder, &params, true)
	if err != nil {
		return err
	}

	err = a.makeHttpRequest(req, nil)
	if err != nil {
		return fmt.Errorf("failed to cancel order, orderID: %s, err: %s", orderID, err)
	}
	return nil
}

type ApiOrderBook struct {
	Timestamp      string               `json:"timestamp"`
	Microtimestamp string               `json:"microtimestamp"`
	Bids           []gateway.PriceArray `json:"bids"`
	Asks           []gateway.PriceArray `json:"asks"`
}

func (a *API) GetOrderBook(symbol string) (ApiOrderBook, error) {
	urlPath := fmt.Sprintf(apiOrderBook, symbol)
	params := url.Values{}

	urlRequest := a.baseURL + urlPath
	if len(params) > 0 {
		urlRequest += params.Encode()
	}

	req, err := http.NewRequest(http.MethodGet, urlRequest, nil)
	if err != nil {
		return ApiOrderBook{}, err
	}

	var orderBook ApiOrderBook
	err = a.makeHttpRequest(req, &orderBook)
	if err != nil {
		return ApiOrderBook{}, err
	}

	return orderBook, nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}

	if len(body) > 0 && string(body[0]) == "[" {
		err = json.Unmarshal(body, responseObject)
		if err != nil {
			return fmt.Errorf("failed to unmarshal body [%s] into responseObject, err: %s", string(body), err)
		}
	} else if len(body) > 0 {
		var apiRes ApiError
		err = json.Unmarshal(body, &apiRes)
		if err != nil {
			return fmt.Errorf("failed to unmarshal body [%s] into APIRes, err: %s", string(body), err)
		}

		if apiRes.Status == "error" {
			return fmt.Errorf("request to api [%s] failed, returned reason [%d], body [%s]", req.URL, apiRes.Reason, string(body))
		}

		if responseObject != nil {
			err = json.Unmarshal(body, responseObject)
			if err != nil {
				return fmt.Errorf("failed to unmarshal data [%s] into responseObject, err: %s", string(body), err)
			}
		}
	} else {
		if res.StatusCode != 200 {
			return fmt.Errorf("Blank body returned error http status code [%d] please check...", res.StatusCode)
		}
	}

	return nil
}
