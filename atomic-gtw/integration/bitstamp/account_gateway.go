package bitstamp

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	api     *API
	options gateway.Options
	tickCh  chan gateway.Tick
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		api:     api,
		options: options,
		tickCh:  tickCh,
	}
}

func (g *AccountGateway) Connect() error {
	return nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID)

}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	accountBalance, err := g.api.AccountBalance()
	if err != nil {
		return balances, err
	}

	balances = make([]gateway.Balance, 0, len(accountBalance))
	for _, balance := range accountBalance {
		balances = append(balances, gateway.Balance{
			Asset:     strings.ToUpper(balance.Currency),
			Available: balance.Available,
			Total:     balance.Total,
		})
	}

	return
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	openOrders, err := g.api.OpenOrders(market.Symbol)
	if err != nil {
		return orders, err
	}

	orders = make([]gateway.Order, 0, len(openOrders))
	for _, order := range openOrders {
		orders = append(orders, mapAPIOrderToCommon(order, market))
	}

	return
}

func mapAPIOrderToCommon(o ApiOrder, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market: market,
		ID:     o.Id,
		Side:   mapAPIOrderTypeToCommonSide(o.Type),
		Amount: o.Amount,
		Price:  o.Price,
	}
}

func mapAPIOrderTypeToCommonSide(orderType string) gateway.Side {
	if orderType == "0" {
		return gateway.Bid
	} else if orderType == "1" {
		return gateway.Ask
	} else {
		log.Printf("Bitstamp invalid order side \"%s\"", orderType)
		return ""
	}
}

type APIOrderReq struct {
	Amount string `json:"amount"`
	Price  string `json:"price"`
}

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	orderReq := APIOrderReq{
		Amount: utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		Price:  utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
	}
	side := ""
	if order.Side == gateway.Bid {
		side = "buy"
	} else if order.Side == gateway.Ask {
		side = "sell"
	}

	orderID, err := g.api.PlaceOrder(orderReq, side, order.Market.Symbol)
	if err != nil {
		return orderId, err
	}

	return orderID, err
}

func (g *AccountGateway) SubscribeMarkets(markets []gateway.Market) error {
	log.Printf("%s subscribing to %d markets...", Exchange.Name, len(markets))

	token, _, err := g.api.GetToken()
	if err != nil {
		return err
	}

	ws := NewWsSession()
	err = ws.Connect()
	if err != nil {
		return err
	}

	ch := make(chan WsResponse)
	ws.SubscribeMessages(ch, nil)
	go g.subscriptionMessageHandler(ch)

	for _, market := range markets {
		userTradeReq := WsRequest{
			Event: "bts:subscribe",
			Data: WsChannel{
				Channel: fmt.Sprintf("private-my_trades_%s", market.Symbol),
				Auth:    token,
			},
		}

		err = ws.SendRequest(userTradeReq)
		if err != nil {
			return err
		}
	}

	return nil
}

func (g *AccountGateway) subscriptionMessageHandler(ch chan WsResponse) {
	for msg := range ch {
		channel, symbol := parseChannel(msg.Channel)
		if channel == "" || symbol == "" {
			log.Printf("%s account gateway failed to parse channel [%s] extracted channel [%s] and symbol [%s]", Exchange, msg.Channel, channel, symbol)
			continue
		}

		switch msg.Event {
		case "private-my_trades":
			wsTrade := WsUserTrade{}
			err := json.Unmarshal(msg.Data, &wsTrade)
			if err != nil {
				wsError := utils.NewWsError(
					Exchange.Name,
					utils.WsUnmarshalError,
					err.Error(),
					string(msg.Data),
				)

				log.Println(wsError.AsError())
			} else {
				g.processUserFill(wsTrade, symbol)
			}
		default:
			log.Printf("%s messageHandler received unknown channel [%s]", Exchange, msg.Event)
		}
	}
}

func (g *AccountGateway) processUserFill(wsUserTade WsUserTrade, symbol string) error {
	var side gateway.Side
	if wsUserTade.Side == "buy" {
		side = gateway.Bid
	} else {
		side = gateway.Ask
	}

	event := gateway.Event{
		Type: gateway.FillEvent,
		Data: gateway.Fill{
			ID:        strconv.FormatInt(wsUserTade.Id, 10),
			OrderID:   strconv.FormatInt(wsUserTade.OrderID, 10),
			Timestamp: gateway.ParseTimestamp(wsUserTade.MicroTimestamp),
			Symbol:    symbol,
			Side:      side,
			Amount:    wsUserTade.Amount,
			Price:     wsUserTade.Price,
			Fee:       wsUserTade.Fee,
		},
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          []gateway.Event{event},
	}
	return nil
}
