package bitstamp

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	tickCh chan gateway.Tick
}

func NewMarketDataGateway(tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		tickCh: tickCh,
	}
}

func (mg *MarketDataGateway) Connect() error {
	return nil
}

func (mg *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	log.Printf("%s subscribing to %d markets...", Exchange.Name, len(markets))

	ws := NewWsSession()
	err := ws.Connect()
	if err != nil {
		return err
	}

	ch := make(chan WsResponse)
	ws.SubscribeMessages(ch, nil)
	go mg.subscriptionMessageHandler(ch)

	for _, market := range markets {
		bookReq := WsRequest{
			Event: "bts:subscribe",
			Data: WsChannel{
				Channel: fmt.Sprintf("order_book_%s", market.Symbol),
			},
		}
		err := ws.SendRequest(bookReq)
		if err != nil {
			return err
		}

		tradesReq := WsRequest{
			Event: "bts:subscribe",
			Data: WsChannel{
				Channel: fmt.Sprintf("live_trades_%s", market.Symbol),
			},
		}
		err = ws.SendRequest(tradesReq)
		if err != nil {
			return err
		}
	}

	return nil
}

var channelMatch = regexp.MustCompile(`(.+)_([a-z]+)$`)

func parseChannel(str string) (channel, symbol string) {
	matches := channelMatch.FindStringSubmatch(str)
	if len(matches) != 3 {
		return "", ""
	}

	return matches[1], matches[2]
}

func (mg *MarketDataGateway) subscriptionMessageHandler(ch chan WsResponse) {
	for msg := range ch {
		channel, symbol := parseChannel(msg.Channel)
		if channel == "" || symbol == "" {
			log.Printf("%s market data gateway failed to parse channel [%s] extracted channel [%s] and symbol [%s]", Exchange, msg.Channel, channel, symbol)
			continue
		}

		switch channel {
		case "live_trades":
			wsTrade := WsTrade{}
			err := json.Unmarshal(msg.Data, &wsTrade)
			if err != nil {
				wsError := utils.NewWsError(
					Exchange.Name,
					utils.WsUnmarshalError,
					err.Error(),
					string(msg.Data),
				)

				log.Println(wsError.AsError())
			} else {
				mg.processTrade(wsTrade, symbol)
			}
		case "order_book":
			wsBook := WsBook{}
			err := json.Unmarshal(msg.Data, &wsBook)
			if err != nil {
				wsError := utils.NewWsError(
					Exchange.Name,
					utils.WsUnmarshalError,
					err.Error(),
					string(msg.Data),
				)

				log.Println(wsError.AsError())
			} else {
				mg.processBook(wsBook, symbol)
			}
		}
	}
}

func (g *MarketDataGateway) processTrade(wsTrade WsTrade, symbol string) {
	var side gateway.Side
	if wsTrade.Type == 0 {
		side = gateway.Bid
	} else {
		side = gateway.Ask
	}

	tradeID := strconv.FormatInt(wsTrade.Id, 10)

	eventLog := make([]gateway.Event, 0, 1)
	eventLog = append(eventLog, gateway.Event{
		Type: gateway.TradeEvent,
		Data: gateway.Trade{
			Timestamp: gateway.ParseTimestamp(wsTrade.MicroTimestamp),
			Symbol:    symbol,
			ID:        tradeID,
			Direction: side,
			Amount:    wsTrade.Amount,
			Price:     wsTrade.Price,
		},
	})

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}
}

func (g *MarketDataGateway) processBook(book WsBook, symbol string) error {
	eventLog := make([]gateway.Event, 0, len(book.Bids)+len(book.Asks)+1)
	appendEvents := func(symbol string, side gateway.Side, dataArray [][2]string) error {
		for _, data := range dataArray {
			price, err := strconv.ParseFloat(data[0], 64)

			if err != nil {
				return fmt.Errorf("unmarshal price \"%s\" err: %s", data[0], err)
			}
			amount, err := strconv.ParseFloat(data[1], 64)
			if err != nil {
				return fmt.Errorf("unmarshal amount \"%s\" err: %s", data[1], err)
			}

			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  price,
					Amount: amount,
				},
			}

			eventLog = append(eventLog, event)
		}

		return nil
	}

	eventLog = append(eventLog, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: symbol,
		},
	})

	if err := appendEvents(symbol, gateway.Ask, book.Asks); err != nil {
		return fmt.Errorf("failed append ask events, err: %s", err)
	}
	if err := appendEvents(symbol, gateway.Bid, book.Bids); err != nil {
		return fmt.Errorf("failed append bid events, err: %s", err)
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	return nil
}
