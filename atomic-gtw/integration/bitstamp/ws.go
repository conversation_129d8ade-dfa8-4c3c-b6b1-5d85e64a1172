package bitstamp

import (
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

const wsUrl = "wss://ws.bitstamp.net"

type WsSession struct {
	conn             *websocket.Conn
	connWriterMutex  *sync.Mutex
	quit             chan bool
	subscribers      map[chan WsResponse]bool
	subscribersMutex *sync.Mutex
}

type WsRequest struct {
	Event string    `json:"event"`
	Data  WsChannel `json:"data"`
}

type WsChannel struct {
	Channel string `json:"channel"`
	Auth    string `json:"auth,omitempty"`
}

type WsResponse struct {
	Channel string          `json:"channel"`
	Event   string          `json:"event"`
	Data    json.RawMessage `json:"data"`
}

type WsOrder struct {
	Id       int64   `json:"id"`
	Type     int     `json:"order_type"`
	Price    float64 `json:"price"`
	Amount   float64 `json:"amount"`
	Datetime string  `json:"datetime"`
}

type WsUserTrade struct {
	Id             int64   `json:"id"`
	OrderID        int64   `json:"order_id"`
	ClientOrderID  string  `json:"client_order_id"`
	Amount         float64 `json:"amount,string"`
	Price          float64 `json:"price,string"`
	Fee            float64 `json:"fee,string"`
	Side           string  `json:"side"`
	MicroTimestamp int64   `json:"microtimestamp,string"`
}

type WsTrade struct {
	Id             int64   `json:"id"`
	Type           int     `json:"type"`
	Price          float64 `json:"price"`
	Amount         float64 `json:"amount"`
	Timestamp      int64   `json:"timestamp,string"`
	MicroTimestamp int64   `json:"microtimestamp,string"`
	BuyOrderId     int64   `json:"buy_order_id"`
	SellOrderId    int64   `json:"sell_order_id"`
}

type WsBook struct {
	Timestamp string      `json:"timestamp"`
	Bids      [][2]string `json:"bids"`
	Asks      [][2]string `json:"asks"`
}

func NewWsSession() *WsSession {
	return &WsSession{
		quit:             make(chan bool),
		subscribers:      make(map[chan WsResponse]bool),
		subscribersMutex: &sync.Mutex{},
		connWriterMutex:  &sync.Mutex{},
	}
}

func (s *WsSession) Connect() error {
	dialer := websocket.Dialer{}

	ws, _, err := dialer.Dial(wsUrl, nil)
	if err != nil {
		return err
	}

	s.conn = ws

	go s.messageHandler()

	return nil
}

func (s *WsSession) messageHandler() {
	for {
		_, data, err := s.conn.ReadMessage()
		if err != nil {
			// Delay panicking, so we can finish sending message to subscribers
			go func() {
				time.Sleep(100 * time.Millisecond)
				panic(fmt.Errorf("%s failed to read from ws, err: %s", Exchange.Name, err))
			}()
			return
		}

		resp := WsResponse{}
		json.Unmarshal(data, &resp)
		if err != nil {
			log.Printf("%s ws unmarhsal error: %s", Exchange.Name, err)
			continue
		}

		s.subscribersMutex.Lock()
		for ch := range s.subscribers {
			ch <- resp
		}
		s.subscribersMutex.Unlock()
	}
}

func (session *WsSession) SendRequest(request WsRequest) error {
	data, err := json.Marshal(request)
	if err != nil {
		return err
	}

	return session.WriteMessage(data)
}

func (session *WsSession) WriteMessage(data []byte) error {
	session.connWriterMutex.Lock()
	err := session.conn.WriteMessage(websocket.TextMessage, data)
	session.connWriterMutex.Unlock()

	if err != nil {
		return err
	}

	return nil
}

func (session *WsSession) SubscribeMessages(ch chan WsResponse, quit chan bool) {
	session.addSubscriber(ch)

	if quit != nil {
		go func() {
			<-quit
			session.removeSubscriber(ch)
		}()
	}
}

func (session *WsSession) addSubscriber(ch chan WsResponse) {
	session.subscribersMutex.Lock()
	defer session.subscribersMutex.Unlock()

	session.subscribers[ch] = true
}

func (session *WsSession) removeSubscriber(ch chan WsResponse) {
	session.subscribersMutex.Lock()
	defer session.subscribersMutex.Unlock()

	delete(session.subscribers, ch)
}
