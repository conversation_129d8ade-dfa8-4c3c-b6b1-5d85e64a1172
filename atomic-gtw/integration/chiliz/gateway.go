package chiliz

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Chiliz",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

// https://chiliz.zendesk.com/hc/en-us/articles/************-Chiliz-net-Fee-Structure
const chzTakerFee = 0
const chzMakerFee = 0.006
const nonChzTakerFee = 0.002
const nonChzMakerFee = 0.002

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (gtw *Gateway) Connect() error {
	ws := NewWsSession(gtw.options)
	if err := ws.Connect(); err != nil {
		return err
	}

	gtw.accountGateway = NewAccountGateway(gtw.api, gtw.tickCh)
	gtw.marketDataGateway = NewMarketDataGateway(gtw.options, gtw.tickCh)

	if err := gtw.marketDataGateway.Connect(ws); err != nil {
		return err
	}

	if err := gtw.accountGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (gtw *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return gtw.marketDataGateway.SubscribeMarkets(markets)
}

func (gtw *Gateway) Close() error {
	return nil
}

func (gtw *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.Symbols()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(symbols))
	for _, symbol := range symbols {
		if symbol.Status == "TRADING" {
			commonMarkets = append(commonMarkets, symbolToCommonMarket(symbol))
		}
	}

	return commonMarkets, nil
}

func (gtw *Gateway) AccountGateway() gateway.AccountGateway {
	return gtw.accountGateway
}

func (gtw *Gateway) Tick() chan gateway.Tick {
	return gtw.tickCh
}

func symbolToCommonMarket(symbol SymbolResponse) gateway.Market {
	sym := symbol.BaseAsset + symbol.QuoteAsset
	priceTick := 1 / math.Pow10(int(symbol.BaseAssetPrecision))
	amountTick := 1 / math.Pow10(int(symbol.QuotePrecision))

	priceFilter, err := symbol.SelectFilter("PRICE_FILTER")
	if err != nil {
		panic(err)
	}

	lotFilter, err := symbol.SelectFilter("LOT_SIZE")
	if err != nil {
		panic(err)
	}

	pair := gateway.Pair{
		Base:  strings.ToUpper(symbol.BaseAsset),
		Quote: strings.ToUpper(symbol.QuoteAsset),
	}

	return gateway.Market{
		Exchange:               Exchange,
		Pair:                   pair,
		Symbol:                 sym,
		TakerFee:               selectTakerFee(pair),
		MakerFee:               selectMakerFee(pair),
		PriceTick:              priceTick,
		AmountTick:             amountTick,
		MinimumOrderSize:       lotFilter.MinQty,
		MinimumOrderMoneyValue: priceFilter.MinPrice,
		PaysFeeInStock:         true, // TODO: Check, I'm not sure about the value here
		MarketType:             gateway.SpotMarket,
	}
}

func selectTakerFee(pair gateway.Pair) float64 {
	if isChzSymbol(pair) {
		return chzTakerFee
	}

	return nonChzTakerFee
}

func selectMakerFee(pair gateway.Pair) float64 {
	if isChzSymbol(pair) {
		return chzMakerFee
	}

	return nonChzMakerFee
}

func isChzSymbol(pair gateway.Pair) bool {
	return pair.Base == "CHZ" || pair.Quote == "CHZ"
}

func parsePriceLevelsToDepth(levels [][]string) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel

	for _, level := range levels {
		if len(level) < 2 {
			return nil, fmt.Errorf("invalid price level: %v", level)
		}
		price, err := strconv.ParseFloat(level[0], 64)
		if err != nil {
			return nil, err
		}
		amount, err := strconv.ParseFloat(level[1], 64)
		if err != nil {
			return nil, err
		}
		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		})
	}

	return priceLevels, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := gtw.api.DepthBook(market.Symbol, params)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	asks, err := parsePriceLevelsToDepth(depth.Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	bids, err := parsePriceLevelsToDepth(depth.Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	depthBook := gateway.DepthBook{
		Asks: asks,
		Bids: bids,
	}

	return depthBook, nil
}
