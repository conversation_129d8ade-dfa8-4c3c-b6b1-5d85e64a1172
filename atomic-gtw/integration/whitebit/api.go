package whitebit

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha512"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/buger/jsonparser"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBase           = "https://whitebit.com/api"
	apiMarkets        = apiBase + "/v4/public/markets"
	apiAccountBalance = apiBase + "/v4/trade-account/balance"
	apiOpenOrders     = apiBase + "/v4/orders"
	apiOrderDeals     = apiBase + "/v4/trade-account/order"
	apiCreateOrder    = apiBase + "/v4/order/new"
	apiOrderDelete    = apiBase + "/v4/order/cancel"
	apiDepthBook      = apiBase + "/v4/public/orderbook/%s?limit=%d"
)

type API struct {
	options       gateway.Options
	client        *utils.HttpClient
	lastNonceUsed int64
}

func (api *API) getNonce() int64 {
	nonce := time.Now().UnixMilli()
	if nonce <= api.lastNonceUsed {
		nonce = api.lastNonceUsed + 1
	}

	api.lastNonceUsed = nonce
	return api.lastNonceUsed
}

func NewAPI(options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options: options,
		client:  client,
	}
}

type APIResponse struct {
	Success *bool            `json:"success,omitempty"`
	Message string           `json:"message"`
	Result  *json.RawMessage `json:"result,omitempty"`
}

type APIMarket struct {
	Name      string  `json:"name"`
	Money     string  `json:"money"`
	Stock     string  `json:"stock"`
	StockPrec int     `json:"stockPrec,string"`
	MoneyPrec int     `json:"moneyPrec,string"`
	MinAmount float64 `json:"minAmount,string"`
	MinTotal  float64 `json:"minTotal,string"`
}

func (a *API) Markets() ([]APIMarket, error) {
	var res []APIMarket
	req, err := a.newPublicAPIRequest(http.MethodGet, apiMarkets)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APIBalance struct {
	Available float64 `json:"available,string"`
	Freeze    float64 `json:"freeze,string"`
}

func (a *API) AccountBalances() (map[string]APIBalance, error) {
	var res map[string]APIBalance
	req, err := a.newAPIRequest(http.MethodPost, apiAccountBalance, nil, nil)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APIOrder struct {
	Amount    float64     `json:"amount,string"`
	DealFee   float64     `json:"dealFee,string"`
	DealMoney float64     `json:"dealMoney,string"`
	DealStock float64     `json:"dealStock,string"`
	Left      float64     `json:"left,string"`
	MakerFee  float64     `json:"makerFee,string"`
	Market    string      `json:"market"`
	Price     float64     `json:"price,string"`
	Side      string      `json:"side"`
	TakerFee  float64     `json:"takerFee,string"`
	Timestamp float64     `json:"timestamp"`
	Type      string      `json:"type"`
	ID        json.Number `json:"id,Number"`
	OrderID   json.Number `json:"orderId,Number"`
}

func (a *API) OpenOrders(market string) ([]APIOrder, error) {
	params := make(map[string]interface{})
	params["market"] = market
	params["limit"] = "100"

	var res []APIOrder
	req, err := a.newAPIRequest(http.MethodPost, apiOpenOrders, nil, params)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APIDeal struct {
	Amount      float64     `json:"amount,string"`
	Deal        float64     `json:"deal,string"`
	Fee         float64     `json:"fee,string"`
	ID          json.Number `json:"id,Number"`
	DealOrderID json.Number `json:"dealOrderId,Number"`
	Price       float64     `json:"price,string"`
	Role        int         `json:"role"`
	Time        float64     `json:"time"`
}

type APIOrderDeals struct {
	Records []APIDeal `json:"records"`
}

func (a *API) OrderDeals(orderId string) ([]APIDeal, error) {
	params := make(map[string]interface{})
	params["orderId"] = orderId

	var res APIOrderDeals
	req, err := a.newAPIRequest(http.MethodPost, apiOrderDeals, nil, params)
	if err != nil {
		return res.Records, err
	}

	err = a.makeHttpRequest(req, &res)
	return res.Records, err
}

func (a *API) CreateOrder(side, market, price, amount string, postOnly bool) (APIOrder, error) {
	params := make(map[string]interface{})
	params["market"] = market
	params["side"] = side
	params["price"] = price
	params["amount"] = amount

	if postOnly {
		params["postOnly"] = true
	}

	var res APIOrder
	req, err := a.newAPIRequest(http.MethodPost, apiCreateOrder, nil, params)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) CancelOrder(market, orderID string) (APIOrder, error) {
	params := make(map[string]interface{})
	params["market"] = market
	params["orderId"] = orderID

	var res APIOrder
	req, err := a.newAPIRequest(http.MethodPost, apiOrderDelete, nil, params)
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

type APIDepthBook struct {
	Asks [][]string `json:"asks"`
	Bids [][]string `json:"bids"`
}

func (a *API) DepthBook(market string, params gateway.GetDepthParams) (APIDepthBook, error) {
	if params.Limit == 0 {
		params.Limit = 100
	}
	var res APIDepthBook
	req, err := a.newPublicAPIRequest(http.MethodGet, fmt.Sprintf(apiDepthBook, market, params.Limit))
	if err != nil {
		return res, err
	}

	err = a.makeHttpRequest(req, &res)
	return res, err
}

func (a *API) newPublicAPIRequest(method, uri string) (*http.Request, error) {
	req, err := http.NewRequest(method, uri, nil)
	return req, err
}

func (a *API) newAPIRequest(method, url string, params *url.Values, bodyParams map[string]interface{}) (*http.Request, error) {
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json;charset=UTF-8")

	if a.options.ApiKey != "" {
		nonce := a.getNonce()

		if bodyParams == nil {
			bodyParams = make(map[string]interface{})
		}

		bodyParams["request"] = req.URL.Path
		bodyParams["nonce"] = strconv.FormatInt(nonce, 10)
		bodyParams["nonceWindow"] = true // Allow for a 5 seconds window between nonces

		data, err := json.Marshal(bodyParams)
		if err != nil {
			return nil, fmt.Errorf("failed marshal body params, err: %s", err)
		}

		req.Body = ioutil.NopCloser(bytes.NewReader(data))
		req.ContentLength = int64(len(data))

		payload := base64.StdEncoding.EncodeToString(data)
		hmac := hmac.New(sha512.New, []byte(a.options.ApiSecret))
		hmac.Write([]byte(payload))
		signature := hex.EncodeToString(hmac.Sum(nil))

		req.Header.Set("X-TXC-APIKEY", a.options.ApiKey)
		req.Header.Set("X-TXC-PAYLOAD", payload)
		req.Header.Set("X-TXC-SIGNATURE", signature)
	}

	return req, nil
}

func (a *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.client.SendRequest(req)
	if err != nil {
		return err
	}

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}

	errCode, dataType, _, _ := jsonparser.Get(body, "code")
	if dataType != jsonparser.NotExist {
		return fmt.Errorf("%s %s responded with error code [%s]\nresponse body: %s", req.Method, req.URL.String(), string(errCode), string(body))
	}

	err = json.Unmarshal(body, responseObject)
	if err != nil {
		return err
	}

	return nil
}
