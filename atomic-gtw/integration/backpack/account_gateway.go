package backpack

import (
	"crypto/ed25519"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	options    gateway.Options
	api        *API
	tickCh     chan gateway.Tick
	ws         *utils.WsClient
	publicKey  string
	privateKey ed25519.PrivateKey
}

func NewAccountGateway(api *API, options gateway.Options, tickCh chan gateway.Tick) *AccountGateway {
	privateKeyBytes, err := base64.StdEncoding.DecodeString(options.ApiSecret)
	var privateKey ed25519.PrivateKey
	if err == nil {
		if len(privateKeyBytes) == ed25519.SeedSize {
			privateKey = ed25519.NewKeyFromSeed(privateKeyBytes)
		} else if len(privateKeyBytes) == ed25519.PrivateKeySize {
			privateKey = privateKeyBytes
		} else {
			privateKey = nil
		}
	} else {
		privateKey = nil
	}

	return &AccountGateway{
		api:        api,
		options:    options,
		tickCh:     tickCh,
		publicKey:  options.ApiKey,
		privateKey: privateKey,
	}
}

func (g *AccountGateway) Connect() error {
	if g.privateKey == nil || g.publicKey == "" {
		return fmt.Errorf("API key or secret is not set or invalid")
	}

	g.ws = utils.NewWsClient()
	g.ws.SetProxies(g.options.Proxies)

	err := g.ws.Connect(wsAPI)
	if err != nil {
		return fmt.Errorf("ws connect err: %s", err)
	}

	timestamp := time.Now().UnixMilli()
	window := int64(5000)

	signingString := fmt.Sprintf("instruction=subscribe&timestamp=%d&window=%d", timestamp, window)
	signature := ed25519.Sign(g.privateKey, []byte(signingString))
	signatureBase64 := base64.StdEncoding.EncodeToString(signature)

	subscribeMsg := map[string]interface{}{
		"method": "SUBSCRIBE",
		"params": []string{"account.orderUpdate"},
		"signature": []string{
			g.publicKey,
			signatureBase64,
			strconv.FormatInt(timestamp, 10),
			strconv.FormatInt(window, 10),
		},
	}

	subscribeBytes, _ := json.Marshal(subscribeMsg)
	err = g.ws.WriteMessage(subscribeBytes)
	if err != nil {
		return fmt.Errorf("failed to subscribe to account updates: %s", err)
	}

	msgCh := make(chan []byte)
	g.ws.SubscribeMessages(msgCh)

	go g.handleWebSocketMessages(msgCh)

	go g.keepConnectionAlive()

	return nil
}

func (g *AccountGateway) keepConnectionAlive() {
	for {
		time.Sleep(30 * time.Second)
		if g.ws != nil {
			pingMsg := []byte(`{"method":"PING"}`)
			if err := g.ws.WriteMessage(pingMsg); err != nil {
				log.Panicf("Backpack account ws ping error: %s", err)
			}
		}
	}
}

func (g *AccountGateway) handleWebSocketMessages(msgCh chan []byte) {
	for data := range msgCh {
		var message websocketMessage
		if err := json.Unmarshal(data, &message); err != nil {
			var response struct {
				ID     int    `json:"id"`
				Method string `json:"method"`
				Result bool   `json:"result"`
			}

			if err = json.Unmarshal(data, &response); err == nil {
				if response.Result {
					log.Printf("Successfully %s to/from WebSocket streams", response.Method)
				} else {
					log.Printf("Failed to %s to/from WebSocket streams", response.Method)
				}
				continue
			}

			var pong struct {
				Method string `json:"method"`
			}

			if err = json.Unmarshal(data, &pong); err == nil && pong.Method == "PONG" {
				continue
			}

			log.Printf("Backpack account data unmarshal err: %s", err)
			continue
		}

		if message.Stream == "account.orderUpdate" {
			g.processOrderUpdate(message.Data)
		}
	}
}

type orderUpdateEvent struct {
	EventType        string  `json:"e"`
	EventTime        int64   `json:"E"`
	Symbol           string  `json:"s"`
	ClientOrderID    int     `json:"c,omitempty"`
	Side             string  `json:"S"`
	OrderType        string  `json:"o"`
	TimeInForce      string  `json:"f"`
	Quantity         float64 `json:"q,string"`
	QuoteQuantity    float64 `json:"Q,string"`
	Price            float64 `json:"p,string"`
	OrderState       string  `json:"X"`
	OrderID          string  `json:"i"`
	ExecutedQuantity float64 `json:"z,string"`
	ExecutedQuoteQty float64 `json:"Z,string"`
	LastFilledPrice  float64 `json:"L,string"`
	Fee              float64 `json:"n,string"`
	FeeSymbol        string  `json:"N,omitempty"`
	IsMaker          bool    `json:"m,omitempty"`
	TradeID          int64   `json:"t,omitempty"`
	EngineTimestamp  int64   `json:"T"`
}

func (g *AccountGateway) processOrderUpdate(data []byte) {
	var update orderUpdateEvent
	err := json.Unmarshal(data, &update)
	if err != nil {
		log.Printf("Failed to unmarshal order update: %s", err)
		return
	}

	var orderState gateway.OrderState
	switch update.OrderState {
	case "New":
		orderState = gateway.OrderOpen
	case "PartiallyFilled":
		orderState = gateway.OrderPartiallyFilled
	case "Filled":
		orderState = gateway.OrderFullyFilled
	case "Cancelled":
		orderState = gateway.OrderCancelled
	case "Expired":
		orderState = gateway.OrderCancelled
	default:
		orderState = gateway.OrderUnknown
	}

	var side gateway.Side
	if update.Side == "Bid" {
		side = gateway.Bid
	} else {
		side = gateway.Ask
	}

	event := gateway.Event{
		Type: gateway.OrderUpdateEvent,
		Data: gateway.Order{
			ID:               update.OrderID,
			ClientOrderID:    strconv.Itoa(update.ClientOrderID),
			State:            orderState,
			Side:             side,
			Price:            update.Price,
			Amount:           update.Quantity,
			FilledAmount:     update.ExecutedQuantity,
			Fee:              update.Fee,
			FeeAsset:         update.FeeSymbol,
			FilledMoneyValue: update.ExecutedQuoteQty,
		},
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          []gateway.Event{event},
	}
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	res, err := g.api.AccountBalance()
	if err != nil {
		return []gateway.Balance{}, err
	}

	balances := make([]gateway.Balance, 0, len(res))
	for asset, balance := range res {
		balances = append(balances, gateway.Balance{
			Asset:     asset,
			Available: balance.Available,
			Total:     balance.Available + balance.Locked + balance.Staked,
		})
	}

	return balances, nil
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	res, err := g.api.OpenOrders(market.Symbol)
	if err != nil {
		return []gateway.Order{}, err
	}

	orders := make([]gateway.Order, 0, len(res))
	for _, order := range res {
		var orderState gateway.OrderState
		switch order.Status {
		case "New":
			orderState = gateway.OrderOpen
		case "PartiallyFilled":
			orderState = gateway.OrderPartiallyFilled
		case "Filled":
			orderState = gateway.OrderFullyFilled
		case "Cancelled":
			orderState = gateway.OrderCancelled
		default:
			orderState = gateway.OrderUnknown
		}

		var side gateway.Side
		if order.Side == "Bid" {
			side = gateway.Bid
		} else {
			side = gateway.Ask
		}

		var orderType gateway.OrderType
		if order.Type == "Market" {
			orderType = gateway.MarketOrder
		} else {
			orderType = gateway.LimitOrder
		}

		orders = append(orders, gateway.Order{
			ID:            order.Id,
			ClientOrderID: strconv.Itoa(order.ClientId),
			Market:        market,
			State:         orderState,
			Type:          orderType,
			Side:          side,
			Price:         order.Price,
			Amount:        order.Quantity,
			FilledAmount:  order.ExecutedQuantity,
		})
	}

	return orders, nil
}

func (g *AccountGateway) SendOrder(order gateway.Order) (string, error) {
	var side string
	if order.Side == gateway.Bid {
		side = "Bid"
	} else {
		side = "Ask"
	}

	var orderType string
	var timeInForce string
	var postOnly bool

	if order.Type == gateway.MarketOrder {
		orderType = "Market"
		timeInForce = "IOC"
	} else {
		orderType = "Limit"

		if order.PostOnly {
			postOnly = true
		}

		timeInForce = "GTC"
	}

	apiOrder := APIPlaceOrder{
		Symbol:              order.Market.Symbol,
		Side:                side,
		OrderType:           orderType,
		TimeInForce:         timeInForce,
		PostOnly:            postOnly,
		SelfTradePrevention: "RejectBoth",
	}

	if order.Type == gateway.LimitOrder {
		apiOrder.Price = utils.FloatToStringWithTick(order.Price, order.Market.PriceTick)
	}

	apiOrder.Quantity = utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick)

	placedOrder, err := g.api.PlaceOrder(apiOrder)
	if err != nil {
		return "", err
	}

	return placedOrder.Id, nil
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return g.api.CancelOrder(order.ID, order.Market.Symbol)
}
