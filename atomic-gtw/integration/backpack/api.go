package backpack

import (
	"bytes"
	"crypto/ed25519"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiSpot = "https://api.backpack.exchange"
	wsAPI   = "wss://ws.backpack.exchange"

	public     = false
	apiSymbols = "/api/v1/markets"
	apiDepth   = "/api/v1/depth"

	private           = true
	apiPlaceOrder     = "/api/v1/order"
	apiCancelOrder    = "/api/v1/order"
	apiOpenOrders     = "/api/v1/orders"
	apiAccountBalance = "/api/v1/capital"
)

type API struct {
	options    gateway.Options
	httpClient *utils.HttpClient
	baseURL    string
	publicKey  string
	privateKey ed25519.PrivateKey
}

func NewAPI(options gateway.Options, baseURL string) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	privateKeyBytes, err := base64.StdEncoding.DecodeString(options.ApiSecret)
	if err != nil {
		log.Printf("Failed to decode API secret as base64: %s", err)
		privateKeyBytes = nil
	}

	var privateKey ed25519.PrivateKey
	if len(privateKeyBytes) == ed25519.SeedSize {
		privateKey = ed25519.NewKeyFromSeed(privateKeyBytes)
	} else if len(privateKeyBytes) == ed25519.PrivateKeySize {
		privateKey = privateKeyBytes
	} else {
		log.Printf("Invalid private key length: %d bytes (expected 32 or 64)", len(privateKeyBytes))
		privateKey = nil
	}

	return &API{
		options:    options,
		httpClient: client,
		baseURL:    baseURL,
		publicKey:  options.ApiKey,
		privateKey: privateKey,
	}
}

type apiError struct {
	Endpoint string
	Exchange string
	Err      error
}

func (e *apiError) Error() string {
	return "[" + e.Exchange + "] " + "failed to request api [" + e.Endpoint + "] error: " + e.Err.Error()
}

func (a *API) newAPIError(endpoint string, err error) *apiError {
	return &apiError{endpoint, Exchange.String(), err}
}

func (a *API) Markets() ([]APISymbol, error) {
	req, err := a.newHTTPRequest(http.MethodGet, map[string]string{
		"marketType": "SPOT",
	}, nil, apiSymbols, public)
	if err != nil {
		return []APISymbol{}, a.newAPIError(apiSymbols, err)
	}

	var symbols []APISymbol
	if err = a.makeHTTPRequest(req, &symbols); err != nil {
		return []APISymbol{}, a.newAPIError(apiSymbols, err)
	}

	return symbols, nil
}

type APISymbol struct {
	Symbol         string `json:"symbol"`
	BaseSymbol     string `json:"baseSymbol"`
	QuoteSymbol    string `json:"quoteSymbol"`
	MarketType     string `json:"marketType"`
	OrderBookState string `json:"orderBookState"`
	Filters        struct {
		Price    PriceFilter    `json:"price"`
		Quantity QuantityFilter `json:"quantity"`
	} `json:"filters"`
}

type PriceFilter struct {
	MinPrice float64 `json:"minPrice,string"`
	MaxPrice float64 `json:"maxPrice,string"`
	TickSize float64 `json:"tickSize,string"`
}

type QuantityFilter struct {
	MinQuantity float64 `json:"minQuantity,string"`
	MaxQuantity float64 `json:"maxQuantity,string"`
	StepSize    float64 `json:"stepSize,string"`
}

type APIDepth struct {
	Asks         []gateway.PriceArray `json:"asks"`
	Bids         []gateway.PriceArray `json:"bids"`
	LastUpdateId string               `json:"lastUpdateId"`
	Timestamp    int64                `json:"timestamp"`
}

func (a *API) Depth(symbol string) (APIDepth, error) {
	params := map[string]string{
		"symbol": symbol,
	}
	req, err := a.newHTTPRequest(http.MethodGet, params, nil, apiDepth, public)
	if err != nil {
		return APIDepth{}, a.newAPIError(apiDepth, err)
	}

	var res APIDepth
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return APIDepth{}, a.newAPIError(apiDepth, err)
	}

	return res, nil
}

type APIBalance struct {
	Available float64 `json:"available,string"`
	Locked    float64 `json:"locked,string"`
	Staked    float64 `json:"staked,string"`
}

func (a *API) AccountBalance() (map[string]APIBalance, error) {
	req, err := a.newHTTPRequest(http.MethodGet, map[string]string{}, nil, apiAccountBalance, private)
	if err != nil {
		return nil, a.newAPIError(apiAccountBalance, err)
	}

	var res map[string]APIBalance
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return nil, a.newAPIError(apiAccountBalance, err)
	}

	return res, nil
}

type APIOrder struct {
	Id                  string  `json:"id"`
	ClientId            int     `json:"clientId"`
	Symbol              string  `json:"symbol"`
	Side                string  `json:"side"`
	Type                string  `json:"orderType"`
	Status              string  `json:"status"`
	Price               float64 `json:"price,string"`
	Quantity            float64 `json:"quantity,string"`
	ExecutedQuantity    float64 `json:"executedQuantity,string"`
	TimeInForce         string  `json:"timeInForce"`
	CreatedAt           int64   `json:"createdAt"`
	SelfTradePrevention string  `json:"selfTradePrevention"`
}

func (a *API) OpenOrders(symbol string) ([]APIOrder, error) {
	params := map[string]string{}
	if symbol != "" {
		params["symbol"] = symbol
	}

	req, err := a.newHTTPRequest(http.MethodGet, params, nil, apiOpenOrders, private)
	if err != nil {
		return []APIOrder{}, a.newAPIError(apiOpenOrders, err)
	}

	var res []APIOrder
	if err = a.makeHTTPRequest(req, &res); err != nil {
		return []APIOrder{}, a.newAPIError(apiOpenOrders, err)
	}

	return res, nil
}

type APIPlaceOrder struct {
	Symbol              string `json:"symbol"`
	Side                string `json:"side"`
	OrderType           string `json:"orderType"`
	TimeInForce         string `json:"timeInForce,omitempty"`
	Price               string `json:"price,omitempty"`
	Quantity            string `json:"quantity,omitempty"`
	QuoteQuantity       string `json:"quoteQuantity,omitempty"`
	ClientId            *int   `json:"clientId,omitempty"`
	SelfTradePrevention string `json:"selfTradePrevention,omitempty"`
	PostOnly            bool   `json:"postOnly,omitempty"`
}

func (a *API) PlaceOrder(order APIPlaceOrder) (APIOrder, error) {
	body, err := json.Marshal(order)
	if err != nil {
		return APIOrder{}, a.newAPIError(apiPlaceOrder, err)
	}

	req, err := a.newHTTPRequest(http.MethodPost, nil, body, apiPlaceOrder, private)
	if err != nil {
		return APIOrder{}, a.newAPIError(apiPlaceOrder, err)
	}

	var res APIOrder
	if err = a.makeHTTPRequest(req, &res); err != nil {
		errStr := err.Error()
		switch {
		case strings.Contains(errStr, "INSUFFICIENT_FUNDS"):
			return APIOrder{}, gateway.InsufficientBalanceErr
		case strings.Contains(errStr, "quantity too small"):
			return APIOrder{}, gateway.MinOrderSizeErr
		case strings.Contains(errStr, "Limit orders must specify a positive"):
			return APIOrder{}, gateway.MinOrderSizeErr
		}

		return APIOrder{}, a.newAPIError(apiPlaceOrder, err)
	}

	return res, nil
}

func (a *API) CancelOrder(orderID string, symbol string) error {
	body, err := json.Marshal(struct {
		OrderID string `json:"orderId"`
		Symbol  string `json:"symbol"`
	}{
		OrderID: orderID,
		Symbol:  symbol,
	})
	if err != nil {
		return a.newAPIError(apiCancelOrder, err)
	}

	req, err := a.newHTTPRequest(http.MethodDelete, nil, body, apiCancelOrder, private)
	if err != nil {
		return a.newAPIError(apiCancelOrder, err)
	}

	if err = a.makeHTTPRequest(req, nil); err != nil {
		if strings.Contains(err.Error(), "No order found") {
			return gateway.OrderNotFoundErr
		}
		return a.newAPIError(apiCancelOrder, err)
	}

	return nil
}

// generateSignature creates a signature for Backpack API requests
func (a *API) generateSignature(instruction string, params map[string]string, body []byte, timestamp int64, window int64) (string, error) {
	var parts []string

	parts = append(parts, "instruction="+instruction)
	if body != nil && len(body) > 0 {
		var bodyMap map[string]interface{}
		if err := json.Unmarshal(body, &bodyMap); err == nil {
			keys := make([]string, 0, len(bodyMap))
			for k := range bodyMap {
				keys = append(keys, k)
			}
			sort.Strings(keys)

			for _, k := range keys {
				v := bodyMap[k]
				var valueStr string
				switch val := v.(type) {
				case string:
					valueStr = val
				case bool:
					valueStr = strconv.FormatBool(val)
				case float64:
					valueStr = strconv.FormatFloat(val, 'f', -1, 64)
				case int:
					valueStr = strconv.Itoa(val)
				default:
					valueStr = fmt.Sprintf("%v", val)
				}
				if valueStr != "" && valueStr != "0" && valueStr != "false" {
					parts = append(parts, k+"="+valueStr)
				}
			}
		}
	}

	if len(params) > 0 {
		keys := make([]string, 0, len(params))
		for k := range params {
			keys = append(keys, k)
		}
		sort.Strings(keys)

		for _, k := range keys {
			if params[k] != "" {
				parts = append(parts, k+"="+params[k])
			}
		}
	}

	parts = append(parts, fmt.Sprintf("timestamp=%d", timestamp))
	parts = append(parts, fmt.Sprintf("window=%d", window))
	signingString := strings.Join(parts, "&")
	signature := ed25519.Sign(a.privateKey, []byte(signingString))
	return base64.StdEncoding.EncodeToString(signature), nil
}

// getInstructionForEndpoint returns the appropriate instruction for the given endpoint and method
func (a *API) getInstructionForEndpoint(endpoint, method string) string {
	switch {
	case endpoint == apiAccountBalance:
		return "balanceQuery"
	case endpoint == apiOpenOrders:
		return "orderQueryAll"
	case endpoint == apiPlaceOrder && method == http.MethodPost:
		return "orderExecute"
	case endpoint == apiCancelOrder && method == http.MethodDelete:
		return "orderCancel"
	default:
		return "accountQuery"
	}
}

func (a *API) newHTTPRequest(method string, params map[string]string, body []byte, endpoint string, isPrivate bool) (*http.Request, error) {
	parsedURL, err := a.httpClient.ParseURLRequest(a.baseURL, endpoint)
	if err != nil {
		return &http.Request{}, err
	}

	var reqBody io.Reader
	if body != nil {
		reqBody = bytes.NewReader(body)
	}

	req, err := http.NewRequest(method, parsedURL.String(), reqBody)
	if err != nil {
		return nil, err
	}

	// Add query parameters for GET requests
	if params != nil && len(params) > 0 {
		q := req.URL.Query()
		for k, v := range params {
			q.Set(k, v)
		}
		req.URL.RawQuery = q.Encode()
	}

	if isPrivate && a.privateKey != nil {
		timestamp := time.Now().UnixMilli()
		window := int64(5000)
		instruction := a.getInstructionForEndpoint(endpoint, method)

		signature, err := a.generateSignature(instruction, params, body, timestamp, window)
		if err != nil {
			return nil, fmt.Errorf("failed to generate signature: %w", err)
		}

		req.Header.Set("Content-Type", "application/json;charset=UTF-8")
		req.Header.Set("X-API-Key", a.publicKey)
		req.Header.Set("X-Timestamp", strconv.FormatInt(timestamp, 10))
		req.Header.Set("X-Window", strconv.FormatInt(window, 10))
		req.Header.Set("X-Signature", signature)
	}

	return req, nil
}

func (a *API) makeHTTPRequest(req *http.Request, responseObject interface{}) error {
	res, err := a.httpClient.SendRequest(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return err
	}

	if res.StatusCode >= 400 {
		var apiErr struct {
			Code    string `json:"code"`
			Message string `json:"message"`
		}
		if err = json.Unmarshal(body, &apiErr); err != nil {
			return fmt.Errorf("status code %d, body: %s", res.StatusCode, string(body))
		}
		return fmt.Errorf("status code %d, error: %s - %s", res.StatusCode, apiErr.Code, apiErr.Message)
	}

	if responseObject != nil {
		if err = json.Unmarshal(body, responseObject); err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %w", string(body), err)
		}
	}

	return nil
}
