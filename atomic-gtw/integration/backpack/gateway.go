package backpack

import (
	"fmt"
	"strconv"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

var Exchange = gateway.Exchange{
	Name: "Backpack",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	api               *API
	tickCh            chan gateway.Tick
}

func NewGateway(options gateway.Options) gateway.Gateway {
	var apiBaseURL string
	apiBaseURL = apiSpot // Use the default API URL

	gtw := &Gateway{
		options: options,
		api:     NewAPI(options, apiBaseURL),
		tickCh:  make(chan gateway.Tick, 100_000),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (g *Gateway) Connect() error {
	// Initialize account gateway
	g.accountGateway = NewAccountGateway(g.api, g.options, g.tickCh)
	if g.options.ApiKey != "" && g.options.ApiSecret != "" {
		if err := g.accountGateway.Connect(); err != nil {
			return fmt.Errorf("account gateway connect error: %s", err)
		}
	}

	// Initialize market data gateway
	g.marketDataGateway = NewMarketDataGateway(g.options, g.api, g.tickCh)
	if err := g.marketDataGateway.Connect(); err != nil {
		return err
	}

	return nil
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return g.marketDataGateway.SubscribeMarkets(markets)
}

func (g *Gateway) Close() error {
	return nil
}

func (g *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.Markets()
	if err != nil {
		return nil, err
	}

	markets := make([]gateway.Market, 0, len(symbols))
	for _, symbol := range symbols {
		market := gateway.Market{
			Exchange: Exchange,
			Symbol:   symbol.Symbol,
			Pair: gateway.Pair{
				Base:  symbol.BaseSymbol,
				Quote: symbol.QuoteSymbol,
			},
			PriceTick:        symbol.Filters.Price.TickSize,
			AmountTick:       symbol.Filters.Quantity.StepSize,
			MinimumOrderSize: symbol.Filters.Quantity.MinQuantity,
			Closed:           symbol.OrderBookState != "Open",
			MarketType:       gateway.SpotMarket,
		}

		markets = append(markets, market)
	}

	return markets, nil
}

func (g *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	depth, err := g.api.Depth(market.Symbol)
	if err != nil {
		return gateway.DepthBook{}, err
	}

	// Convert asks
	asks := make([]gateway.PriceLevel, 0, len(depth.Asks))
	for _, ask := range depth.Asks {
		asks = append(asks, gateway.PriceLevel{
			Price:  ask.Price,
			Amount: ask.Amount,
		})
	}

	// Convert bids
	bids := make([]gateway.PriceLevel, 0, len(depth.Bids))
	for _, bid := range depth.Bids {
		bids = append(bids, gateway.PriceLevel{
			Price:  bid.Price,
			Amount: bid.Amount,
		})
	}

	sequence, _ := strconv.ParseInt(depth.LastUpdateId, 10, 64)
	depthBook := gateway.DepthBook{
		Sequence: sequence,
		Asks:     asks,
		Bids:     bids,
	}

	return depthBook, nil
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return g.accountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return g.tickCh
}
