package backpack

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MarketDataGateway struct {
	options     gateway.Options
	api         *API
	tickCh      chan gateway.Tick
	depthQueues map[string]*depthQueue
}

type depthQueue struct {
	ReceivedSnapshot bool
	Queue            []depthEvent
	Lock             *sync.Mutex
}

func NewMarketDataGateway(options gateway.Options, api *API, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options:     options,
		api:         api,
		tickCh:      tickCh,
		depthQueues: make(map[string]*depthQueue),
	}
}

func (g *MarketDataGateway) Connect() error {
	return nil
}

type websocketMessage struct {
	Stream string          `json:"stream"`
	Data   json.RawMessage `json:"data"`
}

type depthEvent struct {
	EventType       string               `json:"e"`
	EventTime       int64                `json:"E"`
	Symbol          string               `json:"s"`
	FirstUpdateID   int64                `json:"U"`
	LastUpdateID    int64                `json:"u"`
	BidUpdates      []gateway.PriceArray `json:"b"`
	AskUpdates      []gateway.PriceArray `json:"a"`
	EngineTimestamp int64                `json:"T"`
}

type tradeEvent struct {
	EventType     string  `json:"e"`
	EventTime     int64   `json:"E"`
	Symbol        string  `json:"s"`
	Price         float64 `json:"p,string"`
	Quantity      float64 `json:"q,string"`
	BuyerOrderID  string  `json:"b"`
	SellerOrderID string  `json:"a"`
	TradeID       int64   `json:"t"`
	Timestamp     int64   `json:"T"`
	IsBuyerMaker  bool    `json:"m"`
}

func (g *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	ws := utils.NewWsClient()
	ws.SetProxies(g.options.Proxies)

	if err := ws.Connect(wsAPI); err != nil {
		return fmt.Errorf("failed to connect to Backpack websocket: %s", err)
	}

	msgCh := make(chan []byte)
	ws.SubscribeMessages(msgCh)

	go g.handleWebSocketMessages(msgCh)

	go g.keepConnectionAlive(ws)

	if len(markets) == 0 {
		return fmt.Errorf("no markets to subscribe to")
	}

	depthStreams := make([]string, 0, len(markets))
	tradeStreams := make([]string, 0, len(markets))

	for _, market := range markets {
		g.depthQueues[market.Symbol] = &depthQueue{
			Queue: make([]depthEvent, 0),
			Lock:  &sync.Mutex{},
		}
		depthStreams = append(depthStreams, fmt.Sprintf("depth.%s", market.Symbol))
		tradeStreams = append(tradeStreams, fmt.Sprintf("trade.%s", market.Symbol))
	}

	if len(depthStreams) > 0 {
		depthSubscribe := struct {
			Method string   `json:"method"`
			Params []string `json:"params"`
			ID     int      `json:"id"`
		}{
			Method: "SUBSCRIBE",
			Params: depthStreams,
			ID:     1,
		}

		depthMsg, err := json.Marshal(depthSubscribe)
		if err != nil {
			return fmt.Errorf("failed to marshal depth subscribe message: %s", err)
		}

		if err = ws.WriteMessage(depthMsg); err != nil {
			return fmt.Errorf("failed to subscribe to depth streams: %s", err)
		}
	}

	if len(tradeStreams) > 0 {
		tradeSubscribe := struct {
			Method string   `json:"method"`
			Params []string `json:"params"`
			ID     int      `json:"id"`
		}{
			Method: "SUBSCRIBE",
			Params: tradeStreams,
			ID:     2,
		}

		tradeMsg, err := json.Marshal(tradeSubscribe)
		if err != nil {
			return fmt.Errorf("failed to marshal trade subscribe message: %s", err)
		}

		if err = ws.WriteMessage(tradeMsg); err != nil {
			return fmt.Errorf("failed to subscribe to trade streams: %s", err)
		}
	}

	for _, market := range markets {
		go func(symbol string) {
			snapshot, err := g.api.Depth(symbol)
			if err != nil {
				log.Printf("Failed to fetch order book snapshot for %s: %s", symbol, err)
				return
			}

			g.processInitialSnapshot(symbol, snapshot)
		}(market.Symbol)
	}

	return nil
}

func (g *MarketDataGateway) processInitialSnapshot(symbol string, snapshot APIDepth) {
	queue, ok := g.depthQueues[symbol]
	if !ok {
		log.Printf("No depth queue found for symbol %s when processing snapshot", symbol)
		return
	}

	queue.Lock.Lock()
	defer queue.Lock.Unlock()

	eventLog := make([]gateway.Event, 0)
	eventLog = append(eventLog, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: symbol,
		},
	})

	for _, ask := range snapshot.Asks {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Ask,
				Price:  ask.Price,
				Amount: ask.Amount,
			},
		})
	}

	for _, bid := range snapshot.Bids {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: symbol,
				Side:   gateway.Bid,
				Price:  bid.Price,
				Amount: bid.Amount,
			},
		})
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          eventLog,
	}

	lastUpdateId, _ := strconv.ParseInt(snapshot.LastUpdateId, 10, 64)
	for _, event := range queue.Queue {
		if event.LastUpdateID > lastUpdateId {
			g.processDepthUpdate(event)
		}
	}

	queue.ReceivedSnapshot = true
	queue.Queue = nil
}

func (g *MarketDataGateway) keepConnectionAlive(ws *utils.WsClient) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if ws != nil {
				pingMsg := websocket.PingMessage
				if err := ws.WriteRawMessage(pingMsg, []byte{}); err != nil {
					log.Panicf("Backpack ws ping error: %s", err)
				}
			}
		}
	}
}

func (g *MarketDataGateway) handleWebSocketMessages(msgCh chan []byte) {
	for data := range msgCh {
		var errorResponse struct {
			ID    *int `json:"id"`
			Error struct {
				Code    int    `json:"code"`
				Message string `json:"message"`
			} `json:"error"`
		}

		if err := json.Unmarshal(data, &errorResponse); err == nil && errorResponse.Error.Code != 0 {
			log.Panicf("Backpack WebSocket error: code=%d message=%s", errorResponse.Error.Code, errorResponse.Error.Message)
		}

		var message websocketMessage
		if err := json.Unmarshal(data, &message); err != nil {
			var response struct {
				ID     int    `json:"id"`
				Method string `json:"method"`
				Result bool   `json:"result"`
			}

			if err = json.Unmarshal(data, &response); err == nil {
				log.Panicf("Failed to %s to/from WebSocket streams", response.Method)

			}
			log.Panicf("Backpack market data unmarshal err: %s", err)
		}

		if strings.HasPrefix(message.Stream, "depth.") {
			var depth depthEvent
			err := json.Unmarshal(message.Data, &depth)
			if err != nil {
				log.Panicf("Failed to unmarshal depth event: %s", err)
			}

			queue, ok := g.depthQueues[depth.Symbol]
			if !ok {
				log.Printf("No depth queue found for symbol %s", depth.Symbol)
				continue
			}

			queue.Lock.Lock()
			if queue.ReceivedSnapshot {
				queue.Lock.Unlock()
				g.processDepthUpdate(depth)
			} else {
				queue.Queue = append(queue.Queue, depth)
				queue.Lock.Unlock()
			}
		} else if strings.HasPrefix(message.Stream, "trade.") {
			g.processTradeEvent(message.Data)
		}
	}
}

func (g *MarketDataGateway) processDepthUpdate(depth depthEvent) {
	eventLog := make([]gateway.Event, 0)

	for _, ask := range depth.AskUpdates {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: depth.Symbol,
				Side:   gateway.Ask,
				Price:  ask.Price,
				Amount: ask.Amount,
			},
		})
	}

	for _, bid := range depth.BidUpdates {
		eventLog = append(eventLog, gateway.Event{
			Type: gateway.DepthEvent,
			Data: gateway.Depth{
				Symbol: depth.Symbol,
				Side:   gateway.Bid,
				Price:  bid.Price,
				Amount: bid.Amount,
			},
		})
	}

	if len(eventLog) > 0 {
		g.tickCh <- gateway.Tick{
			ReceivedTimestamp: time.Now(),
			EventLog:          eventLog,
		}
	}
}

func (g *MarketDataGateway) processTradeEvent(data []byte) {
	var trade tradeEvent
	err := json.Unmarshal(data, &trade)
	if err != nil {
		log.Panicf("Failed to unmarshal trade event: %s", err)
	}

	var direction gateway.Side
	if trade.IsBuyerMaker {
		direction = gateway.Ask
	} else {
		direction = gateway.Bid
	}

	g.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog: []gateway.Event{
			{
				Type: gateway.TradeEvent,
				Data: gateway.Trade{
					ID:        strconv.FormatInt(trade.TradeID, 10),
					Timestamp: time.Unix(0, trade.Timestamp*int64(time.Microsecond)),
					Symbol:    trade.Symbol,
					Direction: direction,
					Price:     trade.Price,
					Amount:    trade.Quantity,
				},
			},
		},
	}
}
