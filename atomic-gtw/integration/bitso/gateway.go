package bitso

import (
	"log"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
)

const (
	defaultAmountTick = 0.********
)

var customAmountTick = map[string]float64{
	"XRP":  0.000001,
	"TUSD": 0.01,
	"USD":  0.01,
}

var customPriceTick = map[string]float64{
	"SHIB": 0.********,
}

var Exchange = gateway.Exchange{
	Name: "Bitso",
}

type Gateway struct {
	base.Gateway
	options           gateway.Options
	accountGateway    *AccountGateway
	marketDataGateway *MarketDataGateway
	tickCh            chan gateway.Tick
	api               *API
}

func NewGateway(options gateway.Options) gateway.Gateway {
	gtw := &Gateway{
		options: options,
		api:     NewAPI(options),
		tickCh:  make(chan gateway.Tick, 100),
	}
	gtw.Gateway = base.NewGateway(gtw)
	return gtw
}

func (gtw *Gateway) Connect() error {
	var err error
	ws := NewWsSession(gtw.options)

	if len(gtw.options.Proxies) > 0 {
		s := rand.NewSource(time.Now().Unix())
		r := rand.New(s)
		ri := r.Intn(len(gtw.options.Proxies))
		proxy := gtw.options.Proxies[ri]

		log.Printf("Bitso using [%s] proxy server to connect mktd ws", proxy)

		ws.SetProxy(proxy)
	}

	if err = ws.Connect(); err != nil {
		return err
	}

	gtw.accountGateway = NewAccountGateway(gtw.options, gtw.api, gtw.tickCh)
	gtw.marketDataGateway = NewMarketDataGateway(gtw.options, gtw.tickCh)

	if err = gtw.marketDataGateway.Connect(ws, gtw.api); err != nil {
		return err
	}

	if gtw.options.ApiKey != "" {
		// Set account gateway so market data gateway tracks trade updates
		gtw.marketDataGateway.SetAccountGateway(gtw.accountGateway)
	}

	return nil
}

func (gtw *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return gtw.marketDataGateway.SubscribeMarkets(markets)
}

func (gtw *Gateway) Close() error {
	return nil
}

func (gtw *Gateway) Exchange() gateway.Exchange {
	return Exchange
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	symbols, err := g.api.FetchSymbols()
	if err != nil {
		return nil, err
	}

	commonMarkets := make([]gateway.Market, 0, len(symbols))
	for _, symbol := range symbols {
		commonMarkets = append(commonMarkets, symbolToCommonMarket(symbol))
	}

	return commonMarkets, nil
}

func (gtw *Gateway) AccountGateway() gateway.AccountGateway {
	return gtw.accountGateway
}

func (gtw *Gateway) Tick() chan gateway.Tick {
	return gtw.tickCh
}

func symbolToCommonMarket(symbol ApiAvailableBook) gateway.Market {
	splitedSymbol := strings.Split(symbol.Symbol, "_") // e.g., "btc_mxn"
	if len(splitedSymbol) < 2 {
		log.Printf("Bitso market [%s] impossible to extract base/quote assets", symbol.Symbol)
		return gateway.Market{}
	}

	pair := gateway.Pair{
		Base:  strings.ToUpper(splitedSymbol[0]),
		Quote: strings.ToUpper(splitedSymbol[1]),
	}
	amountTick, ok := customAmountTick[pair.Base]
	if !ok {
		amountTick = defaultAmountTick
	}
	priceTick, ok := customPriceTick[pair.Base]
	if !ok {
		priceTick = symbol.TickSize
	}

	return gateway.Market{
		Exchange:               Exchange,
		Pair:                   pair,
		Symbol:                 symbol.Symbol,
		TakerFee:               symbol.Fees.FlatRate.Taker / 100,
		MakerFee:               symbol.Fees.FlatRate.Maker / 100,
		PriceTick:              priceTick,
		AmountTick:             amountTick,
		MinimumOrderSize:       symbol.MinAmount,
		MinimumOrderMoneyValue: symbol.MinValue,
		MarketType:             gateway.SpotMarket,
	}
}

func parsePriceLevelsToDepth(levels []ApiOrderBookResult) ([]gateway.PriceLevel, error) {
	var priceLevels []gateway.PriceLevel
	for _, level := range levels {
		price, err := strconv.ParseFloat(level.Price, 64)
		if err != nil {
			return nil, err
		}
		amount, err := strconv.ParseFloat(level.Amount, 64)
		if err != nil {
			return nil, err
		}

		priceLevels = append(priceLevels, gateway.PriceLevel{
			Price:  price,
			Amount: amount,
		})
	}
	return priceLevels, nil
}

func (gtw *Gateway) GetDepthBook(market gateway.Market, params gateway.GetDepthParams) (gateway.DepthBook, error) {
	var asks []gateway.PriceLevel
	var bids []gateway.PriceLevel

	depth, err := gtw.api.FetchOrderBook(market.Symbol)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	asks, err = parsePriceLevelsToDepth(depth.Asks)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	bids, err = parsePriceLevelsToDepth(depth.Bids)
	if err != nil {
		return gateway.DepthBook{}, err
	}
	depthBook := gateway.DepthBook{
		Sequence: depth.Sequence,
		Asks:     asks,
		Bids:     bids,
	}

	return depthBook, nil
}
