package bitso

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type MarketDataGateway struct {
	options   gateway.Options
	tickCh    chan gateway.Tick
	queues    map[string]depthQueue
	queueLock sync.Mutex
	ws        *WsSession
	api       *API
	accGtw    *AccountGateway
}

func NewMarketDataGateway(opts gateway.Options, tickCh chan gateway.Tick) *MarketDataGateway {
	return &MarketDataGateway{
		options: opts,
		tickCh:  tickCh,
		queues:  make(map[string]depthQueue),
	}
}

func (mktGtw *MarketDataGateway) SetAccountGateway(accGtw *AccountGateway) {
	mktGtw.accGtw = accGtw
}

func (mktGtw *MarketDataGateway) Connect(ws *WsSession, api *API) error {
	mktGtw.ws = ws
	mktGtw.api = api

	return nil
}

// priceLevel and priceLevels are used to represent the orderbook
type priceLevel map[string]float64

func (pxs priceLevel) Total() float64 {
	sum := 0.0
	for _, amount := range pxs {
		sum += amount
	}
	return sum
}

type priceLevels map[string]priceLevel

func (pxs priceLevels) UpdateAndReturnTotal(price string, orderID string, amount float64) float64 {
	px, ok := pxs[price]
	if !ok {
		px = make(priceLevel)
		pxs[price] = px
	}
	if amount > 0 {
		px[orderID] = amount
	} else {
		delete(px, orderID)
	}
	// Cleanup price level if it's empty
	if len(px) <= 0 {
		delete(pxs, price)
	}
	return px.Total()
}

type depthQueue struct {
	ReceivedSnapshot bool
	Queue            []WsResponse
	Asks             priceLevels
	Bids             priceLevels
	Lock             *sync.Mutex
	LastSequence     int64
}

func (mktGtw *MarketDataGateway) SubscribeMarkets(markets []gateway.Market) error {
	log.Printf("Bitso subscribing to %d markets...", len(markets))

	ch := make(chan WsResponse)
	mktGtw.ws.SubscribeMessages(ch, nil)

	go mktGtw.subscriptionMessageHandler(ch)

	var requests []WsRequest
	for _, market := range markets {
		orderBookRequest := WsRequest{
			Action: "subscribe",
			Symbol: market.Symbol,
			Type:   "diff-orders",
		}

		tradesRequest := WsRequest{
			Action: "subscribe",
			Symbol: market.Symbol,
			Type:   "trades",
		}

		requests = append(requests, orderBookRequest, tradesRequest)
	}

	err := mktGtw.ws.RequestSubscriptions(requests)
	if err != nil {
		return err
	}

	// Ws messages will be queued, now we need to fetch each initial orderbook
	// snapshot for each market
	for _, market := range markets {
		err := mktGtw.fetchOrderBookSnapshot(market.Symbol)
		if err != nil {
			return err
		}
	}

	return nil
}

func (mktGtw *MarketDataGateway) getQueue(symbol string) depthQueue {
	mktGtw.queueLock.Lock()
	defer mktGtw.queueLock.Unlock()
	queue, ok := mktGtw.queues[symbol]
	if !ok {
		queue = depthQueue{
			ReceivedSnapshot: false,
			Queue:            make([]WsResponse, 0),
			Asks:             make(priceLevels),
			Bids:             make(priceLevels),
			Lock:             &sync.Mutex{},
		}
		mktGtw.queues[symbol] = queue
	}
	return queue
}

func (mktGtw *MarketDataGateway) setQueue(symbol string, queue depthQueue) {
	mktGtw.queueLock.Lock()
	defer mktGtw.queueLock.Unlock()
	mktGtw.queues[symbol] = queue
}

func (mktGtw *MarketDataGateway) fetchOrderBookSnapshot(symbol string) error {
	// This might be rate limited, if we try to fetch too many markets
	// Allow for retries when this happens.
	var snapshot ApiOrderBook
	var snapshotErr error
	maxRetries := 3
	retryDelay := 1 * time.Second
	for i := 0; i < maxRetries; i++ {
		snapshot, snapshotErr = mktGtw.api.FetchOrderBook(symbol)
		if snapshotErr != nil && tooManyRequestsMatch.MatchString(snapshotErr.Error()) {
			log.Printf("%s rate limited while trying to fetch orderbook [%s], retrying in %v second...", Exchange, symbol, retryDelay)
			time.Sleep(retryDelay)
			retryDelay = retryDelay + 1*time.Second
			continue
		}
		break
	}

	if snapshotErr != nil {
		return snapshotErr
	}

	// Lock while we process the snapshot
	queue := mktGtw.getQueue(symbol)
	queue.Lock.Lock()
	defer queue.Lock.Unlock()

	// Map orderbook snapshot to our internal representation
	var asks = make(priceLevels)
	var bids = make(priceLevels)
	var totalAsks = make(map[string]float64)
	var totalBids = make(map[string]float64)
	mapOrders := func(orders []ApiOrderBookResult, side gateway.Side) error {
		for _, order := range orders {
			amount, err := strconv.ParseFloat(order.Amount, 64)
			if err != nil {
				return fmt.Errorf("%s parse amount \"%s\" into float64 from book %s response, err: %w", side, order.Amount, symbol, err)
			}
			if side == gateway.Ask {
				total := asks.UpdateAndReturnTotal(order.Price, order.OrderID, amount)
				totalAsks[order.Price] = total
			} else {
				total := bids.UpdateAndReturnTotal(order.Price, order.OrderID, amount)
				totalBids[order.Price] = total
			}
		}
		return nil
	}
	if err := mapOrders(snapshot.Asks, gateway.Ask); err != nil {
		return err
	}
	if err := mapOrders(snapshot.Bids, gateway.Bid); err != nil {
		return err
	}

	// Dispatch events based on initial price levels
	var eventLogs = make([]gateway.Event, 0, len(asks)+len(bids)+1)

	appendOrderEvents := func(symbol string, side gateway.Side, prices map[string]float64) error {
		for priceStr, total := range prices {
			px, err := strconv.ParseFloat(priceStr, 64)
			if err != nil {
				return fmt.Errorf("parse price \"%s\" into float64 from book %s response, err: %w", priceStr, symbol, err)
			}

			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  px,
					Amount: total,
				},
			}

			eventLogs = append(eventLogs, event)
		}

		return nil
	}

	eventLogs = append(eventLogs, gateway.Event{
		Type: gateway.SnapshotSequenceEvent,
		Data: gateway.SnapshotSequence{
			Symbol: symbol,
		},
	})

	if err := appendOrderEvents(symbol, gateway.Bid, totalBids); err != nil {
		return err
	}
	if err := appendOrderEvents(symbol, gateway.Ask, totalAsks); err != nil {
		return err
	}

	mktGtw.dispatchEventLogs(eventLogs)

	// Now that we have the snapshot, we can process the queued messages
	i := 0
	lastSequence := snapshot.Sequence
	for _, msg := range queue.Queue {
		if msg.Sequence > snapshot.Sequence {
			i++
			lastSequence = msg.Sequence
			if err := mktGtw.processDiffOrder(symbol, msg.Payload); err != nil {
				return fmt.Errorf("snapshot process diff order err: %w", err)
			}
		}
	}

	queue.Queue = nil
	queue.Asks = asks
	queue.Bids = bids
	queue.ReceivedSnapshot = true
	queue.LastSequence = lastSequence
	mktGtw.setQueue(symbol, queue)

	log.Printf("%s market [%s] processed OB snapshot, ask prices/orders [%d/%d], bid prices/orders [%d/%d], queued depth events [%d],", Exchange, symbol, len(asks), len(snapshot.Asks), len(bids), len(snapshot.Bids), i)

	return nil
}

func (mktGtw *MarketDataGateway) subscriptionMessageHandler(ch chan WsResponse) {
	for msg := range ch {
		switch msg.Type {
		case "diff-orders":
			if err := mktGtw.processRawOrderBookUpdate(msg); err != nil {
				panic(err)
			}
		case "trades":
			if err := mktGtw.processTrades(msg); err != nil {
				panic(err)
			}
		}
	}
}

func (mktGtw *MarketDataGateway) processRawOrderBookUpdate(msg WsResponse) error {
	// Empty msg
	if len(msg.Payload) <= 0 {
		return nil
	}

	// Check if message can be processed or should be queued
	// Also, grab a lock to avoid race conditions
	queue := mktGtw.getQueue(msg.Symbol)
	queue.Lock.Lock()
	defer queue.Lock.Unlock()
	if !queue.ReceivedSnapshot {
		queue.Queue = append(queue.Queue, msg)
		mktGtw.setQueue(msg.Symbol, queue)
		return nil
	}

	// Check sequence number
	if msg.Sequence != queue.LastSequence+1 {
		sequenceGap := msg.Sequence - queue.LastSequence
		log.Printf("%s received out of sequence message for market [%s], expected sequence [%d], received sequence [%d], sequence gap [%d]", Exchange, msg.Symbol, queue.LastSequence+1, msg.Sequence, sequenceGap)
	}
	queue.LastSequence = msg.Sequence
	defer mktGtw.setQueue(msg.Symbol, queue)

	// If we have a snapshot, we can process the message
	if err := mktGtw.processDiffOrder(msg.Symbol, msg.Payload); err != nil {
		return fmt.Errorf("process diff order err: %w", err)
	}

	return nil
}

func (mktGtw *MarketDataGateway) processDiffOrder(symbol string, payload json.RawMessage) error {
	var diffOrders []WsOrder
	if err := json.Unmarshal(payload, &diffOrders); err != nil {
		return fmt.Errorf("unmarshal err: %w, payload: %s", err, string(payload))
	}

	queue := mktGtw.getQueue(symbol)

	totalBids := make(map[string]float64)
	totalAsks := make(map[string]float64)
	for _, order := range diffOrders {
		var amount float64
		if order.Status == "open" && order.Amount != "" {
			val, err := strconv.ParseFloat(order.Amount, 64)
			if err != nil {
				log.Printf("%s failed parse float from payload:\n%s", Exchange, string(payload))
				return fmt.Errorf("parse amount \"%s\" into float64 of book %s response, err: %w", order.Amount, symbol, err)
			}
			amount = val
		}

		if normalizeSide(order.Side) == gateway.Bid {
			total := queue.Bids.UpdateAndReturnTotal(order.Price, order.OrderID, amount)
			totalBids[order.Price] = total
		} else {
			total := queue.Bids.UpdateAndReturnTotal(order.Price, order.OrderID, amount)
			totalAsks[order.Price] = total
		}
	}

	// Dispatch events based on updated price levels
	eventLog := make([]gateway.Event, 0, len(diffOrders))
	appendEventsFromPriceLevels := func(side gateway.Side, updatedPrices map[string]float64) error {
		for price, total := range updatedPrices {
			px, err := strconv.ParseFloat(price, 64)
			if err != nil {
				log.Printf("%s failed parse float from payload:\n%s", Exchange, string(payload))
				return fmt.Errorf("parse price \"%s\" into float64 of book %s response, err: %w", price, symbol, err)
			}

			event := gateway.Event{
				Type: gateway.DepthEvent,
				Data: gateway.Depth{
					Symbol: symbol,
					Side:   side,
					Price:  px,
					Amount: total,
				},
			}

			eventLog = append(eventLog, event)
		}

		return nil
	}
	if err := appendEventsFromPriceLevels(gateway.Bid, totalBids); err != nil {
		return err
	}
	if err := appendEventsFromPriceLevels(gateway.Ask, totalAsks); err != nil {
		return err
	}

	mktGtw.dispatchEventLogs(eventLog)

	return nil
}

func (mktGtw *MarketDataGateway) processTrades(msg WsResponse) error {
	var eventLogs []gateway.Event

	trades := []WsTrade{}

	// As far as I checked, we receive an empty payload as the first
	// message sent for this method.
	if len(msg.Payload) <= 0 {
		return nil
	}

	if err := json.Unmarshal(msg.Payload, &trades); err != nil {
		return err
	}

	for _, trade := range trades {
		eventLogs = append(eventLogs, gateway.Event{
			Type: gateway.TradeEvent,
			Data: gateway.Trade{
				Timestamp: time.Unix(0, trade.TimestampMs*int64(time.Millisecond)),
				Symbol:    msg.Symbol,
				ID:        strconv.FormatInt(trade.TransactionID, 10),
				Direction: normalizeSide(trade.TakerSide),
				Amount:    trade.Amount,
				Price:     trade.Rate,
			},
		})
	}

	mktGtw.dispatchEventLogs(eventLogs)

	if mktGtw.accGtw != nil {
		mktGtw.accGtw.processTradeUpdate(msg.Symbol, trades)
	}

	return nil
}

func (mktGtw *MarketDataGateway) dispatchEventLogs(events []gateway.Event) {
	mktGtw.tickCh <- gateway.Tick{
		ReceivedTimestamp: time.Now(),
		EventLog:          events,
	}
}

func normalizeSide(t int8) gateway.Side {
	if t == 0 {
		return gateway.Bid
	} else if t == 1 {
		return gateway.Ask
	} else {
		return ""
	}
}

func reverseSide(side gateway.Side) gateway.Side {
	if side == gateway.Bid {
		return gateway.Ask
	}
	return gateway.Bid
}
