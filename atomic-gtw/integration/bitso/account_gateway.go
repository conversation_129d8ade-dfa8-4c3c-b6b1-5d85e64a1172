package bitso

import (
	"log"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/utils"
)

type AccountGateway struct {
	base.AccountGateway
	options gateway.Options
	api     *API
	tickCh  chan gateway.Tick
}

func NewAccountGateway(options gateway.Options, api *API, tickCh chan gateway.Tick) *AccountGateway {
	return &AccountGateway{
		options: options,
		api:     api,
		tickCh:  tickCh,
	}
}

func (g *AccountGateway) processTradeUpdate(symbol string, trades []WsTrade) {
	events := make([]gateway.Event, 0, len(trades)*2)

	for _, t := range trades {
		fill := gateway.Fill{
			ID:        strconv.FormatInt(t.TransactionID, 10),
			Timestamp: time.Unix(0, t.TimestampMs*int64(time.Millisecond)),
			Symbol:    symbol,
			Side:      normalizeSide(t.TakerSide),
			Amount:    t.Amount,
			Price:     t.Rate,
			OrderID:   t.TakerOrderID,
			Taker:     true,
		}

		// Track the fill as the taker
		events = append(events, gateway.NewFillEvent(fill))

		// Now track the fill as the maker
		fill.OrderID = t.MakerOrderID
		fill.Side = reverseSide(fill.Side)
		fill.Taker = false
		events = append(events, gateway.NewFillEvent(fill))
	}

	if len(events) > 0 {
		g.tickCh <- gateway.TickWithEvents(events...)
	}
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	res, err := g.api.FetchOpenOrders(market.Symbol)
	if err != nil {
		return []gateway.Order{}, err
	}

	orders := make([]gateway.Order, len(res))

	for i, openOrder := range res {
		orders[i] = bitsoOrderToGatewayOrder(openOrder, market)
	}

	return orders, nil
}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	balances, err := g.api.FetchBalances()

	if err != nil {
		return []gateway.Balance{}, err
	}

	gatewayBalances := make([]gateway.Balance, len(balances))
	for i, balance := range balances {
		gatewayBalances[i] = gateway.Balance{
			Asset:     strings.ToUpper(balance.Currency),
			Total:     balance.Total,
			Available: balance.Available,
		}
	}

	return gatewayBalances, nil
}

var insufficientBalanceMatch = regexp.MustCompile(`(0379)`)
var invalidNonceMatch = regexp.MustCompile(`(0206|0207)`)
var tooManyRequestsMatch = regexp.MustCompile(`(0801|0802)`)

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	createOrderReq := ApiCreateOrderRequest{
		Symbol: order.Market.Symbol,
		Side:   gatewaySideToBitsoSide(order.Side),
		Type:   "limit",
		Major:  utils.FloatToStringWithTick(order.Amount, order.Market.AmountTick),
		Price:  utils.FloatToStringWithTick(order.Price, order.Market.PriceTick),
	}

	if order.PostOnly {
		createOrderReq.TimeInForce = "postonly"
	}

	oid, err := g.api.CreateOrder(createOrderReq)
	if err != nil {
		switch {
		case insufficientBalanceMatch.MatchString(err.Error()):
			return "", gateway.InsufficientBalanceErr
		case invalidNonceMatch.MatchString(err.Error()):
			return "", gateway.InvalidNonceErr
		case tooManyRequestsMatch.MatchString(err.Error()):
			return "", gateway.RateLimitErr
		default:
			return "", err
		}
	}

	return oid, err
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	err := g.api.CancelOrder(order.ID)
	if err != nil {
		return err
	}

	return nil
}

func orderStatusToGatewayState(status string) gateway.OrderState {
	switch status {
	case "queued":
		return gateway.OrderSent
	case "open":
		return gateway.OrderOpen
	case "partially filled":
		return gateway.OrderPartiallyFilled
	case "closed":
		return gateway.OrderClosed
	case "cancelled":
		return gateway.OrderCancelled
	case "completed":
		return gateway.OrderFullyFilled
	default:
		log.Printf("Bitso orderStatusToGatewayState unkown order status [%s]", status)
		return gateway.OrderUnknown
	}
}

func bitsoOrderToGatewayOrder(order ApiOrder, market gateway.Market) gateway.Order {
	return gateway.Order{
		Market:       market,
		ID:           order.ID,
		State:        orderStatusToGatewayState(order.Status),
		Amount:       order.OriginalAmount,
		Price:        order.Price,
		FilledAmount: order.OriginalAmount - order.UnfilledAmount,
		Side:         bitsoSideToGatewaySide(order.Side),
	}
}

func bitsoSideToGatewaySide(side string) gateway.Side {
	if side == "buy" {
		return gateway.Bid
	}

	return gateway.Ask
}

func gatewaySideToBitsoSide(side gateway.Side) string {
	if side == gateway.Bid {
		return "buy"
	}

	return "sell"
}
