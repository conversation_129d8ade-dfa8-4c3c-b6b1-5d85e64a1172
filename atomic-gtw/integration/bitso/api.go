package bitso

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/buger/jsonparser"
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

const (
	apiBase           = "https://api.bitso.com"
	apiOrderBook      = "/v3/order_book"
	apiAvailableBooks = "/v3/available_books"
	apiAccountBalance = "/v3/balance"
	apiOrders         = "/v3/orders"
	apiOpenOrders     = "/v3/open_orders"
)

type ApiResponse struct {
	Success bool            `json:"success"`
	Payload json.RawMessage `json:"payload"`
}

type ApiError struct {
	Message string `json:"message"`
	Code    string `json:"code"`
}

type ApiAuth struct {
	Nonce       int64
	HttpMethod  string
	RequestPath string
	Payload     json.RawMessage
}

// We can receive floats as strings like ".001", what breaks the json struct parse.
// For this reason I receive them with a "X" var, and parse it to the "non-X"
// var as float (e.g., XMaxValue -> parsefloat MaxValue)
type ApiAvailableBook struct {
	Symbol    string  `json:"book"`
	MinAmount float64 `json:"minimum_amount,string"`
	MaxAmount float64 `json:"maximum_amount,string"`
	MinPrice  float64 `json:"minimum_price,string"`
	MaxPrice  float64 `json:"maximum_price,string"`
	MinValue  float64 `json:"minimum_value,string"`
	MaxValue  float64 `json:"maximum_value,string"`
	TickSize  float64 `json:"tick_size,string"`
	Fees      ApiFee  `json:"fees"`
}

type ApiFee struct {
	FlatRate struct {
		Maker float64 `json:"maker,string"`
		Taker float64 `json:"taker,string"`
	} `json:"flat_tate"`
}

type ApiBalance struct {
	Currency  string  `json:"currency"`
	Total     float64 `json:"total,string"`
	Locked    float64 `json:"locked,string"`
	Available float64 `json:"available,string"`
}

// An order must be specified in terms of major or minor, never both.
// Major = crypto
// Minor = FIAT
type ApiCreateOrderRequest struct {
	Symbol      string `json:"book"`
	Side        string `json:"side"`
	Type        string `json:"type"`
	Major       string `json:"major,omitempty"`
	Minor       string `json:"minor,omitempty"`
	Price       string `json:"price,omitempty"`
	Stop        string `json:"stop,omitempty"`
	TimeInForce string `json:"time_in_force,omitempty"`
	OriginID    string `json:"origin_id,omitempty"`
}

type ApiOrderBook struct {
	Sequence int64                `json:"sequence,string"`
	Asks     []ApiOrderBookResult `json:"asks"`
	Bids     []ApiOrderBookResult `json:"bids"`
}

type ApiOrderBookResult struct {
	Symbol  string `json:"book"`
	OrderID string `json:"oid"`
	Price   string `json:"price"`
	Amount  string `json:"amount"`
}

type ApiOrder struct {
	Symbol         string  `json:"book"`
	OriginalAmount float64 `json:"original_amount,string"`
	UnfilledAmount float64 `json:"unfilled_amount,string"`
	OriginalValue  float64 `json:"original_value,string"`
	CreatedAt      string  `json:"created_at"`
	UpdatedAt      string  `json:"updated_at"`
	Price          float64 `json:"price,string"`
	ID             string  `json:"oid"`
	Side           string  `json:"side"`
	Status         string  `json:"status"`
	Type           string  `json:"type"`
}

type API struct {
	options             gateway.Options
	httpClient          *utils.HttpClient
	noProxiesHttpClient *utils.HttpClient
	lastNonceUsed       int64
	nonceMutex          sync.Mutex
}

func NewAPI(options gateway.Options) *API {
	client := utils.NewHttpClient()
	client.UseProxies(options.Proxies)

	return &API{
		options:             options,
		httpClient:          client,
		noProxiesHttpClient: utils.NewHttpClient(),
	}
}

func (api *API) getNonce() int64 {
	api.nonceMutex.Lock()
	defer api.nonceMutex.Unlock()

	nonce := time.Now().UnixMicro()
	if nonce <= api.lastNonceUsed {
		nonce = api.lastNonceUsed + 1
	}

	api.lastNonceUsed = nonce
	return api.lastNonceUsed
}

func (api *API) FetchOrderBook(symbol string) (orderBook ApiOrderBook, err error) {
	path := apiOrderBook + "?aggregate=false&book=" + symbol
	req, err := api.newHttpRequest(http.MethodGet, path, nil, false)
	if err != nil {
		return orderBook, err
	}

	response := ApiResponse{}

	if err = api.makeHttpRequest(req, &response); err != nil {
		return orderBook, err
	}

	if err = json.Unmarshal(response.Payload, &orderBook); err != nil {
		return orderBook, fmt.Errorf("Failed in parse Bitso order book, err: %g", err)
	}

	return orderBook, nil
}

func (api *API) FetchSymbols() (symbols []ApiAvailableBook, err error) {
	req, err := api.newHttpRequest(http.MethodGet, apiAvailableBooks, nil, false)
	if err != nil {
		return symbols, err
	}

	response := ApiResponse{}

	if err = api.makeHttpRequest(req, &response); err != nil {
		return symbols, err
	}

	if err = json.Unmarshal(response.Payload, &symbols); err != nil {
		return symbols, fmt.Errorf("Failed in parse Bitso symbols, err: %g", err)
	}

	return symbols, nil
}

func (api *API) FetchBalances() (balances []ApiBalance, err error) {
	req, err := api.newHttpRequest(http.MethodGet, apiAccountBalance, nil, true)
	if err != nil {
		return balances, err
	}

	response := ApiResponse{}

	if err = api.makeHttpRequest(req, &response); err != nil {
		return balances, err
	}

	data, _, _, _ := jsonparser.Get(response.Payload, "balances")

	if err = json.Unmarshal(data, &balances); err != nil {
		return balances, fmt.Errorf("Failed in parse Bitso balances, err: %g", err)
	}

	return balances, nil
}

func (api *API) CreateOrder(createOrderReq ApiCreateOrderRequest) (string, error) {
	body, err := json.Marshal(createOrderReq)
	if err != nil {
		return "", err
	}

	req, err := api.newHttpRequest(http.MethodPost, apiOrders, body, true)
	if err != nil {
		return "", err
	}

	response := ApiResponse{}

	if err = api.makeHttpRequest(req, &response); err != nil {
		return "", err
	}

	oid, _, _, _ := jsonparser.Get(response.Payload, "oid")

	return string(oid), nil
}

func (api *API) CancelOrder(orderID string) error {
	path := fmt.Sprintf("%s/%s", apiOrders, orderID)
	req, err := api.newHttpRequest(http.MethodDelete, path, nil, true)
	if err != nil {
		return err
	}

	if err = api.makeHttpRequest(req, nil); err != nil {
		return err
	}

	return nil
}

func (api *API) FetchOpenOrders(symbol string) (orders []ApiOrder, err error) {
	filterOrdersBySymbol := func(orders []ApiOrder) []ApiOrder {
		n := 0
		for _, order := range orders {
			if order.Symbol == symbol {
				orders[n] = order
				n++
			}
		}

		return orders[:n]
	}

	// TODO: Bitso allow us to filter with the query parameter "book", but
	// there is a bug with this filter that they're solving first. When they
	// launch the fix, we can use the commented url bellow and stop using
	// filterOrdersBySymbol func.
	//url := fmt.Sprintf("%s%s?book=%s", apiBase, apiOpenOrders, symbol)

	req, err := api.newHttpRequest(http.MethodGet, apiOpenOrders, nil, true)

	if err != nil {
		return orders, err
	}

	response := ApiResponse{}

	if err = api.makeHttpRequest(req, &response); err != nil {
		return orders, err
	}

	if err = json.Unmarshal(response.Payload, &orders); err != nil {
		return orders, fmt.Errorf("Failed in parse Bitso open orders, err: %g", err)
	}

	return filterOrdersBySymbol(orders), nil
}

func (api *API) LookupOrders(ids []string) (orders []ApiOrder, err error) {
	path := fmt.Sprintf("%s?oids=%s", apiOrders, strings.Join(ids, ","))
	req, err := api.newHttpRequest(http.MethodGet, path, nil, true)
	if err != nil {
		return orders, err
	}

	response := ApiResponse{}
	if err = api.makeHttpRequest(req, &response); err != nil {
		return orders, err
	}

	if err = json.Unmarshal(response.Payload, &orders); err != nil {
		return orders, fmt.Errorf("Failed in parse Bitso orders lookup, err: %g", err)
	}

	return orders, nil
}

func (api *API) makeHttpRequest(req *http.Request, responseObject interface{}) error {
	body, err := api.sendHttpRequest(req)
	if err != nil {
		return err
	}

	errVal, dataType, _, _ := jsonparser.Get(body, "error")
	if dataType != jsonparser.NotExist {
		return fmt.Errorf("Bitso %s %s responded with error message: %s\nresponse body: %s", req.Method, req.URL.String(), string(errVal), string(body))
	}

	if responseObject != nil {
		err = json.Unmarshal(body, responseObject)

		if err != nil {
			return fmt.Errorf("failed to unmarshal json, body: %s, unmarshal err: %s", body, err)
		}
	}

	return nil
}

func (api *API) newHttpRequest(method string, path string, data []byte, auth bool) (*http.Request, error) {
	body := io.Reader(nil)
	if data != nil {
		body = bytes.NewBuffer(data)
	}

	fullUrl := apiBase + path
	req, err := http.NewRequest(method, fullUrl, body)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")

	// All requests should be authenticated if the API key are set
	// since even for public endpoints, if we authenticate, our rate limit
	// is increased, to match our accounts rate limit.
	if auth || api.options.AuthAllRequests {
		auth := ApiAuth{
			Nonce:       api.getNonce(),
			HttpMethod:  method,
			RequestPath: path,
		}

		if data != nil {
			auth.Payload = data
		}

		message := fmt.Sprint(auth.Nonce) + auth.HttpMethod + auth.RequestPath + string(auth.Payload)

		signer := hmac.New(sha256.New, []byte(api.options.ApiSecret))
		signer.Write([]byte(message))

		signature := hex.EncodeToString(signer.Sum(nil))

		authHeader := fmt.Sprintf("Bitso %s:%v:%s", api.options.ApiKey, auth.Nonce, signature)

		req.Header.Set("Authorization", authHeader)
	}

	return req, nil
}

func (api *API) sendHttpRequest(req *http.Request) ([]byte, error) {
	var httpClient *utils.HttpClient
	if api.options.NoProxiesForPrivateRequests && req.Header.Get("Authorization") != "" {
		httpClient = api.noProxiesHttpClient
	} else {
		httpClient = api.httpClient
	}

	res, err := httpClient.SendRequest(req)
	if err != nil {
		return nil, err
	}

	body, err := ioutil.ReadAll(res.Body)

	return body, err
}
