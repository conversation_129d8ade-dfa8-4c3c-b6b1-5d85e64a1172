package safegateway

import (
	"fmt"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/gateway/base"
	"github.com/herenow/atomic-gtw/safegateway/service"
	"github.com/vmihailenco/msgpack/v5"
)

type AccountGateway struct {
	base.AccountGateway
	gtw *SafeGateway
}

func NewAccountGateway(gtw *SafeGateway) gateway.AccountGateway {
	return &AccountGateway{
		gtw: gtw,
	}
}

func (g *AccountGateway) Balances() (balances []gateway.Balance, err error) {
	err = g.gtw.requestService(service.ACC_GTW_BALANCES, nil, &balances, 15*time.Second)
	return balances, err
}

func (g *AccountGateway) OpenOrders(market gateway.Market) (orders []gateway.Order, err error) {
	data, err := msgpack.Marshal(market)
	if err != nil {
		return orders, fmt.Errorf("marshal market: %w", err)
	}
	err = g.gtw.requestService(service.ACC_GTW_OPEN_ORDERS, data, &orders, 15*time.Second)
	return orders, err
}

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	data, err := msgpack.Marshal(order)
	if err != nil {
		return orderId, fmt.Errorf("marshal order: %w", err)
	}
	err = g.gtw.requestService(service.ACC_GTW_SEND_ORDER, data, &orderId, 60*time.Second)
	return orderId, err
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	data, err := msgpack.Marshal(order)
	if err != nil {
		return fmt.Errorf("marshal order: %w", err)
	}
	return g.gtw.requestService(service.ACC_GTW_CANCEL_ORDER, data, nil, 15*time.Second)
}
