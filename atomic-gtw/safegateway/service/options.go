package service

import (
	"fmt"
	"reflect"

	"github.com/herenow/atomic-gtw/gateway"
)

// Init options when initializing a safe gateway service (process)
type Options struct {
	Exchange string          `json:"exchange" required:"true"`
	RPCAddr  string          `json:"rpc-addr" required:"true"`
	GtwOpts  gateway.Options `json:"gtw-opts"`
}

// Check that the options are valid
func (o Options) Validate() error {
	// Check required fields
	val := reflect.ValueOf(o)
	for i := 0; i < val.Type().NumField(); i++ {
		tag := val.Type().Field(i).Tag

		required := tag.Get("required")
		if required == "true" && val.Field(i).Kind() == reflect.String && val.Field(i).String() == "" {
			return fmt.Errorf("%s is required", tag.Get("json"))
		}
	}

	// Check exchange exists
	_, ok := gateway.ExchangeBySymbol(o.Exchange)
	if !ok {
		return fmt.Errorf("exchange [%s] not found, available exchanges are:\n%s", o.Exchange, gateway.Exchanges)
	}

	return nil
}
