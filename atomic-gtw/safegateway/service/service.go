package service

import (
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/vmihailenco/msgpack/v5"
	"go.nanomsg.org/mangos/v3/protocol/pair"
)

type ServiceMethod string

const (
	REPLY                 ServiceMethod = "REPLY"
	ALIVE                 ServiceMethod = "ALIVE"
	GTW_GET_MARKETS       ServiceMethod = "GTW/GET_MARKETS"
	GTW_GET_DEPTH_BOOK    ServiceMethod = "GTW/GET_DEPTH_BOOK"
	GTW_SUBSCRIBE_MARKETS ServiceMethod = "GTW/SUBSCRIBE_MARKETS"
	GTW_TICK              ServiceMethod = "GTW/TICK"
	ACC_GTW_BALANCES      ServiceMethod = "ACC_GTW/BALANCES"
	ACC_GTW_OPEN_ORDERS   ServiceMethod = "ACC_GTW/OPEN_ORDERS"
	ACC_GTW_SEND_ORDER    ServiceMethod = "ACC_GTW/SEND_ORDER"
	ACC_GTW_CANCEL_ORDER  ServiceMethod = "ACC_GTW/CANCEL_ORDER"
)

type SafeGatewayService interface {
	Init() error
	GetMarkets(Message) (interface{}, error)
	GetDepthBook(Message) (interface{}, error)
	SubscribeMarkets(Message) (interface{}, error)
}

type serviceEndpoint func(Message) (interface{}, error)

type safeGatewayService struct {
	gtw       gateway.Gateway
	opts      Options
	transport SafeGatewayTransport
	services  map[ServiceMethod]serviceEndpoint
}

func NewSafeGatewayService(opts Options) SafeGatewayService {
	return &safeGatewayService{
		opts:     opts,
		services: make(map[ServiceMethod]serviceEndpoint),
	}
}

func (s *safeGatewayService) Init() error {
	exchange, ok := gateway.ExchangeBySymbol(s.opts.Exchange)
	if !ok {
		return fmt.Errorf("exchange \"%s\" not found, available exchanges are:\n%s", s.opts.Exchange, gateway.Exchanges)
	}

	gtw, ok := gateway.NewByExchange(exchange, s.opts.GtwOpts)
	if !ok {
		return fmt.Errorf("gtw not found for exchange: %s", exchange)
	}
	s.gtw = gtw

	// Initialize communication with socket
	log.Printf("Safe gateway instance connecting to sock at %s", s.opts.RPCAddr)

	sock, err := pair.NewSocket()
	if err != nil {
		return fmt.Errorf("new  socket pair: %w", err)
	}

	err = sock.Dial(s.opts.RPCAddr)
	if err != nil {
		return fmt.Errorf("socket dail: %w", err)
	}

	transportMsgCh := make(chan Message, 10)
	s.transport = NewSafeGatewayTransport(sock)
	s.transport.OnMessage(func(msg Message) {
		transportMsgCh <- msg
	})
	s.transport.OnDisconnect(func(err error) {
		if err := sock.Close(); err != nil {
			log.Printf("Failed to close socket: %s", err)
		}
		panic(fmt.Errorf("Transport disconnect: %w", err))
	})

	if err := s.transport.Init(); err != nil {
		return fmt.Errorf("init transport: %w", err)
	}

	// Panic if the transport layer doesn't receive ALIVE messages in time
	// If we don't receive an ALIVE message every 5 seconds, we assume the gateway is dead
	// and since we are a child process, we should die too
	aliveCheckInterval := 5 * time.Second
	s.transport.SetAliveTimeout(aliveCheckInterval, func() {
		panic(fmt.Errorf("ALIVE messages not received in %v, gateway is dead", aliveCheckInterval))
	})

	// Connect to gateway after everything is initialized
	err = s.gtw.Connect()
	if err != nil {
		return fmt.Errorf("connect to gateway: %w", err)
	}

	// Register services
	s.registerService(GTW_GET_MARKETS, s.GetMarkets)
	s.registerService(GTW_GET_DEPTH_BOOK, s.GetDepthBook)
	s.registerService(GTW_SUBSCRIBE_MARKETS, s.SubscribeMarkets)
	s.registerService(ACC_GTW_BALANCES, s.AccBalances)
	s.registerService(ACC_GTW_OPEN_ORDERS, s.AccOpenOrders)
	s.registerService(ACC_GTW_SEND_ORDER, s.AccSendOrder)
	s.registerService(ACC_GTW_CANCEL_ORDER, s.AccCancelOrder)

	// Service event loop
	tickSeq := int64(0)
	for {
		select {
		case tick := <-s.gtw.Tick():
			tickSeq += 1
			if err := s.processTick(tickSeq, tick); err != nil {
				return fmt.Errorf("process tick: %w", err)
			}
		case msg := <-transportMsgCh:
			if err := s.processMessage(msg); err != nil {
				return fmt.Errorf("process message: %w", err)
			}
		}
	}
}

func (s *safeGatewayService) registerService(method ServiceMethod, endpoint serviceEndpoint) {
	s.services[method] = endpoint
}

func (s *safeGatewayService) processMessage(msg Message) error {
	fnc, ok := s.services[msg.Method]
	if ok {
		go func() {
			replyMsg := Message{
				Method: REPLY,
				ReqID:  msg.ReqID,
			}

			res, err := fnc(msg)
			if err != nil {
				replyMsg.Err = err
			} else {
				data, err := encodeMessageData(res)
				if err != nil {
					err = fmt.Errorf("encode reply: %w", err)
					replyMsg.Err = err
				} else {
					replyMsg.Data = data
				}
			}

			if err := s.transport.WriteMessage(replyMsg); err != nil {
				log.Printf("Failed to send reply [%s] to message [%s]: %s", replyMsg, msg, err)
			}
		}()
	}

	return nil
}

// Process tick requests and send them to transport
func (s *safeGatewayService) processTick(seq int64, tick gateway.Tick) error {
	tick.Sequence = seq
	tick.ReceivedTimestamp = time.Now()

	data, err := s.tickEncoder(tick)
	if err != nil {
		return fmt.Errorf("encode err: %w", err)
	}

	err = s.tickWriter(data)
	if err != nil {
		return fmt.Errorf("write err: %w", err)
	}

	return nil
}

func (s *safeGatewayService) tickEncoder(tick gateway.Tick) ([]byte, error) {
	data, err := msgpack.Marshal(&tick)
	if err != nil {
		return data, fmt.Errorf("encode tick msgpack: %w", err)
	}

	return data, nil
}

func (s *safeGatewayService) tickWriter(data []byte) error {
	msg := Message{
		Method: GTW_TICK,
		Data:   data,
	}

	if err := s.transport.WriteMessage(msg); err != nil {
		return fmt.Errorf("send tick: %w", err)
	}

	return nil
}

func encodeMessageData(data interface{}) ([]byte, error) {
	return msgpack.Marshal(data)
}

func (s *safeGatewayService) GetMarkets(_ Message) (interface{}, error) {
	return s.gtw.GetMarkets()
}

type GetDepthBookRequest struct {
	Market gateway.Market
	Params gateway.GetDepthParams
}

func (s *safeGatewayService) GetDepthBook(msg Message) (interface{}, error) {
	var request GetDepthBookRequest
	if err := msgpack.Unmarshal(msg.Data, &request); err != nil {
		return nil, fmt.Errorf("unmarshal market: %w", err)
	}
	return s.gtw.GetDepthBook(request.Market, request.Params)
}

func (s *safeGatewayService) SubscribeMarkets(msg Message) (interface{}, error) {
	var markets []gateway.Market
	if err := msgpack.Unmarshal(msg.Data, &markets); err != nil {
		return nil, fmt.Errorf("unmarshal symbols: %w", err)
	}

	if err := s.gtw.SubscribeMarkets(markets); err != nil {
		return nil, err
	}

	return nil, nil
}

func (s *safeGatewayService) AccBalances(_ Message) (interface{}, error) {
	return s.gtw.AccountGateway().Balances()
}

func (s *safeGatewayService) AccOpenOrders(msg Message) (interface{}, error) {
	var market gateway.Market
	if err := msgpack.Unmarshal(msg.Data, &market); err != nil {
		return nil, fmt.Errorf("unmarshal market: %w", err)
	}

	return s.gtw.AccountGateway().OpenOrders(market)
}

func (s *safeGatewayService) AccSendOrder(msg Message) (interface{}, error) {
	var order gateway.Order
	if err := msgpack.Unmarshal(msg.Data, &order); err != nil {
		return nil, fmt.Errorf("unmarshal order: %w", err)
	}

	return s.gtw.AccountGateway().SendOrder(order)
}

func (s *safeGatewayService) AccCancelOrder(msg Message) (interface{}, error) {
	var order gateway.Order
	if err := msgpack.Unmarshal(msg.Data, &order); err != nil {
		return nil, fmt.Errorf("unmarshal order: %w", err)
	}

	err := s.gtw.AccountGateway().CancelOrder(order)
	return nil, err
}
