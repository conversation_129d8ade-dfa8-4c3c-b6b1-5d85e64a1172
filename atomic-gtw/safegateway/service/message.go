package service

import (
	"bytes"
	"errors"
)

var (
	MSG_PART_SEPARATOR = []byte("\t")
)

type Message struct {
	Method ServiceMethod
	ReqID  string
	Err    error
	Data   []byte
}

func (m Message) IsError() bool {
	return m.Err != nil
}

func (m Message) ToBytes() []byte {
	// Pre-allocate a buffer and then copy data into it
	errStr := ""
	if m.Err != nil {
		errStr = m.Err.Error()
	}
	totalLen := len(m.Method) + len(m.ReqID) + len(errStr) + len(m.Data) + 3*len(MSG_PART_SEPARATOR)
	data := make([]byte, 0, totalLen)
	data = append(data, []byte(m.Method)...)
	data = append(data, MSG_PART_SEPARATOR...)
	data = append(data, []byte(m.ReqID)...)
	data = append(data, MSG_PART_SEPARATOR...)
	data = append(data, []byte(errStr)...)
	data = append(data, MSG_PART_SEPARATOR...)
	data = append(data, m.Data...)
	return data
}

func NewMessageFromBytes(data []byte) Message {
	// Split message in two, the first part is the message type, the second part is the message
	parts := bytes.SplitN(data, MSG_PART_SEPARATOR, 4)
	if len(parts) < 1 {
		return Message{}
	}
	var msg = Message{
		Method: ServiceMethod(parts[0]),
	}
	if len(parts) > 1 {
		msg.ReqID = string(parts[1])
	}
	if len(parts) > 2 {
		if errStr := string(parts[2]); errStr != "" {
			msg.Err = errors.New(errStr)
		}
	}
	if len(parts) > 3 {
		msg.Data = parts[3]
	}
	return msg
}
