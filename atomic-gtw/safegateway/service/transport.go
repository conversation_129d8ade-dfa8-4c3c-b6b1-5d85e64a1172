package service

import (
	"context"
	"fmt"
	"math/rand"
	"strconv"
	"sync"
	"time"

	"go.nanomsg.org/mangos/v3"
	_ "go.nanomsg.org/mangos/v3/transport/all"
)

type SafeGatewayTransport interface {
	Init() error
	SetAliveTimeout(time.Duration, func())
	OnMessage(func(Message))
	OnDisconnect(func(error))
	WriteMessage(Message) error
	SendRequest(context.Context, ServiceMethod, []byte) (Message, error)
}

type safeGatewayTransport struct {
	sock           mangos.Socket
	aliveTimer     *time.Ticker
	reqCbs         map[string]chan Message
	reqCbsMutex    sync.RWMutex
	onAliveTimeout func()
	onMessage      func(Message)
	onDisconnect   func(error)
	aliveTimeout   time.Duration
	done           chan struct{}
	closeOnce      sync.Once
}

func NewSafeGatewayTransport(sock mangos.Socket) SafeGatewayTransport {
	return &safeGatewayTransport{
		sock:   sock,
		reqCbs: make(map[string]chan Message),
		done:   make(chan struct{}),
	}
}

func (s *safeGatewayTransport) Init() error {
	// Start core processing goroutines
	go s.aliveMessenger()
	go s.msgProcessor()

	return nil
}

func (s *safeGatewayTransport) SetAliveTimeout(timeout time.Duration, fnc func()) {
	s.aliveTimeout = timeout
	s.aliveTimer = time.NewTicker(timeout)
	s.onAliveTimeout = fnc
	go s.aliveChecker()
}

func (s *safeGatewayTransport) OnMessage(fnc func(Message)) {
	s.onMessage = fnc
}

func (s *safeGatewayTransport) OnDisconnect(fnc func(error)) {
	s.onDisconnect = fnc
}

func (s *safeGatewayTransport) close(err error) {
	s.closeOnce.Do(func() {
		close(s.done)

		// Cancel all pending requests with error
		s.reqCbsMutex.Lock()
		for reqID, ch := range s.reqCbs {
			close(ch)
			delete(s.reqCbs, reqID)
		}
		s.reqCbsMutex.Unlock()

		if s.onDisconnect != nil {
			s.onDisconnect(err)
		}
	})
}

func (s *safeGatewayTransport) aliveMessenger() {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.done:
			return
		case <-ticker.C:
			if err := s.sendAliveMessage(); err != nil {
				s.close(fmt.Errorf("send alive failed: %w", err))
				return
			}
		}
	}
}

func (s *safeGatewayTransport) sendAliveMessage() error {
	msg := Message{
		Method: ALIVE,
	}
	return s.WriteMessage(msg)
}

func (s *safeGatewayTransport) resetAliveTimer() {
	s.aliveTimer.Reset(s.aliveTimeout)
}

func (s *safeGatewayTransport) aliveChecker() {
	for {
		select {
		case <-s.done:
			return
		case <-s.aliveTimer.C:
			if s.onAliveTimeout != nil {
				s.onAliveTimeout()
			}
			return
		}
	}
}

func (s *safeGatewayTransport) msgProcessor() {
	for {
		select {
		case <-s.done:
			return
		default:
			data, err := s.sock.Recv()
			if err != nil {
				s.close(fmt.Errorf("socket receive failed: %w", err))
				return
			}

			msg := NewMessageFromBytes(data)

			if msg.Method == ALIVE && s.aliveTimer != nil {
				s.resetAliveTimer()
			}

			if msg.Method == REPLY {
				s.reqCbsMutex.RLock()
				ch, ok := s.reqCbs[msg.ReqID]
				s.reqCbsMutex.RUnlock()

				if ok {
					// Use select to prevent blocking if channel is full
					select {
					case ch <- msg:
					default:
					}
				}
			}

			if s.onMessage != nil {
				s.onMessage(msg)
			}
		}
	}
}

func (s *safeGatewayTransport) WriteMessage(message Message) error {
	data := message.ToBytes()
	if err := s.sock.Send(data); err != nil {
		s.close(fmt.Errorf("socket send failed: %w", err))
		return err
	}
	return nil
}

func (s *safeGatewayTransport) SendRequest(ctx context.Context, method ServiceMethod, data []byte) (Message, error) {
	// Generate request ID and create message
	reqID := s.genReqID()
	reqMsg := Message{
		Method: method,
		ReqID:  reqID,
		Data:   data,
	}

	// Create reply channel with buffer to prevent blocking
	replyCh := make(chan Message, 1)

	// Register callback before sending to prevent race condition
	s.reqCbsMutex.Lock()
	s.reqCbs[reqID] = replyCh
	s.reqCbsMutex.Unlock()

	// Clean up when we're done
	defer func() {
		s.reqCbsMutex.Lock()
		delete(s.reqCbs, reqID)
		s.reqCbsMutex.Unlock()
	}()

	// Send the request
	if err := s.WriteMessage(reqMsg); err != nil {
		return Message{}, fmt.Errorf("failed to send request: %w", err)
	}

	// Wait for response or timeout
	select {
	case <-ctx.Done():
		return Message{}, ctx.Err()
	case <-s.done:
		return Message{}, fmt.Errorf("transport closed")
	case msg, ok := <-replyCh:
		if !ok {
			return Message{}, fmt.Errorf("reply channel closed")
		}
		return msg, nil
	}
}

func (s *safeGatewayTransport) genReqID() string {
	ts := time.Now().UnixMilli()
	rand := 10000000000 + rand.Intn(89999999999)
	return strconv.FormatInt(ts+int64(rand), 16)
}
