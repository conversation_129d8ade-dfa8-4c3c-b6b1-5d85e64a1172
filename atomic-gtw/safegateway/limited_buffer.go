package safegateway

import "bytes"

type LimitedBuffer struct {
	buf    bytes.Buffer
	maxLen int
}

func NewLimitedBuffer(maxLen int) *LimitedBuffer {
	return &LimitedBuffer{
		maxLen: maxLen,
	}
}

func (lb *LimitedBuffer) Write(p []byte) (n int, err error) {
	n, err = lb.buf.Write(p)
	if err != nil {
		return n, err
	}

	if lb.buf.Len() > lb.maxLen {
		data := lb.buf.Bytes()
		lb.buf.Reset()
		_, err = lb.buf.Write(data[len(data)-lb.maxLen:])
	}

	return n, err
}

func (lb *LimitedBuffer) String() string {
	return lb.buf.String()
}

func (lb *LimitedBuffer) Bytes() []byte {
	return lb.buf.Bytes()
}
