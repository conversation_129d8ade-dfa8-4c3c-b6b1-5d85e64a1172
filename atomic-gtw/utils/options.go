package utils

import "strings"

// Splits a comma-separated string into a slice of strings, trimming whitespace from each part.
// New lines are considered as separators as well.
func SplitOpts(opts string) []string {
	if opts == "" {
		return []string{}
	}
	opts = strings.ReplaceAll(opts, "\n", ",")
	parts := strings.Split(opts, ",")
	for i, part := range parts {
		parts[i] = strings.TrimSpace(part)
	}
	return parts
}
