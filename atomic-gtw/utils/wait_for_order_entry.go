package utils

import (
	"fmt"
	"time"
)

type GetOrderStatus func() (string, error)
type CheckOrderStatusPending func(status string) bool

func WaitForOrderEntry(maxWait, retryInterval time.Duration, getFnc GetOrderStatus, checkFnc CheckOrderStatusPending) error {
	// Wait for order to be open
	startWait := time.Now()
	waitUntil := startWait.Add(maxWait)
	waitErr := error(nil)
	for {
		status, err := getFnc()
		if err != nil {
			if time.Now().After(waitUntil) {
				return fmt.Errorf("timeout waiting for order to be open, last err: %s", err)
			}

			time.Sleep(retryInterval)
			continue
		}

		if !checkFnc(status) {
			break
		}

		if time.Now().After(waitUntil) {
			waitErr = fmt.Errorf("timeout waiting for order to be open")
			break
		}

		time.Sleep(retryInterval)
	}

	return waitErr

}
