package utils

import (
	"context"
	"crypto/tls"
	"math"
	"net"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/backoff"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
)

const (

	// MaxBackoffDelay is a maximum interval between reconnected attempts.
	MaxBackoffDelay = 10 * time.Second
)

const (
	// defaultMaxReceiveMessageSize is the default maximum size of received messages.
	defaultMaxReceiveMessageSize = 128 * 1024 * 1024 // 128MB

	// defaultMaxSendMessageSize is the default maximum size of sent messages.
	defaultMaxSendMessageSize = math.MaxInt32 // 2GB

	// defaultConnectionTimeout is the default connection timeout duration.
	defaultConnectionTimeout = 20 * time.Second // 20 seconds
)

var kacp = keepalive.ClientParameters{
	Time:                30 * time.Second, // Send pings every 10 seconds if there is no activity
	Timeout:             5 * time.Second,  // Wait 1 second for ping ack before considering the connection is dead
	PermitWithoutStream: true,             // Send pings even without active streams
}

// NewGrpcClient creates a client connection to the given target with default options.
func NewGrpcClient(hostName string, creds *tls.Config, apiMiddleware grpc.UnaryClientInterceptor, clientOpts ...grpc.DialOption) (*grpc.ClientConn, error) {
	var grpcSecureOpt credentials.TransportCredentials
	if creds == nil {
		grpcSecureOpt = insecure.NewCredentials()
	} else {
		grpcSecureOpt = credentials.NewTLS(creds)
	}

	// gRPC maintains connection pool inside grpc.ClientConn.
	// This connection pool has auto reconnect feature.
	// If the connection goes down, gRPC will try to reconnect using exponential backoff strategy:
	var cp = grpc.ConnectParams{
		Backoff:           backoff.DefaultConfig,
		MinConnectTimeout: defaultConnectionTimeout,
	}
	cp.Backoff.MaxDelay = MaxBackoffDelay

	dialOptions := []grpc.DialOption{
		grpc.WithDefaultCallOptions(
			grpc.MaxCallRecvMsgSize(defaultMaxReceiveMessageSize),
			grpc.MaxCallSendMsgSize(defaultMaxSendMessageSize),
		),
		grpc.WithConnectParams(cp),
		grpc.WithKeepaliveParams(kacp),
		grpc.WithChainUnaryInterceptor(apiMiddleware),
	}

	dialOptions = append(dialOptions, clientOpts...)

	return BlockingDial(
		context.Background(),
		"tcp",
		hostName,
		grpcSecureOpt,
		dialOptions...,
	)
}

// BlockingDial is a helper method to dial the given address, using optional TLS credentials,
// and blocking until the returned connection is ready. If the given credentials are nil, the
// connection will be insecure (plain-text).
func BlockingDial(ctx context.Context, network, address string, creds credentials.TransportCredentials, opts ...grpc.DialOption) (*grpc.ClientConn, error) {
	if creds == nil {
		creds = insecure.NewCredentials()
	}

	// grpc.Dial doesn't provide any information on permanent connection errors (like
	// TLS handshake failures). So in order to provide good error messages, we need a
	// custom dialer that can provide that info. That means we manage the TLS handshake.
	result := make(chan interface{}, 1)

	writeResult := func(res interface{}) {
		// non-blocking write: we only need the first result
		select {
		case result <- res:
		default:
		}
	}

	// custom credentials and dialer will notify on error via the
	// writeResult function
	creds = &errSignalingCreds{
		TransportCredentials: creds,
		writeResult:          writeResult,
	}

	dialer := func(ctx context.Context, address string) (net.Conn, error) {
		// NB: We *could* handle the TLS handshake ourselves, in the custom
		// dialer (instead of customizing both the dialer and the credentials).
		// But that requires using insecure.NewCredentials() dial transport
		// option (so that the gRPC library doesn't *also* try to do a
		// handshake). And that would mean that the library would send the
		// wrong ":scheme" metaheader to servers: it would send "http" instead
		// of "https" because it is unaware that TLS is actually in use.
		conn, err := (&net.Dialer{}).DialContext(ctx, network, address)
		if err != nil {
			writeResult(err)
		}
		return conn, err
	}

	// Even with grpc.FailOnNonTempDialError, this call will usually timeout in
	// the face of TLS handshake errors. So we can't rely on grpc.WithBlock() to
	// know when we're done. So we run it in a goroutine and then use result
	// channel to either get the connection or fail-fast.
	go func() {
		// We put grpc.FailOnNonTempDialError *before* the explicitly provided
		// options so that it could be overridden.
		opts = append([]grpc.DialOption{grpc.FailOnNonTempDialError(true)}, opts...)
		// But we don't want caller to be able to override these two, so we put
		// them *after* the explicitly provided options.
		opts = append(opts, grpc.WithBlock(), grpc.WithContextDialer(dialer), grpc.WithTransportCredentials(creds))

		conn, err := grpc.NewClient(address, opts...)
		var res interface{}
		if err != nil {
			res = err
		} else {
			res = conn
		}
		writeResult(res)
	}()

	select {
	case res := <-result:
		if conn, ok := res.(*grpc.ClientConn); ok {
			return conn, nil
		}
		return nil, res.(error)
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// errSignalingCreds is a wrapper around a TransportCredentials value, but
// it will use the writeResult function to notify on error.
type errSignalingCreds struct {
	credentials.TransportCredentials
	writeResult func(res interface{})
}

func (c *errSignalingCreds) ClientHandshake(ctx context.Context, addr string, rawConn net.Conn) (net.Conn, credentials.AuthInfo, error) {
	conn, auth, err := c.TransportCredentials.ClientHandshake(ctx, addr, rawConn)
	if err != nil {
		c.writeResult(err)
	}
	return conn, auth, err
}
