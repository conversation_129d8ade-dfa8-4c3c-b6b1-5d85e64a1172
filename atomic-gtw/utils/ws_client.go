package utils

import (
	"bytes"
	"compress/gzip"
	"fmt"
	"io/ioutil"
	"log"
	"math/rand"
	"net/http"
	"net/url"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"golang.org/x/net/proxy"
)

type WsDeflateMode string

const (
	WsDeflateGzip WsDeflateMode = "gzip"
	WsNoDeflate   WsDeflateMode = ""
)

type WsClient struct {
	conn             *websocket.Conn
	connWriterMutex  *sync.Mutex
	subscribers      map[chan []byte]bool
	subscribersMutex *sync.Mutex
	quitCb           sync.Map
	deflateMode      WsDeflateMode
	headers          http.Header
	proxies          []*url.URL
	isClosing        bool
	tag              string
}

func NewWsClient() *WsClient {
	return &WsClient{
		subscribers:      make(map[chan []byte]bool),
		subscribersMutex: &sync.Mutex{},
		connWriterMutex:  &sync.Mutex{},
		quitCb:           sync.Map{},
		deflateMode:      WsDeflateGzip,
	}
}

func (w *WsClient) SetTag(tag string) {
	w.tag = tag
}

func (w *WsClient) tagf(str string) string {
	if w.tag == "" {
		return str
	}
	return "[" + w.tag + "] " + str
}

func (w *WsClient) SetDeflate(m WsDeflateMode) {
	w.deflateMode = m
}

func (w *WsClient) SetHeaders(h http.Header) {
	w.headers = h
}

func (w *WsClient) SetProxies(p []*url.URL) {
	w.proxies = p
}

func (w *WsClient) SetPongHandler(cb func(appData string) error) {
	w.conn.SetPongHandler(cb)
}

func (w *WsClient) SetPingHandler(cb func(appData string) error) {
	w.conn.SetPingHandler(cb)
}

func (w *WsClient) Connect(uri string) error {
	dialer := websocket.Dialer{}

	if len(w.proxies) > 0 {
		randSource := rand.NewSource(time.Now().UnixNano())
		r := rand.New(randSource)
		n := r.Intn(len(w.proxies))
		socksURL := w.proxies[n]

		log.Printf(w.tagf("Ws using proxy: %s"), socksURL)

		proxyDialer, err := proxy.FromURL(socksURL, proxy.Direct)
		if err != nil {
			return fmt.Errorf(w.tagf("proxy from uri %s failed, err: %s"), socksURL, err)
		}

		dialer.NetDial = proxyDialer.Dial
	}

	conn, _, err := dialer.Dial(uri, w.headers)
	if err != nil {
		return err
	}

	w.conn = conn
	go w.messageHandler()

	return nil
}

func (w *WsClient) Close() error {
	w.isClosing = true
	return w.conn.Close()
}

func (w *WsClient) messageHandler() {
	for {
		dataType, data, err := w.conn.ReadMessage()
		if err != nil {
			w.quitCb.Range(func(key any, val any) bool {
				quit := key.(chan bool)
				quit <- true
				return true
			})
			if !w.isClosing {
				panic(fmt.Errorf(w.tagf("failed to read from ws, err: %s"), err))
			}
			return
		}

		if dataType == websocket.BinaryMessage {
			switch w.deflateMode {
			case WsDeflateGzip:
				msgReader := bytes.NewReader(data)
				reader, err := gzip.NewReader(msgReader)
				if err != nil {
					log.Printf(w.tagf("Failed to initialize gzip reader, err %s"), err)
					continue
				}
				decompressedData, err := ioutil.ReadAll(reader)
				if err != nil {
					log.Printf(w.tagf("Failed to read gzip, err %s"), err)
					continue
				}

				reader.Close()
				data = decompressedData
			}
		}

		w.subscribersMutex.Lock()
		for ch := range w.subscribers {
			ch <- data
		}
		w.subscribersMutex.Unlock()
	}
}

func (w *WsClient) WriteMessage(data []byte) error {
	return w.WriteRawMessage(websocket.TextMessage, data)
}

func (w *WsClient) WriteControl(messageType int, data []byte, deadline time.Time) error {
	return w.conn.WriteControl(messageType, data, deadline)
}

func (w *WsClient) WriteRawMessage(dataType int, data []byte) error {
	w.connWriterMutex.Lock()
	defer w.connWriterMutex.Unlock()
	return w.conn.WriteMessage(dataType, data)
}

func (w *WsClient) OnDisconnect(quit chan bool) {
	w.quitCb.Store(quit, struct{}{})
}

func (w *WsClient) SubscribeMessages(ch chan []byte) {
	w.addSubscriber(ch)
}

func (w *WsClient) RemoveSubscriber(ch chan []byte) {
	w.removeSubscriber(ch)
}

func (w *WsClient) addSubscriber(ch chan []byte) {
	w.subscribersMutex.Lock()
	defer w.subscribersMutex.Unlock()

	w.subscribers[ch] = true
}

func (w *WsClient) removeSubscriber(ch chan []byte) {
	w.subscribersMutex.Lock()
	defer w.subscribersMutex.Unlock()

	delete(w.subscribers, ch)
}
