package utils

import (
	"crypto/tls"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"net/url"
	"strings"
	"time"
)

func ProxyToURL(proxy string) (*url.URL, error) {
	proxyURL, err := url.Parse(proxy)
	if err != nil {
		return nil, fmt.Errorf("invalid socks5 proxy uri [%s], url parse error: %s", proxy, err)
	}

	return proxyURL, err
}

func ProxiesToURL(list []string) (proxies []*url.URL, err error) {
	proxies = make([]*url.URL, 0)

	for _, proxy := range list {
		url, err := ProxyToURL(proxy)
		if err != nil {
			return proxies, err
		}

		proxies = append(proxies, url)
	}

	return proxies, nil
}

type proxyTestRes struct {
	proxy *url.URL
	err   error
}

var PROXY_TEST_URLS = []string{
	"https://www.google.com/",
	"https://www.cloudflare.com/",
	"https://www.microsoft.com/en-us",
	"https://www.amazon.com/",
	"https://www.wikipedia.org/",
	"https://www.apple.com/",
	"https://www.youtube.com/",
	"https://github.com/",
	"https://www.facebook.com/",
	"https://www.mozilla.org/en-US",
}

func TestProxies(proxies []*url.URL) ([]*url.URL, map[*url.URL]error) {
	resCh := make(chan proxyTestRes)

	log.Printf("Testing proxies against %d URLs", len(PROXY_TEST_URLS))

	for _, p := range proxies {
		go func(proxy *url.URL) {
			client := http.Client{
				Timeout: 3 * time.Second,
				Transport: &http.Transport{
					TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
					Proxy: func(req *http.Request) (*url.URL, error) {
						return proxy, nil
					},
				},
			}

			err := proxyTestAgainstURL(client, PROXY_TEST_URLS)
			if err != nil {
				resCh <- proxyTestRes{proxy, err}
				return
			}

			resCh <- proxyTestRes{proxy, nil}
		}(p)
	}

	workingProxies := make([]*url.URL, 0)
	errMap := make(map[*url.URL]error)

	if len(proxies) > 0 {
		done := 0
		for res := range resCh {
			if res.err != nil {
				errMap[res.proxy] = res.err
			} else {
				workingProxies = append(workingProxies, res.proxy)
			}

			done += 1
			if done >= len(proxies) {
				break
			}
		}
	}

	return workingProxies, errMap
}

func init() {
	rand.Seed(time.Now().UnixNano())
}

func shuffledIndices(n int) []int {
	indices := make([]int, n)
	for i := 0; i < n; i++ {
		indices[i] = i
	}
	rand.Shuffle(n, func(i, j int) {
		indices[i], indices[j] = indices[j], indices[i]
	})
	return indices
}

func proxyTestAgainstURL(client http.Client, urls []string) error {
	var lastErr error

	shuffled := shuffledIndices(len(urls))
	maxChecks := 5
	checked := 0

	for _, i := range shuffled {
		if checked >= maxChecks {
			break
		}
		checked++

		url := urls[i]
		_, err := client.Get(url)
		if err != nil {
			lastErr = fmt.Errorf("proxy connection error to [%s]: %s", url, err)
			continue
		}

		// If a valid connection is made, return success
		return nil

	}

	return fmt.Errorf("all proxy tests failed after checking %d URLs, last err: %s", checked, lastErr)
}

func splitStringArray(opt string) []string {
	parts := strings.Split(opt, ",")
	list := make([]string, 0)

	for _, val := range parts {
		val = strings.TrimSpace(val)
		if val != "" {
			list = append(list, val)
		}
	}

	return list
}

func ParseProxiesOpt(opt string) (proxies []*url.URL, err error) {
	list := splitStringArray(opt)

	return ProxiesToURL(list)
}

func ParseAndTestProxiesOpt(opt string) ([]*url.URL, error) {
	list := splitStringArray(opt)

	proxies, err := ProxiesToURL(list)
	if err != nil {
		return proxies, err
	}

	log.Printf("Testing [%d] proxies [%s]", len(proxies), proxies)

	working, errMap := TestProxies(proxies)
	if len(errMap) > 0 {
		for p, err := range errMap {
			log.Printf("Proxy [%s] is not working [%s]", p, err)
		}
	}

	minWorkingTolerance := 0.8 // At least 80% of proxies need to be working
	if float64(len(working))/float64(len(proxies)) < minWorkingTolerance {
		return working, fmt.Errorf("too many proxies are not working [%d/%d], need at least %.0f%% of proxies working!", len(working), len(proxies), minWorkingTolerance*100)
	}

	return working, nil
}
