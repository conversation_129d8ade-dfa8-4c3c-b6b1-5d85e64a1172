package utils

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
)

type ctrl struct {
	statusCode int
	response   interface{}
}

type MockResponse struct {
	Endpoint   string
	StatusCode int
	Response   interface{}
}

func (c *ctrl) mockHandler(w http.ResponseWriter, r *http.Request) {
	resp := []byte("{}")

	if c.response != nil {
		respType := reflect.TypeOf(c.response)
		switch respType.Kind() {
		case reflect.String:
			resp = []byte(c.response.(string))
		case reflect.Struct, reflect.Ptr:
			resp, _ = json.Marshal(c.response)
		}
	}

	w.Write<PERSON>eader(c.statusCode)
	w.Write(resp)
}

func HttpMock(requestPath string, statusCode int, response interface{}) *httptest.Server {
	c := &ctrl{statusCode, response}

	handler := http.NewServeMux()

	handler.HandleFunc(requestPath, c.mockHandler)

	return httptest.NewServer(handler)
}
