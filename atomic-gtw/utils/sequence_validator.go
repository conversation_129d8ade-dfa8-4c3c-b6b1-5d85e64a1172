package utils

type SequenceValidator struct {
	seqList          []int64
	seqSet           map[int64]struct{}
	maxMemorizedSeqs int
}

const DefaultMaxMemorizedSeqs = 1000

func NewSequenceValidator(limit int) *SequenceValidator {
	return &SequenceValidator{
		maxMemorizedSeqs: limit,
		seqList:          make([]int64, 0, limit),
		seqSet:           make(map[int64]struct{}),
	}
}

func (sv *SequenceValidator) Reset() {
	sv.seqList = sv.seqList[:0]
	sv.seqSet = make(map[int64]struct{})
}

func (sv *SequenceValidator) SetLimit(limit int) {
	sv.maxMemorizedSeqs = limit
	for len(sv.seqList) > limit {
		delete(sv.seqSet, sv.seqList[0])
		sv.seqList = sv.seqList[1:]
	}
}

// Checks if the sequence number was already processed, if it was, return false
// otherwise, add the sequence number to the memory and return true
func (sv *SequenceValidator) Validate(seq int64) bool {
	if _, exists := sv.seqSet[seq]; exists {
		return false
	}

	sv.seqSet[seq] = struct{}{}
	sv.seqList = append(sv.seqList, seq)

	if len(sv.seqList) > sv.maxMemorizedSeqs {
		delete(sv.seqSet, sv.seqList[0])
		sv.seqList = sv.seqList[1:]
	}

	return true
}
