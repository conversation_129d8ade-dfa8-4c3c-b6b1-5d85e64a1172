package utils

import (
	"log"
	"math/rand"
	"sync"
	"time"
)

// OrderTracking represents the pooling order functionality for the account gateway.
// It provides methods for tracking and updating orders through a ticker.
// The pooling order functionality continuously pools orders until they are filled or no longer tracked.
// It handles periodic updates of order state and sends the updates through the `tickCh` channel.
type OrderTracking struct {
	trackedOrders map[string]struct{}
	lockedOrders  map[string]*sync.Mutex
	updateFnc     OrderTrackingUpdateFnc
	intervals     time.Duration
	mu            sync.RWMutex
}

type OrderTrackingStep string

const (
	OrderTrackingUntrackStep = "untrack"
	OrderTrackingMonitorStep = "monitor"
	OrderTrackingInitialStep = "initial"
)

type OrderTrackingUpdateFnc func(orderID string, step OrderTrackingStep) error

func NewOrderTracking() *OrderTracking {
	return &OrderTracking{
		intervals:     5 * time.Second,
		trackedOrders: make(map[string]struct{}),
		lockedOrders:  make(map[string]*sync.Mutex),
		mu:            sync.RWMutex{},
	}
}

func (o *OrderTracking) SetUpdateIntervals(t time.Duration) {
	o.intervals = t
}

func (o *OrderTracking) SetUpdateFunc(fnc OrderTrackingUpdateFnc) {
	o.updateFnc = fnc
}

// OrderExists checks whether the specified order is still being tracked in the track list.
// It returns true if the order is being tracked, and false otherwise.
func (o *OrderTracking) OrderExists(orderID string) bool {
	o.mu.RLock()
	defer o.mu.RUnlock()
	_, exists := o.trackedOrders[orderID]
	return exists
}

// TrackOrder adds the specified order to the track list.
func (o *OrderTracking) TrackOrder(orderID string) {
	o.mu.Lock()
	defer o.mu.Unlock()
	o.trackedOrders[orderID] = struct{}{}
}

func (o *OrderTracking) UntrackOrder(orderID string) {
	o.mu.Lock()
	defer o.mu.Unlock()
	delete(o.trackedOrders, orderID)
}

// UntrackOrderWithUpdate removes the specified order from the track list and updates it
// if the order is still being tracked.
func (o *OrderTracking) UntrackOrderWithUpdate(orderID string) {
	if o.OrderExists(orderID) {
		o.UntrackOrder(orderID)
		go func() {
			if err := o.executeUpdate(orderID, OrderTrackingUntrackStep); err != nil {
				log.Printf("[OrderTracking] [%s] error updating order [%s] error: %s", OrderTrackingUntrackStep, orderID, err)
			}
		}()
	}
}

func (o *OrderTracking) LockMonitoring(orderID string) {
	o.mu.Lock()
	orderMu, isLocking := o.lockedOrders[orderID]
	if !isLocking {
		orderMu = &sync.Mutex{}
		o.lockedOrders[orderID] = orderMu
	}
	o.mu.Unlock()

	orderMu.Lock()
}

func (o *OrderTracking) UnlockMonitoring(orderID string) {
	o.mu.Lock()
	orderMu, isLocking := o.lockedOrders[orderID]
	if isLocking {
		orderMu.Unlock()
		delete(o.lockedOrders, orderID)
	}
	o.mu.Unlock()
}

func (o *OrderTracking) WaitMonitoringLock(orderID string) {
	o.mu.RLock()
	orderMu, isLocking := o.lockedOrders[orderID]
	o.mu.RUnlock()

	if isLocking {
		orderMu.Lock()
		orderMu.Unlock()
	}
}

func (o *OrderTracking) TrackAndMonitorOrder(orderID string) {
	go func() {
		if err := o.executeUpdate(orderID, OrderTrackingInitialStep); err != nil {
			log.Printf("[OrderTracking] [%s] error updating order [%s] error: %s", OrderTrackingInitialStep, orderID, err)
		}
	}()
	o.MonitorOrder(orderID)
}

// MonitorOrder continuously pools an order for updates until it is filled or no longer tracked.
// It creates a new ticker with a 5-second interval and starts a goroutine to pool the order.
// The goroutine periodically updates the tick for the tracked order until it is no longer being tracked.
// If the order is no longer tracked, the goroutine stops and the method returns.
// The method also applies jitter to the ticker interval to avoid synchronization patterns.
func (o *OrderTracking) MonitorOrder(orderID string) {
	ticker := time.NewTicker(o.intervals)
	o.TrackOrder(orderID)
	go func(orderID string) {
		for range ticker.C {
			o.WaitMonitoringLock(orderID)
			if !o.OrderExists(orderID) {
				ticker.Stop()
				return
			}
			if err := o.executeUpdate(orderID, OrderTrackingMonitorStep); err != nil {
				log.Printf("[OrderTracking] [%s] error updating order [%s] error: %s", OrderTrackingMonitorStep, orderID, err)
			}
			addJitter(o.intervals, ticker)
		}
	}(orderID)
}

func (o *OrderTracking) executeUpdate(orderID string, step OrderTrackingStep) error {
	if o.updateFnc != nil {
		return o.updateFnc(orderID, step)
	}
	return nil
}

// addJitter generate a random jitter between -500ms and +500ms to the request
func addJitter(intervals time.Duration, ticker *time.Ticker) {
	randSource := rand.NewSource(time.Now().UnixNano())
	jitter := time.Duration(rand.New(randSource).Intn(1000)-500) * time.Millisecond
	ticker.Reset(intervals + jitter)
}
