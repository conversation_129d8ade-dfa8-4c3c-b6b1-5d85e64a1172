package utils

import (
	"net/http"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNewOrderTracking(t *testing.T) {
	NewOrderTracking()
}

func TestOrderExists(t *testing.T) {
	tests := []struct {
		purpose      string
		orderID      string
		mockedOrders map[string]struct{}
		mock         func(trackedOrders map[string]struct{}) *OrderTracking
		expectRes    bool
	}{
		{
			purpose:      "Success order not exists",
			orderID:      "1234",
			mockedOrders: map[string]struct{}{"123": {}},
			mock: func(trackedOrders map[string]struct{}) *OrderTracking {
				return &OrderTracking{
					trackedOrders: trackedOrders,
					mu:            sync.RWMutex{},
				}
			},
			expectRes: false,
		},
		{
			purpose:      "Success order exists",
			orderID:      "1234",
			mockedOrders: map[string]struct{}{"1234": {}},
			mock: func(trackedOrders map[string]struct{}) *OrderTracking {
				return &OrderTracking{
					trackedOrders: trackedOrders,
					mu:            sync.RWMutex{},
				}
			},
			expectRes: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			pooling := tt.mock(tt.mockedOrders)

			orderExist := pooling.OrderExists(tt.orderID)
			assert.Equal(t, tt.expectRes, orderExist)
		})
	}
}

func TestTrackOrder(t *testing.T) {
	tests := []struct {
		purpose      string
		orderID      string
		mockedOrders map[string]struct{}
		mock         func(trackedOrders map[string]struct{}) *OrderTracking
		expectRes    bool
	}{
		{
			purpose:      "Success",
			orderID:      "1234",
			mockedOrders: map[string]struct{}{"1234": {}},
			mock: func(trackedOrders map[string]struct{}) *OrderTracking {
				return &OrderTracking{
					trackedOrders: trackedOrders,
					mu:            sync.RWMutex{},
				}
			},
			expectRes: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			pooling := tt.mock(tt.mockedOrders)

			pooling.TrackOrder(tt.orderID)
			assert.Equal(t, tt.expectRes, pooling.OrderExists(tt.orderID))
		})
	}
}

func TestUntrackOrderWithUpdate(t *testing.T) {
	tests := []struct {
		purpose      string
		orderID      string
		mockedOrders map[string]struct{}
		mockHttpRes  MockResponse
		mock         func(trackedOrders map[string]struct{}) *OrderTracking
		expectRes    bool
	}{
		{
			purpose:      "Success",
			orderID:      "1020705308",
			mockedOrders: map[string]struct{}{"1020705308": {}},
			mock: func(trackedOrders map[string]struct{}) *OrderTracking {
				return &OrderTracking{
					trackedOrders: trackedOrders,
					mu:            sync.RWMutex{},
				}
			},
			mockHttpRes: MockResponse{
				Endpoint:   "v1",
				StatusCode: http.StatusOK,
				Response:   "",
			},
			expectRes: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := HttpMock(tt.mockHttpRes.Endpoint, tt.mockHttpRes.StatusCode, tt.mockHttpRes.Response)
			defer mockServer.Close()

			pooling := tt.mock(tt.mockedOrders)

			pooling.UntrackOrderWithUpdate(tt.orderID)
			assert.Equal(t, tt.expectRes, pooling.OrderExists(tt.orderID))

		})
	}
}

func TestTrackAndMonitorOrder(t *testing.T) {
	tests := []struct {
		purpose      string
		orderID      string
		mockedOrders map[string]struct{}
		mockHttpRes  MockResponse
		mock         func(trackedOrders map[string]struct{}) *OrderTracking
		expectRes    bool
	}{
		{
			purpose:      "Success receive order fully filled",
			orderID:      "1020705308",
			mockedOrders: map[string]struct{}{"1020705308": {}},
			mock: func(trackedOrders map[string]struct{}) *OrderTracking {
				return &OrderTracking{
					trackedOrders: trackedOrders,
					mu:            sync.RWMutex{},
				}
			},
			mockHttpRes: MockResponse{
				Endpoint:   "v1",
				StatusCode: http.StatusOK,
				Response:   "",
			},
			expectRes: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.purpose, func(t *testing.T) {
			mockServer := HttpMock(tt.mockHttpRes.Endpoint, tt.mockHttpRes.StatusCode, tt.mockHttpRes.Response)
			defer mockServer.Close()

			pooling := tt.mock(tt.mockedOrders)

			pooling.SetUpdateIntervals(5 * time.Second)

			pooling.TrackAndMonitorOrder(tt.orderID)
			assert.Equal(t, tt.expectRes, pooling.OrderExists(tt.orderID))
		})

	}
}
