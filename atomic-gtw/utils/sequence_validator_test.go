package utils

import (
	"testing"
)

func TestNewSequenceValidator(t *testing.T) {
	sv := NewSequenceValidator(100)

	// Test that a new sequence is accepted
	if !sv.Validate(1) {
		t.<PERSON><PERSON><PERSON>("Expected Validate(1) to return true for a new validator")
	}

	// Test that the same sequence is rejected
	if sv.Validate(1) {
		t.<PERSON>("Expected Validate(1) to return false for a repeated sequence")
	}
}

func TestReset(t *testing.T) {
	sv := NewSequenceValidator(10)
	sv.Validate(1)
	sv.Validate(2)
	sv.Reset()

	// After reset, previously validated sequences should be accepted again
	if !sv.Validate(1) {
		t.<PERSON><PERSON>("Expected Validate(1) to return true after reset")
	}
	if !sv.Validate(2) {
		t.<PERSON><PERSON>("Expected Validate(2) to return true after reset")
	}
}

func TestSetLimit(t *testing.T) {
	sv := NewSequenceValidator(5)
	for i := 1; i <= 5; i++ {
		sv.Validate(int64(i))
	}

	sv.SetLimit(3)

	// The oldest sequences should now be accepted again
	if !sv.Validate(1) {
		t.<PERSON>("Expected Validate(1) to return true after reducing limit")
	}
	if !sv.Validate(2) {
		t.Errorf("Expected Validate(2) to return true after reducing limit")
	}

	// The newest sequences should still be rejected
	if sv.Validate(5) {
		t.Errorf("Expected Validate(5) to return false after reducing limit")
	}
}

func TestValidate(t *testing.T) {
	sv := NewSequenceValidator(3)

	// Test adding new sequences
	if !sv.Validate(1) {
		t.Errorf("Expected Validate(1) to return true")
	}
	if !sv.Validate(2) {
		t.Errorf("Expected Validate(2) to return true")
	}
	if !sv.Validate(3) {
		t.Errorf("Expected Validate(3) to return true")
	}

	// Test duplicate sequence
	if sv.Validate(2) {
		t.Errorf("Expected Validate(2) to return false for duplicate")
	}

	// Test overflow
	if !sv.Validate(4) {
		t.Errorf("Expected Validate(4) to return true")
	}

	// Check if the oldest element was removed due to overflow
	if !sv.Validate(1) {
		t.Errorf("Expected Validate(1) to return true after overflow")
	}
}

func TestValidateLargeSequence(t *testing.T) {
	sv := NewSequenceValidator(1000)

	for i := 1; i <= 2000; i++ {
		sv.Validate(int64(i))
	}

	// Check if the oldest elements were removed
	if !sv.Validate(1000) {
		t.Errorf("Expected Validate(1000) to return true after overflow")
	}

	// Check if the newest elements are still recognized
	if sv.Validate(2000) {
		t.Errorf("Expected Validate(2000) to return false")
	}
}
