package utils

import (
	"crypto/rand"
	"math/big"
	"strconv"
	"sync"
)

const (
	alphanumericChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
)

// GenerateRandomAlphanumeric generates a random alphanumeric string of specified length
func GenerateRandomAlphanumeric(length int) string {
	if length <= 0 {
		return ""
	}

	result := make([]byte, length)
	charSetLen := big.NewInt(int64(len(alphanumericChars)))

	for i := 0; i < length; i++ {
		num, _ := rand.Int(rand.Reader, charSetLen)
		result[i] = alphanumericChars[num.Int64()]
	}

	return string(result)
}

// GenerateRandomInt64 generates a random int64
func GenerateRandomInt64() int64 {
	max := big.NewInt(9223372036854775807) // max int64
	num, _ := rand.Int(rand.<PERSON>, max)
	return num.Int64()
}

// SequentialGenerator generates sequential IDs with an optional prefix
type SequentialGenerator struct {
	prefix  string
	counter int64
	mutex   sync.Mutex
}

// NewSequentialGenerator creates a new sequential ID generator
func NewSequentialGenerator(prefix string) *SequentialGenerator {
	return &SequentialGenerator{
		prefix:  prefix,
		counter: 0,
	}
}

// Next returns the next sequential ID as a string
func (s *SequentialGenerator) Next() string {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.counter++
	return s.prefix + strconv.FormatInt(s.counter, 10)
}

// NextInt64 returns the next sequential ID as an int64
func (s *SequentialGenerator) NextInt64() int64 {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.counter++
	return s.counter
}
