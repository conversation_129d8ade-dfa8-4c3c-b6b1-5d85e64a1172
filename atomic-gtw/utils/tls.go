package utils

import (
	"crypto/tls"
)

var (
	// ModernCiphers provides the highest level of security for modern devices.
	ModernCiphers = []uint16{
		tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305,  // 0xcca9
		tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,    // 0xcca8
		tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256, // 0xc02b
		tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,   // 0xc02f
		tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384, // 0xc02c
		tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,   // 0xc030
		tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256, // 0xc023
		tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,   // 0xc027
	}
)

// NewTLS returns a TLS configuration tuned for performance and security based on
// the recommendations in:
// https://blog.gopheracademy.com/advent-2016/exposing-go-on-the-internet/
//
// AES128 & SHA256 preferred over AES256 & SHA384:
// https://github.com/ssllabs/research/wiki/SSL-and-TLS-Deployment-Best-Practices#31-avoid-too-much-security
func NewTLS() *tls.Config {
	return &tls.Config{
		// Only use curves that have assembly implementations.
		CurvePreferences: []tls.CurveID{
			tls.X25519,
			tls.CurveP256,
		},
		MinVersion:   tls.VersionTLS12,
		CipherSuites: ModernCiphers,
	}
}
