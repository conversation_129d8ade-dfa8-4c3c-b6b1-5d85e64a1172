package utils

import (
	"testing"
)

func TestFloatToStringWithTick(t *testing.T) {
	tests := []struct {
		tick     float64
		value    float64
		expected string
	}{
		{1.0, 10.0, "10"},
		{0.0, 10.0, "10"},
		{0.1, 10.0, "10.0"},
		{0.2, 10.0, "10.0"},
		{0.25, 10.0, "10.00"},
		{0.25, 10.5, "10.50"},
		{0.5, 10.25, "10.0"},
		{0.5, 9.50, "9.5"},
		{0.00001, 0.5, "0.50000"},
		{0.001, 0.525, "0.525"},
		{0.001, 0.6001, "0.600"},
		{0.00125, 0.005, "0.00500"},
		{0.00125, 0.00125, "0.00125"},
		{0.00125, 0.004, "0.00375"},
		{0.00125, 0.0025, "0.00250"},
		{0.0001, 0.6, "0.6000"},
		{0.0001, 0.0006, "0.0006"},
		{0.00001, 2.0, "2.00000"},
		{0.000001, 0.1, "0.100000"},
	}

	for _, test := range tests {
		if result := FloatToStringWithTick(test.value, test.tick); result != test.expected {
			t.Errorf("FloatToStringWithTick of %f with tick %f was incorrect, got: %s, expected: %s.", test.value, test.tick, result, test.expected)
		}
	}
}

func TestFloorToTick(t *testing.T) {
	tests := []struct {
		value    float64
		tick     float64
		expected float64
	}{
		{161457.561752, 0.01, 161457.56},
		{2.005, 0.001, 2.005},
		{2.00499999999, 0.001, 2.005},      // This is a floating point error, we need to round it up
		{2.0049999999999998, 0.001, 2.005}, // After a certain "precision" point we round it up, since it can be a floating point innacuracy error
		{1.114, 0.125, 1.00},
		{1.5, 0.125, 1.5},
		{0.00000000025, 0.0000000001, 0.0000000002},
		{0.0000000005, 0.0000000001, 0.0000000005},
		{0.000000000003, 0.000000000001, 0.000000000003},
		{0.0001, 0.1, 0.0},
		{0, 10, 0},
		{25, 50, 0},
		{49.9999999999, 50, 50},
		{50.000001, 50, 50},
		{-50, 50, -50},
		{-25, 50, -0},
	}

	for _, test := range tests {
		if result := FloorToTick(test.value, test.tick); result != test.expected {
			t.Errorf("FloorToTick of %f with tick %f was incorrect, got: %f, expected: %f.", test.value, test.tick, result, test.expected)
		}
	}
}

func TestTickPrecision(t *testing.T) {
	tests := []struct {
		tick     float64
		expected int
	}{
		{10, 0},
		{1.0, 0},
		{0.0, 0},
		{0.1, 1},
		{0.2, 1},
		{0.25, 2},
		{0.0125, 4},
		{0.0001, 4},
		{0.000150, 5},
		{0.125, 3},
		{0.05, 2},
		{1.5, 1},
		{10.5, 1},
		{10.25, 2},
		{0.000000000001, 12},
		{10.00125, 5},
		{100000000000.005, 3},
	}

	for _, test := range tests {
		if result := TickPrecision(test.tick); result != test.expected {
			t.Errorf("TickPrecision of tick %f was incorrect, got: %d, expected: %d.", test.tick, result, test.expected)
		}
	}
}
