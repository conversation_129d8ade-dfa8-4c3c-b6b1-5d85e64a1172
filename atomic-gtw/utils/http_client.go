package utils

import (
	"crypto/tls"
	"math/rand"
	"net/http"
	"net/url"
	"path"
	"sync"
	"time"
)

type HttpClient struct {
	proxies     []*url.URL
	proxyCursor int64
	lock        *sync.RWMutex
	httpClient  http.Client
	transport   *http.Transport
}

func NewHttpClient() *HttpClient {
	transport := &http.Transport{
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
		MaxIdleConnsPerHost: 2,
	}

	return &HttpClient{
		proxies:   make([]*url.URL, 0),
		lock:      &sync.RWMutex{},
		transport: transport,
		httpClient: http.Client{
			Transport: transport,
			Timeout:   10 * time.Second,
			// Dont follow redirects
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				return http.ErrUseLastResponse
			},
		},
	}
}

func (c *HttpClient) SetTimeout(timeout time.Duration) {
	c.httpClient.Timeout = timeout
}

func (c *HttpClient) getNextProxy(req *http.Request) (*url.URL, error) {
	c.lock.RLock()
	defer c.lock.RUnlock()

	// If proxyCursor is at start, we must randomize the initial proxy
	// to avoid all requests going to the same proxy, at the first request.
	if c.proxyCursor == 0 {
		c.proxyCursor = int64(rand.Intn(len(c.proxies)))
	}

	c.proxyCursor += 1

	proxy := c.proxies[c.proxyCursor%int64(len(c.proxies))]

	return proxy, nil
}

func (c *HttpClient) UseProxies(proxies []*url.URL) {
	c.lock.Lock()
	defer c.lock.Unlock()

	c.proxies = append(c.proxies, proxies...)

	if len(c.proxies) > 0 {
		c.transport.Proxy = c.getNextProxy
	}
}

func (c *HttpClient) SendRequest(req *http.Request) (*http.Response, error) {
	return c.httpClient.Do(req)
}

func (c *HttpClient) ParseURLRequest(baseURL, endpoint string) (*url.URL, error) {
	u, err := url.Parse(baseURL)
	if err != nil {
		return &url.URL{}, err
	}
	urlParsed := u.ResolveReference(&url.URL{Path: path.Join(endpoint)})
	return urlParsed, nil
}
