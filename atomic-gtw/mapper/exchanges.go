package mapper

import (
	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/integration/arbitrumsupply"
	"github.com/herenow/atomic-gtw/integration/azbit"
	"github.com/herenow/atomic-gtw/integration/backpack"
	"github.com/herenow/atomic-gtw/integration/biconomy"
	"github.com/herenow/atomic-gtw/integration/bigone"
	"github.com/herenow/atomic-gtw/integration/binance"
	"github.com/herenow/atomic-gtw/integration/binancefutures"
	"github.com/herenow/atomic-gtw/integration/bit"
	"github.com/herenow/atomic-gtw/integration/bitfinex"
	"github.com/herenow/atomic-gtw/integration/bitget"
	"github.com/herenow/atomic-gtw/integration/bitmart"
	"github.com/herenow/atomic-gtw/integration/bitmex"
	"github.com/herenow/atomic-gtw/integration/bitrue"
	"github.com/herenow/atomic-gtw/integration/bitso"
	"github.com/herenow/atomic-gtw/integration/bitstamp"
	"github.com/herenow/atomic-gtw/integration/bitypreco"
	"github.com/herenow/atomic-gtw/integration/bybit"
	"github.com/herenow/atomic-gtw/integration/chiliz"
	"github.com/herenow/atomic-gtw/integration/coinbase"
	"github.com/herenow/atomic-gtw/integration/coinsbit"
	"github.com/herenow/atomic-gtw/integration/coinsph"
	"github.com/herenow/atomic-gtw/integration/coinstore"
	"github.com/herenow/atomic-gtw/integration/cointiger"
	"github.com/herenow/atomic-gtw/integration/cryptocom"
	"github.com/herenow/atomic-gtw/integration/cryptomkt"
	"github.com/herenow/atomic-gtw/integration/currencylayer"
	"github.com/herenow/atomic-gtw/integration/deribit"
	"github.com/herenow/atomic-gtw/integration/digifinex"
	"github.com/herenow/atomic-gtw/integration/digitra"
	"github.com/herenow/atomic-gtw/integration/fastforex"
	"github.com/herenow/atomic-gtw/integration/fmfw"
	"github.com/herenow/atomic-gtw/integration/foxbit"
	"github.com/herenow/atomic-gtw/integration/gateio"
	"github.com/herenow/atomic-gtw/integration/gemini"
	"github.com/herenow/atomic-gtw/integration/hotcoin"
	"github.com/herenow/atomic-gtw/integration/huobi"
	"github.com/herenow/atomic-gtw/integration/hyperliquid"
	"github.com/herenow/atomic-gtw/integration/klever"
	"github.com/herenow/atomic-gtw/integration/kraken"
	"github.com/herenow/atomic-gtw/integration/kucoin"
	"github.com/herenow/atomic-gtw/integration/latoken"
	"github.com/herenow/atomic-gtw/integration/lbank"
	"github.com/herenow/atomic-gtw/integration/mercadobitcoin"
	"github.com/herenow/atomic-gtw/integration/mexc"
	"github.com/herenow/atomic-gtw/integration/novadax"
	"github.com/herenow/atomic-gtw/integration/okx"
	"github.com/herenow/atomic-gtw/integration/onetrading"
	"github.com/herenow/atomic-gtw/integration/p2pb2b"
	"github.com/herenow/atomic-gtw/integration/pancakeswapv3"
	"github.com/herenow/atomic-gtw/integration/probit"
	"github.com/herenow/atomic-gtw/integration/ripio"
	"github.com/herenow/atomic-gtw/integration/temp_uniswapv4_evawbtc"
	"github.com/herenow/atomic-gtw/integration/trubit"
	"github.com/herenow/atomic-gtw/integration/uniswap/v3"
	"github.com/herenow/atomic-gtw/integration/uniswap/v4"
	"github.com/herenow/atomic-gtw/integration/upbit"
	"github.com/herenow/atomic-gtw/integration/valr"
	"github.com/herenow/atomic-gtw/integration/whitebit"
	"github.com/herenow/atomic-gtw/integration/xt"
)

func init() {
	gateway.RegisterExchange(latoken.Exchange, latoken.NewGateway)
	gateway.RegisterExchange(huobi.Exchange, huobi.NewGateway)
	gateway.RegisterExchange(bitfinex.Exchange, bitfinex.NewGateway)
	gateway.RegisterExchange(binance.Exchange, binance.NewGateway)
	gateway.RegisterExchange(binancefutures.Exchange, binancefutures.NewGateway)
	gateway.RegisterExchange(ripio.Exchange, ripio.NewGateway)
	gateway.RegisterExchange(currencylayer.Exchange, currencylayer.NewGateway)
	gateway.RegisterExchange(bitmex.Exchange, bitmex.NewGateway)
	gateway.RegisterExchange(upbit.Exchange, upbit.NewGateway)
	gateway.RegisterExchange(mercadobitcoin.Exchange, mercadobitcoin.NewGateway)
	gateway.RegisterExchange(probit.Exchange, probit.NewGateway)
	gateway.RegisterExchange(p2pb2b.Exchange, p2pb2b.NewGateway)
	gateway.RegisterExchange(chiliz.Exchange, chiliz.NewGateway)
	gateway.RegisterExchange(gateio.Exchange, gateio.NewGateway)
	gateway.RegisterExchange(bitso.Exchange, bitso.NewGateway)
	gateway.RegisterExchange(foxbit.Exchange, foxbit.NewGateway)
	gateway.RegisterExchange(novadax.Exchange, novadax.NewGateway)
	gateway.RegisterExchange(gemini.Exchange, gemini.NewGateway)
	gateway.RegisterExchange(mexc.Exchange, mexc.NewGateway)
	gateway.RegisterExchange(digifinex.Exchange, digifinex.NewGateway)
	gateway.RegisterExchange(bigone.Exchange, bigone.NewGateway)
	gateway.RegisterExchange(lbank.Exchange, lbank.NewGateway)
	gateway.RegisterExchange(bitmart.Exchange, bitmart.NewGateway)
	gateway.RegisterExchange(kucoin.Exchange, kucoin.NewGateway)
	gateway.RegisterExchange(xt.Exchange, xt.NewGateway)
	gateway.RegisterExchange(bitrue.Exchange, bitrue.NewGateway)
	gateway.RegisterExchange(coinstore.Exchange, coinstore.NewGateway)
	gateway.RegisterExchange(cointiger.Exchange, cointiger.NewGateway)
	gateway.RegisterExchange(digitra.Exchange, digitra.NewGateway)
	gateway.RegisterExchange(fmfw.Exchange, fmfw.NewGateway)
	gateway.RegisterExchange(hotcoin.Exchange, hotcoin.NewGateway)
	gateway.RegisterExchange(biconomy.Exchange, biconomy.NewGateway)
	gateway.RegisterExchange(whitebit.Exchange, whitebit.NewGateway)
	gateway.RegisterExchange(fastforex.Exchange, fastforex.NewGateway)
	gateway.RegisterExchange(coinbase.Exchange, coinbase.NewGateway)
	gateway.RegisterExchange(deribit.Exchange, deribit.NewGateway)
	gateway.RegisterExchange(cryptocom.Exchange, cryptocom.NewGateway)
	gateway.RegisterExchange(kraken.Exchange, kraken.NewGateway)
	gateway.RegisterExchange(bitstamp.Exchange, bitstamp.NewGateway)
	gateway.RegisterExchange(okx.Exchange, okx.NewGateway)
	gateway.RegisterExchange(bybit.Exchange, bybit.NewGateway)
	gateway.RegisterExchange(bitget.Exchange, bitget.NewGateway)
	gateway.RegisterExchange(bit.Exchange, bit.NewGateway)
	gateway.RegisterExchange(coinsbit.Exchange, coinsbit.NewGateway)
	gateway.RegisterExchange(azbit.Exchange, azbit.NewGateway)
	gateway.RegisterExchange(bitypreco.Exchange, bitypreco.NewGateway)
	gateway.RegisterExchange(coinsph.Exchange, coinsph.NewGateway)
	gateway.RegisterExchange(onetrading.Exchange, onetrading.NewGateway)
	gateway.RegisterExchange(cryptomkt.Exchange, cryptomkt.NewGateway)
	gateway.RegisterExchange(klever.Exchange, klever.NewGateway)
	gateway.RegisterExchange(hyperliquid.Exchange, hyperliquid.NewGateway)
	gateway.RegisterExchange(trubit.Exchange, trubit.NewGateway)
	gateway.RegisterExchange(arbitrumsupply.Exchange, arbitrumsupply.NewGateway)
	gateway.RegisterExchange(valr.Exchange, valr.NewGateway)
	gateway.RegisterExchange(backpack.Exchange, backpack.NewGateway)
	gateway.RegisterExchange(temp_uniswapv4_evawbtc.Exchange, temp_uniswapv4_evawbtc.NewGateway)

	// For DEX we need to register the exchange with the chain id
	for _, chainID := range gateway.Chains {
		gateway.RegisterExchange(uniswapv3.NewExchange(chainID), uniswapv3.NewGateway(chainID))
		gateway.RegisterExchange(uniswapv4.NewExchange(chainID), uniswapv4.NewGateway(chainID))
		gateway.RegisterExchange(pancakeswapv3.NewExchange(chainID), pancakeswapv3.NewGateway(chainID))
	}
}
