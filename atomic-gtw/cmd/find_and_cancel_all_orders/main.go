package main

import (
	"fmt"
	"log"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	_ "github.com/herenow/atomic-gtw/mapper"
	"github.com/jessevdk/go-flags"
	_ "github.com/lib/pq"
)

type Options struct {
	gateway.Options
	Exchange    string `long:"exchange" description:"Exchange to look for arbitrages"`
	Concurrency int    `long:"concurrency" description:"Number of concurrent workers to use"`
	DontCancel  bool   `long:"dontCancel" description:"Don't cancel orders"`
	Symbol      string `long:"symbol" description:"Symbol to look for"`
}

var opts Options

var optsParser = flags.NewParser(&opts, flags.Default)

func main() {
	// Parse flags
	if _, err := optsParser.Parse(); err != nil {
		if flagsErr, ok := err.(*flags.Error); ok && flagsErr.Type != flags.ErrHelp {
			fmt.Printf("Failed to parse args, err:\n%s\n", err)
		}
		os.Exit(0)
	}

	if opts.Exchange == "" {
		log.Fatal("You must pass an --exchange")
	}

	exchange, ok := gateway.ExchangeBySymbol(opts.Exchange)
	if !ok {
		log.Printf("Failed to find exchange \"%s\"", opts.Exchange)
		log.Printf("Available exchange are:")
		for _, exchange := range gateway.Exchanges {
			log.Printf("- %s", exchange.Symbol())
		}
		return
	}

	options := gateway.Options{
		ApiKey:        opts.ApiKey,
		ApiSecret:     opts.ApiSecret,
		ApiMemo:       opts.ApiMemo,
		ApiPassphrase: opts.ApiPassphrase,
		Token:         opts.Token,
		Cookie:        opts.Cookie,
		UserID:        opts.UserID,
		UserAgent:     opts.UserAgent,
		Staging:       opts.Staging,
		Verbose:       opts.Verbose,
		FxSource:      opts.FxSource,
		Proxies:       opts.Proxies,
	}
	gtw, ok := gateway.NewByExchange(exchange, options)
	if !ok {
		log.Printf("Failed to find gateway for exchange %s", exchange)
		return
	}

	// Connect gtw
	err := gtw.Connect()
	if err != nil {
		log.Fatal(err)
	}

	log.Printf("Connected to exchange...")

	// Available balance
	balances, err := gtw.AccountGateway().Balances()
	if err != nil {
		log.Printf("Failed to gtw.AccountGateway().Balances(), err: %s", err)
	} else {
		log.Printf("Returned balances: %d", len(balances))
		log.Printf("Printing balances w/ total > 0")
		for _, b := range balances {
			if b.Total > 0 {
				log.Printf("%s : Total %f : Avail %f", b.Asset, b.Total, b.Available)
			}
		}
	}

	// Find all open orders on all markets
	markets, err := gtw.GetMarkets()
	if err != nil {
		log.Fatalf("Failed to fetch markets, err: %s", err)
	}
	log.Printf("Found %d markets", len(markets))
	concurrency := opts.Concurrency
	if concurrency <= 0 {
		concurrency = 1
	}
	workCh := make(chan gateway.Market)

	log.Printf("Finding and closing all open orders, on [%d] distint markets, concurrency [%d]...", len(markets), concurrency)
	log.Printf("This may take a while...")

	var totalProcessed uint64
	var wg sync.WaitGroup
	for workerID := 0; workerID < concurrency; workerID++ {
		go func(workerID int) {
			for market := range workCh {
				wg.Add(1)

				orders, err := gtw.AccountGateway().OpenOrders(market)
				if err != nil {
					log.Printf("Failed to fetch open orders, err: %s", err)
				}

				if len(orders) > 0 {
					var totalBalance float64
					for _, order := range orders {
						totalBalance += order.Amount * order.Price
					}

					var orderIDs []string
					for _, order := range orders {
						orderIDs = append(orderIDs, order.ID)
					}

					log.Printf("Found %d %s open orders on %s, total balance consumed: %f %s, cancelling open orders", len(orders), orderIDs, market, totalBalance, market.Pair.Quote)

					if !opts.DontCancel {
						for _, order := range orders {
							err := gtw.AccountGateway().CancelOrder(order)
							if err != nil {
								log.Printf("Failed to cancel order %s, err: %s", order.ID, err)
							} else {
								log.Printf("Cancelled order %s", order.ID)
							}
						}
					}
				}

				atomic.AddUint64(&totalProcessed, 1)
				total := atomic.LoadUint64(&totalProcessed)

				if total%10 == 0 {
					log.Printf("Processed [%d/%d] markets", total, len(markets))
				}

				wg.Done()
			}
		}(workerID)
	}

	// Dispatch jobs
	for _, market := range markets {
		if (opts.Symbol != "" && market.Symbol == opts.Symbol) || opts.Symbol == "" {
			workCh <- market
		}
	}

	log.Printf("Waiting for workers to finish last jobs...")
	time.Sleep(1 * time.Second)
	wg.Wait()
	log.Printf("Done!")
}
