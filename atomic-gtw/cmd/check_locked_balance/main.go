package main

import (
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	_ "github.com/herenow/atomic-gtw/mapper"
	"github.com/jessevdk/go-flags"
)

type Options struct {
	gateway.Options
	Exchange    string `long:"exchange" description:"Exchange to look for arbitrages"`
	Assets      string `long:"assets" description:"Assets to look for, comma separated"`
	Concurrency int    `long:"concurrency" description:"Number of concurrent workers to use"`
}

var opts Options

var optsParser = flags.NewParser(&opts, flags.Default)

func main() {
	// Parse flags
	if _, err := optsParser.Parse(); err != nil {
		if flagsErr, ok := err.(*flags.Error); ok && flagsErr.Type != flags.ErrHelp {
			fmt.Printf("Failed to parse args, err:\n%s\n", err)
		}
		os.Exit(0)
	}

	var checkAssets []string
	if opts.Assets != "" {
		checkAssets = strings.Split(opts.Assets, ",")
	}

	// Find exchange and gateway
	exchange, ok := gateway.ExchangeBySymbol(opts.Exchange)
	if !ok {
		log.Printf("Failed to find exchange \"%s\"", opts.Exchange)
		log.Printf("Available exchange are:")
		for _, exchange := range gateway.Exchanges {
			log.Printf("- %s", exchange.Symbol())
		}
		return
	}

	options := gateway.Options{
		ApiKey:        opts.ApiKey,
		ApiSecret:     opts.ApiSecret,
		ApiMemo:       opts.ApiMemo,
		ApiPassphrase: opts.ApiPassphrase,
		Token:         opts.Token,
		Cookie:        opts.Cookie,
		UserID:        opts.UserID,
		UserAgent:     opts.UserAgent,
		Staging:       opts.Staging,
		Verbose:       opts.Verbose,
		FxSource:      opts.FxSource,
		Proxies:       opts.Proxies,
	}
	gtw, ok := gateway.NewByExchange(exchange, options)
	if !ok {
		log.Printf("Failed to find gateway for exchange %s", exchange)
		return
	}

	// Connect gateway
	err := gtw.Connect()
	if err != nil {
		log.Fatal(err)
	}
	defer gtw.Close()

	// Get account gateway and balances
	agw := gtw.AccountGateway()
	balances, err := agw.Balances()
	if err != nil {
		log.Printf("Failed to fetch balances, err: %s", err)
		return
	}

	// Find balance of specified asset
	totalLockedMap := make(map[string]float64)

	// Find open orders on all markets
	markets, err := gtw.GetMarkets()
	if err != nil {
		log.Fatalf("Failed to fetch markets, err: %s", err)
	}
	concurrency := opts.Concurrency
	if concurrency <= 0 {
		concurrency = 1
	}
	workCh := make(chan gateway.Market)

	log.Printf("Finding and summing locked balance on all open orders, on [%d] distinct markets, concurrency [%d]...", len(markets), concurrency)

	var (
		openOrderLock sync.Mutex
		openOrders    []gateway.Order
		totalLocked   float64
		wg            sync.WaitGroup
	)

	// Dispatch workers to find open orders on each market
	for workerID := 0; workerID < concurrency; workerID++ {
		go func(workerID int) {
			for market := range workCh {
				wg.Add(1)

				orders, err := gtw.AccountGateway().OpenOrders(market)
				if err != nil {
					log.Printf("Failed to fetch open orders on %s, err: %s", market, err)
					wg.Done()
					continue
				}

				if len(orders) > 0 {
					openOrderLock.Lock()
					openOrders = append(openOrders, orders...)

					var majorLocked, minorLocked float64
					for _, order := range orders {
						if order.Side == gateway.Bid {
							minorLocked += order.Amount * order.Price
						} else if order.Side == gateway.Ask {
							majorLocked += order.Amount
						} else {
							log.Printf("%s unknown order side \"%s\" for order %s", market.Exchange, order.Side, order)
						}
					}

					totalLockedMap[market.Pair.Base] += majorLocked
					totalLockedMap[market.Pair.Quote] += minorLocked

					//if majorLocked > 0 {
					log.Printf("Found %d open orders on %s, total locked balance: %f %s, total locked money: %f %s", len(orders), market, majorLocked, market.Pair.Base, minorLocked, market.Pair.Quote)
					for _, order := range orders {
						log.Printf("\tOrder: %s", order)
					}
					//}

					openOrderLock.Unlock()
				}

				wg.Done()
			}
		}(workerID)
	}

	// Dispatch jobs to workers
	for _, market := range markets {
		if len(checkAssets) == 0 || marketInAssets(market, checkAssets) {
			workCh <- market
		}
	}

	time.Sleep(1 * time.Second)
	wg.Wait()

	// Check total locked balance against current balances
	for _, balance := range balances {
		if len(checkAssets) == 0 || balanceInAssets(balance, checkAssets) {
			actualLocked := balance.Total - balance.Available
			totalLocked, ok = totalLockedMap[balance.Asset]
			if !ok && actualLocked > 0 {
				log.Printf("Found %f locked balance on %s, but no open orders found on any market", actualLocked, balance.Asset)
			} else if actualLocked > (totalLocked * 1.01) { // 1% tolerance
				log.Printf("Found %f locked balance on %s, but only %f locked balance found on open orders, diff %f", actualLocked, balance.Asset, totalLocked, actualLocked-totalLocked)
			}
		}
	}
}

func marketInAssets(market gateway.Market, assets []string) bool {
	for _, asset := range assets {
		if market.Pair.Base == asset || market.Pair.Quote == asset {
			return true
		}
	}
	return false
}

func balanceInAssets(balance gateway.Balance, assets []string) bool {
	for _, asset := range assets {
		if balance.Asset == asset {
			return true
		}
	}
	return false
}
