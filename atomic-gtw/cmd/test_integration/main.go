package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	_ "github.com/herenow/atomic-gtw/mapper"
	"github.com/herenow/atomic-gtw/safegateway"
	"github.com/herenow/atomic-gtw/utils"
	"github.com/jessevdk/go-flags"
)

type Options struct {
	gateway.Options
	SubscribeMarkets bool          `long:"subscribeMarkets" description:"Subscribe to market data updates"`
	CancelOpenOrders bool          `long:"cancelOpenOrders" description:"Cancel open orders"`
	SafeGateway      bool          `long:"safeGateway" description:"Wrap the gateway with a safe gateway"`
	Exchange         string        `long:"exchange" description:"Exchange to look for arbitrages"`
	Symbol           string        `long:"symbol" description:"Test order market symbol symbol"`
	Price            float64       `long:"price" description:"Test order price"`
	Amount           float64       `long:"amount" description:"Test order amount"`
	WaitBeforeOpen   time.Duration `long:"waitBeforeOpen" description:"Wait before open order" default:"3s"`
	WaitBeforeCancel time.Duration `long:"waitBeforeCancel" default:"5s"`
	WaitBeforeExit   time.Duration `long:"waitBeforeExit" default:"5s"`
	Buy              bool          `long:"buy" description:"Buy test order"`
	Sell             bool          `long:"sell" description:"Sell test order"`
	PostOnly         bool          `long:"postOnly" description:"Send the order as postOnly"`
	PgDB             string        `long:"pgDB" description:"Postgres database uri"`
	BotID            string        `long:"botID" description:"Bot identification on DB"`
	Proxies          string        `long:"proxies" description:"Comma separated list of proxy servers to use"`
}

var opts Options

var optsParser = flags.NewParser(&opts, flags.Default)

func main() {
	// Parse flags
	if _, err := optsParser.Parse(); err != nil {
		if flagsErr, ok := err.(*flags.Error); ok && flagsErr.Type != flags.ErrHelp {
			fmt.Printf("Failed to parse args, err:\n%s\n", err)
		}
		os.Exit(0)
	}

	if opts.Exchange == "" || opts.Symbol == "" || opts.Price == 0 || opts.Amount == 0 {
		log.Fatal("You must pass an --exchange --symbol --price --amount")
	}

	if !opts.Buy && !opts.Sell {
		log.Fatal("You must pass a --buy or --sell flag")
	}

	if opts.BotID == "" {
		opts.BotID = "test_integration"
	}

	exchange, ok := gateway.ExchangeBySymbol(opts.Exchange)
	if !ok {
		log.Printf("Failed to find exchange \"%s\"", opts.Exchange)
		log.Printf("Available exchange are:")
		for _, exchange := range gateway.Exchanges {
			log.Printf("- %s", exchange.Symbol())
		}
		return
	}

	proxies, err := utils.ParseAndTestProxiesOpt(opts.Proxies)
	if err != nil {
		log.Fatalf("invalid --proxies opt, err: %s", err)
	}

	options := gateway.Options{
		ApiKey:               opts.ApiKey,
		ApiSecret:            opts.ApiSecret,
		ApiMemo:              opts.ApiMemo,
		ApiPassphrase:        opts.ApiPassphrase,
		ApiVersion:           opts.ApiVersion,
		Token:                opts.Token,
		Cookie:               opts.Cookie,
		UserID:               opts.UserID,
		UserAgent:            opts.UserAgent,
		Staging:              opts.Staging,
		Verbose:              opts.Verbose,
		FxSource:             opts.FxSource,
		PollMarketData:       opts.PollMarketData,
		PollAccountData:      opts.PollAccountData,
		WaitForOrderEntry:    opts.WaitForOrderEntry,
		MaxWaitForOrderEntry: opts.MaxWaitForOrderEntry,
		APIBaseURL:           opts.APIBaseURL,
		WSBaseURL:            opts.WSBaseURL,
		AccountWSBaseURL:     opts.AccountWSBaseURL,
		Proxies:              proxies,
	}
	gtw, ok := gateway.NewByExchange(exchange, options)
	if !ok {
		log.Printf("Failed to find gateway for exchange %s", exchange)
		return
	}

	// Wrap gtw with safe gateway if requested
	if opts.SafeGateway {
		safeOpts := safegateway.SafeGatewayOptions{
			OnDisconnect: func(err error) {
				log.Printf("Safe gateway disconnected: %s", err)
			},
			OnConnect: func() {
				log.Printf("Safe gateway connected")
			},
		}

		gtw = safegateway.NewSafeGateway(
			exchange,
			options,
			safeOpts,
		)
	}

	// Connect gtw
	err = gtw.Connect()
	if err != nil {
		log.Fatal(err)
	}

	log.Printf("Connected to exchange...")

	// Find markets
	markets, err := gtw.GetMarkets()
	if err != nil {
		log.Fatalf("Failed to fetch markets, err: %s", err)
	}
	market, ok := gateway.MarketBySymbol(markets, opts.Symbol)
	if !ok {
		log.Printf("Available markets at %s: %s", gtw.Exchange(), gateway.MarketsToSymbols(markets))
		log.Fatalf("Failed to locate symbol %s", opts.Symbol)
	}

	log.Printf("Available markets:\n%+v", markets)
	log.Printf("Total markets: %d", len(markets))

	// Print orderbook from REST
	res, err := gtw.GetDepthBook(market, gateway.DefaultDepthParams)
	if err != nil {
		log.Printf("Failed to get depth book, err: %s", err)
	} else {
		// Prints top 10 bids and asks
		log.Printf("Depth book top 10 bids/asks:")
		for i := 0; i < 10; i++ {
			str := ""
			if i < len(res.Bids) {
				str += fmt.Sprintf("%f %f [bid]", res.Bids[i].Price, res.Bids[i].Amount)
			}
			if i < len(res.Asks) {
				if str != "" {
					str += " | "
				}
				str += fmt.Sprintf("%f %f [ask]", res.Asks[i].Price, res.Asks[i].Amount)
			}
			if str != "" {
				fmt.Println(str)
			}
		}
	}

	// Print balances
	balances, err := gtw.AccountGateway().Balances()
	if err != nil {
		log.Printf("Failed to gtw.AccountGateway().Balances(), err: %s", err)
	} else {
		log.Printf("Returned balances: %d", len(balances))
		log.Printf("Printing balances w/ total > 0")
		for _, b := range balances {
			if b.Total > 0 {
				log.Printf("%s : Total %f : Avail %f", b.Asset, b.Total, b.Available)
			}
		}
	}

	// Subscribe to market updates
	if opts.SubscribeMarkets {
		err = gtw.SubscribeMarkets([]gateway.Market{market})
		if err != nil {
			log.Printf("Failed to SubscribeMarkets(), err: %s", err)
		}
	}

	// Tick processor
	go func() {
		for tick := range gtw.Tick() {
			log.Printf("Received tick update:\n%s", tick)
		}
	}()

	orders, err := gtw.AccountGateway().OpenOrders(market)
	if err != nil {
		log.Printf("Failed to fetch open orders, err: %s", err)
	}

	log.Printf("Open orders")
	for _, order := range orders {
		log.Printf("Order: %s", order)

		if opts.CancelOpenOrders {
			log.Printf("Cancelling order [%s]...", order)
			err := gtw.AccountGateway().CancelOrder(order)
			if err != nil {
				log.Printf("Failed to cancel order [%s], err: %s", order, err)
			}
		}
	}

	var side gateway.Side
	if opts.Buy {
		side = gateway.Bid
	} else if opts.Sell {
		side = gateway.Ask
	}

	// Try to place order
	order := gateway.Order{
		Market:        market,
		ClientOrderID: fmt.Sprintf("test-%d", time.Now().UnixNano()),
		Side:          side,
		Price:         opts.Price,
		Amount:        opts.Amount,
	}

	if opts.PostOnly {
		order.PostOnly = true
	}

	log.Printf("Opening order %s in %v...", order, opts.WaitBeforeOpen)

	time.Sleep(opts.WaitBeforeOpen)

	reqAt := time.Now()
	orderId, err := gtw.AccountGateway().SendOrder(order)
	if err != nil {
		log.Fatalf("Failed to send order %+v, err: %s", order, err)
	}

	order.ID = orderId

	log.Printf("Successfully sent order %+v, order id: %s, response time: %v", order, orderId, time.Now().Sub(reqAt))
	log.Printf("Waiting %v seconds before cancelling order...", opts.WaitBeforeCancel)
	time.Sleep(opts.WaitBeforeCancel)

	order.ID = orderId
	reqAt = time.Now()
	err = gtw.AccountGateway().CancelOrder(order)
	if err != nil {
		log.Fatalf("Failed to cancel order, err: %s", err)
	}

	log.Printf("Canceled order, response time %v!", time.Now().Sub(reqAt))
	log.Printf("Waiting %v seconds before exiting...", opts.WaitBeforeExit)
	time.Sleep(opts.WaitBeforeExit)
}
