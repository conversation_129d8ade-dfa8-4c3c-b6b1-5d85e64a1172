package main

import (
	"encoding/json"
	"log"
	"os"

	_ "github.com/herenow/atomic-gtw/mapper"
	"github.com/herenow/atomic-gtw/safegateway/service"
)

func main() {
	// Check if at least one argument is provided
	if len(os.Args) < 2 {
		log.Fatalf("Usage: %s CONFIG_JSON_STRING", os.Args[0])
	}

	// Parse the initialization options from the first argument
	optsStr := os.Args[1]
	log.Printf("Initializing safe gateway service w/ options:\n%s", optsStr)

	var opts service.Options
	if err := json.Unmarshal([]byte(optsStr), &opts); err != nil {
		log.Fatalf("Failed to decode init options: %s", err)
	}

	// Validate the options
	if err := opts.Validate(); err != nil {
		log.Fatalf("Invalid options: %s", err)
	}

	svc := service.NewSafeGatewayService(opts)
	err := svc.Init()
	if err != nil {
		log.Fatalf("Failed to initialize safe gateway service: %s", err)
	}
}
