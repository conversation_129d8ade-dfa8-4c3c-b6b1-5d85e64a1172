package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/herenow/atomic-gtw/cmd/gateway_check/checks"
	"github.com/herenow/atomic-gtw/gateway"
	_ "github.com/herenow/atomic-gtw/mapper"
	"github.com/jessevdk/go-flags"
)

type Options struct {
	// Exchange authentication
	TestSymbol    string  `long:"symbol" description:"Override the test market with a specific trading pair (e.g. BTC/BRL)"`
	ApiKey        string  `long:"apiKey" description:"Exchange API key"`
	ApiSecret     string  `long:"apiSecret" description:"Exchange API secret"`
	ExchangeName  string  `long:"exchange" required:"true" description:"Exchange to run checks against"`
	Staging       bool    `long:"staging" description:"Use exchange staging/testnet environment"`
	Environment   string  `long:"env" default:"prod" description:"Environment label for reporting (e.g. prod, staging)"`
	SlackWebhook  string  `long:"slackWebhook" description:"Slack webhook URL for notifications"`
	FlowType      string  `long:"flowType" default:"all" description:"Type of flows to run (market_data, account, all)"`
	MinOrderValue float64 `long:"minOrderValue" description:"Minimum order value for testing"`
	MaxOrderValue float64 `long:"maxOrderValue" description:"Maximum order value for testing"`

	// Timeouts
	FlowTimeout      time.Duration `long:"flowTimeout" default:"3m" description:"Maximum time allowed for each flow"`
	OperationTimeout time.Duration `long:"operationTimeout" default:"30s" description:"Maximum time for individual operations"`
	MaxDelay         time.Duration `long:"maxDelay" default:"10s" description:"Maximum acceptable delay for operations"`
}

func main() {
	var opts Options
	parser := flags.NewParser(&opts, flags.Default)

	if _, err := parser.Parse(); err != nil {
		var flagsErr *flags.Error
		if errors.As(err, &flagsErr) && errors.Is(flagsErr.Type, flags.ErrHelp) {
			os.Exit(0)
		}
	}

	// Validate flow type
	if !isValidFlowType(opts.FlowType) {
		log.Fatalf("Invalid flow type: %s. Must be one of: market_data, account, all", opts.FlowType)
	}

	// Initialize gateway
	gtw, err := initializeGateway(opts)
	if err != nil {
		log.Fatalf("Failed to initialize gateway: %v", err)
	}
	defer gtw.Close()

	// Create check options
	checkOpts := checks.Options{
		FlowTimeout:      opts.FlowTimeout,
		OperationTimeout: opts.OperationTimeout,
		MaxDelay:         opts.MaxDelay,
		FlowType:         opts.FlowType,
		MinOrderValue:    opts.MinOrderValue,
		MaxOrderValue:    opts.MaxOrderValue,
		TestSymbol:       opts.TestSymbol,
	}

	// Create and run check
	ctx := context.Background()
	gtwCheck := checks.NewCheck(checkOpts)

	// Set up cleanup handler for CTRL+C using the check's cleanup flow
	gtwCheck.InitiateCleanup(gtw)

	// Run the checks
	checkResult := gtwCheck.Run(ctx, gtw)

	// Generate summary
	summaryOptions := checks.SummaryOptions{
		ExchangeName: opts.ExchangeName,
		Environment:  opts.Environment,
	}

	summary := checks.GenerateSummary(checkResult.FlowResults, checkOpts.FlowType, summaryOptions)

	// Print results
	summary.PrintConsole()

	// Send to Slack if webhook provided
	if opts.SlackWebhook != "" {
		if err = summary.SendToSlack(opts.SlackWebhook); err != nil {
			log.Printf("Warning: Failed to send Slack notification: %v", err)
		}
	}

	// Exit with error code if any flows failed
	if summary.FailedFlows > 0 {
		os.Exit(1)
	}
}

func initializeGateway(opts Options) (gateway.Gateway, error) {
	exchange, found := gateway.ExchangeBySymbol(opts.ExchangeName)
	if !found {
		return nil, fmt.Errorf("exchange %s not found", opts.ExchangeName)
	}

	gtw, found := gateway.NewByExchange(exchange, gateway.Options{
		ApiKey:    opts.ApiKey,
		ApiSecret: opts.ApiSecret,
		Staging:   opts.Staging,
	})
	if !found {
		return nil, fmt.Errorf("gateway not found for exchange %s", opts.ExchangeName)
	}

	if err := gtw.Connect(); err != nil {
		return nil, fmt.Errorf("failed to connect to exchange: %w", err)
	}

	return gtw, nil
}

func isValidFlowType(flowType string) bool {
	switch flowType {
	case checks.MarketDataFlows, checks.AccountFlows, checks.AllFlows:
		return true
	default:
		return false
	}
}
