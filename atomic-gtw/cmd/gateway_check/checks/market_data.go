package checks

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/book"
)

const (
	MinimumPriceLevels     = 10 // Minimum number of price levels expected
	MinimumUpdates         = 10 // Minimum number of updates
	MaxCrossedBookStates   = 0  // Maximum allowed number of crossed book states
	InitialSnapshotTimeout = 20 * time.Second
	TradeWaitTimeout       = 3 * time.Minute
	MinValidationTime      = 5 * time.Second  // Minimum time to run validation
	RequiredStability      = 2 * time.Second  // Time book must be stable
	MaxUpdateGap           = 30 * time.Second // Maximum time between updates
)

type validationState struct {
	ReceivedSnapshot  bool
	BidsInSnapshot    int
	AsksInSnapshot    int
	UpdateCount       int
	CrossedBooks      int
	PriceZeroValues   int
	TradesReceived    int
	LastTradeTime     time.Time
	LastUpdateTime    time.Time
	StableBookTime    time.Time
	BookStable        bool
	CurrentBookStats  *BookStats
	MaxSpreadObserved float64
	LastBookCrossTime time.Time
	TopLevelUpdates   int    // Updates to best bid/ask
	TotalTickUpdates  int    // All ticks updates
	LastBookError     string // Last book-related error message
}

type MarketDataValidationFlow struct {
	options     Options
	tickManager *TickManager
	evTimer     *EventCollector
}

func NewMarketDataValidationFlow(options Options, tickManager *TickManager) *MarketDataValidationFlow {
	return &MarketDataValidationFlow{
		options:     options,
		tickManager: tickManager,
		evTimer:     NewEventCollector(),
	}
}

func (f *MarketDataValidationFlow) Name() string {
	return "MarketDataValidation"
}

func (f *MarketDataValidationFlow) Prerequisites() []Prerequisite {
	return []Prerequisite{} // No prerequisites needed for market data validation
}

func (f *MarketDataValidationFlow) Run(ctx context.Context, _ gateway.Gateway) FlowResult {
	start := time.Now()

	f.evTimer.RecordTime(EventOrderCreated)
	vState := &validationState{
		LastUpdateTime: time.Now(),
	}

	// Subscribe to market data events
	depthCh, depthSub := f.tickManager.SubscribeDepth()
	tradeCh, tradeSub := f.tickManager.SubscribeTrades()
	bookCh, bookSub := f.tickManager.SubscribeBookUpdates()

	defer depthSub.Close()
	defer tradeSub.Close()
	defer bookSub.Close()

	timeoutCtx, cancel := context.WithTimeout(ctx, f.options.FlowTimeout)
	defer cancel()

	snapshotCtx, cancelSnapshot := context.WithTimeout(timeoutCtx, InitialSnapshotTimeout)
	defer cancelSnapshot()

	tradeCtx, cancelTrade := context.WithTimeout(timeoutCtx, TradeWaitTimeout)
	defer cancelTrade()

	updateTicker := time.NewTicker(100 * time.Millisecond)
	defer updateTicker.Stop()

	// Get initial book reference
	ob, exists := f.tickManager.GetBook(f.options.TestMarket.Symbol)
	if !exists {
		return f.generateErrorResult("order book not registered", start, vState)
	}

	for {
		select {
		case <-tradeCtx.Done():
			if vState.TradesReceived == 0 {
				return f.generateErrorResult("timeout waiting for trades", start, vState)
			}

		case <-snapshotCtx.Done():
			if !vState.ReceivedSnapshot {
				return f.generateErrorResult("timeout waiting for initial snapshot", start, vState)
			}

		case <-timeoutCtx.Done():
			return f.generateErrorResult("overall validation timeout", start, vState)

		case <-updateTicker.C:
			if err := f.validateBookState(ob, vState); err != nil {
				vState.LastBookError = err.Error()
			}

			if time.Since(vState.LastUpdateTime) > MaxUpdateGap {
				return f.generateErrorResult(fmt.Sprintf("no updates received for %v", MaxUpdateGap), start, vState)
			}

		case depth := <-depthCh:
			if err := f.processDepthEvent(depth, ob, vState, f.evTimer); err != nil {
				return f.generateErrorResult(err.Error(), start, vState)
			}

		case trade := <-tradeCh:
			if err := f.processTradeEvent(trade, vState, f.evTimer); err != nil {
				return f.generateErrorResult(err.Error(), start, vState)
			}

		case <-bookCh:
			// Book has been updated, check stability
			vState.TotalTickUpdates++
			if !vState.BookStable {
				if vState.StableBookTime.IsZero() {
					vState.StableBookTime = time.Now()
				} else if time.Since(vState.StableBookTime) > RequiredStability {
					vState.BookStable = true
					log.Printf("[MarketDataValidation]: book stabilized after %v", time.Since(start))
				}
			}

			// Update current book stats
			vState.CurrentBookStats = analyzeBook(ob)
			if vState.CurrentBookStats.SpreadPct > vState.MaxSpreadObserved {
				vState.MaxSpreadObserved = vState.CurrentBookStats.SpreadPct
			}

			// Check if we can complete validation
			if f.canCompleteValidation(start, vState) {
				result := f.generateSuccessResult(start, vState, f.evTimer)

				// Final validity checks
				if err := f.performFinalValidation(vState); err != nil {
					result.Status = FlowFailed
					result.Error = err
				}

				return result
			}
		}
	}
}

func (f *MarketDataValidationFlow) processTradeEvent(trade gateway.Trade, state *validationState, events *EventCollector) error {
	if state.TradesReceived == 0 {
		events.RecordTime(EventTradeReceived)
	}

	if err := f.validateTradeEvent(trade); err != nil {
		return fmt.Errorf("invalid trade data: %w", err)
	}

	state.TradesReceived++
	state.LastTradeTime = time.Now()
	return nil
}

func (f *MarketDataValidationFlow) processDepthEvent(depth gateway.Depth, book *book.Book, state *validationState, events *EventCollector) error {
	events.RecordTime(EventBookUpdate)

	if !state.ReceivedSnapshot {
		events.RecordTime(EventBookSnapshot)
		state.ReceivedSnapshot = true
	}

	state.UpdateCount++
	state.LastUpdateTime = time.Now()

	// Count updates by side
	if depth.Side == gateway.Bid {
		state.BidsInSnapshot++
		if isBestBid(book, depth.Price) {
			state.TopLevelUpdates++
		}
	} else {
		state.AsksInSnapshot++
		if isBestAsk(book, depth.Price) {
			state.TopLevelUpdates++
		}
	}

	if depth.Price == 0 {
		state.PriceZeroValues++
	}

	return nil
}

func (f *MarketDataValidationFlow) validateBookState(ob *book.Book, state *validationState) error {
	if !state.ReceivedSnapshot {
		return nil // Wait for snapshot
	}

	// Check for crossed books
	if isCrossedBook(ob) {
		state.CrossedBooks++
		state.LastBookCrossTime = time.Now()
		return fmt.Errorf("detected crossed book state")
	}

	// Validate spread
	stats := analyzeBook(ob)
	if stats.SpreadPct < 0 {
		return fmt.Errorf("negative spread detected: %.8f%%", stats.SpreadPct)
	}
	if stats.SpreadPct > 5.0 { // 5% max spread threshold
		return fmt.Errorf("excessive spread detected: %.8f%%", stats.SpreadPct)
	}

	return nil
}

func (f *MarketDataValidationFlow) canCompleteValidation(start time.Time, state *validationState) bool {
	if !state.ReceivedSnapshot || !state.BookStable {
		return false
	}

	minimumTimePassed := time.Since(start) > MinValidationTime
	hasMinimumUpdates := state.UpdateCount >= MinimumUpdates
	hasMinimumTrades := state.TradesReceived > 0
	hasMinimumDepth := state.BidsInSnapshot >= MinimumPriceLevels &&
		state.AsksInSnapshot >= MinimumPriceLevels

	return minimumTimePassed && hasMinimumUpdates && hasMinimumTrades && hasMinimumDepth
}

func (f *MarketDataValidationFlow) performFinalValidation(state *validationState) error {

	// Validate final state
	if state.CrossedBooks > MaxCrossedBookStates {
		return fmt.Errorf("too many crossed book states observed: %d", state.CrossedBooks)
	}

	if state.PriceZeroValues > 0 {
		return fmt.Errorf("zero price value on updates found")
	}

	// Validate book quality
	if state.CurrentBookStats.BidCount < MinimumPriceLevels ||
		state.CurrentBookStats.AskCount < MinimumPriceLevels {
		return fmt.Errorf("insufficient final book depth: bids=%d asks=%d",
			state.CurrentBookStats.BidCount,
			state.CurrentBookStats.AskCount)
	}

	return nil
}

func (f *MarketDataValidationFlow) generateSuccessResult(start time.Time, state *validationState, events *EventCollector) FlowResult {
	return FlowResult{
		Name:     f.Name(),
		Status:   FlowPassed,
		Duration: time.Since(start),
		Measurements: []Measurement{
			{
				Name:  "InitialSnapshotTime",
				Value: events.TimeBetween(EventOrderCreated, EventBookSnapshot),
				Tags:  map[string]string{"market": f.options.TestMarket.Symbol},
			},
			{
				Name:  "TimeToFirstTrade",
				Value: events.TimeBetween(EventBookSnapshot, EventTradeReceived),
				Tags:  map[string]string{"market": f.options.TestMarket.Symbol},
			},
			{
				Name:  "BookStabilizationTime",
				Value: time.Since(state.StableBookTime),
				Tags:  map[string]string{"market": f.options.TestMarket.Symbol},
			},
		},
		Metadata: map[string]interface{}{
			"bidsInSnapshot":      state.BidsInSnapshot,
			"asksInSnapshot":      state.AsksInSnapshot,
			"updateCount":         state.UpdateCount,
			"topLevelUpdates":     state.TopLevelUpdates,
			"crossedBooks":        state.CrossedBooks,
			"priceWithZeroValues": state.PriceZeroValues,
			"tradesReceived":      state.TradesReceived,
			"maxSpreadObserved":   fmt.Sprintf("%.4f%%", state.MaxSpreadObserved),
			"bookStats":           formatBookStats(state.CurrentBookStats),
		},
	}
}

func (f *MarketDataValidationFlow) generateErrorResult(errMsg string, start time.Time, state *validationState) FlowResult {
	return FlowResult{
		Name:     f.Name(),
		Status:   FlowFailed,
		Duration: time.Since(start),
		Error:    fmt.Errorf("%s (last book error: %s)", errMsg, state.LastBookError),
		Metadata: map[string]interface{}{
			"bidsInSnapshot":    state.BidsInSnapshot,
			"asksInSnapshot":    state.AsksInSnapshot,
			"updateCount":       state.UpdateCount,
			"crossedBooks":      state.CrossedBooks,
			"tradesReceived":    state.TradesReceived,
			"lastBookStats":     formatBookStats(state.CurrentBookStats),
			"lastUpdateTime":    state.LastUpdateTime,
			"lastTradeTime":     state.LastTradeTime,
			"lastBookCrossTime": state.LastBookCrossTime,
		},
	}
}

func isBestBid(b *book.Book, price float64) bool {
	best, hasBest := b.Bids.Top()
	return hasBest && price >= best.Value
}

func isBestAsk(b *book.Book, price float64) bool {
	best, hasBest := b.Asks.Top()
	return hasBest && price <= best.Value
}

func (f *MarketDataValidationFlow) validateTradeEvent(trade gateway.Trade) error {
	if trade.Symbol == "" {
		return fmt.Errorf("trade missing Symbol")
	}
	if trade.Direction != gateway.Bid && trade.Direction != gateway.Ask {
		return fmt.Errorf("invalid trade Direction: %v", trade.Direction)
	}
	if trade.Price <= 0 {
		return fmt.Errorf("invalid trade Price: %v", trade.Price)
	}
	if trade.Amount <= 0 {
		return fmt.Errorf("invalid trade Amount: %v", trade.Amount)
	}
	if trade.Timestamp.IsZero() {
		return fmt.Errorf("trade missing Timestamp")
	}
	if trade.Timestamp.After(time.Now().Add(5 * time.Minute)) {
		return fmt.Errorf("trade timestamp too far in future: %v", trade.Timestamp)
	}
	return nil
}

type BookStats struct {
	BidCount   int     // Number of bid price levels
	AskCount   int     // Number of ask price levels
	BidVolume  float64 // Total volume on bid side
	AskVolume  float64 // Total volume on ask side
	SpreadPct  float64 // Spread as percentage
	CrossCount int     // Number of crossed prices
	BestBid    float64 // Best bid price
	BestAsk    float64 // Best ask price
	Timestamp  time.Time
}

func analyzeBook(b *book.Book) *BookStats {
	stats := &BookStats{
		BidCount:  b.Bids.Length(),
		AskCount:  b.Asks.Length(),
		Timestamp: time.Now(),
	}

	bestBid, hasBid := b.Bids.Top()
	bestAsk, hasAsk := b.Asks.Top()

	if hasBid {
		stats.BestBid = bestBid.Value
	}
	if hasAsk {
		stats.BestAsk = bestAsk.Value
	}

	// Calculate volumes and weighted prices
	var bidWeightedSum float64

	for _, price := range b.Bids.TopRange(1000) {
		stats.BidVolume += price.Amount
		bidWeightedSum += price.Value * price.Amount
	}

	for _, price := range b.Asks.TopRange(1000) {
		stats.AskVolume += price.Amount
	}

	// Calculate spread percentage
	if hasBid && hasAsk && bestAsk.Value > 0 {
		stats.SpreadPct = ((bestAsk.Value - bestBid.Value) / bestAsk.Value) * 100
	}

	// Count crossed prices
	stats.CrossCount = countCrossedPrices(b)

	return stats
}

func isCrossedBook(b *book.Book) bool {
	bestBid, hasBid := b.Bids.Top()
	bestAsk, hasAsk := b.Asks.Top()

	if !hasBid || !hasAsk {
		return false // Can't be crossed if we don't have both sides
	}

	return bestBid.Value >= bestAsk.Value
}

func countCrossedPrices(b *book.Book) int {
	crossed := 0
	bestBid, hasBid := b.Bids.Top()

	if !hasBid {
		return 0
	}

	asks := b.Asks.TopRange(1000)
	for _, ask := range asks {
		if bestBid.Value >= ask.Value {
			crossed++
		}
	}

	return crossed
}
