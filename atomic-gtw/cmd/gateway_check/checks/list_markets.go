package checks

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type ListMarketsFlow struct {
	options Options
}

type MarketValidation struct {
	Market   gateway.Market
	Valid    bool
	Errors   []string
	Warnings []string
}

func NewListMarketsFlow(options Options) *ListMarketsFlow {
	return &ListMarketsFlow{
		options: options,
	}
}

func (f *ListMarketsFlow) Name() string {
	return "ListMarkets"
}

func (f *ListMarketsFlow) Prerequisites() []Prerequisite {
	return []Prerequisite{} // No prerequisites needed for listing markets
}

func (f *ListMarketsFlow) Run(ctx context.Context, gtw gateway.Gateway) FlowResult {
	start := time.Now()

	// Fetch markets
	markets, err := gtw.GetMarkets()
	if err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to get markets: %w", err),
		}
	}

	if len(markets) == 0 {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("no markets returned from exchange"),
		}
	}

	// Validate each market
	var totalErrors, totalWarnings int
	var validMarkets, invalidMarkets int

	for _, market := range markets {
		validation := f.validateMarket(market)

		if validation.Valid {
			validMarkets++
		} else {
			invalidMarkets++
		}

		totalErrors += len(validation.Errors)
		totalWarnings += len(validation.Warnings)
	}

	// Log validation results
	log.Printf("[ListMarkets] Validated %d markets: %d valid, %d invalid (%d errors, %d warnings)",
		len(markets), validMarkets, invalidMarkets, totalErrors, totalWarnings)

	// Check if we have major issues
	var status FlowStatus
	var resultErr error

	if invalidMarkets > 0 {
		status = FlowFailed
		resultErr = fmt.Errorf("%d markets failed validation", invalidMarkets)
	} else if totalWarnings > 0 {
		status = FlowPassed
	} else {
		status = FlowPassed
	}

	return FlowResult{
		Name:     f.Name(),
		Status:   status,
		Error:    resultErr,
		Duration: time.Since(start),
		Metadata: map[string]interface{}{
			"totalMarkets":   len(markets),
			"validMarkets":   validMarkets,
			"invalidMarkets": invalidMarkets,
			"totalErrors":    totalErrors,
			"totalWarnings":  totalWarnings,
		},
	}
}

func (f *ListMarketsFlow) validateMarket(market gateway.Market) MarketValidation {
	validation := MarketValidation{
		Market: market,
		Valid:  true,
	}

	// Validate Exchange field
	if market.Exchange.Name == "" {
		validation.addError("missing Exchange name")
	}

	// Validate Symbol
	if market.Symbol == "" {
		validation.addError("missing Symbol")
	}

	// Validate Pair
	if market.Pair.Base == "" {
		validation.addError("missing Base asset in Pair")
	}
	if market.Pair.Quote == "" {
		validation.addError("missing Quote asset in Pair")
	}

	// Validate price tick
	if market.PriceTick <= 0 {
		validation.addError("invalid or missing PriceTick")
	}

	// Validate amount tick
	if market.AmountTick <= 0 {
		validation.addError("invalid or missing AmountTick")
	}

	// Validate minimum order size
	if market.MinimumOrderSize <= 0 {
		validation.addError("invalid or missing MinimumOrderSize")
	}

	// Warn if minimum order value is not set (helpful but not required)
	if market.MinimumOrderMoneyValue <= 0 {
		validation.addWarning("missing MinimumOrderMoneyValue")
	}

	// Special checks for futures markets
	if market.FuturesContract {
		if market.FuturesMarginAsset == "" {
			validation.addError("futures market missing FuturesMarginAsset")
		}
	}

	return validation
}

func (v *MarketValidation) addError(err string) {
	v.Errors = append(v.Errors, err)
	v.Valid = false
}

func (v *MarketValidation) addWarning(warn string) {
	v.Warnings = append(v.Warnings, warn)
}
