package checks

import (
	"fmt"
	"math"
	"strings"
	"time"
	"unicode"

	"github.com/herenow/atomic-gtw/gateway"
)

// Human-readable labels for metadata and measurement keys
var humanReadableLabels = map[string]string{
	// Metadata labels
	"crossedBooks":        "Book Crossings",
	"bidsInSnapshot":      "Bids in Snapshot",
	"asksInSnapshot":      "Asks in Snapshot",
	"updateCount":         "Total Updates",
	"topLevelUpdates":     "Top Level Updates",
	"priceWithZeroValues": "Zero Price Updates",
	"tradesReceived":      "Trades Received",
	"maxSpreadObserved":   "Maximum Spread",
	"orderID":             "Order ID",
	"finalState":          "Final State",
	"orderPrice":          "Order Price",
	"orderAmount":         "Order Amount",
	"orderNotional":       "Order Value",
	"isOrderQueued":       "Order Queued",
	"fillsReceived":       "Fills Received",
	"averagePrice":        "Average Fill Price",
	"totalValue":          "Total Fill Value",
	"orderInBook":         "Order in Book",
	"uniqueAmount":        "Unique Amount",
	"quoteAsset":          "Quote Asset",
	"totalErrors":         "Total Errors",
	"totalWarnings":       "Total Warnings",
	"validMarkets":        "Valid Markets",
	"invalidMarkets":      "Invalid Markets",
	"nonZeroBalances":     "Non-zero Balances",
	"totalBalances":       "Total Balances",
	"validBalances":       "Valid Balances",
	"invalidBalances":     "Invalid Balances",

	// Measurement labels
	"OrderToCreateDelay":            "Order Creation Time",
	"OrderCreatedToBookDelay":       "Book Update Time",
	"TimeToFirstFill":               "Time to First Fill",
	"TimeInPartialState":            "Partial Fill Duration",
	"TotalExecutionTime":            "Total Execution Time",
	"InitialUpdateDelay":            "Initial Update Delay",
	"CancelToConfirm":               "Cancel Confirmation Time",
	"TimeToRemoveFromOpenOrders":    "Order Removal Time",
	"TotalOpenOrdersVisibilityTime": "Total Order Visibility",
	"InitialSnapshotTime":           "Initial Snapshot Time",
	"BookStabilizationTime":         "Book Stabilization Time",
}

// getHumanReadableLabel returns a human readable label for a key
func getHumanReadableLabel(key string) string {
	if label, ok := humanReadableLabels[key]; ok {
		return label
	}
	// If no mapping exists, convert camelCase to Title Case with spaces
	runes := []rune(key)
	var result strings.Builder

	for i := 0; i < len(runes); i++ {
		if i > 0 && unicode.IsUpper(runes[i]) {
			result.WriteRune(' ')
		}
		if i == 0 {
			result.WriteRune(unicode.ToUpper(runes[i]))
		} else {
			result.WriteRune(runes[i])
		}
	}

	return result.String()
}

// formatDurationWithPrecision formats duration with appropriate precision
func formatDurationWithPrecision(d time.Duration) string {
	ms := float64(d.Nanoseconds()) / float64(time.Millisecond)
	if d < time.Second {
		return fmt.Sprintf("%.3fms", ms)
	}
	return fmt.Sprintf("%.2fs", d.Seconds())
}

// getStatusEmoji returns an appropriate emoji for each status
func getStatusEmoji(status FlowStatus) string {
	switch status {
	case FlowPassed:
		return "✅"
	case FlowFailed:
		return "❌"
	case FlowSkipped:
		return "⏭️"
	case FlowTimeout:
		return "⏰"
	default:
		return "❓"
	}
}

// formatMetadataValue formats metadata values based on their key and type
func formatMetadataValue(key string, value interface{}) string {
	switch key {
	case "bookStats":
		if stats, ok := value.(BookStats); ok {
			return formatBookStats(&stats)
		}
	case "orderUpdate":
		if order, ok := value.(gateway.Order); ok {
			return formatOrderUpdate(order)
		}
	case "balanceSnapshots":
		if snapshots, ok := value.([]BalanceSnapshot); ok {
			return formatBalanceSnapshotHistory(snapshots)
		}
	case "executionTiming":
		if timing, ok := value.(map[string]interface{}); ok {
			return formatExecutionTiming(timing)
		}
	case "orderID":
		return fmt.Sprintf("%v", value)
	case "finalState":
		return fmt.Sprintf("%v", value)
	case "orderPrice":
		return fmt.Sprintf("%.8f", value)
	case "orderAmount":
		return fmt.Sprintf("%.8f", value)
	case "orderNotional":
		return fmt.Sprintf("%.2f", value)
	case "isOrderQueued":
		if queued, ok := value.(bool); ok {
			return fmt.Sprintf("%v", queued)
		}
	case "fillsReceived":
		return fmt.Sprintf("%v", value)
	case "tradesReceived":
		return fmt.Sprintf("%v", value)
	case "crossedBooks":
		return fmt.Sprintf("%v", value)
	case "updateCount":
		return fmt.Sprintf("%v", value)
	case "maxSpreadObserved":
		return fmt.Sprintf("%v", value)
	case "zeroAmountRatio":
		if ratio, ok := value.(float64); ok {
			return fmt.Sprintf("%.2f%%", ratio*100)
		}
	case "bid", "ask":
		if testMap, ok := value.(map[string]interface{}); ok {
			var parts []string

			// Format order details
			if market, ok := testMap["market"].(string); ok {
				parts = append(parts, fmt.Sprintf("Market: %s", market))
			}
			if side, ok := testMap["side"].(gateway.Side); ok {
				parts = append(parts, fmt.Sprintf("Side: %s", side))
			}

			// Format amounts and prices with proper precision
			if amount, ok := testMap["orderAmount"].(float64); ok {
				parts = append(parts, fmt.Sprintf("Amount: %.8f", amount))
			}
			if price, ok := testMap["orderPrice"].(float64); ok {
				parts = append(parts, fmt.Sprintf("Price: %.2f", price))
			}
			if total, ok := testMap["orderTotal"].(float64); ok {
				parts = append(parts, fmt.Sprintf("Total: %.2f", total))
			}

			// Format balance information
			if asset, ok := testMap["assetTested"].(string); ok {
				if balance, ok := testMap["assetBalance"].(float64); ok {
					parts = append(parts, fmt.Sprintf("Balance: %.8f %s", balance, asset))
				}
			}

			return "\n\t  " + strings.Join(parts, "\n\t  ")
		}
	}
	return fmt.Sprintf("%v", value)
}

func formatExecutionTiming(timing map[string]interface{}) string {
	var parts []string

	if firstFill, ok := timing["firstFill"].(time.Time); !ok || !firstFill.IsZero() {
		parts = append(parts, fmt.Sprintf("First Fill: %s", firstFill.Format("15:04:05.000")))
	}

	if lastFill, ok := timing["lastFill"].(time.Time); !ok || !lastFill.IsZero() {
		parts = append(parts, fmt.Sprintf("Last Fill: %s", lastFill.Format("15:04:05.000")))
	}

	if fillCount, ok := timing["fillCount"].(int); ok {
		parts = append(parts, fmt.Sprintf("Fill Count: %d", fillCount))
	}

	if avgPrice, ok := timing["averagePrice"].(float64); ok {
		parts = append(parts, fmt.Sprintf("Avg Price: %.8f", avgPrice))
	}

	if totalValue, ok := timing["totalValue"].(float64); ok {
		parts = append(parts, fmt.Sprintf("Total Value: %.8f", totalValue))
	}

	return strings.Join(parts, ", ")
}

func formatBookStats(stats *BookStats) string {
	if stats == nil {
		return "N/A"
	}
	return fmt.Sprintf("%d/%d bids/asks | Vol: %.2f/%.2f | Prices: %.2f/%.2f | Spread: %.4f%%",
		stats.BidCount,
		stats.AskCount,
		stats.BidVolume,
		stats.AskVolume,
		stats.BestBid,
		stats.BestAsk,
		stats.SpreadPct,
	)
}

func formatOrderUpdate(order gateway.Order) string {
	return fmt.Sprintf(
		"Order: %s | Type: %s | Side: %s | State: %s | Amount: %.8f | Price: %.8f",
		order.ID,
		order.Type,
		order.Side,
		order.State,
		order.Amount,
		order.Price,
	)
}

func formatBalanceSnapshotHistory(snapshots []BalanceSnapshot) string {
	if len(snapshots) == 0 {
		return "No balance history"
	}

	// Only show first and last snapshot if we have multiple entries
	var relevantSnapshots []BalanceSnapshot
	if len(snapshots) > 1 {
		relevantSnapshots = []BalanceSnapshot{
			snapshots[0],                // Initial balance
			snapshots[len(snapshots)-1], // Final balance
		}
	} else {
		relevantSnapshots = snapshots
	}

	var builder strings.Builder
	var firstSnap = relevantSnapshots[0]

	// Show initial balances
	builder.WriteString(fmt.Sprintf("\n\tInitial: %s %.8f %s | %.8f %s\n",
		firstSnap.Stage,
		firstSnap.BaseBalance.Total,
		firstSnap.BaseBalance.Asset,
		firstSnap.QuoteBalance.Total,
		firstSnap.QuoteBalance.Asset,
	))

	// Show final balances and changes if we have more than one snapshot
	if len(relevantSnapshots) > 1 {
		lastSnap := relevantSnapshots[1]
		baseChange := lastSnap.BaseBalance.Total - firstSnap.BaseBalance.Total
		quoteChange := lastSnap.QuoteBalance.Total - firstSnap.QuoteBalance.Total

		// Only show changes if they're non-zero
		if baseChange != 0 || quoteChange != 0 {
			builder.WriteString(fmt.Sprintf("\tFinal  : %s %.8f %s | %.8f %s\n",
				lastSnap.Stage,
				lastSnap.BaseBalance.Total,
				lastSnap.BaseBalance.Asset,
				lastSnap.QuoteBalance.Total,
				lastSnap.QuoteBalance.Asset,
			))

			builder.WriteString(fmt.Sprintf("\tNet    : %s%.8f %s | %s%.8f %s",
				getChangePrefix(baseChange),
				math.Abs(baseChange),
				firstSnap.BaseBalance.Asset,
				getChangePrefix(quoteChange),
				math.Abs(quoteChange),
				firstSnap.QuoteBalance.Asset,
			))
		}
	}

	return builder.String()
}

func getChangePrefix(change float64) string {
	if change > 0 {
		return "+"
	}
	if change < 0 {
		return "-"
	}
	return " "
}
