package checks

import (
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

// FlowStatus represents the state of a test flow execution
type FlowStatus string

const (
	FlowPassed  FlowStatus = "PASSED"
	FlowFailed  FlowStatus = "FAILED"
	FlowSkipped FlowStatus = "SKIPPED"
	FlowTimeout FlowStatus = "TIMEOUT"
)

// EventType types
type EventType string

const (
	EventOrderSent                     EventType = "OrderSent"
	EventOrderCreated                  EventType = "OrderCreated"
	EventOrderInBook                   EventType = "OrderInBook"
	EventBookSnapshot                  EventType = "BookSnapshot"
	EventBookUpdate                    EventType = "BookUpdate"
	EventTradeReceived                 EventType = "TradeReceived"
	EventOrderExecuted                 EventType = "OrderExecuted"
	EventOrderPartiallyExecuted        EventType = "OrderPartiallyExecuted"
	EventOrderFullyExecuted            EventType = "LatestExecution"
	EventCancelSent                    EventType = "CancelSent"
	EventOrderCanceled                 EventType = "OrderRemoved"
	EventOpenOrdersCheckStarted        EventType = "OpenOrdersCheckStarted"
	EventOpenOrdersVisible             EventType = "OpenOrdersVisible"
	EventOpenOrdersRemovalCheckStarted EventType = "OpenOrdersRemovalCheckStarted"
	EventOpenOrdersRemoved             EventType = "OpenOrdersRemoved"
)

type FlowResult struct {
	Name         string
	Status       FlowStatus
	Duration     time.Duration
	Error        error
	Measurements []Measurement
	Metadata     map[string]interface{}
}

type Measurement struct {
	Name  string
	Value time.Duration
	Tags  map[string]string
}

type Prerequisite struct {
	Name        string
	Description string
	Validate    func(gtw gateway.Gateway) error
}
