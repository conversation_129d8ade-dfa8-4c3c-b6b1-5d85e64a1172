package checks

import (
	"log"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-tools/pkg/book"
)

const (
	// Channel buffer sizes
	depthBufferSize      = 10000
	tradeBufferSize      = 1000
	orderBufferSize      = 100
	executionBufferSize  = 100
	bookUpdateBufferSize = 1000
)

type EventSubscription interface {
	Close()
}

type TickManager struct {
	// Book management
	books      map[string]*book.Book
	booksMutex *sync.RWMutex

	// Order management
	orders           map[string]gateway.Order
	orderUpdateQueue map[string][]gateway.Order
	orderFillQueue   map[string][]gateway.Fill
	ordersMutex      *sync.RWMutex

	// Event subscriptions
	orderUpdateSubs   map[chan gateway.Order]struct{}
	orderExecSubs     map[chan gateway.Execution]struct{}
	depthSubs         map[chan gateway.Depth]struct{}
	tradeSubs         map[chan gateway.Trade]struct{}
	bookUpdateSubs    map[chan gateway.Market]struct{}
	subscriptionMutex *sync.RWMutex
}

func NewTickManager() *TickManager {
	return &TickManager{
		// Book tracking
		books:      make(map[string]*book.Book),
		booksMutex: &sync.RWMutex{},

		// Order tracking
		orders:           make(map[string]gateway.Order),
		orderUpdateQueue: make(map[string][]gateway.Order),
		orderFillQueue:   make(map[string][]gateway.Fill),
		ordersMutex:      &sync.RWMutex{},

		// Subscriptions
		orderUpdateSubs:   make(map[chan gateway.Order]struct{}),
		orderExecSubs:     make(map[chan gateway.Execution]struct{}),
		depthSubs:         make(map[chan gateway.Depth]struct{}),
		tradeSubs:         make(map[chan gateway.Trade]struct{}),
		bookUpdateSubs:    make(map[chan gateway.Market]struct{}),
		subscriptionMutex: &sync.RWMutex{},
	}
}

// ProcessTick processes incoming gateway ticks and distributes events to subscribers
func (tm *TickManager) ProcessTick(tick gateway.Tick) {
	var snapshotSequence bool
	var symbol string
	var bids []book.Price
	var asks []book.Price

	for _, event := range tick.EventLog {
		switch event.Type {
		case gateway.SnapshotSequenceEvent:
			seq := event.Data.(gateway.SnapshotSequence)
			symbol = seq.Symbol
			snapshotSequence = true

		case gateway.DepthEvent:
			depth := event.Data.(gateway.Depth)
			symbol = depth.Symbol
			tm.dispatchDepthEvent(depth)

			price := book.Price{
				Value:  depth.Price,
				Amount: depth.Amount,
			}

			if depth.Side == gateway.Bid {
				bids = append(bids, price)
			} else {
				asks = append(asks, price)
			}

		case gateway.TradeEvent:
			trade := event.Data.(gateway.Trade)
			tm.dispatchTradeEvent(trade)

		case gateway.OrderUpdateEvent:
			order := event.Data.(gateway.Order)
			tm.UpdateOrder(order)

		case gateway.FillEvent:
			fill := event.Data.(gateway.Fill)
			tm.FillEvent(fill)
		}
	}

	// Update order book if we have depth updates
	if symbol != "" {
		if b, exists := tm.GetBook(symbol); exists {
			if len(bids) > 0 || len(asks) > 0 || snapshotSequence {
				if snapshotSequence {
					b.Snapshot(bids, asks)
				} else {
					b.DeltaUpdate(bids, asks)
				}
				tm.notifyBookUpdate(symbol)
			}
		}
	}
}

func (tm *TickManager) GetOrder(id string) (gateway.Order, bool) {
	tm.ordersMutex.RLock()
	defer tm.ordersMutex.RUnlock()

	order, ok := tm.orders[id]
	return order, ok
}

func (tm *TickManager) LoadOrder(order gateway.Order) {
	tm.ordersMutex.Lock()
	defer tm.ordersMutex.Unlock()

	current, ok := tm.orders[order.ID]
	if ok {
		log.Printf("[TickManager]: load order already has a loaded order with id [%s], updating updatable attributes", order.ID)
		current.Market = order.Market
		current.Side = order.Side
		if current.Price == 0.0 {
			current.Price = order.Price
		}
		if current.Amount == 0.0 {
			current.Amount = order.Amount
		}
	} else {
		current = order
	}

	tm.orders[order.ID] = current

	// Process queued updates
	if updates, ok := tm.orderUpdateQueue[order.ID]; ok && len(updates) > 0 {
		log.Printf("[TickManager]: processing %d queued updates for order %s", len(updates), order.ID)
		for _, update := range updates {
			go tm.UpdateOrder(update)
		}
		delete(tm.orderUpdateQueue, order.ID)
	}

	// Process queued fills
	if fills, ok := tm.orderFillQueue[order.ID]; ok && len(fills) > 0 {
		log.Printf("[TickManager]: processing %d queued fills for order %s", len(fills), order.ID)
		for _, fill := range fills {
			go tm.FillEvent(fill)
		}
		delete(tm.orderFillQueue, order.ID)
	}
}

func (tm *TickManager) UpdateOrder(order gateway.Order) {
	tm.ordersMutex.Lock()
	defer tm.ordersMutex.Unlock()

	current, ok := tm.orders[order.ID]
	if !ok {
		if _, exists := tm.orderUpdateQueue[order.ID]; !exists {
			tm.orderUpdateQueue[order.ID] = make([]gateway.Order, 0, 1)
		}
		tm.orderUpdateQueue[order.ID] = append(tm.orderUpdateQueue[order.ID], order)
		return
	}

	executed := order.FilledAmount - current.FilledAmount
	executedValue := order.FilledAmount * order.AvgPrice
	if executed > 0 {
		tm.dispatchOrderExecution(order, order.AvgPrice, executed, "", order.Fee-current.Fee, order.FeeAsset)
	}

	// Update order state
	if order.FilledAmount != 0 {
		current.FilledAmount = order.FilledAmount
	}
	if executedValue != 0 {
		current.FilledMoneyValue = executedValue
	}
	if order.State != "" {
		current.State = order.State
	}
	if order.Price != 0.0 {
		current.Price = order.Price
	}
	if order.AvgPrice != 0.0 {
		current.AvgPrice = order.AvgPrice
	}
	if order.Fee != 0.0 {
		current.Fee = order.Fee
	}
	if order.FeeAsset != "" {
		current.FeeAsset = order.FeeAsset
	}
	tm.orders[order.ID] = current

	// Dispatch update
	tm.dispatchOrderUpdate(current)
}

func (tm *TickManager) FillEvent(fill gateway.Fill) {
	tm.ordersMutex.Lock()
	defer tm.ordersMutex.Unlock()

	orderID := fill.OrderID
	if orderID == "" {
		log.Printf("[TickManager]: received fill event without order id\n%+v", fill)
		return
	}

	current, ok := tm.orders[orderID]
	if !ok {
		if _, exists := tm.orderFillQueue[orderID]; !exists {
			tm.orderFillQueue[orderID] = make([]gateway.Fill, 0, 1)
		}
		tm.orderFillQueue[orderID] = append(tm.orderFillQueue[orderID], fill)
		return
	}

	previousFilled := current.FilledAmount
	filledValue := fill.Amount * fill.Price

	current.FilledAmount += fill.Amount
	current.FilledMoneyValue += filledValue
	if current.FilledAmount > 0 {
		current.AvgPrice = current.FilledMoneyValue / current.FilledAmount
	}

	if current.FilledAmount > previousFilled {
		tm.dispatchOrderExecution(current, fill.Price, fill.Amount, fill.ID, fill.Fee, fill.FeeAsset)
	}

	tm.dispatchOrderUpdate(current)
	tm.orders[orderID] = current
}

func (tm *TickManager) GetBook(symbol string) (*book.Book, bool) {
	tm.booksMutex.RLock()
	defer tm.booksMutex.RUnlock()

	b, exists := tm.books[symbol]
	return b, exists
}

func (tm *TickManager) RegisterBook(market gateway.Market) *book.Book {
	tm.booksMutex.Lock()
	defer tm.booksMutex.Unlock()

	if b, exists := tm.books[market.Symbol]; exists {
		return b
	}

	b := book.NewBook(market.Symbol)
	tm.books[market.Symbol] = b
	return b
}

func (tm *TickManager) SubscribeDepth() (chan gateway.Depth, EventSubscription) {
	tm.subscriptionMutex.Lock()
	defer tm.subscriptionMutex.Unlock()

	ch := make(chan gateway.Depth, depthBufferSize)
	tm.depthSubs[ch] = struct{}{}

	return ch, &subscription{
		manager: tm,
		depthCh: ch,
	}
}

func (tm *TickManager) SubscribeTrades() (chan gateway.Trade, EventSubscription) {
	tm.subscriptionMutex.Lock()
	defer tm.subscriptionMutex.Unlock()

	ch := make(chan gateway.Trade, tradeBufferSize)
	tm.tradeSubs[ch] = struct{}{}

	return ch, &subscription{
		manager: tm,
		tradeCh: ch,
	}
}

func (tm *TickManager) SubscribeOrderUpdates() (chan gateway.Order, EventSubscription) {
	tm.subscriptionMutex.Lock()
	defer tm.subscriptionMutex.Unlock()

	ch := make(chan gateway.Order, orderBufferSize)
	tm.orderUpdateSubs[ch] = struct{}{}

	return ch, &subscription{
		manager: tm,
		orderCh: ch,
	}
}

func (tm *TickManager) SubscribeOrderExecutions() (chan gateway.Execution, EventSubscription) {
	tm.subscriptionMutex.Lock()
	defer tm.subscriptionMutex.Unlock()

	ch := make(chan gateway.Execution, executionBufferSize)
	tm.orderExecSubs[ch] = struct{}{}

	return ch, &subscription{
		manager:     tm,
		executionCh: ch,
	}
}

func (tm *TickManager) SubscribeBookUpdates() (chan gateway.Market, EventSubscription) {
	tm.subscriptionMutex.Lock()
	defer tm.subscriptionMutex.Unlock()

	ch := make(chan gateway.Market, bookUpdateBufferSize)
	tm.bookUpdateSubs[ch] = struct{}{}

	return ch, &subscription{
		manager: tm,
		bookCh:  ch,
	}
}

func (tm *TickManager) dispatchDepthEvent(depth gateway.Depth) {
	tm.subscriptionMutex.RLock()
	defer tm.subscriptionMutex.RUnlock()

	for ch := range tm.depthSubs {
		select {
		case ch <- depth:
		default:
			log.Printf("[TickManager]: Depth channel full, dropping update for %s", depth.Symbol)
		}
	}
}

func (tm *TickManager) dispatchTradeEvent(trade gateway.Trade) {
	tm.subscriptionMutex.RLock()
	defer tm.subscriptionMutex.RUnlock()

	for ch := range tm.tradeSubs {
		select {
		case ch <- trade:
		default:
			log.Printf("[TickManager]: Trade channel full, dropping update for %s", trade.Symbol)
		}
	}
}

func (tm *TickManager) dispatchOrderUpdate(order gateway.Order) {
	tm.subscriptionMutex.RLock()
	defer tm.subscriptionMutex.RUnlock()

	for ch := range tm.orderUpdateSubs {
		select {
		case ch <- order:
		default:
			log.Printf("[TickManager]: Order update channel full, dropping update for order %s", order.ID)
		}
	}
}

func (tm *TickManager) dispatchOrderExecution(order gateway.Order, price, amount float64, fillID string, fee float64, feeAsset string) {
	tm.subscriptionMutex.RLock()
	defer tm.subscriptionMutex.RUnlock()

	exec := gateway.Execution{
		Time:     time.Now(),
		Market:   order.Market,
		TradeID:  fillID,
		OrderID:  order.ID,
		Side:     order.Side,
		Price:    price,
		Amount:   amount,
		Fee:      fee,
		FeeAsset: feeAsset,
	}

	for ch := range tm.orderExecSubs {
		select {
		case ch <- exec:
		default:
			log.Printf("[TickManager]: Execution channel full, dropping update for order %s", order.ID)
		}
	}
}

func (tm *TickManager) notifyBookUpdate(symbol string) {
	tm.subscriptionMutex.RLock()
	defer tm.subscriptionMutex.RUnlock()

	if b, exists := tm.books[symbol]; exists {
		stats := analyzeBook(b)
		if stats.BestBid > 0 && stats.BestAsk > 0 {
			for ch := range tm.bookUpdateSubs {
				select {
				case ch <- gateway.Market{Symbol: symbol}:
				default:
					log.Printf("[TickManager]: Book update channel full, dropping update for %s", symbol)
				}
			}
		}
	}
}

func (tm *TickManager) unsubscribeDepth(ch chan gateway.Depth) {
	tm.subscriptionMutex.Lock()
	defer tm.subscriptionMutex.Unlock()
	delete(tm.depthSubs, ch)
}

func (tm *TickManager) unsubscribeTrades(ch chan gateway.Trade) {
	tm.subscriptionMutex.Lock()
	defer tm.subscriptionMutex.Unlock()
	delete(tm.tradeSubs, ch)
}

func (tm *TickManager) unsubscribeOrderUpdates(ch chan gateway.Order) {
	tm.subscriptionMutex.Lock()
	defer tm.subscriptionMutex.Unlock()
	delete(tm.orderUpdateSubs, ch)
}

func (tm *TickManager) unsubscribeOrderExecutions(ch chan gateway.Execution) {
	tm.subscriptionMutex.Lock()
	defer tm.subscriptionMutex.Unlock()
	delete(tm.orderExecSubs, ch)
}

func (tm *TickManager) unsubscribeBookUpdates(ch chan gateway.Market) {
	tm.subscriptionMutex.Lock()
	defer tm.subscriptionMutex.Unlock()
	delete(tm.bookUpdateSubs, ch)
}

type subscription struct {
	manager     *TickManager
	depthCh     chan gateway.Depth
	tradeCh     chan gateway.Trade
	orderCh     chan gateway.Order
	executionCh chan gateway.Execution
	bookCh      chan gateway.Market
}

func (s *subscription) Close() {
	if s.depthCh != nil {
		s.manager.unsubscribeDepth(s.depthCh)
		close(s.depthCh)
	}
	if s.tradeCh != nil {
		s.manager.unsubscribeTrades(s.tradeCh)
		close(s.tradeCh)
	}
	if s.orderCh != nil {
		s.manager.unsubscribeOrderUpdates(s.orderCh)
		close(s.orderCh)
	}
	if s.executionCh != nil {
		s.manager.unsubscribeOrderExecutions(s.executionCh)
		close(s.executionCh)
	}
	if s.bookCh != nil {
		s.manager.unsubscribeBookUpdates(s.bookCh)
		close(s.bookCh)
	}
}
