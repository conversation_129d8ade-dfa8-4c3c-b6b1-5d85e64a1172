package checks

import (
	"context"
	"fmt"
	"log"
	"sort"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

const (
	TotalMarketsToMonitor = 20
)

// priorityMarket represents a market with its priority level
type priorityMarket struct {
	Pair     gateway.Pair
	Priority int // Lower number means higher priority
}

// getPriorityMarkets returns the list of priority markets in order of importance
func getPriorityMarkets() []priorityMarket {
	return []priorityMarket{
		{
			Pair:     gateway.Pair{Base: "BTC", Quote: "BRL"},
			Priority: 1,
		},
		{
			Pair:     gateway.Pair{Base: "ETH", Quote: "BTC"},
			Priority: 2,
		},
		{
			Pair:     gateway.Pair{Base: "BTC", Quote: "USDC"},
			Priority: 3,
		},
		{
			Pair:     gateway.Pair{Base: "BTC", Quote: "USD"},
			Priority: 4,
		},
		{
			Pair:     gateway.Pair{Base: "BTC", Quote: "USDT"},
			Priority: 5,
		},
	}
}

type CheckResult struct {
	Name        string
	FlowResults []FlowResult
	Duration    time.Duration
}

type GatewayCheck struct {
	flows       []OrderCheckFlow
	tickManager *TickManager
	options     Options
	cleanup     *AccountCleanupFlow // Add cleanup flow reference
}

func NewCheck(options Options) *GatewayCheck {
	tickManager := NewTickManager()
	return &GatewayCheck{
		options:     options,
		tickManager: tickManager,
		flows:       make([]OrderCheckFlow, 0),
		cleanup:     NewAccountCleanupFlow(options),
	}
}

func (c *GatewayCheck) InitiateCleanup(gtw gateway.Gateway) {
	c.cleanup.CleanupOnInterrupt(gtw)
}

// Run executes all configured flows in sequence
func (c *GatewayCheck) Run(ctx context.Context, gtw gateway.Gateway) CheckResult {
	start := time.Now()

	if err := c.initialize(ctx, gtw); err != nil {
		return CheckResult{
			Name: "GatewayCheck",
			FlowResults: []FlowResult{{
				Name:     "Initialization",
				Status:   FlowFailed,
				Error:    err,
				Duration: time.Since(start),
			}},
			Duration: time.Since(start),
		}
	}

	var results []FlowResult
	var prereqFail bool

	for _, flow := range c.flows {
		for _, prereq := range flow.Prerequisites() {
			if err := prereq.Validate(gtw); err != nil {
				prereqFail = true
				results = append(results, FlowResult{
					Name:     flow.Name(),
					Status:   FlowSkipped,
					Error:    err,
					Duration: 0,
				})
				continue
			}
		}

		if !prereqFail {
			flowCtx, cancel := context.WithTimeout(ctx, c.options.FlowTimeout)
			result := flow.Run(flowCtx, gtw)
			cancel()
			results = append(results, result)
		}
	}

	return CheckResult{
		Name:        "GatewayCheck",
		FlowResults: results,
		Duration:    time.Since(start),
	}
}

func (c *GatewayCheck) initialize(_ context.Context, gtw gateway.Gateway) error {
	markets, err := gtw.GetMarkets()
	if err != nil {
		return fmt.Errorf("failed to get markets: %v", err)
	}

	var balances []gateway.Balance
	if c.options.FlowType == AccountFlows || c.options.FlowType == AllFlows {
		balances, err = gtw.AccountGateway().Balances()
		if err != nil {
			return fmt.Errorf("failed to get balances: %v", err)
		}
	}

	// Find the best market for testing
	if c.options.TestSymbol != "" {
		overrideMarket, err := findOverrideMarket(markets, c.options.TestSymbol)
		if err != nil {
			return fmt.Errorf("failed to use override market: %v", err)
		}
		log.Printf("Using override market %s for testing", overrideMarket.Symbol)
		c.options.TestMarket = overrideMarket
	} else {
		// Find the best market for testing using existing logic
		testMarket, found := findBestTestMarket(markets, balances)
		if !found {
			return fmt.Errorf("no suitable test market found with available balance")
		}
		c.options.TestMarket = testMarket
	}

	c.cleanup.options = c.options

	// Prioritize market subscriptions
	prioritizedMarkets := prioritizeMarketSubscriptions(markets)

	// Register books for monitoring (up to TotalMarketsToMonitor)
	monitorCount := TotalMarketsToMonitor
	if len(prioritizedMarkets) < monitorCount {
		monitorCount = len(prioritizedMarkets)
	}

	for _, market := range prioritizedMarkets[:monitorCount] {
		c.tickManager.RegisterBook(market)
	}

	// Log subscription details
	log.Printf("Subscribing to %d markets:", monitorCount)
	for i, market := range prioritizedMarkets[:monitorCount] {
		isPriority := false
		priority := 0
		for _, pm := range getPriorityMarkets() {
			if market.Pair.Base == pm.Pair.Base && market.Pair.Quote == pm.Pair.Quote {
				isPriority = true
				priority = pm.Priority
				break
			}
		}

		priorityStr := ""
		if isPriority {
			priorityStr = fmt.Sprintf(" (Priority: %d)", priority)
		}

		log.Printf("%-2v %-7s%s",
			i+1,
			market.Symbol,
			priorityStr,
		)

		c.tickManager.RegisterBook(market)
	}
	log.Printf("%s", strings.Repeat("-", 40))

	if err = gtw.SubscribeMarkets(prioritizedMarkets[:monitorCount]); err != nil {
		return fmt.Errorf("failed to subscribe to markets: %v", err)
	}

	go func() {
		for tick := range gtw.Tick() {
			c.tickManager.ProcessTick(tick)
		}
	}()

	// Separate flows by type
	var flows []OrderCheckFlow

	// Market Data flows
	if c.options.FlowType == MarketDataFlows || c.options.FlowType == AllFlows {
		marketDataFlows := []OrderCheckFlow{
			NewListMarketsFlow(c.options),
			NewMarketDataValidationFlow(c.options, c.tickManager),
		}
		flows = append(flows, marketDataFlows...)
	}

	// Account flows - only add if we have API credentials and appropriate flow type
	if c.options.FlowType == AccountFlows || c.options.FlowType == AllFlows {
		accountFlows := []OrderCheckFlow{
			NewListBalancesFlow(c.options),
			NewOrderCancellationFlow(c.options, c.tickManager),
			NewInsufficientBalanceFlow(c.options, c.tickManager),
			NewMinOrderSizeFlow(c.options, c.tickManager),
			NewOrderBookPlacementFlow(c.options, c.tickManager),
			NewOrderExecutionFlow(c.options, c.tickManager),
			NewAccountCleanupFlow(c.options),
		}
		flows = append(flows, accountFlows...)
	}

	c.flows = flows

	return nil
}

// findBestTestMarket selects the best market for testing based on:
// 1. Available priority markets
// 2. Available balance
func findBestTestMarket(markets []gateway.Market, balances []gateway.Balance) (gateway.Market, bool) {
	priorityMarkets := getPriorityMarkets()
	marketsByPriority := make(map[string]gateway.Market)
	var highestPriorityMarket gateway.Market
	highestPrioritySet := false

	// Index available markets by pair string
	for _, market := range markets {
		pairStr := market.Pair.Base + "/" + market.Pair.Quote
		marketsByPriority[pairStr] = market
	}

	// Create balance lookup map
	balanceMap := make(map[string]gateway.Balance)
	for _, balance := range balances {
		balanceMap[balance.Asset] = balance
	}

	// Try to find the highest priority market with available balances
	for _, pm := range priorityMarkets {
		pairStr := pm.Pair.Base + "/" + pm.Pair.Quote
		market, exists := marketsByPriority[pairStr]
		if !exists {
			continue
		}

		// Keep track of highest priority market
		if !highestPrioritySet {
			highestPrioritySet = true
			highestPriorityMarket = market
		}

		// Check for balances
		baseBalance, hasBase := balanceMap[pm.Pair.Base]
		quoteBalance, hasQuote := balanceMap[pm.Pair.Quote]

		// Neither Base nor Quote balance is available, so go to the next market.
		if !hasBase || !hasQuote {
			continue
		}

		// Found market with sufficient balance
		if quoteBalance.Available >= market.MinimumOrderMoneyValue*1.5 ||
			baseBalance.Available >= market.MinimumOrderMoneyValue*1.5 {
			log.Printf("Selected test market %s (priority %d) with %s: %.8f / %s: %.8f available balances",
				pairStr, pm.Priority, pm.Pair.Base, baseBalance.Available, pm.Pair.Quote, quoteBalance.Available)
			return market, true
		}
	}

	// No market found with sufficient balance, use highest priority market
	if highestPrioritySet {
		log.Printf("WARNING: No priority markets found with sufficient balance. Using highest priority market %s for testing",
			highestPriorityMarket.Symbol)
		return highestPriorityMarket, true
	}

	// No priority markets available at all
	return gateway.Market{}, false
}

// prioritizeMarketSubscriptions reorders markets to ensure priority markets are subscribed first
func prioritizeMarketSubscriptions(markets []gateway.Market) []gateway.Market {
	priorityMarkets := getPriorityMarkets()
	priorityMap := make(map[string]int)

	// Create priority lookup map
	for _, pm := range priorityMarkets {
		pairStr := pm.Pair.Base + "/" + pm.Pair.Quote
		priorityMap[pairStr] = pm.Priority
	}

	// Sort markets based on priority
	sort.SliceStable(markets, func(i, j int) bool {
		iPriority := priorityMap[markets[i].Pair.Base+"/"+markets[i].Pair.Quote]
		jPriority := priorityMap[markets[j].Pair.Base+"/"+markets[j].Pair.Quote]

		// If both markets are priority markets, sort by priority
		if iPriority > 0 && jPriority > 0 {
			return iPriority < jPriority
		}

		// If only one is a priority market, it should come first
		if iPriority > 0 {
			return true
		}
		if jPriority > 0 {
			return false
		}

		// For non-priority markets, maintain original order
		return i < j
	})

	return markets
}

func sufficientBalancePrereq(opt Options) Prerequisite {
	return Prerequisite{
		Name:        "Sufficient Balance",
		Description: "Check if account has sufficient balance for test order",
		Validate: func(gtw gateway.Gateway) error {
			balances, err := gtw.AccountGateway().Balances()
			if err != nil {
				return fmt.Errorf("failed to get balances: %w", err)
			}

			// Get quote balance
			balance, ok := gateway.BalanceByAsset(balances, opt.TestMarket.Pair.Quote)
			if !ok {
				return fmt.Errorf("balance not found for %s", opt.TestMarket.Pair.Quote)
			}

			// Calculate minimum required balance
			// Use either MinOrderValue or calculate from market minimums
			requiredBalance := opt.MinOrderValue
			if requiredBalance == 0 {
				// If MinOrderValue not set, use market minimum order value * 1.5 for safety margin
				if opt.TestMarket.MinimumOrderMoneyValue > 0 {
					requiredBalance = opt.TestMarket.MinimumOrderMoneyValue * 1.5
				} else {
					// Fallback to a reasonable default if market minimums not available
					requiredBalance = 10.0 // Default to 10 units of quote currency
				}
			}

			if balance.Available < requiredBalance {
				return fmt.Errorf("insufficient balance: have %.8f %s, need %.8f %s",
					balance.Available,
					opt.TestMarket.Pair.Quote,
					requiredBalance,
					opt.TestMarket.Pair.Quote,
				)
			}

			// Also check base balance for potential sell orders
			baseBalance, ok := gateway.BalanceByAsset(balances, opt.TestMarket.Pair.Base)
			if !ok {
				return fmt.Errorf("balance not found for %s", opt.TestMarket.Pair.Base)
			}

			// Calculate minimum base amount
			minBaseAmount := opt.TestMarket.MinimumOrderSize
			if minBaseAmount == 0 {
				minBaseAmount = 0.001 // Fallback default
			}

			if baseBalance.Available < minBaseAmount*1.5 {
				log.Printf("Warning: Low base asset balance (%.8f %s), some tests may be limited",
					baseBalance.Available,
					opt.TestMarket.Pair.Base,
				)
			}

			return nil
		},
	}
}

// Add new helper function to find override market
func findOverrideMarket(markets []gateway.Market, symbol string) (gateway.Market, error) {
	// First try exact symbol match
	for _, market := range markets {
		if market.Symbol == symbol {
			return market, nil
		}
	}

	// Try matching base/quote pair format
	parts := strings.Split(symbol, "/")
	if len(parts) == 2 {
		base := strings.ToUpper(strings.TrimSpace(parts[0]))
		quote := strings.ToUpper(strings.TrimSpace(parts[1]))

		for _, market := range markets {
			if market.Pair.Base == base && market.Pair.Quote == quote {
				return market, nil
			}
		}
	}

	// If we get here, we couldn't find the market
	return gateway.Market{}, fmt.Errorf("market %s not found. Available markets: %s",
		symbol,
		formatAvailableMarkets(markets),
	)
}

// Helper function to format available markets for error message
func formatAvailableMarkets(markets []gateway.Market) string {
	if len(markets) == 0 {
		return "none"
	}

	// Only show first 10 markets to avoid huge error messages
	maxShow := 10
	var marketList []string

	for i := 0; i < len(markets) && i < maxShow; i++ {
		marketList = append(marketList, fmt.Sprintf("%s (%s/%s)",
			markets[i].Symbol,
			markets[i].Pair.Base,
			markets[i].Pair.Quote))
	}

	remaining := len(markets) - maxShow
	if remaining > 0 {
		marketList = append(marketList, fmt.Sprintf("and %d more...", remaining))
	}

	return strings.Join(marketList, ", ")
}
