package checks

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type OrderCancellationFlow struct {
	options     Options
	tickManager *TickManager
	evTimer     *EventCollector
}

func NewOrderCancellationFlow(options Options, tickManager *TickManager) *OrderCancellationFlow {
	return &OrderCancellationFlow{
		options:     options,
		tickManager: tickManager,
		evTimer:     NewEventCollector(),
	}
}

func (f *OrderCancellationFlow) Name() string {
	return "OrderCancellation"
}

func (f *OrderCancellationFlow) Prerequisites() []Prerequisite {
	return []Prerequisite{
		sufficientBalancePrereq(f.options),
	}
}

func (f *OrderCancellationFlow) Run(ctx context.Context, gtw gateway.Gateway) FlowResult {
	start := time.Now()

	orderCh, orderSub := f.tickManager.SubscribeOrderUpdates()
	defer orderSub.Close()

	price, amount, err := f.calculateValidOrderParams()
	if err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to calculate valid order parameters: %w", err),
		}
	}

	log.Printf("[OrderCancellation] Placing test order: price=%.8f amount=%.8f total=%.8f %s",
		price,
		amount,
		price*amount,
		f.options.TestMarket.Pair.Quote,
	)

	order := gateway.Order{
		Market: f.options.TestMarket,
		Side:   gateway.Bid,
		Type:   gateway.LimitOrder,
		Amount: amount,
		Price:  price,
	}

	f.evTimer.RecordTime(EventOrderSent)
	orderID, err := gtw.AccountGateway().SendOrder(order)
	if err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to place order: %w", err),
		}
	}

	order.ID = orderID
	f.tickManager.LoadOrder(order)

	// Wait for order to appear in open orders
	log.Printf("[OrderCancellation] Waiting for order to appear in open orders...")
	f.evTimer.RecordTime(EventOpenOrdersCheckStarted)
	if err = f.waitForOrderInOpenOrders(ctx, gtw, order, true); err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("order did not appear in open orders: %w", err),
		}
	}
	f.evTimer.RecordTime(EventOpenOrdersVisible)

	// Wait for cancellation confirmation via websocket
	cancelCtx, cancel := context.WithTimeout(ctx, f.options.OperationTimeout)
	defer cancel()

	for {
		select {
		case <-cancelCtx.Done():
			return FlowResult{
				Name:     f.Name(),
				Status:   FlowTimeout,
				Duration: time.Since(start),
				Error:    fmt.Errorf("timeout waiting for cancellation confirmation"),
			}

		case update := <-orderCh:
			if update.ID == orderID {
				if update.State == gateway.OrderOpen {
					f.evTimer.RecordTime(EventOrderCreated)

					log.Printf("[OrderCancellation] Canceling order %s", orderID)
					f.evTimer.RecordTime(EventCancelSent)
					if err = gtw.AccountGateway().CancelOrder(order); err != nil {
						return FlowResult{
							Name:     f.Name(),
							Status:   FlowFailed,
							Duration: time.Since(start),
							Error:    fmt.Errorf("failed to cancel order: %w", err),
						}
					}
				}

				if update.State == gateway.OrderCancelled {
					f.evTimer.RecordTime(EventOrderCanceled)

					// Verify order is removed from open orders
					log.Printf("[OrderCancellation] Order cancelled via websocket, verifying removal from open orders...")
					f.evTimer.RecordTime(EventOpenOrdersRemovalCheckStarted)
					if err = f.waitForOrderInOpenOrders(ctx, gtw, order, false); err != nil {
						return FlowResult{
							Name:     f.Name(),
							Status:   FlowFailed,
							Duration: time.Since(start),
							Error:    fmt.Errorf("order still present in open orders after cancellation: %w", err),
						}
					}
					f.evTimer.RecordTime(EventOpenOrdersRemoved)

					return FlowResult{
						Name:     f.Name(),
						Status:   FlowPassed,
						Duration: time.Since(start),
						Measurements: []Measurement{
							{
								Name:  "OrderToOpen",
								Value: f.evTimer.TimeBetween(EventOrderSent, EventOrderCreated),
								Tags:  map[string]string{"market": order.Market.Symbol},
							},
							{
								Name:  "TimeToAppearInOpenOrders",
								Value: f.evTimer.TimeBetween(EventOpenOrdersCheckStarted, EventOpenOrdersVisible),
								Tags:  map[string]string{"market": order.Market.Symbol},
							},
							{
								Name:  "CancelToConfirm",
								Value: f.evTimer.TimeBetween(EventCancelSent, EventOrderCanceled),
								Tags:  map[string]string{"market": order.Market.Symbol},
							},
							{
								Name:  "TimeToRemoveFromOpenOrders",
								Value: f.evTimer.TimeBetween(EventOpenOrdersRemovalCheckStarted, EventOpenOrdersRemoved),
								Tags:  map[string]string{"market": order.Market.Symbol},
							},
							{
								Name:  "TotalOpenOrdersVisibilityTime",
								Value: f.evTimer.TimeBetween(EventOpenOrdersVisible, EventOpenOrdersRemoved),
								Tags:  map[string]string{"market": order.Market.Symbol},
							},
						},
						Metadata: map[string]interface{}{
							"orderID":       orderID,
							"finalState":    update.State,
							"orderUpdate":   update,
							"orderPrice":    price,
							"orderAmount":   amount,
							"orderNotional": price * amount,
							"quoteAsset":    f.options.TestMarket.Pair.Quote,
						},
					}
				}
			}
		}
	}
}

// waitForOrderInOpenOrders checks if an order is present/absent in the open orders list
// If expectPresent is true, waits for order to appear, otherwise waits for it to disappear
func (f *OrderCancellationFlow) waitForOrderInOpenOrders(ctx context.Context, gtw gateway.Gateway, order gateway.Order, expectPresent bool) error {
	retryInterval := 500 * time.Millisecond
	maxAttempts := 10

	for i := 0; i < maxAttempts; i++ {
		select {
		case <-ctx.Done():
			return fmt.Errorf("context cancelled while checking open orders")
		default:
		}

		openOrders, err := gtw.AccountGateway().OpenOrders(order.Market)
		if err != nil {
			return fmt.Errorf("failed to fetch open orders: %w", err)
		}

		found := false
		for _, openOrder := range openOrders {
			if openOrder.ID == order.ID {
				found = true
				break
			}
		}

		if found == expectPresent {
			return nil // Found the expected state
		}

		if i < maxAttempts-1 {
			time.Sleep(retryInterval)
		}
	}

	if expectPresent {
		return fmt.Errorf("order never appeared in open orders")
	}
	return fmt.Errorf("order still present in open orders")
}

func (f *OrderCancellationFlow) calculateValidOrderParams() (price float64, amount float64, err error) {
	book, ok := f.tickManager.GetBook(f.options.TestMarket.Symbol)
	if !ok {
		return 0, 0, fmt.Errorf("order book not found for %s", f.options.TestMarket.Symbol)
	}

	// Get current market price
	bestBid, hasBid := book.Bids.Top()
	if !hasBid {
		return 0, 0, fmt.Errorf("no bids in order book")
	}

	// Place bid 20% below market price to avoid execution
	price = utils.FloorToTick(bestBid.Value*0.8, f.options.TestMarket.PriceTick)

	// Calculate minimum amount that satisfies both minimum size and minimum notional value
	minAmount := f.options.TestMarket.MinimumOrderSize
	minNotional := f.options.TestMarket.MinimumOrderMoneyValue

	if minNotional == 0 {
		minNotional = 11 // Default to slightly above 10 if not specified
	}

	// Calculate required amount to meet minimum notional
	requiredAmount := minNotional / price
	if requiredAmount > minAmount {
		minAmount = requiredAmount
	}

	// Add 10% to ensure we're safely above minimums
	amount = utils.FloorToTick(minAmount*1.1, f.options.TestMarket.AmountTick)

	// Check if we need to adjust the amount to meet minimum notional
	for amount*price < minNotional {
		// Increment amount by one tick until we meet the minimum notional
		amount = utils.FloorToTick(amount+f.options.TestMarket.AmountTick, f.options.TestMarket.AmountTick)
	}

	// Final validation
	if amount*price < minNotional {
		log.Printf("[OrderCancellation]: still unable to meet minimum notional after adjustments")
		// We continue anyway since we've made our best effort
	}

	return price, amount, nil
}
