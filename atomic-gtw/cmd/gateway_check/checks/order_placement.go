package checks

import (
	"context"
	"fmt"
	"log"
	"math"
	"math/rand"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
	"github.com/herenow/atomic-tools/pkg/book"
)

const (
	maxPriceLevels = 10    // Maximum price levels to monitor
	depthLimit     = 10    // Maximum levels requested from the exchange
	defaultMinSize = 0.001 // Default min order size
	epsilon        = 1e-8  // Small value for float comparison
)

type ObPlacementFlow struct {
	options     Options
	tickManager *TickManager
	evTimer     *EventCollector
}

func NewOrderBookPlacementFlow(options Options, tickManager *TickManager) *ObPlacementFlow {
	return &ObPlacementFlow{
		options:     options,
		tickManager: tickManager,
		evTimer:     NewEventCollector(),
	}
}

func (f *ObPlacementFlow) Name() string {
	return "OrderBookPlacement"
}

func (f *ObPlacementFlow) Prerequisites() []Prerequisite {
	return []Prerequisite{
		{
			Name:        "Market Depth",
			Description: "Check if market has sufficient depth for testing",
			Validate: func(gtw gateway.Gateway) error {
				ob, ok := f.tickManager.GetBook(f.options.TestMarket.Symbol)
				if !ok {
					return fmt.Errorf("order book not found for %s", f.options.TestMarket.Symbol)
				}

				maxRetries := 10
				retryDelay := 1 * time.Second

				for i := 0; i < maxRetries; i++ {
					askLen := ob.Asks.Length()
					bidLen := ob.Bids.Length()

					if askLen >= maxPriceLevels && bidLen >= maxPriceLevels {
						log.Printf("[OrderbookPlacement]: book populated with %d bids and %d asks", bidLen, askLen)
						return nil
					}

					if i < maxRetries-1 {
						log.Printf("[OrderbookPlacement]: waiting for book population, current levels - bids: %d, asks: %d (attempt %d/%d)",
							bidLen, askLen, i+1, maxRetries)
						time.Sleep(retryDelay)
					}
				}

				return fmt.Errorf("insufficient market depth after %d attempts", maxRetries)
			},
		},
		sufficientBalancePrereq(f.options),
	}
}

func (f *ObPlacementFlow) Run(ctx context.Context, gtw gateway.Gateway) FlowResult {
	start := time.Now()

	// Get book from tick manager
	ob, ok := f.tickManager.GetBook(f.options.TestMarket.Symbol)
	if !ok {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("order book not found for %s", f.options.TestMarket.Symbol),
		}
	}

	// Calculate test order parameters
	price, uniqueAmount, err := f.calculateOrderParams(ob)
	if err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to calculate order parameters: %w", err),
		}
	}

	// Setup event subscriptions
	depthCh, depthSub := f.tickManager.SubscribeDepth()
	defer depthSub.Close()

	orderCh, orderSub := f.tickManager.SubscribeOrderUpdates()
	defer orderSub.Close()

	// Setup order tracking
	var orderInBook bool
	var orderID string

	// Setup context and monitoring
	orderCtx, cancel := context.WithTimeout(ctx, f.options.OperationTimeout)
	defer cancel()

	// Place the order
	log.Printf("[OrderBookPlacement]: placing test order: price=%.8f amount=%.8f", price, uniqueAmount)

	order := gateway.Order{
		Market:   f.options.TestMarket,
		Side:     gateway.Bid,
		Type:     gateway.LimitOrder,
		Amount:   uniqueAmount,
		Price:    price,
		PostOnly: true,
	}

	f.evTimer.RecordTime(EventOrderSent)
	orderID, err = gtw.AccountGateway().SendOrder(order)
	if err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to place order: %w", err),
		}
	}

	order.ID = orderID
	f.tickManager.LoadOrder(order)

	var orderCreated bool

	for {
		select {
		case <-orderCtx.Done():
			// Attempt to cancel order on timeout
			if err = gtw.AccountGateway().CancelOrder(order); err != nil {
				log.Printf("[OrderBookPlacement]: failed to cancel order on timeout: %v", err)
			}
			return FlowResult{
				Name:     f.Name(),
				Status:   FlowTimeout,
				Duration: time.Since(start),
				Error:    fmt.Errorf("timeout waiting for order confirmation"),
			}

		case update := <-orderCh:
			if update.ID == orderID && update.State == gateway.OrderOpen && !orderCreated {
				orderCreated = true
				f.evTimer.RecordTime(EventOrderCreated)
				log.Printf("[OrderBookPlacement]: order created event received")
			}

		case depth := <-depthCh:
			if depth.Side == gateway.Bid {
				log.Printf("[OrderBookPlacement]: received depth event - Price: %.8f (expected: %.8f) Amount: %.8f (expected: %.8f)",
					depth.Price, price, depth.Amount, uniqueAmount)

				if approximatelyEqual(depth.Price, price, f.options.TestMarket.PriceTick) &&
					approximatelyEqual(depth.Amount, uniqueAmount, f.options.TestMarket.AmountTick) {

					if !orderInBook {
						orderInBook = true
						f.evTimer.RecordTime(EventOrderInBook)
						log.Printf("[OrderBookPlacement]: found matching depth event")

						// Calculate timing measurements
						creationDelay := f.evTimer.TimeBetween(EventOrderSent, EventOrderCreated)
						bookUpdateDelay := f.evTimer.TimeBetween(EventOrderCreated, EventOrderInBook)

						// Clean up the order
						if err = gtw.AccountGateway().CancelOrder(order); err != nil {
							log.Printf("[OrderBookPlacement]: failed to cancel order on cleanup: %v", err)
						}

						return FlowResult{
							Name:     f.Name(),
							Status:   FlowPassed,
							Duration: time.Since(start),
							Measurements: []Measurement{
								{
									Name:  "OrderToCreateDelay",
									Value: creationDelay,
									Tags:  map[string]string{"market": order.Market.Symbol},
								},
								{
									Name:  "OrderCreatedToBookDelay",
									Value: bookUpdateDelay,
									Tags:  map[string]string{"market": order.Market.Symbol},
								},
							},
							Metadata: map[string]interface{}{
								"orderID":       orderID,
								"orderInBook":   true,
								"isOrderQueued": bookUpdateDelay > f.options.MaxDelay,
								"uniqueAmount":  uniqueAmount,
								"orderPrice":    price,
							},
						}
					}
				}
			}
		}
	}
}

func (f *ObPlacementFlow) calculateOrderParams(book *book.Book) (price float64, amount float64, err error) {
	bidLevels := book.Bids.TopRange(maxPriceLevels)
	askLevels := book.Asks.TopRange(maxPriceLevels)
	if len(bidLevels) < depthLimit || len(askLevels) < depthLimit {
		return 0, 0, fmt.Errorf("insufficient order book depth levels")
	}

	// Build comprehensive price map from both sides of the book
	priceMap := make(map[float64]bool)
	for _, level := range bidLevels {
		priceMap[level.Value] = true
	}

	// Get the lowest bid we can see for reference
	lowestBid := bidLevels[len(bidLevels)-1].Value

	// Start looking for a price 5% below the lowest visible bid
	basePrice := lowestBid * 0.95

	// Generate a series of candidate prices below the market
	var selectedPrice float64
	maxAttempts := 50
	safetyGap := 10 // Number of price ticks to stay away from existing orders

	for attempt := 0; attempt < maxAttempts; attempt++ {
		// Move further from market with each attempt
		priceOffset := float64(attempt) * f.options.TestMarket.PriceTick * float64(safetyGap)
		candidatePrice := utils.FloorToTick(basePrice-priceOffset, f.options.TestMarket.PriceTick)

		// Check for a clear price gap
		hasNearbyOrders := false
		for i := -safetyGap; i <= safetyGap; i++ {
			nearbyPrice := utils.FloorToTick(candidatePrice+(float64(i)*f.options.TestMarket.PriceTick),
				f.options.TestMarket.PriceTick)
			if priceMap[nearbyPrice] {
				hasNearbyOrders = true
				break
			}
		}

		if !hasNearbyOrders {
			selectedPrice = candidatePrice
			break
		}
	}

	if selectedPrice == 0 {
		return 0, 0, fmt.Errorf("could not find a clear price level after %d attempts", maxAttempts)
	}

	// Calculate amount constraints
	minSize := f.options.TestMarket.MinimumOrderSize
	if minSize == 0 {
		minSize = defaultMinSize
	}

	// Make sure we're well above minimum size
	safeMinSize := minSize * 2 // Double the minimum size to be safe

	// Target a total order value between 10-15 USDT
	targetValue := 10.0 + (rand.Float64() * 5.0) // Random value between 10-15 USDT
	baseAmount := targetValue / selectedPrice

	// Ensure we're above minimum size
	if baseAmount < safeMinSize {
		baseAmount = safeMinSize
	}

	// Add a small random component for uniqueness while preserving minimum size
	randomComponent := baseAmount * 0.1 // 10% randomness
	uniqueAmount := utils.FloorToTick(
		baseAmount+(rand.Float64()*randomComponent),
		f.options.TestMarket.AmountTick,
	)

	// Final check to ensure we're above minimum size
	if uniqueAmount < minSize {
		uniqueAmount = utils.FloorToTick(minSize*2, f.options.TestMarket.AmountTick)
	}

	percentFromMarket := ((lowestBid - selectedPrice) / lowestBid) * 100
	totalValue := uniqueAmount * selectedPrice

	log.Printf("[OrderBookPlacement]: generated unique order: price=%.8f amount=%.8f value=%.2f %s (%.2f%% below market, min_size=%.8f, gap_ticks=%d)",
		selectedPrice, uniqueAmount, totalValue,
		f.options.TestMarket.Pair.Quote, percentFromMarket, minSize, safetyGap)

	return selectedPrice, uniqueAmount, nil
}

// approximatelyEqual checks if two floats are equal within the tick size precision
func approximatelyEqual(a, b, tick float64) bool {
	if tick == 0 {
		tick = epsilon
	}
	diff := math.Abs(a - b)
	return diff < tick/2
}
