package checks

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type ListBalancesFlow struct {
	options Options
}

type BalanceValidation struct {
	Balance  gateway.Balance
	Valid    bool
	Errors   []string
	Warnings []string
}

func NewListBalancesFlow(options Options) *ListBalancesFlow {
	return &ListBalancesFlow{
		options: options,
	}
}

func (f *ListBalancesFlow) Name() string {
	return "ListBalances"
}

func (f *ListBalancesFlow) Prerequisites() []Prerequisite {
	return []Prerequisite{} // No prerequisites needed for listing balances
}

func (f *ListBalancesFlow) Run(ctx context.Context, gtw gateway.Gateway) FlowResult {
	start := time.Now()

	// Fetch balances
	balances, err := gtw.AccountGateway().Balances()
	if err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to get balances: %w", err),
		}
	}

	if len(balances) == 0 {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("no balances returned from exchange"),
		}
	}

	// Validate each balance
	validations := make([]BalanceValidation, len(balances))
	var totalErrors, totalWarnings int
	var validBalances, invalidBalances, nonZeroBalances int

	for i, balance := range balances {
		validation := f.validateBalance(balance)
		validations[i] = validation

		if validation.Valid {
			validBalances++
		} else {
			invalidBalances++
		}

		if balance.Total > 0 {
			nonZeroBalances++
		}

		totalErrors += len(validation.Errors)
		totalWarnings += len(validation.Warnings)
	}

	// Log validation results
	log.Printf("[ListBalances] Validated %d balances: %d valid, %d invalid (%d errors, %d warnings), %d non-zero balances",
		len(balances), validBalances, invalidBalances, totalErrors, totalWarnings, nonZeroBalances)

	// Check if we have major issues
	var status FlowStatus
	var resultErr error

	if invalidBalances > 0 {
		status = FlowFailed
		resultErr = fmt.Errorf("%d balances failed validation", invalidBalances)
	} else if totalWarnings > 0 {
		status = FlowPassed
	} else {
		status = FlowPassed
	}

	return FlowResult{
		Name:     f.Name(),
		Status:   status,
		Error:    resultErr,
		Duration: time.Since(start),
		Metadata: map[string]interface{}{
			"totalBalances":   len(balances),
			"validBalances":   validBalances,
			"invalidBalances": invalidBalances,
			"nonZeroBalances": nonZeroBalances,
			"totalErrors":     totalErrors,
			"totalWarnings":   totalWarnings,
		},
	}
}

func (f *ListBalancesFlow) validateBalance(balance gateway.Balance) BalanceValidation {
	validation := BalanceValidation{
		Balance: balance,
		Valid:   true,
	}

	// Validate Asset name
	if balance.Asset == "" {
		validation.addError("missing Asset name")
	} else {
		// Check for common formatting issues
		if balance.Asset != strings.TrimSpace(balance.Asset) {
			validation.addError("Asset name contains leading/trailing whitespace")
		}
		if balance.Asset != strings.ToUpper(balance.Asset) {
			validation.addWarning("Asset name should be uppercase")
		}
	}

	// Validate Available balance
	if balance.Available < 0 {
		validation.addError(fmt.Sprintf("Negative Available balance: %f", balance.Available))
	}

	// Validate Total balance
	if balance.Total < 0 {
		validation.addError(fmt.Sprintf("Negative Total balance: %f", balance.Total))
	}

	// Validate relationship between Available and Total
	if balance.Available > balance.Total {
		validation.addError(fmt.Sprintf("Available balance (%f) exceeds Total balance (%f)",
			balance.Available, balance.Total))
	}

	// Check for suspiciously large balances (potential overflow/error)
	const maxReasonableBalance = 1e12 // 1 trillion
	if balance.Total > maxReasonableBalance {
		validation.addWarning(fmt.Sprintf("unusually large Total balance: %f", balance.Total))
	}
	if balance.Available > maxReasonableBalance {
		validation.addWarning(fmt.Sprintf("unusually large Available balance: %f", balance.Available))
	}

	// Check for precision issues (too many decimal places might indicate floating-point errors)
	const maxDecimals = 12
	if countDecimals(balance.Total) > maxDecimals {
		validation.addWarning(fmt.Sprintf("Total balance has unusually high precision: %f", balance.Total))
	}
	if countDecimals(balance.Available) > maxDecimals {
		validation.addWarning(fmt.Sprintf("Available balance has unusually high precision: %f", balance.Available))
	}

	// If there are locked funds (Total > Available), check if the difference makes sense
	if balance.Total > balance.Available {
		lockedAmount := balance.Total - balance.Available
		if lockedAmount < 1e-8 {
			validation.addWarning("Very small locked amount might indicate rounding error")
		}
	}

	return validation
}

func (v *BalanceValidation) addError(err string) {
	v.Errors = append(v.Errors, err)
	v.Valid = false
}

func (v *BalanceValidation) addWarning(warn string) {
	v.Warnings = append(v.Warnings, warn)
}

// Helper function to count decimal places
func countDecimals(f float64) int {
	str := fmt.Sprintf("%f", f)
	if idx := strings.IndexByte(str, '.'); idx != -1 {
		return len(str) - idx - 1
	}
	return 0
}
