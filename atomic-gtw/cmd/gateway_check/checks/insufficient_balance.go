package checks

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type InsufficientBalanceFlow struct {
	options     Options
	tickManager *TickManager
}

type testResult struct {
	Success     bool
	Error       error
	OrderUpdate gateway.Order
	CancelError error
	Metadata    map[string]interface{}
}

func NewInsufficientBalanceFlow(options Options, tickManager *TickManager) *InsufficientBalanceFlow {
	return &InsufficientBalanceFlow{
		options:     options,
		tickManager: tickManager,
	}
}

func (f *InsufficientBalanceFlow) Name() string {
	return "InsufficientBalance"
}

func (f *InsufficientBalanceFlow) Prerequisites() []Prerequisite {
	return []Prerequisite{} // No prerequisites as we specifically want to test insufficient balance scenarios
}

func (f *InsufficientBalanceFlow) Run(ctx context.Context, gtw gateway.Gateway) FlowResult {
	start := time.Now()

	balances, err := gtw.AccountGateway().Balances()
	if err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to get balances: %w", err),
		}
	}

	markets, err := gtw.GetMarkets()
	if err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to get markets: %w", err),
		}
	}

	var bidMarket, askMarket gateway.Market

	// Create balance lookup map
	balanceMap := make(map[string]float64)
	for _, balance := range balances {
		balanceMap[balance.Asset] = balance.Available
	}

	// Look for markets with zero balance in either base or quote
	for _, market := range markets {
		baseBalance := balanceMap[market.Pair.Base]
		quoteBalance := balanceMap[market.Pair.Quote]

		if baseBalance == 0 && quoteBalance > 0 && askMarket.Symbol == "" {
			// Zero base balance, good for ASK test
			askMarket = market
		}
		if quoteBalance == 0 && baseBalance > 0 && bidMarket.Symbol == "" {
			// Zero quote balance, good for BID test
			bidMarket = market
		}

		if askMarket.Symbol != "" && bidMarket.Symbol != "" {
			break
		}
	}

	bidResult := f.testOperation(ctx, gtw, bidMarket, gateway.Bid)
	askResult := f.testOperation(ctx, gtw, askMarket, gateway.Ask)

	if !bidResult.Success || !askResult.Success {
		var errMsg string
		if !bidResult.Success {
			errMsg += fmt.Sprintf("BID test failed: %v. ", bidResult.Error)
		}
		if !askResult.Success {
			errMsg += fmt.Sprintf("ASK test failed: %v", askResult.Error)
		}

		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf(errMsg),
			Metadata: map[string]interface{}{
				"bid":      bidResult.Metadata,
				"ask":      askResult.Metadata,
				"bidError": bidResult.Error,
				"askError": askResult.Error,
			},
		}
	}

	return FlowResult{
		Name:     f.Name(),
		Status:   FlowPassed,
		Duration: time.Since(start),
		Metadata: map[string]interface{}{
			"bid": bidResult.Metadata,
			"ask": askResult.Metadata,
		},
	}
}

func (f *InsufficientBalanceFlow) testOperation(_ context.Context, gtw gateway.Gateway, market gateway.Market, side gateway.Side) testResult {
	// Set up order update subscription
	orderCh, orderSub := f.tickManager.SubscribeOrderUpdates()
	defer orderSub.Close()

	// If no specific market found, use test market
	if market.Symbol == "" {
		market = f.options.TestMarket
	}

	// Get current market prices from order book
	book, ok := f.tickManager.GetBook(market.Symbol)
	if !ok {
		return testResult{
			Success: false,
			Error:   fmt.Errorf("order book not found for %s", market.Symbol),
		}
	}

	bestBid, hasBid := book.Bids.Top()
	bestAsk, hasAsk := book.Asks.Top()
	if !hasBid || !hasAsk {
		return testResult{
			Success: false,
			Error:   fmt.Errorf("incomplete order book data"),
		}
	}

	// Calculate order parameters based on side
	var price, amount float64
	var relevantBalance gateway.Balance
	var relevantAsset string

	balances, err := gtw.AccountGateway().Balances()
	if err != nil {
		return testResult{
			Success: false,
			Error:   fmt.Errorf("failed to get balances: %w", err),
		}
	}

	if side == gateway.Bid {
		price = utils.FloorToTick(bestBid.Value*0.9, market.PriceTick) // 10% below best bid
		relevantAsset = market.Pair.Quote
		relevantBalance, _ = gateway.BalanceByAsset(balances, relevantAsset)
		amount = utils.FloorToTick((relevantBalance.Available*1.2)/price, market.AmountTick) // only 1.2 times to avoid loss
	} else {
		price = utils.FloorToTick(bestAsk.Value*1.1, market.PriceTick) // 10% above best ask
		relevantAsset = market.Pair.Base
		relevantBalance, _ = gateway.BalanceByAsset(balances, relevantAsset)
		amount = utils.FloorToTick(relevantBalance.Available*1.2, market.AmountTick) // only 1.2 times to avoid loss
	}

	log.Printf("[InsufficientBalance] Testing %s order: market=%s amount=%.8f price=%.8f total=%.8f available=%s=%.8f",
		side,
		market.Symbol,
		amount,
		price,
		amount*price,
		relevantAsset,
		relevantBalance.Available,
	)

	order := gateway.Order{
		Market: market,
		Side:   side,
		Type:   gateway.LimitOrder,
		Amount: amount,
		Price:  price,
	}

	result := testResult{
		Success: true,
		Metadata: map[string]interface{}{
			"market":       market.Symbol,
			"side":         side,
			"orderAmount":  amount,
			"orderPrice":   price,
			"orderTotal":   amount * price,
			"assetBalance": relevantBalance.Available,
			"assetTested":  relevantAsset,
		},
	}

	orderID, err := gtw.AccountGateway().SendOrder(order)
	if err == nil {
		// Unexpected: Order was accepted despite insufficient balance
		// Try to cancel it immediately
		order.ID = orderID
		f.tickManager.LoadOrder(order)

		cancelErr := gtw.AccountGateway().CancelOrder(order)
		if cancelErr != nil {
			log.Printf("[InsufficientBalance] Warning: Failed to cancel unexpectedly accepted order: %v", cancelErr)
		}

		result.Success = false
		result.Error = fmt.Errorf("expected InsufficientBalanceErr but order was accepted")
		result.CancelError = cancelErr
		return result
	}

	// Verify we got the expected insufficient balance error
	if !errors.Is(err, gateway.InsufficientBalanceErr) {
		result.Success = false
		result.Error = fmt.Errorf("expected InsufficientBalanceErr but got: %w", err)
		return result
	}

	// Monitor for any unexpected updates
	select {
	case update := <-orderCh:
		result.Success = false
		result.Error = fmt.Errorf("received unexpected order update")
		result.OrderUpdate = update
		return result
	case <-time.After(2 * time.Second):
		// Expected behavior - no updates should come through
	}

	return result
}
