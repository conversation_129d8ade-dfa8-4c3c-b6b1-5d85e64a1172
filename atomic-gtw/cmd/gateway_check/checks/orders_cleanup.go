package checks

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"sync"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type AccountCleanupFlow struct {
	options Options
}

type cleanupResult struct {
	Market      string
	TotalOrders int
	Cancelled   int
	Failed      []string
	FetchError  error
}

func NewAccountCleanupFlow(options Options) *AccountCleanupFlow {
	return &AccountCleanupFlow{
		options: options,
	}
}

func (f *AccountCleanupFlow) Name() string {
	return "AccountCleanup"
}

func (f *AccountCleanupFlow) Prerequisites() []Prerequisite {
	return []Prerequisite{}
}

// CleanupOnInterrupt sets up cleanup handling for CTRL+C
func (f *AccountCleanupFlow) CleanupOnInterrupt(gtw gateway.Gateway) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)

	go func() {
		<-c
		log.Printf("\n[AccountCleanup] Interrupt received, cleaning up open orders...")

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		result := f.Run(ctx, gtw)
		if result.Error != nil {
			log.Printf("[AccountCleanup] Error during cleanup: %v", result.Error)
		} else {
			log.Printf("[AccountCleanup] Cleanup completed successfully")
		}
		os.Exit(1)
	}()
}

func (f *AccountCleanupFlow) Run(ctx context.Context, gtw gateway.Gateway) FlowResult {
	start := time.Now()

	market := f.options.TestMarket
	result := f.cleanupMarket(ctx, gtw, market)

	// Determine flow status
	status := FlowPassed
	var resultError error

	if result.FetchError != nil {
		status = FlowFailed
		resultError = fmt.Errorf("failed to fetch orders for market %s: %v", market.Symbol, result.FetchError)
	} else if len(result.Failed) > 0 {
		status = FlowFailed
		resultError = fmt.Errorf("failed to cancel some orders: %v", result.Failed)
	}

	return FlowResult{
		Name:     f.Name(),
		Status:   status,
		Duration: time.Since(start),
		Error:    resultError,
		Metadata: map[string]interface{}{
			"market":          market.Symbol,
			"totalOrders":     result.TotalOrders,
			"cancelledOrders": result.Cancelled,
			"failedCancels":   len(result.Failed),
		},
	}
}

func (f *AccountCleanupFlow) cleanupMarket(ctx context.Context, gtw gateway.Gateway, market gateway.Market) cleanupResult {
	result := cleanupResult{
		Market: market.Symbol,
	}

	openOrders, err := gtw.AccountGateway().OpenOrders(market)
	if err != nil {
		log.Printf("[AccountCleanup] Warning: Failed to fetch open orders for %s: %v", market.Symbol, err)
		result.FetchError = err
		return result
	}

	if len(openOrders) == 0 {
		log.Printf("[AccountCleanup] No open orders found in market %s", market.Symbol)
		return result
	}

	result.TotalOrders = len(openOrders)
	log.Printf("[AccountCleanup] Found %d open orders in market %s", len(openOrders), market.Symbol)

	var wg sync.WaitGroup
	var mu sync.Mutex // For thread-safe access to result

	for _, order := range openOrders {
		if !isActiveOrder(order.State) {
			continue
		}

		wg.Add(1)
		go func(o gateway.Order) {
			defer wg.Done()

			select {
			case <-ctx.Done():
				mu.Lock()
				result.Failed = append(result.Failed, fmt.Sprintf("context cancelled while cancelling order %s", o.ID))
				mu.Unlock()
				return
			default:
				log.Printf("[AccountCleanup] Cancelling order %s in state %s", o.ID, o.State)

				err := gtw.AccountGateway().CancelOrder(o)

				mu.Lock()
				defer mu.Unlock()

				if err != nil {
					if err != gateway.AlreadyCancelledErr {
						errMsg := fmt.Sprintf("failed to cancel order %s: %v", o.ID, err)
						result.Failed = append(result.Failed, errMsg)
						log.Printf("[AccountCleanup] Error: %s", errMsg)
					} else {
						result.Cancelled++
					}
				} else {
					result.Cancelled++
					log.Printf("[AccountCleanup] Successfully cancelled order %s", o.ID)
				}
			}
		}(order)
	}

	wg.Wait()
	return result
}

func isActiveOrder(state gateway.OrderState) bool {
	switch state {
	case gateway.OrderOpen,
		gateway.OrderPartiallyFilled,
		gateway.OrderSent:
		return true
	default:
		return false
	}
}
