package checks

import (
	"context"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
)

type OrderCheckFlow interface {
	Name() string
	Run(ctx context.Context, gtw gateway.Gateway) FlowResult
	Prerequisites() []Prerequisite
}

// Add these constants
const (
	MarketDataFlows = "market_data"
	AccountFlows    = "account"
	AllFlows        = "all"
)

func getFlowTypeDescription(flowType string) string {
	switch flowType {
	case MarketDataFlows:
		return "Market Data"
	case AccountFlows:
		return "Account"
	case AllFlows:
		return "Full Exchange"
	default:
		return "Unknown"
	}
}

// Options for order checks
type Options struct {
	TestMarket gateway.Market // The actual market to use for testing
	TestSymbol string         // The user-specified market symbol override
	FlowType   string         // "market_data", "account", "all"

	// Timeout settings
	FlowTimeout      time.Duration // Overall timeout for each flow
	OperationTimeout time.Duration // Timeout for individual operations (replaces BookTimeout & ExecutionTimeout)

	// Monitoring thresholds
	MaxDelay time.Duration // Maximum acceptable delay for any operation

	// Optional test parameters
	MinOrderValue float64 // Minimum order value to use for testing (optional)
	MaxOrderValue float64 // Maximum order value to use for testing (optional)
}
