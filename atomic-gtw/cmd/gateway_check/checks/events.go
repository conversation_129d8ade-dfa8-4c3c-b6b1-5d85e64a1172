package checks

import (
	"sync"
	"time"
)

type EventCollector struct {
	times map[EventType]time.Time
	mux   sync.RWMutex
}

func NewEventCollector() *EventCollector {
	return &EventCollector{
		times: make(map[EventType]time.Time),
	}
}

func (c *EventCollector) RecordTime(eventType EventType) {
	c.mux.Lock()
	c.times[eventType] = time.Now()
	c.mux.Unlock()
}

func (c *EventCollector) TimeBetween(from, to EventType) time.Duration {
	c.mux.RLock()
	defer c.mux.RUnlock()

	fromTime, ok1 := c.times[from]
	toTime, ok2 := c.times[to]

	if !ok1 || !ok2 {
		return 0
	}

	return toTime.Sub(fromTime)
}
