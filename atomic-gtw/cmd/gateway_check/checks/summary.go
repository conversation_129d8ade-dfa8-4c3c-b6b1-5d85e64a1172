package checks

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"
)

// SummaryOptions contains configuration for the summary generator
type SummaryOptions struct {
	ExchangeName string
	Environment  string
}

// Summary represents the complete test execution summary
type Summary struct {
	Exchange        string
	Environment     string
	ExecutionTime   time.Time
	TotalDuration   time.Duration
	OverallStatus   FlowStatus
	PassedFlows     int
	FailedFlows     int
	SkippedFlows    int
	TimeoutFlows    int
	DetailedResults []FlowSummary
	FlowType        string
}

// FlowSummary contains detailed information about a single flow execution
type FlowSummary struct {
	Name         string
	Status       FlowStatus
	Duration     time.Duration
	Error        string
	Measurements []Measurement
	Metadata     map[string]interface{}
}

// GenerateSummary creates a summary from check results
func GenerateSummary(results []FlowResult, flowType string, options SummaryOptions) *Summary {
	summary := &Summary{
		Exchange:        options.ExchangeName,
		Environment:     options.Environment,
		ExecutionTime:   time.Now(),
		DetailedResults: make([]FlowSummary, 0, len(results)),
		FlowType:        getFlowTypeDescription(flowType),
	}

	var totalDuration time.Duration
	for _, result := range results {
		flowSummary := FlowSummary{
			Name:         result.Name,
			Status:       result.Status,
			Duration:     result.Duration,
			Measurements: result.Measurements,
			Metadata:     result.Metadata,
		}

		if result.Error != nil {
			flowSummary.Error = result.Error.Error()
		}

		switch result.Status {
		case FlowPassed:
			summary.PassedFlows++
		case FlowFailed:
			summary.FailedFlows++
		case FlowSkipped:
			summary.SkippedFlows++
		case FlowTimeout:
			summary.TimeoutFlows++
		}

		totalDuration += result.Duration
		summary.DetailedResults = append(summary.DetailedResults, flowSummary)
	}

	summary.TotalDuration = totalDuration
	summary.OverallStatus = determineOverallStatus(summary)

	return summary
}

// determineOverallStatus calculates the overall status based on flow results
func determineOverallStatus(s *Summary) FlowStatus {
	if s.FailedFlows > 0 {
		return FlowFailed
	}
	if s.TimeoutFlows > 0 {
		return FlowTimeout
	}
	if s.PassedFlows == 0 && s.SkippedFlows > 0 {
		return FlowSkipped
	}
	return FlowPassed
}

// PrintConsole outputs the improved summary to console
func (s *Summary) PrintConsole() {
	printDetailedResults(s)
	printHeader(s)
	printSummary(s)
}

func printHeader(s *Summary) {
	fmt.Printf("\n%s\n", strings.Repeat("=", 80))
	fmt.Printf("📊 Gateway Check Summary - %s (%s) (%s)\n", s.Exchange, s.Environment, s.FlowType)
	fmt.Printf("%s\n\n", strings.Repeat("=", 80))

	fmt.Printf("🕒 Execution Time: %s\n", s.ExecutionTime.Format("2006-01-02 15:04:05.000"))
	fmt.Printf("⏱️  Total Duration: %s\n", formatDurationWithPrecision(s.TotalDuration))
	fmt.Printf("📈 Overall Status: %s\n\n", getStatusEmoji(s.OverallStatus)+" "+string(s.OverallStatus))
}

func printSummary(s *Summary) {
	fmt.Printf("Summary:\n")
	fmt.Printf("✅ Passed:  %d\tSkipped: %d\n", s.PassedFlows, s.SkippedFlows)
	fmt.Printf("❌ Failed:  %d\t⏰ Timeout: %d\n", s.FailedFlows, s.TimeoutFlows)
}

func printDetailedResults(s *Summary) {
	fmt.Printf("%s\n", strings.Repeat("-", 80))
	fmt.Printf("Detailed Results:\n")
	fmt.Printf("%s\n", strings.Repeat("-", 80))

	for _, flow := range s.DetailedResults {
		printFlowResult(flow)
	}
}

func printFlowResult(flow FlowSummary) {
	fmt.Printf("\n🔍 %s %s %s\n", flow.Name, getStatusEmoji(flow.Status), formatDurationWithPrecision(flow.Duration))

	if flow.Error != "" {
		fmt.Printf("\t⚠️  Error: %s\n", flow.Error)
	}

	if len(flow.Measurements) > 0 {
		printMeasurements(flow.Measurements)
	}

	if len(flow.Metadata) > 0 {
		printMetadata(flow.Metadata)
	}

	fmt.Printf("\n%s\n", strings.Repeat("-", 40))
}

func printMeasurements(measurements []Measurement) {
	fmt.Printf("\n\t📏 Measurements:\n")
	for _, m := range measurements {
		fmt.Printf("\t• %s: %s\n", getHumanReadableLabel(m.Name), formatDurationWithPrecision(m.Value))
	}
}

func printMetadata(metadata map[string]interface{}) {
	fmt.Printf("\n\t📋 Metadata:\n")
	for k, v := range metadata {
		fmt.Printf("\t• %s: %s\n", getHumanReadableLabel(k), formatMetadataValue(k, v))
	}
}

// SendToSlack sends the summary to a Slack webhook
func (s *Summary) SendToSlack(webhookURL string) error {
	message := s.createSlackMessage()

	payload, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal slack message: %w", err)
	}

	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(payload))
	if err != nil {
		return fmt.Errorf("failed to send slack message: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("slack API returned non-200 status: %d", resp.StatusCode)
	}

	return nil
}

func (s *Summary) createSlackMessage() map[string]interface{} {
	// Create main message header with larger text
	headerText := fmt.Sprintf("\n\n>🔄 *Gateway Check Results - %s %s (%s) - ⏱️ Took: %s*",
		s.Exchange,
		s.Environment,
		s.FlowType,
		formatDurationWithPrecision(s.TotalDuration),
	)

	// Create status summary with block quote for better visibility
	statusText := fmt.Sprintf(
		">*Status Summary*\n>✅ Passed: %d | ⏭️ Skipped: %d | ❌ Failed: %d | ⏰ Timeout: %d",
		s.PassedFlows, s.SkippedFlows, s.FailedFlows, s.TimeoutFlows,
	)

	// Create flow resume with proper alignment
	var flowResults strings.Builder
	flowResults.WriteString(">*Flow Resume*\n")

	// Find the longest flow name and number combination for padding
	maxLength := 0
	for i, flow := range s.DetailedResults {
		// Calculate total length including number prefix (e.g., "1. ")
		totalLength := len(fmt.Sprintf("%d. %s", i+1, flow.Name))
		if totalLength > maxLength {
			maxLength = totalLength
		}
	}

	// Format each flow result with aligned emojis
	for i, flow := range s.DetailedResults {
		// Format prefix (number + name)
		prefix := fmt.Sprintf("%d. %s", i+1, flow.Name)

		// Calculate padding needed to align emojis
		padding := strings.Repeat(" ", maxLength-len(prefix)+4)

		// Get status emoji
		var statusEmoji string
		switch flow.Status {
		case FlowPassed:
			statusEmoji = "✅"
		case FlowFailed:
			statusEmoji = "❌"
		case FlowSkipped:
			statusEmoji = "⏭️"
		case FlowTimeout:
			statusEmoji = "⏰"
		}

		// Format duration
		duration := formatDurationWithPrecision(flow.Duration)

		// Add the formatted line to results
		flowResults.WriteString(fmt.Sprintf(">%s%s%s (%s)\n", prefix, padding, statusEmoji, duration))
	}

	message := map[string]interface{}{
		"text":   fmt.Sprintf("\n\n%s\n%s\n%s\n\n", headerText, statusText, flowResults.String()),
		"mrkdwn": true,
	}

	var attachments []map[string]interface{}

	// Create a single attachment for each flow with collapsible content
	for _, flow := range s.DetailedResults {
		attachment := createFlowAttachment(flow)
		attachments = append(attachments, attachment)
	}

	message["attachments"] = attachments
	return message
}

func createFlowAttachment(flow FlowSummary) map[string]interface{} {
	color := getFlowStatusColor(flow.Status)

	// Create title text with better spacing
	titleText := fmt.Sprintf("*%s* %s (%s)", flow.Name, getStatusEmoji(flow.Status), formatDurationWithPrecision(flow.Duration))

	var mainText strings.Builder
	mainText.WriteString(titleText)

	// Add error section if present
	if flow.Error != "" {
		mainText.WriteString(fmt.Sprintf("\n\n>⚠️  *Error*\n>```%s```", flow.Error))
	}

	// Create details section
	if hasDetails := len(flow.Measurements) > 0 || len(flow.Metadata) > 0; hasDetails {
		detailsText := createDetailsSection(flow)
		mainText.WriteString(fmt.Sprintf("\n%s", detailsText))
	}

	return map[string]interface{}{
		"mrkdwn_in": []string{"text", "pretext"},
		"color":     color,
		"text":      mainText.String(),
		"fallback":  fmt.Sprintf("%s (%s) %s", flow.Name, formatDurationWithPrecision(flow.Duration), string(flow.Status)),
	}
}

func createDetailsSection(flow FlowSummary) string {
	var details strings.Builder

	// Add measurements section
	if len(flow.Measurements) > 0 {
		details.WriteString("```\n📊 Measurements\n")
		details.WriteString(strings.Repeat("─", 40) + "\n")

		// Find maximum measurement name length
		maxLength := 0
		for _, m := range flow.Measurements {
			label := fmt.Sprintf("• %s", getHumanReadableLabel(m.Name))
			if len(label) > maxLength {
				maxLength = len(label)
			}
		}

		// Print measurements with aligned colons
		for _, m := range flow.Measurements {
			label := fmt.Sprintf("• %s", getHumanReadableLabel(m.Name))
			padding := strings.Repeat(" ", maxLength-len(label))
			details.WriteString(fmt.Sprintf("%s%s: %s\n",
				label,
				padding,
				formatDurationWithPrecision(m.Value)))
		}
		details.WriteString("```\n")
	}

	// Add metrics section with two columns
	if len(flow.Metadata) > 0 {
		details.WriteString("```\n📋 Metrics\n")
		details.WriteString(strings.Repeat("─", 40) + "\n")

		// Split metrics into two columns
		metricPairs := make([][2]string, 0)

		for k, v := range flow.Metadata {
			metric := fmt.Sprintf("• %-20s: %s", getHumanReadableLabel(k), formatMetadataValue(k, v))

			if len(metricPairs) == 0 || len(metricPairs[len(metricPairs)-1]) == 2 {
				metricPairs = append(metricPairs, [2]string{metric, ""})
			} else {
				metricPairs[len(metricPairs)-1][1] = metric
			}
		}

		// Print metrics in two columns
		maxWidth := 40
		for _, pair := range metricPairs {
			padding := maxWidth - len(stripAnsi(pair[0]))
			if pair[1] != "" {
				details.WriteString(fmt.Sprintf("%s%s%s\n",
					pair[0],
					strings.Repeat(" ", padding),
					pair[1]))
			} else {
				details.WriteString(fmt.Sprintf("%s\n", pair[0]))
			}
		}
		details.WriteString("```")
	}

	return details.String()
}

// stripAnsi strip ANSI escape codes for length calculation
func stripAnsi(str string) string {
	var result strings.Builder
	inEscape := false

	for _, c := range str {
		if c == '\x1b' {
			inEscape = true
			continue
		}
		if !inEscape {
			result.WriteRune(c)
		}
		if inEscape && c == 'm' {
			inEscape = false
		}
	}
	return result.String()
}

func getFlowStatusColor(status FlowStatus) string {
	switch status {
	case FlowPassed:
		return "#36a64f" // Green
	case FlowFailed:
		return "#ff0000" // Red
	case FlowTimeout:
		return "#daa038" // Orange
	case FlowSkipped:
		return "#808080" // Gray
	default:
		return "#808080"
	}
}
