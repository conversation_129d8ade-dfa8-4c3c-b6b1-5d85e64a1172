package checks

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
	"github.com/herenow/atomic-tools/pkg/book"
)

type BalanceSnapshot struct {
	Timestamp    time.Time
	Stage        string
	BaseBalance  gateway.Balance
	QuoteBalance gateway.Balance
}

type OrderExecutionFlow struct {
	options     Options
	tickManager *TickManager
	evTimer     *EventCollector
}

type executionProgress struct {
	firstExecutionTime time.Time
	lastExecutionTime  time.Time
	orderCreatedTime   time.Time
	fillsReceived      int
	totalFilled        float64
	averagePrice       float64
	totalValue         float64
	orderFullyFilled   bool
}

func NewOrderExecutionFlow(options Options, tickManager *TickManager) *OrderExecutionFlow {
	return &OrderExecutionFlow{
		options:     options,
		tickManager: tickManager,
		evTimer:     NewEventCollector(),
	}
}

func (f *OrderExecutionFlow) Name() string {
	return "OrderExecution"
}

func (f *OrderExecutionFlow) Prerequisites() []Prerequisite {
	return []Prerequisite{
		sufficientBalancePrereq(f.options),
	}
}

func (f *OrderExecutionFlow) Run(ctx context.Context, gtw gateway.Gateway) FlowResult {
	start := time.Now()

	orderUpdatesStream, orderSub := f.tickManager.SubscribeOrderUpdates()
	defer orderSub.Close()

	orderExecutionStream, execSub := f.tickManager.SubscribeOrderExecutions()
	defer execSub.Close()

	progress := &executionProgress{}

	balanceSnapshots := make([]BalanceSnapshot, 0)

	balanceSnapshots, flowErr := f.getBalanceSnapshot(gtw, "pre-trade", balanceSnapshots, start)
	if flowErr != nil {
		return *flowErr
	}

	ob, ok := f.tickManager.GetBook(f.options.TestMarket.Symbol)
	if !ok {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("order book not found for %s", f.options.TestMarket.Symbol),
		}
	}

	side, err := f.determineOrderSide(gtw, ob)
	if err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to determine order side: %w", err),
		}
	}

	price, amount, err := f.calculateOrderParams(side, ob)
	if err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to calculate order parameters: %w", err),
		}
	}

	log.Printf("[OrderExecution] Placing %s order: price=%.8f amount=%.8f", side, price, amount)

	order := gateway.Order{
		Market: f.options.TestMarket,
		Side:   side,
		Type:   gateway.LimitOrder,
		Amount: amount,
		Price:  price,
	}

	f.evTimer.RecordTime(EventOrderSent)
	orderID, err := gtw.AccountGateway().SendOrder(order)
	if err != nil {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to place order: %w", err),
			Metadata: map[string]interface{}{
				"orderPrice":        order.Price,
				"orderAmount":       order.Amount,
				"orderValue":        price * amount,
				"minimumOrderSize":  utils.FloatToStringWithTick(order.Market.MinimumOrderSize, order.Market.AmountTick),
				"minimumOrderValue": order.Market.MinimumOrderMoneyValue,
				"market":            order.Market.Symbol,
				"side":              order.Side,
				"quoteAsset":        order.Market.Pair.Quote,
				"baseAsset":         order.Market.Pair.Base,
			},
		}
	}

	order.ID = orderID
	f.tickManager.LoadOrder(order)

	balanceSnapshots, flowErr = f.getBalanceSnapshot(gtw, "post-trade", balanceSnapshots, start)
	if flowErr != nil {
		return *flowErr
	}

	updateCtx, cancel := context.WithTimeout(ctx, f.options.FlowTimeout)
	defer cancel()

	for {
		select {
		case <-updateCtx.Done():
			if err = gtw.AccountGateway().CancelOrder(order); err != nil {
				log.Printf("Warning: Failed to cancel order on timeout: %v", err)
			}
			return FlowResult{
				Name:     f.Name(),
				Status:   FlowTimeout,
				Duration: time.Since(start),
				Error:    fmt.Errorf("timeout waiting for order updates"),
			}

		case update := <-orderUpdatesStream:
			log.Printf("[OrderExecution] Received order update ID=%s State=%s Filled=%.8f/%.8f",
				update.ID, update.State, update.FilledAmount, update.Amount)

			if update.ID == orderID {
				f.processOrderUpdate(update, progress, order)

				if isTerminalState(update.State) {
					return f.handleTerminalState(gtw, start, order, update, balanceSnapshots, progress)
				}
			}

		case execution := <-orderExecutionStream:
			log.Printf("[OrderExecution] Received execution: OrderID=%s Amount=%.8f Price=%.8f",
				execution.OrderID, execution.Amount, execution.Price)

			if execution.OrderID == orderID {
				if err = f.processExecution(execution, progress, order); err != nil {
					return FlowResult{
						Name:     f.Name(),
						Status:   FlowFailed,
						Duration: time.Since(start),
						Error:    fmt.Errorf("execution processing error: %w", err),
					}
				}

				// Only check terminal state after processing execution
				execOrder, ok := f.tickManager.GetOrder(orderID)
				if ok && isTerminalState(execOrder.State) && progress.orderFullyFilled {
					return f.handleTerminalState(gtw, start, execOrder, execOrder, balanceSnapshots, progress)
				}
			}
		}
	}
}

func (f *OrderExecutionFlow) processOrderUpdate(update gateway.Order, progress *executionProgress, originalOrder gateway.Order) {
	now := time.Now()

	// Update order creation time if this is the first open state
	if update.State == gateway.OrderOpen && progress.orderCreatedTime.IsZero() {
		progress.orderCreatedTime = now
		f.evTimer.RecordTime(EventOrderCreated)
	}

	// Handle fully filled state
	if update.State == gateway.OrderFullyFilled {
		progress.orderFullyFilled = true
		f.evTimer.RecordTime(EventOrderFullyExecuted)

		// If we haven't received any fills yet but the order is fully filled,
		// update the progress with the fill information from the order update
		if progress.fillsReceived == 0 {
			progress.fillsReceived = 1
			progress.firstExecutionTime = now
			progress.lastExecutionTime = now
			progress.totalFilled = update.FilledAmount
			progress.totalValue = update.FilledAmount * update.AvgPrice
			progress.averagePrice = update.AvgPrice

			// Record execution events
			f.evTimer.RecordTime(EventOrderExecuted)
			f.evTimer.RecordTime(EventOrderFullyExecuted)

			log.Printf("[OrderExecution] Order fully filled from update: amount=%.8f avg_price=%.8f total_value=%.8f",
				update.FilledAmount,
				update.AvgPrice,
				progress.totalValue)
		}
	}
}

func (f *OrderExecutionFlow) processExecution(execution gateway.Execution, progress *executionProgress, originalOrder gateway.Order) error {
	now := time.Now()

	// Record first execution timing, but only if we've seen the order created event
	if progress.fillsReceived == 0 {
		if !progress.orderCreatedTime.IsZero() {
			progress.firstExecutionTime = now
			f.evTimer.RecordTime(EventOrderExecuted)

			// For partially filled orders, also record the partial execution event
			if execution.Amount < originalOrder.Amount {
				f.evTimer.RecordTime(EventOrderPartiallyExecuted)
			}
		} else {
			progress.firstExecutionTime = now
		}
	} else {
		// Record partial execution for subsequent fills that don't complete the order
		totalFilledAfter := progress.totalFilled + execution.Amount
		if totalFilledAfter < originalOrder.Amount {
			f.evTimer.RecordTime(EventOrderPartiallyExecuted)
		}
		progress.lastExecutionTime = now
	}

	// Update execution progress
	progress.fillsReceived++
	progress.totalFilled += execution.Amount
	progress.totalValue += execution.Amount * execution.Price

	if progress.totalFilled > 0 {
		progress.averagePrice = progress.totalValue / progress.totalFilled
	}

	// Check if this fill completes the order
	if progress.totalFilled >= originalOrder.Amount {
		f.evTimer.RecordTime(EventOrderFullyExecuted)
	}

	fillPct := (progress.totalFilled / originalOrder.Amount) * 100

	log.Printf("[OrderExecution] fill received: id=%s amount=%.8f price=%.8f (fill #%d, %.2f%% complete, avg price: %.8f)",
		execution.OrderID,
		execution.Amount,
		execution.Price,
		progress.fillsReceived,
		fillPct,
		progress.averagePrice)

	if execution.Price == 0 {
		return fmt.Errorf("received execution with zero price")
	}

	return nil
}

func (f *OrderExecutionFlow) handleTerminalState(gtw gateway.Gateway, start time.Time,
	order gateway.Order, update gateway.Order, snapshots []BalanceSnapshot, progress *executionProgress) FlowResult {

	snapshots, flowErr := f.getBalanceSnapshot(gtw, "final-trade", snapshots, start)
	if flowErr != nil {
		return *flowErr
	}

	// Calculate timing measurements
	initialDelay := f.evTimer.TimeBetween(EventOrderSent, EventOrderCreated)
	timeToFirstFill := f.evTimer.TimeBetween(EventOrderCreated, EventOrderExecuted)
	timeToComplete := f.evTimer.TimeBetween(EventOrderExecuted, EventOrderFullyExecuted)

	// Only calculate partial execution timings if we had multiple fills
	var partialExecutionTime time.Duration
	if progress.fillsReceived > 1 {
		// Time between first partial execution and final execution
		partialExecutionTime = f.evTimer.TimeBetween(EventOrderPartiallyExecuted, EventOrderFullyExecuted)
	}

	var executionTimeDescription string
	if progress.fillsReceived > 1 {
		executionTimeDescription = fmt.Sprintf("Total execution time: %v (including partial fills)", timeToComplete)
	} else {
		executionTimeDescription = "Single fill execution"
	}

	log.Printf("[OrderExecution] Order complete - Initial delay: %v, Time to first fill: %v, %s",
		initialDelay, timeToFirstFill, executionTimeDescription)

	measurements := []Measurement{
		{
			Name:  "InitialUpdateDelay",
			Value: initialDelay,
			Tags:  map[string]string{"market": order.Market.Symbol},
		},
		{
			Name:  "TimeToFirstFill",
			Value: timeToFirstFill,
			Tags:  map[string]string{"market": order.Market.Symbol},
		},
	}

	if progress.fillsReceived > 1 {
		measurements = append(measurements,
			Measurement{
				Name:  "TimeInPartialState",
				Value: partialExecutionTime,
				Tags:  map[string]string{"market": order.Market.Symbol},
			},
			Measurement{
				Name:  "TotalExecutionTime",
				Value: timeToComplete,
				Tags:  map[string]string{"market": order.Market.Symbol},
			},
		)
	}

	return FlowResult{
		Name:         f.Name(),
		Status:       FlowPassed,
		Duration:     time.Since(start),
		Measurements: measurements,
		Metadata: map[string]interface{}{
			"orderID":          order.ID,
			"orderUpdate":      update,
			"fillsReceived":    progress.fillsReceived,
			"totalFilled":      utils.FloatToStringWithTick(progress.totalFilled, order.Market.AmountTick),
			"averagePrice":     progress.averagePrice,
			"balanceSnapshots": snapshots,
			"executionTiming": map[string]interface{}{
				"firstFill":       progress.firstExecutionTime,
				"lastFill":        progress.lastExecutionTime,
				"fillCount":       progress.fillsReceived,
				"averagePrice":    progress.averagePrice,
				"totalValue":      progress.totalValue,
				"singleFill":      progress.fillsReceived == 1,
				"partiallyFilled": progress.fillsReceived > 1,
			},
		},
	}
}

func (f *OrderExecutionFlow) calculateOrderParams(side gateway.Side, book *book.Book) (price, amount float64, err error) {
	switch side {
	case gateway.Bid:
		// For bids, place slightly below best ask to ensure execution
		bestAsk, ok := book.Asks.Top()
		if !ok {
			return 0, 0, fmt.Errorf("no asks in order book")
		}
		price = bestAsk.Value * 1.01 // 1% below best ask to ensure execution

	case gateway.Ask:
		// For asks, place slightly above best bid
		bestBid, ok := book.Bids.Top()
		if !ok {
			return 0, 0, fmt.Errorf("no bids in order book")
		}
		price = bestBid.Value * 0.99 // 1% above best bid to ensure execution
	}

	// Calculate minimum amount that satisfies both minimum size and minimum notional value
	minAmount := f.options.TestMarket.MinimumOrderSize
	minNotional := f.options.TestMarket.MinimumOrderMoneyValue

	if minNotional == 0 {
		minNotional = 11 // Default to slightly above 10 if not specified
	}

	// Calculate required amount to meet minimum notional
	requiredAmount := minNotional / price
	if requiredAmount > minAmount {
		minAmount = requiredAmount
	}

	// Add 10% to ensure we're safely above minimums
	amount = utils.FloorToTick(minAmount*1.1, f.options.TestMarket.AmountTick)

	// Check if we need to adjust the amount to meet minimum notional
	for amount*price < minNotional {
		// Increment amount by one tick until we meet the minimum notional
		amount = utils.FloorToTick(amount+f.options.TestMarket.AmountTick, f.options.TestMarket.AmountTick)
	}

	// Final validation
	if amount*price < minNotional {
		return 0, 0, fmt.Errorf(
			"unable to meet minimum notional value requirement: got %.8f %s, need %.8f %s (price=%.8f, amount=%.8f)",
			price*amount,
			f.options.TestMarket.Pair.Quote,
			minNotional,
			f.options.TestMarket.Pair.Quote,
			price,
			amount,
		)
	}

	log.Printf("[OrderExecution] Calculated order parameters: side=%s price=%.8f amount=%.8f total_value=%.8f %s (min_notional=%.8f)",
		side, price, amount, price*amount, f.options.TestMarket.Pair.Quote, minNotional)

	return price, amount, nil
}

func (f *OrderExecutionFlow) determineOrderSide(gtw gateway.Gateway, book *book.Book) (gateway.Side, error) {
	balances, err := gtw.AccountGateway().Balances()
	if err != nil {
		return "", fmt.Errorf("failed to get balances: %w", err)
	}

	baseBalance, hasBase := gateway.BalanceByAsset(balances, f.options.TestMarket.Pair.Base)
	quoteBalance, hasQuote := gateway.BalanceByAsset(balances, f.options.TestMarket.Pair.Quote)

	if !hasBase && !hasQuote {
		return "", fmt.Errorf("no balance found for either %s or %s",
			f.options.TestMarket.Pair.Base,
			f.options.TestMarket.Pair.Quote)
	}

	// Get current market price from orderbook
	bestAsk, hasAsk := book.Asks.Top()
	if !hasAsk {
		return "", fmt.Errorf("no ask price available in orderbook")
	}

	// Calculate minimum required amounts
	minBaseAmount := f.options.TestMarket.MinimumOrderSize
	minQuoteAmount := minBaseAmount * bestAsk.Value

	// Check which side has sufficient balance
	canSell := hasBase && baseBalance.Available >= minBaseAmount
	canBuy := hasQuote && quoteBalance.Available >= minQuoteAmount

	switch {
	case canBuy && canSell:
		if rand.Int31()%2 == 0 {
			return gateway.Ask, nil
		}
		return gateway.Bid, nil
	case canSell:
		return gateway.Ask, nil
	case canBuy:
		return gateway.Bid, nil
	default:
		return "", fmt.Errorf("insufficient balance for either side")
	}
}

func (f *OrderExecutionFlow) getBalanceSnapshot(gtw gateway.Gateway, stage string, snapshots []BalanceSnapshot, start time.Time) ([]BalanceSnapshot, *FlowResult) {
	balances, err := gtw.AccountGateway().Balances()
	if err != nil {
		return nil, &FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to get balances: %w", err),
		}
	}

	base, ok := gateway.BalanceByAsset(balances, f.options.TestMarket.Pair.Base)
	if !ok {
		return nil, &FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("%s balance not found", f.options.TestMarket.Pair.Base),
		}
	}

	quote, ok := gateway.BalanceByAsset(balances, f.options.TestMarket.Pair.Quote)
	if !ok {
		return nil, &FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("%s balance not found", f.options.TestMarket.Pair.Quote),
		}
	}

	snapshots = append(snapshots, BalanceSnapshot{
		Timestamp:    time.Now(),
		Stage:        stage,
		BaseBalance:  base,
		QuoteBalance: quote,
	})

	return snapshots, nil
}

func isTerminalState(state gateway.OrderState) bool {
	return state == gateway.OrderFullyFilled ||
		state == gateway.OrderCancelled ||
		state == gateway.OrderClosed
}
