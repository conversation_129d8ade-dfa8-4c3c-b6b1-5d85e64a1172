package checks

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/herenow/atomic-gtw/gateway"
	"github.com/herenow/atomic-gtw/utils"
)

type MinOrderSizeFlow struct {
	options     Options
	tickManager *TickManager
}

func NewMinOrderSizeFlow(options Options, tickManager *TickManager) *MinOrderSizeFlow {
	return &MinOrderSizeFlow{
		options:     options,
		tickManager: tickManager,
	}
}

func (f *MinOrderSizeFlow) Name() string {
	return "MinOrderSize"
}

func (f *MinOrderSizeFlow) Prerequisites() []Prerequisite {
	return []Prerequisite{
		{
			Name:        "Market Minimum Size",
			Description: "Check if market has minimum order size defined",
			Validate: func(gtw gateway.Gateway) error {
				if f.options.TestMarket.MinimumOrderSize == 0 {
					return fmt.Errorf("market %s does not have minimum order size defined", f.options.TestMarket.Symbol)
				}
				return nil
			},
		},
		sufficientBalancePrereq(f.options),
	}
}

func (f *MinOrderSizeFlow) Run(ctx context.Context, gtw gateway.Gateway) FlowResult {
	start := time.Now()

	// Get book reference
	book, ok := f.tickManager.GetBook(f.options.TestMarket.Symbol)
	if !ok {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("order book not found for %s", f.options.TestMarket.Symbol),
		}
	}

	// Get the current best bid price to place our bid below
	bestBid, hasBid := book.Bids.Top()
	if !hasBid {
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("no bids in order book"),
		}
	}

	// Place the price 20% below market to avoid execution
	price := utils.FloorToTick(bestBid.Value*0.8, f.options.TestMarket.PriceTick)

	// Set up order update subscription
	orderCh, orderSub := f.tickManager.SubscribeOrderUpdates()
	defer orderSub.Close()

	log.Printf("[MinOrderSize] Testing with price=%.8f amount=0 (min=%.8f)",
		price, f.options.TestMarket.MinimumOrderSize)

	// Attempt to place order with zero amount
	order := gateway.Order{
		Market: f.options.TestMarket,
		Side:   gateway.Bid,
		Type:   gateway.LimitOrder,
		Amount: 0,
		Price:  price,
	}

	// Attempt to place order with zero amount
	orderID, err := gtw.AccountGateway().SendOrder(order)

	// Store order result for metadata
	metadata := map[string]interface{}{
		"price":           price,
		"minOrderSize":    f.options.TestMarket.MinimumOrderSize,
		"errorReceived":   "",
		"errorValidation": "",
	}

	if err == nil {
		order.ID = orderID
		metadata["orderID"] = orderID

		// Unexpected: Order was accepted despite zero amount
		// Cancel it immediately and monitor the cancellation
		log.Printf("[MinOrderSize] WARNING: Zero amount order was unexpectedly accepted (ID: %s), attempting to cancel", orderID)

		cancelErr := gtw.AccountGateway().CancelOrder(order)
		if cancelErr != nil {
			log.Printf("[MinOrderSize] ERROR: Failed to cancel unexpectedly accepted order: %v", cancelErr)
			metadata["cancelError"] = cancelErr.Error()
		}

		// Monitor order updates to ensure cancellation
		timeout := time.After(5 * time.Second)
		for {
			select {
			case update := <-orderCh:
				if update.ID == orderID {
					metadata["finalState"] = update.State
					if update.State == gateway.OrderCancelled {
						return FlowResult{
							Name:     f.Name(),
							Status:   FlowFailed,
							Duration: time.Since(start),
							Error:    fmt.Errorf("order was unexpectedly accepted but successfully cancelled"),
							Metadata: metadata,
						}
					}
				}
			case <-timeout:
				// Timeout waiting for cancellation confirmation
				return FlowResult{
					Name:     f.Name(),
					Status:   FlowFailed,
					Duration: time.Since(start),
					Error:    fmt.Errorf("order was unexpectedly accepted and cancellation status unknown"),
					Metadata: metadata,
				}
			}
		}
	}

	// Expected case: Order should be rejected with MinOrderSizeErr
	metadata["errorReceived"] = err.Error()

	if !errors.Is(err, gateway.MinOrderSizeErr) {
		metadata["errorValidation"] = fmt.Sprintf("Expected MinOrderSizeErr but got: %v", err)
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("expected MinOrderSizeErr but got: %w", err),
			Metadata: metadata,
		}
	}

	// Monitor for any unexpected updates
	select {
	case update := <-orderCh:
		metadata["unexpectedUpdate"] = fmt.Sprintf("%+v", update)
		return FlowResult{
			Name:     f.Name(),
			Status:   FlowFailed,
			Duration: time.Since(start),
			Error:    fmt.Errorf("received unexpected order update for rejected order: %v", update),
			Metadata: metadata,
		}
	case <-time.After(2 * time.Second):
		// Expected behavior - no updates should come through
	}

	metadata["errorValidation"] = "Received expected MinOrderSizeErr"
	return FlowResult{
		Name:     f.Name(),
		Status:   FlowPassed,
		Duration: time.Since(start),
		Metadata: metadata,
	}
}
