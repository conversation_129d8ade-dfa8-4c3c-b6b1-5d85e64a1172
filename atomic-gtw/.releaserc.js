module.exports = {
  branches: ['master'],
  plugins: [
    ['@semantic-release/commit-analyzer', {
      preset: 'conventionalcommits',
      releaseRules: [
        // 0.x.0
        { type: 'feat', release: 'minor' },

        // 0.0.x
        { type: 'fix',      release: 'patch' },
        { type: 'refactor', release: 'patch' },
        { type: 'chore',    release: 'patch' },

        // 0.0.0
        { type: 'docs',         release: false },
        { type: 'test',         release: false },
        { type: 'ci',           release: false },
        { scope: 'no-release',  release: false },
      ],
      parserOpts: {
        noteKeywords: ['BREAKING CHANGE', 'BREAKING CHANGES'],
      },
    }],
    ['@semantic-release/release-notes-generator', {
      preset: 'conventionalcommits',
      presetConfig: {
        types: [
          /**
           * @see
           * https://github.com/semantic-release/release-notes-generator
           * https://github.com/conventional-changelog/conventional-changelog-config-spec/blob/master/versions/2.0.0/README.md
           */
          { type: 'feat',            section: '✨ Features',         hidden: false },
          { type: 'fix',             section: '🐛 Bug Fixes',        hidden: false },
          { type: 'docs',            section: '📝 Docs',             hidden: false },
          { type: 'refactor',        section: '♻️ Refactor',         hidden: false },
          { type: 'revert',          section: '🕐 Reverts',          hidden: false },
          { type: 'ci',              section: '💫 CI/CD',            hidden: false },
          { type: 'test',            section: '✅ Tests',            hidden: false },
          { type: 'chore',           section: '📦 Chores',           hidden: false },
          { type: 'BREAKING CHANGE', section: '⚠️ Breaking Changes', hidden: false },
        ],
      },
    }],
    ['@semantic-release/changelog', {
      changelogFile: 'CHANGELOG.md',
      changelogTitle: '# 🚦 Atomic Funds Changelog',
    }],
    ['@semantic-release/git',{
      assets: ['CHANGELOG.md'],
    }],
    ['@semantic-release/github', {
      assets: [{ path: 'CHANGELOG.md', label: 'changelog' }],
    }],
  ],
}