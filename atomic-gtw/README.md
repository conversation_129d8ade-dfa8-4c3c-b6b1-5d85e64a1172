# Atomic Gateway

## Supported exchanges

| Icon                                          | Name           | Symbol           | Order Entry | Market Data | Status      |
|-----------------------------------------------|----------------|------------------|-------------|-------------|-------------|
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1117.png" alt="Azbit" width="24" height="24"> | Azbit | azbit | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/9452.png" alt="Backpack" width="24" height="24"> | Backpack | backpack | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/937.png" alt="Biconomy" width="24" height="24"> | Biconomy | biconomy | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/330.png" alt="BigONE" width="24" height="24"> | BigONE | bigone | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/270.png" alt="Binance" width="24" height="24"> | Binance | binance | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/270.png" alt="Binance Futures" width="24" height="24"> | Binance Futures | binancefutures | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1161.png" alt="Bit" width="24" height="24"> | Bit.com | bit | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/37.png" alt="Bitfinex" width="24" height="24"> | Bitfinex | bitfinex | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/513.png" alt="Bitget" width="24" height="24"> | Bitget | bitget | ✓ | ✓ | Maintained |
| <img src="https://cdn.coinranking.com/0KoAUbthl/bitmart.svg?size=72x72" alt="BitMart" width="24" height="24" style="background: #fff"> | BitMart | bitmart | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/157.png" alt="Bitmex" width="24" height="24"> | Bitmex | bitmex | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/433.png" alt="Bitrue" width="24" height="24"> | Bitrue | bitrue | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/125.png" alt="Bitso" width="24" height="24"> | Bitso | bitso | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/70.png" alt="Bitstamp" width="24" height="24"> | Bitstamp | bitstamp | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1239.png" alt="Bitypreco" width="24" height="24"> | Bitypreco | bitypreco | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/521.png" alt="Bybit" width="24" height="24"> | Bybit | bybit | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/997.png" alt="Chiliz" width="24" height="24"> | Chiliz | chiliz | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/89.png" alt="Coinbase" width="24" height="24"> | Coinbase | coinbase | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/5344.png" alt="Coins.ph" width="24" height="24"> | Coins.ph | coinsph | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/460.png" alt="Coinsbit" width="24" height="24"> | Coinsbit | coinsbit | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1411.png" alt="Coinstore" width="24" height="24"> | Coinstore | coinstore | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/391.png" alt="CoinTiger" width="24" height="24"> | CoinTiger | cointiger | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1149.png" alt="Crypto.com" width="24" height="24"> | Crypto.com | cryptocom | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/290.png" alt="CryptoMarket" width="24" height="24"> | CryptoMarket | cryptomkt | ✓ | ✓ | Maintained |
| <img src="https://currencylayer.com/site_images/currencylayer_icon.png" alt="CurrencyLayer" width="24" height="24"> | CurrencyLayer | currencylayer | ✓ | X | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/522.png" alt="Deribit" width="24" height="24"> | Deribit | deribit | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/407.png" alt="DigiFinex" width="24" height="24"> | DigiFinex | digifinex | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/10599.png" alt="Digitra" width="24" height="24"> | Digitra | digitra | ✓ | ✓ | Maintained |
| <img src="https://cdn.prod.website-files.com/5fd63383c2fe7919cf1f0148/6228e79a88aa497a4758d5af_ff-256x256.png" alt="FastForex" width="24" height="24"> | FastForex | fastforex | ✓ | X | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/857.png" alt="FMFW" width="24" height="24"> | FMFW | fmfw | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1215.png" alt="Foxbit" width="24" height="24"> | Foxbit | foxbit | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/302.png" alt="Gate.io" width="24" height="24"> | Gate.io | gateio | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/151.png" alt="Gemini" width="24" height="24"> | Gemini | gemini | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/943.png" alt="Hotcoin" width="24" height="24"> | Hotcoin | hotcoin | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/102.png" alt="Huobi" width="24" height="24"> | Huobi | huobi | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/8112.png" alt="Hyperliquid" width="24" height="24"> | Hyperliquid | hyperliquid | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1524.png" alt="Klever" width="24" height="24"> | Klever | klever | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/24.png" alt="Kraken" width="24" height="24"> | Kraken | kraken | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/311.png" alt="KuCoin" width="24" height="24"> | KuCoin | kucoin | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/380.png" alt="LAToken" width="24" height="24"> | LAToken | latoken | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/333.png" alt="LBank" width="24" height="24"> | LBank | lbank | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/258.png" alt="Mercado Bitcoin" width="24" height="24"> | Mercado Bitcoin | mercadobitcoin | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/544.png" alt="MEXC" width="24" height="24"> | MEXC | mexc | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/622.png" alt="NovaDAX" width="24" height="24"> | NovaDAX | novadax | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/294.png" alt="OKX" width="24" height="24"> | OKX | okx | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/597.png" alt="OneTrading" width="24" height="24"> | OneTrading | onetrading | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/501.png" alt="P2PB2B" width="24" height="24"> | P2PB2B | p2pb2b | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/501.png" alt="Probit" width="24" height="24"> | Probit | probit | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1100.png" alt="Ripio" width="24" height="24"> | Ripio | ripio | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1133.png" alt="TruBit" width="24" height="24"> | TruBit | trubit | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1069.png" alt="Uniswap V3" width="24" height="24"> | Uniswap V3 | uniswapv3 | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1069.png" alt="Uniswap V4" width="24" height="24"> | Uniswap V4 | temp_uniswapv4_evawbtc | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/351.png" alt="Upbit" width="24" height="24"> | Upbit | upbit | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/908.png" alt="VALR" width="24" height="24"> | VALR | valr | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/501.png" alt="WhiteBit" width="24" height="24"> | WhiteBit | whitebit | ✓ | ✓ | Maintained |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/525.png" alt="XT.com" width="24" height="24"> | XT.com | xt | ✓ | ✓ | Maintained |
| <img src="https://www.mercadocripto.livecoins.com.br/static/media/exchangelogo.a1d6c645fe4d4c7d1be10a26869e7870.svg" alt="" width="24" height="24"> | Bitcoin Trade | bitcointrade | ✓ | ✓ | **Deprecated (acquired by ripio)** |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/1165.png" alt="PancakeSwap" width="24" height="24"> | PancakeSwap | pancakeswap | ✓ | ✓ | **Deprecated (needs to be updated)** |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/524.png" alt="FTX" width="24" height="24"> | FTX | ftx | ✓ | ✓ | **Deprecated (bankrupt)** |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/400.png" alt="Hotbit" width="24" height="24"> | Hotbit | hotbit | ✓ | ✓ | **Deprecated (closed)** |
| <img src="https://cdn.coinranking.com/rhDZXgGuA/exmarkets.svg?size=48x48" alt="Exmarkets" width="24" height="24"> | Exmarkets | exmarkets | ✓ | ✓ | **Deprecated (disappeared)** |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/300.png" alt="Bitz" width="24" height="24"> | Bitz | bitz | ✓ | ✓ | **Deprecated (disappeared)** |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/368.png" alt="IDAX" width="24" height="24"> | IDAX | idax | ✓ | ✓ | **Deprecated (disappeared)** |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/482.png" alt="BKEX" width="24" height="24"> | BKEX | bkex | ✓ | ✓ | **Deprecated (disappeared)** |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/22.png" alt="Bittrex" width="24" height="24"> | Bittrex | bittrex | ✓ | ✓ | **Deprecated (closed)** |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/489.png" alt="BitGlobal" width="24" height="24"> | BitGlobal | bitglobal | ✓ | ✓ | **Deprecated (disappeared)** |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/403.png" alt="BitForex" width="24" height="24"> | BitForex | bitforex | ✓ | ✓ | **Deprecated (closed)** |
| <img src="https://s2.coinmarketcap.com/static/img/exchanges/64x64/343.png" alt="Coinbene" width="24" height="24"> | Coinbene | coinbene | ✓ | ✓ | **Deprecated (closed)** |

## Commit Message Structure

The commit message should be structured as follows:
For more information, refer to the [Conventional Commits 1.0.0](https://www.conventionalcommits.org/en/v1.0.0/).

```git
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

- `fix:` patches a bug in your codebase (correlates with `PATCH` in Semantic Versioning).
- `feat:` introduces a new feature to the codebase (correlates with `MINOR` in Semantic Versioning).
- `BREAKING CHANGE:` introduces a breaking API change (correlates with `MAJOR` in Semantic Versioning).
- Other types allowed: `build:`, `chore:`, `ci:`, `docs:`, `style:`, `refactor:`, `perf:`, `test:`, and others.
- Footers can be added with additional information.

## Semantic Versioning
Version numbers follow the `MAJOR`.`MINOR`.`PATCH` format. Increment the version as follows:

`MAJOR` version: Incompatible API changes.

`MINOR` version: Backward-compatible functionality additions.

`PATCH` version: Backward-compatible bug fixes.

Additional labels for pre-release and build metadata are available as extensions to the `MAJOR`.`MINOR`.`PATCH` format.

For more information, refer to the [Semantic Versioning 2.0.0 Specification](https://semver.org/spec/v2.0.0.html#semantic-versioning-200).

### Examples

#### Releases: 
- `1.0.0`: Initial release.
- `1.1.0`: Backward-compatible feature added.
- `1.1.1`: Backward-compatible bug fix.
- `2.0.0`: Incompatible API change.
- `2.0.1-alpha`: Pre-release version of the 2.0.1 patch release.
- `2.0.1-alpha.1`: Pre-release version with additional changes before the official release.
- `2.0.1-beta`: Pre-release version indicating an unstable release candidate.
- `2.0.1-rc.1`: Pre-release version indicating the first release candidate.
- `2.0.1+build123`: Patch release with build metadata.


#### Commits:
```git
feat(config)!: allow provided config object to extend other configs

BREAKING CHANGE: extends key in config file is now used for 
extending other config files
```

```git
feat!: send an email to the customer when a product is shipped
```

```git
feat(api)!: send an email to the customer when a product is shipped
```

```git
chore!: drop support for Node 6
```
```git
BREAKING CHANGE: use JavaScript features not available in Node 6.
```

```git
docs: correct spelling of CHANGELOG
```

```git
fix: prevent racing of requests

Introduce a request ID and a reference to the latest request. Dismiss
incoming responses other than from the latest request.

Reviewed-by: Z
Refs: #123
```

### Commit Guidelines

- Commits `MUST` start with a `type` and can have an `optional scope`.
- Types: `feat` for new features, `fix` for bug fixes.
- A `scope` describes a section of the codebase.
- `Description` is a short summary of the changes.
- Additional information can be added in the `body` and `footers`.
- Breaking changes indicated with `BREAKING CHANGE` or `!` before the colon.
