package base

import (
	"log"

	"github.com/herenow/atomic-gtw/gateway"
)

var baseAccountGateway = &AccountGateway{}
var baseTickCh = make(chan gateway.Tick)

func NewGateway(gtw gateway.Gateway) Gateway {
	return Gateway{
		gtwInstance: gtw,
	}
}

type Gateway struct {
	gtwInstance   gateway.Gateway
	cachedMarkets []gateway.Market
}

func (g *Gateway) Connect() error {
	return gateway.NotImplementedErr
}

func (g *Gateway) Close() error {
	return gateway.NotImplementedErr
}

func (g *Gateway) Exchange() gateway.Exchange {
	return gateway.Exchange{Name: "Gateway"}
}

// TODO: This will be removed, once, be fully deprecate the Markets() method
func (g *Gateway) Markets() []gateway.Market {
	gtwInstance := g.gtwInstance
	if gtwInstance == nil {
		return []gateway.Market{}
	}

	if g.cachedMarkets != nil {
		log.Printf("Returning cached markets")
		return g.cachedMarkets
	}

	exchange := g.gtwInstance.Exchange()
	log.Printf("%s - DEPRECATION NOTICE - Markets() will be removed in the future, please use GetMarkets() instead", exchange)
	log.Printf("%s Markets() not implemented returning GetMarkets() and caching response instead", exchange)
	markets, err := gtwInstance.GetMarkets()
	if err != nil {
		log.Printf("%s GetMarkets() failed, err %s", exchange, err)
	}

	g.cachedMarkets = markets

	return markets
}

func (g *Gateway) GetMarkets() ([]gateway.Market, error) {
	if g.gtwInstance == nil {
		return []gateway.Market{}, gateway.NotImplementedErr
	}

	log.Printf("%s GetMarkets() not implemented - returning Markets() instead ", g.gtwInstance.Exchange())

	return g.gtwInstance.Markets(), nil
}

func (g *Gateway) GetDepthBook(_ gateway.Market, _ gateway.GetDepthParams) (gateway.DepthBook, error) {
	return gateway.DepthBook{}, gateway.NotImplementedErr
}

func (g *Gateway) SubscribeMarkets(markets []gateway.Market) error {
	return gateway.NotImplementedErr
}

func (g *Gateway) AccountGateway() gateway.AccountGateway {
	return baseAccountGateway
}

func (g *Gateway) Tick() chan gateway.Tick {
	return baseTickCh
}

func (g *Gateway) SupportedMethods() gateway.Methods {
	return gateway.Methods{}
}
