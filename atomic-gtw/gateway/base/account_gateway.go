package base

import (
	"github.com/herenow/atomic-gtw/gateway"
)

type AccountGateway struct{}

func (g *AccountGateway) Balances() ([]gateway.Balance, error) {
	return []gateway.Balance{}, gateway.NotImplementedErr
}

func (g *AccountGateway) OpenOrders(market gateway.Market) ([]gateway.Order, error) {
	return []gateway.Order{}, gateway.NotImplementedErr
}

func (g *AccountGateway) SendOrder(order gateway.Order) (orderId string, err error) {
	return "", gateway.NotImplementedErr
}

func (g *AccountGateway) ReplaceOrder(replaceOrder gateway.Order) (gateway.Order, error) {
	return gateway.Order{}, gateway.NotImplementedErr
}

func (g *AccountGateway) CancelOrder(order gateway.Order) error {
	return gateway.NotImplementedErr
}

func (g *AccountGateway) Positions() ([]gateway.Position, error) {
	return []gateway.Position{}, gateway.NotImplementedErr
}

func (g *AccountGateway) NewClientOrderID() string {
	return ""
}
