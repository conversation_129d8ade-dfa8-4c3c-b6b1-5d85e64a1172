package gateway

import (
	"errors"
	"fmt"
)

var (
	NotImplementedErr      = errors.New("not implemented")
	OrderNotFoundErr       = errors.New("order not found")
	AlreadyCancelledErr    = errors.New("order is already cancelled")
	InsufficientBalanceErr = errors.New("insufficient Balance on Exchange")
	InvalidNonceErr        = errors.New("the request's nonce was invalid")
	RateLimitErr           = errors.New("request too frequent, rate limited")
	MinOrderSizeErr        = errors.New("order size is too small")
)

type OrderNotOpenedErr struct {
	Err error
}

func (e *OrderNotOpenedErr) Error() string {
	return fmt.Sprintf("Order was not opened, unkown error: %s", e.Err.Error())
}

// Checks if the error is a known error, and thus, the order
// was not opened on the exchange.
func CheckOrderNotOpenedErr(err error) bool {
	switch err {
	case InsufficientBalanceErr, InvalidNonceErr, RateLimitErr, MinOrderSizeErr, OrderNotFoundErr, AlreadyCancelledErr:
		return true
	}

	if _, ok := err.(*OrderNotOpenedErr); ok {
		return true
	}

	return false
}

func CheckAlreadyCancelledErr(err error) bool {
	return err == AlreadyCancelledErr
}
