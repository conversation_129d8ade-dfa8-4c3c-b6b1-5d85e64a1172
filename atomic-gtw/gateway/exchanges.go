package gateway

import (
	"regexp"
	"strings"
	"sync"
)

type Exchange struct {
	Name    string  `json:"name"`
	ChainID ChainID `json:"chain_id"`
}

func (e Exchange) String() string {
	str := e.Name
	if e.ChainID != 0 {
		str += "/" + e.ChainID.String()
	}
	return str
}

var exchangeIDCache sync.Map

func (e Exchange) ID() string {
	if id, ok := exchangeIDCache.Load(e); ok {
		return id.(string)
	}

	id := ExchangeToSymbol(e.String())
	exchangeIDCache.Store(e, id)
	return id
}

// Keep for backward compatibility, but we should use ID() instead
func (e Exchange) Symbol() string {
	return e.ID()
}

var exchangeSymReplacer = regexp.MustCompile(`[^a-zA-Z0-9\/]+`)

func ExchangeToSymbol(str string) string {
	str = strings.ToLower(str)
	str = exchangeSymReplacer.ReplaceAllString(str, "")
	return str
}

type NewGateway func(Options) Gateway

var Exchanges = make([]Exchange, 0)
var exchangeMap = make(map[Exchange]NewGateway)

func RegisterExchange(exg Exchange, newGtw NewGateway) {
	_, ok := exchangeMap[exg]
	if !ok {
		Exchanges = append(Exchanges, exg)
	}

	exchangeMap[exg] = newGtw
}

func NewByExchange(exg Exchange, options Options) (Gateway, bool) {
	newGtw, ok := exchangeMap[exg]
	if ok {
		return newGtw(options), true
	}

	return nil, false
}

func ExchangeBySymbol(sym string) (Exchange, bool) {
	for _, exg := range Exchanges {
		if exg.Symbol() == ExchangeToSymbol(sym) {
			return exg, true
		}
	}

	return Exchange{}, false
}
