package gateway

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/herenow/atomic-gtw/utils"
	"github.com/herenow/atomic-tools/pkg/book"
)

type MarketType string

const (
	SpotMarket    MarketType = "spot"
	FuturesMarket MarketType = "futures"
	OptionsMarket MarketType = "options"
)

type Market struct {
	Exchange               Exchange   `json:"exchange"`
	MarketType             MarketType `json:"market_type"`
	Pair                   Pair       `json:"pair"`
	Symbol                 string     `json:"symbol"`
	TakerFee               float64    `json:"taker_fee"`
	MakerFee               float64    `json:"maker_fee"`
	PriceTick              float64    `json:"price_tick"`
	AmountTick             float64    `json:"amount_tick"`
	MinimumOrderSize       float64    `json:"minimum_order_size"`
	MinimumOrderMoneyValue float64    `json:"minimum_order_money_value"`
	FuturesMarginAsset     string     `json:"futures_margin_asset"`
	// Not available for trading
	Closed bool `json:"closed"`
	// Pays fees in base currency instead of the quote currency.
	// This is not a standard behavior, most markets charge the fee in
	// the quote currency not the base currency.
	PaysFeeInStock bool `json:"pays_fee_in_stock"`
	// Fee tier used by some DEX such as Uniswap v4 pools
	FeeTier int64 `json:"fee_tier"`
	// Legacy attributes, TODO: stop depending on this
	Book *book.Book `json:"-"`
	// Can be used to store additional information used internally
	Meta any `json:"meta"`
}

func (m Market) ID() string {
	return m.Exchange.Name + ":" + m.Symbol
}

func (m Market) String() string {
	return m.ID()
}

// When sending data to safegateway via msgpack, interface{} (any) will be marshalled as a map[string]any.
// We must decode back to the expected type when reading from any .Meta field.
func DecodeMeta[T any](meta any) (T, error) {
	var result T
	switch v := meta.(type) {
	case T:
		return v, nil
	case map[string]any:
		b, err := json.Marshal(v)
		if err != nil {
			return result, fmt.Errorf("marshal meta map: %w", err)
		}
		if err := json.Unmarshal(b, &result); err != nil {
			return result, fmt.Errorf("unmarshal meta map: %w", err)
		}
		return result, nil
	case []byte:
		if err := json.Unmarshal(v, &result); err != nil {
			return result, fmt.Errorf("unmarshal meta bytes: %w", err)
		}
		return result, nil
	case nil:
		return result, fmt.Errorf("meta is nil")
	default:
		return result, fmt.Errorf("unexpected meta type %T", meta)
	}
}

type Balance struct {
	Asset     string  `json:"asset"`
	Available float64 `json:"available"`
	Total     float64 `json:"total"`
}

type Pair struct {
	Base  string `json:"base"`
	Quote string `json:"quote"`
}

func (p Pair) String() string {
	return p.Base + "/" + p.Quote
}

type OrderType string

const (
	LimitOrder  OrderType = "limit"
	MarketOrder OrderType = "market"
)

type Side string

const (
	Bid Side = "bid"
	Ask Side = "ask"
)

type OrderState string

const (
	OrderSent            OrderState = "sent"
	OrderOpen            OrderState = "open"
	OrderCancelled       OrderState = "cancelled"
	OrderPartiallyFilled OrderState = "partially_filled"
	OrderClosed          OrderState = "closed"
	OrderFullyFilled     OrderState = "fully_filled"
	OrderRejected        OrderState = "rejected"
	OrderUnknown         OrderState = "unknown"
)

func OrderStateOpen(st OrderState) bool {
	return st == OrderOpen || st == OrderPartiallyFilled
}

func OrderStateTerminated(st OrderState) bool {
	return st == OrderCancelled || st == OrderFullyFilled || st == OrderClosed || st == OrderRejected
}

type Order struct {
	ID               string     `json:"id"`
	ClientOrderID    string     `json:"client_order_id"`
	Market           Market     `json:"market"`
	State            OrderState `json:"state"`
	Type             OrderType  `json:"type"`
	Side             Side       `json:"side"`
	Price            float64    `json:"price"`
	AvgPrice         float64    `json:"avg_price"`
	Amount           float64    `json:"amount"`
	FilledAmount     float64    `json:"filled_amount"`
	FilledMoneyValue float64    `json:"filled_money_value"` // FilledMoneyValue is the (FilledAmount * AvgPrice) of the order.
	Fee              float64    `json:"fee"`
	FeeAsset         string     `json:"fee_asset"` // FeeAsset defines which side (Base or Quote), the fee is applied.
	PostOnly         bool       `json:"post_only"`
	Error            error      `json:"error"`
	NewClientOrderID string     `json:"new_client_order_id"` // For order replace requests
	RemainingAmount  float64    `json:"remaining_amount"`    // Set remaining amount for replacement order
	Tags             []string   `json:"tags"`                // Useful tags when persisting the order to the DB
}

func (o *Order) LeftAmount() float64 {
	return o.Amount - o.FilledAmount
}

func (o Order) String() string {
	return fmt.Sprintf(
		"%s:%s:%s:%s:%s:%s:%s@%s",
		o.Market.String(),
		o.ID,
		o.ClientOrderID,
		o.Type,
		o.Side,
		o.State,
		utils.FloatToStringWithTick(o.Amount, o.Market.AmountTick),
		utils.FloatToStringWithTick(o.Price, o.Market.PriceTick),
	)
}

type Execution struct {
	Time     time.Time `json:"time"`
	Market   Market    `json:"market"`
	TradeID  string    `json:"trade_id"`
	OrderID  string    `json:"order_id"`
	Side     Side      `json:"side"`
	Amount   float64   `json:"amount"`
	Price    float64   `json:"price"`
	Tags     []string  `json:"tags"` // Useful tags when persisting the order to the DB
	Fee      float64   `json:"fee"`
	FeeAsset string    `json:"fee_asset"`
	Meta     any       `json:"meta"` // Meta is a generic field to store additional information
}

func (e Execution) String() string {
	return fmt.Sprintf(
		"%s:%s:%s:%s:%s@%s:%s",
		e.Market.String(),
		e.OrderID,
		e.TradeID,
		e.Side,
		utils.FloatToStringWithTick(e.Amount, e.Market.AmountTick),
		utils.FloatToStringWithTick(e.Price, e.Market.PriceTick),
		e.Time.Format("2006-01-02T15:04:05.000Z"),
	)
}

type PriceArray struct {
	Price  float64
	Amount float64
}

func (p PriceArray) ToPriceLevel() PriceLevel {
	return PriceLevel{
		Price:  p.Price,
		Amount: p.Amount,
	}
}

func (p *PriceArray) UnmarshalJSON(b []byte) error {
	var data [2]interface{}
	if err := json.Unmarshal(b, &data); err != nil {
		return err
	}

	price, err := priceArrayParseFloat(data[0])
	if err != nil {
		return err
	}
	amount, err := priceArrayParseFloat(data[1])
	if err != nil {
		return err
	}

	p.Price = price
	p.Amount = amount

	return nil
}

func PriceArrayToPriceLevels(p []PriceArray) []PriceLevel {
	levels := make([]PriceLevel, len(p))
	for i, priceArray := range p {
		levels[i] = priceArray.ToPriceLevel()
	}
	return levels
}

func PriceArrayToDepthEvents(symbol string, side Side, p []PriceArray) []Event {
	events := make([]Event, len(p))
	for i, priceArray := range p {
		events[i] = Event{
			Type: DepthEvent,
			Data: Depth{
				Symbol: symbol,
				Side:   side,
				Price:  priceArray.Price,
				Amount: priceArray.Amount,
			},
		}
	}
	return events
}

func priceArrayParseFloat(data interface{}) (float64, error) {
	switch v := data.(type) {
	case string:
		f, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return 0, err
		}
		return f, nil
	case int64:
		return float64(v), nil
	case float64:
		return v, nil
	}

	return 0, errors.New(fmt.Sprintf("Failed to type cast %+v", data))
}

type PriceLevel struct {
	Price  float64 `json:"price"`
	Amount float64 `json:"amount"`
}

type GetDepthParams struct {
	Limit int `json:"limit"`
}

var DefaultDepthParams = GetDepthParams{}

type DepthBook struct {
	Sequence int64        `json:"sequence"`
	Asks     []PriceLevel `json:"asks"`
	Bids     []PriceLevel `json:"bids"`
}

func (d DepthBook) Sort() DepthBook {
	d.Asks = PriceLevelsSortAsks(d.Asks)
	d.Bids = PriceLevelsSortBids(d.Bids)
	return d
}

type Position struct {
	Symbol        string  `json:"symbol"`
	Amount        float64 `json:"amount"`
	Value         float64 `json:"value"`
	ExecutedPNL   float64 `json:"executed_pnl"`
	UnrealizedPNL float64 `json:"unrealized_pnl"`
}

// TokenConfig stores token information across different chains
type TokenConfig struct {
	Symbol    string
	Name      string
	Decimals  uint
	Addresses map[ChainID]string // Map of chain ID to token address
}

// TokenRegistry manages token information across chains
type TokenRegistry struct {
	chainID    ChainID
	tokens     map[string]*TokenConfig // Symbol -> Config
	addrTokens map[string]*TokenConfig // Address -> Config
}

func NewTokenRegistry(chainID ChainID) *TokenRegistry {
	registry := &TokenRegistry{
		chainID:    chainID,
		tokens:     make(map[string]*TokenConfig),
		addrTokens: make(map[string]*TokenConfig),
	}

	// Register common tokens across multiple chains
	registry.RegisterToken("WETH", "Wrapped Ether", 18, map[ChainID]string{
		ChainEthereum: "******************************************",
		ChainArbitrum: "******************************************",
	})

	registry.RegisterToken("USDC", "USD Coin", 6, map[ChainID]string{
		ChainEthereum: "******************************************",
		ChainArbitrum: "******************************************",
	})

	return registry
}

func (r *TokenRegistry) RegisterToken(symbol string, name string, decimals uint, addresses map[ChainID]string) {
	config := &TokenConfig{
		Symbol:    symbol,
		Name:      name,
		Decimals:  decimals,
		Addresses: addresses,
	}
	r.tokens[symbol] = config

	// Also index by address for this chain
	if addr, ok := addresses[r.chainID]; ok {
		r.addrTokens[addr] = config
	}
}

func (r *TokenRegistry) GetTokenConfig(symbol string) (*TokenConfig, error) {
	token, ok := r.tokens[symbol]
	if !ok {
		return nil, fmt.Errorf("token %s not found", symbol)
	}
	return token, nil
}

func (r *TokenRegistry) GetTokenAddress(symbol string) (string, error) {
	token, err := r.GetTokenConfig(symbol)
	if err != nil {
		return "", err
	}

	address, ok := token.Addresses[r.chainID]
	if !ok {
		return "", fmt.Errorf("token %s not available on chain %d", symbol, r.chainID)
	}

	return address, nil
}

func (r *TokenRegistry) GetTokenByAddress(address string) (*TokenConfig, error) {
	token, ok := r.addrTokens[address]
	if !ok {
		return nil, fmt.Errorf("token address %s not found", address)
	}
	return token, nil
}

// GetAvailableTokens returns a list of all registered token symbols
func (r *TokenRegistry) GetAvailableTokens() []string {
	tokens := make([]string, 0, len(r.tokens))
	for symbol := range r.tokens {
		// Only include tokens that have an address for the current chain
		if _, ok := r.tokens[symbol].Addresses[r.chainID]; ok {
			tokens = append(tokens, symbol)
		}
	}
	return tokens
}
