package gateway

import "strconv"

// ChainID represents different blockchain networks
type ChainID int

const (
	ChainEthereum ChainID = 1
	ChainArbitrum ChainID = 42161
	ChainPolygon  ChainID = 137
	ChainBSC      ChainID = 56
)

// Chains maps chain names to their ChainID
var Chains = map[string]ChainID{
	"ethereum": ChainEthereum,
	"arbitrum": ChainArbitrum,
	"polygon":  ChainPolygon,
	"bsc":      ChainBSC,
}

var ChainNames = func() map[ChainID]string {
	m := make(map[ChainID]string)
	for k, v := range Chains {
		m[v] = k
	}
	return m
}()

func (id ChainID) String() string {
	if name, ok := ChainNames[id]; ok {
		return name
	}
	return strconv.Itoa(int(id))
}

func ChainToID(chain string) (ChainID, bool) {
	id, ok := Chains[chain]
	return id, ok
}
