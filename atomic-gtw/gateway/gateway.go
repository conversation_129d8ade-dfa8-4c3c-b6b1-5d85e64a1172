package gateway

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/vmihailenco/msgpack/v5"
)

type GatewayState string

const (
	GatewayConnected    GatewayState = "connected"
	GatewayDisconnected GatewayState = "disconnected"
	GatewayLoaded       GatewayState = "loaded"
	GatewayFailed       GatewayState = "failed"
)

type Gateway interface {
	Exchange() Exchange
	GetMarkets() ([]Market, error)
	GetDepthBook(Market, GetDepthParams) (DepthBook, error)
	Markets() []Market
	AccountGateway() AccountGateway
	Connect() error
	Close() error
	SubscribeMarkets(markets []Market) error
	Tick() chan Tick
	SupportedMethods() Methods
}

type AccountGateway interface {
	Balances() ([]Balance, error)
	OpenOrders(Market) ([]Order, error)
	SendOrder(Order) (orderId string, err error)
	ReplaceOrder(Order) (Order, error)
	CancelOrder(Order) error
	Positions() ([]Position, error)
	NewClientOrderID() string
}

type Methods struct {
	CIDMapping bool // Create and map a order with a given CID
}

type Tick struct {
	Sequence          int64
	ServerTimestamp   time.Time
	ReceivedTimestamp time.Time
	GtwID             string
	EventLog          []Event
}

func (t Tick) String() string {
	lines := make([]string, len(t.EventLog))
	for i, event := range t.EventLog {
		line := event.Type.String()
		switch event.Type {
		case SnapshotSequenceEvent:
			snapshot := event.Data.(SnapshotSequence)
			line += fmt.Sprintf("\t%s", snapshot.Symbol)
		case DepthEvent:
			depth := event.Data.(Depth)
			line += fmt.Sprintf("\t%s", depth.Symbol)
			line += fmt.Sprintf("\t%s", depth.Side)
			line += fmt.Sprintf("\t%f", depth.Price)
			line += fmt.Sprintf("\t%f", depth.Amount)
		case TradeEvent:
			trade := event.Data.(Trade)
			line += fmt.Sprintf("\t%s", trade.ID)
			line += fmt.Sprintf("\t%s", trade.Timestamp)
			line += fmt.Sprintf("\t%s", trade.Symbol)
			line += fmt.Sprintf("\t%s", trade.Direction)
			line += fmt.Sprintf("\t%f", trade.Price)
			line += fmt.Sprintf("\t%f", trade.Amount)
		case OkEvent:
			ok := event.Data.(Ok)
			line += fmt.Sprintf("\t%s", ok.Symbol)
			line += fmt.Sprintf("\t%s", ok.OrderID)
			line += fmt.Sprintf("\t%s", ok.ClientOrderID)
		case CancelEvent:
			cancel := event.Data.(Cancel)
			line += fmt.Sprintf("\t%s", cancel.Symbol)
			line += fmt.Sprintf("\t%s", cancel.OrderID)
			line += fmt.Sprintf("\t%s", cancel.ClientOrderID)
		case FillEvent:
			fill := event.Data.(Fill)
			line += fmt.Sprintf("\t%s", fill.ID)
			line += fmt.Sprintf("\t%s", fill.OrderID)
			line += fmt.Sprintf("\t%s", fill.Symbol)
			line += fmt.Sprintf("\t%s", fill.Side)
			line += fmt.Sprintf("\t%f", fill.Price)
			line += fmt.Sprintf("\t%f", fill.Amount)
			line += fmt.Sprintf("\t%f", fill.Fee)
			line += fmt.Sprintf("\t%s", fill.FeeAsset)
		case OrderUpdateEvent:
			order := event.Data.(Order)
			line += fmt.Sprintf("\t%s", order.ID)
			line += fmt.Sprintf("\t%s", order.Market.Symbol)
			line += fmt.Sprintf("\t%s", order.Side)
			line += fmt.Sprintf("\t%s", order.State)
			line += fmt.Sprintf("\t%.12f", order.Price)
			line += fmt.Sprintf("\t%f", order.Amount)
			line += fmt.Sprintf("\t%f", order.FilledAmount)
			line += fmt.Sprintf("\t%f", order.AvgPrice)
		case BitmexInstrumentEvent:
			instr := event.Data.(BitmexInstrument)
			line += fmt.Sprintf("\t%f", instr.LastPrice)
			line += fmt.Sprintf("\t%s", instr.LastTickDirection)
			line += fmt.Sprintf("\t%f", instr.MarkPrice)
			line += fmt.Sprintf("\t%f", instr.FairPrice)
		}
		lines[i] = line
	}

	return fmt.Sprintf("\n%d\t%v\t%v\t%s\n%s", t.Sequence, t.ServerTimestamp, t.ReceivedTimestamp, t.GtwID, strings.Join(lines, "\n"))
}

type EventType int8

const (
	SnapshotSequenceEvent EventType = 1  // Flags that we should clear the order book
	DepthEvent            EventType = 2  // Depth order book price level update
	RawOrderEvent         EventType = 3  // Raw order book order update
	TradeEvent            EventType = 4  // Market trade occured
	OkEvent               EventType = 5  // User order ok event
	CancelEvent           EventType = 6  // User order cancel event
	FillEvent             EventType = 7  // User order fill event
	OrderUpdateEvent      EventType = 8  // User order fill event
	BitmexInstrumentEvent EventType = 50 // Bitmex instrument update
)

func (e EventType) String() string {
	switch e {
	case SnapshotSequenceEvent:
		return "snapshot"
	case DepthEvent:
		return "depth"
	case RawOrderEvent:
		return "raw_order"
	case TradeEvent:
		return "trade"
	case OkEvent:
		return "ok"
	case CancelEvent:
		return "cancel"
	case FillEvent:
		return "fill"
	case OrderUpdateEvent:
		return "order_update"
	case BitmexInstrumentEvent:
		return "bitmex_instrument"
	}
	return strconv.Itoa(int(e))
}

type Event struct {
	Type EventType   `json:"type"`
	Data interface{} `json:"data"`
}

var _ msgpack.CustomEncoder = (*Event)(nil)
var _ msgpack.CustomDecoder = (*Event)(nil)

func (e *Event) EncodeMsgpack(enc *msgpack.Encoder) error {
	err := enc.Encode(e.Type)
	if err != nil {
		return err
	}

	err = enc.Encode(&e.Data)
	if err != nil {
		return err
	}
	return nil
}

func NewEventDataByType(t EventType) any {
	var data interface{}
	switch t {
	case SnapshotSequenceEvent:
		data = SnapshotSequence{}
	case DepthEvent:
		data = Depth{}
	case RawOrderEvent:
		data = RawOrder{}
	case TradeEvent:
		data = Trade{}
	case OkEvent:
		data = Ok{}
	case CancelEvent:
		data = Cancel{}
	case FillEvent:
		data = Fill{}
	case OrderUpdateEvent:
		data = Order{}
	case BitmexInstrumentEvent:
		data = BitmexInstrument{}
	}
	return data
}

func (e *Event) DecodeMsgpack(dec *msgpack.Decoder) error {
	err := dec.Decode(&e.Type)
	if err != nil {
		return fmt.Errorf("decode event.Type: %s", err)
	}

	switch e.Type {
	case SnapshotSequenceEvent:
		snapshotSequence := SnapshotSequence{}
		err = dec.Decode(&snapshotSequence)
		e.Data = snapshotSequence
	case DepthEvent:
		depth := Depth{}
		err = dec.Decode(&depth)
		e.Data = depth
	case RawOrderEvent:
		rawOrder := RawOrder{}
		err = dec.Decode(&rawOrder)
		e.Data = rawOrder
	case TradeEvent:
		trade := Trade{}
		err = dec.Decode(&trade)
		e.Data = trade
	case OkEvent:
		ok := Ok{}
		err = dec.Decode(&ok)
		e.Data = ok
	case CancelEvent:
		cancel := Cancel{}
		err = dec.Decode(&e.Data)
		e.Data = cancel
	case FillEvent:
		fill := Fill{}
		err = dec.Decode(&fill)
		e.Data = fill
	case OrderUpdateEvent:
		order := Order{}
		err = dec.Decode(&order)
		e.Data = order
	case BitmexInstrumentEvent:
		bitmexInstrument := BitmexInstrument{}
		err = dec.Decode(&bitmexInstrument)
		e.Data = bitmexInstrument
	default:
		return fmt.Errorf("unknown unmarshaler for event type: %s", e.Type)
	}
	if err != nil {
		return fmt.Errorf("event [%s] decode err event.Data: %s", e.Type, err)
	}

	return nil
}

type SnapshotSequence struct {
	Symbol string `json:"symbol"`
}

type Depth struct {
	Symbol string  `json:"symbol"`
	Side   Side    `json:"side"`
	Amount float64 `json:"amount"`
	Price  float64 `json:"price"`
}

type RawOrder struct {
	Timestamp time.Time `json:"timestamp"`
	Symbol    string    `json:"symbol"`
	ID        string    `json:"id"`
	Side      Side      `json:"side"`
	Amount    float64   `json:"amount"`
	Price     float64   `json:"price"`
}

type Trade struct {
	Timestamp time.Time `json:"timestamp"`
	Symbol    string    `json:"symbol"`
	ID        string    `json:"id"`
	OrderID   string    `json:"order_id"`  // Matched against order on the book
	Direction Side      `json:"direction"` // Aggressor side
	Amount    float64   `json:"amount"`
	Price     float64   `json:"price"`
	Meta      any       `json:"meta"` // Meta is a generic field to store additional information
}

// Generate a fake ID for a trade, some exchanges do not provide an ID for trades
// in this case, we can try to generate a fake ID based on the trade fingerprint data.
// But this is not guaranteed to be unique.
func (t Trade) FakeID() string {
	str := fmt.Sprintf(
		"%s%s%s%.12f%.12f",
		t.Timestamp.Format("2006-01-02T15:04:05.999999999Z07:00"), // High precision timestamp
		t.Symbol,
		t.Direction,
		t.Price,
		t.Amount,
	)
	hash := md5.Sum([]byte(str))
	return hex.EncodeToString(hash[:])
}

type Ok struct {
	Timestamp     time.Time `json:"timestamp"`
	Symbol        string    `json:"symbol"`
	OrderID       string    `json:"order_id"`
	ClientOrderID string    `json:"client_order_id"`
}

type Cancel struct {
	Timestamp     time.Time `json:"timestamp"`
	Symbol        string    `json:"symbol"`
	OrderID       string    `json:"order_id"`
	ClientOrderID string    `json:"client_order_id"`
}

type Fill struct {
	Timestamp     time.Time `json:"timestamp"`
	Symbol        string    `json:"symbol"`
	ID            string    `json:"id"`
	OrderID       string    `json:"order_id"`
	ClientOrderID string    `json:"client_order_id"`
	Side          Side      `json:"side"`
	Amount        float64   `json:"amount"`
	Price         float64   `json:"price"`
	FullyFilled   bool      `json:"fully_filled"`
	Taker         bool      `json:"taker"`
	Fee           float64   `json:"fee"`
	FeeAsset      string    `json:"fee_asset"`
}

const FeeInMoney string = "_money"
const FeeInStock string = "_stock"

type BitmexInstrument struct {
	Symbol                string    `json:"symbol"`
	LastPrice             float64   `json:"lastPrice"`
	LastTickDirection     string    `json:"lastTickDirection"`
	MarkPrice             float64   `json:"markPrice"`
	LastChangePcnt        float64   `json:"lastChangePcnt"`
	OpenValue             float64   `json:"openValue"`
	IndicativeSettlePrice float64   `json:"indicativeSettlePrice"`
	FairBasis             float64   `json:"fairBasis"`
	FairPrice             float64   `json:"fairPrice"`
	LastPriceProtected    float64   `json:"lastPriceProtected"`
	ImpactMidPrice        float64   `json:"impactMidPrice"`
	ImpactBidPrice        float64   `json:"impactBidPrice"`
	ImpactAskPrice        float64   `json:"impactAskPrice"`
	BidPrice              float64   `json:"bidPrice"`
	MiPrice               float64   `json:"midPrice"`
	AskPrice              float64   `json:"askPrice"`
	Timestamp             time.Time `json:"timestamp"`
}
