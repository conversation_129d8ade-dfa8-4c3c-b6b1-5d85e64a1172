root = "/app"
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
full_bin = ""
args_bin = ["--listen=:40000 --headless=true --api-version=2 --accept-multiclient exec /usr/bin/botmanager server env=dev"]
bin = "/go/bin/dlv"
cmd = "go build -gcflags='all=-N -l' -o /usr/bin/botmanager cmd/app/*.go"
delay = 1
exclude_dir = ["bm", "assets", "tmp", "vendor", "testdata", "signoz", "_clients_examples", ".idea", ".github", "atomic-ordermanager", "atomic-tools", "atomic-gtw", "atomic-protocols"]
exclude_file = ["docker-compose.yml", "Dockerfile", "Makefile"]
exclude_regex = []
exclude_unchanged = false
follow_symlink = false
include_dir = []
include_ext = ["go", "tpl", "tmpl", "html", "yaml", "yml", "toml"]
include_file = [".air.toml"]
kill_delay = "2s"
log = "build-errors.log"
rerun = false
rerun_delay = 500
send_interrupt = true
stop_on_error = true

[color]
app = ""
build = "yellow"
main = "magenta"
runner = "green"
watcher = "cyan"

[log]
main_only = false
time = false

[misc]
clean_on_exit = true

[screen]
clear_on_rebuild = false
keep_scroll = true