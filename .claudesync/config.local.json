{"active_provider": "claude.ai", "active_organization_id": "8fa74846-cc6e-4452-84f3-5220cfa9356e", "active_project_id": "c7fd1e77-803a-43a3-a8ef-dc3241fc814f", "active_project_name": "atomic-bm", "local_path": "/home/<USER>/GolandProjects/atomic-bm", "two_way_sync": false, "prune_remote_files": true, "upload_delay": 0.5, "max_file_size": 1048576, "compression_algorithm": "none", "log_level": "INFO", "default_sync_category": "atomic-bm", "file_categories": {"atomic-bm": {"description": "Source files", "patterns": ["go.mod", "docker-compose.yml", "atomic-protocols", "!atomic-protocols/gen", "internal/app", "internal/auth", "internal/controller/bot", "internal/services/botsservice", "!internal/db/repo/botrepo", "internal/middlewares", "internal/pkg", "internal/ws", "internal/db/migrations", "!internal/services", "!internal/db/repo", "!bin", "!docs", "pkg", "cmd", "!devops"]}}}