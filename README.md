# BM Service

Bot Manager Service for trading applications.

## Prerequisites

- Go (version 1.24+)
- pnpm
- Docker
- Nomad (for deployment)
- A `GITHUB_TOKEN` with `repo` scope exported in your shell.

## Local Development

1.  **Install Dependencies**
    ```bash
    make install
    ```

2.  **Run the Service**
    The service can be run directly:
    ```bash
    make run
    ```
    Or with Docker Compose for a full environment (Postgres, Redis, etc.):
    ```bash
    docker-compose up --build
    ```

## Release and Deployment Workflow

This project uses `semantic-release` to automate versioning and releases based on Conventional Commits. The entire process is managed via the `Makefile`.

### Commit Convention

Commits must follow the [Conventional Commits specification](https://www.conventionalcommits.org/).
- `feat:` for new features (results in a MINOR release).
- `fix:` for bug fixes (results in a PATCH release).
- `BREAKING CHANGE:` in the footer for breaking changes (results in a MAJOR release).
- Other types like `chore:`, `docs:`, `style:`, `refactor:`, `test:` will not trigger a release.

### Step 1: Create a Release

After committing your changes, create a new release:

```bash
make release
```

This command will:
1.  Analyze your commits since the last release.
2.  Determine the next version number.
3.  Update `cmd/app/version.go` with the new version.
4.  Generate/update `CHANGELOG.md`.
5.  Commit the updated files (`package.json`, `CHANGELOG.md`, `version.go`).
6.  Create a new Git tag (e.g., `v1.2.3`).
7.  Create a new Release on GitHub with the generated changelog.

### Step 2: Deploy to Nomad

Once `make release` is complete, it will output the new version tag. Use this tag to deploy the service.

```bash
# Replace vX.Y.Z with the new tag from the release step
make deploy VERSION=vX.Y.Z
```

This command will:
1.  Build a new Docker image tagged with the specified `VERSION`.
2.  Push the image to your Docker registry.
3.  Update `bm.nomad.hcl` with the new image tag.
4.  Provide the command to run the Nomad job (`nomad job run bm.nomad.hcl`).

---
_This README was updated to reflect the new automated release process._
